"""The prompt formatter for Ender models."""

import logging
import typing
from collections import defaultdict
from dataclasses import dataclass, field
from typing import Literal, Optional, Sequence, Tuple, cast

import dataclasses_json
from marshmallow import fields

from base.diff_utils.diff_formatter_completions import (
    format_tokenized_prompt_chunks,
    tokenize_prompt_chunks,
)

from base.prompt_format.common import PromptChunk
from base.prompt_format.util import (
    concatenate_retrieved_chunks,
    head_n,
    prompt_chunks_by_origin,
    trailing_n,
    update_retrieval_budget,
)
from base.prompt_format_completion.overlap import filter_visible_chunks_by_content
from base.prompt_format_completion.prompt_formatter import (
    CompletionPromptFormatter,
    PromptCache,
    PromptFormatterOutput,
    PromptInput,
    TokenList,
)
from base.prompt_format_completion.stateful_caching import (
    StatefulCachingConfig,
)
from base.prompt_format_completion.stateless_caching import (
    StatelessCachingConfig,
    quantize_by_char_len,
    segment_prefix_stateless,
)
from base.prompt_format_completion.token_apportionment import (
    TokenApportionmentConfig,
    apportion_context_budget,
)
from base.ranges.range_types import <PERSON>r<PERSON>ang<PERSON>
from base.static_analysis.signature_utils import SymbolSignature
from base.tokenizers.tokenizer import (
    RagSpecialTokens,
    Tokenizer,
    NextEditGenSpecialTokens,
)

Component = Literal[
    "path",
    "prefix",
    "suffix",
    "retrieval",
    "signature",
    "diff",
    "nearby_prefix",
]
ALL_COMPONENTS: tuple[Component, ...] = typing.get_args(Component)


@dataclass
class TokenBudget(dataclasses_json.DataClassJsonMixin):
    """The maximum number of tokens for each component."""

    max_prompt_length: int
    """The maximum number of tokens for the entire prompt."""

    path_len: int
    """The number of tokens of the path to include."""

    prefix_len: int
    """The number of tokens of the prefix to include."""

    retrieval_len: int
    """The number of tokens from retrieved chunks to include."""

    suffix_len: int
    """The number of tokens of the suffix to include."""

    per_retriever_max_tokens: dict[str, int] = field(default_factory=dict)
    """The number of tokens of per retriever to include."""

    def __post_init__(self):
        if self.path_len < 0:
            raise ValueError(f"{self.path_len} must be non-negative.")
        if self.prefix_len < 0:
            raise ValueError(f"{self.prefix_len=} must be non-negative.")
        if self.suffix_len < 0:
            raise ValueError(f"{self.suffix_len=} must be non-negative.")
        if self.retrieval_len < 0:
            raise ValueError(f"{self.retrieval_len=} must be non-negative.")


@dataclass
class EnderPromptFormatterConfig(dataclasses_json.DataClassJsonMixin):
    """The configuration for the Ender prompt formatter."""

    stateless_caching_config: StatelessCachingConfig = field(
        default_factory=StatelessCachingConfig
    )
    """The stateless caching configuration."""

    stateful_caching_config: StatefulCachingConfig = field(
        default_factory=StatefulCachingConfig
    )
    """Configuration of use of PromptCache. Controls how chunks are selected
    and ordered in the retrieval component."""

    component_order: tuple[Component, ...] = field(
        metadata=dataclasses_json.config(mm_field=fields.List(fields.Str())),
        default_factory=lambda: (
            "path",
            "retrieval",
            "signature",
            "prefix",
            "suffix",
        ),
    )

    signature_chunk_origin: str = "signature_retriever"
    """The origin of the signature chunk."""

    filter_visible_chunks_by_content: bool = True

    token_budget: TokenBudget | None = None
    """The token apportionment configuration."""

    min_prefix_suffix_len: Optional[int] = None

    def __post_init__(self):
        # check the components; we cannot directly use the Component Literal type because
        # it is not compatible with the dataclass_json library.
        for component in self.component_order:
            if component not in ALL_COMPONENTS:
                raise ValueError(
                    f"Invalid component name {component}. Must be one of {ALL_COMPONENTS}"
                )

    def get_components(self) -> tuple[Component, ...]:
        return self.component_order  # type: ignore


class EnderPromptFormatter(CompletionPromptFormatter):
    """The prompter for Ender models."""

    def __init__(
        self,
        apportionment_config: TokenApportionmentConfig | None,
        prompt_formatter_config: EnderPromptFormatterConfig,
        tokenizer: Tokenizer,
    ):
        self._apportionment_config = apportionment_config
        self._stateless_caching_config = (
            prompt_formatter_config.stateless_caching_config
        )
        self._token_budget = prompt_formatter_config.token_budget
        self.component_order = prompt_formatter_config.component_order
        self.signature_chunk_origin = prompt_formatter_config.signature_chunk_origin
        self.filter_visible_chunks_by_content = (
            prompt_formatter_config.filter_visible_chunks_by_content
        )
        self.min_prefix_suffix_len = prompt_formatter_config.min_prefix_suffix_len
        self.tokenizer = tokenizer

        # Checking for concrete types here provides static type checking with
        # runtime protection.
        special_tokens = self.tokenizer.special_tokens
        if not isinstance(special_tokens, RagSpecialTokens):
            raise ValueError(
                f"Can't use the given tokenizer with EnderPromptFormatter: {type(self.tokenizer)}."
                f"{special_tokens=} must have RagSpecialTokens."
            )

        self.ret_prefix = [special_tokens.retrieval_section]
        self.ret_start = [special_tokens.ret_start]
        self.ret_body = [special_tokens.ret_body]
        self.prefix_body = [special_tokens.prefix_body]
        self.filename_tokens = [special_tokens.filename]

        self.diff_prefix = []
        self.diff_end = []
        self.has_diff_component = "diff" in self.component_order
        if self.has_diff_component:
            if not isinstance(special_tokens, NextEditGenSpecialTokens):
                raise ValueError(
                    f"Can't use the given tokenizer with EnderPromptFormatter: {type(self.tokenizer)}."
                    f"{special_tokens=} must have NextEditGenSpecialTokens."
                )
            self.diff_prefix = [special_tokens.diff_section, special_tokens.newline]
            self.diff_end = [special_tokens.newline]

        self.sig_prefix = [special_tokens.signature_section]
        self.sig_start = [special_tokens.sig_begin, special_tokens.newline]
        self.sig_end = [special_tokens.newline, special_tokens.sig_end]
        self.sig_delimiter = [special_tokens.newline, special_tokens.newline]
        self.sig_lookup = [special_tokens.sig_lookup]

        if self._stateless_caching_config.nearby_prefix_token_len > 0:
            self.fim_prefix = [special_tokens.far_prefix]
        else:
            self.fim_prefix = [special_tokens.fim_prefix]

        self.fim_suffix = [special_tokens.fim_suffix]

        self.nearby_prefix = [special_tokens.fim_prefix]
        self.fim_middle = [special_tokens.fim_middle]
        self.newline = [special_tokens.newline]
        self.begin_sequence = list(special_tokens.begin_sequence)

        self.stateful_caching_config = prompt_formatter_config.stateful_caching_config
        if self.stateful_caching_config.enabled:
            # If the path component is before the retrieval component, then a change in path should
            # clear the PromptCache, as the prompt will differ in its prefix before even considering
            # retrieved chunks.
            component_idxs = {
                component: idx for idx, component in enumerate(self.component_order)
            }
            self._path_invalidates_cache = (
                component_idxs["path"] < component_idxs["retrieval"]
            )
        else:
            self._path_invalidates_cache = False

    def _tokenize_signature_chunk(self, doc: PromptChunk) -> TokenList:
        return self.tokenizer.tokenize_safe(doc.text)

    def format_prompt(
        self,
        prompt_input: PromptInput,
        max_output_token_count: int,
    ) -> PromptFormatterOutput:
        output, out_cache = self.format_prompt_with_cache(
            prompt_input, max_output_token_count, PromptCache(), []
        )
        return output

    def supports_caching(self) -> bool:
        return self.stateful_caching_config.enabled

    def format_prompt_with_cache(
        self,
        prompt_input: PromptInput,
        max_output_token_count: int,
        prompt_cache: PromptCache,
        invalid_blobs: typing.Sequence[bytes],
    ) -> Tuple[PromptFormatterOutput, PromptCache]:
        r"""Format prompt for Ender models.

        Args:
            prompt_input: an instance of PromptInput class, containing all raw input.
            max_output_token_count: The maximal number of tokens that the current completion request should generate.

        Returns:
            A prompt of length at most self.seq_length - max_output_token_count, in tokens.

        Prompt structure (newlines are explicitly shown):
            == Path Component ==
            <filename>{path}\n
            == Retrieval Component ==
            <|retrieval_section|>
            (<|ret-start|><filename>{path}<|ret-body|>{content})*
            == Signature Component ==
            <|sig-begin|>\n
            {content}?
            (\n\n{content})*
            \n<|sig-end|>
            == Diff Component ==
            <|diff_section|>\n
            {diff hunks formatted with `format_file_changes`}
            \n
            == Prefix Component ==
            <fim_prefix>{prefix}
            == Suffix Component ==
            <fim_suffix>{suffix}
            == End of components ==
            <fix_middle>

        Recency chunks are included in the main retrieval section.

        This is an example with retrieval:

            <filename>src/example.py
            <|retrieval_section|><|ret-start|><filename>example_doc.py<|ret-body|># You can aggregate
            # with a maxing
            # function.
            <|ret-start|><filename>example_doc.py<|ret-body|># You can aggregate
            # with a pooling function.
            <|sig-begin|>
            # class2.py
            class 2 body

            # class1.py
            class 1 body
            <|sig-end|><fim_prefix>def aggregate(a,b):
            <fim_suffix>
            return aggregated_output
            <fim_middle>
        """
        component_tokens = defaultdict[Component, TokenList](TokenList)

        tokenized_path = self.tokenizer.tokenize_safe(prompt_input.path)

        prefix_chars = prompt_input.prefix
        prefix_char_offset = prompt_input.prefix_begin

        suffix_chars = prompt_input.suffix

        # If necessary, truncate the prefix in characters
        quantize_char_len = self._stateless_caching_config.quantize_char_len
        if quantize_char_len > 0:
            prefix_chars = quantize_by_char_len(
                prefix_chars=prefix_chars,
                prefix_char_offset=prefix_char_offset,
                quantize_char_len=quantize_char_len,
            )

        raw_prefix_tokens = self.tokenizer.tokenize_safe(prefix_chars)
        raw_suffix_tokens = self.tokenizer.tokenize_safe(prompt_input.suffix)

        # fim suffix and path newline token are separate inputs to apportionment
        num_preference_tokens = (
            len(self.sig_start)  # - 2 for the signature start token
            + len(self.sig_end)  # - 2 for the signature end token
            + len(self.ret_prefix)  # - 1 for the retrieval section token
            + len(self.fim_prefix)  # - 1 for the fim_prefix token.
            + len(self.fim_middle)  # - 1 for the fim_middle token.
            + len(self.begin_sequence)  # - 0 or 1 the begin_sequence token.
            # - up to 1 for the nearby prefix token.
            + int(self._stateless_caching_config.nearby_prefix_token_len > 0)
        )

        if self.has_diff_component:
            num_preference_tokens += len(self.diff_prefix) + len(
                self.diff_end
            )  # - 3 for the diff tokens

        nearby_prefix_token_len = self._stateless_caching_config.nearby_prefix_token_len
        nearby_prefix_token_overlap = (
            self._stateless_caching_config.nearby_prefix_token_overlap
        )

        if self._apportionment_config is not None:
            # This is the original prompt formatter's config style.
            max_content_length = (
                self._apportionment_config.max_content_len
                - max_output_token_count
                - self._apportionment_config.extra_generation_budget
            )

            token_counts = apportion_context_budget(
                max_content_length=max_content_length,
                input_fraction=self._apportionment_config.input_fraction,
                prefix_fraction=self._apportionment_config.prefix_fraction,
                max_path_tokens=self._apportionment_config.max_path_tokens,
                path_prefix=True,
                num_preference_tokens=num_preference_tokens,
                num_path_tokens=len(tokenized_path),
                num_prefix_tokens=len(raw_prefix_tokens) + nearby_prefix_token_overlap,
                num_suffix_tokens=len(raw_suffix_tokens),
                num_path_sep_tokens=len(self.filename_tokens) + len(self.newline),
                num_suffix_sep_tokens=1,
            )
            per_retriever_max_tokens = (
                self._apportionment_config.per_retriever_max_tokens
            )
            max_path_tokens = self._apportionment_config.max_path_tokens
        else:
            # If the user does not set the apportionment config, then we use the token budget.
            assert self._token_budget is not None
            token_counts = self._token_budget
            max_content_length = token_counts.max_prompt_length
            max_path_tokens = token_counts.path_len
            per_retriever_max_tokens = self._token_budget.per_retriever_max_tokens

        logging.info("Token allocation: %s", token_counts)

        if prompt_input.path:
            component_tokens["path"] += self.filename_tokens
            component_tokens["path"] += trailing_n(
                tokenized_path, token_counts.path_len
            )
            component_tokens["path"] += self.newline

        if self._path_invalidates_cache and prompt_input.path != prompt_cache.path:
            # We will also have issues if token_counts.path_len changes...
            # Fortunately today it appears that this value is fixed for a given
            # apportionment config and path.
            prompt_cache = PromptCache()

        quantize_token_len = self._stateless_caching_config.quantize_token_len
        prefix_range, nearby_prefix_range = segment_prefix_stateless(
            raw_prefix_token_len=len(raw_prefix_tokens),
            max_prompt_prefix_tokens=token_counts.prefix_len,
            nearby_prefix_token_len=nearby_prefix_token_len,
            nearby_prefix_token_overlap=nearby_prefix_token_overlap,
            quantize_token_len=quantize_token_len,
        )

        if (nearby_prefix_token_len > 0) != ("nearby_prefix" in self.component_order):
            raise ValueError(
                "Nearby prefix should be in component order iff nearby prefix len > 0"
            )

        nearby_prefix_tokens = []

        if nearby_prefix_range is not None:
            component_tokens["nearby_prefix"] = list(self.nearby_prefix)
            nearby_prefix_tokens = raw_prefix_tokens[
                nearby_prefix_range.start : nearby_prefix_range.stop
            ]
            component_tokens["nearby_prefix"] += nearby_prefix_tokens

        prefix_tokens = raw_prefix_tokens[prefix_range.start : prefix_range.stop]
        component_tokens["prefix"] += self.fim_prefix
        component_tokens["prefix"] += prefix_tokens

        # always add fim_suffix token
        component_tokens["suffix"] += self.fim_suffix
        suffix_tokens = head_n(raw_suffix_tokens, token_counts.suffix_len)
        component_tokens["suffix"] += suffix_tokens

        ret_chunks = list(prompt_input.retrieved_chunks)

        if self.filter_visible_chunks_by_content:
            if nearby_prefix_range is not None:
                total_prefix_tokens = raw_prefix_tokens[
                    prefix_range.start : nearby_prefix_range.stop
                ]
            else:
                total_prefix_tokens = raw_prefix_tokens[
                    prefix_range.start : prefix_range.stop
                ]
            prefix_len = len(self.tokenizer.detokenize(total_prefix_tokens))
            prefix_chars = prefix_chars[len(prefix_chars) - prefix_len :]

            suffix_len = len(self.tokenizer.detokenize(suffix_tokens))
            suffix_chars = suffix_chars[:suffix_len]

            # Separate out diff retriever chunks from the rest as they should not be filtered.
            diff_ret_chunks = [
                chunk for chunk in ret_chunks if chunk.origin == "diff_retriever"
            ]
            ret_chunks = [
                chunk for chunk in ret_chunks if chunk.origin != "diff_retriever"
            ]
            # Filter chunks that are fully contained in the prompt range by content.
            ret_chunks = list(
                filter_visible_chunks_by_content(
                    path=prompt_input.path,
                    prompt_prefix=prefix_chars,
                    prompt_suffix=suffix_chars,
                    chunks=ret_chunks,
                    min_prefix_suffix_len=self.min_prefix_suffix_len,
                )
            )
            # Add back the diff retriever chunks.
            ret_chunks.extend(diff_ret_chunks)

        # materialize the chunks so we can treat each origin separately
        chunk_dict = prompt_chunks_by_origin(ret_chunks)
        logging.info(f"Origins: {list(chunk_dict.keys())}")
        retrieved_chunks_dict = defaultdict(list, chunk_dict)

        # Diff retrieval section
        non_preference_diff_tokens_used = 0
        if self.has_diff_component:
            diff_budget = min(
                per_retriever_max_tokens.get("diff_retriever", 0),
                token_counts.retrieval_len,
            )
            component_tokens["diff"] += self.diff_prefix
            diff_hunks_prompt_chunks = retrieved_chunks_dict["diff_retriever"]
            tokenized_diff_hunks = tokenize_prompt_chunks(
                diff_hunks_prompt_chunks, diff_budget, self.tokenizer
            )
            diff_hunks_tokens = format_tokenized_prompt_chunks(
                list(reversed(tokenized_diff_hunks))
            )
            component_tokens["diff"] += diff_hunks_tokens
            component_tokens["diff"] += self.diff_end
            non_preference_diff_tokens_used = len(diff_hunks_tokens)
            logging.info(
                "Used %d/%d diff tokens",
                len(component_tokens["diff"]),
                diff_budget,
            )

        # NOTE(arun): the fixed tokens are already accounted for in the retrieval
        #   budget.
        signature_budget = min(
            per_retriever_max_tokens.get(self.signature_chunk_origin, 0),
            max(0, token_counts.retrieval_len - non_preference_diff_tokens_used),
        )
        component_tokens["signature"] += self.sig_start
        # NOTE(arun): In a small departure from the research implementation, we only
        # include full signature chunks and don't truncate them. This shouldn't make
        # much of a difference.
        signature_chunk_tokens = concatenate_retrieved_chunks(
            retrieved_chunks=(
                self._tokenize_signature_chunk(chunk)
                for chunk in retrieved_chunks_dict[self.signature_chunk_origin]
            ),
            separator_tokens=self.sig_delimiter,
            max_total_tokens=signature_budget,
        )
        component_tokens["signature"] += signature_chunk_tokens
        component_tokens["signature"] += self.sig_end
        non_preference_signature_tokens_used = len(signature_chunk_tokens)
        logging.info(
            "Used %d/%d signature tokens",
            len(component_tokens["signature"]),
            signature_budget,
        )

        # Retrieval section
        def tokenize_retrieval_chunk(
            doc: PromptChunk, max_path_tokens: int
        ) -> TokenList:
            result = []
            result += self.ret_start
            result += self.filename_tokens
            result += trailing_n(
                self.tokenizer.tokenize_safe(doc.path),
                max_path_tokens,
            )
            result += self.ret_body
            result += self.tokenizer.tokenize_safe(doc.text)
            return result

        # NOTE(pranay): I don't think we need to subtract len(self.begin_sequence) from the retrieval budget as retrieval budget already accounts for it and non_preference_signature_tokens_used doesn't use it.
        # However, I keep it for now to preserve the original logic.
        retrieval_budget = max(
            0,
            token_counts.retrieval_len
            - non_preference_diff_tokens_used
            - (non_preference_signature_tokens_used - len(self.begin_sequence)),
        )
        # Extra check to make sure we don't overrun the prompt length
        retrieval_budget = update_retrieval_budget(
            retrieval_budget=retrieval_budget,
            max_prompt_tokens=max_content_length,
            component_tokens=component_tokens,
            additional_tokens=len(self.begin_sequence),
        )
        assert retrieval_budget >= 0, f"{retrieval_budget=}/{max_content_length=}"

        # the <|retrieval_section|> token is there even if there is no retrieved chunk
        component_tokens["retrieval"] += self.ret_prefix

        # TODO(guy) In this experimental implementation, recency tokens take
        # precedence over dense retrieval tokens. We might want to change this
        # behavior. It's not clearly a problem, because recency tokens are capped
        # by a smallish budget within the retrieval budget.
        recency_retrieval_budget = min(
            per_retriever_max_tokens.get("recency_retriever", 0),
            retrieval_budget,
        )
        assert (
            recency_retrieval_budget >= 0
        ), f"{recency_retrieval_budget=}/{retrieval_budget=}"

        chunk_separator_tokens = []

        recency_included: list[Tuple[PromptChunk, TokenList]] = []
        recency_tokens_total = 0
        recency_chunk_ids = set()
        for chunk in retrieved_chunks_dict["recency_retriever"]:
            tokens = tokenize_retrieval_chunk(
                doc=chunk, max_path_tokens=max_path_tokens
            )
            sep_tokens = len(chunk_separator_tokens) if recency_included else 0
            if (
                recency_tokens_total + len(tokens) + sep_tokens
                > recency_retrieval_budget
            ):
                break
            recency_chunk_ids.add(chunk.unique_id)
            recency_tokens_total += len(tokens) + sep_tokens
            recency_included.append((chunk, tokens))
        if None in recency_chunk_ids:
            # We no longer warn if recency chunks are missing unique IDs, because
            # they currently use a different chunking mechanism and are not critical to
            # deduplicate
            recency_chunk_ids.remove(None)
        logging.info(
            "Used %d/%d recency tokens",
            recency_tokens_total,
            recency_retrieval_budget,
        )

        # Viewed content chunks take precedence over dense retrieval tokens.
        viewed_content_retrieval_budget = min(
            per_retriever_max_tokens.get("recency_retriever_viewed_content", 0),
            retrieval_budget - recency_tokens_total,
        )
        assert (
            viewed_content_retrieval_budget >= 0
        ), f"{viewed_content_retrieval_budget=}/{retrieval_budget=}"
        viewed_content_included: list[Tuple[PromptChunk, TokenList]] = []
        viewed_content_tokens_total = 0
        viewed_content_chunk_ids = set()
        for chunk in retrieved_chunks_dict["recency_retriever_viewed_content"]:
            tokens = tokenize_retrieval_chunk(
                doc=chunk, max_path_tokens=max_path_tokens
            )
            sep_tokens = len(chunk_separator_tokens) if viewed_content_included else 0
            if (
                viewed_content_tokens_total + len(tokens) + sep_tokens
                > viewed_content_retrieval_budget
            ):
                break
            viewed_content_chunk_ids.add(chunk.unique_id)
            viewed_content_tokens_total += len(tokens) + sep_tokens
            viewed_content_included.append((chunk, tokens))
        if None in viewed_content_chunk_ids:
            # We no longer warn if viewed content chunks are missing unique IDs,
            # because they currently use a different chunking mechanism and are not
            # critical to deduplicate
            viewed_content_chunk_ids.remove(None)
        logging.info(
            "Used %d/%d viewed content tokens",
            viewed_content_tokens_total,
            viewed_content_retrieval_budget,
        )

        # all other dense chunks
        remaining_chunks: list[PromptChunk] = []
        for origin, chunks in retrieved_chunks_dict.items():
            # skip chunks for origins that were already processed
            if origin in [
                self.signature_chunk_origin,
                "recency_retriever",
                "recency_retriever_viewed_content",
                "diff_retriever",
            ]:
                continue
            remaining_chunks.extend(
                (
                    chunk
                    for chunk in chunks
                    # skip chunks that were already included as recency chunks
                    if chunk.unique_id not in recency_chunk_ids
                )
            )

        other_retrieval_budget = (
            retrieval_budget - recency_tokens_total - viewed_content_tokens_total
        )
        assert (
            other_retrieval_budget >= 0
        ), f"{other_retrieval_budget=}/{retrieval_budget=}"
        other_included: list[Tuple[PromptChunk, TokenList]] = []
        other_tokens_total = 0
        for chunk in remaining_chunks:
            tokens = tokenize_retrieval_chunk(
                doc=chunk, max_path_tokens=max_path_tokens
            )
            sep_tokens = len(chunk_separator_tokens) if other_included else 0
            if other_tokens_total + len(tokens) + sep_tokens > other_retrieval_budget:
                break
            other_tokens_total += len(tokens) + sep_tokens
            other_included.append((chunk, tokens))
        logging.info(
            "Used %d/%d dense tokens",
            other_tokens_total,
            other_retrieval_budget,
        )

        # By the end of this series of branches, ordered_selected_chunks should
        # contain some combination of recency and dense chunks, along with their
        # tokenized form. These will constitute the retrieval section of the
        # prompt, and be included in the returned PromptCache.
        ordered_selected_chunks: list[Tuple[PromptChunk, TokenList]] = []
        if not self.stateful_caching_config.enabled:
            # True legacy mode
            # This case exists so that formatters which don't configure caching
            # see no difference in the produced prompt.
            ordered_selected_chunks = []
            ordered_selected_chunks.extend(reversed(recency_included))
            ordered_selected_chunks.extend(reversed(viewed_content_included))
            ordered_selected_chunks.extend(reversed(other_included))
        elif not prompt_cache.prompt_ordered_chunks:
            # Brandon has a gut feeling which should be better justified that
            # recency chunks are more liable to change, and putting
            # dense_retriever chunks after them is riskier. Ideally they should
            # probably interleave somewhat.
            ordered_selected_chunks = (
                other_included + viewed_content_included + recency_included
            )
        else:
            # Implementing the least aggressive setting of StatefulCachingConfig,
            # where the only change we can make is to re-order the high-rank
            # retrieved chunks we selected above.
            selected_chunks = (
                other_included + viewed_content_included + recency_included
            )

            def key(chunk: PromptChunk):
                # Leaving out (as of 2024/09): text, origin, header, documentation_metadata
                return (
                    chunk.unique_id,
                    chunk.path,
                    chunk.char_start,
                    chunk.char_end,
                    chunk.blob_name,
                )

            lookup = {key(chunk): idx for idx, (chunk, _) in enumerate(selected_chunks)}
            ordered_selected_chunks = []
            used_indexes = set()
            highest_unbroken = None
            for chunk in prompt_cache.prompt_ordered_chunks:
                idx = lookup.get(key(chunk))
                if idx is None:
                    # Once we fail to match, we don't know how to benefit from the cache anymore,
                    # so fall back to default ordering for remaining chunks.
                    break
                if idx in used_indexes:
                    logging.warning(
                        "Two chunks with same key; prompt state caching assumptions are faulty"
                    )
                    break
                used_indexes.add(idx)
                if idx == len(ordered_selected_chunks) and highest_unbroken in (
                    None,
                    idx - 1,
                ):
                    highest_unbroken = idx
                ordered_selected_chunks.append(selected_chunks[idx])
            common_before = 0
            if highest_unbroken is not None:
                common_before = sum(
                    len(t) for _, t in ordered_selected_chunks[:highest_unbroken]
                )
            common_after = sum(len(t) for _, t in ordered_selected_chunks)
            total = common_after
            for idx, chunk in enumerate(selected_chunks):
                if idx in used_indexes:
                    continue
                ordered_selected_chunks.append(chunk)
                total += len(chunk[1])
            logging.info(
                "Stateful caching: Retrieval tokens total=%d common_before=%d common_after=%d",
                total,
                common_before,
                common_after,
            )

        retrieval_tokens = concatenate_retrieved_chunks(
            retrieved_chunks=(tokens for _, tokens in ordered_selected_chunks),
            separator_tokens=chunk_separator_tokens,
            reverse_chunks=False,
            max_total_tokens=retrieval_budget,
        )
        assert abs(
            len(retrieval_tokens)
            - (recency_tokens_total + viewed_content_tokens_total + other_tokens_total)
        ) <= len(chunk_separator_tokens)
        component_tokens["retrieval"] += retrieval_tokens

        tokens = list(self.begin_sequence)
        for component in self.component_order:
            logging.info(f"{component} : {len(component_tokens[component])} tokens.")
            tokens += component_tokens[component]

        # Always add the FIM middle token.
        tokens += self.fim_middle
        logging.info(
            "Used %d/%d tokens for prompt; reserving %d for output",
            len(tokens),
            max_content_length,
            max_output_token_count,
        )
        assert (
            len(tokens) <= max_content_length
        ), f"{len(tokens)} > {max_content_length}"
        new_cache = PromptCache(
            path=prompt_input.path,
            prompt_ordered_chunks=[chunk for chunk, _ in ordered_selected_chunks],
        )
        return PromptFormatterOutput([tokens]), new_cache

    def format_inline_signatures(self, results: Sequence[SymbolSignature]) -> TokenList:
        """Format inline lookup result as tokens."""
        sigs = "\n\n".join(sig.text for sig in results)
        return self.tokenizer.tokenize_safe(f"\n{sigs}\n")
