package auditocsf

// OCSF Class Constants
const (
	// Category UID for Identity & Access Management
	CategoryIAM = 3

	// Category UID for Application Activity
	CategoryApplicationActivity = 6

	// Class UID for Authentication
	ClassAuthentication = 3002

	// Class UID for Authorize Session
	ClassAuthorizeSession = 3003

	// Class UID for API Activity
	ClassAPIActivity = 6003

	// OCSF Type prefix for Authentication events
	OCSFTypePrefix = "type.ocsf.io/Authentication"
)

// Activity IDs for OCSF events
type ActivityID int

const (
	// Common activity IDs
	ActivityUnknown ActivityID = 0
	ActivityOther   ActivityID = 99

	// Authentication event activity IDs
	ActivityLogon                ActivityID = 1
	ActivityLogoff               ActivityID = 2
	ActivityAuthenticationTicket ActivityID = 3
	ActivityServiceTicketRequest ActivityID = 4
	ActivityServiceTicketRenew   ActivityID = 5
	ActivityPreauth              ActivityID = 6
)

// Authorize Session Activity IDs (reuse same numeric values as per OCSF spec)
const (
	ActivityAssignPrivileges ActivityID = 1
	ActivityAssignGroups     ActivityID = 2
)

// API Activity Activity IDs
const (
	ActivityAPICreate ActivityID = 1
	ActivityAPIRead   ActivityID = 2
	ActivityAPIUpdate ActivityID = 3
	ActivityAPIDelete ActivityID = 4
)

// String returns the string representation of ActivityID for Authentication events
func (a ActivityID) String() string {
	return a.AuthenticationString()
}

// AuthenticationString returns the string representation for Authentication events
func (a ActivityID) AuthenticationString() string {
	switch a {
	case ActivityUnknown:
		return "Unknown"
	case ActivityLogon:
		return "Logon"
	case ActivityLogoff:
		return "Logoff"
	case ActivityAuthenticationTicket:
		return "Authentication Ticket"
	case ActivityServiceTicketRequest:
		return "Service Ticket Request"
	case ActivityServiceTicketRenew:
		return "Service Ticket Renew"
	case ActivityPreauth:
		return "Preauth"
	case ActivityOther:
		return "Other"
	default:
		return "Unknown"
	}
}

// AuthorizeSessionString returns the string representation for Authorize Session events
func (a ActivityID) AuthorizeSessionString() string {
	switch a {
	case ActivityUnknown:
		return "Unknown"
	case ActivityAssignPrivileges:
		return "Assign Privileges"
	case ActivityAssignGroups:
		return "Assign Groups"
	case ActivityOther:
		return "Other"
	default:
		return "Unknown"
	}
}

// APIActivityString returns the string representation for API Activity events
func (a ActivityID) APIActivityString() string {
	switch a {
	case ActivityUnknown:
		return "Unknown"
	case ActivityAPICreate:
		return "Create"
	case ActivityAPIRead:
		return "Read"
	case ActivityAPIUpdate:
		return "Update"
	case ActivityAPIDelete:
		return "Delete"
	case ActivityOther:
		return "Other"
	default:
		return "Unknown"
	}
}

// Status IDs for event status
type StatusID int

const (
	StatusUnknown StatusID = 0
	StatusSuccess StatusID = 1
	StatusFailure StatusID = 2
	StatusOther   StatusID = 99
)

// String returns the string representation of StatusID
func (s StatusID) String() string {
	switch s {
	case StatusUnknown:
		return "Unknown"
	case StatusSuccess:
		return "Success"
	case StatusFailure:
		return "Failure"
	case StatusOther:
		return "Other"
	default:
		return "Unknown"
	}
}

// Severity IDs for event severity
type SeverityID int

const (
	SeverityUnknown       SeverityID = 0
	SeverityInformational SeverityID = 1
	SeverityLow           SeverityID = 2
	SeverityMedium        SeverityID = 3
	SeverityHigh          SeverityID = 4
	SeverityCritical      SeverityID = 5
	SeverityFatal         SeverityID = 6
	SeverityOther         SeverityID = 99
)

// String returns the string representation of SeverityID
func (s SeverityID) String() string {
	switch s {
	case SeverityUnknown:
		return "Unknown"
	case SeverityInformational:
		return "Informational"
	case SeverityLow:
		return "Low"
	case SeverityMedium:
		return "Medium"
	case SeverityHigh:
		return "High"
	case SeverityCritical:
		return "Critical"
	case SeverityFatal:
		return "Fatal"
	case SeverityOther:
		return "Other"
	default:
		return "Unknown"
	}
}

// Action IDs for actions taken
type ActionID int

const (
	ActionUnknown  ActionID = 0
	ActionAllowed  ActionID = 1
	ActionDenied   ActionID = 2
	ActionObserved ActionID = 3
	ActionModified ActionID = 4
	ActionOther    ActionID = 99
)

// String returns the string representation of ActionID
func (a ActionID) String() string {
	switch a {
	case ActionUnknown:
		return "Unknown"
	case ActionAllowed:
		return "Allowed"
	case ActionDenied:
		return "Denied"
	case ActionObserved:
		return "Observed"
	case ActionModified:
		return "Modified"
	case ActionOther:
		return "Other"
	default:
		return "Unknown"
	}
}

// Disposition IDs for outcomes
type DispositionID int

const (
	DispositionUnknown          DispositionID = 0
	DispositionAllowed          DispositionID = 1
	DispositionBlocked          DispositionID = 2
	DispositionQuarantined      DispositionID = 3
	DispositionIsolated         DispositionID = 4
	DispositionDeleted          DispositionID = 5
	DispositionDropped          DispositionID = 6
	DispositionCustom           DispositionID = 7
	DispositionApproved         DispositionID = 8
	DispositionRestored         DispositionID = 9
	DispositionExonerated       DispositionID = 10
	DispositionCorrected        DispositionID = 11
	DispositionPartiallyCorrect DispositionID = 12
	DispositionUncorrected      DispositionID = 13
	DispositionDelayed          DispositionID = 14
	DispositionDetected         DispositionID = 15
	DispositionNoAction         DispositionID = 16
	DispositionLogged           DispositionID = 17
	DispositionTagged           DispositionID = 18
	DispositionAlert            DispositionID = 19
	DispositionCount            DispositionID = 20
	DispositionReset            DispositionID = 21
	DispositionCaptcha          DispositionID = 22
	DispositionChallenge        DispositionID = 23
	DispositionAccessRevoked    DispositionID = 24
	DispositionRejected         DispositionID = 25
	DispositionUnauthorized     DispositionID = 26
	DispositionError            DispositionID = 27
	DispositionOther            DispositionID = 99
)

// String returns the string representation of DispositionID
func (d DispositionID) String() string {
	switch d {
	case DispositionUnknown:
		return "Unknown"
	case DispositionAllowed:
		return "Allowed"
	case DispositionBlocked:
		return "Blocked"
	case DispositionQuarantined:
		return "Quarantined"
	case DispositionIsolated:
		return "Isolated"
	case DispositionDeleted:
		return "Deleted"
	case DispositionDropped:
		return "Dropped"
	case DispositionCustom:
		return "Custom Action"
	case DispositionApproved:
		return "Approved"
	case DispositionRestored:
		return "Restored"
	case DispositionExonerated:
		return "Exonerated"
	case DispositionCorrected:
		return "Corrected"
	case DispositionPartiallyCorrect:
		return "Partially Corrected"
	case DispositionUncorrected:
		return "Uncorrected"
	case DispositionDelayed:
		return "Delayed"
	case DispositionDetected:
		return "Detected"
	case DispositionNoAction:
		return "No Action"
	case DispositionLogged:
		return "Logged"
	case DispositionTagged:
		return "Tagged"
	case DispositionAlert:
		return "Alert"
	case DispositionCount:
		return "Count"
	case DispositionReset:
		return "Reset"
	case DispositionCaptcha:
		return "Captcha"
	case DispositionChallenge:
		return "Challenge"
	case DispositionAccessRevoked:
		return "Access Revoked"
	case DispositionRejected:
		return "Rejected"
	case DispositionUnauthorized:
		return "Unauthorized"
	case DispositionError:
		return "Error"
	case DispositionOther:
		return "Other"
	default:
		return "Unknown"
	}
}

// Auth Protocol IDs for authentication protocols
type AuthProtocolID int

const (
	AuthProtocolUnknown  AuthProtocolID = 0
	AuthProtocolNTLM     AuthProtocolID = 1
	AuthProtocolKerberos AuthProtocolID = 2
	AuthProtocolDigest   AuthProtocolID = 3
	AuthProtocolOpenID   AuthProtocolID = 4
	AuthProtocolSAML     AuthProtocolID = 5
	AuthProtocolOAuth2   AuthProtocolID = 6
	AuthProtocolPAP      AuthProtocolID = 7
	AuthProtocolCHAP     AuthProtocolID = 8
	AuthProtocolEAP      AuthProtocolID = 9
	AuthProtocolRADIUS   AuthProtocolID = 10
	AuthProtocolBasic    AuthProtocolID = 11
	AuthProtocolLDAP     AuthProtocolID = 12
	AuthProtocolOther    AuthProtocolID = 99
)

// String returns the string representation of AuthProtocolID
func (a AuthProtocolID) String() string {
	switch a {
	case AuthProtocolUnknown:
		return "Unknown"
	case AuthProtocolNTLM:
		return "NTLM"
	case AuthProtocolKerberos:
		return "Kerberos"
	case AuthProtocolDigest:
		return "Digest"
	case AuthProtocolOpenID:
		return "OpenID"
	case AuthProtocolSAML:
		return "SAML"
	case AuthProtocolOAuth2:
		return "OAUTH 2.0"
	case AuthProtocolPAP:
		return "PAP"
	case AuthProtocolCHAP:
		return "CHAP"
	case AuthProtocolEAP:
		return "EAP"
	case AuthProtocolRADIUS:
		return "RADIUS"
	case AuthProtocolBasic:
		return "Basic Authentication"
	case AuthProtocolLDAP:
		return "LDAP"
	case AuthProtocolOther:
		return "Other"
	default:
		return "Unknown"
	}
}

// Logon Type IDs for logon types
type LogonTypeID int

const (
	LogonTypeUnknown                 LogonTypeID = 0
	LogonTypeSystem                  LogonTypeID = 1
	LogonTypeInteractive             LogonTypeID = 2
	LogonTypeNetwork                 LogonTypeID = 3
	LogonTypeBatch                   LogonTypeID = 4
	LogonTypeOSService               LogonTypeID = 5
	LogonTypeUnlock                  LogonTypeID = 7
	LogonTypeNetworkCleartext        LogonTypeID = 8
	LogonTypeNewCredentials          LogonTypeID = 9
	LogonTypeRemoteInteractive       LogonTypeID = 10
	LogonTypeCachedInteractive       LogonTypeID = 11
	LogonTypeCachedRemoteInteractive LogonTypeID = 12
	LogonTypeCachedUnlock            LogonTypeID = 13
	LogonTypeOther                   LogonTypeID = 99
)

// String returns the string representation of LogonTypeID
func (l LogonTypeID) String() string {
	switch l {
	case LogonTypeUnknown:
		return "Unknown"
	case LogonTypeSystem:
		return "System"
	case LogonTypeInteractive:
		return "Interactive"
	case LogonTypeNetwork:
		return "Network"
	case LogonTypeBatch:
		return "Batch"
	case LogonTypeOSService:
		return "OS Service"
	case LogonTypeUnlock:
		return "Unlock"
	case LogonTypeNetworkCleartext:
		return "Network Cleartext"
	case LogonTypeNewCredentials:
		return "New Credentials"
	case LogonTypeRemoteInteractive:
		return "Remote Interactive"
	case LogonTypeCachedInteractive:
		return "Cached Interactive"
	case LogonTypeCachedRemoteInteractive:
		return "Cached Remote Interactive"
	case LogonTypeCachedUnlock:
		return "Cached Unlock"
	case LogonTypeOther:
		return "Other"
	default:
		return "Unknown"
	}
}
