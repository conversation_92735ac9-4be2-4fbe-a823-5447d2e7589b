package auditocsf

import (
	"time"
)

// APIActivityEvent represents an OCSF API Activity event
//
// OCSF Profile System:
// The OCSF specification uses a profile system where the base class defines core fields,
// and additional profiles extend the class with specialized fields. The API Activity class
// supports multiple profiles:
//
// Base Profiles:
// - Base API Activity Class: Core fields for API operations
// - Classification Group: Event classification fields (inherited from BaseEvent)
// - Context Group: Additional context like confidence, risk assessment
// - Occurrence Group: Timing and occurrence fields (inherited from BaseEvent)
// - Primary Group: Main event data and disposition
//
// Extension Profiles:
// - Cloud Profile: Cloud environment details (cloud field)
// - Container Profile: Container-specific fields
// - Data Classification Profile: Data sensitivity classification
// - Date/Time Profile: Enhanced date/time handling
// - Host Profile: Host/endpoint information
// - OSINT Profile: Open Source Intelligence data (osint field)
// - Security Control Profile: Security control outcomes (is_alert field)
// - Trace Profile: Distributed tracing information (trace field)
//
// This implementation includes fields from multiple OCSF profiles as indicated in comments.
// When viewing the OCSF specification online, different profile tabs show different field sets.
type APIActivityEvent struct {
	BaseEvent

	// Required fields - Base API Activity Class
	Actor       Actor           `json:"actor"`        // Base: Required
	API         API             `json:"api"`          // Base: Required
	SrcEndpoint NetworkEndpoint `json:"src_endpoint"` // Base: Required

	// Required fields - Profile Extensions
	Cloud Cloud   `json:"cloud"`           // Cloud Profile: Required
	OSINT []OSINT `json:"osint,omitempty"` // OSINT Profile: Required

	// Recommended fields - Base API Activity Class
	DstEndpoint  *NetworkEndpoint  `json:"dst_endpoint,omitempty"`  // Base: Recommended
	HTTPRequest  *HTTPRequest      `json:"http_request,omitempty"`  // Base: Recommended
	HTTPResponse *HTTPResponse     `json:"http_response,omitempty"` // Base: Recommended
	Resources    []ResourceDetails `json:"resources,omitempty"`     // Base: Recommended
	Observables  []Observable      `json:"observables,omitempty"`   // Base: Recommended

	// Recommended fields - Profile Extensions
	IsAlert *bool  `json:"is_alert,omitempty"` // Security Control Profile: Recommended
	Trace   *Trace `json:"trace,omitempty"`    // Trace Profile: Recommended

	// Optional fields - Base API Activity Class
	Attacks        []Attack        `json:"attacks,omitempty"`        // Base: Optional
	Authorizations []Authorization `json:"authorizations,omitempty"` // Base: Optional
	Enrichments    []Enrichment    `json:"enrichments,omitempty"`    // Base: Optional
	FirewallRule   *FirewallRule   `json:"firewall_rule,omitempty"`  // Base: Optional
	Malware        []Malware       `json:"malware,omitempty"`        // Base: Optional
	Policy         *Policy         `json:"policy,omitempty"`         // Base: Optional
	Unmapped       map[string]any  `json:"unmapped,omitempty"`       // Base: Optional

	// Optional fields - Context Group (Base)
	Confidence      string `json:"confidence,omitempty"`       // Base Context: Optional
	ConfidenceID    *int   `json:"confidence_id,omitempty"`    // Base Context: Recommended
	ConfidenceScore *int   `json:"confidence_score,omitempty"` // Base Context: Optional
	RiskDetails     string `json:"risk_details,omitempty"`     // Base Context: Optional
	RiskLevel       string `json:"risk_level,omitempty"`       // Base Context: Optional
	RiskLevelID     *int   `json:"risk_level_id,omitempty"`    // Base Context: Optional
	RiskScore       *int   `json:"risk_score,omitempty"`       // Base Context: Optional

	// Optional fields - Primary Group (Base)
	Disposition     string           `json:"disposition,omitempty"`       // Base Primary: Optional
	DispositionID   *DispositionID   `json:"disposition_id,omitempty"`    // Base Primary: Recommended
	MalwareScanInfo *MalwareScanInfo `json:"malware_scan_info,omitempty"` // Base Primary: Optional
}

// Actor represents the actor object that describes details about the user/role/process
type Actor struct {
	User    *User    `json:"user,omitempty"`
	Process *Process `json:"process,omitempty"`
	Session *Session `json:"session,omitempty"`
}

// API represents details about a typical API call
type API struct {
	Operation string       `json:"operation"`
	Service   APIService   `json:"service"`
	Version   string       `json:"version,omitempty"`
	Request   *APIRequest  `json:"request,omitempty"`
	Response  *APIResponse `json:"response,omitempty"`
}

// APIService represents the API service information
type APIService struct {
	Name    string            `json:"name"`
	UID     string            `json:"uid,omitempty"`
	Version string            `json:"version,omitempty"`
	Labels  map[string]string `json:"labels,omitempty"`
}

// APIRequest represents the API request details
type APIRequest struct {
	UID   string         `json:"uid,omitempty"`
	Data  map[string]any `json:"data,omitempty"`
	Flags []string       `json:"flags,omitempty"`
}

// APIResponse represents the API response details
type APIResponse struct {
	Code    string         `json:"code,omitempty"`
	Error   string         `json:"error,omitempty"`
	Message string         `json:"message,omitempty"`
	Data    map[string]any `json:"data,omitempty"`
}

// Cloud represents details about the Cloud environment
type Cloud struct {
	Account  CloudAccount `json:"account"`
	Provider string       `json:"provider"`
	Region   string       `json:"region,omitempty"`
	Zone     string       `json:"zone,omitempty"`
	Project  *Project     `json:"project,omitempty"`
}

// CloudAccount represents cloud account information
type CloudAccount struct {
	Name   string            `json:"name,omitempty"`
	Type   string            `json:"type,omitempty"`
	TypeID int               `json:"type_id,omitempty"`
	UID    string            `json:"uid"`
	Labels map[string]string `json:"labels,omitempty"`
}

// Project represents cloud project information
type Project struct {
	Name   string            `json:"name,omitempty"`
	UID    string            `json:"uid,omitempty"`
	Labels map[string]string `json:"labels,omitempty"`
}

// HTTPRequest represents HTTP request details
type HTTPRequest struct {
	UID           string            `json:"uid,omitempty"`
	URL           *URL              `json:"url,omitempty"`
	Version       string            `json:"version,omitempty"`
	Method        string            `json:"method,omitempty"`
	Headers       map[string]string `json:"headers,omitempty"`
	UserAgent     string            `json:"user_agent,omitempty"`
	XForwardedFor []string          `json:"x_forwarded_for,omitempty"`
	Referrer      string            `json:"referrer,omitempty"`
	Args          string            `json:"args,omitempty"`
	Length        int               `json:"length,omitempty"`
}

// HTTPResponse represents HTTP response details
type HTTPResponse struct {
	Code    int               `json:"code,omitempty"`
	Message string            `json:"message,omitempty"`
	Headers map[string]string `json:"headers,omitempty"`
	Length  int               `json:"length,omitempty"`
	Latency int               `json:"latency,omitempty"`
}

// URL represents URL information
type URL struct {
	Text     string `json:"text,omitempty"`
	Hostname string `json:"hostname,omitempty"`
	Port     int    `json:"port,omitempty"`
	Path     string `json:"path,omitempty"`
	Query    string `json:"query,omitempty"`
	Fragment string `json:"fragment,omitempty"`
	Scheme   string `json:"scheme,omitempty"`
}

// Process represents process information
type Process struct {
	Name    string   `json:"name,omitempty"`
	PID     int      `json:"pid,omitempty"`
	UID     string   `json:"uid,omitempty"`
	File    *File    `json:"file,omitempty"`
	User    *User    `json:"user,omitempty"`
	Session *Session `json:"session,omitempty"`
}

// File represents file information
type File struct {
	Name string `json:"name,omitempty"`
	Path string `json:"path,omitempty"`
	Type string `json:"type,omitempty"`
	UID  string `json:"uid,omitempty"`
	Size int64  `json:"size,omitempty"`
}

// ResourceDetails represents details about resources that were affected
type ResourceDetails struct {
	Name   string            `json:"name,omitempty"`
	Type   string            `json:"type,omitempty"`
	UID    string            `json:"uid,omitempty"`
	Owner  *User             `json:"owner,omitempty"`
	Data   map[string]any    `json:"data,omitempty"`
	Labels map[string]string `json:"labels,omitempty"`
}

// Observable represents observable information
type Observable struct {
	Name       string      `json:"name"`
	Type       string      `json:"type"`
	TypeID     int         `json:"type_id"`
	Value      string      `json:"value"`
	Reputation *Reputation `json:"reputation,omitempty"`
}

// Reputation represents reputation information
type Reputation struct {
	BaseScore float64 `json:"base_score,omitempty"`
	Provider  string  `json:"provider,omitempty"`
	Score     string  `json:"score,omitempty"`
	ScoreID   int     `json:"score_id,omitempty"`
}

// Trace represents distributed trace information
type Trace struct {
	UID     string `json:"uid,omitempty"`
	SpanID  string `json:"span_id,omitempty"`
	TraceID string `json:"trace_id,omitempty"`
}

// SetDefaults sets default values for required fields
func (e *APIActivityEvent) SetDefaults() {
	if e.CategoryUID == 0 {
		e.CategoryUID = CategoryApplicationActivity
	}
	if e.ClassUID == 0 {
		e.ClassUID = ClassAPIActivity
	}
	if e.TypeUID == 0 {
		e.TypeUID = e.CalculateTypeUID()
	}
	if e.Time.IsZero() {
		e.Time = time.Now().UTC()
	}
	if e.SeverityID == 0 {
		e.SeverityID = SeverityInformational
	}
}

// Validate checks if the event has all required fields
func (e *APIActivityEvent) Validate() error {
	// Validate common BaseEvent fields
	if err := e.BaseEvent.Validate(); err != nil {
		return err
	}

	// APIActivityEvent has no additional required fields beyond BaseEvent
	// The Actor, API, Cloud, and SrcEndpoint fields are required by OCSF spec
	// but we'll allow them to be validated at the application level
	return nil
}

// Additional types needed for API Activity events

// Attack represents MITRE ATT&CK information
type Attack struct {
	Version    string      `json:"version,omitempty"`
	Tactics    []Tactic    `json:"tactics,omitempty"`
	Techniques []Technique `json:"techniques,omitempty"`
}

// Tactic represents MITRE ATT&CK tactic
type Tactic struct {
	Name string `json:"name,omitempty"`
	UID  string `json:"uid,omitempty"`
}

// Technique represents MITRE ATT&CK technique
type Technique struct {
	Name string `json:"name,omitempty"`
	UID  string `json:"uid,omitempty"`
}

// Authorization represents authorization information
type Authorization struct {
	Decision string `json:"decision,omitempty"`
	Policy   string `json:"policy,omitempty"`
}

// Enrichment represents additional information from external data sources
type Enrichment struct {
	Name  string         `json:"name"`
	Value string         `json:"value"`
	Type  string         `json:"type"`
	Data  map[string]any `json:"data,omitempty"`
}

// FirewallRule represents firewall rule information
type FirewallRule struct {
	Name      string `json:"name,omitempty"`
	UID       string `json:"uid,omitempty"`
	Category  string `json:"category,omitempty"`
	Condition string `json:"condition,omitempty"`
}

// Malware represents malware information
type Malware struct {
	Name           string   `json:"name,omitempty"`
	Path           string   `json:"path,omitempty"`
	Classification []string `json:"classification,omitempty"`
}

// MalwareScanInfo represents malware scan information
type MalwareScanInfo struct {
	ScannerName string `json:"scanner_name,omitempty"`
	ScanTime    string `json:"scan_time,omitempty"`
	Result      string `json:"result,omitempty"`
}

// OSINT represents Open Source Intelligence information
type OSINT struct {
	Provider string         `json:"provider,omitempty"`
	Data     map[string]any `json:"data,omitempty"`
}

// Policy represents policy information
type Policy struct {
	Name    string `json:"name,omitempty"`
	UID     string `json:"uid,omitempty"`
	Version string `json:"version,omitempty"`
}

// APIActivityEventBuilder provides a fluent interface for building APIActivityEvent objects
type APIActivityEventBuilder struct {
	event APIActivityEvent
}

// NewAPIActivityEventBuilder creates a new builder with default values
func NewAPIActivityEventBuilder(activityID ActivityID) *APIActivityEventBuilder {
	return &APIActivityEventBuilder{
		event: APIActivityEvent{
			BaseEvent: BaseEvent{
				CategoryUID: CategoryApplicationActivity,
				ClassUID:    ClassAPIActivity,
				SeverityID:  SeverityInformational,
				Time:        time.Now().UTC(),
				ActivityID:  activityID,
				TypeUID:     int64(ClassAPIActivity)*100 + int64(activityID),
				Metadata: Metadata{
					Version: "1.5.0",
					Product: &Product{
						Name:   "Augment",
						Vendor: "Augment Code",
					},
					LogProvider: "augment-audit-ocsf",
				},
			},
		},
	}
}

// WithSeverity sets the severity level
func (b *APIActivityEventBuilder) WithSeverity(severityID SeverityID) *APIActivityEventBuilder {
	b.event.SeverityID = severityID
	return b
}

// WithStatus sets the status information
func (b *APIActivityEventBuilder) WithStatus(statusID StatusID) *APIActivityEventBuilder {
	b.event.StatusID = &statusID
	return b
}

// WithStatusDetail sets the status detail information
func (b *APIActivityEventBuilder) WithStatusDetail(statusCode, statusDetail string) *APIActivityEventBuilder {
	if statusCode != "" {
		b.event.StatusCode = statusCode
	}
	if statusDetail != "" {
		b.event.StatusDetail = statusDetail
	}
	return b
}

// WithAction sets the action taken
func (b *APIActivityEventBuilder) WithAction(actionID ActionID) *APIActivityEventBuilder {
	b.event.ActionID = &actionID
	return b
}

// WithMessage sets the event message
func (b *APIActivityEventBuilder) WithMessage(message string) *APIActivityEventBuilder {
	b.event.Message = message
	return b
}

// WithTime sets the event time
func (b *APIActivityEventBuilder) WithTime(eventTime time.Time) *APIActivityEventBuilder {
	b.event.Time = eventTime
	return b
}

// WithDevice sets the device information
func (b *APIActivityEventBuilder) WithDevice(device Device) *APIActivityEventBuilder {
	b.event.Device = &device
	return b
}

// WithActor sets the actor information (Base API Activity: Required)
func (b *APIActivityEventBuilder) WithActor(actor Actor) *APIActivityEventBuilder {
	b.event.Actor = actor
	return b
}

// WithAPI sets the API information (Base API Activity: Required)
func (b *APIActivityEventBuilder) WithAPI(api API) *APIActivityEventBuilder {
	b.event.API = api
	return b
}

// WithCloud sets the cloud information (Cloud Profile: Required)
func (b *APIActivityEventBuilder) WithCloud(cloud Cloud) *APIActivityEventBuilder {
	b.event.Cloud = cloud
	return b
}

// WithSrcEndpoint sets the source endpoint information (Base API Activity: Required)
func (b *APIActivityEventBuilder) WithSrcEndpoint(endpoint NetworkEndpoint) *APIActivityEventBuilder {
	b.event.SrcEndpoint = endpoint
	return b
}

// WithDstEndpoint sets the destination endpoint information (Base API Activity: Recommended)
func (b *APIActivityEventBuilder) WithDstEndpoint(endpoint NetworkEndpoint) *APIActivityEventBuilder {
	b.event.DstEndpoint = &endpoint
	return b
}

// WithHTTPRequest sets the HTTP request information (Base API Activity: Recommended)
func (b *APIActivityEventBuilder) WithHTTPRequest(request HTTPRequest) *APIActivityEventBuilder {
	b.event.HTTPRequest = &request
	return b
}

// WithHTTPResponse sets the HTTP response information (Base API Activity: Recommended)
func (b *APIActivityEventBuilder) WithHTTPResponse(response HTTPResponse) *APIActivityEventBuilder {
	b.event.HTTPResponse = &response
	return b
}

// WithIsAlert sets whether this is an alertable event (Security Control Profile: Recommended)
func (b *APIActivityEventBuilder) WithIsAlert(isAlert bool) *APIActivityEventBuilder {
	b.event.IsAlert = &isAlert
	return b
}

// WithResources sets the resources affected by the activity (Base API Activity: Recommended)
func (b *APIActivityEventBuilder) WithResources(resources []ResourceDetails) *APIActivityEventBuilder {
	b.event.Resources = resources
	return b
}

// WithDisposition sets the disposition information
func (b *APIActivityEventBuilder) WithDisposition(dispositionID DispositionID) *APIActivityEventBuilder {
	b.event.DispositionID = &dispositionID
	return b
}

// WithTimezoneOffset sets the timezone offset
func (b *APIActivityEventBuilder) WithTimezoneOffset(offset int) *APIActivityEventBuilder {
	b.event.TimezoneOffset = &offset
	return b
}

// WithRawData sets the raw event data
func (b *APIActivityEventBuilder) WithRawData(rawData string) *APIActivityEventBuilder {
	b.event.RawData = rawData
	return b
}

// Build creates the final APIActivityEvent
func (b *APIActivityEventBuilder) Build() APIActivityEvent {
	// Ensure type_uid is calculated if not already set
	if b.event.TypeUID == 0 {
		b.event.TypeUID = int64(b.event.ClassUID)*100 + int64(b.event.ActivityID)
	}

	// Set default metadata if none provided
	if b.event.Metadata.Version == "" {
		b.event.Metadata = Metadata{
			Version: "1.5.0",
			Product: &Product{
				Name:   "Augment",
				Vendor: "Augment Code",
			},
			LogProvider: "augment-audit-ocsf",
		}
	}

	return b.event
}
