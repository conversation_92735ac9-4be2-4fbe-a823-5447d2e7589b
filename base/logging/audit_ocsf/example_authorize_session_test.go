package auditocsf_test

import (
	"fmt"

	auditocsf "github.com/augmentcode/augment/base/logging/audit_ocsf"
)

// Example demonstrates how to use the AuthorizeSessionEvent
func Example_authorizeSession() {
	// Create a logger that doesn't output to stdout for this example
	logger := auditocsf.NewOCSFAuditLogger(discardPrinter{})

	// Example 1: Assign privileges to a user session
	privilegesEvent := auditocsf.NewAssignPrivilegesEvent(
		"admin123",
		"INTERNAL_IAP",
		[]string{"SeDebugPrivilege", "SeBackupPrivilege", "SeRestorePrivilege"},
	).WithMessage("Administrative privileges assigned for system maintenance").
		Build()

	logger.LogAuthorizeSession(privilegesEvent)

	// Example 2: Assign group membership to a user session
	adminGroup := auditocsf.Group{
		Name: "Domain Admins",
		UID:  "S-1-5-21-1234567890-987654321-1122334455-512",
		Type: "Windows Group",
	}

	groupEvent := auditocsf.NewAssignGroupsEvent(
		"user456",
		"INTERNAL_IAP",
		adminGroup,
	).WithMessage("User added to administrative group").
		WithSeverity(auditocsf.SeverityMedium). // Higher severity for admin group assignment
		Build()

	logger.LogAuthorizeSession(groupEvent)

	// Example 3: Using the generic builder for complex scenarios
	complexEvent := auditocsf.NewAuthorizeSessionEventBuilder(auditocsf.ActivityAssignPrivileges).
		WithUserUID("service123", "SERVICE_ACCOUNT").
		WithPrivileges([]string{"SeServiceLogonRight", "SeIncreaseQuotaPrivilege"}).
		WithMessage("Service account privileges configured").
		WithStatus(auditocsf.StatusSuccess).
		WithAction(auditocsf.ActionAllowed).
		WithSeverity(auditocsf.SeverityInformational).
		WithSession(auditocsf.Session{
			UID:      "session-789",
			IsRemote: false,
			IsMFA:    false,
		}).
		Build()

	logger.LogAuthorizeSession(complexEvent)

	fmt.Println("Authorize session events logged successfully")
	// Output: Authorize session events logged successfully
}

// Example demonstrates the difference between Authentication and Authorize Session events
func Example_authenticationVsAuthorizeSession() {
	logger := auditocsf.NewOCSFAuditLogger(discardPrinter{})

	// Step 1: User authenticates (Authentication event)
	authEvent := auditocsf.NewSuccessfulLogonEvent("user123", "INTERNAL_IAP").
		WithMessage("User successfully authenticated").
		WithMFA(true).
		WithAuthProtocol(auditocsf.AuthProtocolKerberos).
		Build()

	logger.LogAuthentication(authEvent)

	// Step 2: System assigns privileges to the authenticated session (Authorize Session event)
	privilegeEvent := auditocsf.NewAssignPrivilegesEvent(
		"user123",
		"INTERNAL_IAP",
		[]string{"SeRemoteInteractiveLogonRight"},
	).WithMessage("Remote desktop privileges assigned to authenticated session").
		Build()

	logger.LogAuthorizeSession(privilegeEvent)

	fmt.Println("Authentication and authorization events logged")
	// Output: Authentication and authorization events logged
}
