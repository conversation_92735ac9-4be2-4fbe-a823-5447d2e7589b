package auditocsf

import (
	"encoding/json"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestOCSFAuditLogger_LogUserLogon(t *testing.T) {
	mockPrinter := &MockPrinter{}
	logger := NewOCSFAuditLogger(mockPrinter)

	err := logger.LogUserLogon("user123", "INTERNAL_IAP", "tenant456", "User logged in successfully")
	require.NoError(t, err)

	output := mockPrinter.GetOutput()
	require.Len(t, output, 1)

	var auditLog OCSFAuditLog
	err = json.Unmarshal([]byte(output[0]), &auditLog)
	require.NoError(t, err)

	// Verify wrapper fields
	assert.Equal(t, "type.eng.augmentcode.com/OCSFAuditLog", auditLog.Type)
	assert.Equal(t, "tenant456", auditLog.Tenant.Name)

	// Extract the nested event
	eventBytes, err := json.Marshal(auditLog.OCSFEvent)
	require.NoError(t, err)

	var event AuthenticationEvent
	err = json.Unmarshal(eventBytes, &event)
	require.NoError(t, err)

	// Verify required fields
	assert.Equal(t, ActivityLogon, event.ActivityID)
	assert.Equal(t, CategoryIAM, event.CategoryUID)
	assert.Equal(t, ClassAuthentication, event.ClassUID)
	assert.Equal(t, int64(300201), event.TypeUID)
	assert.Equal(t, "user123", event.User.UID)
	assert.Equal(t, "INTERNAL_IAP", event.User.Type)
	assert.Equal(t, "User logged in successfully", event.Message)
	assert.Equal(t, "1.5.0", event.Metadata.Version)

	// Verify recommended fields
	assert.NotNil(t, event.StatusID)
	assert.Equal(t, StatusSuccess, *event.StatusID)
}

func TestOCSFAuditLogger_LogFailedAuthentication(t *testing.T) {
	mockPrinter := &MockPrinter{}
	logger := NewOCSFAuditLogger(mockPrinter)

	err := logger.LogFailedAuthentication("user123", "INTERNAL_IAP", "tenant456", "Authentication failed", "INVALID_CREDENTIALS")
	require.NoError(t, err)

	output := mockPrinter.GetOutput()
	require.Len(t, output, 1)

	var auditLog OCSFAuditLog
	err = json.Unmarshal([]byte(output[0]), &auditLog)
	require.NoError(t, err)

	// Verify wrapper fields
	assert.Equal(t, "type.eng.augmentcode.com/OCSFAuditLog", auditLog.Type)
	assert.Equal(t, "tenant456", auditLog.Tenant.Name)

	// Extract the nested event
	eventBytes, err := json.Marshal(auditLog.OCSFEvent)
	require.NoError(t, err)

	var event AuthenticationEvent
	err = json.Unmarshal(eventBytes, &event)
	require.NoError(t, err)

	assert.Equal(t, ActivityLogon, event.ActivityID)
	assert.Equal(t, SeverityMedium, event.SeverityID)
	assert.Equal(t, "Authentication failed", event.Message)
	assert.Equal(t, "INVALID_CREDENTIALS", event.StatusDetail)

	// Verify failure-specific fields
	assert.NotNil(t, event.StatusID)
	assert.Equal(t, StatusFailure, *event.StatusID)
	assert.NotNil(t, event.ActionID)
	assert.Equal(t, ActionDenied, *event.ActionID)
}

func TestBaseEvent_Validate(t *testing.T) {
	validBaseEvent := BaseEvent{
		ActivityID:  ActivityLogon,
		CategoryUID: CategoryIAM,
		ClassUID:    ClassAuthentication,
		SeverityID:  SeverityInformational,
		Time:        time.Now(),
		TypeUID:     300201,
		Metadata: Metadata{
			Version: "1.5.0",
		},
	}

	t.Run("Valid base event", func(t *testing.T) {
		err := validBaseEvent.Validate()
		assert.NoError(t, err)
	})

	t.Run("Missing activity_id", func(t *testing.T) {
		event := validBaseEvent
		event.ActivityID = 0
		err := event.Validate()
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "activity_id is required")
	})

	t.Run("Missing category_uid", func(t *testing.T) {
		event := validBaseEvent
		event.CategoryUID = 0
		err := event.Validate()
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "category_uid is required")
	})

	t.Run("Missing class_uid", func(t *testing.T) {
		event := validBaseEvent
		event.ClassUID = 0
		err := event.Validate()
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "class_uid is required")
	})

	t.Run("Missing severity_id", func(t *testing.T) {
		event := validBaseEvent
		event.SeverityID = 0
		err := event.Validate()
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "severity_id is required")
	})

	t.Run("Missing time", func(t *testing.T) {
		event := validBaseEvent
		event.Time = time.Time{}
		err := event.Validate()
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "time is required")
	})

	t.Run("Missing type_uid", func(t *testing.T) {
		event := validBaseEvent
		event.TypeUID = 0
		err := event.Validate()
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "type_uid is required")
	})

	t.Run("Missing metadata version", func(t *testing.T) {
		event := validBaseEvent
		event.Metadata.Version = ""
		err := event.Validate()
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "metadata.version is required")
	})
}

func TestBaseEvent_CalculateTypeUID(t *testing.T) {
	tests := []struct {
		name       string
		classUID   int
		activityID ActivityID
		expected   int64
	}{
		{"Authentication Logon", ClassAuthentication, ActivityLogon, 300201},
		{"Authentication Logoff", ClassAuthentication, ActivityLogoff, 300202},
		{"Authorize Session Assign Privileges", ClassAuthorizeSession, ActivityAssignPrivileges, 300301},
		{"Authorize Session Assign Groups", ClassAuthorizeSession, ActivityAssignGroups, 300302},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			event := BaseEvent{
				ClassUID:   tt.classUID,
				ActivityID: tt.activityID,
			}
			assert.Equal(t, tt.expected, event.CalculateTypeUID())
		})
	}
}

func TestNewDefaultOCSFAuditLogger(t *testing.T) {
	logger := NewDefaultOCSFAuditLogger()

	assert.NotNil(t, logger)
	assert.NotNil(t, logger.print)
}

func TestStdoutPrinter(t *testing.T) {
	printer := NewStdoutPrinter()

	// This test just ensures the printer doesn't panic
	// In a real scenario, you might want to capture stdout
	printer.Println("Test output")

	// Verify the printer has the expected structure
	assert.NotNil(t, printer.output)
}

func TestOCSFAuditLogger_WithoutEnvField(t *testing.T) {
	// Test that the logger works correctly without the env field
	mockPrinter := &MockPrinter{}
	logger := NewOCSFAuditLogger(mockPrinter)

	// Test that we can log events without any env-related issues
	err := logger.LogUserLogon("user123", "INTERNAL_IAP", "tenant456", "Test logon without env")
	require.NoError(t, err)

	output := mockPrinter.GetOutput()
	require.Len(t, output, 1)

	var auditLog OCSFAuditLog
	err = json.Unmarshal([]byte(output[0]), &auditLog)
	require.NoError(t, err)

	// Verify wrapper fields
	assert.Equal(t, "type.eng.augmentcode.com/OCSFAuditLog", auditLog.Type)
	assert.Equal(t, "tenant456", auditLog.Tenant.Name)

	// Extract the nested event
	eventBytes, err := json.Marshal(auditLog.OCSFEvent)
	require.NoError(t, err)

	var event AuthenticationEvent
	err = json.Unmarshal(eventBytes, &event)
	require.NoError(t, err)

	// Verify the event was logged correctly
	assert.Equal(t, ActivityLogon, event.ActivityID)
	assert.Equal(t, "user123", event.User.UID)
	assert.Equal(t, "INTERNAL_IAP", event.User.Type)
	assert.Equal(t, "Test logon without env", event.Message)
}
