package auditocsf

import (
	"time"
)

// AuthenticationEvent represents an OCSF Authentication event
type AuthenticationEvent struct {
	BaseEvent
	// required
	User User `json:"user"`
	// Authentication-specific fields
	IsRemote       *bool  `json:"is_remote,omitempty"`
	IsMFA          *bool  `json:"is_mfa,omitempty"`
	IsCleartext    *bool  `json:"is_cleartext,omitempty"`
	AuthProtocol   string `json:"auth_protocol,omitempty"`
	AuthProtocolID *int   `json:"auth_protocol_id"`
	LogonType      string `json:"logon_type,omitempty"`
	LogonTypeID    *int   `json:"logon_type_id,omitempty"`
}

// SetDefaults sets default values for required fields
func (e *AuthenticationEvent) SetDefaults() {
	e.CategoryUID = CategoryIAM
	e.ClassUID = ClassAuthentication
	e.TypeUID = e.CalculateTypeUID()

	if e.Time.IsZero() {
		e.Time = time.Now().UTC()
	}

	if e.SeverityID == 0 {
		e.SeverityID = SeverityInformational
	}
}

// Validate checks if the event has all required fields
func (e *AuthenticationEvent) Validate() error {
	// Validate common BaseEvent fields
	if err := e.BaseEvent.Validate(); err != nil {
		return err
	}

	// AuthenticationEvent has no additional required fields beyond BaseEvent
	return nil
}

// AuthenticationEventBuilder provides a fluent interface for building AuthenticationEvent objects
type AuthenticationEventBuilder struct {
	event AuthenticationEvent
}

// NewAuthenticationEventBuilder creates a new builder with default values
func NewAuthenticationEventBuilder(activityID ActivityID) *AuthenticationEventBuilder {
	return &AuthenticationEventBuilder{
		event: AuthenticationEvent{
			BaseEvent: BaseEvent{
				CategoryUID: CategoryIAM,
				ClassUID:    ClassAuthentication,
				SeverityID:  SeverityInformational,
				Time:        time.Now().UTC(),
				ActivityID:  activityID,
				TypeUID:     int64(ClassAuthentication)*100 + int64(activityID),
				Metadata: Metadata{
					Version: "1.5.0",
					Product: &Product{
						Name:   "Augment",
						Vendor: "Augment Code",
					},
					LogProvider: "augment-audit-ocsf",
				},
			},
		},
	}
}

// WithUser sets the user information
func (b *AuthenticationEventBuilder) WithUser(user User) *AuthenticationEventBuilder {
	b.event.User = user
	return b
}

// WithUserUID sets the user UID and type
func (b *AuthenticationEventBuilder) WithUserUID(uid, userType string) *AuthenticationEventBuilder {
	b.event.User.UID = uid
	b.event.User.Type = userType
	return b
}

// WithUserName sets the user name and type
func (b *AuthenticationEventBuilder) WithUserName(name, userType string) *AuthenticationEventBuilder {
	b.event.User.Name = name
	b.event.User.Type = userType
	return b
}

// WithSeverity sets the severity level
func (b *AuthenticationEventBuilder) WithSeverity(severityID SeverityID) *AuthenticationEventBuilder {
	b.event.SeverityID = severityID
	return b
}

// WithStatus sets the status information
func (b *AuthenticationEventBuilder) WithStatus(statusID StatusID) *AuthenticationEventBuilder {
	b.event.StatusID = &statusID
	return b
}

// WithStatusDetail sets the status detail information
func (b *AuthenticationEventBuilder) WithStatusDetail(statusCode, statusDetail string) *AuthenticationEventBuilder {
	if statusCode != "" {
		b.event.StatusCode = statusCode
	}
	if statusDetail != "" {
		b.event.StatusDetail = statusDetail
	}
	return b
}

// WithAction sets the action taken
func (b *AuthenticationEventBuilder) WithAction(actionID ActionID) *AuthenticationEventBuilder {
	b.event.ActionID = &actionID
	return b
}

// WithMessage sets the event message
func (b *AuthenticationEventBuilder) WithMessage(message string) *AuthenticationEventBuilder {
	b.event.Message = message
	return b
}

// WithTime sets the event time
func (b *AuthenticationEventBuilder) WithTime(eventTime time.Time) *AuthenticationEventBuilder {
	b.event.Time = eventTime
	return b
}

// WithDevice sets the device information
func (b *AuthenticationEventBuilder) WithDevice(device Device) *AuthenticationEventBuilder {
	b.event.Device = &device
	return b
}

// WithSession sets the session information
func (b *AuthenticationEventBuilder) WithSession(session Session) *AuthenticationEventBuilder {
	b.event.Session = &session
	return b
}

// WithTimezoneOffset sets the timezone offset
func (b *AuthenticationEventBuilder) WithTimezoneOffset(offset int) *AuthenticationEventBuilder {
	b.event.TimezoneOffset = &offset
	return b
}

// WithRawData sets the raw event data
func (b *AuthenticationEventBuilder) WithRawData(rawData string) *AuthenticationEventBuilder {
	b.event.RawData = rawData
	return b
}

// WithRemote sets whether the authentication is remote
func (b *AuthenticationEventBuilder) WithRemote(isRemote bool) *AuthenticationEventBuilder {
	b.event.IsRemote = &isRemote
	return b
}

// WithMFA sets whether multi-factor authentication was used
func (b *AuthenticationEventBuilder) WithMFA(isMFA bool) *AuthenticationEventBuilder {
	b.event.IsMFA = &isMFA
	return b
}

// WithCleartext sets whether the authentication used cleartext
func (b *AuthenticationEventBuilder) WithCleartext(isCleartext bool) *AuthenticationEventBuilder {
	b.event.IsCleartext = &isCleartext
	return b
}

// WithAuthProtocol sets the authentication protocol
func (b *AuthenticationEventBuilder) WithAuthProtocol(protocolID AuthProtocolID) *AuthenticationEventBuilder {
	protocol := int(protocolID)
	b.event.AuthProtocolID = &protocol
	b.event.AuthProtocol = protocolID.String()
	return b
}

// WithLogonType sets the logon type
func (b *AuthenticationEventBuilder) WithLogonType(logonTypeID LogonTypeID) *AuthenticationEventBuilder {
	logonType := int(logonTypeID)
	b.event.LogonTypeID = &logonType
	b.event.LogonType = logonTypeID.String()
	return b
}

// WithSrcEndpoint sets the source endpoint information (Recommended for Authentication)
func (b *AuthenticationEventBuilder) WithSrcEndpoint(endpoint NetworkEndpoint) *AuthenticationEventBuilder {
	b.event.SrcEndpoint = &endpoint
	return b
}

// WithDstEndpoint sets the destination endpoint information (Recommended for Authentication)
func (b *AuthenticationEventBuilder) WithDstEndpoint(endpoint NetworkEndpoint) *AuthenticationEventBuilder {
	b.event.DstEndpoint = &endpoint
	return b
}

// Build creates the final AuthenticationEvent
func (b *AuthenticationEventBuilder) Build() AuthenticationEvent {
	// Ensure type_uid is calculated if not already set
	if b.event.TypeUID == 0 {
		b.event.TypeUID = int64(b.event.ClassUID)*100 + int64(b.event.ActivityID)
	}

	// Set default metadata if none provided
	if b.event.Metadata.Version == "" {
		b.event.Metadata = Metadata{
			Version: "1.5.0",
			Product: &Product{
				Name:   "Augment",
				Vendor: "Augment Code",
			},
			LogProvider: "augment-audit-ocsf",
		}
	}

	return b.event
}

// Convenience builder functions for common authentication scenarios

// NewSuccessfulLogonEvent creates a builder for a successful logon event
func NewSuccessfulLogonEvent(userID, userType string) *AuthenticationEventBuilder {
	statusID := StatusSuccess
	actionID := ActionAllowed

	return NewAuthenticationEventBuilder(ActivityLogon).
		WithUserUID(userID, userType).
		WithStatus(statusID).
		WithAction(actionID).
		WithSeverity(SeverityInformational)
}

// NewFailedLogonEvent creates a builder for a failed logon event
func NewFailedLogonEvent(userID, userType string) *AuthenticationEventBuilder {
	statusID := StatusFailure
	actionID := ActionDenied

	return NewAuthenticationEventBuilder(ActivityLogon).
		WithUserUID(userID, userType).
		WithStatus(statusID).
		WithAction(actionID).
		WithSeverity(SeverityMedium)
}

// NewLogoffEvent creates a builder for a logoff event
func NewLogoffEvent(userID, userType string) *AuthenticationEventBuilder {
	statusID := StatusSuccess
	actionID := ActionAllowed

	return NewAuthenticationEventBuilder(ActivityLogoff).
		WithUserUID(userID, userType).
		WithStatus(statusID).
		WithAction(actionID).
		WithSeverity(SeverityInformational)
}
