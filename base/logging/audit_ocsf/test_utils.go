package auditocsf

import (
	"sync"
)

// MockPrinter is a test utility that captures printed output for testing purposes.
// This allows other projects to create mocked OCSF audit loggers for testing.
//
// Example usage in tests:
//
//	mockPrinter := &auditocsf.MockPrinter{}
//	logger := auditocsf.NewOCSFAuditLogger(mockPrinter)
//
//	// Use logger in your code...
//	logger.LogAuthentication(event)
//
//	// Verify output in tests
//	output := mockPrinter.GetOutput()
//	assert.Len(t, output, 1)
//	assert.Contains(t, output[0], "expected content")
type MockPrinter struct {
	mu     sync.Mutex
	output []string
}

// Println captures the printed output for later inspection
func (m *MockPrinter) Println(args ...any) {
	m.mu.Lock()
	defer m.mu.Unlock()

	// Convert all args to strings and join them
	var parts []string
	for _, arg := range args {
		if str, ok := arg.(string); ok {
			parts = append(parts, str)
		} else {
			parts = append(parts, "")
		}
	}

	// Store the first string argument (which should be the JSON)
	if len(parts) > 0 {
		m.output = append(m.output, parts[0])
	}
}

// GetOutput returns all captured output
func (m *MockPrinter) GetOutput() []string {
	m.mu.Lock()
	defer m.mu.Unlock()

	// Return a copy to avoid race conditions
	result := make([]string, len(m.output))
	copy(result, m.output)
	return result
}

// GetLastOutput returns the most recent output, or empty string if none
func (m *MockPrinter) GetLastOutput() string {
	m.mu.Lock()
	defer m.mu.Unlock()

	if len(m.output) == 0 {
		return ""
	}
	return m.output[len(m.output)-1]
}

// Clear removes all captured output
func (m *MockPrinter) Clear() {
	m.mu.Lock()
	defer m.mu.Unlock()
	m.output = nil
}

// Count returns the number of captured outputs
func (m *MockPrinter) Count() int {
	m.mu.Lock()
	defer m.mu.Unlock()
	return len(m.output)
}

// NewMockOCSFAuditLogger creates a new OCSF audit logger with a MockPrinter
// for testing purposes. This is a convenience function for tests.
//
// Example usage:
//
//	logger, mockPrinter := auditocsf.NewMockOCSFAuditLogger()
//
//	// Use logger in your code...
//	logger.LogAuthentication(event)
//
//	// Verify output
//	output := mockPrinter.GetOutput()
//	assert.Len(t, output, 1)
func NewMockOCSFAuditLogger() (*OCSFAuditLogger, *MockPrinter) {
	mockPrinter := &MockPrinter{}
	logger := NewOCSFAuditLogger(mockPrinter)
	return logger, mockPrinter
}
