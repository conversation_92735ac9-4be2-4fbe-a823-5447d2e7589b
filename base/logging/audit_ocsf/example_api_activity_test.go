package auditocsf

import (
	"fmt"
	"os"
	"time"
)

// discardPrinter implements Printer interface but discards output for examples
type discardPrinter struct{}

func (d discardPrinter) Println(args ...any) {}

// Example_apiActivity demonstrates how to create and log API Activity events
// This example shows usage of fields from multiple OCSF profiles:
// - Base API Activity fields (actor, api, src_endpoint, http_request, http_response, etc.)
// - Cloud Profile fields (cloud)
// - Security Control Profile fields (is_alert, disposition)
// - Trace Profile fields (trace)
func Example_apiActivity() {
	// Create a logger that doesn't output to stdout for this example
	logger := NewOCSFAuditLogger(discardPrinter{})

	// Example 1: Basic API Create operation
	// This example uses fields from multiple OCSF profiles
	createEvent := NewAPIActivityEventBuilder(ActivityAPICreate).
		WithMessage("User created a new resource"). // BaseEvent field
		WithAPI(API{                                // Base API Activity: Required
			Operation: "CreateResource",
			Service: APIService{
				Name:    "ResourceService",
				UID:     "service-123",
				Version: "v1.2.0",
			},
			Version: "2.0",
		}).
		WithCloud(Cloud{ // Cloud Profile: Required
			Provider: "AWS",
			Region:   "us-east-1",
			Account: CloudAccount{
				UID:  "************",
				Name: "production",
				Type: "AWS Account",
			},
		}).
		WithSrcEndpoint(NetworkEndpoint{ // Base API Activity: Required
			IP:       "*************",
			Hostname: "client.example.com",
			Port:     443,
		}).
		WithHTTPRequest(HTTPRequest{ // Base API Activity: Recommended
			Method:    "POST",
			UserAgent: "MyApp/1.0",
			URL: &URL{
				Text:     "https://api.example.com/resources",
				Hostname: "api.example.com",
				Path:     "/resources",
				Scheme:   "https",
				Port:     443,
			},
		}).
		WithHTTPResponse(HTTPResponse{ // Base API Activity: Recommended
			Code:    201,
			Message: "Created",
			Length:  256,
			Latency: 150,
		}).
		WithSeverity(SeverityInformational). // BaseEvent field
		Build()

	// Log the create event
	if err := logger.LogAPIActivity(createEvent); err != nil {
		fmt.Fprintf(os.Stderr, "Error logging API activity: %v\n", err)
		return
	}

	// Example 2: Complex API Read operation with detailed context
	readEvent := NewAPIActivityEventBuilder(ActivityAPIRead).
		WithMessage("User accessed resource data").
		WithAction(ActionAllowed).
		WithAPI(API{
			Operation: "GetResource",
			Service: APIService{
				Name: "ResourceService",
				UID:  "service-123",
			},
			Request: &APIRequest{
				UID: "req-456",
				Data: map[string]any{
					"resource_id": "res-123",
					"fields":      []string{"name", "status", "created_at"},
				},
			},
			Response: &APIResponse{
				Code:    "200",
				Message: "OK",
				Data: map[string]any{
					"resource": map[string]any{
						"id":         "res-123",
						"name":       "My Resource",
						"status":     "active",
						"created_at": "2024-01-10T08:00:00Z",
					},
				},
			},
		}).
		WithActor(Actor{
			User: &User{
				Name: "John Doe",
				UID:  "actor456",
				Type: "EXTERNAL_USER",
			},
			Session: &Session{
				UID:         "session-789",
				CreatedTime: time.Now().Add(-24 * time.Hour).UTC(),
				IsRemote:    true,
			},
		}).
		WithSrcEndpoint(NetworkEndpoint{
			IP:       "*********",
			Hostname: "mobile-client.example.com",
		}).
		WithCloud(Cloud{
			Provider: "GCP",
			Region:   "us-central1",
			Zone:     "us-central1-a",
			Account: CloudAccount{
				UID:  "gcp-project-456",
				Name: "staging",
				Type: "GCP Project",
			},
		}).
		WithResources([]ResourceDetails{
			{
				Name: "UserResource",
				Type: "Database Record",
				UID:  "res-123",
				Owner: &User{
					Name: "Resource Owner",
					UID:  "owner789",
					Type: "INTERNAL_IAP",
				},
				Data: map[string]any{
					"table": "resources",
					"id":    "res-123",
				},
			},
		}).
		WithDisposition(DispositionAllowed).
		Build()

	// Log the read event
	if err := logger.LogAPIActivity(readEvent); err != nil {
		fmt.Fprintf(os.Stderr, "Error logging API activity: %v\n", err)
		return
	}

	// Example 3: Failed API Delete operation with security context
	deleteEvent := NewAPIActivityEventBuilder(ActivityAPIDelete).
		WithMessage("Failed to delete resource - insufficient permissions").
		WithAPI(API{
			Operation: "DeleteResource",
			Service: APIService{
				Name: "ResourceService",
				UID:  "service-123",
			},
			Response: &APIResponse{
				Code:    "403",
				Error:   "PERMISSION_DENIED",
				Message: "User does not have permission to delete this resource",
			},
		}).
		WithActor(Actor{
			User: &User{
				Name:   "Jane Smith",
				UID:    "actor789",
				TypeID: 1,
			},
		}).
		WithCloud(Cloud{
			Provider: "Azure",
			Region:   "eastus",
			Account: CloudAccount{
				UID:  "azure-sub-789",
				Name: "development",
				Type: "Azure Subscription",
			},
		}).
		WithSrcEndpoint(NetworkEndpoint{
			IP:       "***********",
			Hostname: "admin-console.example.com",
		}).
		WithHTTPResponse(HTTPResponse{
			Code:    403,
			Message: "Forbidden",
			Length:  128,
			Latency: 75,
		}).
		WithSeverity(SeverityMedium).        // BaseEvent field
		WithStatus(StatusFailure).           // BaseEvent field
		WithAction(ActionDenied).            // BaseEvent field
		WithDisposition(DispositionBlocked). // Security Control Profile: Recommended
		Build()

	// Log the failed delete event
	if err := logger.LogAPIActivity(deleteEvent); err != nil {
		fmt.Fprintf(os.Stderr, "Error logging API activity: %v\n", err)
		return
	}

	fmt.Println("API Activity events logged successfully")
	// Output: API Activity events logged successfully
}
