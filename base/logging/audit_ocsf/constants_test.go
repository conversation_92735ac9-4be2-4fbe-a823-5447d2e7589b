package auditocsf

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestActivityIDString(t *testing.T) {
	tests := []struct {
		id       ActivityID
		expected string
	}{
		{ActivityUnknown, "Unknown"},
		{ActivityLogon, "Logon"},
		{Activity<PERSON><PERSON>ff, "Logoff"},
		{ActivityAuthenticationTicket, "Authentication Ticket"},
		{ActivityServiceTicketRequest, "Service Ticket Request"},
		{ActivityServiceTicketRenew, "Service Ticket Renew"},
		{ActivityPreauth, "Preauth"},
		{ActivityOther, "Other"},
		{ActivityID(999), "Unknown"}, // Test unknown value
	}

	for _, tt := range tests {
		t.Run(tt.expected, func(t *testing.T) {
			assert.Equal(t, tt.expected, tt.id.String())
		})
	}
}

func TestStatusIDString(t *testing.T) {
	tests := []struct {
		id       StatusID
		expected string
	}{
		{StatusUnknown, "Unknown"},
		{StatusSuccess, "Success"},
		{StatusFailure, "Failure"},
		{StatusOther, "Other"},
		{StatusID(999), "Unknown"}, // Test unknown value
	}

	for _, tt := range tests {
		t.Run(tt.expected, func(t *testing.T) {
			assert.Equal(t, tt.expected, tt.id.String())
		})
	}
}

func TestSeverityIDString(t *testing.T) {
	tests := []struct {
		id       SeverityID
		expected string
	}{
		{SeverityUnknown, "Unknown"},
		{SeverityInformational, "Informational"},
		{SeverityLow, "Low"},
		{SeverityMedium, "Medium"},
		{SeverityHigh, "High"},
		{SeverityCritical, "Critical"},
		{SeverityFatal, "Fatal"},
		{SeverityOther, "Other"},
		{SeverityID(999), "Unknown"}, // Test unknown value
	}

	for _, tt := range tests {
		t.Run(tt.expected, func(t *testing.T) {
			assert.Equal(t, tt.expected, tt.id.String())
		})
	}
}

func TestActionIDString(t *testing.T) {
	tests := []struct {
		id       ActionID
		expected string
	}{
		{ActionUnknown, "Unknown"},
		{ActionAllowed, "Allowed"},
		{ActionDenied, "Denied"},
		{ActionObserved, "Observed"},
		{ActionModified, "Modified"},
		{ActionOther, "Other"},
		{ActionID(999), "Unknown"}, // Test unknown value
	}

	for _, tt := range tests {
		t.Run(tt.expected, func(t *testing.T) {
			assert.Equal(t, tt.expected, tt.id.String())
		})
	}
}

func TestDispositionIDString(t *testing.T) {
	tests := []struct {
		id       DispositionID
		expected string
	}{
		{DispositionUnknown, "Unknown"},
		{DispositionAllowed, "Allowed"},
		{DispositionBlocked, "Blocked"},
		{DispositionQuarantined, "Quarantined"},
		{DispositionIsolated, "Isolated"},
		{DispositionDeleted, "Deleted"},
		{DispositionDropped, "Dropped"},
		{DispositionCustom, "Custom Action"},
		{DispositionApproved, "Approved"},
		{DispositionRestored, "Restored"},
		{DispositionExonerated, "Exonerated"},
		{DispositionCorrected, "Corrected"},
		{DispositionDetected, "Detected"},
		{DispositionNoAction, "No Action"},
		{DispositionLogged, "Logged"},
		{DispositionAlert, "Alert"},
		{DispositionUnauthorized, "Unauthorized"},
		{DispositionError, "Error"},
		{DispositionOther, "Other"},
		{DispositionID(999), "Unknown"}, // Test unknown value
	}

	for _, tt := range tests {
		t.Run(tt.expected, func(t *testing.T) {
			assert.Equal(t, tt.expected, tt.id.String())
		})
	}
}

func TestAuthProtocolIDString(t *testing.T) {
	tests := []struct {
		id       AuthProtocolID
		expected string
	}{
		{AuthProtocolUnknown, "Unknown"},
		{AuthProtocolNTLM, "NTLM"},
		{AuthProtocolKerberos, "Kerberos"},
		{AuthProtocolDigest, "Digest"},
		{AuthProtocolOpenID, "OpenID"},
		{AuthProtocolSAML, "SAML"},
		{AuthProtocolOAuth2, "OAUTH 2.0"},
		{AuthProtocolPAP, "PAP"},
		{AuthProtocolCHAP, "CHAP"},
		{AuthProtocolEAP, "EAP"},
		{AuthProtocolRADIUS, "RADIUS"},
		{AuthProtocolBasic, "Basic Authentication"},
		{AuthProtocolLDAP, "LDAP"},
		{AuthProtocolOther, "Other"},
		{AuthProtocolID(999), "Unknown"}, // Test unknown value
	}

	for _, tt := range tests {
		t.Run(tt.expected, func(t *testing.T) {
			assert.Equal(t, tt.expected, tt.id.String())
		})
	}
}

func TestLogonTypeIDString(t *testing.T) {
	tests := []struct {
		id       LogonTypeID
		expected string
	}{
		{LogonTypeUnknown, "Unknown"},
		{LogonTypeSystem, "System"},
		{LogonTypeInteractive, "Interactive"},
		{LogonTypeNetwork, "Network"},
		{LogonTypeBatch, "Batch"},
		{LogonTypeOSService, "OS Service"},
		{LogonTypeUnlock, "Unlock"},
		{LogonTypeNetworkCleartext, "Network Cleartext"},
		{LogonTypeNewCredentials, "New Credentials"},
		{LogonTypeRemoteInteractive, "Remote Interactive"},
		{LogonTypeCachedInteractive, "Cached Interactive"},
		{LogonTypeCachedRemoteInteractive, "Cached Remote Interactive"},
		{LogonTypeCachedUnlock, "Cached Unlock"},
		{LogonTypeOther, "Other"},
		{LogonTypeID(999), "Unknown"}, // Test unknown value
	}

	for _, tt := range tests {
		t.Run(tt.expected, func(t *testing.T) {
			assert.Equal(t, tt.expected, tt.id.String())
		})
	}
}

func TestOCSFConstants(t *testing.T) {
	// Test that the main OCSF constants have expected values
	assert.Equal(t, 3, CategoryIAM)
	assert.Equal(t, 3002, ClassAuthentication)
	assert.Equal(t, "type.ocsf.io/Authentication", OCSFTypePrefix)
}

func TestActivityIDValues(t *testing.T) {
	// Test that activity IDs have the correct OCSF values
	assert.Equal(t, ActivityID(0), ActivityUnknown)
	assert.Equal(t, ActivityID(1), ActivityLogon)
	assert.Equal(t, ActivityID(2), ActivityLogoff)
	assert.Equal(t, ActivityID(3), ActivityAuthenticationTicket)
	assert.Equal(t, ActivityID(4), ActivityServiceTicketRequest)
	assert.Equal(t, ActivityID(5), ActivityServiceTicketRenew)
	assert.Equal(t, ActivityID(6), ActivityPreauth)
	assert.Equal(t, ActivityID(99), ActivityOther)
}

func TestStatusIDValues(t *testing.T) {
	// Test that status IDs have the correct OCSF values
	assert.Equal(t, StatusID(0), StatusUnknown)
	assert.Equal(t, StatusID(1), StatusSuccess)
	assert.Equal(t, StatusID(2), StatusFailure)
	assert.Equal(t, StatusID(99), StatusOther)
}

func TestSeverityIDValues(t *testing.T) {
	// Test that severity IDs have the correct OCSF values
	assert.Equal(t, SeverityID(0), SeverityUnknown)
	assert.Equal(t, SeverityID(1), SeverityInformational)
	assert.Equal(t, SeverityID(2), SeverityLow)
	assert.Equal(t, SeverityID(3), SeverityMedium)
	assert.Equal(t, SeverityID(4), SeverityHigh)
	assert.Equal(t, SeverityID(5), SeverityCritical)
	assert.Equal(t, SeverityID(6), SeverityFatal)
	assert.Equal(t, SeverityID(99), SeverityOther)
}
