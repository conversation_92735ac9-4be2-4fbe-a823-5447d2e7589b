package auditocsf

import (
	"encoding/json"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestAuthorizeSessionEvent_SetDefaults(t *testing.T) {
	event := AuthorizeSessionEvent{
		BaseEvent: BaseEvent{
			ActivityID: ActivityAssignPrivileges,
		},
		Privileges: []string{"SeDebugPrivilege", "SeBackupPrivilege"},
	}

	event.SetDefaults()

	assert.Equal(t, CategoryIAM, event.CategoryUID)
	assert.Equal(t, ClassAuthorizeSession, event.ClassUID)
	assert.Equal(t, int64(300301), event.TypeUID) // 3003 * 100 + 1
	assert.Equal(t, SeverityInformational, event.SeverityID)
	assert.False(t, event.Time.IsZero())
}

func TestAuthorizeSessionEvent_CalculateTypeUID(t *testing.T) {
	tests := []struct {
		name       string
		classUID   int
		activityID ActivityID
		expected   int64
	}{
		{"Assign Privileges", ClassAuthorizeSession, ActivityAssignPrivileges, 300301},
		{"Assign Groups", ClassAuthorizeSession, ActivityAssignGroups, 300302},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			event := AuthorizeSessionEvent{
				BaseEvent: BaseEvent{
					ClassUID:   tt.classUID,
					ActivityID: tt.activityID,
				},
			}
			assert.Equal(t, tt.expected, event.CalculateTypeUID())
		})
	}
}

func TestAuthorizeSessionEvent_Validate(t *testing.T) {
	validEvent := AuthorizeSessionEvent{
		BaseEvent: BaseEvent{
			ActivityID:  ActivityAssignPrivileges,
			CategoryUID: CategoryIAM,
			ClassUID:    ClassAuthorizeSession,
			SeverityID:  SeverityInformational,
			Time:        time.Now(),
			TypeUID:     300301,
			Metadata: Metadata{
				Version: "1.5.0",
			},
		},
		Privileges: []string{"SeDebugPrivilege"},
	}

	t.Run("Valid event", func(t *testing.T) {
		err := validEvent.Validate()
		assert.NoError(t, err)
	})

	t.Run("Missing activity_id", func(t *testing.T) {
		event := validEvent
		event.ActivityID = 0
		err := event.Validate()
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "activity_id is required")
	})

	t.Run("Missing privileges and group", func(t *testing.T) {
		event := validEvent
		event.Privileges = nil
		event.Group = nil
		err := event.Validate()
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "either group or privileges must be specified")
	})

	t.Run("Both privileges and group specified", func(t *testing.T) {
		event := validEvent
		event.Group = &Group{Name: "Administrators"}
		err := event.Validate()
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "group and privileges are mutually exclusive")
	})

	t.Run("Missing metadata version", func(t *testing.T) {
		event := validEvent
		event.Metadata.Version = ""
		err := event.Validate()
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "metadata.version is required")
	})
}

func TestAuthorizeSessionEventBuilder_Basic(t *testing.T) {
	builder := NewAuthorizeSessionEventBuilder(ActivityAssignPrivileges)

	event := builder.
		WithUserUID("user123", "INTERNAL_IAP").
		WithPrivileges([]string{"SeDebugPrivilege", "SeBackupPrivilege"}).
		WithMessage("Test authorize session event").
		Build()

	assert.Equal(t, ActivityAssignPrivileges, event.ActivityID)
	assert.Equal(t, "user123", event.User.UID)
	assert.Equal(t, "INTERNAL_IAP", event.User.Type)
	assert.Equal(t, []string{"SeDebugPrivilege", "SeBackupPrivilege"}, event.Privileges)
	assert.Equal(t, "Test authorize session event", event.Message)
	assert.Equal(t, CategoryIAM, event.CategoryUID)
	assert.Equal(t, ClassAuthorizeSession, event.ClassUID)
	assert.Equal(t, int64(300301), event.TypeUID)
}

func TestAuthorizeSessionEventBuilder_WithGroup(t *testing.T) {
	group := Group{
		Name: "Administrators",
		UID:  "S-1-5-32-544",
	}

	event := NewAuthorizeSessionEventBuilder(ActivityAssignGroups).
		WithUserUID("user123", "INTERNAL_IAP").
		WithGroup(group).
		Build()

	assert.Equal(t, &group, event.Group)
	assert.Nil(t, event.Privileges) // Should be cleared due to mutual exclusivity
}

func TestAuthorizeSessionEventBuilder_WithPrivileges(t *testing.T) {
	privileges := []string{"SeDebugPrivilege", "SeBackupPrivilege"}

	event := NewAuthorizeSessionEventBuilder(ActivityAssignPrivileges).
		WithUserUID("user123", "INTERNAL_IAP").
		WithPrivileges(privileges).
		Build()

	assert.Equal(t, privileges, event.Privileges)
	assert.Nil(t, event.Group) // Should be cleared due to mutual exclusivity
}

func TestOCSFAuditLogger_LogAuthorizeSession(t *testing.T) {
	mockPrinter := &MockPrinter{}
	logger := NewOCSFAuditLogger(mockPrinter)

	event := AuthorizeSessionEvent{
		BaseEvent: BaseEvent{
			ActivityID: ActivityAssignPrivileges,
			Message:    "Custom authorize session event",
			Metadata: Metadata{
				Version: "1.5.0",
				Product: &Product{
					Name:    "Test",
					Vendor:  "Test Vendor",
					Feature: "audit-ocsf",
				},
			},
		},
		Privileges: []string{"SeDebugPrivilege"},
	}

	err := logger.LogAuthorizeSession(event)
	require.NoError(t, err)

	output := mockPrinter.GetOutput()
	require.Len(t, output, 1)

	var auditLog OCSFAuditLog
	err = json.Unmarshal([]byte(output[0]), &auditLog)
	require.NoError(t, err)

	// Verify wrapper fields
	assert.Equal(t, "type.eng.augmentcode.com/OCSFAuditLog", auditLog.Type)
	assert.Equal(t, "", auditLog.Tenant.Name)

	// Verify the event was properly wrapped
	eventData, ok := auditLog.OCSFEvent.(map[string]interface{})
	require.True(t, ok)
	assert.Equal(t, float64(1), eventData["activity_id"])
	assert.Equal(t, "Custom authorize session event", eventData["message"])
}

func TestNewAssignPrivilegesEvent(t *testing.T) {
	privileges := []string{"SeDebugPrivilege", "SeBackupPrivilege"}
	event := NewAssignPrivilegesEvent("user123", "INTERNAL_IAP", privileges).Build()

	assert.Equal(t, ActivityAssignPrivileges, event.ActivityID)
	assert.Equal(t, "user123", event.User.UID)
	assert.Equal(t, "INTERNAL_IAP", event.User.Type)
	assert.Equal(t, privileges, event.Privileges)
	assert.Equal(t, StatusSuccess, *event.StatusID)
	assert.Equal(t, ActionAllowed, *event.ActionID)
	assert.Equal(t, SeverityInformational, event.SeverityID)
}

func TestNewAssignGroupsEvent(t *testing.T) {
	group := Group{Name: "Administrators", UID: "S-1-5-32-544"}
	event := NewAssignGroupsEvent("user123", "INTERNAL_IAP", group).Build()

	assert.Equal(t, ActivityAssignGroups, event.ActivityID)
	assert.Equal(t, "user123", event.User.UID)
	assert.Equal(t, "INTERNAL_IAP", event.User.Type)
	assert.Equal(t, &group, event.Group)
	assert.Equal(t, StatusSuccess, *event.StatusID)
	assert.Equal(t, ActionAllowed, *event.ActionID)
	assert.Equal(t, SeverityInformational, event.SeverityID)
}
