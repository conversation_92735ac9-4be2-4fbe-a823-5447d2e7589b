"""
Tests for OCSF Audit Logging Python Implementation

This module contains comprehensive tests for the Python OCSF audit logging
library to ensure functionality matches the Go implementation.
"""

import json
import pytest
from datetime import datetime, timezone

# Import directly from modules using relative imports
from .constants import (
    ActivityID,
    StatusID,
    SeverityID,
    ActionID,
    AuthProtocolID,
    LogonTypeID,
    CATEGORY_IAM,
    CLASS_AUTHENTICATION,
    CLASS_AUTHORIZE_SESSION,
    CLASS_API_ACTIVITY,
)
from .ocsf_models import (
    User,
    Group,
    Device,
    OS,
    Session,
    NetworkEndpoint,
    Metadata,
    Product,
)
from .base_event import BaseEvent
from .authentication_event import (
    AuthenticationEvent,
    new_successful_logon_event,
    new_failed_logon_event,
    new_logoff_event,
)
from .api_activity_event import (
    APIActivityEvent,
    APIActivityEventBuilder,
    Actor,
    API,
    APIService,
    Cloud,
    CloudAccount,
)
from .authorize_session_event import (
    AuthorizeSessionEvent,
    new_assign_privileges_event,
    new_assign_groups_event,
)
from .audit_logger import new_default_ocsf_audit_logger, new_ocsf_audit_logger
from .test_utils import new_mock_ocsf_audit_logger, MockPrinter


class TestConstants:
    """Test OCSF constants and enums"""

    def test_activity_id_values(self):
        """Test ActivityID enum values match Go implementation"""
        assert ActivityID.UNKNOWN == 0
        assert ActivityID.LOGON == 1
        assert ActivityID.LOGOFF == 2
        assert ActivityID.API_CREATE == 1
        assert ActivityID.API_DELETE == 4
        assert ActivityID.ASSIGN_PRIVILEGES == 1
        assert ActivityID.ASSIGN_GROUPS == 2

    def test_status_id_values(self):
        """Test StatusID enum values"""
        assert StatusID.UNKNOWN == 0
        assert StatusID.SUCCESS == 1
        assert StatusID.FAILURE == 2

    def test_severity_id_values(self):
        """Test SeverityID enum values"""
        assert SeverityID.INFORMATIONAL == 1
        assert SeverityID.MEDIUM == 3
        assert SeverityID.HIGH == 4

    def test_string_representations(self):
        """Test string representations of enums"""
        assert str(StatusID.SUCCESS) == "Success"
        assert str(SeverityID.INFORMATIONAL) == "Informational"
        assert str(ActionID.ALLOWED) == "Allowed"


class TestBaseEvent:
    """Test BaseEvent functionality"""

    def test_type_uid_calculation(self):
        """Test type_uid calculation matches Go implementation"""
        event = AuthenticationEvent(
            activity_id=ActivityID.LOGON,
            category_uid=CATEGORY_IAM,
            class_uid=CLASS_AUTHENTICATION,
            severity_id=SeverityID.INFORMATIONAL,
            time=datetime.now(timezone.utc),
            type_uid=0,  # Will be calculated
            metadata=None,
            user=User(),
        )

        calculated_uid = event.calculate_type_uid()
        expected_uid = CLASS_AUTHENTICATION * 100 + int(ActivityID.LOGON)
        assert calculated_uid == expected_uid
        assert calculated_uid == 300201

    def test_validation_required_fields(self):
        """Test validation of required fields"""
        event = AuthenticationEvent(
            activity_id=ActivityID.LOGON,
            category_uid=CATEGORY_IAM,
            class_uid=CLASS_AUTHENTICATION,
            severity_id=SeverityID.INFORMATIONAL,
            time=datetime.utcnow(),
            type_uid=300201,
            metadata=None,  # Missing required metadata
            user=User(),
        )

        with pytest.raises(ValueError, match="metadata.version is required"):
            event.validate()


class TestAuthenticationEvent:
    """Test AuthenticationEvent functionality"""

    def test_successful_logon_builder(self):
        """Test successful logon event builder"""
        event = (
            new_successful_logon_event("user123", "INTERNAL_IAP")
            .with_message("User authenticated successfully")
            .build()
        )

        assert event.activity_id == ActivityID.LOGON
        assert event.user.uid == "user123"
        assert event.user.type == "INTERNAL_IAP"
        assert event.status_id == StatusID.SUCCESS
        assert event.action_id == ActionID.ALLOWED
        assert event.severity_id == SeverityID.INFORMATIONAL
        assert event.message == "User authenticated successfully"

    def test_failed_logon_builder(self):
        """Test failed logon event builder"""
        event = (
            new_failed_logon_event("user456", "EXTERNAL")
            .with_message("Authentication failed")
            .with_status_detail("", "Invalid credentials")
            .build()
        )

        assert event.activity_id == ActivityID.LOGON
        assert event.user.uid == "user456"
        assert event.status_id == StatusID.FAILURE
        assert event.action_id == ActionID.DENIED
        assert event.severity_id == SeverityID.MEDIUM
        assert event.status_detail == "Invalid credentials"

    def test_complex_authentication_event(self):
        """Test complex authentication event with all fields"""
        device = Device(
            name="workstation-01",
            hostname="ws01.example.com",
            ip="**********",
            os=OS(name="Windows", version="10.0.19041"),
        )

        session = Session(
            uid="session-123",
            issuer="domain-controller",
            is_mfa=True,
            is_remote=False,
        )

        event = (
            new_successful_logon_event("admin", "ADMIN")
            .with_message("Admin logon with MFA")
            .with_mfa(True)
            .with_remote(False)
            .with_auth_protocol(AuthProtocolID.KERBEROS)
            .with_logon_type(LogonTypeID.INTERACTIVE)
            .with_device(device)
            .with_session(session)
            .build()
        )

        assert event.is_mfa is True
        assert event.is_remote is False
        assert event.auth_protocol_id == int(AuthProtocolID.KERBEROS)
        assert event.logon_type_id == int(LogonTypeID.INTERACTIVE)
        assert event.device and event.device.name == "workstation-01"
        assert event.session and event.session.is_mfa is True


class TestAuthorizeSessionEvent:
    """Test AuthorizeSessionEvent functionality"""

    def test_assign_privileges_event(self):
        """Test assign privileges event"""
        privileges = ["read", "write", "admin"]
        event = (
            new_assign_privileges_event("user123", "INTERNAL", privileges)
            .with_message("Assigned admin privileges")
            .build()
        )

        assert event.activity_id == ActivityID.ASSIGN_PRIVILEGES
        assert event.user.uid == "user123"
        assert event.privileges == privileges
        assert event.group is None

    def test_assign_groups_event(self):
        """Test assign groups event"""
        group = Group(name="administrators", uid="admin-group-123")
        event = (
            new_assign_groups_event("user456", "INTERNAL", group)
            .with_message("Added to administrators group")
            .build()
        )

        assert event.activity_id == ActivityID.ASSIGN_GROUPS
        assert event.user.uid == "user456"
        assert event.group and event.group.name == "administrators"
        assert event.privileges is None

    def test_mutual_exclusivity_validation(self):
        """Test that group and privileges are mutually exclusive"""

        event = AuthorizeSessionEvent(
            activity_id=ActivityID.ASSIGN_PRIVILEGES,
            category_uid=CATEGORY_IAM,
            class_uid=CLASS_AUTHORIZE_SESSION,
            severity_id=SeverityID.INFORMATIONAL,
            time=datetime.utcnow(),
            type_uid=300301,
            metadata=Metadata(
                version="1.5.0",
                product=Product(name="Test", vendor="Test"),
            ),
            user=User(),
            group=Group(name="test"),
            privileges=["read", "write"],  # Both set - should fail
        )

        with pytest.raises(
            ValueError, match="group and privileges are mutually exclusive"
        ):
            event.validate()


class TestAPIActivityEvent:
    """Test APIActivityEvent functionality"""

    def test_api_create_event(self):
        """Test API create event"""
        event = (
            APIActivityEventBuilder(ActivityID.API_CREATE)
            .with_message("Created new resource")
            .with_api(
                API(
                    operation="CreateResource",
                    service=APIService(name="ResourceService", uid="svc-123"),
                )
            )
            .with_actor(Actor(user=User(uid="user123", type_id=1)))
            .with_cloud(
                Cloud(
                    provider="AWS",
                    account=CloudAccount(uid="************", name="production"),
                )
            )
            .with_src_endpoint(NetworkEndpoint(ip="*************", port=443))
            .build()
        )

        assert event.activity_id == ActivityID.API_CREATE
        assert event.api and event.api.operation == "CreateResource"
        assert event.actor and event.actor.user and event.actor.user.uid == "user123"
        assert event.cloud and event.cloud.provider == "AWS"
        assert event.src_endpoint and event.src_endpoint.ip == "*************"


class TestOCSFAuditLogger:
    """Test OCSFAuditLogger functionality"""

    def test_log_authentication_with_tenant(self):
        """Test logging authentication with tenant"""
        logger, mock_printer = new_mock_ocsf_audit_logger()

        # Create a successful logon event
        event = (
            new_successful_logon_event("user123", "INTERNAL_IAP")
            .with_message("User logged in")
            .build()
        )

        logger.log_authentication_with_tenant(event, "tenant456")

        output = mock_printer.get_output()
        assert len(output) == 1

        # Parse the JSON output
        log_data = json.loads(output[0])
        assert log_data["@type"] == "type.eng.augmentcode.com/OCSFAuditLog"
        assert log_data["tenant"]["name"] == "tenant456"

        event_data = log_data["ocsf_event"]
        assert event_data["activity_id"] == ActivityID.LOGON
        assert event_data["user"]["uid"] == "user123"
        assert event_data["message"] == "User logged in"

    def test_log_failed_authentication_with_tenant(self):
        """Test logging failed authentication with tenant"""
        logger, mock_printer = new_mock_ocsf_audit_logger()

        # Create a failed logon event
        event = (
            new_failed_logon_event("user456", "EXTERNAL")
            .with_message("Authentication failed")
            .with_status_detail("", "Invalid password")
            .build()
        )

        logger.log_authentication_with_tenant(event, "tenant789")

        output = mock_printer.get_output()
        assert len(output) == 1

        log_data = json.loads(output[0])
        event_data = log_data["ocsf_event"]
        assert event_data["status_id"] == StatusID.FAILURE
        assert event_data["status_detail"] == "Invalid password"
        assert event_data["severity_id"] == SeverityID.MEDIUM

    def test_mock_printer_functionality(self):
        """Test MockPrinter utility functions"""
        logger, mock_printer = new_mock_ocsf_audit_logger()

        # Test initial state
        assert mock_printer.count() == 0
        assert mock_printer.get_last_output() == ""

        # Log some events
        event1 = (
            new_successful_logon_event("user1", "TYPE1").with_message("msg1").build()
        )
        event2 = (
            new_successful_logon_event("user2", "TYPE2").with_message("msg2").build()
        )

        logger.log_authentication_with_tenant(event1, "tenant1")
        logger.log_authentication_with_tenant(event2, "tenant2")

        # Test count and output
        assert mock_printer.count() == 2
        output = mock_printer.get_output()
        assert len(output) == 2

        # Test last output
        last_output = mock_printer.get_last_output()
        log_data = json.loads(last_output)
        assert log_data["ocsf_event"]["user"]["uid"] == "user2"

        # Test clear
        mock_printer.clear()
        assert mock_printer.count() == 0
        assert mock_printer.get_last_output() == ""

    def test_logger_handles_invalid_events_gracefully(self):
        """Test that logger silently handles invalid events without raising exceptions"""
        logger, mock_printer = new_mock_ocsf_audit_logger()

        # Create an invalid authentication event (missing required metadata)
        invalid_event = AuthenticationEvent(
            activity_id=ActivityID.LOGON,
            category_uid=CATEGORY_IAM,
            class_uid=CLASS_AUTHENTICATION,
            severity_id=SeverityID.INFORMATIONAL,
            time=datetime.now(timezone.utc),
            type_uid=300201,
            metadata=None,  # Missing required metadata - will cause validation to fail
            user=User(),
        )

        # This should not raise an exception, even though the event is invalid
        logger.log_authentication_with_tenant(invalid_event, "test-tenant")

        # No output should be generated due to the error
        assert mock_printer.count() == 0
        assert mock_printer.get_last_output() == ""

    def test_src_dst_endpoint_serialize_as_null(self):
        """Test that src_endpoint and dst_endpoint serialize as null when None"""
        logger, mock_printer = new_mock_ocsf_audit_logger()

        # Create an event with src_endpoint and dst_endpoint as None
        event = (
            new_successful_logon_event("user123", "INTERNAL_IAP")
            .with_message("Test message")
            .build()
        )

        # Ensure src_endpoint and dst_endpoint are None
        event.src_endpoint = None
        event.dst_endpoint = None

        logger.log_authentication_with_tenant(event, "test-tenant")

        # Parse the output and check that src_endpoint and dst_endpoint are present as null
        output = mock_printer.get_output()
        assert len(output) == 1

        log_data = json.loads(output[0])
        event_data = log_data["ocsf_event"]

        # These fields should be present in the JSON as null
        assert "src_endpoint" in event_data
        assert event_data["src_endpoint"] is None
        assert "dst_endpoint" in event_data
        assert event_data["dst_endpoint"] is None

    def test_log_authentication_with_none_tenant(self):
        """Test that None tenant defaults to 'unknown'"""
        logger, mock_printer = new_mock_ocsf_audit_logger()

        # Create a simple authentication event
        event = new_successful_logon_event("test-user", "user").build()

        # Log the event with None tenant
        logger.log_authentication_with_tenant(event, None)

        # Get the logged output
        output = mock_printer.get_output()
        assert len(output) == 1

        # Parse the JSON
        logged_data = json.loads(output[0])

        # Check that the tenant name is "unknown"
        assert logged_data["tenant"]["name"] == "unknown"


if __name__ == "__main__":
    pytest.main([__file__])
