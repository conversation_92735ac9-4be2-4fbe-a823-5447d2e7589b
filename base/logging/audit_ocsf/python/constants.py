"""
OCSF Constants and Enums

This module contains all OCSF constants, enums, and class definitions
matching the Go implementation.
"""

from enum import IntEnum


# OCSF Class Constants
CATEGORY_IAM = 3  # Identity & Access Management
CATEGORY_APPLICATION_ACTIVITY = 6  # Application Activity

CLASS_AUTHENTICATION = 3002  # Authentication
CLASS_AUTHORIZE_SESSION = 3003  # Authorize Session
CLASS_API_ACTIVITY = 6003  # API Activity

# OCSF Type prefix for Authentication events
OCSF_TYPE_PREFIX = "type.ocsf.io/Authentication"


class ActivityID(IntEnum):
    """Activity IDs for OCSF events"""

    # Common activity IDs
    UNKNOWN = 0
    OTHER = 99

    # Authentication event activity IDs
    LOGON = 1
    LOGOFF = 2
    AUTHENTICATION_TICKET = 3
    SERVICE_TICKET_REQUEST = 4
    SERVICE_TICKET_RENEW = 5
    PREAUTH = 6

    # Authorize Session Activity IDs (reuse same numeric values as per OCSF spec)
    ASSIGN_PRIVILEGES = 1
    ASSIGN_GROUPS = 2

    # API Activity Activity IDs
    API_CREATE = 1
    API_READ = 2
    API_UPDATE = 3
    API_DELETE = 4

    def authentication_string(self) -> str:
        """Returns the string representation for Authentication events"""
        mapping = {
            self.UNKNOWN: "Unknown",
            self.LOGON: "Logon",
            self.LOGOFF: "Logoff",
            self.AUTHENTICATION_TICKET: "Authentication Ticket",
            self.SERVICE_TICKET_REQUEST: "Service Ticket Request",
            self.SERVICE_TICKET_RENEW: "Service Ticket Renew",
            self.PREAUTH: "Preauth",
            self.OTHER: "Other",
        }
        return mapping.get(self, "Unknown")

    def authorize_session_string(self) -> str:
        """Returns the string representation for Authorize Session events"""
        mapping = {
            self.UNKNOWN: "Unknown",
            self.ASSIGN_PRIVILEGES: "Assign Privileges",
            self.ASSIGN_GROUPS: "Assign Groups",
            self.OTHER: "Other",
        }
        return mapping.get(self, "Unknown")

    def api_activity_string(self) -> str:
        """Returns the string representation for API Activity events"""
        mapping = {
            self.UNKNOWN: "Unknown",
            self.API_CREATE: "Create",
            self.API_READ: "Read",
            self.API_UPDATE: "Update",
            self.API_DELETE: "Delete",
            self.OTHER: "Other",
        }
        return mapping.get(self, "Unknown")


class StatusID(IntEnum):
    """Status IDs for event status"""

    UNKNOWN = 0
    SUCCESS = 1
    FAILURE = 2
    OTHER = 99

    def __str__(self) -> str:
        mapping = {
            self.UNKNOWN: "Unknown",
            self.SUCCESS: "Success",
            self.FAILURE: "Failure",
            self.OTHER: "Other",
        }
        return mapping.get(self, "Unknown")


class SeverityID(IntEnum):
    """Severity IDs for event severity"""

    UNKNOWN = 0
    INFORMATIONAL = 1
    LOW = 2
    MEDIUM = 3
    HIGH = 4
    CRITICAL = 5
    FATAL = 6
    OTHER = 99

    def __str__(self) -> str:
        mapping = {
            self.UNKNOWN: "Unknown",
            self.INFORMATIONAL: "Informational",
            self.LOW: "Low",
            self.MEDIUM: "Medium",
            self.HIGH: "High",
            self.CRITICAL: "Critical",
            self.FATAL: "Fatal",
            self.OTHER: "Other",
        }
        return mapping.get(self, "Unknown")


class ActionID(IntEnum):
    """Action IDs for actions taken"""

    UNKNOWN = 0
    ALLOWED = 1
    DENIED = 2
    OBSERVED = 3
    MODIFIED = 4
    OTHER = 99

    def __str__(self) -> str:
        mapping = {
            self.UNKNOWN: "Unknown",
            self.ALLOWED: "Allowed",
            self.DENIED: "Denied",
            self.OBSERVED: "Observed",
            self.MODIFIED: "Modified",
            self.OTHER: "Other",
        }
        return mapping.get(self, "Unknown")


class DispositionID(IntEnum):
    """Disposition IDs for outcomes"""

    UNKNOWN = 0
    ALLOWED = 1
    BLOCKED = 2
    QUARANTINED = 3
    ISOLATED = 4
    DELETED = 5
    DROPPED = 6
    CUSTOM = 7
    APPROVED = 8
    RESTORED = 9
    EXONERATED = 10
    CORRECTED = 11
    PARTIALLY_CORRECT = 12
    UNCORRECTED = 13
    DELAYED = 14
    DETECTED = 15
    NO_ACTION = 16
    LOGGED = 17
    TAGGED = 18
    ALERT = 19
    COUNT = 20
    RESET = 21
    CAPTCHA = 22
    CHALLENGE = 23
    ACCESS_REVOKED = 24
    REJECTED = 25
    UNAUTHORIZED = 26
    ERROR = 27
    OTHER = 99

    def __str__(self) -> str:
        mapping = {
            self.UNKNOWN: "Unknown",
            self.ALLOWED: "Allowed",
            self.BLOCKED: "Blocked",
            self.QUARANTINED: "Quarantined",
            self.ISOLATED: "Isolated",
            self.DELETED: "Deleted",
            self.DROPPED: "Dropped",
            self.CUSTOM: "Custom Action",
            self.APPROVED: "Approved",
            self.RESTORED: "Restored",
            self.EXONERATED: "Exonerated",
            self.CORRECTED: "Corrected",
            self.PARTIALLY_CORRECT: "Partially Corrected",
            self.UNCORRECTED: "Uncorrected",
            self.DELAYED: "Delayed",
            self.DETECTED: "Detected",
            self.NO_ACTION: "No Action",
            self.LOGGED: "Logged",
            self.TAGGED: "Tagged",
            self.ALERT: "Alert",
            self.COUNT: "Count",
            self.RESET: "Reset",
            self.CAPTCHA: "Captcha",
            self.CHALLENGE: "Challenge",
            self.ACCESS_REVOKED: "Access Revoked",
            self.REJECTED: "Rejected",
            self.UNAUTHORIZED: "Unauthorized",
            self.ERROR: "Error",
            self.OTHER: "Other",
        }
        return mapping.get(self, "Unknown")


class AuthProtocolID(IntEnum):
    """Auth Protocol IDs for authentication protocols"""

    UNKNOWN = 0
    NTLM = 1
    KERBEROS = 2
    DIGEST = 3
    OPENID = 4
    SAML = 5
    OAUTH2 = 6
    PAP = 7
    CHAP = 8
    EAP = 9
    RADIUS = 10
    BASIC = 11
    LDAP = 12
    OTHER = 99

    def __str__(self) -> str:
        mapping = {
            self.UNKNOWN: "Unknown",
            self.NTLM: "NTLM",
            self.KERBEROS: "Kerberos",
            self.DIGEST: "Digest",
            self.OPENID: "OpenID",
            self.SAML: "SAML",
            self.OAUTH2: "OAUTH 2.0",
            self.PAP: "PAP",
            self.CHAP: "CHAP",
            self.EAP: "EAP",
            self.RADIUS: "RADIUS",
            self.BASIC: "Basic Authentication",
            self.LDAP: "LDAP",
            self.OTHER: "Other",
        }
        return mapping.get(self, "Unknown")


class LogonTypeID(IntEnum):
    """Logon Type IDs for logon types"""

    UNKNOWN = 0
    SYSTEM = 1
    INTERACTIVE = 2
    NETWORK = 3
    BATCH = 4
    OS_SERVICE = 5
    UNLOCK = 7
    NETWORK_CLEARTEXT = 8
    NEW_CREDENTIALS = 9
    REMOTE_INTERACTIVE = 10
    CACHED_INTERACTIVE = 11
    CACHED_REMOTE_INTERACTIVE = 12
    CACHED_UNLOCK = 13
    OTHER = 99

    def __str__(self) -> str:
        mapping = {
            self.UNKNOWN: "Unknown",
            self.SYSTEM: "System",
            self.INTERACTIVE: "Interactive",
            self.NETWORK: "Network",
            self.BATCH: "Batch",
            self.OS_SERVICE: "OS Service",
            self.UNLOCK: "Unlock",
            self.NETWORK_CLEARTEXT: "Network Cleartext",
            self.NEW_CREDENTIALS: "New Credentials",
            self.REMOTE_INTERACTIVE: "Remote Interactive",
            self.CACHED_INTERACTIVE: "Cached Interactive",
            self.CACHED_REMOTE_INTERACTIVE: "Cached Remote Interactive",
            self.CACHED_UNLOCK: "Cached Unlock",
            self.OTHER: "Other",
        }
        return mapping.get(self, "Unknown")
