"""
OCSF Audit Logger Implementation

This module contains the main OCSFAuditLogger class and printer interface
for OCSF-compliant audit logging.
"""

import json
import logging
import sys
import threading
from typing import Protocol, Any, Optional

from .ocsf_models import OCSFAuditLog, Tenant, to_dict
from .authentication_event import AuthenticationEvent
from .api_activity_event import APIActivityEvent
from .authorize_session_event import AuthorizeSessionEvent


class Printer(Protocol):
    """Printer interface for output abstraction (same as existing audit package)"""

    def println(self, *args: Any) -> None:
        """Print arguments to output"""
        ...


class StdoutPrinter:
    """StdoutPrinter implements Printer interface for stdout output"""

    def __init__(self):
        self._lock = threading.Lock()

    def println(self, *args: Any) -> None:
        """Print to stdout with synchronization"""
        with self._lock:
            print(*args)
            sys.stdout.flush()


class OCSFAuditLogger:
    """OCSFAuditLogger provides OCSF-compliant audit logging"""

    def __init__(self, printer: Printer):
        self._printer = printer

    def log_authentication(self, event: AuthenticationEvent) -> None:
        """Log an OCSF authentication event"""
        self.log_authentication_with_tenant(event, "")

    def log_authentication_with_tenant(
        self, event: AuthenticationEvent, tenant: str | None
    ) -> None:
        """Log an OCSF authentication event with a specific tenant"""
        try:
            # Set defaults
            event.set_defaults()

            # Validate the event
            event.validate()

            # Use "unknown" as default tenant name if None is provided
            tenant_name = tenant if tenant is not None else "unknown"

            # Wrap the event in the OCSFAuditLog structure
            audit_log = OCSFAuditLog(
                ocsf_event=to_dict(event),
                type="type.eng.augmentcode.com/OCSFAuditLog",
                tenant=Tenant(name=tenant_name),
            )

            # Marshal to JSON
            json_data = json.dumps(audit_log.to_dict())

            # Print the event
            self._printer.println(json_data)
        except Exception as e:
            # Silently ignore any errors to avoid impacting the caller
            logging.warning(
                f"Failed to write Authentication Event audit log record. Error: {e}"
            )

    def log_authorize_session(self, event: AuthorizeSessionEvent) -> None:
        """Log an OCSF authorize session event"""
        self.log_authorize_session_with_tenant(event, "")

    def log_authorize_session_with_tenant(
        self, event: AuthorizeSessionEvent, tenant: str | None
    ) -> None:
        """Log an OCSF authorize session event with a specific tenant"""
        try:
            # Set defaults
            event.set_defaults()

            # Validate the event
            event.validate()

            # Use "unknown" as default tenant name if None is provided
            tenant_name = tenant if tenant is not None else "unknown"

            # Wrap the event in the OCSFAuditLog structure
            audit_log = OCSFAuditLog(
                ocsf_event=to_dict(event),
                type="type.eng.augmentcode.com/OCSFAuditLog",
                tenant=Tenant(name=tenant_name),
            )

            # Marshal to JSON
            json_data = json.dumps(audit_log.to_dict())

            # Print the event
            self._printer.println(json_data)
        except Exception as e:
            # Silently ignore any errors to avoid impacting the caller
            logging.warning(f"Failed to write Authorize Session audit log. Error: {e}")

    def log_api_activity(self, event: APIActivityEvent) -> None:
        """Log an OCSF API activity event"""
        self.log_api_activity_with_tenant(event, "")

    def log_api_activity_with_tenant(
        self, event: APIActivityEvent, tenant: str | None
    ) -> None:
        """Log an OCSF API activity event with a specific tenant"""
        try:
            # Set defaults
            event.set_defaults()

            # Validate the event
            event.validate()

            # Use "unknown" as default tenant name if None is provided
            tenant_name = tenant if tenant is not None else "unknown"

            # Wrap the event in the OCSFAuditLog structure
            audit_log = OCSFAuditLog(
                ocsf_event=to_dict(event),
                type="type.eng.augmentcode.com/OCSFAuditLog",
                tenant=Tenant(name=tenant_name),
            )

            # Marshal to JSON
            json_data = json.dumps(audit_log.to_dict())

            # Print the event
            self._printer.println(json_data)
        except Exception as e:
            # Silently ignore any errors to avoid impacting the caller
            logging.warning(
                f"Failed to write API Activity audit log record. Error: {e}"
            )


def new_default_ocsf_audit_logger() -> OCSFAuditLogger:
    """Create a new OCSF audit logger with default settings"""
    return OCSFAuditLogger(StdoutPrinter())


def new_ocsf_audit_logger(printer: Printer) -> OCSFAuditLogger:
    """Create a new OCSF audit logger with custom printer"""
    return OCSFAuditLogger(printer)
