"""
API Activity Event Implementation

This module contains the APIActivityEvent class and related data structures
for OCSF API Activity events with support for multiple OCSF profiles.
"""

from dataclasses import dataclass
from datetime import datetime, timezone
from typing import Optional, List, Dict, Any

from .base_event import BaseEvent
from .constants import (
    ActivityID,
    SeverityID,
    StatusID,
    ActionID,
    DispositionID,
    CATEGORY_APPLICATION_ACTIVITY,
    CLASS_API_ACTIVITY,
)
from .ocsf_models import User, Session, NetworkEndpoint, Metadata, Product


@dataclass
class Process:
    """Process represents process information"""

    name: Optional[str] = None
    pid: Optional[int] = None
    uid: Optional[str] = None
    file: Optional["File"] = None
    user: Optional[User] = None
    session: Optional[Session] = None


@dataclass
class File:
    """File represents file information"""

    name: Optional[str] = None
    path: Optional[str] = None
    type: Optional[str] = None
    uid: Optional[str] = None
    size: Optional[int] = None


@dataclass
class Actor:
    """Actor represents the actor object that describes details about the user/role/process"""

    user: Optional[User] = None
    process: Optional[Process] = None
    session: Optional[Session] = None


@dataclass
class APIRequest:
    """APIRequest represents the API request details"""

    uid: Optional[str] = None
    data: Optional[Dict[str, Any]] = None
    flags: Optional[List[str]] = None


@dataclass
class APIResponse:
    """APIResponse represents the API response details"""

    code: Optional[str] = None
    error: Optional[str] = None
    message: Optional[str] = None
    data: Optional[Dict[str, Any]] = None


@dataclass
class APIService:
    """APIService represents the API service information"""

    name: str
    uid: Optional[str] = None
    version: Optional[str] = None
    labels: Optional[Dict[str, str]] = None


@dataclass
class API:
    """API represents details about a typical API call"""

    operation: str
    service: APIService
    version: Optional[str] = None
    request: Optional[APIRequest] = None
    response: Optional[APIResponse] = None


@dataclass
class CloudAccount:
    """CloudAccount represents cloud account information"""

    uid: str
    name: Optional[str] = None
    type: Optional[str] = None
    type_id: Optional[int] = None
    labels: Optional[Dict[str, str]] = None


@dataclass
class Project:
    """Project represents cloud project information"""

    name: Optional[str] = None
    uid: Optional[str] = None
    labels: Optional[Dict[str, str]] = None


@dataclass
class Cloud:
    """Cloud represents details about the Cloud environment"""

    account: CloudAccount
    provider: str
    region: Optional[str] = None
    zone: Optional[str] = None
    project: Optional[Project] = None


@dataclass
class URL:
    """URL represents URL information"""

    text: Optional[str] = None
    hostname: Optional[str] = None
    port: Optional[int] = None
    path: Optional[str] = None
    query: Optional[str] = None
    fragment: Optional[str] = None
    scheme: Optional[str] = None


@dataclass
class HTTPRequest:
    """HTTPRequest represents HTTP request details"""

    uid: Optional[str] = None
    url: Optional[URL] = None
    version: Optional[str] = None
    method: Optional[str] = None
    headers: Optional[Dict[str, str]] = None
    user_agent: Optional[str] = None
    x_forwarded_for: Optional[List[str]] = None
    referrer: Optional[str] = None
    args: Optional[str] = None
    length: Optional[int] = None


@dataclass
class HTTPResponse:
    """HTTPResponse represents HTTP response details"""

    code: Optional[int] = None
    message: Optional[str] = None
    headers: Optional[Dict[str, str]] = None
    length: Optional[int] = None
    latency: Optional[int] = None


@dataclass
class ResourceDetails:
    """ResourceDetails represents details about resources that were affected"""

    name: Optional[str] = None
    type: Optional[str] = None
    uid: Optional[str] = None
    owner: Optional[User] = None
    data: Optional[Dict[str, Any]] = None
    labels: Optional[Dict[str, str]] = None


@dataclass
class Reputation:
    """Reputation represents reputation information"""

    base_score: Optional[float] = None
    provider: Optional[str] = None
    score: Optional[str] = None
    score_id: Optional[int] = None


@dataclass
class Observable:
    """Observable represents observable information"""

    name: str
    type: str
    type_id: int
    value: str
    reputation: Optional[Reputation] = None


@dataclass
class Trace:
    """Trace represents distributed trace information"""

    uid: Optional[str] = None
    span_id: Optional[str] = None
    trace_id: Optional[str] = None


@dataclass
class Tactic:
    """Tactic represents MITRE ATT&CK tactic"""

    name: Optional[str] = None
    uid: Optional[str] = None


@dataclass
class Technique:
    """Technique represents MITRE ATT&CK technique"""

    name: Optional[str] = None
    uid: Optional[str] = None


@dataclass
class Attack:
    """Attack represents MITRE ATT&CK information"""

    version: Optional[str] = None
    tactics: Optional[List[Tactic]] = None
    techniques: Optional[List[Technique]] = None


@dataclass
class Authorization:
    """Authorization represents authorization information"""

    decision: Optional[str] = None
    policy: Optional[str] = None


@dataclass
class Enrichment:
    """Enrichment represents additional information from external data sources"""

    name: str
    value: str
    type: str
    data: Optional[Dict[str, Any]] = None


@dataclass
class FirewallRule:
    """FirewallRule represents firewall rule information"""

    name: Optional[str] = None
    uid: Optional[str] = None
    category: Optional[str] = None
    condition: Optional[str] = None


@dataclass
class Malware:
    """Malware represents malware information"""

    name: Optional[str] = None
    path: Optional[str] = None
    classification: Optional[List[str]] = None


@dataclass
class MalwareScanInfo:
    """MalwareScanInfo represents malware scan information"""

    scanner_name: Optional[str] = None
    scan_time: Optional[str] = None
    result: Optional[str] = None


@dataclass
class OSINT:
    """OSINT represents Open Source Intelligence information"""

    provider: Optional[str] = None
    data: Optional[Dict[str, Any]] = None


@dataclass
class Policy:
    """Policy represents policy information"""

    name: Optional[str] = None
    uid: Optional[str] = None
    version: Optional[str] = None


@dataclass
class APIActivityEvent(BaseEvent):
    """
    APIActivityEvent represents an OCSF API Activity event

    OCSF Profile System:
    The OCSF specification uses a profile system where the base class defines core fields,
    and additional profiles extend the class with specialized fields. The API Activity class
    supports multiple profiles as documented in the Go implementation.
    """

    # Required fields - Base API Activity Class
    actor: Optional[Actor] = None
    api: Optional[API] = None

    # Required fields - Profile Extensions
    cloud: Optional[Cloud] = None
    osint: Optional[List[OSINT]] = None

    # Recommended fields - Base API Activity Class
    http_request: Optional[HTTPRequest] = None
    http_response: Optional[HTTPResponse] = None
    resources: Optional[List[ResourceDetails]] = None
    observables: Optional[List[Observable]] = None

    # Recommended fields - Profile Extensions
    is_alert: Optional[bool] = None
    trace: Optional[Trace] = None

    # Optional fields - Base API Activity Class
    attacks: Optional[List[Attack]] = None
    authorizations: Optional[List[Authorization]] = None
    enrichments: Optional[List[Enrichment]] = None
    firewall_rule: Optional[FirewallRule] = None
    malware: Optional[List[Malware]] = None
    policy: Optional[Policy] = None
    unmapped: Optional[Dict[str, Any]] = None

    # Optional fields - Context Group (Base)
    confidence: Optional[str] = None
    confidence_id: Optional[int] = None
    confidence_score: Optional[int] = None
    risk_details: Optional[str] = None
    risk_level: Optional[str] = None
    risk_level_id: Optional[int] = None
    risk_score: Optional[int] = None

    # Optional fields - Primary Group (Base)
    disposition: Optional[str] = None
    disposition_id: Optional[DispositionID] = None
    malware_scan_info: Optional[MalwareScanInfo] = None

    def __post_init__(self):
        """Initialize required fields if not provided"""
        if self.actor is None:
            self.actor = Actor()
        if self.api is None:
            self.api = API(operation="", service=APIService(name=""))
        if self.src_endpoint is None:
            self.src_endpoint = NetworkEndpoint()
        if self.cloud is None:
            self.cloud = Cloud(account=CloudAccount(uid=""), provider="")

    def set_defaults(self) -> None:
        """Set default values for required fields"""
        if self.category_uid == 0:
            self.category_uid = CATEGORY_APPLICATION_ACTIVITY
        if self.class_uid == 0:
            self.class_uid = CLASS_API_ACTIVITY
        if self.type_uid == 0:
            self.type_uid = self.calculate_type_uid()
        if not self.time:
            self.time = datetime.now(timezone.utc)
        if self.severity_id == 0:
            self.severity_id = SeverityID.INFORMATIONAL

    def validate(self) -> None:
        """Validate that the event has all required fields"""
        # Validate common BaseEvent fields
        super().validate()

        # APIActivityEvent has no additional required fields beyond BaseEvent
        # The Actor, API, Cloud, and SrcEndpoint fields are required by OCSF spec
        # but we'll allow them to be validated at the application level


class APIActivityEventBuilder:
    """APIActivityEventBuilder provides a fluent interface for building APIActivityEvent objects"""

    def __init__(self, activity_id: ActivityID):
        self.event = APIActivityEvent(
            activity_id=activity_id,
            category_uid=CATEGORY_APPLICATION_ACTIVITY,
            class_uid=CLASS_API_ACTIVITY,
            severity_id=SeverityID.INFORMATIONAL,
            time=datetime.now(timezone.utc),
            type_uid=CLASS_API_ACTIVITY * 100 + int(activity_id),
            metadata=Metadata(
                version="1.5.0",
                product=Product(
                    name="Augment",
                    vendor="Augment Code",
                ),
                log_provider="augment-audit-ocsf",
            ),
            # Initialize required fields with empty objects
            actor=Actor(),
            api=API(operation="", service=APIService(name="")),
            src_endpoint=NetworkEndpoint(),
            cloud=Cloud(account=CloudAccount(uid=""), provider=""),
        )

    def with_severity(self, severity_id: SeverityID) -> "APIActivityEventBuilder":
        """Set the severity level"""
        self.event.severity_id = severity_id
        return self

    def with_status(self, status_id: StatusID) -> "APIActivityEventBuilder":
        """Set the status information"""
        self.event.status_id = status_id
        return self

    def with_status_detail(
        self, status_code: Optional[str], status_detail: Optional[str]
    ) -> "APIActivityEventBuilder":
        """Set the status detail information"""
        if status_code:
            self.event.status_code = status_code
        if status_detail:
            self.event.status_detail = status_detail
        return self

    def with_action(self, action_id: ActionID) -> "APIActivityEventBuilder":
        """Set the action taken"""
        self.event.action_id = action_id
        return self

    def with_message(self, message: str) -> "APIActivityEventBuilder":
        """Set the event message"""
        self.event.message = message
        return self

    def with_time(self, event_time: datetime) -> "APIActivityEventBuilder":
        """Set the event time"""
        self.event.time = event_time
        return self

    def with_device(self, device) -> "APIActivityEventBuilder":
        """Set the device information"""
        self.event.device = device
        return self

    def with_actor(self, actor: Actor) -> "APIActivityEventBuilder":
        """Set the actor information (Base API Activity: Required)"""
        self.event.actor = actor
        return self

    def with_api(self, api: API) -> "APIActivityEventBuilder":
        """Set the API information (Base API Activity: Required)"""
        self.event.api = api
        return self

    def with_cloud(self, cloud: Cloud) -> "APIActivityEventBuilder":
        """Set the cloud information (Cloud Profile: Required)"""
        self.event.cloud = cloud
        return self

    def with_src_endpoint(self, endpoint: NetworkEndpoint) -> "APIActivityEventBuilder":
        """Set the source endpoint information (Base API Activity: Required)"""
        self.event.src_endpoint = endpoint
        return self

    def with_dst_endpoint(self, endpoint: NetworkEndpoint) -> "APIActivityEventBuilder":
        """Set the destination endpoint information (Base API Activity: Recommended)"""
        self.event.dst_endpoint = endpoint
        return self

    def with_http_request(self, request: HTTPRequest) -> "APIActivityEventBuilder":
        """Set the HTTP request information (Base API Activity: Recommended)"""
        self.event.http_request = request
        return self

    def with_http_response(self, response: HTTPResponse) -> "APIActivityEventBuilder":
        """Set the HTTP response information (Base API Activity: Recommended)"""
        self.event.http_response = response
        return self

    def with_is_alert(self, is_alert: bool) -> "APIActivityEventBuilder":
        """Set whether this is an alertable event (Security Control Profile: Recommended)"""
        self.event.is_alert = is_alert
        return self

    def with_resources(
        self, resources: List[ResourceDetails]
    ) -> "APIActivityEventBuilder":
        """Set the resources affected by the activity (Base API Activity: Recommended)"""
        self.event.resources = resources
        return self

    def with_disposition(
        self, disposition_id: DispositionID
    ) -> "APIActivityEventBuilder":
        """Set the disposition information"""
        self.event.disposition_id = disposition_id
        return self

    def with_timezone_offset(self, offset: int) -> "APIActivityEventBuilder":
        """Set the timezone offset"""
        self.event.timezone_offset = offset
        return self

    def with_raw_data(self, raw_data: str) -> "APIActivityEventBuilder":
        """Set the raw event data"""
        self.event.raw_data = raw_data
        return self

    def build(self) -> APIActivityEvent:
        """Create the final APIActivityEvent"""
        # Ensure type_uid is calculated if not already set
        if self.event.type_uid == 0:
            self.event.type_uid = self.event.calculate_type_uid()

        # Set default metadata if none provided
        if not self.event.metadata:
            self.event.metadata = Metadata(
                version="1.5.0",
                product=Product(
                    name="Augment",
                    vendor="Augment Code",
                ),
                log_provider="augment-audit-ocsf",
            )

        return self.event
