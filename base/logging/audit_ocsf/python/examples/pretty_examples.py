#!/usr/bin/env python3
"""
OCSF Audit Log Examples (Pretty Formatted)

This module demonstrates usage of the Python OCSF Audit Logger library
with pretty-formatted JSON output, similar to the Go examples.
"""

import json
import sys
import os
from datetime import datetime, timezone
from typing import Any

import base.logging.audit_ocsf.python as audit_ocsf


class PrettyPrinter:
    """PrettyPrinter formats JSON output nicely"""

    def println(self, *args: Any) -> None:
        if args and isinstance(args[0], str):
            try:
                pretty_json = json.loads(args[0])
                formatted = json.dumps(pretty_json, indent=2)
                print(formatted)
            except (json.JSONDecodeError, TypeError):
                print(*args)
        else:
            print(*args)


def main():
    """Run the OCSF audit log examples"""
    # Create a logger with pretty printing
    logger = audit_ocsf.new_ocsf_audit_logger(PrettyPrinter())

    print("=== OCSF Audit Log Examples (Pretty Formatted) ===")
    print()

    # Example 1: Simple Authentication Event
    print("1. Simple Successful Logon:")
    print(
        '   Code: event = new_successful_logon_event("user123", "INTERNAL_IAP").with_message("User authenticated successfully").build()'
    )
    print('         logger.log_authentication_with_tenant(event, "tenant456")')
    print("   Output:")
    event = (
        audit_ocsf.new_successful_logon_event("user123", "INTERNAL_IAP")
        .with_message("User authenticated successfully")
        .build()
    )
    logger.log_authentication_with_tenant(event, "tenant456")
    print()

    # Example 2: Complex Authentication with Builder
    print("2. Complex Authentication Event with MFA:")
    print(
        "   Code: new_successful_logon_event().with_mfa(True).with_auth_protocol().with_device()..."
    )
    print("   Output:")
    complex_event = (
        audit_ocsf.new_successful_logon_event("admin123", "INTERNAL_IAP")
        .with_message("Administrative logon with MFA")
        .with_mfa(True)
        .with_remote(False)
        .with_auth_protocol(audit_ocsf.AuthProtocolID.KERBEROS)
        .with_logon_type(audit_ocsf.LogonTypeID.INTERACTIVE)
        .with_device(
            audit_ocsf.Device(
                name="admin-workstation",
                type="Computer",
                hostname="admin-ws.example.com",
                ip="*********",
                os=audit_ocsf.OS(
                    name="Windows",
                    type="Windows",
                    version="10.0.19041",
                ),
            )
        )
        .with_session(
            audit_ocsf.Session(
                uid="session-admin-123",
                uuid="550e8400-e29b-41d4-a716-************",
                issuer="domain-controller",
                created_time=datetime.now(timezone.utc),
                is_remote=False,
                is_mfa=True,
            )
        )
        .build()
    )

    logger.log_authentication(complex_event)
    print()

    # Example 3: API Activity Event
    print("3. API Activity - Create Resource (with Cloud Profile):")
    print(
        "   Code: APIActivityEventBuilder().with_api().with_cloud().with_http_request()..."
    )
    print("   Output:")
    api_event = (
        audit_ocsf.APIActivityEventBuilder(audit_ocsf.ActivityID.API_CREATE)
        .with_message("User created a new resource")
        .with_api(
            audit_ocsf.API(
                operation="CreateResource",
                service=audit_ocsf.APIService(
                    name="ResourceService",
                    uid="service-123",
                    version="v1.2.0",
                ),
                version="2.0",
            )
        )
        .with_actor(
            audit_ocsf.Actor(
                user=audit_ocsf.User(
                    uid="user123",
                    type_id=1,
                ),
            )
        )
        .with_cloud(
            audit_ocsf.Cloud(
                provider="AWS",
                region="us-east-1",
                account=audit_ocsf.CloudAccount(
                    uid="************",
                    name="production",
                    type="AWS Account",
                ),
            )
        )
        .with_src_endpoint(
            audit_ocsf.NetworkEndpoint(
                ip="*************",
                hostname="client.example.com",
                port=443,
            )
        )
        .with_http_request(
            audit_ocsf.HTTPRequest(
                method="POST",
                user_agent="MyApp/1.0",
                url=audit_ocsf.URL(
                    text="https://api.example.com/resources",
                    hostname="api.example.com",
                    path="/resources",
                    scheme="https",
                    port=443,
                ),
            )
        )
        .with_http_response(
            audit_ocsf.HTTPResponse(
                code=201,
                message="Created",
                length=256,
                latency=150,
            )
        )
        .with_severity(audit_ocsf.SeverityID.INFORMATIONAL)
        .build()
    )

    logger.log_api_activity(api_event)
    print()

    # Example 4: Failed API Activity with Security Control Profile
    print("4. Failed API Activity (with Security Control Profile):")
    print(
        "   Code: APIActivityEventBuilder().with_disposition(DispositionID.BLOCKED)..."
    )
    print("   Output:")
    failed_api_event = (
        audit_ocsf.APIActivityEventBuilder(audit_ocsf.ActivityID.API_DELETE)
        .with_message("Failed to delete resource - insufficient permissions")
        .with_api(
            audit_ocsf.API(
                operation="DeleteResource",
                service=audit_ocsf.APIService(
                    name="ResourceService",
                    uid="service-123",
                ),
                response=audit_ocsf.APIResponse(
                    code="403",
                    error="PERMISSION_DENIED",
                    message="User does not have permission to delete this resource",
                ),
            )
        )
        .with_actor(
            audit_ocsf.Actor(
                user=audit_ocsf.User(
                    name="Jane Smith",
                    uid="actor789",
                    type_id=1,
                ),
            )
        )
        .with_cloud(
            audit_ocsf.Cloud(
                provider="Azure",
                region="eastus",
                account=audit_ocsf.CloudAccount(
                    uid="azure-sub-789",
                    name="development",
                    type="Azure Subscription",
                ),
            )
        )
        .with_src_endpoint(
            audit_ocsf.NetworkEndpoint(
                ip="***********",
                hostname="admin-console.example.com",
            )
        )
        .with_http_response(
            audit_ocsf.HTTPResponse(
                code=403,
                message="Forbidden",
                length=128,
                latency=75,
            )
        )
        .with_severity(audit_ocsf.SeverityID.MEDIUM)
        .with_status(audit_ocsf.StatusID.FAILURE)
        .with_action(audit_ocsf.ActionID.DENIED)
        .with_disposition(audit_ocsf.DispositionID.BLOCKED)
        .build()
    )

    logger.log_api_activity(failed_api_event)
    print()

    print("=== End of Examples ===")


if __name__ == "__main__":
    main()
