"""
Test Utilities for OCSF Audit Logging

This module provides test utilities including MockPrinter for testing
OCSF audit logging functionality.
"""

import threading
from typing import List, Any, Tuple

from .audit_logger import OCSFAuditLogger


class MockPrinter:
    """
    MockPrinter is a test utility that captures printed output for testing purposes.
    This allows other projects to create mocked OCSF audit loggers for testing.

    Example usage in tests:

        mock_printer = MockPrinter()
        logger = new_ocsf_audit_logger(mock_printer)

        # Use logger in your code...
        logger.log_authentication(event)

        # Verify output in tests
        output = mock_printer.get_output()
        assert len(output) == 1
        assert "expected content" in output[0]
    """

    def __init__(self):
        self._lock = threading.Lock()
        self._output: List[str] = []

    def println(self, *args: Any) -> None:
        """Capture the printed output for later inspection"""
        with self._lock:
            # Convert all args to strings and join them
            parts = []
            for arg in args:
                if isinstance(arg, str):
                    parts.append(arg)
                else:
                    parts.append("")

            # Store the first string argument (which should be the JSON)
            if parts:
                self._output.append(parts[0])

    def get_output(self) -> List[str]:
        """Return all captured output"""
        with self._lock:
            # Return a copy to avoid race conditions
            return self._output.copy()

    def get_last_output(self) -> str:
        """Return the most recent output, or empty string if none"""
        with self._lock:
            if not self._output:
                return ""
            return self._output[-1]

    def clear(self) -> None:
        """Remove all captured output"""
        with self._lock:
            self._output.clear()

    def count(self) -> int:
        """Return the number of captured outputs"""
        with self._lock:
            return len(self._output)


def new_mock_ocsf_audit_logger() -> Tuple[OCSFAuditLogger, MockPrinter]:
    """
    Create a new OCSF audit logger with a MockPrinter for testing purposes.
    This is a convenience function for tests.

    Example usage:

        logger, mock_printer = new_mock_ocsf_audit_logger()

        # Use logger in your code...
        logger.log_authentication(event)

        # Verify output
        output = mock_printer.get_output()
        assert len(output) == 1

    Returns:
        Tuple of (OCSFAuditLogger, MockPrinter)
    """
    mock_printer = MockPrinter()
    logger = OCSFAuditLogger(mock_printer)
    return logger, mock_printer
