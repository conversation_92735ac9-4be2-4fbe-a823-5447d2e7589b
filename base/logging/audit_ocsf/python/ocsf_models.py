"""
OCSF Data Models

This module contains all OCSF data structures implemented as Python dataclasses
with proper type hints and JSON serialization support.
"""

from dataclasses import dataclass, field
from datetime import datetime
from typing import Optional, Dict, Any, List
import json


@dataclass
class Product:
    """Product represents the product that generated the event"""

    name: str
    version: Optional[str] = None
    vendor: Optional[str] = None
    feature: Optional[str] = None
    language: Optional[str] = None


@dataclass
class Metadata:
    """Metadata represents OCSF metadata object"""

    version: Optional[str] = None
    product: Optional[Product] = None
    profiles: Optional[List[str]] = None
    event_code: Optional[str] = None
    extensions: Optional[Dict[str, str]] = None
    log_name: Optional[str] = None
    log_provider: Optional[str] = None
    original_time: Optional[str] = None


@dataclass
class Group:
    """Group represents a user group"""

    name: str
    uid: Optional[str] = None
    type: Optional[str] = None


@dataclass
class Credential:
    """Credential represents user credentials"""

    uid: Optional[str] = None
    type: Optional[str] = None


@dataclass
class Account:
    """Account represents user account information"""

    name: Optional[str] = None
    type: Optional[str] = None
    type_id: Optional[int] = None
    uid: Optional[str] = None


@dataclass
class Session:
    """Session represents authentication session"""

    uid: Optional[str] = None
    uuid: Optional[str] = None
    issuer: Optional[str] = None
    created_time: Optional[datetime] = None
    expiration_time: Optional[datetime] = None
    is_remote: Optional[bool] = None
    is_mfa: Optional[bool] = None


@dataclass
class User:
    """User represents OCSF user object"""

    name: Optional[str] = None
    uid: Optional[str] = None
    type: Optional[str] = None
    type_id: Optional[int] = None
    domain: Optional[str] = None
    email_addr: Optional[str] = None
    full_name: Optional[str] = None
    groups: Optional[List[Group]] = None
    credential: Optional[Credential] = None
    account: Optional[Account] = None
    session: Optional[Session] = None
    risk_level: Optional[str] = None
    risk_level_id: Optional[int] = None
    risk_score: Optional[int] = None


@dataclass
class OS:
    """OS represents operating system information"""

    name: Optional[str] = None
    type: Optional[str] = None
    type_id: Optional[int] = None
    build: Optional[str] = None
    version: Optional[str] = None


@dataclass
class Location:
    """Location represents geographical location"""

    city: Optional[str] = None
    country: Optional[str] = None
    coordinates: Optional[List[float]] = None
    continent: Optional[str] = None
    region: Optional[str] = None


@dataclass
class NetworkInterface:
    """NetworkInterface represents network interface information"""

    name: Optional[str] = None
    type: Optional[str] = None
    uid: Optional[str] = None
    ip: Optional[str] = None
    mac: Optional[str] = None


@dataclass
class Subnet:
    """Subnet represents network subnet information"""

    name: Optional[str] = None
    type: Optional[str] = None
    uid: Optional[str] = None


@dataclass
class VPC:
    """VPC represents Virtual Private Cloud information"""

    name: Optional[str] = None
    type: Optional[str] = None
    uid: Optional[str] = None


@dataclass
class NetworkEndpoint:
    """NetworkEndpoint represents network endpoint information"""

    name: Optional[str] = None
    port: Optional[int] = None
    ip: Optional[str] = None
    hostname: Optional[str] = None
    domain: Optional[str] = None
    subnet: Optional[Subnet] = None
    vpc: Optional[VPC] = None
    location: Optional[Location] = None


@dataclass
class Device:
    """Device represents OCSF device object"""

    name: Optional[str] = None
    type: Optional[str] = None
    type_id: Optional[int] = None
    uid: Optional[str] = None
    hostname: Optional[str] = None
    ip: Optional[str] = None
    mac: Optional[str] = None
    os: Optional[OS] = None
    location: Optional[Location] = None
    network_interfaces: Optional[List[NetworkInterface]] = None


@dataclass
class Tenant:
    """Tenant represents tenant information in the audit log"""

    name: str


@dataclass
class OCSFAuditLog:
    """OCSFAuditLog represents the wrapper structure for OCSF events"""

    ocsf_event: Any
    type: str = "type.eng.augmentcode.com/OCSFAuditLog"
    tenant: Optional[Tenant] = None

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization"""
        result = {
            "ocsf_event": self.ocsf_event,
            "@type": self.type,
        }
        if self.tenant:
            result["tenant"] = {"name": self.tenant.name}
        return result


def to_dict(obj: Any) -> Any:
    """
    Convert dataclass objects to dictionaries for JSON serialization.
    Handles nested dataclasses, lists, and datetime objects.

    Fields marked with metadata={'serialize_null': True} will be included
    as null in the output even when their value is None.
    """
    if obj is None:
        return None
    elif isinstance(obj, datetime):
        return obj.isoformat() + "Z" if obj.tzinfo is None else obj.isoformat()
    elif hasattr(obj, "__dataclass_fields__"):
        # This is a dataclass
        result = {}

        for field_name, field_def in obj.__dataclass_fields__.items():
            value = getattr(obj, field_name)
            if value is not None:
                result[field_name] = to_dict(value)
            elif hasattr(field_def, "metadata") and field_def.metadata.get(
                "serialize_null", False
            ):
                # Include fields marked with serialize_null=True as null even when None
                result[field_name] = None
        return result
    elif isinstance(obj, list):
        return [to_dict(item) for item in obj]
    elif isinstance(obj, dict):
        return {key: to_dict(value) for key, value in obj.items()}
    else:
        return obj


def to_json(obj: Any, **kwargs) -> str:
    """Convert object to JSON string with proper serialization"""
    return json.dumps(to_dict(obj), **kwargs)
