"""
OCSF (Open Cybersecurity Schema Framework) Audit Logging for Python

This package provides OCSF-compliant audit logging functionality following
the OCSF Schema version 1.5.0 specification.

Main exports:
- OCSFAuditLogger: Main audit logger class
- Event classes: AuthenticationEvent, APIActivityEvent, AuthorizeSessionEvent
- Builder classes: For fluent event construction
- Constants: All OCSF enums and constants
- Test utilities: MockPrinter for testing
"""

# Import all main classes and constants for easy access
from .constants import (
    ActivityID,
    StatusID,
    SeverityID,
    ActionID,
    DispositionID,
    AuthProtocolID,
    LogonTypeID,
    # Class and Category constants
    CATEGORY_IAM,
    CATEGORY_APPLICATION_ACTIVITY,
    CLASS_AUTHENTICATION,
    CLASS_AUTHORIZE_SESSION,
    CLASS_API_ACTIVITY,
)

from .ocsf_models import (
    Metadata,
    Product,
    User,
    Group,
    Credential,
    Account,
    Session,
    Device,
    OS,
    Location,
    NetworkInterface,
    NetworkEndpoint,
    Subnet,
    VPC,
    Tenant,
    OCSFAuditLog,
)

from .base_event import BaseEvent

from .authentication_event import (
    AuthenticationEvent,
    AuthenticationEventBuilder,
    new_successful_logon_event,
    new_failed_logon_event,
    new_logoff_event,
)

from .api_activity_event import (
    APIActivityEvent,
    APIActivityEventBuilder,
    Actor,
    API,
    APIService,
    APIRequest,
    APIResponse,
    Cloud,
    CloudAccount,
    Project,
    HTTPRequest,
    HTTPResponse,
    URL,
    Process,
    File,
    ResourceDetails,
    Observable,
    Reputation,
    Trace,
)

from .authorize_session_event import (
    AuthorizeSessionEvent,
    AuthorizeSessionEventBuilder,
    new_assign_privileges_event,
    new_assign_groups_event,
)

from .audit_logger import (
    OCSFAuditLogger,
    Printer,
    StdoutPrinter,
    new_default_ocsf_audit_logger,
    new_ocsf_audit_logger,
)

from .test_utils import (
    MockPrinter,
    new_mock_ocsf_audit_logger,
)

__version__ = "1.5.0"
__all__ = [
    # Constants
    "ActivityID",
    "StatusID",
    "SeverityID",
    "ActionID",
    "DispositionID",
    "AuthProtocolID",
    "LogonTypeID",
    "CATEGORY_IAM",
    "CATEGORY_APPLICATION_ACTIVITY",
    "CLASS_AUTHENTICATION",
    "CLASS_AUTHORIZE_SESSION",
    "CLASS_API_ACTIVITY",
    # Models
    "Metadata",
    "Product",
    "User",
    "Group",
    "Credential",
    "Account",
    "Session",
    "Device",
    "OS",
    "Location",
    "NetworkInterface",
    "NetworkEndpoint",
    "Subnet",
    "VPC",
    "Tenant",
    "OCSFAuditLog",
    # Base Event
    "BaseEvent",
    # Authentication
    "AuthenticationEvent",
    "AuthenticationEventBuilder",
    "new_successful_logon_event",
    "new_failed_logon_event",
    "new_logoff_event",
    # API Activity
    "APIActivityEvent",
    "APIActivityEventBuilder",
    "Actor",
    "API",
    "APIService",
    "APIRequest",
    "APIResponse",
    "Cloud",
    "CloudAccount",
    "Project",
    "HTTPRequest",
    "HTTPResponse",
    "URL",
    "Process",
    "File",
    "ResourceDetails",
    "Observable",
    "Reputation",
    "Trace",
    # Authorize Session
    "AuthorizeSessionEvent",
    "AuthorizeSessionEventBuilder",
    "new_assign_privileges_event",
    "new_assign_groups_event",
    # Audit Logger
    "OCSFAuditLogger",
    "Printer",
    "StdoutPrinter",
    "new_default_ocsf_audit_logger",
    "new_ocsf_audit_logger",
    # Test Utils
    "MockPrinter",
    "new_mock_ocsf_audit_logger",
]
