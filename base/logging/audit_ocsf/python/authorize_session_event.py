"""
Authorize Session Event Implementation

This module contains the AuthorizeSessionEvent class and builder for OCSF
Authorize Session events with validation for mutually exclusive fields.
"""

from dataclasses import dataclass, field
from datetime import datetime, timezone
from typing import Optional, List

from .base_event import BaseEvent
from .constants import (
    ActivityID,
    SeverityID,
    StatusID,
    ActionID,
    CATEGORY_IAM,
    CLASS_AUTHORIZE_SESSION,
)
from .ocsf_models import (
    User,
    Group,
    Device,
    Session,
    NetworkEndpoint,
    Metadata,
    Product,
)


@dataclass
class AuthorizeSessionEvent(BaseEvent):
    """AuthorizeSessionEvent represents an OCSF Authorize Session event"""

    # Required
    user: User = field(default_factory=User)

    # Authorize Session specific fields (mutually exclusive)
    group: Optional[Group] = None  # Group assigned to the session
    privileges: Optional[List[str]] = None  # List of privileges assigned to the session

    def __post_init__(self):
        """Initialize user if not provided"""
        if self.user is None:
            self.user = User()

    def set_defaults(self) -> None:
        """Set default values for required fields"""
        self.category_uid = CATEGORY_IAM
        self.class_uid = CLASS_AUTHORIZE_SESSION
        self.type_uid = self.calculate_type_uid()

        if not self.time:
            self.time = datetime.now(timezone.utc)

        if self.severity_id == 0:
            self.severity_id = SeverityID.INFORMATIONAL

    def validate(self) -> None:
        """Validate that the event has all required fields"""
        # Validate common BaseEvent fields
        super().validate()

        # Validate AuthorizeSessionEvent-specific requirements
        # Validate that either group or privileges is set (mutually exclusive)
        if self.group is None and (
            self.privileges is None or len(self.privileges) == 0
        ):
            raise ValueError("either group or privileges must be specified")
        if (
            self.group is not None
            and self.privileges is not None
            and len(self.privileges) > 0
        ):
            raise ValueError("group and privileges are mutually exclusive")


class AuthorizeSessionEventBuilder:
    """AuthorizeSessionEventBuilder provides a fluent interface for building AuthorizeSessionEvent objects"""

    def __init__(self, activity_id: ActivityID):
        self.event = AuthorizeSessionEvent(
            activity_id=activity_id,
            category_uid=CATEGORY_IAM,
            class_uid=CLASS_AUTHORIZE_SESSION,
            severity_id=SeverityID.INFORMATIONAL,
            time=datetime.now(timezone.utc),
            type_uid=CLASS_AUTHORIZE_SESSION * 100 + int(activity_id),
            metadata=Metadata(
                version="1.5.0",
                product=Product(
                    name="Augment",
                    vendor="Augment Code",
                ),
                log_provider="augment-audit-ocsf",
            ),
            user=User(),  # Initialize with empty user
        )

    def with_user(self, user: User) -> "AuthorizeSessionEventBuilder":
        """Set the user information"""
        self.event.user = user
        return self

    def with_user_uid(self, uid: str, user_type: str) -> "AuthorizeSessionEventBuilder":
        """Set the user UID and type"""
        self.event.user.uid = uid
        self.event.user.type = user_type
        return self

    def with_user_name(
        self, name: str, user_type: str
    ) -> "AuthorizeSessionEventBuilder":
        """Set the user name and type"""
        self.event.user.name = name
        self.event.user.type = user_type
        return self

    def with_severity(self, severity_id: SeverityID) -> "AuthorizeSessionEventBuilder":
        """Set the severity level"""
        self.event.severity_id = severity_id
        return self

    def with_status(self, status_id: StatusID) -> "AuthorizeSessionEventBuilder":
        """Set the status information"""
        self.event.status_id = status_id
        return self

    def with_status_detail(
        self, status_code: Optional[str], status_detail: Optional[str]
    ) -> "AuthorizeSessionEventBuilder":
        """Set the status detail information"""
        if status_code:
            self.event.status_code = status_code
        if status_detail:
            self.event.status_detail = status_detail
        return self

    def with_action(self, action_id: ActionID) -> "AuthorizeSessionEventBuilder":
        """Set the action taken"""
        self.event.action_id = action_id
        return self

    def with_message(self, message: str) -> "AuthorizeSessionEventBuilder":
        """Set the event message"""
        self.event.message = message
        return self

    def with_time(self, event_time: datetime) -> "AuthorizeSessionEventBuilder":
        """Set the event time"""
        self.event.time = event_time
        return self

    def with_device(self, device: Device) -> "AuthorizeSessionEventBuilder":
        """Set the device information"""
        self.event.device = device
        return self

    def with_session(self, session: Session) -> "AuthorizeSessionEventBuilder":
        """Set the session information"""
        self.event.session = session
        return self

    def with_timezone_offset(self, offset: int) -> "AuthorizeSessionEventBuilder":
        """Set the timezone offset"""
        self.event.timezone_offset = offset
        return self

    def with_raw_data(self, raw_data: str) -> "AuthorizeSessionEventBuilder":
        """Set the raw event data"""
        self.event.raw_data = raw_data
        return self

    def with_group(self, group: Group) -> "AuthorizeSessionEventBuilder":
        """Set the group assigned to the session (mutually exclusive with privileges)"""
        self.event.group = group
        self.event.privileges = None  # Clear privileges to maintain mutual exclusivity
        return self

    def with_privileges(self, privileges: List[str]) -> "AuthorizeSessionEventBuilder":
        """Set the privileges assigned to the session (mutually exclusive with group)"""
        self.event.privileges = privileges
        self.event.group = None  # Clear group to maintain mutual exclusivity
        return self

    def with_src_endpoint(
        self, endpoint: NetworkEndpoint
    ) -> "AuthorizeSessionEventBuilder":
        """Set the source endpoint information (Recommended for AuthorizeSession)"""
        self.event.src_endpoint = endpoint
        return self

    def with_dst_endpoint(
        self, endpoint: NetworkEndpoint
    ) -> "AuthorizeSessionEventBuilder":
        """Set the destination endpoint information (Recommended for AuthorizeSession)"""
        self.event.dst_endpoint = endpoint
        return self

    def build(self) -> AuthorizeSessionEvent:
        """Create the final AuthorizeSessionEvent"""
        # Ensure type_uid is calculated if not already set
        if self.event.type_uid == 0:
            self.event.type_uid = self.event.calculate_type_uid()

        # Set default metadata if none provided
        if not self.event.metadata:
            self.event.metadata = Metadata(
                version="1.5.0",
                product=Product(
                    name="Augment",
                    vendor="Augment Code",
                ),
                log_provider="augment-audit-ocsf",
            )

        return self.event


# Convenience builder functions for common authorize session scenarios


def new_assign_privileges_event(
    user_id: str, user_type: str, privileges: List[str]
) -> AuthorizeSessionEventBuilder:
    """Create a builder for an assign privileges event"""
    return (
        AuthorizeSessionEventBuilder(ActivityID.ASSIGN_PRIVILEGES)
        .with_user_uid(user_id, user_type)
        .with_privileges(privileges)
        .with_status(StatusID.SUCCESS)
        .with_action(ActionID.ALLOWED)
        .with_severity(SeverityID.INFORMATIONAL)
    )


def new_assign_groups_event(
    user_id: str, user_type: str, group: Group
) -> AuthorizeSessionEventBuilder:
    """Create a builder for an assign groups event"""
    return (
        AuthorizeSessionEventBuilder(ActivityID.ASSIGN_GROUPS)
        .with_user_uid(user_id, user_type)
        .with_group(group)
        .with_status(StatusID.SUCCESS)
        .with_action(ActionID.ALLOWED)
        .with_severity(SeverityID.INFORMATIONAL)
    )
