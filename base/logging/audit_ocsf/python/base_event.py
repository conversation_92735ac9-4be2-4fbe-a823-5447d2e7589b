"""
Base Event Implementation

This module contains the BaseEvent class that provides common OCSF fields
and functionality shared across all event types.
"""

from dataclasses import dataclass, field
from datetime import datetime
from typing import Optional

from .constants import ActivityID, SeverityID, StatusID, ActionID
from .ocsf_models import <PERSON>ada<PERSON>, Device, Session, NetworkEndpoint


@dataclass
class BaseEvent:
    """BaseEvent contains common OCSF fields shared across multiple event types"""

    # Required fields
    activity_id: ActivityID
    category_uid: int
    class_uid: int
    severity_id: SeverityID
    time: datetime
    type_uid: int
    # metadata for each event type is assigned right before log output
    metadata: Optional[Metadata]

    # Recommended fields
    action_id: Optional[ActionID] = None
    status_id: Optional[StatusID] = None
    message: Optional[str] = None
    device: Optional[Device] = None
    session: Optional[Session] = None
    status_code: Optional[str] = None
    status_detail: Optional[str] = None
    # Not using None default below per requirements from Rubrik
    src_endpoint: Optional[NetworkEndpoint] = field(
        default=None, metadata={"serialize_null": True}
    )  # Recommended for Authentication and AuthorizeSession
    dst_endpoint: Optional[NetworkEndpoint] = field(
        default=None, metadata={"serialize_null": True}
    )  # Recommended for Authentication and AuthorizeSession

    # Optional fields
    count: Optional[int] = None
    duration: Optional[int] = None
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    timezone_offset: Optional[int] = None
    raw_data: Optional[str] = None

    def calculate_type_uid(self) -> int:
        """Calculate the OCSF type_uid from class_uid and activity_id"""
        return self.class_uid * 100 + int(self.activity_id)

    def validate(self) -> None:
        """Validate that the BaseEvent has all required OCSF fields"""
        if self.activity_id == 0:
            raise ValueError("activity_id is required")
        if self.category_uid == 0:
            raise ValueError("category_uid is required")
        if self.class_uid == 0:
            raise ValueError("class_uid is required")
        if self.severity_id == 0:
            raise ValueError("severity_id is required")
        if not self.time:
            raise ValueError("time is required")
        if self.type_uid == 0:
            raise ValueError("type_uid is required")
        if not self.metadata or not self.metadata.version:
            raise ValueError("metadata.version is required")

    def to_dict(self) -> dict:
        """Convert the event to a dictionary for JSON serialization"""
        from .ocsf_models import to_dict

        return to_dict(self)
