"""
Authentication Event Implementation

This module contains the AuthenticationEvent class and builder for OCSF
Authentication events with fluent builder pattern.
"""

from dataclasses import dataclass, field
from datetime import datetime, timezone
from typing import Optional

from .base_event import BaseEvent
from .constants import (
    ActivityID,
    SeverityID,
    StatusID,
    ActionID,
    AuthProtocolID,
    LogonTypeID,
    CATEGORY_IAM,
    CLASS_AUTHENTICATION,
)
from .ocsf_models import User, Device, Session, NetworkEndpoint, Metadata, Product


@dataclass
class AuthenticationEvent(BaseEvent):
    """AuthenticationEvent represents an OCSF Authentication event"""

    # Required
    user: User = field(default_factory=User)

    # Authentication-specific fields
    is_remote: Optional[bool] = None
    is_mfa: Optional[bool] = None
    is_cleartext: Optional[bool] = None
    auth_protocol: Optional[str] = None
    auth_protocol_id: Optional[int] = None
    logon_type: Optional[str] = None
    logon_type_id: Optional[int] = None

    def __post_init__(self):
        """Initialize user if not provided"""
        if self.user is None:
            self.user = User()

    def set_defaults(self) -> None:
        """Set default values for required fields"""
        self.category_uid = CATEGORY_IAM
        self.class_uid = CLASS_AUTHENTICATION
        self.type_uid = self.calculate_type_uid()

        if not self.time:
            self.time = datetime.now(timezone.utc)

        if self.severity_id == 0:
            self.severity_id = SeverityID.INFORMATIONAL

    def validate(self) -> None:
        """Validate that the event has all required fields"""
        # Validate common BaseEvent fields
        super().validate()

        # AuthenticationEvent has no additional required fields beyond BaseEvent
        # The user field is required but we'll allow it to be validated at application level


class AuthenticationEventBuilder:
    """AuthenticationEventBuilder provides a fluent interface for building AuthenticationEvent objects"""

    def __init__(self, activity_id: ActivityID):
        self.event = AuthenticationEvent(
            activity_id=activity_id,
            category_uid=CATEGORY_IAM,
            class_uid=CLASS_AUTHENTICATION,
            severity_id=SeverityID.INFORMATIONAL,
            time=datetime.now(timezone.utc),
            type_uid=CLASS_AUTHENTICATION * 100 + int(activity_id),
            metadata=Metadata(
                version="1.5.0",
                product=Product(
                    name="Augment",
                    vendor="Augment Code",
                ),
                log_provider="augment-audit-ocsf",
            ),
            user=User(),  # Initialize with empty user
        )

    def with_user(self, user: User) -> "AuthenticationEventBuilder":
        """Set the user information"""
        self.event.user = user
        return self

    def with_user_uid(self, uid: str, user_type: str) -> "AuthenticationEventBuilder":
        """Set the user UID and type"""
        self.event.user.uid = uid
        self.event.user.type = user_type
        return self

    def with_user_name(self, name: str, user_type: str) -> "AuthenticationEventBuilder":
        """Set the user name and type"""
        self.event.user.name = name
        self.event.user.type = user_type
        return self

    def with_severity(self, severity_id: SeverityID) -> "AuthenticationEventBuilder":
        """Set the severity level"""
        self.event.severity_id = severity_id
        return self

    def with_status(self, status_id: StatusID) -> "AuthenticationEventBuilder":
        """Set the status information"""
        self.event.status_id = status_id
        return self

    def with_status_detail(
        self, status_code: Optional[str], status_detail: Optional[str]
    ) -> "AuthenticationEventBuilder":
        """Set the status detail information"""
        if status_code:
            self.event.status_code = status_code
        if status_detail:
            self.event.status_detail = status_detail
        return self

    def with_action(self, action_id: ActionID) -> "AuthenticationEventBuilder":
        """Set the action taken"""
        self.event.action_id = action_id
        return self

    def with_message(self, message: str) -> "AuthenticationEventBuilder":
        """Set the event message"""
        self.event.message = message
        return self

    def with_time(self, event_time: datetime) -> "AuthenticationEventBuilder":
        """Set the event time"""
        self.event.time = event_time
        return self

    def with_device(self, device: Device) -> "AuthenticationEventBuilder":
        """Set the device information"""
        self.event.device = device
        return self

    def with_session(self, session: Session) -> "AuthenticationEventBuilder":
        """Set the session information"""
        self.event.session = session
        return self

    def with_timezone_offset(self, offset: int) -> "AuthenticationEventBuilder":
        """Set the timezone offset"""
        self.event.timezone_offset = offset
        return self

    def with_raw_data(self, raw_data: str) -> "AuthenticationEventBuilder":
        """Set the raw event data"""
        self.event.raw_data = raw_data
        return self

    def with_remote(self, is_remote: bool) -> "AuthenticationEventBuilder":
        """Set whether the authentication is remote"""
        self.event.is_remote = is_remote
        return self

    def with_mfa(self, is_mfa: bool) -> "AuthenticationEventBuilder":
        """Set whether multi-factor authentication was used"""
        self.event.is_mfa = is_mfa
        return self

    def with_cleartext(self, is_cleartext: bool) -> "AuthenticationEventBuilder":
        """Set whether the authentication used cleartext"""
        self.event.is_cleartext = is_cleartext
        return self

    def with_auth_protocol(
        self, protocol_id: AuthProtocolID
    ) -> "AuthenticationEventBuilder":
        """Set the authentication protocol"""
        self.event.auth_protocol_id = int(protocol_id)
        self.event.auth_protocol = str(protocol_id)
        return self

    def with_logon_type(
        self, logon_type_id: LogonTypeID
    ) -> "AuthenticationEventBuilder":
        """Set the logon type"""
        self.event.logon_type_id = int(logon_type_id)
        self.event.logon_type = str(logon_type_id)
        return self

    def with_src_endpoint(
        self, endpoint: NetworkEndpoint
    ) -> "AuthenticationEventBuilder":
        """Set the source endpoint information (Recommended for Authentication)"""
        self.event.src_endpoint = endpoint
        return self

    def with_dst_endpoint(
        self, endpoint: NetworkEndpoint
    ) -> "AuthenticationEventBuilder":
        """Set the destination endpoint information (Recommended for Authentication)"""
        self.event.dst_endpoint = endpoint
        return self

    def build(self) -> AuthenticationEvent:
        """Create the final AuthenticationEvent"""
        # Ensure type_uid is calculated if not already set
        if self.event.type_uid == 0:
            self.event.type_uid = self.event.calculate_type_uid()

        # Set default metadata if none provided
        if not self.event.metadata:
            self.event.metadata = Metadata(
                version="1.5.0",
                product=Product(
                    name="Augment",
                    vendor="Augment Code",
                ),
                log_provider="augment-audit-ocsf",
            )

        return self.event


# Convenience builder functions for common authentication scenarios


def new_successful_logon_event(
    user_id: str, user_type: str
) -> AuthenticationEventBuilder:
    """Create a builder for a successful logon event"""
    return (
        AuthenticationEventBuilder(ActivityID.LOGON)
        .with_user_uid(user_id, user_type)
        .with_status(StatusID.SUCCESS)
        .with_action(ActionID.ALLOWED)
        .with_severity(SeverityID.INFORMATIONAL)
    )


def new_failed_logon_event(user_id: str, user_type: str) -> AuthenticationEventBuilder:
    """Create a builder for a failed logon event"""
    return (
        AuthenticationEventBuilder(ActivityID.LOGON)
        .with_user_uid(user_id, user_type)
        .with_status(StatusID.FAILURE)
        .with_action(ActionID.DENIED)
        .with_severity(SeverityID.MEDIUM)
    )


def new_logoff_event(user_id: str, user_type: str) -> AuthenticationEventBuilder:
    """Create a builder for a logoff event"""
    return (
        AuthenticationEventBuilder(ActivityID.LOGOFF)
        .with_user_uid(user_id, user_type)
        .with_status(StatusID.SUCCESS)
        .with_action(ActionID.ALLOWED)
        .with_severity(SeverityID.INFORMATIONAL)
    )
