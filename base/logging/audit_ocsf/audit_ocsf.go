package auditocsf

import (
	"encoding/json"
	"fmt"
	"os"
	"sync"
	"time"
)

// Metadata represents OCSF metadata object
type Metadata struct {
	Version      string            `json:"version"`
	Product      *Product          `json:"product,omitempty"`
	Profiles     []string          `json:"profiles,omitempty"`
	EventCode    string            `json:"event_code,omitempty"`
	Extensions   map[string]string `json:"extensions,omitempty"`
	LogName      string            `json:"log_name,omitempty"`
	LogProvider  string            `json:"log_provider,omitempty"`
	OriginalTime string            `json:"original_time,omitempty"`
}

// Product represents the product that generated the event
type Product struct {
	Name     string `json:"name"`
	Version  string `json:"version,omitempty"`
	Vendor   string `json:"vendor,omitempty"`
	Feature  string `json:"feature,omitempty"`
	Language string `json:"lang,omitempty"`
}

// User represents OCSF user object
type User struct {
	Name        string      `json:"name,omitempty"`
	UID         string      `json:"uid,omitempty"`
	Type        string      `json:"type,omitempty"`
	TypeID      int         `json:"type_id,omitempty"`
	Domain      string      `json:"domain,omitempty"`
	EmailAddr   string      `json:"email_addr,omitempty"`
	FullName    string      `json:"full_name,omitempty"`
	Groups      []Group     `json:"groups,omitempty"`
	Credential  *Credential `json:"credential,omitempty"`
	Account     *Account    `json:"account,omitempty"`
	Session     *Session    `json:"session,omitempty"`
	RiskLevel   string      `json:"risk_level,omitempty"`
	RiskLevelID int         `json:"risk_level_id,omitempty"`
	RiskScore   int         `json:"risk_score,omitempty"`
}

// Group represents a user group
type Group struct {
	Name string `json:"name"`
	UID  string `json:"uid,omitempty"`
	Type string `json:"type,omitempty"`
}

// Credential represents user credentials
type Credential struct {
	UID  string `json:"uid,omitempty"`
	Type string `json:"type,omitempty"`
}

// Account represents user account information
type Account struct {
	Name   string `json:"name,omitempty"`
	Type   string `json:"type,omitempty"`
	TypeID int    `json:"type_id,omitempty"`
	UID    string `json:"uid,omitempty"`
}

// Session represents authentication session
type Session struct {
	UID            string    `json:"uid,omitempty"`
	UUID           string    `json:"uuid,omitempty"`
	Issuer         string    `json:"issuer,omitempty"`
	CreatedTime    time.Time `json:"created_time,omitempty"`
	ExpirationTime time.Time `json:"expiration_time,omitempty"`
	IsRemote       bool      `json:"is_remote,omitempty"`
	IsMFA          bool      `json:"is_mfa,omitempty"`
}

// Device represents OCSF device object
type Device struct {
	Name              string             `json:"name,omitempty"`
	Type              string             `json:"type,omitempty"`
	TypeID            int                `json:"type_id,omitempty"`
	UID               string             `json:"uid,omitempty"`
	Hostname          string             `json:"hostname,omitempty"`
	IP                string             `json:"ip,omitempty"`
	MAC               string             `json:"mac,omitempty"`
	OS                *OS                `json:"os,omitempty"`
	Location          *Location          `json:"location,omitempty"`
	NetworkInterfaces []NetworkInterface `json:"network_interfaces,omitempty"`
}

// OS represents operating system information
type OS struct {
	Name    string `json:"name,omitempty"`
	Type    string `json:"type,omitempty"`
	TypeID  int    `json:"type_id,omitempty"`
	Build   string `json:"build,omitempty"`
	Version string `json:"version,omitempty"`
}

// Location represents geographical location
type Location struct {
	City        string    `json:"city,omitempty"`
	Country     string    `json:"country,omitempty"`
	Coordinates []float64 `json:"coordinates,omitempty"`
	Continent   string    `json:"continent,omitempty"`
	Region      string    `json:"region,omitempty"`
}

// NetworkInterface represents network interface information
type NetworkInterface struct {
	Name string `json:"name,omitempty"`
	Type string `json:"type,omitempty"`
	UID  string `json:"uid,omitempty"`
	IP   string `json:"ip,omitempty"`
	MAC  string `json:"mac,omitempty"`
}

// NetworkEndpoint represents network endpoint information
type NetworkEndpoint struct {
	Name     string    `json:"name,omitempty"`
	Port     int       `json:"port,omitempty"`
	IP       string    `json:"ip,omitempty"`
	Hostname string    `json:"hostname,omitempty"`
	Domain   string    `json:"domain,omitempty"`
	Subnet   *Subnet   `json:"subnet,omitempty"`
	VPC      *VPC      `json:"vpc,omitempty"`
	Location *Location `json:"location,omitempty"`
}

// Subnet represents network subnet information
type Subnet struct {
	Name string `json:"name,omitempty"`
	Type string `json:"type,omitempty"`
	UID  string `json:"uid,omitempty"`
}

// VPC represents Virtual Private Cloud information
type VPC struct {
	Name string `json:"name,omitempty"`
	Type string `json:"type,omitempty"`
	UID  string `json:"uid,omitempty"`
}

// OCSFAuditLog represents the wrapper structure for OCSF events
// Tenant represents tenant information in the audit log
type Tenant struct {
	Name string `json:"name"`
}

type OCSFAuditLog struct {
	OCSFEvent any    `json:"ocsf_event"`
	Type      string `json:"@type"`
	Tenant    Tenant `json:"tenant"`
}

// BaseEvent contains common OCSF fields shared across multiple event types
type BaseEvent struct {
	// Required fields
	ActivityID  ActivityID `json:"activity_id"`
	CategoryUID int        `json:"category_uid"`
	ClassUID    int        `json:"class_uid"`
	SeverityID  SeverityID `json:"severity_id"`
	Time        time.Time  `json:"time"`
	TypeUID     int64      `json:"type_uid"`
	Metadata    Metadata   `json:"metadata"`

	// Recommended fields
	ActionID     *ActionID `json:"action_id,omitempty"`
	StatusID     *StatusID `json:"status_id"`
	Message      string    `json:"message,omitempty"`
	Device       *Device   `json:"device,omitempty"`
	Session      *Session  `json:"session,omitempty"`
	StatusCode   string    `json:"status_code,omitempty"`
	StatusDetail string    `json:"status_detail,omitempty"`
	// Not using omitempty below per requirements from Rubrik
	SrcEndpoint *NetworkEndpoint `json:"src_endpoint"` // Recommended for Authentication and AuthorizeSession
	DstEndpoint *NetworkEndpoint `json:"dst_endpoint"` // Recommended for Authentication and AuthorizeSession

	// Optional fields
	Count          *int       `json:"count,omitempty"`
	Duration       *int64     `json:"duration,omitempty"`
	StartTime      *time.Time `json:"start_time,omitempty"`
	EndTime        *time.Time `json:"end_time,omitempty"`
	TimezoneOffset *int       `json:"timezone_offset,omitempty"`
	RawData        string     `json:"raw_data,omitempty"`
}

// CalculateTypeUID calculates the OCSF type_uid from class_uid and activity_id
func (e *BaseEvent) CalculateTypeUID() int64 {
	return int64(e.ClassUID)*100 + int64(e.ActivityID)
}

// Validate checks if the BaseEvent has all required OCSF fields
func (e *BaseEvent) Validate() error {
	if e.ActivityID == 0 {
		return fmt.Errorf("activity_id is required")
	}
	if e.CategoryUID == 0 {
		return fmt.Errorf("category_uid is required")
	}
	if e.ClassUID == 0 {
		return fmt.Errorf("class_uid is required")
	}
	if e.SeverityID == 0 {
		return fmt.Errorf("severity_id is required")
	}
	if e.Time.IsZero() {
		return fmt.Errorf("time is required")
	}
	if e.TypeUID == 0 {
		return fmt.Errorf("type_uid is required")
	}
	if e.Metadata.Version == "" {
		return fmt.Errorf("metadata.version is required")
	}
	return nil
}

// Printer interface for output abstraction (same as existing audit package)
type Printer interface {
	Println(args ...any)
}

// StdoutPrinter implements Printer interface for stdout output
type StdoutPrinter struct {
	output *sync.Mutex
}

// NewStdoutPrinter creates a new stdout printer
func NewStdoutPrinter() StdoutPrinter {
	return StdoutPrinter{
		output: &sync.Mutex{},
	}
}

// Println prints to stdout with synchronization
func (p StdoutPrinter) Println(args ...any) {
	p.output.Lock()
	defer p.output.Unlock()
	fmt.Println(args...)
	os.Stdout.Sync()
}

// OCSFAuditLogger provides OCSF-compliant audit logging
type OCSFAuditLogger struct {
	print Printer
}

// NewDefaultOCSFAuditLogger creates a new OCSF audit logger with default settings
func NewDefaultOCSFAuditLogger() *OCSFAuditLogger {
	return &OCSFAuditLogger{
		print: NewStdoutPrinter(),
	}
}

// NewOCSFAuditLogger creates a new OCSF audit logger with custom printer
func NewOCSFAuditLogger(printer Printer) *OCSFAuditLogger {
	return &OCSFAuditLogger{
		print: printer,
	}
}

// LogAuthentication logs an OCSF authentication event
func (l *OCSFAuditLogger) LogAuthentication(event AuthenticationEvent) error {
	return l.LogAuthenticationWithTenant(event, "")
}

// LogAuthenticationWithTenant logs an OCSF authentication event with a specific tenant
func (l *OCSFAuditLogger) LogAuthenticationWithTenant(event AuthenticationEvent, tenant string) error {
	// Set defaults
	event.SetDefaults()

	// Validate the event
	if err := event.Validate(); err != nil {
		return fmt.Errorf("invalid authentication event: %w", err)
	}

	// Wrap the event in the OCSFAuditLog structure
	auditLog := OCSFAuditLog{
		OCSFEvent: event,
		Type:      "type.eng.augmentcode.com/OCSFAuditLog",
		Tenant:    Tenant{Name: tenant},
	}

	// Marshal to JSON
	jsonData, err := json.Marshal(auditLog)
	if err != nil {
		return fmt.Errorf("failed to marshal audit log: %w", err)
	}

	// Print the event
	l.print.Println(string(jsonData))
	return nil
}

// LogUserLogon logs a user logon event
func (l *OCSFAuditLogger) LogUserLogon(userID, userType, tenantName, message string) error {
	event := NewSuccessfulLogonEvent(userID, userType).
		WithMessage(message).
		Build()

	return l.LogAuthenticationWithTenant(event, tenantName)
}

// LogFailedAuthentication logs a failed authentication attempt
func (l *OCSFAuditLogger) LogFailedAuthentication(userID, userType, tenantName, message, statusDetail string) error {
	event := NewFailedLogonEvent(userID, userType).
		WithMessage(message).
		WithStatusDetail("", statusDetail). // No status code provided in this method
		Build()

	return l.LogAuthenticationWithTenant(event, tenantName)
}

// LogAuthorizeSession logs an OCSF authorize session event
func (l *OCSFAuditLogger) LogAuthorizeSession(event AuthorizeSessionEvent) error {
	return l.LogAuthorizeSessionWithTenant(event, "")
}

// LogAuthorizeSessionWithTenant logs an OCSF authorize session event with a specific tenant
func (l *OCSFAuditLogger) LogAuthorizeSessionWithTenant(event AuthorizeSessionEvent, tenant string) error {
	// Set defaults
	event.SetDefaults()

	// Validate the event
	if err := event.Validate(); err != nil {
		return fmt.Errorf("invalid authorize session event: %w", err)
	}

	// Wrap the event in the OCSFAuditLog structure
	auditLog := OCSFAuditLog{
		OCSFEvent: event,
		Type:      "type.eng.augmentcode.com/OCSFAuditLog",
		Tenant:    Tenant{Name: tenant},
	}

	// Marshal to JSON
	jsonData, err := json.Marshal(auditLog)
	if err != nil {
		return fmt.Errorf("failed to marshal audit log: %w", err)
	}

	// Print the event
	l.print.Println(string(jsonData))
	return nil
}

// LogAPIActivity logs an OCSF API activity event
func (l *OCSFAuditLogger) LogAPIActivity(event APIActivityEvent) error {
	return l.LogAPIActivityWithTenant(event, "")
}

// LogAPIActivityWithTenant logs an OCSF API activity event with a specific tenant
func (l *OCSFAuditLogger) LogAPIActivityWithTenant(event APIActivityEvent, tenant string) error {
	// Set defaults
	event.SetDefaults()

	// Validate the event
	if err := event.Validate(); err != nil {
		return fmt.Errorf("invalid API activity event: %w", err)
	}

	// Wrap the event in the OCSFAuditLog structure
	auditLog := OCSFAuditLog{
		OCSFEvent: event,
		Type:      "type.eng.augmentcode.com/OCSFAuditLog",
		Tenant:    Tenant{Name: tenant},
	}

	// Marshal to JSON
	jsonData, err := json.Marshal(auditLog)
	if err != nil {
		return fmt.Errorf("failed to marshal audit log: %w", err)
	}

	// Print the event
	l.print.Println(string(jsonData))
	return nil
}
