package auditocsf

import (
	"encoding/json"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestAuthenticationEvent_SetDefaults(t *testing.T) {
	event := AuthenticationEvent{
		BaseEvent: BaseEvent{
			ActivityID: ActivityLogon,
		},
	}

	event.SetDefaults()

	assert.Equal(t, CategoryIAM, event.CategoryUID)
	assert.Equal(t, ClassAuthentication, event.ClassUID)
	assert.Equal(t, int64(300201), event.TypeUID) // 3002 * 100 + 1
	assert.Equal(t, SeverityInformational, event.SeverityID)
	assert.False(t, event.Time.IsZero())
}

func TestAuthenticationEvent_CalculateTypeUID(t *testing.T) {
	tests := []struct {
		name       string
		classUID   int
		activityID ActivityID
		expected   int64
	}{
		{"Logon", ClassAuthentication, ActivityLogon, 300201},
		{"Logoff", ClassAuthentication, ActivityLogoff, 300202},
		{"Auth Ticket", ClassAuthentication, ActivityAuthenticationTicket, 300203},
		{"Service Ticket", ClassAuthentication, ActivityServiceTicketRequest, 300204},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			event := AuthenticationEvent{
				BaseEvent: BaseEvent{
					ClassUID:   tt.classUID,
					ActivityID: tt.activityID,
				},
			}
			assert.Equal(t, tt.expected, event.CalculateTypeUID())
		})
	}
}

func TestAuthenticationEvent_Validate(t *testing.T) {
	validEvent := AuthenticationEvent{
		BaseEvent: BaseEvent{
			ActivityID:  ActivityLogon,
			CategoryUID: CategoryIAM,
			ClassUID:    ClassAuthentication,
			SeverityID:  SeverityInformational,
			Time:        time.Now(),
			TypeUID:     300201,
			Metadata: Metadata{
				Version: "1.5.0",
			},
		},
	}

	t.Run("Valid event", func(t *testing.T) {
		err := validEvent.Validate()
		assert.NoError(t, err)
	})

	t.Run("Missing activity_id", func(t *testing.T) {
		event := validEvent
		event.ActivityID = 0
		err := event.Validate()
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "activity_id is required")
	})

	t.Run("Missing user", func(t *testing.T) {
		event := validEvent
		event.User = User{}
		err := event.Validate()
		assert.NoError(t, err) // User validation was removed
	})

	t.Run("Missing metadata version", func(t *testing.T) {
		event := validEvent
		event.Metadata.Version = ""
		err := event.Validate()
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "metadata.version is required")
	})
}

func TestOCSFAuditLogger_LogAuthentication(t *testing.T) {
	mockPrinter := &MockPrinter{}
	logger := NewOCSFAuditLogger(mockPrinter)

	event := AuthenticationEvent{
		BaseEvent: BaseEvent{
			ActivityID: ActivityLogon,
			Message:    "Custom authentication event",
			Metadata: Metadata{
				Version: "1.5.0",
				Product: &Product{
					Name:    "Test",
					Vendor:  "Test Vendor",
					Feature: "audit-ocsf",
				},
			},
		},
	}

	err := logger.LogAuthentication(event)
	require.NoError(t, err)

	output := mockPrinter.GetOutput()
	require.Len(t, output, 1)

	var auditLog OCSFAuditLog
	err = json.Unmarshal([]byte(output[0]), &auditLog)
	require.NoError(t, err)

	// Verify wrapper fields
	assert.Equal(t, "type.eng.augmentcode.com/OCSFAuditLog", auditLog.Type)
	assert.Equal(t, "", auditLog.Tenant.Name)

	// Verify the event was properly wrapped
	eventData, ok := auditLog.OCSFEvent.(map[string]interface{})
	require.True(t, ok)
	assert.Equal(t, float64(1), eventData["activity_id"])
	assert.Equal(t, "Custom authentication event", eventData["message"])
}

func TestNewSuccessfulLogonEvent(t *testing.T) {
	event := NewSuccessfulLogonEvent("user123", "INTERNAL_IAP").Build()

	assert.Equal(t, ActivityLogon, event.ActivityID)
	assert.Equal(t, "user123", event.User.UID)
	assert.Equal(t, "INTERNAL_IAP", event.User.Type)
	assert.Equal(t, StatusSuccess, *event.StatusID)
	assert.Equal(t, ActionAllowed, *event.ActionID)
	assert.Equal(t, SeverityInformational, event.SeverityID)
}

func TestNewFailedLogonEvent(t *testing.T) {
	event := NewFailedLogonEvent("user123", "INTERNAL_IAP").Build()

	assert.Equal(t, ActivityLogon, event.ActivityID)
	assert.Equal(t, "user123", event.User.UID)
	assert.Equal(t, "INTERNAL_IAP", event.User.Type)
	assert.Equal(t, StatusFailure, *event.StatusID)
	assert.Equal(t, ActionDenied, *event.ActionID)
	assert.Equal(t, SeverityMedium, event.SeverityID)
}

func TestNewLogoffEvent(t *testing.T) {
	event := NewLogoffEvent("user123", "INTERNAL_IAP").Build()

	assert.Equal(t, ActivityLogoff, event.ActivityID)
	assert.Equal(t, "user123", event.User.UID)
	assert.Equal(t, "INTERNAL_IAP", event.User.Type)
	assert.Equal(t, StatusSuccess, *event.StatusID)
	assert.Equal(t, ActionAllowed, *event.ActionID)
	assert.Equal(t, SeverityInformational, event.SeverityID)
}
