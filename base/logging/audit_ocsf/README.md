# OCSF Audit Logging

This package provides OCSF (Open Cybersecurity Schema Framework) compliant audit logging functionality for the Augment platform. It implements the OCSF Schema version 1.5.0 specification, focusing on the Authentication category.

## Overview

The OCSF audit logger runs alongside the existing audit logging system without conflicts or interference. It provides structured logging that conforms to the OCSF standard, enabling better interoperability with security tools and SIEM systems.

## Features

- **OCSF 1.5.0 Compliance**: Implements the Authentication class (3002) from the OCSF specification
- **Multi-language Support**: Go implementation with planned Python and Ruby support
- **Coexistence**: Runs alongside existing audit logging without interference
- **Structured Logging**: Provides standardized, machine-readable audit logs

## OCSF Authentication Class

This implementation focuses on the Authentication class (3002) which reports authentication session activities including:

- User logon/logoff attempts
- Authentication ticket requests (Kerberos)
- Service ticket operations
- Pre-authentication stages

### Required Fields

The implementation includes all required OCSF fields:

- `activity_id`: The normalized identifier of the activity that triggered the event
- `category_uid`: The category unique identifier (3 for Identity & Access Management)
- `class_uid`: The unique identifier of the class (3002 for Authentication)
- `severity_id`: The normalized identifier of the event severity
- `time`: The normalized event occurrence time
- `type_uid`: The event type ID (calculated as class_uid * 100 + activity_id)
- `user`: The subject (user/role or account) to authenticate
- `metadata`: The metadata associated with the event

### Recommended Fields

The implementation also includes recommended fields for enhanced logging:

- `action_id`: The action taken by the system
- `disposition_id`: The outcome or disposition of the action
- `status_id`: The normalized identifier of the event status
- `message`: Description of the event
- `device`: Information about the device involved
- `session`: The authenticated user session details

## Project Structure

- `audit_ocsf.go` - Main OCSF audit logger implementation with data structures, Builder pattern, and logging methods
- `constants.go` - All OCSF constant definitions (Activity IDs, Status IDs, Severity IDs, etc.) with String() methods
- `constants_test.go` - Unit tests for OCSF constants and their string representations
- `audit_ocsf_test.go` - Unit tests for the main audit logger functionality
- `builder_test.go` - Comprehensive unit tests for the Builder pattern
- `example_test.go` - Example code demonstrating various usage patterns
- `BUILD` - Bazel build configuration for Go, Python, and Rust
- `audit_ocsf.py` - Python implementation placeholder
- `audit_ocsf.rs` - Rust implementation placeholder
- `Cargo.toml` - Rust package configuration

## Builder Pattern

The OCSF audit logger provides a fluent Builder pattern for creating complex events. This makes it easy to construct events with the exact fields you need while ensuring OCSF compliance.

### Builder Features

- **Fluent Interface**: Chain method calls for readable event construction
- **Type Safety**: Compile-time validation of OCSF constants and field types
- **Automatic Defaults**: Required fields are automatically set with sensible defaults
- **Convenience Builders**: Pre-configured builders for common authentication scenarios
- **Extensible**: Easy to add custom metadata and extensions

### Available Builders

- `NewAuthenticationEventBuilder()` - Generic builder for any authentication event
- `NewSuccessfulLogonEvent(userID, userType)` - Pre-configured for successful logons
- `NewFailedLogonEvent(userID, userType)` - Pre-configured for failed logons
- `NewLogoffEvent(userID, userType)` - Pre-configured for logoff events
- `NewKerberosTicketEvent(userID, userType)` - Pre-configured for Kerberos ticket events
- `NewServiceTicketEvent(userID, userType)` - Pre-configured for service ticket events
- `NewPreauthEvent(userID, userType)` - Pre-configured for pre-authentication events

## Usage

```go
import "github.com/augmentcode/augment/base/logging/audit_ocsf"

// Create a new OCSF audit logger
logger := auditocsf.NewDefaultOCSFAuditLogger()

// Method 1: Use convenience methods
logger.LogUserLogon("user123", "INTERNAL_IAP", "tenant456", "User authenticated successfully")
logger.LogFailedAuthentication("user123", "INTERNAL_IAP", "tenant456", "Authentication failed", "INVALID_CREDENTIALS")

// Method 2: Use Builder pattern for simple events
event := auditocsf.NewSuccessfulLogonEvent("user123", "INTERNAL_IAP").
    WithMessage("User logged in via SAML SSO").
    WithMFA(true).
    WithRemote(true).
    WithAuthProtocol(auditocsf.AuthProtocolSAML).
    WithExtension("tenant", "tenant456").
    Build()
logger.LogAuthentication(event)

// Method 3: Use Builder pattern for complex custom events
event = auditocsf.NewAuthenticationEventBuilder().
    WithActivity(auditocsf.ActivityLogon).
    WithUserUID("user123", "INTERNAL_IAP").
    WithSeverity(auditocsf.SeverityHigh).
    WithStatus(auditocsf.StatusSuccess).
    WithAction(auditocsf.ActionAllowed).
    WithDisposition(auditocsf.DispositionAllowed).
    WithMessage("High-privilege user authentication").
    WithMFA(true).
    WithRemote(false).
    WithAuthProtocol(auditocsf.AuthProtocolKerberos).
    WithLogonType(auditocsf.LogonTypeInteractive).
    WithDevice(auditocsf.Device{
        Name:     "workstation-01",
        Hostname: "ws01.example.com",
        IP:       "*************",
    }).
    WithSession(auditocsf.Session{
        UID:      "session-123",
        IsRemote: false,
        IsMFA:    true,
    }).
    WithExtension("tenant", "tenant456").
    WithExtension("environment", "production").
    Build()
logger.LogAuthentication(event)

// Method 4: Use convenience builders for specific scenarios
kerberosEvent := auditocsf.NewKerberosTicketEvent("user123", "INTERNAL_IAP").
    WithMessage("Kerberos TGT requested").
    WithExtension("tenant", "tenant456").
    Build()
logger.LogAuthentication(kerberosEvent)

failedEvent := auditocsf.NewFailedLogonEvent("user456", "EXTERNAL").
    WithMessage("Failed authentication attempt").
    WithStatusDetail("0x18", "INVALID_CREDENTIALS").
    WithSeverity(auditocsf.SeverityHigh).
    WithExtension("source_ip", "***********").
    Build()
logger.LogAuthentication(failedEvent)
```

## References

- [OCSF Schema 1.5.0](https://schema.ocsf.io/1.5.0/)
- [OCSF Authentication Class](https://schema.ocsf.io/1.5.0/classes/authentication)
- [OCSF GitHub Repository](https://github.com/ocsf/ocsf-schema)
