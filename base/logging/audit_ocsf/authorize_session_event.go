package auditocsf

import (
	"fmt"
	"time"
)

// AuthorizeSessionEvent represents an OCSF Authorize Session event
type AuthorizeSessionEvent struct {
	BaseEvent
	// required
	User User `json:"user"`

	// Authorize Session specific fields (mutually exclusive)
	Group      *Group   `json:"group,omitempty"`      // Group assigned to the session
	Privileges []string `json:"privileges,omitempty"` // List of privileges assigned to the session
}

// SetDefaults sets default values for required fields
func (e *AuthorizeSessionEvent) SetDefaults() {
	e.CategoryUID = CategoryIAM
	e.ClassUID = ClassAuthorizeSession
	e.TypeUID = e.CalculateTypeUID()

	if e.Time.IsZero() {
		e.Time = time.Now().UTC()
	}

	if e.SeverityID == 0 {
		e.SeverityID = SeverityInformational
	}
}

// Validate checks if the event has all required fields
func (e *AuthorizeSessionEvent) Validate() error {
	// Validate common BaseEvent fields
	if err := e.BaseEvent.Validate(); err != nil {
		return err
	}

	// Validate AuthorizeSessionEvent-specific requirements
	// Validate that either group or privileges is set (mutually exclusive)
	if e.Group == nil && len(e.Privileges) == 0 {
		return fmt.Errorf("either group or privileges must be specified")
	}
	if e.Group != nil && len(e.Privileges) > 0 {
		return fmt.Errorf("group and privileges are mutually exclusive")
	}
	return nil
}

// AuthorizeSessionEventBuilder provides a fluent interface for building AuthorizeSessionEvent objects
type AuthorizeSessionEventBuilder struct {
	event AuthorizeSessionEvent
}

// NewAuthorizeSessionEventBuilder creates a new builder with default values
func NewAuthorizeSessionEventBuilder(activityID ActivityID) *AuthorizeSessionEventBuilder {
	return &AuthorizeSessionEventBuilder{
		event: AuthorizeSessionEvent{
			BaseEvent: BaseEvent{
				CategoryUID: CategoryIAM,
				ClassUID:    ClassAuthorizeSession,
				SeverityID:  SeverityInformational,
				Time:        time.Now().UTC(),
				ActivityID:  activityID,
				TypeUID:     int64(ClassAuthorizeSession)*100 + int64(activityID),
				Metadata: Metadata{
					Version: "1.5.0",
					Product: &Product{
						Name:   "Augment",
						Vendor: "Augment Code",
					},
					LogProvider: "augment-audit-ocsf",
				},
			},
		},
	}
}

// WithUser sets the user information
func (b *AuthorizeSessionEventBuilder) WithUser(user User) *AuthorizeSessionEventBuilder {
	b.event.User = user
	return b
}

// WithUserUID sets the user UID and type
func (b *AuthorizeSessionEventBuilder) WithUserUID(uid, userType string) *AuthorizeSessionEventBuilder {
	b.event.User.UID = uid
	b.event.User.Type = userType
	return b
}

// WithUserName sets the user name and type
func (b *AuthorizeSessionEventBuilder) WithUserName(name, userType string) *AuthorizeSessionEventBuilder {
	b.event.User.Name = name
	b.event.User.Type = userType
	return b
}

// WithSeverity sets the severity level
func (b *AuthorizeSessionEventBuilder) WithSeverity(severityID SeverityID) *AuthorizeSessionEventBuilder {
	b.event.SeverityID = severityID
	return b
}

// WithStatus sets the status information
func (b *AuthorizeSessionEventBuilder) WithStatus(statusID StatusID) *AuthorizeSessionEventBuilder {
	b.event.StatusID = &statusID
	return b
}

// WithStatusDetail sets the status detail information
func (b *AuthorizeSessionEventBuilder) WithStatusDetail(statusCode, statusDetail string) *AuthorizeSessionEventBuilder {
	if statusCode != "" {
		b.event.StatusCode = statusCode
	}
	if statusDetail != "" {
		b.event.StatusDetail = statusDetail
	}
	return b
}

// WithAction sets the action taken
func (b *AuthorizeSessionEventBuilder) WithAction(actionID ActionID) *AuthorizeSessionEventBuilder {
	b.event.ActionID = &actionID
	return b
}

// WithMessage sets the event message
func (b *AuthorizeSessionEventBuilder) WithMessage(message string) *AuthorizeSessionEventBuilder {
	b.event.Message = message
	return b
}

// WithTime sets the event time
func (b *AuthorizeSessionEventBuilder) WithTime(eventTime time.Time) *AuthorizeSessionEventBuilder {
	b.event.Time = eventTime
	return b
}

// WithDevice sets the device information
func (b *AuthorizeSessionEventBuilder) WithDevice(device Device) *AuthorizeSessionEventBuilder {
	b.event.Device = &device
	return b
}

// WithSession sets the session information
func (b *AuthorizeSessionEventBuilder) WithSession(session Session) *AuthorizeSessionEventBuilder {
	b.event.Session = &session
	return b
}

// WithTimezoneOffset sets the timezone offset
func (b *AuthorizeSessionEventBuilder) WithTimezoneOffset(offset int) *AuthorizeSessionEventBuilder {
	b.event.TimezoneOffset = &offset
	return b
}

// WithRawData sets the raw event data
func (b *AuthorizeSessionEventBuilder) WithRawData(rawData string) *AuthorizeSessionEventBuilder {
	b.event.RawData = rawData
	return b
}

// WithGroup sets the group assigned to the session (mutually exclusive with privileges)
func (b *AuthorizeSessionEventBuilder) WithGroup(group Group) *AuthorizeSessionEventBuilder {
	b.event.Group = &group
	b.event.Privileges = nil // Clear privileges to maintain mutual exclusivity
	return b
}

// WithPrivileges sets the privileges assigned to the session (mutually exclusive with group)
func (b *AuthorizeSessionEventBuilder) WithPrivileges(privileges []string) *AuthorizeSessionEventBuilder {
	b.event.Privileges = privileges
	b.event.Group = nil // Clear group to maintain mutual exclusivity
	return b
}

// WithSrcEndpoint sets the source endpoint information (Recommended for AuthorizeSession)
func (b *AuthorizeSessionEventBuilder) WithSrcEndpoint(endpoint NetworkEndpoint) *AuthorizeSessionEventBuilder {
	b.event.SrcEndpoint = &endpoint
	return b
}

// WithDstEndpoint sets the destination endpoint information (Recommended for AuthorizeSession)
func (b *AuthorizeSessionEventBuilder) WithDstEndpoint(endpoint NetworkEndpoint) *AuthorizeSessionEventBuilder {
	b.event.DstEndpoint = &endpoint
	return b
}

// Build creates the final AuthorizeSessionEvent
func (b *AuthorizeSessionEventBuilder) Build() AuthorizeSessionEvent {
	// Ensure type_uid is calculated if not already set
	if b.event.TypeUID == 0 {
		b.event.TypeUID = int64(b.event.ClassUID)*100 + int64(b.event.ActivityID)
	}

	// Set default metadata if none provided
	if b.event.Metadata.Version == "" {
		b.event.Metadata = Metadata{
			Version: "1.5.0",
			Product: &Product{
				Name:   "Augment",
				Vendor: "Augment Code",
			},
			LogProvider: "augment-audit-ocsf",
		}
	}

	return b.event
}

// Convenience builder functions for common authorize session scenarios

// NewAssignPrivilegesEvent creates a builder for an assign privileges event
func NewAssignPrivilegesEvent(userID, userType string, privileges []string) *AuthorizeSessionEventBuilder {
	statusID := StatusSuccess
	actionID := ActionAllowed

	return NewAuthorizeSessionEventBuilder(ActivityAssignPrivileges).
		WithUserUID(userID, userType).
		WithPrivileges(privileges).
		WithStatus(statusID).
		WithAction(actionID).
		WithSeverity(SeverityInformational)
}

// NewAssignGroupsEvent creates a builder for an assign groups event
func NewAssignGroupsEvent(userID, userType string, group Group) *AuthorizeSessionEventBuilder {
	statusID := StatusSuccess
	actionID := ActionAllowed

	return NewAuthorizeSessionEventBuilder(ActivityAssignGroups).
		WithUserUID(userID, userType).
		WithGroup(group).
		WithStatus(statusID).
		WithAction(actionID).
		WithSeverity(SeverityInformational)
}
