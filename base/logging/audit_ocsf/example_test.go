package auditocsf_test

import (
	"time"

	auditocsf "github.com/augmentcode/augment/base/logging/audit_ocsf"
)

// discardPrinter implements Printer interface but discards output
type discardPrinter struct{}

func (d discardPrinter) Println(args ...any) {}

// Example demonstrates basic usage of the OCSF audit logger
func Example_basicUsage() {
	logger := auditocsf.NewDefaultOCSFAuditLogger()

	// Simple logon
	logger.LogUserLogon("user123", "INTERNAL_IAP", "tenant456", "User authenticated successfully")

	// Failed authentication
	logger.LogFailedAuthentication("user456", "EXTERNAL", "tenant456", "Authentication failed", "INVALID_CREDENTIALS")
}

// Example demonstrates the Builder pattern for creating authentication events
func Example_builderPattern() {
	logger := auditocsf.NewDefaultOCSFAuditLogger()

	// Using convenience builders
	successEvent := auditocsf.NewSuccessfulLogonEvent("user123", "INTERNAL_IAP").
		WithMessage("SAML SSO authentication").
		WithMFA(true).
		WithRemote(true).
		WithAuthProtocol(auditocsf.AuthProtocolSAML).
		WithLogonType(auditocsf.LogonTypeRemoteInteractive).
		Build()

	logger.LogAuthentication(successEvent)

	// Using the generic builder for complex scenarios
	complexEvent := auditocsf.NewAuthenticationEventBuilder(auditocsf.ActivityLogon).
		WithUserUID("admin123", "INTERNAL_IAP").
		WithSeverity(auditocsf.SeverityHigh).
		WithStatus(auditocsf.StatusSuccess).
		WithAction(auditocsf.ActionAllowed).
		WithMessage("High-privilege administrative logon").
		WithMFA(true).
		WithRemote(false).
		WithAuthProtocol(auditocsf.AuthProtocolKerberos).
		WithLogonType(auditocsf.LogonTypeInteractive).
		WithDevice(auditocsf.Device{
			Name:     "admin-workstation",
			Type:     "Computer",
			Hostname: "admin-ws.example.com",
			IP:       "*********",
			OS: &auditocsf.OS{
				Name:    "Windows",
				Type:    "Windows",
				Version: "10.0.19041",
			},
		}).
		WithSession(auditocsf.Session{
			UID:         "session-admin-123",
			UUID:        "550e8400-e29b-41d4-a716-************",
			Issuer:      "domain-controller",
			CreatedTime: time.Now().UTC(),
			IsRemote:    false,
			IsMFA:       true,
		}).
		Build()

	logger.LogAuthentication(complexEvent)
}

// Example demonstrates failed authentication scenarios
func Example_failedAuthentication() {
	logger := auditocsf.NewDefaultOCSFAuditLogger()

	// Basic failed logon
	failedEvent := auditocsf.NewFailedLogonEvent("user456", "EXTERNAL").
		WithMessage("Failed authentication attempt").
		WithStatusDetail("0x18", "INVALID_CREDENTIALS").
		WithSeverity(auditocsf.SeverityHigh).
		Build()

	logger.LogAuthentication(failedEvent)

	// Account locked scenario
	lockedEvent := auditocsf.NewFailedLogonEvent("user789", "INTERNAL_IAP").
		WithMessage("Authentication failed - account locked").
		WithStatusDetail("0x19", "ACCOUNT_LOCKED_OUT").
		WithSeverity(auditocsf.SeverityMedium).
		Build()

	logger.LogAuthentication(lockedEvent)

	// Suspicious activity
	suspiciousEvent := auditocsf.NewFailedLogonEvent("user999", "EXTERNAL").
		WithMessage("Suspicious authentication attempt detected").
		WithStatusDetail("0x20", "SUSPICIOUS_ACTIVITY").
		WithSeverity(auditocsf.SeverityCritical).
		WithAction(auditocsf.ActionDenied).
		Build()

	logger.LogAuthentication(suspiciousEvent)
}

// Example demonstrates multi-factor authentication events
func Example_multiFactorAuthentication() {
	logger := auditocsf.NewDefaultOCSFAuditLogger()

	// Successful MFA logon
	mfaEvent := auditocsf.NewSuccessfulLogonEvent("user123", "INTERNAL_IAP").
		WithMessage("Multi-factor authentication successful").
		WithMFA(true).
		WithRemote(true).
		WithAuthProtocol(auditocsf.AuthProtocolOAuth2).
		WithLogonType(auditocsf.LogonTypeRemoteInteractive).
		Build()

	logger.LogAuthentication(mfaEvent)

	// MFA challenge
	challengeEvent := auditocsf.NewAuthenticationEventBuilder(auditocsf.ActivityPreauth).
		WithUserUID("user123", "INTERNAL_IAP").
		WithMessage("MFA challenge initiated").
		WithStatus(auditocsf.StatusSuccess).
		WithAction(auditocsf.ActionObserved).
		WithMFA(true).
		Build()

	logger.LogAuthentication(challengeEvent)
}
