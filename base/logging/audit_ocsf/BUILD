load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")
load("//base:base.bzl", "BASE_VISIBILITY")
load("//tools/bzl:python.bzl", "py_binary", "py_library")
load("//tools/bzl/pytest:defs.bzl", "pytest_test")

# Go library for OCSF audit logging
go_library(
    name = "audit_ocsf_go",
    srcs = [
        "api_activity_event.go",
        "audit_ocsf.go",
        "authentication_event.go",
        "authorize_session_event.go",
        "constants.go",
        "test_utils.go",
    ],
    importpath = "github.com/augmentcode/augment/base/logging/audit_ocsf",
    visibility = BASE_VISIBILITY,
)

# Go tests for OCSF audit logging
go_test(
    name = "audit_ocsf_go_test",
    srcs = [
        "api_activity_event_test.go",
        "audit_ocsf_test.go",
        "authentication_event_test.go",
        "authorize_session_event_test.go",
        "builder_test.go",
        "constants_test.go",
        "example_api_activity_test.go",
        "example_authorize_session_test.go",
        "example_test.go",
    ],
    embed = [
        ":audit_ocsf_go",
    ],
    deps = [
        "@com_github_stretchr_testify//assert",
        "@com_github_stretchr_testify//require",
    ],
)

# Python library for OCSF audit logging
py_library(
    name = "audit_ocsf_py",
    srcs = [
        "python/__init__.py",
        "python/api_activity_event.py",
        "python/audit_logger.py",
        "python/authentication_event.py",
        "python/authorize_session_event.py",
        "python/base_event.py",
        "python/constants.py",
        "python/ocsf_models.py",
        "python/test_utils.py",
    ],
    visibility = BASE_VISIBILITY,
)

# Python examples for OCSF audit logging
py_binary(
    name = "pretty_examples_py",
    srcs = ["python/examples/pretty_examples.py"],
    main = "python/examples/pretty_examples.py",
    visibility = ["//visibility:private"],
    deps = [":audit_ocsf_py"],
)

# Python tests for OCSF audit logging
pytest_test(
    name = "audit_ocsf_test_py",
    srcs = ["python/audit_ocsf_test.py"],
    deps = [":audit_ocsf_py"],
)
