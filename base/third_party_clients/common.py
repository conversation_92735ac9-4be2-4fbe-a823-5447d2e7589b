from grpc import Rp<PERSON><PERSON><PERSON>r, StatusCode
from typing import Optional

from base.error_details.error_details_pb2 import ErrorDetails


class InternalRpcError(RpcError):
    def __init__(self, message: str):
        self.message = message

    def code(self):
        return StatusCode.INTERNAL

    def details(self):
        return f"internal: {self.message}"


class UnavailableRpcError(RpcError):
    def __init__(self, message: str):
        self.message = message

    def code(self):
        return StatusCode.UNAVAILABLE

    def details(self):
        return f"unavailable: {self.message}"


class ResourceExhaustedRpcError(RpcError):
    def __init__(
        self,
        message: str,
        retry_may_succeed: bool = True,
        retry_after_seconds: int | None = None,
    ):
        self.message = message
        self.retry_may_succeed = retry_may_succeed
        self.retry_after_seconds = retry_after_seconds
        pass

    def code(self):
        return StatusCode.RESOURCE_EXHAUSTED

    def details(self):
        return f"resource exhausted: {self.message}"


class InvalidArgumentRpcError(RpcError):
    """Exception for invalid arguments in the gRPC service."""

    def __init__(self, message: str, error_details: Optional[ErrorDetails] = None):
        self.message = message
        self.error_details = error_details

    def code(self):
        return StatusCode.INVALID_ARGUMENT

    def details(self):
        return f"invalid argument: {self.message}"
