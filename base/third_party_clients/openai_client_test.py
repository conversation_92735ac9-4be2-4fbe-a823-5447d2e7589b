"""Unit tests for the OpenAIClient."""

import pytest

import base.feature_flags
from openai import BadRequestError
from base.third_party_clients.openai_client import (
    should_return_partial_tool_use_block,
    _SAVE_FILE_PARTIAL_TOOL_USE,
    OpenAIClient,
)
from base.third_party_clients.common import (
    InvalidArgumentRpcError,
    InternalRpcError,
)
from base.third_party_clients.third_party_model_client import ToolUseResponse
from base.error_details.error_details_pb2 import ErrorCode


@pytest.fixture
def feature_flags():
    yield from base.feature_flags.feature_flag_fixture()


@pytest.mark.parametrize(
    "tool_use_block, expected",
    [
        (
            ToolUseResponse(
                tool_use_id="toolu_vrtx_01PA4JbnRWpgYuQGXSxbCp6H",
                tool_name="str-replace-editor",
                input={
                    "command": "str_replace",
                    "path": "example/file.py",
                    "old_str_1": "def old_function():",
                    "old_str_start_line_1": 10,
                    "old_str_end_line_1": 10,
                    "new_str_1": "def new_function():",
                },
            ),
            True,
        ),
        (
            ToolUseResponse(
                tool_use_id="toolu_vrtx_01PA4JbnRWpgYuQGXSxbCp6H",
                tool_name="str-replace-editor",
                input={
                    "command": "view",
                    "path": "example/file.py",
                },
            ),
            False,
        ),
        (
            ToolUseResponse(
                tool_use_id="toolu_vrtx_01PA4JbnRWpgYuQGXSxbCp6H",
                tool_name="save-file",
                input={
                    "path": "example/file.py",
                    "file_content_1": "def new_function():",
                },
            ),
            True,
        ),
        (
            ToolUseResponse(
                tool_use_id="toolu_vrtx_01PA4JbnRWpgYuQGXSxbCp6H",
                tool_name="save-file",
                input={
                    "path": "example/file.py",
                },
            ),
            True,
        ),
        (
            ToolUseResponse(
                tool_use_id="toolu_vrtx_01PA4JbnRWpgYuQGXSxbCp6H",
                tool_name="save-file",
                input={
                    "file_content": "def new_function():",
                },
            ),
            False,
        ),
    ],
)
def test_should_return_partial_tool_use_block(
    feature_flags, tool_use_block: ToolUseResponse, expected: bool
):
    """Test that the client correctly determines whether to return a partial tool use block."""
    feature_flags.set_flag(_SAVE_FILE_PARTIAL_TOOL_USE, True)
    assert should_return_partial_tool_use_block(tool_use_block) == expected


class FakeBadRequestError(BadRequestError):
    """Lightweight BadRequestError for testing that only carries body/message.

    We avoid calling the real base __init__ and ensure __str__ is safe.
    """

    def __init__(self, message: str, body):
        self.body = body
        self._message = message

    def __str__(self) -> str:  # pragma: no cover - simple override
        return self._message


def run_handle_error(*args, **kwargs):
    return OpenAIClient(
        api_key="sk-test-not-a-real-key",  # pragma: allowlist secret
        endpoint="openai",
        model_name="gpt-4o-2024-08-06",
        temperature=1,
        max_output_tokens=128,
    )._handle_openai_error(*args, **kwargs)


def test_handle_openai_error_bad_request_internal_maps_to_internal_error():
    err = FakeBadRequestError(
        message="Your organization must be verified to stream this model",
        body={
            "error": {
                "message": "Your organization must be verified to stream this model"
            }
        },
    )
    with pytest.raises(InternalRpcError):
        run_handle_error(err, model_caller="unit-test", is_streaming=True)


def test_handle_openai_error_bad_request_tool_result_without_use_returns_error_details():
    err = FakeBadRequestError(
        message="'tool_call_id' of abc not found in 'tool_calls' of previous message",
        body={
            "error": {
                "message": "'tool_call_id' of abc not found in 'tool_calls' of previous message"
            }
        },
    )
    with pytest.raises(InvalidArgumentRpcError) as ei:
        run_handle_error(err, model_caller="unit-test", is_streaming=False)
    assert ei.value.error_details is not None
    assert ei.value.error_details.code == ErrorCode.INVALID_TOOL_USE_HISTORY
    assert ei.value.error_details.message == "Invalid tool use history"
    assert (
        ei.value.error_details.detail
        == "Tool result block found without corresponding tool use block"
    )


def test_handle_openai_error_bad_request_tool_use_without_result_returns_error_details():
    err = FakeBadRequestError(
        message="'tool_calls' must be followed by tool messages responding to each 'tool_call_id'",
        body={
            "error": {
                "message": "'tool_calls' must be followed by tool messages responding to each 'tool_call_id'"
            }
        },
    )
    with pytest.raises(InvalidArgumentRpcError) as ei:
        run_handle_error(err, model_caller="unit-test", is_streaming=True)
    assert ei.value.error_details is not None
    assert ei.value.error_details.code == ErrorCode.INVALID_TOOL_USE_HISTORY
    assert ei.value.error_details.message == "Invalid tool use history"
    assert (
        ei.value.error_details.detail
        == "Tool use block found without corresponding tool result block"
    )


def test_handle_openai_error_bad_request_unknown_reason_has_no_error_details():
    err = FakeBadRequestError(
        message="Some other validation issue",
        body={"error": {"message": "Some other validation issue"}},
    )
    with pytest.raises(InvalidArgumentRpcError) as ei:
        run_handle_error(err, model_caller="unit-test")
    assert ei.value.error_details is None
