package buildinfo

import (
	"testing"
)

func TestBuildInfo(t *testing.T) {
	t.<PERSON>()
	keys, err := Info.KeysErr()
	if err != nil {
		t.<PERSON>("KeysErr: %v.", err)
	}
	for _, k := range keys {
		if v, err := Info.KeyErr(k); err != nil {
			t.<PERSON>rf("%s: %s [%v]", k, v, err)
		} else {
			t.Logf("%s: %s", k, v)
		}
	}
}

func TestBuildInfoIndividual(t *testing.T) {
	t.<PERSON>()

	if v, err := Info.BuildEmbedLabelErr(); err != nil {
		t.<PERSON>rf("BuildEmbedLabel: %v.", err)
	} else {
		t.Logf("BuildEmbedLabel: %s", v)
	}

	if v, err := Info.BuildHostErr(); err != nil {
		t.<PERSON>rf("BuildHost: %v.", err)
	} else {
		t.Logf("BuildHost: %s", v)
	}

	if v, err := Info.BuildTimestampErr(); err != nil {
		t.<PERSON><PERSON><PERSON>("BuildTimestamp: %v.", err)
	} else {
		t.Logf("BuildTimestamp: %s", v)
	}

	if v, err := Info.BuildUserErr(); err != nil {
		t.Errorf("BuildUser: %v.", err)
	} else {
		t.Logf("BuildUser: %s", v)
	}

	if v, err := Info.BuildUserNamespaceErr(); err != nil {
		t.Errorf("BuildUserNamespace: %v.", err)
	} else {
		t.Logf("BuildUserNamespace: %s", v)
	}

	if v, err := Info.BuildVersionErr(); err != nil {
		t.Errorf("BuildVersion: %v.", err)
	} else {
		t.Logf("BuildVersion: %s", v)
	}

	if v, err := Info.GitBranchErr(); err != nil {
		t.Errorf("GitBranch: %v.", err)
	} else {
		t.Logf("GitBranch: %s", v)
	}

	if v, err := Info.GitCommitErr(); err != nil {
		t.Errorf("GitCommit: %v.", err)
	} else {
		t.Logf("GitCommit: %s", v)
	}

	if v, err := Info.GitCommitShortErr(); err != nil {
		t.Errorf("GitCommitShort: %v.", err)
	} else {
		t.Logf("GitCommitShort: %s", v)
	}

	if v, err := Info.GitCommitTimestampErr(); err != nil {
		t.Errorf("GitCommitTimestamp: %v.", err)
	} else {
		t.Logf("GitCommitTimestamp: %s", v)
	}

	if v, err := Info.GitAuthorTimestampErr(); err != nil {
		t.Errorf("GitAuthorTimestamp: %v.", err)
	} else {
		t.Logf("GitAuthorTimestamp: %s", v)
	}

	if v, err := Info.GitDirtyErr(); err != nil {
		t.Errorf("GitDirty: %v.", err)
	} else {
		t.Logf("GitDirty: %v", v)
	}
}
