# buildinfo library

This package provides support for accessing build time stamp metadata from the
`workspace-status` command. Users do not need to worry about bazel's
`--workspace-status-command` or `--stamp` flags. Just include the appropriate
data dependency or language binding.

Currently, this includes:
 - Build Info: timestamp, username, hostname, etc.
 - Git Info: commit, branch, diry, timestamps, etc.
 - Version Info: passed in at build time.

## Usage

The `buildinfo_json` rule provides a `buildinfo.json` file which is simply a
reformatted version of `stable-status.txt` and `volatile-status.txt`. Include
`//tools/bzl/buildinfo:buildinfo_json` in your `data` or `srcs` dependencies.
There's no need to worry about passing the `bazel build --stamp` flag.

### Language Bindings

This package also provides higher level language bindings on top of the above
raw json. For example, `//tools/bzl/buildinfo:buildinfo_go` provides support
for returning native types such as `time.Time` for `BUILD_TIMESTAMP`.

Prefer the language bindings over the raw json when you can. Add support for other
languages as needed.

### `STABLE_BUILD_VERSION`

There is a special stamp value called `STABLE_BUILD_VERSION` which is set by
the corresponding environmental variable at build time. Example:

```
STABLE_BUILD_VERSION=1.2.3 bazel build //path/to/my:client
```

### External

The `bazelinfo_external_json` rule provides `buildinfo.external.json`. This
rule is where we track which values we're okay to release with external clients.

## Adding Values

To add new values, update the `workspace-status` script. This script is run
*outside* of bazel and so should not pull in a complicated set of dependencies;
stick to the standard library.

When adding a new value, please also take a look at updating the per-language
bindings, especially if the value is a type other than a string.
