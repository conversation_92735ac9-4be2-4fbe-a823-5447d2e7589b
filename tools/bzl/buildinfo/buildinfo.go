package buildinfo

import (
	"encoding/json"
	"fmt"
	"sort"
	"strconv"
	"time"

	"github.com/augmentcode/augment/infra/lib/logger"

	_ "embed"
)

//go:embed buildinfo.json
var raw []byte

// Info is a global, singleton BuildInfo.
var Info = NewBuildInfo(raw)

// BuildInfo provides access to stamped bazel data generated by the workspace-status-script. Most
// users should use the global [Info] var. Raw access by key names such as `BUILD_TIMESTAMP` (or keys
// that this library isn't aware of) is supported. However higher level access such as [BuildTimestamp]
// return native types such as [time.Time].
//
// All methods have a `FooErr` and `Foo` pair, with the latter logging errors and returning sane default
// or zero values for the key or type.
type BuildInfo struct {
	logp *logger.Logger
	data map[string]string
	err  error
}

// NewBuildInfo builds a new [BuildInfo]. Most users should the existing global [Info] var instead.
func NewBuildInfo(raw []byte) *BuildInfo {
	data := map[string]string{}
	bi := &BuildInfo{}
	if err := json.Unmarshal(raw, &data); err != nil {
		bi.err = err
	} else {
		bi.data = data
	}
	return bi
}

func (b *BuildInfo) log() *logger.Logger {
	if b == nil {
		return nil
	} else {
		return b.logp
	}
}

func (b *BuildInfo) DataErr() (map[string]string, error) {
	if b == nil {
		return nil, fmt.Errorf("nil buildinfo")
	} else if b.err != nil {
		return nil, b.err
	} else {
		return b.data, nil
	}
}

func (b *BuildInfo) Data() map[string]string {
	data, err := b.DataErr()
	if err != nil {
		b.log().Err(err).Msg("buildinfo data error")
	}
	return data
}

func (b *BuildInfo) KeysErr() ([]string, error) {
	if data, err := b.DataErr(); err != nil {
		return nil, err
	} else {
		keys := []string{}
		for k := range data {
			keys = append(keys, k)
		}
		sort.Strings(keys)
		return keys, nil
	}
}

func (b *BuildInfo) Keys() []string {
	keys, err := b.KeysErr()
	if err != nil {
		b.log().Err(err).Msg("buildinfo keys error")
	}
	return keys
}

func (b *BuildInfo) KeyErr(k string) (string, error) {
	if data, err := b.DataErr(); err != nil {
		return "", err
	} else if val, ok := data[k]; !ok {
		return "", fmt.Errorf("key not found: %s", k)
	} else {
		return val, nil
	}
}

func (b *BuildInfo) Key(k string) string {
	v, err := b.KeyErr(k)
	if err != nil {
		b.log().Err(err).Msg(k)
	}
	return v
}

////////////////////////////////////////////////////////////////////////////////
//
// Access to individually supported fields. Each field has a pair of
// `FooErr()` and `Foo()` methods. Any field not supported here can be added, or
// use the [KeyErr] and [Key] methods above.
//

// BuildEmbedLabelErr returns the `BUILD_EMBED_LABEL` key.
func (b *BuildInfo) BuildEmbedLabelErr() (string, error) {
	return b.KeyErr("BUILD_EMBED_LABEL")
}

// BuildEmbedLabel returns the `BUILD_EMBED_LABEL` key.
func (b *BuildInfo) BuildEmbedLabel() string {
	v, err := b.BuildEmbedLabelErr()
	if err != nil {
		b.log().Err(err).Msg("BuildEmbedLabel")
	}
	return v
}

// BuildHostErr returns the `BUILD_HOST` key.
func (b *BuildInfo) BuildHostErr() (string, error) {
	return b.KeyErr("BUILD_HOST")
}

// BuildHost returns the `BUILD_HOST` key.
func (b *BuildInfo) BuildHost() string {
	v, err := b.BuildHostErr()
	if err != nil {
		b.log().Err(err).Msg("BuildHost")
	}
	return v
}

// BuildTimestampErr returns the `BUILD_TIMESTAMP` key.
func (b *BuildInfo) BuildTimestampErr() (time.Time, error) {
	if s, err := b.KeyErr("BUILD_TIMESTAMP"); err != nil {
		return time.Time{}, err
	} else if i, err := strconv.ParseInt(s, 10, 64); err != nil {
		return time.Time{}, err
	} else {
		return time.Unix(i, 0), nil
	}
}

// BuildTimestamp returns the `BUILD_TIMESTAMP` key.
func (b *BuildInfo) BuildTimestamp() time.Time {
	t, err := b.BuildTimestampErr()
	if err != nil {
		b.log().Err(err).Msg("BuildTimestamp")
	}
	return t
}

// BuildUserErr returns the `BUILD_USER` key.
func (b *BuildInfo) BuildUserErr() (string, error) {
	return b.KeyErr("BUILD_USER")
}

// BuildUser returns the `BUILD_USER` key.
func (b *BuildInfo) BuildUser() string {
	v, err := b.BuildUserErr()
	if err != nil {
		b.log().Err(err).Msg("BuildUser")
	}
	return v
}

// BuildUserNamespaceErr returns the `BUILD_USER_NAMESPACE` key.
func (b *BuildInfo) BuildUserNamespaceErr() (string, error) {
	return b.KeyErr("BUILD_USER_NAMESPACE")
}

// BuildUserNamespace returns the `BUILD_USER_NAMESPACE` key.
func (b *BuildInfo) BuildUserNamespace() string {
	v, err := b.BuildUserNamespaceErr()
	if err != nil {
		b.log().Err(err).Msg("BuildUserNamespace")
	}
	return v
}

// BuildVersionErr returns the `STABLE_BUILD_VERSION` key.
func (b *BuildInfo) BuildVersionErr() (string, error) {
	return b.KeyErr("STABLE_BUILD_VERSION")
}

// BuildVersion returns the `STABLE_BUILD_VERSION` key.
func (b *BuildInfo) BuildVersion() string {
	v, err := b.BuildVersionErr()
	if err != nil {
		b.log().Err(err).Msg("BuildVersion")
	}
	return v
}

// GitBranchErr returns the `STABLE_GIT_BRANCH` key.
func (b *BuildInfo) GitBranchErr() (string, error) {
	return b.KeyErr("STABLE_GIT_BRANCH")
}

// GitBranch returns the `STABLE_GIT_BRANCH` key.
func (b *BuildInfo) GitBranch() string {
	v, err := b.GitBranchErr()
	if err != nil {
		b.log().Err(err).Msg("GitBranch")
	}
	return v
}

// GitCommitErr returns the `STABLE_GIT_COMMIT` key.
func (b *BuildInfo) GitCommitErr() (string, error) {
	return b.KeyErr("STABLE_GIT_COMMIT")
}

// GitCommit returns the `STABLE_GIT_COMMIT` key.
func (b *BuildInfo) GitCommit() string {
	v, err := b.GitCommitErr()
	if err != nil {
		b.log().Err(err).Msg("GitCommit")
	}
	return v
}

// GitCommitShortErr returns the `STABLE_GIT_COMMIT_SHORT` key.
func (b *BuildInfo) GitCommitShortErr() (string, error) {
	return b.KeyErr("STABLE_GIT_COMMIT_SHORT")
}

// GitCommitShort returns the `STABLE_GIT_COMMIT_SHORT` key.
func (b *BuildInfo) GitCommitShort() string {
	v, err := b.GitCommitShortErr()
	if err != nil {
		b.log().Err(err).Msg("GitCommitShort")
	}
	return v
}

// GitCommitTimestampErr returns the `STABLE_GIT_COMMIT_TIMESTAMP` key.
func (b *BuildInfo) GitCommitTimestampErr() (time.Time, error) {
	if s, err := b.KeyErr("STABLE_GIT_COMMIT_TIMESTAMP"); err != nil {
		return time.Time{}, err
	} else if i, err := strconv.ParseInt(s, 10, 64); err != nil {
		return time.Time{}, err
	} else {
		return time.Unix(i, 0), nil
	}
}

// GitCommitTimestamp returns the `STABLE_GIT_COMMIT_TIMESTAMP` key.
func (b *BuildInfo) GitCommitTimestamp() time.Time {
	t, err := b.GitCommitTimestampErr()
	if err != nil {
		b.log().Err(err).Msg("GitCommitTimestamp")
	}
	return t
}

// GitAuthorTimestampErr returns the `STABLE_GIT_AUTHOR_TIMESTAMP` key.
func (b *BuildInfo) GitAuthorTimestampErr() (time.Time, error) {
	if s, err := b.KeyErr("STABLE_GIT_AUTHOR_TIMESTAMP"); err != nil {
		return time.Time{}, err
	} else if i, err := strconv.ParseInt(s, 10, 64); err != nil {
		return time.Time{}, err
	} else {
		return time.Unix(i, 0), nil
	}
}

// GitAuthorTimestamp returns the `STABLE_GIT_AUTHOR_TIMESTAMP` key.
func (b *BuildInfo) GitAuthorTimestamp() time.Time {
	t, err := b.GitAuthorTimestampErr()
	if err != nil {
		b.log().Err(err).Msg("GitAuthorTimestamp")
	}
	return t
}

// GitDirtyErr returns the `STABLE_GIT_DIRTY` key.
func (b *BuildInfo) GitDirtyErr() (bool, error) {
	if s, err := b.KeyErr("STABLE_GIT_DIRTY"); err != nil {
		return false, err
	} else if b, err := strconv.ParseBool(s); err != nil {
		return false, err
	} else {
		return b, nil
	}
}

// GitDirty returns the `STABLE_GIT_DIRTY` key.
func (b *BuildInfo) GitDirty() bool {
	v, err := b.GitDirtyErr()
	if err != nil {
		b.log().Err(err).Msg("GitDirty")
	}
	return v
}
