load("//tools/bzl:go.bzl", "go_library", "go_test")

package(default_visibility = ["//visibility:private"])

################################################################################
#
# SoT: workspace-status > *-status.txt > buildinfo.json
#
# This converts the {stable,volatile}-status.txt files into JSON, which in turn
# is used as the SoT for all of the other language bindings.
#
# NOTE(mattm): We can add one more layer here (in go) if we want to add some dynamic
# info and do: buildinfo.raw.json -> go layer -> buildinfo.json to then use as
# the SoT for the other language bindings.
#

genrule(
    name = "buildinfo_json",
    outs = ["buildinfo.json"],
    cmd_bash = """
    set -euo pipefail
    declare -ra cmd=(
        $(JQ_BIN) -n
        --rawfile stable   "bazel-out/stable-status.txt"
        --rawfile volatile "bazel-out/volatile-status.txt"
        '
            ($$stable + $$volatile)
            | split("\\n")
            | map(select(length > 0))
            | map(split(" "))
            | map({key: .[0], value: (.[1:] | join(" "))})
            | from_entries
        '
    )
    "$${cmd[@]}" > "$@"
    """,
    stamp = 1,
    toolchains = ["@jq_toolchains//:resolved_toolchain"],
    visibility = ["//visibility:public"],
)

# A stripped down version of buildinfo.json with explicit opt-in of values that we can expose to
# external users. Any item not included here isn't necessary unsafe to share; the list is kept
# minimal based on need.
genrule(
    name = "buildinfo_external_json",
    srcs = [":buildinfo_json"],
    outs = ["buildinfo.external.json"],
    cmd_bash = """
    set -euo pipefail
    declare -ra keys=(
        STABLE_BUILD_VERSION
        STABLE_GIT_COMMIT_SHORT
        STABLE_GIT_DIRTY
    )
    declare -ra cmd=(
        $(JQ_BIN)
        'with_entries(select(.key | IN($$ARGS.positional[])))'
        --args
        "$${keys[@]}"
    )
    < "$<" "$${cmd[@]}" > "$@"
    """,
    toolchains = ["@jq_toolchains//:resolved_toolchain"],
    visibility = ["//visibility:public"],
)

################################################################################
#
# Language Bindings
#

go_library(
    name = "buildinfo_go",
    srcs = ["buildinfo.go"],
    embedsrcs = [":buildinfo_json"],
    importpath = "github.com/augmentcode/augment/tools/bzl/buildinfo",
    visibility = ["//visibility:public"],
    deps = [
        "//infra/lib/logger",
    ],
)

go_test(
    name = "buildinfo_go_test",
    srcs = ["buildinfo_test.go"],
    embed = [":buildinfo_go"],
    deps = [
        "@com_github_google_go_cmp//cmp",
    ],
)
