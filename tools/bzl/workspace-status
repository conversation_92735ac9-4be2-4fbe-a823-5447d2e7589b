#!/usr/bin/env python3
"""A small tool to generate workspace status information.

In addition, it also performs some environment checks
to ensure that <PERSON><PERSON> is only called in valid setups.
"""

import json
import os
import pathlib
import subprocess


def find_build_user():
    path = pathlib.Path.home().joinpath(".augment", "user.json")
    if path.exists():
        user_data = json.loads(path.read_text(encoding="utf-8"))
        user_name = user_data["name"]
        print(f"BUILD_USER {user_name}")
        print(f"BUILD_USER_NAMESPACE dev-{user_name}")
        return
    if "BAZEL_BUILD_USER" in os.environ:
        user_name = os.environ.get("BAZEL_BUILD_USER")
        print(f"BUILD_USER {user_name}")
    else:
        user_name = os.environ.get("USER", "none")
        print(f"BUILD_USER {user_name}")

    if "BUILD_USER_NAMESPACE" in os.environ:
        namespace = os.environ.get("BUILD_USER_NAMESPACE")
        print(f"BUILD_USER_NAMESPACE {namespace}")
    else:
        print("BUILD_USER_NAMESPACE none")


def generic_build_version():
    """A generic stamp value that passes an env var as a bazel stamp.

    As long as we're only building one releasable artifact at a time, or multiple as the same
    version, we only need one generic stamp value.
    """
    ver = os.environ.get("STABLE_BUILD_VERSION", "0.0.0")
    print(f"STABLE_BUILD_VERSION {ver}")


def vscode_release_version():
    print(
        f"STABLE_VSCODE_RELEASE_VERSION {os.environ.get('STABLE_VSCODE_RELEASE_VERSION', '0.0.0')}"
    )


def run_git(cmd: list[str]) -> tuple[int, str]:
    cmd = ["/usr/bin/git"] + cmd
    try:
        res = subprocess.run(
            cmd, shell=False, check=True, capture_output=True, text=True
        )
        return res.returncode, res.stdout.strip()
    except subprocess.CalledProcessError as e:
        return e.returncode, "unknown"
    except FileNotFoundError:
        return -1, "unknown"


def git_info():
    _, git_commit = run_git(["rev-parse", "HEAD"])
    _, git_commit_short = run_git(["rev-parse", "--short", "HEAD"])
    _, git_commit_ts = run_git(["show", "-s", "--format=format:%ct", "HEAD"])
    _, git_author_ts = run_git(["show", "-s", "--format=format:%at", "HEAD"])
    _, git_branch = run_git(["rev-parse", "--abbrev-ref", "HEAD"])
    git_dirty_rc, git_dirty_out = run_git(["diff-index", "--quiet", "HEAD", "--"])
    git_dirty = str(bool(git_dirty_rc)).lower() if git_dirty_rc >= 0 else git_dirty_out

    print(f"STABLE_GIT_COMMIT {git_commit}")
    print(f"STABLE_GIT_COMMIT_SHORT {git_commit_short}")
    print(f"STABLE_GIT_COMMIT_TIMESTAMP {git_commit_ts}")
    print(f"STABLE_GIT_AUTHOR_TIMESTAMP {git_author_ts}")
    print(f"STABLE_GIT_BRANCH {git_branch}")
    print(f"STABLE_GIT_DIRTY {git_dirty}")


def main():
    find_build_user()
    generic_build_version()
    vscode_release_version()
    git_info()


if __name__ == "__main__":
    main()
