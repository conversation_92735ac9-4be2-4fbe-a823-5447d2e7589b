load("@io_bazel_rules_go//proto:compiler.bzl", "go_proto_compiler")

## bazel query --output label '@com_connectrpc_connect//...'
## @com_connectrpc_connect//cmd/protoc-gen-connect-go:protoc-gen-connect-go
go_proto_compiler(
    name = "connect_go_proto_compiler",
    plugin = "@com_connectrpc_connect//cmd/protoc-gen-connect-go:protoc-gen-connect-go",
    suffix = ".connect.go",
    visibility = ["//visibility:public"],
    deps = ["@com_connectrpc_connect//:go_default_library"],
)

constraint_setting(name = "ubuntu_version")

constraint_value(
    name = "2004",
    constraint_setting = ":ubuntu_version",
)

constraint_value(
    name = "2204",
    constraint_setting = ":ubuntu_version",
)

# Denotes the cloud vendor used by Augment
constraint_setting(name = "au_cloud")

constraint_value(
    name = "au_cloud_cw",
    constraint_setting = ":au_cloud",
    visibility = ["//visibility:public"],
)

constraint_value(
    name = "au_cloud_gcp",
    constraint_setting = ":au_cloud",
    visibility = ["//visibility:public"],
)

# CoreWeave
platform(
    name = "cw_ubuntu2004_linux_x86",
    constraint_values = [
        "@platforms//os:linux",
        "@platforms//cpu:x86_64",
        ":2004",
        ":au_cloud_cw",
    ],
    exec_properties = {
        "distro": "focal",
    },
)

platform(
    name = "cw_ubuntu2204_linux_x86",
    constraint_values = [
        "@platforms//os:linux",
        "@platforms//cpu:x86_64",
        ":2204",
        ":au_cloud_cw",
    ],
    exec_properties = {
        "distro": "jammy",
    },
)

# GCP
platform(
    name = "gcp_ubuntu2004_linux_x86_64",
    constraint_values = [
        "@platforms//os:linux",
        "@platforms//cpu:x86_64",
        ":2004",
        ":au_cloud_gcp",
    ],
    exec_properties = {
        "distro": "focal",
    },
)

platform(
    name = "gcp_ubuntu2204_linux_x86_64",
    constraint_values = [
        "@platforms//os:linux",
        "@platforms//cpu:x86_64",
        ":2204",
        ":au_cloud_gcp",
    ],
    exec_properties = {
        "distro": "jammy",
    },
)

platform(
    name = "darwin_aarch64",
    constraint_values = [
        "@platforms//os:osx",
        "@platforms//cpu:aarch64",
    ],
)
