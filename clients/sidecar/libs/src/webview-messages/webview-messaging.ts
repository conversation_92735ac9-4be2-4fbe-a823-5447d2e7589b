import { AgentWebviewMessageHandler } from "../agent/agent-webview-messages";
import { AggregateCheckpointManager } from "../agent/checkpoint/aggregate-checkpoint-manager";
import { TaskWebviewMessageHandler } from "../agent/task/task-webview-messages";
import { TaskManager } from "../agent/task/task-manager";
import { ToolsWebviewMessageHandler } from "../tools/tool-webview-messages";
import { ToolsModel } from "../tools/tools-model";
import { ExchangeWebviewMessageHandler } from "../exchange-storage/exchange-webview-messages";
import { ExchangeManager } from "../exchange-storage/exchange-manager";
import { ToolUseStateWebviewMessageHandler } from "../tooluse-storage/tooluse-webview-messages";
import { ToolUseStateManager } from "../tooluse-storage/tooluse-manager";
import { RulesWebviewMessageHandler } from "../chat/rules-webview-msg-handler";
import { RulesService } from "../chat/rules-service";
import { MemoryWebviewMessageHandler } from "../agent/memory/memory-webview-messages";
import {
  CommonWebViewMessageType,
  WebViewMessage,
} from "./common-webview-messages";
import { AgentWebViewMessageType } from "./message-types/agent-messages";
import { TaskWebViewMessageType } from "./message-types/task-messages";
import { ToolsWebViewMessageType } from "./message-types/tool-messages";
import { ExchangeWebViewMessageType } from "./message-types/exchange-messages";
import { RulesWebViewMessageType } from "./message-types/rules-messages";
import { ToolUseStateWebViewMessageType } from "./message-types/tooluse-messages";
import { GitMessageType } from "../git/git-messages";
import { MemoryWebViewMessageType } from "./message-types/memory-messages";
import {
  IWebviewMessageConsumer,
  PostMessageFn,
  WebviewMessageBroker,
} from "./webview-messages-broker";
import { GitMessageHandler } from "../git/git-message-handler";
import { GitOperationsService } from "../git/git-operations-service";

export type MessageConsumerTypes =
  | CommonWebViewMessageType
  | ToolsWebViewMessageType
  | AgentWebViewMessageType
  | TaskWebViewMessageType
  | ExchangeWebViewMessageType
  | RulesWebViewMessageType
  | ToolUseStateWebViewMessageType
  | MemoryWebViewMessageType
  | GitMessageType;

export class WebviewMessaging {
  private _broker: WebviewMessageBroker<MessageConsumerTypes>;

  constructor(
    // Note: This isn't particularly clean, ideally the client logic added
    // to AggregateCheckpointManager would be moved global singleton like the
    // other client interfaces and then the sidecar can be responsible for
    // creating the AggregateCheckpointManager.
    // However, the sidecar needs this to setup one of the webview message
    // consumers this seems like an "ok" place for this dependency.
    aggregateCheckpointManager: AggregateCheckpointManager,
    toolsModel: ToolsModel,
    taskManager: TaskManager,
    exchangeManager: ExchangeManager,
    toolUseStateManager: ToolUseStateManager,
    rulesService: RulesService,
    gitOperationsService: GitOperationsService,
  ) {
    this._broker = new WebviewMessageBroker();
    const agentWebviewMessageHandler = new AgentWebviewMessageHandler(
      aggregateCheckpointManager,
      toolsModel,
    );

    const webviewMsgConsumers = [
      agentWebviewMessageHandler,
      new ToolsWebviewMessageHandler(toolsModel),
      new TaskWebviewMessageHandler(taskManager),
      new ExchangeWebviewMessageHandler(exchangeManager),
      new ToolUseStateWebviewMessageHandler(toolUseStateManager),
      new RulesWebviewMessageHandler(rulesService),
      new GitMessageHandler(gitOperationsService),
      new MemoryWebviewMessageHandler(toolsModel),
    ];
    for (const webviewMsgConsumer of webviewMsgConsumers) {
      this._broker.registerHandler(
        webviewMsgConsumer as IWebviewMessageConsumer<MessageConsumerTypes>,
      );
    }
  }

  onMessage(
    msg: WebViewMessage<MessageConsumerTypes>,
    postMessage: PostMessageFn<MessageConsumerTypes>,
  ): boolean {
    return this._broker.handle(msg, postMessage);
  }
}
