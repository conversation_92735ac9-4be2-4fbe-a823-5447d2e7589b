import { WebViewMessage } from "../common-webview-messages";

export const MEMORY_EXPERIMENTS = {
  default: {
    isClassifyAndDistill: true,
    promptKey: "classify_and_distill_prompt",
  },
  v1_success: {
    isClassifyAndDistill: true,
    // TODO: Add programmatic success detection for:
    // - Commits in PR (Best)
    // - Commits in-flight (Good)
    // - Accept All actions (Good)
    // - User gratitude expressions (Okay)
    // For now, rely on prompt-based success detection
    promptKey: "classify_and_distill_success_prompt",
  },
  v2_scope: {
    isClassifyAndDistill: true,
    // Adds explicit scope information to memories inline in the content
    promptKey: "classify_and_distill_scope_prompt",
  },
} as const;
export type MemoryExperiment = keyof typeof MEMORY_EXPERIMENTS;

export enum MemoryWebViewMessageType {
  memoryCreated = "memory-created",
  memoryCreatedResponse = "memory-created-response",
  memoryProcessed = "memory-processed",
  getMemoriesByState = "get-memories-by-state",
  getMemoriesByStateResponse = "get-memories-by-state-response",
  updateMemoryState = "update-memory-state",
  updateMemoryStateResponse = "update-memory-state-response",
  flushPendingMemories = "flush-pending-memories",
  flushPendingMemoriesResponse = "flush-pending-memories-response",
}

/**
 * Standardized schema for memory entries as required by the memory listener system
 */
export interface MemoryEntry {
  id: string;
  content: string;
  version: string;
}

/**
 * Additional metadata for memory creation events
 */
export interface MemoryCreationMetadata {
  conversationId: string;
  requestId: string;
  memoriesRequestId: string;
  timestamp: number;
}

/**
 * Message sent from webview when a new memory is created
 */
export type MemoryCreatedMessage =
  WebViewMessage<MemoryWebViewMessageType.memoryCreated> & {
    data: {
      memory: MemoryEntry;
      metadata: MemoryCreationMetadata;
    };
  };

/**
 * Response message confirming memory creation was processed
 */
export type MemoryCreatedResponse =
  WebViewMessage<MemoryWebViewMessageType.memoryCreatedResponse> & {
    data: {
      success: boolean;
      error?: string;
      memoryId: string;
    };
  };

/**
 * Message sent when memory has been successfully processed and uploaded as blob
 */
export type MemoryProcessedMessage =
  WebViewMessage<MemoryWebViewMessageType.memoryProcessed> & {
    data: {
      memoryId: string;
      blobName?: string;
      checkpointId?: string;
    };
  };

/**
 * Memory state types
 */
export const MEMORY_STATES = [
  "pending",
  "user_accepted",
  "repo_accepted",
  "user_rejected",
] as const;
export type MemoryState = (typeof MEMORY_STATES)[number];

/**
 * Extended memory info with state
 */
export interface MemoryInfoWithState extends MemoryEntry {
  timestamp: number;
  blobName?: string;
  state: MemoryState;
  scope?: string;
  requestId?: string;
}

/**
 * Request to get memories filtered by state and optionally by version
 */
export type GetMemoriesByStateMessage =
  WebViewMessage<MemoryWebViewMessageType.getMemoriesByState> & {
    data: {
      state: MemoryState;
      version?: string;
    };
  };

/**
 * Response with memories filtered by state
 */
export type GetMemoriesByStateResponse =
  WebViewMessage<MemoryWebViewMessageType.getMemoriesByStateResponse> & {
    data: {
      memories: MemoryInfoWithState[];
    };
  };

/**
 * Request to update a memory's state
 */
export type UpdateMemoryStateMessage =
  WebViewMessage<MemoryWebViewMessageType.updateMemoryState> & {
    data: {
      memoryId: string;
      newState: MemoryState;
      editedContent?: string; // Optional, only used when state is "user_edited"
    };
  };

/**
 * Response confirming memory state update
 */
export type UpdateMemoryStateResponse =
  WebViewMessage<MemoryWebViewMessageType.updateMemoryStateResponse> & {
    data: {
      success: boolean;
      error?: string;
      memoryId: string;
      newState: MemoryState;
    };
  };

/**
 * Request to flush all pending memories to permanent storage
 */
export type FlushPendingMemoriesMessage =
  WebViewMessage<MemoryWebViewMessageType.flushPendingMemories> & {
    data: Record<string, never>; // No data needed for flush operation
  };

/**
 * Response confirming pending memories flush
 */
export type FlushPendingMemoriesResponse =
  WebViewMessage<MemoryWebViewMessageType.flushPendingMemoriesResponse> & {
    data: {
      success: boolean;
      error?: string;
      flushedCount: number;
    };
  };

/**
 * Union type for all memory-related webview messages
 */
export type MemoryWebViewMessage =
  | MemoryCreatedMessage
  | MemoryCreatedResponse
  | MemoryProcessedMessage
  | GetMemoriesByStateMessage
  | GetMemoriesByStateResponse
  | UpdateMemoryStateMessage
  | UpdateMemoryStateResponse
  | FlushPendingMemoriesMessage
  | FlushPendingMemoriesResponse;
