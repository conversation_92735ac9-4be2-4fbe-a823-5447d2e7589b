import { type MemoryEntry } from "@augment-internal/sidecar-libs/src/webview-messages/message-types/memory-messages";
import {
  extractScopeFromContent,
  removeScopeFromContent,
  parseMemoriesFromContent,
  formatMemoriesToContent,
} from "../memory-parser";

describe("memory-parser", () => {
  describe("extractScopeFromContent", () => {
    it("should extract scope from content with scope prefix", () => {
      const content =
        "[SCOPE: codebase_items; task_items; category_items] This is the actual memory content";
      const scope = extractScopeFromContent(content);
      expect(scope).toBe("codebase_items; task_items; category_items");
    });

    it("should return undefined for content without scope", () => {
      const content = "This is just regular memory content";
      const scope = extractScopeFromContent(content);
      expect(scope).toBeUndefined();
    });

    it("should handle scope with extra whitespace", () => {
      const content = "[SCOPE:   codebase_items; task_items   ] Memory content";
      const scope = extractScopeFromContent(content);
      expect(scope).toBe("codebase_items; task_items");
    });
  });

  describe("removeScopeFromContent", () => {
    it("should remove scope prefix from content", () => {
      const content =
        "[SCOPE: codebase_items; task_items] This is the actual memory content";
      const cleanContent = removeScopeFromContent(content);
      expect(cleanContent).toBe("This is the actual memory content");
    });

    it("should return original content if no scope prefix", () => {
      const content = "This is just regular memory content";
      const cleanContent = removeScopeFromContent(content);
      expect(cleanContent).toBe("This is just regular memory content");
    });

    it("should handle content with only scope", () => {
      const content = "[SCOPE: codebase_items]";
      const cleanContent = removeScopeFromContent(content);
      expect(cleanContent).toBe("");
    });
  });

  describe("parseMemoriesFromContent", () => {
    it("should parse simple memory list", () => {
      const content = `# Comments are ignored
- First memory
- Second memory
- Third memory`;

      const memories = parseMemoriesFromContent(content);
      expect(memories).toHaveLength(3);
      expect(memories[0]).toEqual({
        id: "0",
        version: "DEFAULT",
        content: "First memory",
      });
      expect(memories[1]).toEqual({
        id: "1",
        version: "DEFAULT",
        content: "Second memory",
      });
      expect(memories[2]).toEqual({
        id: "2",
        version: "DEFAULT",
        content: "Third memory",
      });
    });

    it("should handle multi-line memories", () => {
      const content = `- First memory
  that spans multiple lines
- Second memory
  also spans
  multiple lines`;

      const memories = parseMemoriesFromContent(content);
      expect(memories).toHaveLength(2);
      expect(memories[0].content).toBe(
        "First memory that spans multiple lines",
      );
      expect(memories[1].content).toBe(
        "Second memory also spans multiple lines",
      );
    });

    it("should skip empty lines and comments", () => {
      const content = `# This is a comment

- First memory

# Another comment
- Second memory

`;

      const memories = parseMemoriesFromContent(content);
      expect(memories).toHaveLength(2);
      expect(memories[0].content).toBe("First memory");
      expect(memories[1].content).toBe("Second memory");
    });

    it("should use custom version", () => {
      const content = "- Test memory";
      const memories = parseMemoriesFromContent(content, "v2.0");
      expect(memories[0].version).toBe("v2.0");
    });

    it("should handle empty content", () => {
      const memories = parseMemoriesFromContent("");
      expect(memories).toHaveLength(0);
    });
  });

  describe("formatMemoriesToContent", () => {
    it("should format single memory to content", () => {
      const memories = [
        { id: "1", content: "Test memory", version: "DEFAULT" },
      ];
      const content = formatMemoriesToContent(memories);
      expect(content).toBe("- Test memory");
    });

    it("should format multiple memories to content", () => {
      const memories = [
        { id: "1", content: "First memory", version: "DEFAULT" },
        { id: "2", content: "Second memory", version: "DEFAULT" },
      ];
      const content = formatMemoriesToContent(memories);
      expect(content).toBe("- First memory\n- Second memory");
    });

    it("should handle empty array", () => {
      const memories: MemoryEntry[] = [];
      const content = formatMemoriesToContent(memories);
      expect(content).toBe("");
    });

    it("should handle memories with scope", () => {
      const memories = [
        { id: "1", content: "[SCOPE: test] Test memory", version: "DEFAULT" },
      ];
      const content = formatMemoriesToContent(memories);
      expect(content).toBe("- [SCOPE: test] Test memory");
    });
  });
});
