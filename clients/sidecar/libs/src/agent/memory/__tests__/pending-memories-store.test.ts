import { PendingMemoriesStore } from "../pending-memories-store";
import type { IPluginFileStore } from "../../../client-interfaces/plugin-file-store";
import { MemoryEntry } from "../../../webview-messages/message-types/memory-messages";

// Mock IPluginFileStore
class MockPluginFileStore implements IPluginFileStore {
  private _assets = new Map<string, Uint8Array>();

  saveAsset(path: string, content: Uint8Array): Promise<void> {
    this._assets.set(path, content);
    return Promise.resolve();
  }

  loadAsset(path: string): Promise<Uint8Array | undefined> {
    return Promise.resolve(this._assets.get(path));
  }

  deleteAsset(path: string): Promise<void> {
    this._assets.delete(path);
    return Promise.resolve();
  }

  getAssetPath(path: string): Promise<string> {
    if (!this._assets.has(path)) {
      return Promise.reject(new Error(`Asset not found: ${path}`));
    }
    return Promise.resolve(`/mock/path/${path}`);
  }

  listAssets(prefix: string): Promise<string[]> {
    return Promise.resolve(
      Array.from(this._assets.keys()).filter((key) => key.startsWith(prefix)),
    );
  }

  // Helper method for testing
  getAssetAsString(path: string): string | undefined {
    const content = this._assets.get(path);
    return content ? new TextDecoder().decode(content) : undefined;
  }
}

describe("PendingMemoriesStore", () => {
  let mockFileStore: MockPluginFileStore;
  let pendingStore: PendingMemoriesStore;
  const testPath = "test-pending.md";

  beforeEach(() => {
    mockFileStore = new MockPluginFileStore();
    pendingStore = new PendingMemoriesStore(mockFileStore, testPath);
  });

  describe("append", () => {
    it("should append a memory to empty store", async () => {
      const memory: MemoryEntry = {
        id: "test-1",
        content: "Test memory content",
        version: "v1",
      };

      await pendingStore.append(memory, "request-123");

      const content = mockFileStore.getAssetAsString(testPath);
      expect(content).toContain('"requestId": "request-123"');
      expect(content).toContain('"id": "test-1"');
      expect(content).toContain('"version": "v1"');
      expect(content).toContain('"content": "Test memory content"');
    });

    it("should append multiple memories", async () => {
      const memory1: MemoryEntry = {
        id: "test-1",
        content: "First memory",
        version: "v1",
      };
      const memory2: MemoryEntry = {
        id: "test-2",
        content: "Second memory",
        version: "v1",
      };

      await pendingStore.append(memory1, "request-1");
      await pendingStore.append(memory2, "request-2");

      const content = mockFileStore.getAssetAsString(testPath);
      expect(content).toContain('"requestId": "request-1"');
      expect(content).toContain('"requestId": "request-2"');
      expect(content).toContain('"content": "First memory"');
      expect(content).toContain('"content": "Second memory"');
    });

    it("should handle memory with scope", async () => {
      const memory: MemoryEntry = {
        id: "test-1",
        content: "[SCOPE: codebase] Memory with scope",
        version: "v1",
      };

      await pendingStore.append(memory, "request-123");

      const content = mockFileStore.getAssetAsString(testPath);
      expect(content).toContain('"requestId": "request-123"');
      expect(content).toContain(
        '"content": "[SCOPE: codebase] Memory with scope"',
      );
    });
  });

  describe("listPending", () => {
    it("should return empty array for empty store", () => {
      const memories = pendingStore.listPending();
      expect(memories).toEqual([]);
    });

    it("should parse pending memories correctly", async () => {
      // Manually set up JSON state content
      const state = {
        memories: [
          {
            id: "1",
            content: "First memory",
            version: "DEFAULT",
            timestamp: Date.now(),
          },
          {
            id: "2",
            content: "Second memory",
            version: "DEFAULT",
            timestamp: Date.now(),
          },
        ],
        lastProcessedTimestamp: Date.now(),
      };
      const content = JSON.stringify(state);
      await mockFileStore.saveAsset(
        testPath,
        new TextEncoder().encode(content),
      );

      // Create a new store instance to load the state
      const newStore = new PendingMemoriesStore(mockFileStore, testPath);
      // Wait a bit for async state loading
      await new Promise((resolve) => setTimeout(resolve, 10));

      const memories = newStore.listPending();
      expect(memories).toHaveLength(2);
      expect(memories[0].content).toBe("First memory");
      expect(memories[1].content).toBe("Second memory");
    });

    it("should handle multiline memories", async () => {
      // Set up JSON state with multiline content
      const state = {
        memories: [
          {
            id: "1",
            content: "First memory\ncontinued on next line",
            version: "DEFAULT",
            timestamp: Date.now(),
          },
          {
            id: "2",
            content: "Second memory",
            version: "DEFAULT",
            timestamp: Date.now(),
          },
        ],
        lastProcessedTimestamp: Date.now(),
      };
      const content = JSON.stringify(state);
      await mockFileStore.saveAsset(
        testPath,
        new TextEncoder().encode(content),
      );

      // Create a new store instance to load the state
      const newStore = new PendingMemoriesStore(mockFileStore, testPath);
      // Wait a bit for async state loading
      await new Promise((resolve) => setTimeout(resolve, 10));

      const memories = newStore.listPending();
      expect(memories).toHaveLength(2);
      expect(memories[0].content).toBe("First memory\ncontinued on next line");
      expect(memories[1].content).toBe("Second memory");
    });

    it("should preserve requestId when reading memories back", async () => {
      const memory: MemoryEntry = {
        id: "test-1",
        content: "Test memory with requestId",
        version: "v1",
      };

      await pendingStore.append(memory, "request-abc123");

      const memories = pendingStore.listPending();
      expect(memories).toHaveLength(1);
      expect(memories[0].requestId).toBe("request-abc123");
      expect(memories[0].content).toBe("Test memory with requestId");
      expect(memories[0].id).toBe("test-1");
      expect(memories[0].version).toBe("v1");
    });

    it("should handle memories without requestId", async () => {
      const memory: MemoryEntry = {
        id: "test-2",
        content: "Test memory without requestId",
        version: "v1",
      };

      await pendingStore.append(memory); // No requestId provided

      const memories = pendingStore.listPending();
      expect(memories).toHaveLength(1);
      expect(memories[0].requestId).toBeUndefined();
      expect(memories[0].content).toBe("Test memory without requestId");
      expect(memories[0].id).toBe("test-2");
    });
  });

  describe("removePending", () => {
    beforeEach(async () => {
      // Set up test data
      const memory1: MemoryEntry = {
        id: "test-1",
        content: "First memory",
        version: "v1",
      };
      const memory2: MemoryEntry = {
        id: "test-2",
        content: "Second memory",
        version: "v1",
      };
      await pendingStore.append(memory1, "request-1");
      await pendingStore.append(memory2, "request-2");
    });

    it("should remove memories matching predicate", async () => {
      const removedCount = await pendingStore.removePending((entry) =>
        entry.content.includes("First"),
      );

      expect(removedCount).toBe(1);

      const memories = pendingStore.listPending();
      expect(memories).toHaveLength(1);
      expect(memories[0].content).toBe("Second memory");
    });

    it("should remove all memories if predicate matches all", async () => {
      const removedCount = await pendingStore.removePending(() => true);

      expect(removedCount).toBe(2);

      const memories = pendingStore.listPending();
      expect(memories).toHaveLength(0);
    });

    it("should remove no memories if predicate matches none", async () => {
      const removedCount = await pendingStore.removePending((entry) =>
        entry.content.includes("Nonexistent"),
      );

      expect(removedCount).toBe(0);

      const memories = pendingStore.listPending();
      expect(memories).toHaveLength(2);
    });
  });

  describe("clearAll", () => {
    it("should clear all pending memories", async () => {
      // Add some memories first
      const memory: MemoryEntry = {
        id: "test-1",
        content: "Test memory",
        version: "v1",
      };
      await pendingStore.append(memory, "request-1");

      // Clear all
      await pendingStore.clearAll();

      const memories = pendingStore.listPending();
      expect(memories).toHaveLength(0);

      const content = mockFileStore.getAssetAsString(testPath);
      expect(content).toContain('"memories": []');
      expect(content).toContain('"lastProcessedTimestamp": 0');
    });
    describe("parseFromContent", () => {
      it("should parse memories from content string", () => {
        const state = {
          memories: [
            {
              id: "1",
              content: "First memory",
              version: "DEFAULT",
              timestamp: 1234567890,
            },
            {
              id: "2",
              content: "Second memory",
              version: "DEFAULT",
              timestamp: 1234567891,
            },
            {
              id: "3",
              content: "[SCOPE: test] Third memory with scope",
              version: "DEFAULT",
              timestamp: 1234567892,
              scope: "test",
            },
          ],
          lastProcessedTimestamp: 1234567892,
        };
        const content = JSON.stringify(state);

        const memories = pendingStore.parseFromContent(content);
        expect(memories).toHaveLength(3);
        expect(memories[0].content).toBe("First memory");
        expect(memories[1].content).toBe("Second memory");
        expect(memories[2].content).toBe(
          "[SCOPE: test] Third memory with scope",
        );
        expect(memories[2].scope).toBe("test");
      });

      it("should handle empty content", () => {
        const memories = pendingStore.parseFromContent("");
        expect(memories).toHaveLength(0);
      });

      it("should handle content with comments and empty lines", () => {
        // For JSON format, this test should handle malformed JSON gracefully
        const content = `# Comments are ignored

- First memory
# Another comment
- Second memory

`;
        const memories = pendingStore.parseFromContent(content);
        expect(memories).toHaveLength(0); // Should return empty array for invalid JSON
      });
    });

    describe("formatEntries", () => {
      it("should format pending memory entries to content", () => {
        const entries = [
          {
            id: "1",
            content: "First memory",
            version: "DEFAULT",
            timestamp: 1234567890,
          },
          {
            id: "2",
            content: "Second memory",
            version: "DEFAULT",
            timestamp: 1234567891,
          },
        ];

        const content = pendingStore.formatEntries(entries);
        expect(content).toContain('"memories"');
        expect(content).toContain('"id": "1"');
        expect(content).toContain('"id": "2"');
        expect(content).toContain('"content": "First memory"');
        expect(content).toContain('"content": "Second memory"');
      });

      it("should handle empty array", () => {
        const content = pendingStore.formatEntries([]);
        expect(content).toContain('"memories": []');
        expect(content).toContain('"lastProcessedTimestamp": 0');
      });
    });
  });
});
