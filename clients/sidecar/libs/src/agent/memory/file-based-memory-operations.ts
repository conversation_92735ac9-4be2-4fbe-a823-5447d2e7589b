import { getLogger } from "../../logging";
import type { IPluginFileStore } from "../../client-interfaces/plugin-file-store";
import {
  MemoryEntry,
  MemoryInfoWithState,
  MemoryState,
} from "../../webview-messages/message-types/memory-messages";
import { IMemoryDatabaseOperations } from "./memory-webview-messages";
import {
  PendingMemoriesStore,
  PendingMemoryEntry,
} from "./pending-memories-store";
import { ToolsModel } from "../../tools/tools-model";
import { SidecarToolType } from "../../tools/sidecar-tools/sidecar-tool-types";
import { createRequestId } from "../../utils/request-id";
import { Exchange } from "../../chat/chat-types";
import { ToolHostName } from "../../tools/tool-types";

const logger = getLogger("FileBasedMemoryOperations");

/**
 * FileBasedMemoryOperations implements IMemoryDatabaseOperations using file-based storage.
 */
export class FileBasedMemoryOperations implements IMemoryDatabaseOperations {
  private _pendingStore: PendingMemoriesStore;
  private readonly _pendingMemoriesAssetName = "augment-memories-pending.json";

  constructor(
    private readonly _fileStore: IPluginFileStore,
    private readonly _toolsModel: ToolsModel,
  ) {
    this._pendingStore = new PendingMemoriesStore(
      this._fileStore,
      this._pendingMemoriesAssetName,
    );
    logger.debug("FileBasedMemoryOperations initialized");
  }

  /**
   * Call the remember tool directly to persist a memory
   */
  private async _callRememberTool(
    memory: string,
    requestId?: string,
  ): Promise<void> {
    if (!this._toolsModel) {
      logger.warn("ToolsModel not available, cannot call remember tool");
      return;
    }

    try {
      // Find the SidecarToolHost
      const sidecarHost = this._toolsModel.hosts.find(
        (host) => host.getName() === ToolHostName.sidecarToolHost,
      );

      if (!sidecarHost) {
        logger.error("SidecarToolHost not found");
        throw new Error("SidecarToolHost not found");
      }

      // Get the RememberTool
      const rememberTool = sidecarHost.getTool(SidecarToolType.remember);
      if (!rememberTool) {
        logger.error("RememberTool not found");
        throw new Error("RememberTool not found");
      }

      // Prepare the tool input
      const toolInput = {
        memory: memory,
        isComplexNewMemory: false,
        caller: "memory_acceptance", // This will be used for tracking
        memoriesRequestId: requestId,
      };

      // Call the RememberTool directly
      const toolUseId = createRequestId();
      const chatHistory: Exchange[] = []; // Empty chat history for direct calls
      const abortSignal = new AbortController().signal;

      const response = await rememberTool.call(
        toolInput,
        chatHistory,
        abortSignal,
        toolUseId,
      );

      if (response.isError) {
        logger.error("RememberTool returned error:", response.text);
        throw new Error(`RememberTool error: ${response.text}`);
      }

      logger.debug(`Successfully persisted memory via RememberTool`);
    } catch (error) {
      logger.error("Failed to call remember tool:", error);
      throw error;
    }
  }

  /**
   * Process a memory entry by writing it to the pending store
   */
  async processMemoryEntry(
    memoryEntry: MemoryEntry,
    _requestId?: string,
  ): Promise<void> {
    logger.debug(`Processing memory entry: ${memoryEntry.id}`);

    try {
      // For now, all new memories go to pending store regardless of state parameter
      // State transitions are handled via updateMemoryState
      await this._pendingStore.append(memoryEntry, _requestId);

      logger.debug(`Successfully processed memory entry: ${memoryEntry.id}`);
    } catch (error) {
      logger.error(`Failed to process memory entry ${memoryEntry.id}:`, error);
      throw error;
    }
  }

  /**
   * Async version of getMemoriesByState for better file-based operations
   */
  getMemoriesByState(
    state: MemoryState,
    version?: string,
  ): MemoryInfoWithState[] {
    logger.debug(
      `Getting memories by state: ${state}${version ? ` (version: ${version})` : ""}`,
    );

    try {
      const memories: MemoryInfoWithState[] = [];

      if (state === "pending") {
        // Get pending memories
        const pendingEntries = this._pendingStore.listPending();
        for (const entry of pendingEntries) {
          // Apply version filtering if version is specified
          const versionMatches =
            version === undefined || entry.version === version;
          if (versionMatches) {
            memories.push({
              ...entry,
              state: "pending",
              blobName: undefined, // Pending memories don't have blob names
            });
          }
        }
      } else {
        logger.debug(
          `State "${state}" is not pending, skipping pending memory retrieval`,
        );
      }

      logger.debug(`Found ${memories.length} memories for state ${state}`);
      return memories;
    } catch (error) {
      logger.error(`Failed to get memories by state ${state}:`, error);
      return [];
    }
  }

  /**
   * Update a memory's state and optionally its content
   */
  async updateMemoryState(
    memoryId: string,
    newState: MemoryState,
    editedContent?: string,
  ): Promise<void> {
    logger.debug(`Updating memory ${memoryId} to state ${newState}`);

    try {
      // Find the memory in pending store first
      const pendingMemories = this._pendingStore.listPending();
      const pendingMemory = pendingMemories.find((m) => m.id === memoryId);

      if (pendingMemory) {
        await this._handlePendingMemoryStateUpdate(
          pendingMemory,
          newState,
          editedContent,
        );
      }

      logger.debug(
        `Successfully updated memory ${memoryId} to state ${newState}`,
      );
    } catch (error) {
      logger.error(`Failed to update memory ${memoryId} state:`, error);
      throw error;
    }
  }

  /**
   * Flush all pending memories to permanent storage
   */
  async flushPendingMemories(): Promise<number> {
    logger.debug("Flushing all pending memories using remember tool callback");

    try {
      const pendingMemories = this._pendingStore.listPending();

      if (pendingMemories.length === 0) {
        logger.debug("No pending memories to flush");
        return 0;
      }

      let flushedCount = 0;

      // Process each pending memory through the remember tool
      for (const pending of pendingMemories) {
        try {
          await this._callRememberTool(pending.content, pending.requestId);
          flushedCount++;
          logger.debug(`Flushed memory ${pending.id} via remember tool`);
        } catch (error) {
          logger.error(`Failed to flush memory ${pending.id}:`, error);
        }
      }

      // Clear pending storage
      await this._pendingStore.clearAll();

      logger.debug(`Successfully flushed ${flushedCount} pending memories`);
      return flushedCount;
    } catch (error) {
      logger.error("Failed to flush pending memories:", error);
      throw error;
    }
  }

  /**
   * Handle state updates for pending memories
   */
  private async _handlePendingMemoryStateUpdate(
    pendingMemory: PendingMemoryEntry,
    newState: MemoryState,
    editedContent?: string,
  ): Promise<void> {
    logger.debug(
      `Handling state update for pending memory ${pendingMemory.id} to ${newState}`,
    );
    const content = editedContent || pendingMemory.content;

    if (newState === "user_accepted" || newState === "repo_accepted") {
      // Call remember tool directly
      try {
        await this._callRememberTool(content, pendingMemory.requestId);
        logger.debug(
          `Called remember tool for accepted memory ${pendingMemory.id}`,
        );
      } catch (error) {
        logger.error(
          `Failed to call remember tool for memory ${pendingMemory.id}:`,
          error,
        );
      }

      // Remove from pending storage after processing
      await this._pendingStore.removePending((m) => m.id === pendingMemory.id);
    } else if (newState === "user_rejected") {
      // Remove from pending storage
      await this._pendingStore.removePending((m) => m.id === pendingMemory.id);
    } else if (editedContent) {
      // Update content in pending storage (remove old, add new)
      await this._pendingStore.removePending((m) => m.id === pendingMemory.id);

      const updatedEntry: MemoryEntry = {
        id: pendingMemory.id,
        content: editedContent,
        version: pendingMemory.version,
      };

      await this._pendingStore.append(updatedEntry, pendingMemory.requestId);
    }
  }
}
