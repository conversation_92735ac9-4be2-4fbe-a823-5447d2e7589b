import { MemoryEntry } from "../../webview-messages/message-types/memory-messages";

/**
 * Extract scope information from memory content for scope experiment
 * Expected format: "[SCOPE: codebase_items; task_items; category_items] actual memory content"
 * @param content The memory content that may contain scope information
 * @returns The extracted scope string or undefined if no scope found
 */
export function extractScopeFromContent(content: string): string | undefined {
  const scopeMatch = content.match(/^\[SCOPE:\s*([^\]]+)\]/);
  if (scopeMatch) {
    return scopeMatch[1].trim();
  }
  return undefined;
}

/**
 * Remove scope information from memory content, returning clean content
 * @param content The memory content that may contain scope information
 * @returns The content with scope information removed
 */
export function removeScopeFromContent(content: string): string {
  return content.replace(/^\[SCOPE:\s*[^\]]+\]\s*/, "").trim();
}

/**
 * Parse memories from a memory file content string
 * Expected format:
 * ```
 * # Comments are ignored
 * - First memory content
 * - Second memory content
 *   that spans multiple lines
 * - Third memory content
 * ```
 *
 * @param content The memory file content to parse
 * @param version The version to assign to parsed memories (defaults to "DEFAULT")
 * @returns Array of parsed MemoryEntry objects
 */
export function parseMemoriesFromContent(
  content: string,
  version: string = "DEFAULT",
): MemoryEntry[] {
  const lines = content.split("\n");
  const memories: MemoryEntry[] = [];

  let currentMemory = "";
  let memoryIndex = 0;

  for (const line of lines) {
    const trimmedLine = line.trim();

    // Skip empty lines and comments
    if (!trimmedLine || trimmedLine.startsWith("#")) {
      continue;
    }

    // Check if this is a new memory (starts with "- ")
    if (trimmedLine.startsWith("- ")) {
      // Save previous memory if exists
      if (currentMemory) {
        const memoryEntry: MemoryEntry = {
          id: `${memoryIndex}`,
          version,
          content: currentMemory.trim(),
        };
        memories.push(memoryEntry);
        memoryIndex++;
      }

      // Start new memory
      currentMemory = trimmedLine.substring(2); // Remove "- "
    } else {
      // Continue current memory
      currentMemory += " " + trimmedLine;
    }
  }

  // Don't forget the last memory
  if (currentMemory) {
    const memoryEntry: MemoryEntry = {
      id: `${memoryIndex}`,
      version,
      content: currentMemory.trim(),
    };
    memories.push(memoryEntry);
  }

  return memories;
}

/**
 * Format memory entries into markdown content
 * @param memories Array of memory entries to format
 * @returns Formatted markdown string
 */
export function formatMemoriesToContent(memories: MemoryEntry[]): string {
  return memories.map((memory) => `- ${memory.content}`).join("\n");
}
