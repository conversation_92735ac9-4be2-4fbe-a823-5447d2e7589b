import { getLogger } from "../../logging";
import type { IPluginFileStore } from "../../client-interfaces/plugin-file-store";
import { MemoryEntry } from "../../webview-messages/message-types/memory-messages";

const logger = getLogger("PendingMemoriesStore");

/**
 * Pending memory entry with additional metadata
 */
export interface PendingMemoryEntry extends MemoryEntry {
  requestId?: string;
  timestamp: number;
  scope?: string;
}

/**
 * State interface for the PendingMemoriesStore
 */
export interface PendingMemoriesState {
  memories: PendingMemoryEntry[];
  lastProcessedTimestamp: number;
}

/**
 * Predicate function for filtering pending memories
 */
export type PendingMemoryPredicate = (entry: PendingMemoryEntry) => boolean;

/**
 * PendingMemoriesStore manages pending memories in a JSON state file.
 * Provides append/list/remove/clear operations for pending memory entries.
 * Similar to MemoryDatabase but focused on pending memories only.
 */
export class PendingMemoriesStore {
  private readonly _pendingPath: string;
  private _state: PendingMemoriesState;
  private _writeLock = Promise.resolve();

  constructor(
    private readonly _fileStore: IPluginFileStore,
    pendingPath: string,
  ) {
    this._pendingPath = pendingPath;

    // Initialize state
    this._state = {
      memories: [],
      lastProcessedTimestamp: 0,
    };

    logger.debug(`PendingMemoriesStore initialized with path: ${pendingPath}`);

    // Load existing state
    void this._loadState();
  }

  /**
   * Load state from persistent storage
   */
  private async _loadState(): Promise<void> {
    try {
      const stateData = await this._fileStore.loadAsset(this._pendingPath);
      if (stateData && stateData.length > 0) {
        const stateString = new TextDecoder().decode(stateData);
        this._state = JSON.parse(stateString) as PendingMemoriesState;
        logger.debug(
          `Loaded pending memories state with ${this._state.memories.length} memories`,
        );
      } else {
        logger.debug(
          "No existing pending memories state found, starting fresh",
        );
      }
    } catch (error) {
      logger.error("Failed to load pending memories state", error);
    }
  }

  /**
   * Save state to persistent storage
   */
  private async _saveState(): Promise<void> {
    try {
      const stateData = JSON.stringify(this._state, null, 2);
      const stateBytes = new TextEncoder().encode(stateData);
      await this._fileStore.saveAsset(this._pendingPath, stateBytes);
    } catch (error) {
      logger.error("Failed to save pending memories state", error);
    }
  }

  /**
   * Append a memory entry to the pending store
   */
  async append(memory: MemoryEntry, requestId?: string): Promise<void> {
    logger.debug(`Appending memory to pending store: ${memory.id}`);

    const pendingEntry: PendingMemoryEntry = {
      ...memory,
      requestId,
      timestamp: Date.now(),
    };

    // Serialize writes to prevent corruption
    this._writeLock = this._writeLock.then(async () => {
      try {
        // Add to in-memory state
        this._state.memories.push(pendingEntry);
        this._state.lastProcessedTimestamp = pendingEntry.timestamp;

        // Save state to disk
        await this._saveState();

        logger.debug(
          `Successfully appended memory ${memory.id} to pending store`,
        );
      } catch (error) {
        logger.error(
          `Failed to append memory ${memory.id} to pending store:`,
          error,
        );
        throw error;
      }
    });

    await this._writeLock;
  }

  /**
   * List all pending memory entries
   */
  listPending(): PendingMemoryEntry[] {
    logger.debug("Listing pending memories");
    try {
      return [...this._state.memories]; // Return a copy to prevent external mutation
    } catch (error) {
      return [];
    }
  }

  /**
   * Remove pending memories matching the predicate
   */
  async removePending(predicate: PendingMemoryPredicate): Promise<number> {
    logger.debug("Removing pending memories matching predicate");

    let removedCount = 0;

    // Serialize writes to prevent corruption
    this._writeLock = this._writeLock.then(async () => {
      try {
        const originalLength = this._state.memories.length;

        // Filter out memories that match the predicate
        this._state.memories = this._state.memories.filter((entry) => {
          const shouldRemove = predicate(entry);
          if (shouldRemove) {
            logger.debug(`Removing pending memory: ${entry.id}`);
          }
          return !shouldRemove;
        });

        removedCount = originalLength - this._state.memories.length;

        // Save updated state
        await this._saveState();

        logger.debug(`Removed ${removedCount} pending memories`);
      } catch (error) {
        logger.error("Failed to remove pending memories:", error);
        throw error;
      }
    });

    await this._writeLock;
    return removedCount;
  }

  /**
   * Clear all pending memories
   */
  async clearAll(): Promise<void> {
    logger.debug("Clearing all pending memories");

    // Serialize writes to prevent corruption
    this._writeLock = this._writeLock.then(async () => {
      try {
        // Clear in-memory state
        this._state.memories = [];
        this._state.lastProcessedTimestamp = 0;

        // Save empty state
        await this._saveState();

        logger.debug("Successfully cleared all pending memories");
      } catch (error) {
        logger.error("Failed to clear pending memories:", error);
        throw error;
      }
    });

    await this._writeLock;
  }

  /**
   * Parse memories from a JSON state string without loading from file
   * Useful for testing and external memory processing
   */
  parseFromContent(content: string): PendingMemoryEntry[] {
    logger.debug("Parsing memories from provided JSON content");
    try {
      const state = JSON.parse(content) as PendingMemoriesState;
      return state.memories || [];
    } catch (error) {
      logger.warn(
        "Failed to parse JSON content, returning empty array:",
        error,
      );
      return [];
    }
  }

  /**
   * Get the formatted JSON content for a set of pending memory entries
   * Useful for testing and content generation
   */
  formatEntries(entries: PendingMemoryEntry[]): string {
    const state: PendingMemoriesState = {
      memories: entries,
      lastProcessedTimestamp:
        entries.length > 0 ? Math.max(...entries.map((e) => e.timestamp)) : 0,
    };
    return JSON.stringify(state, null, 2);
  }
}
