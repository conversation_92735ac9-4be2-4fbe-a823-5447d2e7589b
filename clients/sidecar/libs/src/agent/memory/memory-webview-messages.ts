import { IWebviewMessageConsumer } from "../../webview-messages/webview-messages-broker";
import { getLogger, AugmentLogger } from "../../logging";
import {
  CommonWebViewMessageType,
  WebViewMessage,
} from "../../webview-messages/common-webview-messages";
import {
  MemoryWebViewMessageType,
  MemoryCreatedMessage,
  MemoryCreatedResponse,
  MemoryEntry,
  GetMemoriesByStateMessage,
  GetMemoriesByStateResponse,
  UpdateMemoryStateMessage,
  UpdateMemoryStateResponse,
  FlushPendingMemoriesMessage,
  FlushPendingMemoriesResponse,
  MemoryInfoWithState,
  MemoryState,
} from "../../webview-messages/message-types/memory-messages";
import { FileBasedMemoryOperations } from "./file-based-memory-operations";
import { getPluginFileStore } from "../../client-interfaces/plugin-file-store";
import { ToolsModel } from "../../tools/tools-model";

/**
 * Interface for memory database operations
 */
export interface IMemoryDatabaseOperations {
  processMemoryEntry(
    memoryEntry: MemoryEntry,
    requestId?: string,
  ): Promise<void>;
  getMemoriesByState(
    state: MemoryState,
    version?: string,
  ): MemoryInfoWithState[];
  updateMemoryState(
    memoryId: string,
    newState: MemoryState,
    editedContent?: string,
  ): Promise<void>;
  flushPendingMemories?(): Promise<number>;
}

/**
 * Handles webview messages related to memory operations.
 * This class acts as the bridge between the webview messaging system
 * and the memory listener service.
 */
export class MemoryWebviewMessageHandler
  implements IWebviewMessageConsumer<MemoryWebViewMessageType>
{
  public readonly supportedTypes = MemoryWebViewMessageType;
  private readonly _logger: AugmentLogger;
  private readonly _memoryOperations: FileBasedMemoryOperations;

  constructor(toolsModel: ToolsModel) {
    this._logger = getLogger("MemoryWebviewMessageHandler");
    this._memoryOperations = new FileBasedMemoryOperations(
      getPluginFileStore(),
      toolsModel,
    );
    this._logger.info("Memory webview message handler initialized");
  }

  /**
   * Handle incoming webview messages related to memory operations
   */
  public async handle(
    msg: WebViewMessage<MemoryWebViewMessageType>,
    postMessage: (
      msg: WebViewMessage<MemoryWebViewMessageType | CommonWebViewMessageType>,
    ) => void,
  ): Promise<void> {
    try {
      switch (msg.type) {
        case MemoryWebViewMessageType.memoryCreated: {
          const memoryMessage = msg as MemoryCreatedMessage;
          await this._handleMemoryCreated(memoryMessage, postMessage);
          break;
        }
        case MemoryWebViewMessageType.getMemoriesByState: {
          const getMemoriesMessage = msg as GetMemoriesByStateMessage;
          this._handleGetMemoriesByState(getMemoriesMessage, postMessage);
          break;
        }
        case MemoryWebViewMessageType.updateMemoryState: {
          const updateMessage = msg as UpdateMemoryStateMessage;
          await this._handleUpdateMemoryState(updateMessage, postMessage);
          break;
        }
        case MemoryWebViewMessageType.flushPendingMemories: {
          const flushMessage = msg as FlushPendingMemoriesMessage;
          await this._handleFlushPendingMemories(flushMessage, postMessage);
          break;
        }
        default: {
          this._logger.warn(`Unhandled memory message type: ${msg.type}`);
          postMessage({ type: CommonWebViewMessageType.empty });
          break;
        }
      }
    } catch (error) {
      this._logger.error("Failed to handle webview message", error);
      throw error; // Re-throw to maintain error handling behavior
    }
  }

  /**
   * Handle memory created messages
   */
  private async _handleMemoryCreated(
    message: MemoryCreatedMessage,
    postMessage: (
      msg: WebViewMessage<MemoryWebViewMessageType | CommonWebViewMessageType>,
    ) => void,
  ): Promise<void> {
    this._logger.debug(
      `Handling memory created message for memory: ${message.data.memory.id}`,
    );

    try {
      // Process the memory entry
      await this._memoryOperations.processMemoryEntry(
        message.data.memory,
        message.data.metadata.requestId,
      );
      this._logger.debug(
        `Successfully processed memory entry: ${message.data.memory.id}`,
      );

      // Send success response
      const response: MemoryCreatedResponse = {
        type: MemoryWebViewMessageType.memoryCreatedResponse,
        data: {
          success: true,
          memoryId: message.data.memory.id,
        },
      };
      postMessage(response);
    } catch (error) {
      this._logger.error(
        `Failed to process memory entry ${message.data.memory.id}`,
        error,
      );

      // Send error response
      const response: MemoryCreatedResponse = {
        type: MemoryWebViewMessageType.memoryCreatedResponse,
        data: {
          success: false,
          error: error instanceof Error ? error.message : "Unknown error",
          memoryId: message.data.memory.id,
        },
      };
      postMessage(response);
    }
  }

  /**
   * Handle get memories by state messages
   */
  private _handleGetMemoriesByState(
    message: GetMemoriesByStateMessage,
    postMessage: (
      msg: WebViewMessage<MemoryWebViewMessageType | CommonWebViewMessageType>,
    ) => void,
  ): void {
    try {
      const memories = this._memoryOperations.getMemoriesByState(
        message.data.state,
        message.data.version,
      );

      const response: GetMemoriesByStateResponse = {
        type: MemoryWebViewMessageType.getMemoriesByStateResponse,
        data: {
          memories,
        },
      };
      postMessage(response);
    } catch (error) {
      this._logger.error(
        `Failed to get memories by state ${message.data.state}${
          message.data.version ? ` with version ${message.data.version}` : ""
        }`,
        error,
      );

      // Send empty response on error
      const response: GetMemoriesByStateResponse = {
        type: MemoryWebViewMessageType.getMemoriesByStateResponse,
        data: {
          memories: [],
        },
      };
      postMessage(response);
    }
  }

  /**
   * Handle update memory state messages
   */
  private async _handleUpdateMemoryState(
    message: UpdateMemoryStateMessage,
    postMessage: (
      msg: WebViewMessage<MemoryWebViewMessageType | CommonWebViewMessageType>,
    ) => void,
  ): Promise<void> {
    this._logger.debug(
      `Handling update memory state for ${message.data.memoryId} to ${message.data.newState}`,
    );

    try {
      await this._memoryOperations.updateMemoryState(
        message.data.memoryId,
        message.data.newState,
        message.data.editedContent,
      );

      const response: UpdateMemoryStateResponse = {
        type: MemoryWebViewMessageType.updateMemoryStateResponse,
        data: {
          success: true,
          memoryId: message.data.memoryId,
          newState: message.data.newState,
        },
      };
      postMessage(response);
    } catch (error) {
      this._logger.error(
        `Failed to update memory state for ${message.data.memoryId}`,
        error,
      );

      const response: UpdateMemoryStateResponse = {
        type: MemoryWebViewMessageType.updateMemoryStateResponse,
        data: {
          success: false,
          error: error instanceof Error ? error.message : "Unknown error",
          memoryId: message.data.memoryId,
          newState: message.data.newState,
        },
      };
      postMessage(response);
    }
  }

  /**
   * Handle flush pending memories messages
   */
  private async _handleFlushPendingMemories(
    _message: FlushPendingMemoriesMessage,
    postMessage: (
      msg: WebViewMessage<MemoryWebViewMessageType | CommonWebViewMessageType>,
    ) => void,
  ): Promise<void> {
    this._logger.debug("Handling flush pending memories request");

    try {
      const flushedCount =
        (await this._memoryOperations.flushPendingMemories?.()) ?? 0;

      const response: FlushPendingMemoriesResponse = {
        type: MemoryWebViewMessageType.flushPendingMemoriesResponse,
        data: {
          success: true,
          flushedCount,
        },
      };
      postMessage(response);
    } catch (error) {
      this._logger.error("Failed to flush pending memories", error);

      const response: FlushPendingMemoriesResponse = {
        type: MemoryWebViewMessageType.flushPendingMemoriesResponse,
        data: {
          success: false,
          error: error instanceof Error ? error.message : "Unknown error",
          flushedCount: 0,
        },
      };
      postMessage(response);
    }
  }
}
