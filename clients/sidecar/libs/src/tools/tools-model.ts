import { AggregateCheckpointManager } from "../agent/checkpoint/aggregate-checkpoint-manager";
import {
  AgentGlobalStateKeys,
  DEFAULT_SWARM_MODE_SETTINGS,
  SwarmModeSettings,
} from "../agent/agent-edit-types";
import { ChatMode, Exchange } from "../chat/chat-types";
import { IClientFeatureFlags } from "../client-interfaces/feature-flags";
import { IPluginFileStore } from "../client-interfaces/plugin-file-store";
import { getLogger } from "../logging";
import { McpHost } from "./mcp/mcp-host";
import {
  RemoteInfoSource,
  RemoteToolHost,
} from "./remote-tools/remote-tool-host";
import { SidecarToolHost } from "./sidecar-tools/sidecar-tool-host";
import { SidecarToolType } from "./sidecar-tools/sidecar-tool-types";
import { IToolHost } from "./tool-host";
import { ITool } from "./tool-host-base";
import {
  McpServerConfig,
  ToolDefinitionWithSettings,
  ToolHostName,
  ToolHostOptions,
  ToolStartupErrorFn,
  ToolType,
  ToolUseRequestEventReporter,
  ToolUseResponse,
  ToolApprovalConfig,
} from "./tool-types";
import {
  CheckToolCallSafeRequestData,
  CheckToolCallSafeResponseData,
} from "../webview-messages/message-types/tool-messages";
import { IToolApprovalConfigManager } from "./approval-config/i-tool-approval-config-manager";
import { EventEmitter } from "events";
import {
  PluginStateNamespace,
  PluginStateScope,
} from "../client-interfaces/plugin-state";
import { getStateForSidecar } from "../client-interfaces/plugin-state";
import { MemorySnapshotManager } from "../agent/memory/memory-snapshot-manager";
import { TaskManager } from "../agent/task/task-manager";
import {
  generatePKCEPair,
  generateOAuthUrlAndClientIdForPartner,
} from "../auth/pkce-utils";
import {
  REMOTE_PARTNER_MCP_CONFIG,
  PARTNER_MCP_SERVERS,
} from "./partner-remote-mcp";
import { OAuthMetadata } from "./oauth-types";

/**
 * Manages the tools functionality.
 *
 * These are "tools" in the sense of Claude's Tool Use
 * {@link https://docs.anthropic.com/en/docs/build-with-claude/tool-use}
 * or Open AI Function Calling
 * {@link https://platform.openai.com/docs/guides/function-calling}
 *
 * `ToolsModel` aggregates multiple tool hosts implementing the `IToolHost`
 * interface and provides an abstraction over them . Examples of tools hosts
 * include:
 * - MCP servers (`McpHost`)
 * - Our backend with its remote tools (`RemoteToolHost`) like web search or the
 *   Github API
 * - Client side tools (`SidecarToolHost`) like the shell tool
 *
 * Currently, tools from local MCP (Model Context Protocol) servers are supported.
 */
export class ToolsModel {
  // Static event name for host restart events
  private static readonly RESTART_HOSTS_EVENT =
    "augment/clients/sidecar/events/restart-hosts";

  // Storage key for persisting chat mode
  private static readonly CHAT_MODE_STORAGE_KEY =
    "augment/clients/sidecar/chat-mode";

  // hosts with tools available to the model based on the current policy
  private _hosts: IToolHost[] = [];
  // all tool hosts, even disabled ones, to provide information for the settings panel
  private _allToolHosts: IToolHost[] = [];

  private _chatMode?: ChatMode;
  private readonly _logger = getLogger("ToolsModel");
  private _memorySnapshotManager: MemorySnapshotManager;

  /**
   * Event emitter for host restart events
   * @private
   */
  private readonly _onRestartHostsEmitter = new EventEmitter();

  /**
   * Timeout for debouncing restart hosts calls
   * @private
   */
  private _restartHostsTimeout: NodeJS.Timeout | undefined = undefined;

  /**
   * Event that fires when hosts are restarted
   * Subscribers can use this to perform actions after hosts are restarted
   */
  public readonly onRestartHosts = (
    listener: () => void,
  ): { dispose: () => void } => {
    this._onRestartHostsEmitter.on(ToolsModel.RESTART_HOSTS_EVENT, listener);
    return {
      dispose: () => {
        this._onRestartHostsEmitter.off(
          ToolsModel.RESTART_HOSTS_EVENT,
          listener,
        );
      },
    };
  };

  constructor(
    private _mcpServers: McpServerConfig[],
    private _clientToolHostFactory: (options: ToolHostOptions) => IToolHost,
    private _remoteInfoSource: RemoteInfoSource,
    private _mcpToolsStartupErrorFn: ToolStartupErrorFn,
    private _clientFeatureFlags: IClientFeatureFlags,
    private _checkpointManager: AggregateCheckpointManager,
    private _getAgentMemories: () => Promise<string | undefined>,
    private _getMemoriesAbsPath: () => string | undefined,
    private _getToolUseRequestEventReporter: () =>
      | ToolUseRequestEventReporter
      | undefined,
    private _toolApprovalConfigManager: IToolApprovalConfigManager,
    private _options: ToolsModelOptions = {},
    private _taskManager: TaskManager | undefined = undefined,
  ) {
    // Initialize the memory snapshot manager
    this._memorySnapshotManager = new MemorySnapshotManager(
      this._getAgentMemories,
    );

    // Load saved mode (if any) and then initialize
    void this.loadSavedMode().then(() => {
      // Apply the initial config after loading the saved mode
      void this.restartHosts();
    });
  }

  /**
   * Loads the saved chat mode from plugin state storage.
   * @returns A promise that resolves when the mode is loaded
   */
  private async loadSavedMode(): Promise<void> {
    try {
      const pluginState = getStateForSidecar();
      const savedMode = await pluginState.getValue<ChatMode>(
        PluginStateNamespace.agent,
        ToolsModel.CHAT_MODE_STORAGE_KEY,
        PluginStateScope.global,
      );

      // Only load if the saved mode is valid and we dont have a mode already
      if (
        savedMode &&
        Object.values(ChatMode).includes(savedMode) &&
        this._chatMode === undefined
      ) {
        this._chatMode = savedMode;
        this._logger.info(`Loaded saved chat mode: ${String(savedMode)}`);
      }
    } catch (error) {
      this._logger.warn(`Failed to load saved chat mode: ${String(error)}`);
    }
  }

  /**
   * Public method to get the current swarm mode enablement state
   */
  public async getEnableAgentSwarmMode(): Promise<boolean> {
    return this.computeEnableAgentSwarmMode();
  }

  /**
   * Computes whether agent swarm mode should be enabled based on:
   * - Feature flag is enabled
   * - User has used remote agents before
   * - User has enabled swarm mode in their options
   */
  private async computeEnableAgentSwarmMode(): Promise<boolean> {
    try {
      // Check if feature flag is enabled
      const featureFlagEnabled =
        this._clientFeatureFlags.flags.enableAgentSwarmMode;
      this._logger.debug(
        `Feature flag enableAgentSwarmMode: ${featureFlagEnabled}`,
      );
      if (!featureFlagEnabled) {
        return false;
      }

      // Check if user has ever used remote agents
      const pluginState = getStateForSidecar();
      const hasEverUsedRemoteAgent = await pluginState.getValue<boolean>(
        PluginStateNamespace.agent,
        AgentGlobalStateKeys.hasEverUsedRemoteAgent,
        PluginStateScope.global,
      );
      this._logger.debug(
        `Has ever used remote agent: ${hasEverUsedRemoteAgent}`,
      );

      if (!hasEverUsedRemoteAgent) {
        return false;
      }

      // Check if user has enabled swarm mode in their settings
      const swarmModeSettings = await pluginState.getValue<SwarmModeSettings>(
        PluginStateNamespace.agent,
        AgentGlobalStateKeys.swarmModeSettings,
        PluginStateScope.global,
      );

      const userEnabledSwarmMode =
        swarmModeSettings?.enabled ?? DEFAULT_SWARM_MODE_SETTINGS.enabled;
      this._logger.debug(`User enabled swarm mode: ${userEnabledSwarmMode}`);

      const result = userEnabledSwarmMode;
      this._logger.info(
        `Computed enableAgentSwarmMode: ${result} (featureFlag: ${featureFlagEnabled}, hasUsedRemote: ${hasEverUsedRemoteAgent}, userEnabled: ${userEnabledSwarmMode})`,
      );

      return result;
    } catch (error) {
      this._logger.warn(
        `Failed to compute enableAgentSwarmMode: ${String(error)}`,
      );
      return false;
    }
  }

  public get memoriesAbsPath(): string | undefined {
    return this._getMemoriesAbsPath();
  }

  /**
   * Gets agent memories with snapshot support.
   * @param conversationId The current conversation ID
   * @returns The agent memories content
   *
   * NOTE: This ideally should not live here for much longer. Yes, the toolsModel uses
   * memories, but ideally the memories should move into their own independent model. It's
   * semantically incorrect to say that tools "has" memories.
   */
  public async getAgentMemoriesWithSnapshot(
    conversationId: string,
  ): Promise<string | undefined> {
    return this._memorySnapshotManager.getMemorySnapshot(conversationId);
  }

  /**
   * Forces an update of the memory snapshot.
   * This should be called when the memories file is manually edited.
   *
   * NOTE: This ideally should not live here for much longer. Yes, the toolsModel uses
   * memories, but ideally the memories should move into their own independent model. It's
   * semantically incorrect to say that tools "has" memories.
   */
  public async forceMemorySnapshotUpdate(): Promise<void> {
    await this._memorySnapshotManager.forceUpdateSnapshot();
  }

  /**
   * @returns A map of *enabled* tool names to tool hosts.
   */
  private async getToolMap(): Promise<Map<string, IToolHost>> {
    const toolMap = new Map<string, IToolHost>();
    for (const host of this.hosts) {
      const toolDefinitions = await host.getToolDefinitions();
      for (const toolDefinition of toolDefinitions) {
        if (!this.isEnabled(toolDefinition)) {
          continue;
        }
        toolMap.set(toolDefinition.definition.name.toString(), host);
      }
    }

    return toolMap;
  }

  public get hosts(): readonly IToolHost[] {
    return this._hosts;
  }

  public get chatMode(): ChatMode {
    return this._chatMode ?? ChatMode.chat;
  }

  /**
   * Validates that the given chat mode is supported.
   * @param mode The chat mode to validate
   * @throws Error if the chat mode is not supported
   */
  private validateChatMode(mode: ChatMode): void {
    // Validate that the current chat mode is explicitly supported.
    // This check is in place so if we add a new chat mode, this code will fail
    // instead of silently not working.
    const supportedChatModes = [
      ChatMode.chat,
      ChatMode.agent,
      ChatMode.remoteAgent,
      ChatMode.memories,
      ChatMode.orientation,
      ChatMode.memoriesCompression,
      ChatMode.cliAgent,
    ];
    if (!supportedChatModes.includes(mode)) {
      throw new Error(
        `Unsupported chat mode: ${String(mode)}. Supported modes: ${supportedChatModes.join(", ")}`,
      );
    }
  }

  /**
   * Returns the SidecarToolHost instance if available.
   * This is used to access the MemoryUpdateManager for memory update notifications.
   * @returns The SidecarToolHost instance or undefined if not available
   */
  public getSidecarToolHost(): SidecarToolHost | undefined {
    for (const host of this._hosts) {
      if (host instanceof SidecarToolHost) {
        return host;
      }
    }
    return undefined;
  }

  public setMode(mode: ChatMode) {
    // Validate the chat mode first
    this.validateChatMode(mode);

    if (mode === this._chatMode) {
      return;
    }

    this._chatMode = mode;

    // Save mode to plugin state
    try {
      const pluginState = getStateForSidecar();
      void pluginState.setValue(
        PluginStateNamespace.agent,
        ToolsModel.CHAT_MODE_STORAGE_KEY,
        mode,
        PluginStateScope.global,
      );
      this._logger.info(`Saved chat mode: ${String(mode)}`);
    } catch (error) {
      this._logger.warn(`Failed to save chat mode: ${String(error)}`);
    }

    void this.restartHosts();
  }

  public onFlagsChanged() {
    this.debouncedRestartHosts();
  }

  public setMcpServers(mcpServers: McpServerConfig[]) {
    this._mcpServers = mcpServers;
    this.debouncedRestartHosts(true);
  }

  public getMcpServers(): McpServerConfig[] {
    return this._mcpServers;
  }

  /**
   * Debounced version of restartHosts to prevent rapid successive calls
   * @param mcpOnly Whether to restart only MCP hosts
   */
  private debouncedRestartHosts(mcpOnly: boolean = false): void {
    // Clear any existing timeout
    if (this._restartHostsTimeout) {
      clearTimeout(this._restartHostsTimeout);
    }

    // Set a new timeout to restart hosts after a short delay
    this._restartHostsTimeout = setTimeout(() => {
      void this.restartHosts(mcpOnly);
      this._restartHostsTimeout = undefined;
    }, 100); // 100ms debounce delay
  }

  public async getToolDefinitions(): Promise<ToolDefinitionWithSettings[]> {
    const toolDefinitions: ToolDefinitionWithSettings[] = [];
    for (const host of this.hosts) {
      const tools = await host.getToolDefinitions();
      for (const tool of tools) {
        if (!this.isEnabled(tool)) {
          continue;
        }
        toolDefinitions.push(tool);
      }
    }
    return toolDefinitions;
  }

  /**
   * Return ToolDefinitionWithSettings for all tools.
   */
  public async getToolStatusForSettingsPanel(
    useCache: boolean = true,
  ): Promise<ToolDefinitionWithSettings[]> {
    const toolDefinitions: ToolDefinitionWithSettings[] = [];

    for (const host of this._allToolHosts) {
      const tools = await host.getAllToolDefinitions(useCache);
      toolDefinitions.push(...tools);
    }

    toolDefinitions.push(
      REMOTE_PARTNER_MCP_CONFIG[PARTNER_MCP_SERVERS.STRIPE].toolDefinition,
      REMOTE_PARTNER_MCP_CONFIG[PARTNER_MCP_SERVERS.SENTRY].toolDefinition,
    );

    // Inject the tool approval config for each tool.
    for (const tool of toolDefinitions) {
      const toolApprovalConfig =
        await this._toolApprovalConfigManager.getToolApprovalConfig({
          // Eventually if we support configurations per agent mode, this should be passed from frontend
          // to determine what mode to show settings for.
          mode: "manual",
          toolId: tool.identifier,
        });
      tool.toolApprovalConfig = toolApprovalConfig;
    }

    return toolDefinitions;
  }

  public async callTool(
    chatRequestId: string,
    toolUseId: string,
    toolName: string,
    toolInput: Record<string, unknown>,
    chatHistory: Exchange[],
    conversationId: string,
  ): Promise<ToolUseResponse> {
    const toolMap = await this.getToolMap();
    const host = toolMap.get(toolName);
    if (host === undefined) {
      return {
        isError: true,
        text: `Tool ${toolName} not found.`,
      };
    }

    const startTime = Date.now();
    const result = await host.callTool(
      chatRequestId,
      toolUseId,
      toolName,
      toolInput,
      chatHistory,
    );
    const toolUseRequestEventReporter = this._getToolUseRequestEventReporter();
    if (toolUseRequestEventReporter !== undefined) {
      // Calculate the tool output length in characters
      const toolOutputLen = result.text ? result.text.length : undefined;

      toolUseRequestEventReporter.reportEvent(
        chatRequestId,
        toolName,
        toolUseId,
        toolInput,
        result.isError,
        Date.now() - startTime,
        host instanceof McpHost,
        conversationId,
        chatHistory.length,
        result.requestId,
        toolOutputLen,
      );
    }
    return result;
  }

  /**
   * Gets the tool definition for a given tool name.
   * @param toolName The name of the tool to find
   * @returns The tool definition with settings
   * @throws Error if the tool host or tool definition cannot be found
   */
  private async getToolDefinitionForToolName(
    toolName: string,
  ): Promise<ToolDefinitionWithSettings> {
    const toolMap = await this.getToolMap();
    const host = toolMap.get(toolName);
    if (host === undefined) {
      throw new Error(`Cannot find the host for tool '${toolName}'.`);
    }
    const toolDefinitions = await host.getAllToolDefinitions();
    const toolDefinition = toolDefinitions.find(
      (tool) => tool.definition.name === toolName,
    );
    if (toolDefinition === undefined) {
      throw new Error(`Cannot find tool definition for tool '${toolName}'.`);
    }
    return toolDefinition;
  }

  /**
   * Checks if a tool call is safe. If a tool call is safe, it can be run
   * without user approval.
   * @param request The tool call safety check request
   * @returns The tool call safety check response
   */
  public async checkToolCallSafe(
    request: CheckToolCallSafeRequestData,
  ): Promise<CheckToolCallSafeResponseData> {
    // Get tool approval config (need to map name to identifier).
    const toolDefinition = await this.getToolDefinitionForToolName(
      request.toolName,
    );
    const toolApprovalConfig: ToolApprovalConfig | undefined =
      await this._toolApprovalConfigManager.getToolApprovalConfig({
        mode: request.agentMode || "",
        toolId: toolDefinition.identifier,
      });

    // Add the tool approval config to the request, and then let the host check if it's safe.
    const enrichedRequest: CheckToolCallSafeRequestData = {
      ...request,
      toolApprovalConfig,
    };

    // Get the host for the tool to check if it's safe
    const toolMap = await this.getToolMap();
    const host = toolMap.get(request.toolName);
    if (host === undefined) {
      throw new Error(`Cannot find the host for tool '${request.toolName}'.`);
    }

    const isSafe = await host.checkToolCallSafe(enrichedRequest);
    return { isSafe };
  }

  public async checkToolExists(toolName: string): Promise<boolean> {
    const toolMap = await this.getToolMap();
    return toolMap.has(toolName);
  }

  public async cancelToolRun(
    requestId: string,
    toolUseId: string,
  ): Promise<void> {
    // Find the host that is running the tool and close / reopen it.
    //
    // While the MCP SDK has a cancellation API, its usage still amounts to closing
    // and restarting the server, and so essentially relaunching the local process.
    // Implementing the cancellation by restarting the server ourselves seems to
    // be a bit simpler than using the cancellation API, and not much efficiency
    // is lost.
    for (let i = 0; i < this.hosts.length; i++) {
      const host = this.hosts[i];
      if (host.isRequestActive(requestId, toolUseId)) {
        // Close the old host
        const closePromise = host.close(true);

        // Create a new host
        this._hosts[i] = host.factory(closePromise);

        // Return the close promise so that the caller waits for the cancellation to finish
        return closePromise;
      }
    }

    // Nothing to cancel.
    return Promise.resolve();
  }

  public async restartHosts(mcpOnly: boolean = false): Promise<void> {
    // Validate the current chat mode
    this.validateChatMode(this.chatMode);

    // All tool hosts
    // Remarks:
    // - The new mcp hosts will wait for the closeAllPromise to resolve
    //   before serving any requests. This ensures that all past work has
    //   quiesced.
    // - In TypeScript, it's fine to await the same promise (closeAllPromise)
    //   multiple times.
    let hostsToClose = this._allToolHosts;
    if (mcpOnly) {
      hostsToClose = this._allToolHosts.filter(
        (host) => host.getName() === ToolHostName.mcpHost,
      );
    }
    const closePromises = hostsToClose.map((host) => host.close());
    const closeAllPromise = Promise.all(closePromises).then(() => {});

    const mcpHosts = this._mcpServers
      .filter((config) => !config.disabled)
      .map(
        (config) =>
          new McpHost(config, closeAllPromise, this._mcpToolsStartupErrorFn),
      );

    let remoteToolHost: RemoteToolHost | undefined;
    let localToolHost: IToolHost | undefined;
    let sidecarToolHost: SidecarToolHost | undefined;
    if (mcpOnly) {
      remoteToolHost = this._allToolHosts.find(
        (host) => host.getName() === ToolHostName.remoteToolHost,
      ) as RemoteToolHost;
      localToolHost = this._allToolHosts.find(
        (host) => host.getName() === ToolHostName.localToolHost,
      );
      sidecarToolHost = this._allToolHosts.find(
        (host) => host.getName() === ToolHostName.sidecarToolHost,
      ) as SidecarToolHost;
    }
    remoteToolHost =
      remoteToolHost ?? new RemoteToolHost(this._remoteInfoSource);

    localToolHost =
      localToolHost ??
      this._clientToolHostFactory({
        chatMode: this.chatMode,
        enableAgentSwarmMode: await this.computeEnableAgentSwarmMode(),
      });
    sidecarToolHost =
      sidecarToolHost ??
      new SidecarToolHost(
        this.chatMode,
        this._clientFeatureFlags,
        this._checkpointManager,
        this._getAgentMemories,
        this._getMemoriesAbsPath,
        this._options.unsupportedSidecarTools ?? new Set<SidecarToolType>(),
        this._options.userAgent,
        this._taskManager,
        this._options.fileStore,
      );

    // keep all hosts for reporting to the settings panel
    this._allToolHosts = [
      ...mcpHosts,
      localToolHost,
      remoteToolHost,
      sidecarToolHost,
    ];

    // Validate that the current chat mode is explicitly supported
    this.validateChatMode(this.chatMode);

    // the model might use a subset of the hosts, depending on the mode
    const useTools =
      (this._chatMode === ChatMode.chat &&
        this._clientFeatureFlags.flags.enableChatWithTools) ||
      (this._chatMode === ChatMode.agent &&
        this._clientFeatureFlags.flags.enableAgentMode) ||
      this._chatMode === ChatMode.remoteAgent || // Always use tools in the remote agent
      this._chatMode === ChatMode.cliAgent; // Always use tools in the CLI agent
    if (useTools) {
      this._hosts = [
        ...mcpHosts,
        localToolHost,
        remoteToolHost,
        sidecarToolHost,
      ];
    } else {
      this._hosts = [];
    }

    void this.logTools();

    // Notify all listeners that hosts have been restarted
    this._onRestartHostsEmitter.emit(ToolsModel.RESTART_HOSTS_EVENT);
  }

  public closeAllToolProcesses() {
    for (const host of this.hosts) {
      void host.closeAllToolProcesses();
    }
  }

  public getTool<T extends ToolType>(toolName: string): ITool<T> | undefined {
    for (const host of this.hosts) {
      const tool = host.getTool<T>(toolName);
      if (tool) {
        return tool;
      }
    }
    return undefined;
  }

  private async logTools() {
    this._logger.info(
      `Tools Mode: ${this._chatMode} (${this.hosts.length} hosts)`,
    );
    for (const host of this.hosts) {
      const toolDefs = await host.getToolDefinitions();

      const enabledTools: ToolType[] = [];
      const disabledTools: ToolType[] = [];
      for (const toolDef of toolDefs) {
        const list = this.isEnabled(toolDef) ? enabledTools : disabledTools;
        list.push(toolDef.definition.name);
      }
      const enabledToolNames = enabledTools
        .map((name) => ` + ${name}`)
        .join("\n");
      const disabledToolNames = disabledTools
        .map((name) => ` - ${name}`)
        .join("\n");

      this._logger.info(
        `Host: ${host.getName()} (${toolDefs.length} tools: ${enabledToolNames.length} enabled, ${disabledToolNames.length} disabled})\n${enabledToolNames}\n${disabledToolNames}`,
      );
    }
  }

  private isEnabled(_toolDefinition: ToolDefinitionWithSettings) {
    return _toolDefinition.enabled;
  }

  /**
   * Generate OAuth URL for an MCP server with PKCE and store the verifier.
   * This should be called when the user initiates the OAuth flow.
   *
   * @param mcpName - The name of the MCP server (e.g., "stripe", "sentry")
   * @param oauthMetadata - Optional OAuth metadata to use directly for generic MCP servers.
   *                        When provided, bypasses the need to look up metadata from stored server configuration.
   *                        When not provided, falls back to looking up the MCP server configuration.
   * @param resource - The resource MCP endpoint
   * @returns The OAuth URL for the user to visit
   */
  public async generateMcpOAuthUrl(
    mcpName: string,
    oauthMetadata?: OAuthMetadata,
    resource?: string,
  ): Promise<string> {
    // Generate PKCE pair
    const { codeVerifier, codeChallenge } = generatePKCEPair();

    // Generate OAuth URL and get client ID based on MCP name
    let oauthResult: { url: string; clientId: string };

    // First check if this is a hardcoded partner MCP server
    const partnerMcpName = mcpName.toLowerCase() as PARTNER_MCP_SERVERS;
    if (oauthMetadata && resource) {
      oauthResult = await this.generateOAuthUrlWithMetadata(
        mcpName,
        codeChallenge,
        oauthMetadata,
        resource,
      );
    } else {
      switch (partnerMcpName) {
        case PARTNER_MCP_SERVERS.STRIPE:
          oauthResult = await generateOAuthUrlAndClientIdForPartner(
            PARTNER_MCP_SERVERS.STRIPE,
            "stripe",
            codeChallenge,
          );
          break;
        case PARTNER_MCP_SERVERS.SENTRY:
          oauthResult = await generateOAuthUrlAndClientIdForPartner(
            PARTNER_MCP_SERVERS.SENTRY,
            "sentry",
            codeChallenge,
          );
          break;
        default:
          throw new Error(`Unsupported partner MCP name: ${mcpName}`);
      }
    }

    // Store OAuth state with client ID for later verification during callback
    await this.storeMcpOAuthState(mcpName, codeVerifier, oauthResult.clientId);

    return oauthResult.url;
  }

  /**
   * Generate OAuth URL using provided OAuth metadata directly.
   * This method bypasses the need to look up server configuration.
   *
   * @param mcpName - The name of the MCP server
   * @param codeChallenge - The PKCE code challenge
   * @param oauthMetadata - The OAuth metadata to use for URL generation
   * @returns Object containing the OAuth URL and client ID
   */
  private async generateOAuthUrlWithMetadata(
    mcpName: string,
    codeChallenge: string,
    oauthMetadata: OAuthMetadata,
    resource: string,
  ): Promise<{ url: string; clientId: string }> {
    // Validate required OAuth endpoints
    if (!oauthMetadata.authorization_endpoint) {
      throw new Error(
        `OAuth metadata is missing authorization_endpoint for MCP server: ${mcpName}`,
      );
    }
    if (!oauthMetadata.token_endpoint) {
      throw new Error(
        `OAuth metadata is missing token_endpoint for MCP server: ${mcpName}`,
      );
    }

    // Try dynamic client registration if supported
    let clientId: string;
    if (oauthMetadata.registration_endpoint) {
      clientId = await this.dynamicallyRegisterMcpClient(
        oauthMetadata.registration_endpoint,
        mcpName,
      );
    } else {
      // Use a default client ID for servers that don't support dynamic registration
      // This assumes the server has been pre-configured with this client ID
      clientId = "augment-code-assistant";
    }

    // Build the authorization URL
    const authUrl = new URL(oauthMetadata.authorization_endpoint);
    const params = new URLSearchParams({
      response_type: "code",
      client_id: clientId,
      redirect_uri: `vscode://augment.vscode-augment/auth/mcp/${mcpName}`,
      resource,
      code_challenge: codeChallenge,
      code_challenge_method: "S256",
    });

    authUrl.search = params.toString();

    return {
      url: authUrl.toString(),
      clientId,
    };
  }

  /**
   * Dynamically register a client with an OAuth server.
   *
   * @param registrationEndpoint - The OAuth dynamic client registration endpoint
   * @param mcpName - The name of the MCP server for the redirect URI
   * @returns The registered client ID
   */
  private async dynamicallyRegisterMcpClient(
    registrationEndpoint: string,
    mcpName: string,
  ): Promise<string> {
    // Try multiple registration approaches in case the first one fails
    const registrationRequest = {
      client_name: "Augment Code",
      client_uri: "https://augmentcode.com",
      redirect_uris: [`vscode://augment.vscode-augment/auth/mcp/${mcpName}`],
      grant_types: ["authorization_code"],
      response_types: ["code"],
      token_endpoint_auth_method: "none", // Public client using PKCE
      software_id: "augment-code-assistant",
      software_version: "1.0.0",
      // PKCE support indicators
      code_challenge_methods_supported: ["S256"],
      application_type: "native",
    };

    const response = await fetch(registrationEndpoint, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Accept: "application/json",
        "User-Agent": "Augment-Code-Assistant/1.0.0",
      },
      body: JSON.stringify(registrationRequest),
    });

    if (!response.ok) {
      const errorText = await response.text();
      let errorDetails = `${response.status} ${response.statusText}`;

      try {
        const errorJson = JSON.parse(errorText) as {
          error?: string;
          error_description?: string;
        };
        if (errorJson.error) {
          errorDetails += ` - ${errorJson.error}`;
          if (errorJson.error_description) {
            errorDetails += `: ${errorJson.error_description}`;
          }
        }
      } catch {
        // If error response is not JSON, include raw text
        errorDetails += ` - ${errorText}`;
      }

      this._logger.error(
        `OAuth client registration failed for ${mcpName}. Request: ${JSON.stringify(registrationRequest, null, 2)}. Error: ${errorDetails}`,
      );
      throw new Error(`Registration failed: ${errorDetails}`);
    }

    const registrationResult = (await response.json()) as Record<string, any>;
    const clientId = registrationResult?.client_id as string;

    if (!clientId) {
      throw new Error("Registration response did not include client_id");
    }

    this._logger.info(
      `Successfully registered OAuth client for MCP ${mcpName}: ${clientId}`,
    );
    return clientId;
  }

  /**
   * Store OAuth state for an MCP server using plugin state storage.
   *
   * @param mcpName - The name of the MCP server
   * @param codeVerifier - The PKCE code verifier to store
   * @param clientId - The dynamically generated client ID to store
   */
  private async storeMcpOAuthState(
    mcpName: string,
    codeVerifier: string,
    clientId: string,
  ): Promise<void> {
    const pluginState = getStateForSidecar();
    const stateKey = `mcp-oauth-state.${mcpName}`;

    const oauthState = {
      codeVerifier,
      mcpName,
      creationTime: Date.now(),
      clientId,
    };

    await pluginState.setValue(
      PluginStateNamespace.agent,
      stateKey,
      oauthState,
      PluginStateScope.global,
    );

    this._logger.info(
      `Stored OAuth state for MCP: ${mcpName}${clientId ? ` with client ID: ${clientId}` : ""}`,
    );
  }

  /**
   * Retrieve stored OAuth state for an MCP server.
   *
   * @param mcpName - The name of the MCP server
   * @returns The stored OAuth state, or null if not found or expired
   */
  public async getMcpOAuthState(mcpName: string): Promise<{
    codeVerifier: string;
    mcpName: string;
    creationTime: number;
    clientId: string;
  } | null> {
    const pluginState = getStateForSidecar();
    const stateKey = `mcp-oauth-state.${mcpName}`;

    try {
      const storedState = await pluginState.getValue<{
        codeVerifier: string;
        mcpName: string;
        creationTime: number;
        clientId: string;
      }>(PluginStateNamespace.agent, stateKey, PluginStateScope.global);

      if (!storedState) {
        return null;
      }

      // Check if state has expired (10 minutes)
      const now = Date.now();
      const isExpired = now - storedState.creationTime > 10 * 60 * 1000;

      if (isExpired) {
        // Clean up expired state
        await this.clearMcpOAuthState(mcpName);
        return null;
      }

      return storedState;
    } catch (error) {
      this._logger.warn(
        `Failed to retrieve OAuth state for ${mcpName}: ${String(error)}`,
      );
      return null;
    }
  }

  /**
   * Clear stored OAuth state for an MCP server.
   * This should be called after successful token exchange or when cleaning up expired state.
   *
   * @param mcpName - The name of the MCP server
   */
  public async clearMcpOAuthState(mcpName: string): Promise<void> {
    const pluginState = getStateForSidecar();
    const stateKey = `mcp-oauth-state.${mcpName}`;

    try {
      await pluginState.setValue(
        PluginStateNamespace.agent,
        stateKey,
        undefined,
        PluginStateScope.global,
      );
      this._logger.info(`Cleared OAuth state for MCP: ${mcpName}`);
    } catch (error) {
      this._logger.warn(
        `Failed to clear OAuth state for ${mcpName}: ${String(error)}`,
      );
    }
  }
}

type ToolsModelOptions = {
  userAgent?: string;
  unsupportedSidecarTools?: Set<SidecarToolType>;
  fileStore?: IPluginFileStore;
};
