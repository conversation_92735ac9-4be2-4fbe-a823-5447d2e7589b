import { WebFetchTool } from "../web-fetch";
import { LocalToolType } from "../../tool-types";
import {
  TruncatedContentType,
  UntruncatedContentManager,
  TruncatedContentMetadata,
} from "../../../utils/untruncated-content-manager";

// Simple helper to build a mock fetch Response-like object
function makeResponse(body: string, contentType: string) {
  return {
    text: () => body,
    headers: {
      get: (name: string) =>
        name.toLowerCase() === "content-type" ? contentType : null,
    },
  } as unknown as Response;
}

describe("WebFetchTool", () => {
  const urlHtml = "https://example.com/code";
  const urlText = "https://example.com/long.txt";
  let originalFetch: unknown;

  const signal = new AbortController().signal;
  const toolUseId = "test-tooluse-123";

  beforeEach(() => {
    originalFetch = global.fetch as unknown;
  });

  afterEach(() => {
    // Restore the original fetch after each test
    (global as unknown as { fetch?: unknown }).fetch = originalFetch;
    jest.restoreAllMocks();
  });

  test("converts GitHub-style per-line elements into newline-separated text", async () => {
    const html = `
      <table class="highlight">
        <tr><td class="blob-code">first</td></tr>
        <tr><td class="blob-code">second</td></tr>
      </table>
      <div data-line-number="3">third</div>
    `;

    (global as unknown as { fetch: unknown }).fetch = jest
      .fn()
      .mockResolvedValue(makeResponse(html, "text/html; charset=utf-8"));

    const tool = new WebFetchTool();
    const res = await tool.call({ url: urlHtml }, [], signal, toolUseId);

    expect(res.isError).toBe(false);
    // Expect the two blob-code lines to be joined with a single newline
    expect(res.text).toContain("first\nsecond\n");
    // The data-line-number element should also emit a trailing newline
    expect(res.text).toContain("third\n");
    // Ensure we did not insert spurious blank lines between first/second
    expect(res.text).not.toMatch(/first\n\nsecond/);
  });

  test("truncates long content and returns footer with reference ID and tool instructions", async () => {
    // Create content comfortably larger than the 63 KiB limit
    const longText = "line-xyz\n".repeat(10000);

    (global as unknown as { fetch: unknown }).fetch = jest
      .fn()
      .mockResolvedValue(makeResponse(longText, "text/plain; charset=utf-8"));

    // Mock UntruncatedContentManager
    const calls: {
      content?: string;
      contentType?: TruncatedContentType;
      shownRange?: [number, number] | [number, number, number, number];
      toolUseId?: string;
      toolType?: LocalToolType;
      originalCommand?: string;
    } = {};

    const mockContentManager: UntruncatedContentManager = {
      // Only the method used by the tool needs to be implemented
      storeUntruncatedContent: (
        content: string,
        contentType: TruncatedContentType,
        shownRange: [number, number] | [number, number, number, number],
        toolUseIdParam?: string,
        _requestId?: string,
        _conversationId?: string,
        toolTypeParam?: LocalToolType,
        originalCommandParam?: string,
      ): TruncatedContentMetadata => {
        calls.content = content;
        calls.contentType = contentType;
        calls.shownRange = shownRange;
        calls.toolUseId = toolUseIdParam;
        calls.toolType = toolTypeParam;
        calls.originalCommand = originalCommandParam;
        return {
          contentType,
          totalSize: content.length,
          totalLines: content.split("\n").length,
          shownRange,
          referenceId: "ref-abc123",
          toolUseId: toolUseIdParam,
          requestId: undefined,
          conversationId: undefined,
          toolType: toolTypeParam,
          originalCommand: originalCommandParam,
          timestamp: Date.now(),
        };
      },
    } as unknown as UntruncatedContentManager;

    const tool = new WebFetchTool(undefined, mockContentManager);
    const res = await tool.call({ url: urlText }, [], signal, toolUseId);

    expect(res.isError).toBe(false);
    expect(res.text).not.toEqual(longText); // actually truncated

    // Footer must include reference ID and tool instructions
    expect(res.text).toMatch(/Reference ID:\s*ref-abc123/i);
    expect(res.text).toMatch(/view-range-untruncated|search-untruncated/i);

    // Verify the content manager saw the full untruncated content and metadata
    expect(calls.content).toEqual(longText);
    expect(calls.contentType).toBe(TruncatedContentType.ToolOutput);
    expect(calls.toolUseId).toBe(toolUseId);
    expect(calls.toolType).toBe(LocalToolType.webFetch);
    expect(calls.originalCommand).toBe("web-fetch: " + urlText);
  });

  test("raw text payloads preserve newlines (no Turndown)", async () => {
    const body = "a\nb\nc\n";
    (global as unknown as { fetch: unknown }).fetch = jest
      .fn()
      .mockResolvedValue(makeResponse(body, "text/plain"));

    const tool = new WebFetchTool();
    const res = await tool.call({ url: urlText }, [], signal, toolUseId);
    expect(res.isError).toBe(false);
    expect(res.text).toBe(body);
  });
});
