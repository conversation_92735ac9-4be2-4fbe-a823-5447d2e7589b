import { Exchange } from "../../chat/chat-types";
import {
  ToolBase,
  ToolSafety,
  ToolUseResponse,
  LocalToolType,
} from "../tool-types";
import { SidecarToolType } from "./sidecar-tool-types";
import TurndownService from "turndown";
import {
  UntruncatedContentManager,
  TruncatedContentType,
} from "../../utils/untruncated-content-manager";
import {
  truncateWithMetadata,
  TruncateOptions,
} from "../../utils/truncation-utils";

const DEFAULT_USER_AGENT = "Augment-WebFetch/1.0";

// Minimal shape for an element-like node in environments without DOM typings
type MinimalElement = {
  nodeType: number;
  hasAttribute?: (name: string) => boolean;
  getAttribute?: (name: string) => string | null;
};

function isElementNode(n: unknown): n is MinimalElement {
  if (typeof n !== "object" || n === null) return false;
  const maybe = n as Record<string, unknown>;
  return typeof maybe["nodeType"] === "number" && maybe["nodeType"] === 1;
}

export class WebFetchTool extends ToolBase<SidecarToolType> {
  private _turndownService: TurndownService;
  private _userAgent: string;
  private _contentManager?: UntruncatedContentManager;
  private readonly _maxOutputLength = 63 * 1024; // 64KiB - 1KiB buffer for additional text

  constructor(userAgent?: string, contentManager?: UntruncatedContentManager) {
    super(SidecarToolType.webFetch, ToolSafety.Unsafe);

    const turndownService = new TurndownService();

    // Remove style and script tags
    turndownService.addRule("removeStyleAndScriptTags", {
      filter: ["style", "script"],
      replacement: function () {
        return "";
      },
    });

    // Heuristic: treat common "code line" elements as separate lines.
    // Many code viewers (e.g., GitHub/GitLab) render each line as its own element
    // without actual newline characters in the HTML. We insert a newline after
    // such elements to preserve line breaks in Markdown.
    // Ensure we do not create double blank lines by trimming trailing newlines first.
    turndownService.addRule("codeLineBreakByDataAttributes", {
      filter: (node) => {
        if (!isElementNode(node)) return false;
        const getAttr = (name: string): string | null => {
          const fn = node.getAttribute;
          return typeof fn === "function" ? fn(name) : null;
        };
        return (
          getAttr("data-line-number") !== null ||
          getAttr("data-lineno") !== null ||
          getAttr("data-line") !== null
        );
      },
      replacement: (content: string) => {
        const trimmed = content ? content.replace(/\n+$/, "") : "";
        return trimmed + "\n";
      },
    });

    // GitHub/GitLab-style classes for per-line code elements
    turndownService.addRule("codeLineBreakByClassNames", {
      filter: (node) => {
        if (!isElementNode(node)) return false;
        const getAttr = node.getAttribute;
        if (typeof getAttr !== "function") return false;
        const className = getAttr("class") || "";
        if (!className) return false;
        const lc = className.toLowerCase();
        const targets = [
          "blob-code", // GitHub
          "blob-code-inner", // GitHub
          "js-file-line", // GitHub
          "line_content", // GitLab
          "line-content", // GitLab
          "file-line", // generic
          "code-line", // generic
        ];
        return targets.some((t) => lc.indexOf(t) !== -1);
      },
      replacement: (content: string) => {
        const trimmed = content ? content.replace(/\n+$/, "") : "";
        return trimmed + "\n";
      },
    });

    // Suppress extra newlines added by table/tr wrappers in GitHub-style code tables
    turndownService.addRule("suppressNewlineForHighlightContainers", {
      filter: (node) => {
        if (!isElementNode(node)) return false;
        const me = node as MinimalElement & {
          nodeName?: string;
          parentNode?: unknown;
        };
        const tag = (me.nodeName ?? "").toString().toLowerCase();
        const getClass = (): string => {
          const fn = me.getAttribute;
          return typeof fn === "function" ? (fn("class") ?? "") : "";
        };
        const hasClass = (cls: string): boolean =>
          getClass().toLowerCase().indexOf(cls) !== -1;
        const isTableHighlight = tag === "table" && hasClass("highlight");

        // Walk up to see if inside a table.highlight
        let p: unknown = me.parentNode;
        let insideHighlight = isTableHighlight;
        while (p && !insideHighlight) {
          if (!isElementNode(p)) {
            break;
          }
          const e = p as MinimalElement & {
            nodeName?: string;
            parentNode?: unknown;
          };
          const pTag = (e.nodeName ?? "").toString().toLowerCase();
          const getPClass = (): string => {
            const fn = e.getAttribute;
            return typeof fn === "function" ? (fn("class") ?? "") : "";
          };
          if (
            pTag === "table" &&
            getPClass().toLowerCase().indexOf("highlight") !== -1
          ) {
            insideHighlight = true;
            break;
          }
          p = (e as unknown as { parentNode?: unknown }).parentNode;
        }

        if (!insideHighlight) return false;
        // Only suppress for container elements that may add their own newlines
        return tag === "table" || tag === "tbody" || tag === "tr";
      },
      replacement: (content: string) => content || "",
    });

    this._turndownService = turndownService;
    this._userAgent = userAgent || DEFAULT_USER_AGENT;
    this._contentManager = contentManager;
  }

  // Very small HTML→text fallback for environments without a DOM (e.g., Bazel node tests)
  // Handles common code-table patterns and data-line* attributes, ensuring exactly one newline per line
  private _fallbackHtmlToText(html: string): string {
    const decodeEntities = (s: string): string =>
      s
        .replace(/&lt;/g, "<")
        .replace(/&gt;/g, ">")
        .replace(/&amp;/g, "&")
        .replace(/&quot;/g, '"')
        .replace(/&#39;/g, "'");

    const removeTags = (s: string): string => s.replace(/<[^>]+>/g, "");

    const lines: string[] = [];

    // 1) Extract per-line TDs used by code viewers (GitHub/GitLab)
    const tdRe =
      /<td[^>]*class="[^"]*(?:blob-code|blob-code-inner|js-file-line|line_content|line-content|file-line|code-line)[^"]*"[^>]*>([\s\S]*?)<\/td>/gi;
    html.replace(tdRe, (_m: string, inner: string): string => {
      const txt = decodeEntities(removeTags(inner)).replace(/\n+$/g, "");
      lines.push(txt);
      return "";
    });

    // 2) Extract elements with data-line* attributes
    const dataLineRe =
      /<([a-zA-Z0-9:-]+)[^>]*\sdata-(?:line-number|lineno|line)\s*=\s*["'][^"']*["'][^>]*>([\s\S]*?)<\/\1>/gi;
    html.replace(
      dataLineRe,
      (_m: string, _tag: string, inner: string): string => {
        const txt = decodeEntities(removeTags(inner)).replace(/\n+$/g, "");
        lines.push(txt);
        return "";
      },
    );

    if (lines.length === 0) {
      // Rough text fallback: just strip tags and decode entities
      return decodeEntities(removeTags(html));
    }

    // Join with exactly one newline and ensure trailing newline
    return lines.join("\n") + "\n";
  }

  public readonly description: string = `\
Fetches data from a webpage and converts it into Markdown.

1. The tool takes in a URL and returns the content of the page in Markdown format;
2. If the return is not valid Markdown, it means the tool cannot successfully parse this page.`;

  public readonly inputSchemaJson: string = JSON.stringify({
    type: "object",
    properties: {
      url: {
        type: "string",
        description: "The URL to fetch.",
      },
    },
    required: ["url"],
  });

  public checkToolCallSafe(_toolInput: Record<string, unknown>): boolean {
    return false;
  }

  public async call(
    toolInput: Record<string, unknown>,
    _chatHistory: Exchange[],
    abortSignal: AbortSignal,
    toolUseId: string,
  ): Promise<ToolUseResponse> {
    const url = toolInput.url as string;
    try {
      const response = await fetch(url, {
        signal: abortSignal,
        headers: {
          "User-Agent": this._userAgent,
        },
      });
      const text = await response.text();

      // Detect content type and only run Turndown for HTML; for text/* (or unknown),
      // return as-is to preserve newlines (e.g., raw.githubusercontent.com)
      const contentType = response.headers.get("content-type") || "";
      const isHtml =
        contentType.includes("text/html") ||
        contentType.includes("application/xhtml");

      const markdown = isHtml
        ? (() => {
            try {
              return this._turndownService.turndown(text);
            } catch (_err) {
              // Fallback when DOM is unavailable (e.g., non-jsdom environments)
              return this._fallbackHtmlToText(text);
            }
          })()
        : text;

      // Ensure a single trailing newline for per-line code blocks denoted by data-line* attributes
      let finalMarkdown = markdown;
      if (isHtml) {
        const hasPerLineAttr = /data-(line-number|lineno|line)\s*=/.test(text);
        if (hasPerLineAttr && !finalMarkdown.endsWith("\n")) {
          finalMarkdown += "\n";
        }
      }

      // Apply truncation if content manager is available and content exceeds limit
      if (
        this._contentManager &&
        finalMarkdown.length > this._maxOutputLength
      ) {
        const options: TruncateOptions = {
          maxBytes: this._maxOutputLength,
          contentType: TruncatedContentType.ToolOutput,
          toolUseId,
          toolType: LocalToolType.webFetch,
          originalCommand: `web-fetch: ${url}`,
        };

        const result = await truncateWithMetadata(
          finalMarkdown,
          options,
          this._contentManager,
          true, // enableUntruncatedContentStorage
        );

        return {
          text: result.truncatedContent,
          isError: false,
        };
      }

      return {
        text: finalMarkdown,
        isError: false,
      };
    } catch (e: unknown) {
      return {
        text: `Failed to fetch URL: ${url}: ${e instanceof Error ? e.message : String(e)}`,
        isError: true,
      };
    }
  }
}
