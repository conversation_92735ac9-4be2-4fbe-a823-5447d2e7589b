import { <PERSON>t<PERSON><PERSON> } from "../../chat/chat-types";
import { IClientFeatureFlags } from "../../client-interfaces/feature-flags";
import { IToolHost } from "../tool-host";
import { ITool, ToolHostBase } from "../tool-host-base";
import { CodebaseRetrievalTool } from "./codebase-retrieval";
import { ToolHostName } from "../tool-types";
import { AgentEditTools } from "./sidecar-tool-types";
import { RemoveFilesTool } from "./remove-files-tool";
import { SaveFileTool } from "./save-file-tool";
import { StrReplaceEditorTool } from "./str-replace-editor-tool/str-replace-editor-tool";
import { ViewTool } from "./view-tool/view-tool";
import { WebFetchTool } from "./web-fetch";
import { AggregateCheckpointManager } from "../../agent/checkpoint/aggregate-checkpoint-manager";
import { SidecarToolType } from "./sidecar-tool-types";
import { RememberTool } from "@augment-internal/sidecar-libs/src/tools/sidecar-tools/remember-tool";
import { MemoryUpdateManager } from "../../agent/memory/memory-update-manager";
import { TaskManager } from "../../agent/task/task-manager";

import {
  ViewTaskListTool,
  ReorganizeTaskListTool,
  UpdateTasksTool,
  AddTasksTool,
} from "./task-tools";
import { ViewRangeUntruncatedTool } from "./untruncated-content-tools/view-range-untruncated-tool";
import { SearchUntruncatedTool } from "./untruncated-content-tools/search-untruncated-tool";
import { UntruncatedContentManager } from "../../utils/untruncated-content-manager";
import { IPluginFileStore } from "../../client-interfaces/plugin-file-store";
import { RenderMermaidTool } from "./render-mermaid-tool/render-mermaid-tool";
import { GrepSearchTool } from "./grep-search-tool";

/**
 * A tool host for tools that run in the sidecar (i.e. a node environment).
 */
export class SidecarToolHost extends ToolHostBase<SidecarToolType> {
  private readonly _memoryUpdateManager: MemoryUpdateManager;
  private _untruncatedContentManager?: UntruncatedContentManager;

  /**
   * Validates that the given chat mode is supported.
   * @param mode The chat mode to validate
   * @throws Error if the chat mode is not supported
   */
  private static validateChatMode(mode: ChatMode): void {
    // Validate that the current chat mode is explicitly supported.
    // This check is in place so if we add a new chat mode, this code will fail
    // instead of silently not working.
    const supportedChatModes = [
      ChatMode.chat,
      ChatMode.agent,
      ChatMode.remoteAgent,
      ChatMode.memories,
      ChatMode.orientation,
      ChatMode.memoriesCompression,
      ChatMode.cliAgent,
    ];
    if (!supportedChatModes.includes(mode)) {
      throw new Error(
        `Unsupported chat mode: ${String(mode)}. Supported modes: ${supportedChatModes.join(", ")}`,
      );
    }
  }

  constructor(
    private readonly _chatMode: ChatMode,
    private readonly _clientFeatureFlags: IClientFeatureFlags,
    private readonly _checkpointManager: AggregateCheckpointManager,
    private readonly _getAgentMemories: () => Promise<string | undefined>,
    private readonly _getAgentMemoriesAbsPath: () => string | undefined,
    private readonly _unsupportedSidecarTools: Set<SidecarToolType>,
    private readonly _userAgent?: string,
    private readonly _taskManager: TaskManager | undefined = undefined,
    private readonly _fileStore?: IPluginFileStore,
  ) {
    // Validate the chat mode first
    SidecarToolHost.validateChatMode(_chatMode);

    // Create the memory update manager first
    const memoryUpdateManager = new MemoryUpdateManager();

    // Create a content manager if file store is available
    let contentManager: UntruncatedContentManager | undefined;
    if (_fileStore) {
      contentManager = new UntruncatedContentManager(_fileStore);
    }

    // Create the tools array
    const tools: ITool<SidecarToolType>[] = [];
    if (_chatMode === ChatMode.remoteAgent) {
      // Remote agent is always configured with the same sidecar tools
      tools.push(
        new WebFetchTool(_userAgent, contentManager),
        new CodebaseRetrievalTool(),
        new RemoveFilesTool(_checkpointManager),
        new SaveFileTool(_checkpointManager),
        new StrReplaceEditorTool(_checkpointManager),
        new ViewTool(),
        // Add RenderMermaidTool for Remote Agent mode
        new RenderMermaidTool(),
      );
      if (_clientFeatureFlags.flags.grepSearchToolEnable) {
        tools.push(new GrepSearchTool());
      }
    } else if (_chatMode === ChatMode.cliAgent) {
      tools.push(
        new WebFetchTool(_userAgent, contentManager),
        new CodebaseRetrievalTool(),
        new RemoveFilesTool(_checkpointManager),
        new SaveFileTool(_checkpointManager),
        new StrReplaceEditorTool(_checkpointManager),
        new ViewTool(),
      );
      if (_clientFeatureFlags.flags.grepSearchToolEnable) {
        tools.push(new GrepSearchTool());
      }
    } else {
      tools.push(
        new WebFetchTool(_userAgent, contentManager),
        new CodebaseRetrievalTool(),
      );

      if (_chatMode === ChatMode.agent) {
        tools.push(new RemoveFilesTool(_checkpointManager));
        tools.push(new SaveFileTool(_checkpointManager));
        if (_taskManager && _clientFeatureFlags.flags.enableTaskList) {
          tools.push(
            new ViewTaskListTool(_taskManager),
            new ReorganizeTaskListTool(_taskManager),
            new UpdateTasksTool(_taskManager),
            new AddTasksTool(_taskManager),
          );
        }
      }

      if (_chatMode === ChatMode.agent) {
        tools.push(
          new RememberTool(
            _getAgentMemories,
            _getAgentMemoriesAbsPath,
            memoryUpdateManager,
          ),
        );
        // Add RenderMermaidTool for Agent mode
        tools.push(new RenderMermaidTool());

        if (_clientFeatureFlags.flags.grepSearchToolEnable) {
          tools.push(new GrepSearchTool());
        }
      }

      // Add untruncated content tools if content manager is available
      if (contentManager) {
        tools.push(new ViewRangeUntruncatedTool(contentManager));
        tools.push(new SearchUntruncatedTool(contentManager));
      }

      if (
        _chatMode === ChatMode.agent &&
        _clientFeatureFlags.flags.agentEditTool ===
          AgentEditTools.strReplaceEditor
      ) {
        tools.push(new StrReplaceEditorTool(_checkpointManager));
        tools.push(new ViewTool());
      }
    }

    // Call super with the tools array
    super(tools, ToolHostName.sidecarToolHost, _unsupportedSidecarTools);

    // Now we can set the instance variables
    this._memoryUpdateManager = memoryUpdateManager;
    this._untruncatedContentManager = contentManager;
  }

  factory(): IToolHost {
    return new SidecarToolHost(
      this._chatMode,
      this._clientFeatureFlags,
      this._checkpointManager,
      this._getAgentMemories,
      this._getAgentMemoriesAbsPath,
      this._unsupportedSidecarTools,
      this._userAgent,
      this._taskManager,
      this._fileStore,
    );
  }

  /**
   * Gets the memory update manager instance.
   * This can be used to register callbacks for memory update events.
   */
  public getMemoryUpdateManager(): MemoryUpdateManager {
    return this._memoryUpdateManager;
  }

  /**
   * Gets the untruncated content manager instance if available.
   * This can be used to access untruncated content functionality.
   *
   * @returns The untruncated content manager or undefined if not available
   */
  public getUntruncatedContentManager(): UntruncatedContentManager | undefined {
    return this._untruncatedContentManager;
  }
}
