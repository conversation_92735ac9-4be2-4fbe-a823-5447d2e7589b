import { Exchange } from "../chat/chat-types";
import { ITool } from "./tool-host-base";
import { SidecarToolType } from "./sidecar-tools/sidecar-tool-types";
import { ChatMode } from "../chat/chat-types";
import { OAuthMetadata } from "@augment-internal/sidecar-libs/src/tools/oauth-types";
/**
 * Options for creating a tool host instance
 */
export interface ToolHostOptions {
  chatMode: ChatMode;
  enableAgentSwarmMode: boolean;
}

export enum LocalToolType {
  readFile = "read-file",
  saveFile = "save-file",
  editFile = "edit-file",
  clarify = "clarify",
  onboardingSubAgent = "onboarding-sub-agent",
  launchProcess = "launch-process",
  killProcess = "kill-process",
  readProcess = "read-process",
  writeProcess = "write-process",
  listProcesses = "list-processes",
  waitProcess = "wait-process",
  openBrowser = "open-browser",
  strReplaceEditor = "str-replace-editor",
  remember = "remember",
  diagnostics = "diagnostics",
  webFetch = "web-fetch",
  setupScript = "setup-script",
  readTerminal = "read-terminal",
  gitCommitRetrieval = "git-commit-retrieval",
  memoryRetrieval = "memory-retrieval",
  // Agent orchestration tools
  startWorkerAgent = "start_worker_agent",
  readWorkerState = "read_worker_state",
  waitForWorkerAgent = "wait_for_worker_agent",
  sendInstructionToWorkerAgent = "send_instruction_to_worker_agent",
  stopWorkerAgent = "stop_worker_agent",
  deleteWorkerAgent = "delete_worker_agent",
  readWorkerAgentEdits = "read_worker_agent_edits",
  applyWorkerAgentEdits = "apply_worker_agent_edits",
  LocalSubAgent = "local-sub-agent",
}

export enum ToolHostName {
  remoteToolHost = "remoteToolHost",
  localToolHost = "localToolHost",
  sidecarToolHost = "sidecarToolHost",
  mcpHost = "mcpHost",
}

export type MCPToolType = string;
export type ToolType =
  | RemoteToolId
  | LocalToolType
  | SidecarToolType
  | MCPToolType;

/**
 * Interface representing a unique tool identifier in the system
 * Uses discriminated union to ensure type safety between host names and tool IDs
 */
export type ToolIdentifier =
  | {
      hostName: ToolHostName.remoteToolHost;
      toolId: RemoteToolId;
    }
  | {
      hostName: ToolHostName.localToolHost;
      toolId: LocalToolType;
    }
  | {
      hostName: ToolHostName.sidecarToolHost;
      toolId: SidecarToolType;
    }
  | {
      hostName: ToolHostName.mcpHost;
      toolId: MCPToolType;
    };

/**
 * ToolDefinitionWithSettings represents a tools definition
 * and all additional state/configuration relevant for running
 * or configuring the tool.
 *
 * For example: a ToolDefinitionWithSettings should contain
 * all necessary information for displaying the tool in the settings
 * page, as well as determining whether the tool should require
 * approval when run.
 */
export interface ToolDefinitionWithSettings {
  definition: ToolDefinition;
  identifier: ToolIdentifier;
  isConfigured: boolean;
  enabled: boolean;
  toolSafety: ToolSafety;
  oauthUrl?: string;
  toolApprovalConfig?: ToolApprovalConfig;
}

export type McpServerType = "stdio" | "http" | "sse";

export type McpServerStdioConfig = {
  type: "stdio";
  command: string;
  args?: string[];
  timeoutMs?: number;
  env?: { [key: string]: string };
  useShellInterpolation?: boolean;
  name?: string; // User-defined name for the MCP server, used for namespacing
  disabled?: boolean;
};

export type McpServerHttpConfig = {
  type: "http" | "sse";
  url: string;
  timeoutMs?: number;
  name?: string; // User-defined name for the MCP server, used for namespacing
  disabled?: boolean;
  accessToken?: string;
  authRequired?: boolean; // Indicates if the server requires authentication (detected via 401 response)
  oauthMetadata?: OAuthMetadata; // OAuth 2.0 metadata discovered from the server
};

export type McpServerConfig = McpServerStdioConfig | McpServerHttpConfig;

export interface ToolUseRequest {
  // The request ID of the chat exchange that generated the tool use
  chatRequestId: string;
  toolUseId: string;
  name: string;
  input: Record<string, unknown>;
  chatHistory: Exchange[];
  conversationId: string;
}

// Response types using the enum-based pattern
export enum ToolResponseContentNodeType {
  ContentText = 0,
  ContentImage = 1,
}

export interface ToolResponseImageContent {
  image_data: string; // Base64-encoded image data
  media_type: string;
}

export interface ToolResponseContentNode {
  type: ToolResponseContentNodeType;
  text_content?: string;
  image_content?: ToolResponseImageContent;
}

export interface ToolUseResponse {
  text: string;
  isError: boolean;
  // The request ID of the tool execution itself, if applicable
  requestId?: string;
  // Array of content nodes (text and images)
  contentNodes?: ToolResponseContentNode[];
}

export type ToolDefinition = {
  name: ToolType;
  description: string;
  input_schema_json: string;
  tool_safety: ToolSafety;
  original_mcp_server_name?: string; // Original server name for MCP tools, needed for UI mapping purposes
  mcp_server_name?: string; // Sanitized MCP server name for MCP tools
  mcp_tool_name?: string; // Original MCP tool name without server suffix
};

export enum ToolSafety {
  /** Tool always needs user approval to run. */
  Unsafe = 0,

  /** Tool does not need user approval to run. */
  Safe = 1,

  /** For some inputs, the tool needs user approval and for some it does not. */
  Check = 2,
}

export function convertIntToToolSafety(value: number): ToolSafety {
  // Check if the value is a valid ToolSafety enum value
  if (Object.values(ToolSafety).includes(value as ToolSafety)) {
    return value as ToolSafety;
  }
  // Default to Unsafe for invalid values
  return ToolSafety.Unsafe;
}

export interface ToolStartupError {
  command?: string;
  args?: string[];
  error?: string;
  stderr?: string;
  isAuthRequired?: boolean; // Indicates if the error is due to authentication requirement (401)
}

export type ToolStartupErrorFn = (details: ToolStartupError) => void;

export abstract class ToolBase<T extends ToolType> implements ITool<T> {
  constructor(
    public name: T,
    public toolSafety: ToolSafety,
  ) {}

  abstract description: string;

  abstract inputSchemaJson: string;

  public version: number = 1;

  abstract checkToolCallSafe(toolInput: Record<string, unknown>): boolean;

  abstract call(
    toolInput: Record<string, unknown>,
    chatHistory: Exchange[],
    abortSignal: AbortSignal,
    toolUseId: string,
  ): Promise<ToolUseResponse>;
}

export enum RemoteToolId {
  Unknown = 0,

  // Google search
  WebSearch = 1,

  // Jira tools (DEPRECATED)
  // JiraSearch = 2,
  // JiraIssue = 3,
  // JiraProject = 4,

  // Notion tools (DEPRECATED)
  // NotionSearch = 5,
  // NotionPage = 6,

  // Linear tools (DEPRECATED)
  // LinearSearchIssues = 7,

  // GitHub tools
  GitHubApi = 8,

  // Confluence tools (DEPRECATED)
  // ConfluenceSearch = 9,
  // ConfluenceContent = 10,
  // ConfluenceSpace = 11,

  // New integration tools
  Linear = 12,
  Jira = 13,
  Confluence = 14,
  Notion = 15,
  Supabase = 16,
  Glean = 17,
}

export enum ToolAvailabilityStatus {
  UnknownStatus = 0,
  Available = 1,
  UserConfigRequired = 2,
}

export type AtlassianToolExtraInput = {
  // The URL of the Atlassian server, e.g. https://augmentcode.atlassian.net
  serverUrl: string;

  // The user's API token
  personalApiToken: string;

  // The user's email address
  username: string;
};

export type NotionToolExtraInput = {
  // The Notion API token
  apiToken: string;
};

export type LinearToolExtraInput = {
  // The Linear API token
  apiToken: string;
};

export type GitHubToolExtraInput = {
  // The GitHub API token
  apiToken: string;
};

export type ExtraToolInput =
  | AtlassianToolExtraInput
  | NotionToolExtraInput
  | LinearToolExtraInput
  | GitHubToolExtraInput;

export type ToolUseRequestEventReporter = {
  reportEvent: (
    chatRequestId: string,
    toolName: string,
    toolUseId: string,
    toolInput: Record<string, unknown>,
    toolOutputIsError: boolean,
    toolRunDurationMs: number,
    isMcpTool: boolean,
    conversationId: string,
    chatHistoryLength: number,
    toolRequestId?: string,
    toolOutputLen?: number,
  ) => void;
};

/**
 * Common shell configuration type shared across the codebase
 */
export interface ShellConfig {
  /** The internal name of the shell (e.g., 'bash', 'zsh', 'powershell') */
  name: string;

  /** The path to the shell executable */
  path?: string;

  /** Arguments to pass to the shell */
  args?: string[];

  /** Environment variables to set for the shell */
  env?: Record<string, string>;

  /** User-friendly display name for the shell */
  friendlyName: string;

  /** Information about the shell's capabilities to show the user */
  supportString?: string;
}

/**
 * Interface for terminal settings
 */
export interface TerminalSettings {
  /** List of supported shells */
  supportedShells: ShellConfig[];

  /** The selected shell's friendlyName */
  selectedShell?: string;

  /** Startup script to run when a new terminal is created */
  startupScript?: string;
}

/**
 * Tool approval configuration types and interfaces
 */

/**
 * Basic tool approval levels matching what most of our tools currently implement/support.
 */
export enum SimpleToolApprovalLevel {
  /** Always require user approval */
  Never = "never",
  /** Never require user approval */
  Always = "always",
  /** Require approval based on the tool arguments. */
  InputDependent = "inputDependent",
}

// This looks silly right now, but in the future ToolApprovalConfig should
// be a union type of all the different, specific tool approval configs.
export type ToolApprovalConfig = {};
