import { createHash, randomBytes } from "crypto";
import {
  REMOTE_PARTNER_MCP_CONFIG,
  REMOTE_PARTNER_MCP_SERVERS,
} from "../tools/partner-remote-mcp";
import { OAuthMetadata } from "@augment-internal/sidecar-libs/src/tools/oauth-types";

/**
 * PKCE (Proof Key for Code Exchange) utilities for OAuth 2.0 flows
 * Based on RFC 7636: https://tools.ietf.org/html/rfc7636
 */

export interface PKCEPair {
  codeVerifier: string;
  codeChallenge: string;
}

/**
 * Generate a PKCE code verifier and challenge pair
 * @returns Object containing codeVerifier and codeChallenge
 */
export function generatePKCEPair(): PKCEPair {
  const codeVerifier = base64URLEncode(randomBytes(32));
  const codeChallenge = base64URLEncode(sha256(Buffer.from(codeVerifier)));

  return {
    codeVerifier,
    codeChallenge,
  };
}

async function dynamicallyRegisterClient(
  resourceMetadataEndpoint: string,
  clientName: string,
): Promise<string | undefined> {
  const metadataResponse = await fetch(resourceMetadataEndpoint);
  const metadata = (await metadataResponse.json()) as OAuthMetadata;
  const registrationEndpoint = metadata?.registration_endpoint;
  const registrationRequest = {
    client_name: "Augment Code",
    client_uri: "https://augmentcode.com",
    redirect_uris: [`vscode://augment.vscode-augment/auth/mcp/${clientName}`],
    grant_types: ["authorization_code"],
    response_types: ["code"],
    token_endpoint_auth_method: "none", // Public client using PKCE
    software_id: "augment-code-assistant",
    software_version: "1.0.0",
    // PKCE support indicators
    code_challenge_methods_supported: ["S256"],
  };

  if (typeof registrationEndpoint !== "string") {
    return undefined;
  }
  const registrationResponse = await fetch(registrationEndpoint, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Accept: "application/json",
      "User-Agent": "Augment-Code-Assistant/1.0.0",
    },
    body: JSON.stringify(registrationRequest),
  });

  const registrationResult = (await registrationResponse.json()) as Record<
    string,
    any
  >;
  const clientID = registrationResult?.client_id as string;

  return clientID;
}

export async function generateOAuthUrlForPartner(
  partner: REMOTE_PARTNER_MCP_SERVERS,
  partnerName: string,
  codeChallenge: string,
): Promise<string> {
  const result = await generateOAuthUrlAndClientIdForPartner(
    partner,
    partnerName,
    codeChallenge,
  );
  return result.url;
}

export async function generateOAuthUrlAndClientIdForPartner(
  partner: REMOTE_PARTNER_MCP_SERVERS,
  partnerName: string,
  codeChallenge: string,
): Promise<{ url: string; clientId: string }> {
  const partnerDetails = REMOTE_PARTNER_MCP_CONFIG[partner];
  const baseUrl = partnerDetails.authDefinition.baseUrl;
  const params = new URLSearchParams({
    ...partnerDetails.authDefinition.params,
    code_challenge: codeChallenge,
    code_challenge_method: "S256",
  });

  const clientID = await dynamicallyRegisterClient(
    partnerDetails.authDefinition.resourceMetadataUrl,
    partnerName,
  );
  if (clientID === undefined) {
    throw new Error("Failed to dynamically register client");
  }
  params.set("client_id", clientID);
  return {
    url: `${baseUrl}?${params.toString()}`,
    clientId: clientID,
  };
}

/**
 * Base64 URL encode a buffer
 * @param data Buffer to encode
 * @returns Base64 URL encoded string
 */
function base64URLEncode(data: Buffer): string {
  return data
    .toString("base64")
    .replace(/\+/g, "-")
    .replace(/\//g, "_")
    .replace(/=/g, "");
}

/**
 * SHA256 hash a buffer
 * @param buffer Buffer to hash
 * @returns SHA256 hash as buffer
 */
function sha256(buffer: Buffer): Buffer {
  return createHash("sha256").update(buffer).digest();
}
