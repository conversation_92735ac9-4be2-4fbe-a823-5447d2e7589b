/* eslint-disable @typescript-eslint/no-unsafe-assignment */
/**
 * Enhanced Status Matrix Implementation
 * Main API and orchestration for status matrix operations
 */

import * as git from "isomorphic-git";
import type { WalkerEntry } from "isomorphic-git";
import {
  type StatusMatrixOptions,
  type StatusMatrixResult,
  type StatusMatrix,
} from "./types";
import { createMetricsTracker, validateOptions, mergeOptions } from "./utils";
import { createHeadWalker, processStatusEntry } from "./core-algorithm";

// Default options for status matrix
const DEFAULT_STATUS_OPTIONS = {
  ref: "HEAD",
  filepaths: ["."],
  ignored: false,
  traverseSymlinks: false,
  traverseSubmodules: false,
  detectBinary: false,
} as const;

/**
 * Enhanced status matrix implementation optimized for performance
 * Follows isomorphic-git's exact algorithm with minimal overhead
 */
export async function enhancedStatusMatrix(
  userOptions: StatusMatrixOptions,
): Promise<StatusMatrixResult> {
  // Validate and merge options
  validateOptions(userOptions);
  const options = mergeOptions(userOptions);

  // Create metrics tracker
  const metricsTracker = createMetricsTracker();

  try {
    // Create walkers directly (simplified from reference implementation)
    const headWalker = await createHeadWalker(options);
    const workdirWalker = git.WORKDIR();
    const stageWalker = git.STAGE();

    // Use git.walk directly following the reference implementation pattern
    const results: (StatusMatrix[number] | null)[] = await git.walk({
      fs: options.fs,
      dir: options.dir,
      gitdir: options.gitdir,
      cache: options.cache,
      trees: [headWalker, workdirWalker, stageWalker],
      map: async function (
        filepath: string,
        [head, workdir, stage]: Array<WalkerEntry | null>,
      ) {
        return await processStatusEntry(
          filepath,
          head,
          workdir,
          stage,
          options,
          metricsTracker,
        );
      },
    });

    // Process results
    const statusMatrix: StatusMatrix = (results || []).filter(
      Boolean,
    ) as StatusMatrix;

    // Finalize metrics
    metricsTracker.finish();

    return {
      matrix: statusMatrix,
      metrics: metricsTracker.metrics,
    };
  } catch (error) {
    metricsTracker.finish();

    if (error instanceof Error) {
      throw new Error(`Status matrix error: ${error.message}`, {
        cause: error,
      });
    }
    throw error;
  }
}

/**
 * Simplified status matrix that matches isomorphic-git's exact interface
 * For drop-in replacement compatibility
 */
export async function statusMatrix(
  options: Pick<
    StatusMatrixOptions,
    | "fs"
    | "dir"
    | "gitdir"
    | "ref"
    | "filepaths"
    | "filter"
    | "cache"
    | "ignored"
  >,
): Promise<StatusMatrix> {
  const result = await enhancedStatusMatrix({
    ...DEFAULT_STATUS_OPTIONS,
    ...options,
  } as StatusMatrixOptions);

  return result.matrix;
}

// Re-export compareGitRefs from git-operations
export { compareGitRefs } from "./git-operations";
