import { TextDocument } from "vscode-languageserver-textdocument";
import {
  createConnection,
  InitializeParams,
  InitializeResult,
  TextDocuments,
} from "vscode-languageserver/node";
import { ChatMode as ProtoChatMode } from "$clients/sidecar/node-process/protos/tools_pb";

import { getLogger } from "./logging";
import { registerToolMethods } from "./tools/tool-methods";
import { InitializeParams as InitializeParamsProto } from "$clients/sidecar/node-process/protos/sidecarrpc_pb";
import { JsonValue } from "@bufbuild/protobuf";
import { clientFeatureFlags } from "./client-interfaces/client-feature-flags";
import { ClientWorkspaces } from "./client-interfaces/client-workspaces";
import { TextDocumentChangeEvent } from "@augment-internal/sidecar-libs/src/agent/agent-edit-types";
import { setLibraryClientWorkspaces } from "@augment-internal/sidecar-libs/src/client-interfaces/client-workspaces";
import { AggregateCheckpointManager } from "@augment-internal/sidecar-libs/src/agent/checkpoint/aggregate-checkpoint-manager";
import { TaskManager } from "@augment-internal/sidecar-libs/src/agent/task/task-manager";
import { FileBackedTaskStorage } from "@augment-internal/sidecar-libs/src/agent/task/task-storage";
import { RulesService } from "@augment-internal/sidecar-libs/src/chat/rules-service";
import { GitOperationsService } from "@augment-internal/sidecar-libs/src/git/git-operations-service";
import { setLibraryAPIClient } from "@augment-internal/sidecar-libs/src/client-interfaces/api-client";
import { APIClient } from "./client-interfaces/api-client";
import { registerWebviewMethods } from "./webviews/webview-methods";
import { registerRulesMethods } from "./rules/rules-methods";
import { setLibraryWebviewMessaging } from "@augment-internal/sidecar-libs/src/client-interfaces/webview-messaging";
import { WebviewMessaging } from "@augment-internal/sidecar-libs/src/webview-messages/webview-messaging";
import { ToolsModel } from "@augment-internal/sidecar-libs/src/tools/tools-model";
import { ExchangeManager } from "@augment-internal/sidecar-libs/src/exchange-storage/exchange-manager";
import { ToolUseStateManager } from "@augment-internal/sidecar-libs/src/tooluse-storage/tooluse-manager";
import { ClientTools } from "./tools/client-tools-host";
import { NodeProcessRemoteInfo } from "./tools/remote-info-source";
import { ChatMode } from "@augment-internal/sidecar-libs/src/chat/chat-types";
import { ToolApprovalConfigManager } from "@augment-internal/sidecar-libs/src/tools/approval-config/tool-approval-config-manager";
import {
  getPluginFileStore,
  setLibraryPluginFileStore,
} from "@augment-internal/sidecar-libs/src/client-interfaces/plugin-file-store";
import { PluginFileStore } from "./client-interfaces/plugin-file-store";
import {
  createDynamicLevelKvStore,
  setLibraryPluginKvStore,
} from "@augment-internal/sidecar-libs/src/client-interfaces/plugin-kv-store";

import { AgentShardStorage } from "@augment-internal/sidecar-libs/src/agent/agent-shard-storage";
import { setLibraryClientActions } from "@augment-internal/sidecar-libs/src/client-interfaces/client-actions";
import { ClientActions } from "./client-interfaces/client-actions";
import { setLibraryClientFeatureFlags } from "@augment-internal/sidecar-libs/src/client-interfaces/feature-flags";
import { setLibraryStateForSidecar } from "@augment-internal/sidecar-libs/src/client-interfaces/plugin-state";
import { PluginStateForSidecar } from "./client-interfaces/plugin-storage-for-sidecar";
import { ToolStartupError } from "@augment-internal/sidecar-libs/src/tools/tool-types";
import { SidecarToolType } from "@augment-internal/sidecar-libs/src/tools/sidecar-tools/sidecar-tool-types";
import path from "path";

// Initialize the logger
const logger = getLogger("server");
logger.info("\n\nStarting sidecar node process....");

process
  .on("unhandledRejection", (reason) => {
    logger.error(`Unhandled Rejection at Promise ${reason as string}`);
  })
  .on("uncaughtException", (err) => {
    logger.error(`Uncaught Exception thrown: ${err.message}`);
  });

const connection = createConnection();
const documents = new TextDocuments(TextDocument);

connection.onExit(() => {
  logger.info("Language server exiting");
});

connection.onInitialize((params: InitializeParams): InitializeResult => {
  logger.info(`Initializing Language Server: ${JSON.stringify(params)}`);

  let chatMode = ProtoChatMode.CHAT;
  let memoriesAbsPath: string | undefined = undefined;
  let projectAbsPath: string | undefined = undefined;

  try {
    const request = InitializeParamsProto.fromJson(
      params as unknown as JsonValue,
      {
        // This is needed so that @type fields from intellij are ignored
        ignoreUnknownFields: true,
      },
    );
    logger.info(
      `Initializing Language Server....parsed proto: ${request.toJsonString()}`,
    );

    if (request.capabilities?.featureFlags) {
      // NOTE: This is a bit of a hack to force the node-process to use the
      // str_replace_editor_tool since IntelliJ does not have any other
      // edit tool.
      request.capabilities.featureFlags.agentEditTool =
        "str_replace_editor_tool";
      clientFeatureFlags.updateFlags(request.capabilities.featureFlags);
    } else {
      logger.warn("Failed to get feature flags from initialize params");
    }

    if (request.capabilities?.initialState?.chatMode) {
      chatMode = request.capabilities.initialState.chatMode;
    }
    memoriesAbsPath = request.capabilities?.initialState?.memoriesAbsPath;
    projectAbsPath = request.capabilities?.initialState?.projectRootAbsPath;
  } catch (err: unknown) {
    logger.warn(
      `Failed to parse initialize params: ${err instanceof Error ? err.message : (err as string)}`,
    );
  }

  const result: InitializeResult = {
    capabilities: {},
  };

  // Initialize the client interfaces
  setLibraryPluginFileStore(new PluginFileStore(connection));

  if (!projectAbsPath) {
    logger.warn("No project path provided in initialize params");
  }
  const kvStorePath = projectAbsPath
    ? path.join(projectAbsPath, "augment-kv-store")
    : "./data/augment-kv-store";
  setLibraryPluginKvStore(createDynamicLevelKvStore(kvStorePath));
  setLibraryStateForSidecar(new PluginStateForSidecar(connection));
  setLibraryClientWorkspaces(new ClientWorkspaces(connection));
  setLibraryAPIClient(new APIClient(connection));
  setLibraryClientActions(new ClientActions(connection));
  setLibraryClientFeatureFlags(clientFeatureFlags);

  const checkpointManager = new AggregateCheckpointManager(
    new AgentShardStorage(),
    () => memoriesAbsPath,
    (_cb: (e: TextDocumentChangeEvent) => unknown) => {
      logger.warn(`documentChangeHandler not implemented yet`);
      return {
        dispose: () => {},
      };
    },
    () => ({ dispose: () => {} }),
    () => ({ dispose: () => {} }),
  );

  // Create a default user agent for the sidecar
  const userAgent = "Augment-Sidecar/1.0";

  const getAgentMemories = async () => {
    const pluginStore = getPluginFileStore();
    const assetPath =
      memoriesAbsPath?.split("/").pop() ?? "augment-memories.md";
    // log that we're looking up this abs path
    logger.info(`Loading memories from ${assetPath}`);
    const asset = await pluginStore.loadAsset(assetPath);
    if (asset) {
      return Buffer.from(asset).toString("utf8");
    }
    logger.warn(`No memories found`);
    return undefined;
  };

  // Create tool approval config manager
  const toolApprovalConfigManager = new ToolApprovalConfigManager(
    getPluginFileStore(),
  );

  // Initialize the task manager
  const taskManager = new TaskManager(new FileBackedTaskStorage());

  const toolsModel = new ToolsModel(
    [], // mcpServers
    (chatMode) => new ClientTools(chatMode), // _clientToolHostFactory
    new NodeProcessRemoteInfo(connection), // _remoteInfoSource
    (_details: ToolStartupError) => {}, // _mcpToolsStartupErrorFn
    clientFeatureFlags, // _clientFeatureFlags
    checkpointManager, // _checkpointManager
    getAgentMemories, // _getAgentMemories
    () => memoriesAbsPath, // _getMemoriesFilePath
    () => undefined, // _getToolUseRequestEventReporter
    toolApprovalConfigManager, // _toolApprovalConfigManager
    {
      userAgent,
      // NOTE: grep search is not supported on intellij yet
      unsupportedSidecarTools: new Set([SidecarToolType.grepSearch]),
    }, // _options
    taskManager, // _taskManager
  );
  logger.info(`Initial chat mode: ${chatMode}`);
  toolsModel.setMode(
    chatMode === ProtoChatMode.AGENT ? ChatMode.agent : ChatMode.chat,
  );

  // Initialize the exchange manager
  const exchangeManager = new ExchangeManager();

  // Initialize the tool use state manager
  const toolUseStateManager = new ToolUseStateManager();

  // Initialize the rules service
  const rulesService = new RulesService();

  // Initialize the git operations service
  const gitOperationsService = new GitOperationsService();

  // Initialize the webview messaging
  setLibraryWebviewMessaging(
    new WebviewMessaging(
      checkpointManager,
      toolsModel,
      taskManager,
      exchangeManager,
      toolUseStateManager,
      rulesService,
      gitOperationsService,
    ),
  );

  // Register LSP methods
  registerToolMethods(connection, toolsModel);
  registerWebviewMethods(connection);
  registerRulesMethods(connection, rulesService);

  return result;
});

connection.onInitialized(() => {
  logger.info("Language server initialized");
});

documents.listen(connection);
connection.listen();

logger.info("Language server started");
