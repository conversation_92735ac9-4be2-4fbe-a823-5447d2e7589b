/* eslint-disable @typescript-eslint/no-unsafe-member-access */

/* eslint-disable @typescript-eslint/no-unsafe-call */

/* eslint-disable @typescript-eslint/no-unsafe-assignment */
import { type AugmentLogger, getLogger } from "./logging";
import { MonitoredParameter } from "./monitored-parameter";
import { DisposableService } from "./utils/disposable-service";
import * as vscode from "./vscode";

export type CodeInstructionConfig = {
    model?: string;
};

const DEFAULT_MODEL_DISPLAY_NAME_TO_ID = {
    // eslint-disable-next-line @typescript-eslint/naming-convention
    Augment: null,
};

// Default OAuth client ID for the Vim extension.
// NOTE(mpauly): We use "v" rather than a more readable value to keep the auth url as short as possible
const DEFAULT_OAUTH_CLIENT_ID = "v";

export type ChatConfig = {
    url?: string;
    model?: string;
    modelDisplayNameToId?: {
        [key: string]: string | null;
    };
    stream?: boolean;
    useRichTextHistory?: boolean;
    smartPasteUsePrecomputation?: boolean;
    experimentalFullFilePaste?: boolean;
    enableEditableHistory?: boolean;
    userGuidelines?: string;
};

export type AgentConfig = {
    model?: string;
};

export type OAuthConfig = {
    clientID?: string;
    url?: string;
};

export type RecencySignalManagerConfig = {
    collectTabSwitchEvents: boolean;
};

export type NextEditConfig = {
    enabled?: boolean; // TODO is this deprecated too?
    // TODO: Deprecate and replace with augment.enableBackgroundSuggestions.
    backgroundEnabled: boolean;

    url?: string;
    locationUrl?: string;
    generationUrl?: string;

    model?: string;

    showInstructionTextbox: boolean;

    /** How long to wait before sending a new request. */
    useDebounceMs?: number;

    /** When enabled, use cursor decorations instead of bottom decorations. */
    useCursorDecorations?: boolean;

    useSmallHover?: boolean;

    noDiffMode?: boolean;
    animateNoDiffMode?: boolean;

    allowDuringDebugging?: boolean;

    /** Use mock results if $filename.next-edit-results.json5 exists. */
    useMockResults?: boolean;

    noDiffModeUseCodeLens?: boolean;

    showDiffByDefault?: boolean;
};

export type PreferenceCollectionConfig = {
    enable: boolean;
    enableRetrievalDataCollection: boolean;
    enableRandomizedMode: boolean;
};

export type SmartPasteConfig = {
    url?: string;
    model?: string;
};

export type InstructionsConfig = {
    model?: string;
};

export type IntegrationsConfig = {
    atlassian?: {
        serverUrl: string;
        personalApiToken: string;
        username: string;
    };
    notion?: {
        apiToken: string;
    };
    linear?: {
        apiToken: string;
    };
    github?: {
        apiToken: string;
    };
};

// NOTE: Prefer `enable*` for naming in config.
export type AugmentConfig = {
    apiToken: string;
    completionURL: string;
    codeInstruction: CodeInstructionConfig;
    chat: ChatConfig;
    agent: AgentConfig;

    modelName: string;
    enableUpload: boolean;
    enableShortcutsAboveSelectedText: boolean;
    shortcutsDisplayDelayMS: number;
    enableEmptyFileHint: boolean;
    // TODO(AU-5531): Use versions of these settings in the .nextEdit object.
    enableBackgroundSuggestions: boolean;
    enableGlobalBackgroundSuggestions: boolean;
    showAllBackgroundSuggestionLineHighlights: boolean;
    // END TODO(AU-5531).
    enableDataCollection: boolean;
    enableDebugFeatures: boolean;
    enableReviewerWorkflows: boolean;
    oauth: OAuthConfig;
    completions: {
        // User settings
        enableAutomaticCompletions: boolean;
        disableCompletionsByLanguage: Set<string>;
        enableQuickSuggestions: boolean;

        // Advanced
        timeoutMs: number;
        maxWaitMs: number;
        addIntelliSenseSuggestions: boolean;
        filterThreshold?: number;
    };
    openFileManagerV2: {
        enabled: boolean;
    };
    nextEdit: NextEditConfig;
    recencySignalManager: RecencySignalManagerConfig;
    preferenceCollection: PreferenceCollectionConfig;
    vcs: {
        watcherEnabled: boolean;
    };
    conflictingCodingAssistantCheck: boolean;
    smartPaste: SmartPasteConfig;
    instructions: InstructionsConfig;
    integrations: IntegrationsConfig;
};
export type AugmentConfigKey = keyof AugmentConfig;

export interface UserConfig {
    completions: {
        enableAutomaticCompletions: boolean;
        disableCompletionsByLanguage: Array<string>;
        enableQuickSuggestions: boolean;
    };

    enableShortcutsAboveSelectedText: boolean;
    shortcutsDisplayDelayMS: number;
    enableEmptyFileHint: boolean;
    enableBackgroundSuggestions: boolean;
    enableGlobalBackgroundSuggestions: boolean;
    showAllBackgroundSuggestionLineHighlights: boolean;
    conflictingCodingAssistantCheck: boolean;
    chat: {
        userGuidelines?: string;
    };
    advanced: {
        apiToken: string;
        completionURL: string;

        // The following are internal only
        oauth: OAuthConfig;
        model: string;
        codeInstruction: CodeInstructionConfig;
        chat: ChatConfig;
        agent: AgentConfig;

        enableDebugFeatures: boolean;
        enableWorkspaceUpload: boolean;
        enableReviewerWorkflows: boolean;
        completions: {
            timeoutMs: number;
            maxWaitMs: number;
            addIntelliSenseSuggestions: boolean;
            filterThreshold?: number;
        };
        openFileManagerV2: {
            enabled: boolean;
        };
        enableDataCollection: boolean;
        nextEditURL?: string;
        nextEditLocationURL?: string;
        nextEditGenerationURL?: string;
        nextEditBackgroundGeneration?: boolean;
        nextEdit: NextEditConfig;
        recencySignalManager: RecencySignalManagerConfig;
        preferenceCollection: PreferenceCollectionConfig;
        vcs: {
            watcherEnabled: boolean;
        };
        smartPaste?: SmartPasteConfig;
        instructions?: InstructionsConfig;
        integrations?: IntegrationsConfig;
    };
}

/**
 * AugmentConfigListener is a class that listens for changes to the extension
 * configuration. It logs configuration changes of interest and notifies listeners
 * when the config has changed. It is preferrable to listen to configuration changes
 * here than to use your own `onDidChangeConfiguration` event listener, as this class
 * will log configuration changes before notifying listeners.
 */
export class AugmentConfigListener extends DisposableService {
    private _config!: AugmentConfig;
    private _configChanged = new vscode.EventEmitter<ConfigChanges>();

    private _configMonitor: MonitoredParameter<AugmentConfig>;

    private readonly _logger: AugmentLogger = getLogger("AugmentConfigListener");

    constructor() {
        super();

        this._configMonitor = new MonitoredParameter<AugmentConfig>("Config", this._logger);

        this._refreshConfig();
        this.addDisposable(
            vscode.workspace.onDidChangeConfiguration(() => {
                return this._refreshConfig();
            })
        );
    }

    // `config` is the current configuration.
    get config(): Readonly<AugmentConfig> {
        return this._config;
    }

    // onDidChange is an event that clients can listen on to be notified of changes
    // to the extension configuration.
    get onDidChange(): vscode.Event<ConfigChanges> {
        return this._configChanged.event;
    }

    // _refreshConfig caches the current extension configuration and logs changes of
    // interest.
    private _refreshConfig() {
        const previousConfig = this._config;
        this._config = AugmentConfigListener.normalizeConfig(this._getUserConfig());
        if (this._configMonitor.update(this._config)) {
            this._configChanged.fire({
                previousConfig,
                newConfig: this._config,
            });
        }
    }

    // normalizeConfig converts UserConfig to an AugmentConfig.
    public static normalizeConfig(config: UserConfig): AugmentConfig {
        return {
            apiToken: config.advanced.apiToken,
            completionURL: config.advanced.completionURL,
            modelName: config.advanced.model,
            conflictingCodingAssistantCheck: config.conflictingCodingAssistantCheck,
            codeInstruction: {
                model: config.advanced.codeInstruction.model || undefined,
            },
            chat: {
                url: config.advanced.chat.url || undefined,
                model: config.advanced.chat.model || undefined,
                stream: config.advanced.chat.stream ?? undefined,
                enableEditableHistory: config.advanced.chat.enableEditableHistory ?? false,
                useRichTextHistory: config.advanced.chat.useRichTextHistory ?? true,
                smartPasteUsePrecomputation:
                    config.advanced.chat.smartPasteUsePrecomputation ?? true,
                experimentalFullFilePaste: config.advanced.chat.experimentalFullFilePaste ?? false,
                modelDisplayNameToId:
                    config.advanced.chat.modelDisplayNameToId || DEFAULT_MODEL_DISPLAY_NAME_TO_ID,
                userGuidelines: config.chat.userGuidelines || "",
            },
            agent: {
                model: config.advanced.agent?.model || undefined,
            },

            oauth: {
                clientID: config.advanced.oauth.clientID || DEFAULT_OAUTH_CLIENT_ID,
                url: config.advanced.oauth.url || "https://auth.augmentcode.com",
            },
            enableUpload: config.advanced.enableWorkspaceUpload,
            enableShortcutsAboveSelectedText: config.enableShortcutsAboveSelectedText,
            shortcutsDisplayDelayMS: config.shortcutsDisplayDelayMS,
            enableEmptyFileHint: config.enableEmptyFileHint,
            enableBackgroundSuggestions: config.enableBackgroundSuggestions,
            enableGlobalBackgroundSuggestions: config.enableGlobalBackgroundSuggestions,
            showAllBackgroundSuggestionLineHighlights:
                config.showAllBackgroundSuggestionLineHighlights,
            enableDataCollection: config.advanced.enableDataCollection,
            enableDebugFeatures: config.advanced.enableDebugFeatures,
            enableReviewerWorkflows: config.advanced.enableReviewerWorkflows,
            completions: {
                enableAutomaticCompletions: config.completions.enableAutomaticCompletions,
                disableCompletionsByLanguage: new Set(
                    config.completions.disableCompletionsByLanguage
                ),
                enableQuickSuggestions: config.completions.enableQuickSuggestions,

                timeoutMs: config.advanced.completions.timeoutMs,
                maxWaitMs: config.advanced.completions.maxWaitMs,
                addIntelliSenseSuggestions: config.advanced.completions.addIntelliSenseSuggestions,
                filterThreshold: config.advanced.completions.filterThreshold,
            },
            openFileManagerV2: {
                enabled: config.advanced.openFileManagerV2.enabled,
            },
            nextEdit: {
                enabled: config.advanced.nextEdit.enabled,
                backgroundEnabled: config.advanced.nextEdit.backgroundEnabled,

                url: config.advanced.nextEdit.url,
                locationUrl: config.advanced.nextEdit.locationUrl || config.advanced.nextEdit.url,
                generationUrl:
                    config.advanced.nextEdit.generationUrl || config.advanced.nextEdit.url,
                showInstructionTextbox: config.advanced.nextEdit.showInstructionTextbox,
                model: config.advanced.nextEdit.model,
                useDebounceMs: config.advanced.nextEdit.useDebounceMs,
                useCursorDecorations: config.advanced.nextEdit.useCursorDecorations,
                useSmallHover: config.advanced.nextEdit.useSmallHover,
                noDiffMode: config.advanced.nextEdit.noDiffMode,
                animateNoDiffMode: config.advanced.nextEdit.animateNoDiffMode,
                allowDuringDebugging: config.advanced.nextEdit.allowDuringDebugging,
                useMockResults: config.advanced.nextEdit.useMockResults,
                noDiffModeUseCodeLens: config.advanced.nextEdit.noDiffModeUseCodeLens,
                showDiffByDefault: config.advanced.nextEdit.showDiffByDefault,
            },
            recencySignalManager: config.advanced.recencySignalManager,
            preferenceCollection: {
                enable: config.advanced.preferenceCollection.enable,
                enableRetrievalDataCollection:
                    config.advanced.preferenceCollection.enableRetrievalDataCollection,
                enableRandomizedMode: config.advanced.preferenceCollection.enableRandomizedMode,
            },
            vcs: {
                watcherEnabled: config.advanced.vcs.watcherEnabled,
            },
            smartPaste: {
                url: config.advanced.smartPaste?.url,
                model: config.advanced.smartPaste?.model,
            },
            instructions: {
                model: config.advanced.instructions?.model,
            },
            integrations: {
                ...config.advanced.integrations,
            },
        };
    }

    private _getUserConfig(): UserConfig {
        const config = vscode.workspace.getConfiguration("augment");
        return AugmentConfigListener.normalizeUserConfig(config);
    }

    public static normalizeUserConfig(config: vscode.WorkspaceConfiguration): UserConfig {
        return {
            completions: {
                enableAutomaticCompletions: booleanWithDefault(
                    config.enableAutomaticCompletions ??
                        config.completions?.enableAutomaticCompletions,
                    true
                ),
                disableCompletionsByLanguage:
                    config.disableCompletionsByLanguage ||
                    config.completions?.disableCompletionsByLanguage ||
                    [],
                enableQuickSuggestions: booleanWithDefault(
                    config.completions?.enableQuickSuggestions,
                    true
                ),
            },
            chat: {
                userGuidelines: config.chat?.userGuidelines || "",
            },
            enableShortcutsAboveSelectedText: booleanWithDefault(
                config.enableShortcutsAboveSelectedText,
                false
            ),
            shortcutsDisplayDelayMS: intWithDefault(config.shortcutsDisplayDelayMS, 2000),
            enableEmptyFileHint: booleanWithDefault(config.enableEmptyFileHint, true),
            enableBackgroundSuggestions: booleanWithDefault(
                config.enableBackgroundSuggestions,
                true
            ),
            enableGlobalBackgroundSuggestions: booleanWithDefault(
                config.enableGlobalBackgroundSuggestions,
                false
            ),
            showAllBackgroundSuggestionLineHighlights: booleanWithDefault(
                // Note(rich): removing the more complicated logic here,
                // since it's not needed yet.
                !config.advanced?.nextEdit?.noDiffMode,
                !config.advanced?.nextEdit?.noDiffMode
            ),
            conflictingCodingAssistantCheck: booleanWithDefault(
                config.conflictingCodingAssistantCheck,
                true
            ),
            advanced: {
                // These options were moved from top level to advanced, so
                // check both places.
                apiToken: (config.advanced?.apiToken || config.apiToken || "").trim().toUpperCase(),
                completionURL: (
                    config.advanced?.completionURL ||
                    config.completionURL ||
                    ""
                ).trim(),

                // Internal settings
                enableWorkspaceUpload: booleanWithDefault(
                    config.advanced?.enableWorkspaceUpload,
                    true
                ),

                model: config.advanced?.model || "",

                enableDebugFeatures: booleanWithDefault(
                    config.advanced?.enableDebugFeatures,
                    false
                ),

                enableReviewerWorkflows: booleanWithDefault(
                    config.advanced?.enableReviewerWorkflows,
                    false
                ),
                enableDataCollection: booleanWithDefault(
                    config.advanced?.enableDataCollection,
                    false
                ),

                codeInstruction: {
                    model: config.advanced?.codeInstruction?.model || undefined,
                },

                chat: {
                    url: config.advanced?.chat?.url || undefined,
                    model: config.advanced?.chat?.model || undefined,
                    stream: config.advanced?.chat?.stream ?? undefined,
                    enableEditableHistory: config.advanced?.chat?.enableEditableHistory,
                    useRichTextHistory: config.advanced?.chat?.useRichTextHistory,
                    smartPasteUsePrecomputation: config.advanced?.chat?.smartPasteUsePrecomputation,
                    modelDisplayNameToId: config.advanced?.chat?.modelDisplayNameToId,
                    experimentalFullFilePaste: config.advanced?.chat?.experimentalFullFilePaste,
                },

                agent: {
                    model: config.advanced?.agent?.model || undefined,
                },

                oauth: {
                    clientID: config.advanced?.oauth?.clientID,
                    url: config.advanced?.oauth?.url,
                },

                completions: {
                    timeoutMs: config.advanced?.completions?.timeoutMs ?? 800,
                    maxWaitMs: config.advanced?.completions?.maxWaitMs ?? 1600,
                    addIntelliSenseSuggestions:
                        config.advanced?.completions?.addIntelliSenseSuggestions ?? true,
                    filterThreshold: config.advanced?.completions?.filter_threshold ?? undefined,
                },
                openFileManagerV2: {
                    enabled: booleanWithDefault(config.advanced?.openFileManager?.useV2, false),
                },
                nextEdit: {
                    enabled: config.advanced?.nextEdit?.enabled,
                    backgroundEnabled: booleanWithDefault(
                        config.advanced?.nextEdit?.backgroundEnabled,
                        true
                    ),

                    url: config.advanced?.nextEdit?.url,
                    locationUrl: config.advanced?.nextEdit?.locationUrl,
                    generationUrl: config.advanced?.nextEdit?.generationUrl,
                    showInstructionTextbox: booleanWithDefault(
                        config.advanced?.nextEdit?.showInstructionTextbox,
                        false
                    ),
                    model: config.advanced?.nextEdit?.model,
                    useDebounceMs: config.advanced?.nextEdit?.useDebounceMs,
                    useCursorDecorations: booleanWithDefault(
                        config.advanced?.nextEdit?.useCursorDecorations,
                        false
                    ),
                    useSmallHover: booleanWithDefault(
                        config.advanced?.nextEdit?.useSmallHover,
                        true
                    ),
                    allowDuringDebugging: booleanWithDefault(
                        config.advanced?.nextEdit?.allowDuringDebugging,
                        false
                    ),
                    useMockResults: booleanWithDefault(
                        config.advanced?.nextEdit?.useMockResults,
                        false
                    ),
                    noDiffModeUseCodeLens: booleanWithDefault(
                        config.advanced?.nextEdit?.noDiffModeUseCodeLens,
                        false
                    ),
                },

                recencySignalManager: {
                    collectTabSwitchEvents:
                        config.advanced?.recencySignalManager?.collectTabSwitchEvents ?? false,
                },
                preferenceCollection: {
                    enable: booleanWithDefault(
                        config.advanced?.preferenceCollection?.enable,
                        false
                    ),
                    enableRetrievalDataCollection: booleanWithDefault(
                        config.advanced?.preferenceCollection?.enableRetrievalDataCollection,
                        false
                    ),
                    enableRandomizedMode: booleanWithDefault(
                        config.advanced?.preferenceCollection?.enableRandomizedMode,
                        true
                    ),
                },
                vcs: {
                    watcherEnabled: booleanWithDefault(config.advanced?.vcs?.watcherEnabled, false),
                },
                smartPaste: {
                    url: config.advanced?.smartPaste?.url,
                    model: config.advanced?.smartPaste?.model,
                },
                instructions: {
                    model: config.advanced?.instructions?.model,
                },
                integrations: {
                    ...config.advanced?.integrations,
                },
            },
        };
    }
}

// This is needed for settings that are internal and VSCode won't provide
// defaults from package.json.
function booleanWithDefault(value: any, valueDefault: boolean): boolean {
    if (value === undefined || value === null) {
        return valueDefault;
    }
    if (typeof value === "string") {
        return value.toLowerCase() !== "false";
    }
    return !!value;
}

function intWithDefault(value: any, valueDefault: number): number {
    if (value === undefined || value === null) {
        return valueDefault;
    }
    if (typeof value === "string") {
        return parseInt(value);
    }
    // eslint-disable-next-line @typescript-eslint/no-unsafe-return
    return value;
}

export type ConfigChanges = {
    previousConfig: AugmentConfig;
    newConfig: AugmentConfig;
};
