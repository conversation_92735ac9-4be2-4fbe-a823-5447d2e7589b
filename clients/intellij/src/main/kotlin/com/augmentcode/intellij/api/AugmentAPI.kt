package com.augmentcode.intellij.api

import com.augmentcode.api.*
import com.augmentcode.intellij.AugmentBundle
import com.augmentcode.intellij.auth.AugmentOAuthState
import com.augmentcode.intellij.settings.AugmentSettings
import com.augmentcode.intellij.utils.SequenceId
import com.augmentcode.rpc.AugmentTypes
import com.augmentcode.rpc.ChatInstructionStreamResult
import com.augmentcode.rpc.ChatModelReplyError
import com.augmentcode.rpc.ChatResult
import com.google.gson.FieldNamingPolicy
import com.google.gson.GsonBuilder
import com.google.protobuf.TypeRegistry
import com.google.protobuf.util.JsonFormat
import com.intellij.openapi.components.service
import com.intellij.openapi.diagnostic.thisLogger
import com.jetbrains.rd.util.UUID
import io.ktor.client.statement.*
import io.ktor.utils.io.*
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import org.jetbrains.annotations.VisibleForTesting
import public_api.PublicApi.BatchUploadRequest
import public_api.PublicApi.BatchUploadResponse
import public_api.PublicApi.CompletionRequest
import public_api.PublicApi.CompletionResponse
import public_api.PublicApi.GetModelsResponse
import public_api.PublicApi.PreferenceSample
import java.util.concurrent.ConcurrentHashMap

data class ConnectionDetails(
  val baseURL: String?,
  val token: String?,
)

sealed class CheckpointResult {
  data class Success(val response: CheckpointBlobsResponse) : CheckpointResult()

  object NeedsReset : CheckpointResult()

  data class OtherFailure(val exception: Exception) : CheckpointResult()
}

const val DEFAULT_PREFIX_SIZE = 4096
const val DEFAULT_SUFFIX_SIZE = 4096

internal const val DEFUALT_STREAM_TIMEOUT_MS = 30_000L
internal const val CHAT_STREAM_TIMEOUT_MS = 300_000L
internal const val MODEL_INFO_TTL_HOURS = 1L

class AugmentAPI() {
  companion object {
    private val logger = thisLogger()

    val instance: AugmentAPI
      get() = service()

    const val CLIENT_NAME = "intellij"
  }

  private val httpClient = AugmentHttpClient()
  private val sequenceId = SequenceId()

  private val modelCache = ConcurrentHashMap<String, GetModelsResponse?>()
  private val modelCacheMutex = Mutex()

  // use gson instead of Ktor's content negotiation because of conflicts in classpath
  // and not working serialization compiler plugin
  private val gson =
    GsonBuilder()
      .setFieldNamingPolicy(FieldNamingPolicy.LOWER_CASE_WITH_UNDERSCORES)
      .create()

  val jsonParser =
    JsonFormat
      .parser()
      .usingTypeRegistry(
        TypeRegistry
          .newBuilder()
          .add(AugmentTypes.getDescriptor().messageTypes)
          .build(),
      ).ignoringUnknownFields()

  fun availableBlocking(): Boolean = runBlocking { available() }

  suspend fun available(): Boolean {
    if (!AugmentSettings.instance.apiToken.isNullOrBlank()) {
      return !AugmentSettings.instance.completionURL.isNullOrBlank()
    }
    val credentials = AugmentOAuthState.instance.getCredentials()
    return credentials != null
  }

  fun nextSequenceId(): Int {
    return sequenceId.next()
  }

  @VisibleForTesting
  fun reset() {
    sequenceId.reset()
  }

  suspend fun complete(completionRequest: CompletionRequest): Pair<String, Result<CompletionResponse>> {
    val requestId = UUID.randomUUID().toString()
    val resp = httpClient.post("completion", completionRequest, requestID = requestId)
    if (resp.status.value !in 200..299) {
      return requestId to Result.failure(AugmentAPIException("completion", resp.status.value, resp.status.description))
    }

    val completionResponseBuilder = CompletionResponse.newBuilder()
    val body = resp.bodyAsText()
    try {
      jsonParser.merge(body, completionResponseBuilder)
      val completionResponse = completionResponseBuilder.build()
      return requestId to Result.success(completionResponse)
    } catch (e: com.google.protobuf.InvalidProtocolBufferException) {
      return requestId to Result.failure(e)
    }
  }

  suspend fun batchUpload(uploadRequest: BatchUploadRequest): Result<BatchUploadResponse> {
    if (uploadRequest.blobsList.isEmpty()) {
      return Result.success(BatchUploadResponse.newBuilder().build())
    }
    val resp = httpClient.post("batch-upload", uploadRequest)
    if (resp.status.value in 200..299) {
      val body = resp.bodyAsText()
      return bodyIntoProto(body, BatchUploadResponse.newBuilder())
    }
    return Result.failure(AugmentAPIException("batch-upload", resp.status.value, resp.status.description))
  }

  suspend fun findMissing(findRequest: FindMissingRequest): FindMissingResponse =
    findMissingResult(findRequest).getOrNull() ?: FindMissingResponse()

  suspend fun findMissingResult(findRequest: FindMissingRequest): Result<FindMissingResponse> {
    val resp = httpClient.post("find-missing", findRequest)
    if (resp.status.value in 200..299) {
      val completionBody = resp.bodyAsText()
      return Result.success(
        gson.fromJson(completionBody, FindMissingResponse::class.java),
      )
    }
    return Result.failure(AugmentAPIException("find-missing", resp.status.value, resp.status.description))
  }

  suspend fun resolveCompletions(resolveRequest: ResolveCompletionsRequest) {
    httpClient.post("resolve-completions", resolveRequest)
  }

  suspend fun token(
    tenantURL: String,
    tokenRequest: AuthTokenRequest,
  ): AuthTokenResult? {
    val resp = httpClient.post("token", tokenRequest, serverBaseURL = tenantURL)
    if (resp.status.value in 200..299) {
      val body = resp.bodyAsText()
      return gson.fromJson(body, AuthTokenResult::class.java)
    }
    return null
  }

  @VisibleForTesting
  fun clearModelCache() {
    modelCache.clear()
  }

  /**
   * Fetches the model config from the server if it is not already cached.
   * Multiple calls to this method while the network call is in flight will not result in
   * multiple network calls.
   */
  @OptIn(DelicateCoroutinesApi::class)
  @Deprecated("Use Augment<App|Project>StateService to get model info.")
  suspend fun fetchModelInfo(): GetModelsResponse? {
    val credentials = AugmentOAuthState.instance.getCredentials()
    val cacheKey =
      AugmentSettings.instance.let {
        listOfNotNull(it.completionURL, it.apiToken, credentials?.tenantURL, credentials?.accessToken).joinToString("-")
      }
    if (cacheKey.isBlank()) {
      return null
    }
    val cachedModel = modelCache.get(cacheKey)
    if (cachedModel != null) {
      return cachedModel
    }

    // there might a race here if a user changes the settings while this is running
    // but chances are so low that it's not worth the complexity
    return modelCacheMutex.withLock {
      // double check within the lock
      modelCache.get(cacheKey)?.let { return@withLock it }

      val freshModelInfo =
        try {
          modelInfoImpl().also {
            val model = it?.findDefaultModel()
            val debugInfo =
              listOf(
                "model.default=${it?.defaultModel}",
                "model.default.context=${model?.suggestedPrefixCharCount}-${model?.suggestedSuffixCharCount}",
                "features.intellijForceEditsMinVersion=${it?.featureFlags?.intellijForceCompletionMinVersion}",
                "features.bypassLanguageFilter=${it?.featureFlags?.bypassLanguageFilter}",
                "features.maxUploadSizeBytes=${it?.featureFlags?.maxUploadSizeBytes}",
              )
            logger.info("Model info refreshed: ${debugInfo.joinToString()}")
          }
        } catch (e: IllegalStateException) {
          logger.warn("Failed to fetch model info", e)
          null
        }
      modelCache[cacheKey] = freshModelInfo ?: return@withLock null

      freshModelInfo
    }
  }

  suspend fun fetchModelInfoNoCache(): GetModelsResponse? {
    return modelInfoImpl()
  }

  suspend fun createCheckpoint(request: CheckpointBlobsRequest): CheckpointResult {
    val resp = httpClient.post("checkpoint-blobs", request)
    return when (resp.status.value) {
      in 200..299 -> {
        val checkpointBody = resp.bodyAsText()
        CheckpointResult.Success(gson.fromJson(checkpointBody, CheckpointBlobsResponse::class.java))
      }

      400, 404 -> {
        logger.warn("Checkpoint not found or invalid, indicating reset needed")
        CheckpointResult.NeedsReset
      }

      else ->
        CheckpointResult.OtherFailure(
          AugmentAPIException(
            "checkpoint-blobs",
            resp.status.value,
            resp.status.description,
          ),
        )
    }
  }

  private suspend fun modelInfoImpl(): GetModelsResponse? {
    val emptyObject = object {}
    val resp = httpClient.post("get-models", emptyObject)
    if (resp.status.value !in 200..299) {
      return null
    }

    val body = resp.bodyAsText()
    try {
      val getModelsResponseBuilder = GetModelsResponse.newBuilder()
      jsonParser.merge(body, getModelsResponseBuilder)
      return getModelsResponseBuilder.build()
    } catch (e: com.google.protobuf.InvalidProtocolBufferException) {
      logger.warn("Failed to parse /get-models response to protobuf type: ${e.message}")
      return null
    }
  }

  suspend fun chat(
    request: ChatRequest,
    requestID: String? = null,
  ): Pair<String, Flow<ChatResult>> {
    val (requestId, flowOfResults) = chatRawResponse(request, requestID)
    return requestId to
      flowOfResults.map { result ->
        if (result.isFailure) {
          val exception = result.exceptionOrNull()!!
          if (exception is AugmentAPIException) {
            if (exception.statusCode == 413) {
              val resultBuilder = ChatResult.newBuilder()
              resultBuilder.setError(
                ChatModelReplyError.newBuilder()
                  .setDisplayErrorMessage(AugmentBundle.message("chat.message.too.large"))
                  .build(),
              )
              return@map resultBuilder.build()
            } else if (exception.statusCode == 429 || (exception.statusCode >= 500 && exception.statusCode < 600)) {
              // If the status is 429 (resource exhausted) or an "unavailable" 5xx error, then
              // we want to show the error and make it as retriable.
              val resultBuilder = ChatResult.newBuilder()
              resultBuilder.setError(
                ChatModelReplyError.newBuilder()
                  .setDisplayErrorMessage(exception.message!!)
                  .setIsRetriable(true)
                  .build(),
              )
              return@map resultBuilder.build()
            }
          } else {
            // If none of the above apply, then it's a non-retriable error.
            val resultBuilder = ChatResult.newBuilder()
            resultBuilder.setError(
              ChatModelReplyError.newBuilder()
                .setDisplayErrorMessage(exception.message!!)
                .build(),
            )
            return@map resultBuilder.build()
          }
        }
        val resultBuilder = ChatResult.newBuilder()
        jsonParser.merge(result.getOrNull()!!, resultBuilder)
        return@map resultBuilder.build()
      }
  }

  suspend fun chatRawResponse(
    request: ChatRequest,
    requestID: String? = null,
  ): Pair<String, Flow<Result<String>>> {
    val (requestId, flowOfResults) =
      streamAndParse(
        "chat-stream",
        request,
        CHAT_STREAM_TIMEOUT_MS,
        requestID = requestID ?: UUID.randomUUID().toString(),
      )
    return requestId to flowOfResults
  }

  suspend fun smartPaste(request: ChatInstructionStreamPayload): Pair<String, Flow<ChatInstructionStreamResult>> {
    val (requestId, flowOfResults) = streamAndParse("smart-paste-stream", request)
    return requestId to
      flowOfResults.map { result ->
        if (result.isFailure) {
          throw result.exceptionOrNull()!!
        }
        val resultBuilder = ChatInstructionStreamResult.newBuilder()
        jsonParser.merge(result.getOrNull(), resultBuilder)
        return@map resultBuilder.build()
      }
  }

  private suspend fun <R> streamAndParse(
    endpoint: String,
    request: R,
    timeoutMs: Long = DEFUALT_STREAM_TIMEOUT_MS,
    requestID: String = UUID.randomUUID().toString(),
  ): Pair<String, Flow<Result<String>>> {
    return requestID to
      flow {
        httpClient.stream(endpoint, request, requestID = requestID, timeoutMs = timeoutMs, handleStream = { resp ->
          when (resp.status.value) {
            in 200..299 -> Unit // continue
            else -> {
              emit(Result.failure(AugmentAPIException(endpoint, resp.status.value, resp.status.description)))
              return@stream
            }
          }

          val channel: ByteReadChannel = resp.bodyAsChannel()
          // await for the first few bytes and then start timing chunk receiving
          channel.awaitContent()
          while (!channel.isClosedForRead) {
            val line =
              try {
                withTimeout(timeoutMs) {
                  channel.readUTF8Line()
                }
              } catch (_: TimeoutCancellationException) {
                throw IllegalStateException("$endpoint experienced network issue")
              }
            if (!line.isNullOrBlank()) {
              emit(Result.success(line))
            }
          }
        })
      }
  }

  suspend fun chatFeedback(request: ChatFeedbackRequest): ChatFeedbackResponse? {
    val resp = httpClient.post("chat-feedback", request)
    if (resp.status.value in 200..299) {
      val checkpointBody = resp.bodyAsText()
      return gson.fromJson(checkpointBody, ChatFeedbackResponse::class.java)
    }
    return null
  }

  suspend fun completionFeedback(request: CompletionFeedbackRequest): CompletionFeedbackResponse? {
    val resp = httpClient.post("completion-feedback", request)
    if (resp.status.value in 200..299) {
      val checkpointBody = resp.bodyAsText()
      return gson.fromJson(checkpointBody, CompletionFeedbackResponse::class.java)
    } else {
      logger.warn("Failed to send completion feedback: ${resp.status.value} ${resp.status.description}")
    }
    return null
  }

  suspend fun saveChat(request: SaveChatRequest): SaveChatResponse? {
    val resp = httpClient.post("save-chat", request)
    if (resp.status.value in 200..299) {
      val body = resp.bodyAsText()
      return gson.fromJson(body, SaveChatResponse::class.java)
    }
    return null
  }

  suspend fun clientMetrics(metrics: List<ClientMetric>) {
    val request =
      ClientMetricsRequest().apply {
        this.metrics =
          metrics.map {
            ClientMetric().apply {
              this.clientMetric = it.clientMetric
              this.value = it.value
            }
          }
      }

    val resp = httpClient.post("client-metrics", request)
    if (resp.status.value in 200..299) {
      logger.info("Metrics sent successfully")
    } else {
      logger.warn("Failed to send metrics: ${resp.status.value} ${resp.status.description}")
    }
  }

  suspend fun onboardingSessionEvents(sessionEvents: List<OnboardingSessionEvent>) {
    val request =
      object {
        // wrap request with object so that when it gets turned into JSON we have {events: [...]}
        val events =
          sessionEvents.map {
            OnboardingSessionEvent().apply {
              this.event_name = it.event_name
              this.event_time_sec = it.event_time_sec
              this.event_time_nsec = it.event_time_nsec
            }
          }
      }

    val resp = httpClient.post("record-onboarding-session-event", request)
    if (resp.status.value in 200..299) {
      logger.info("Onboarding session events sent successfully")
    } else {
      logger.warn("Failed to send onboarding session events: ${resp.status.value} ${resp.status.description}")
    }
  }

  suspend fun searchExternalSources(
    query: String,
    sourceTypes: List<String>,
  ): SearchExternalSourcesResponse? {
    val request =
      SearchExternalSourcesRequest().apply {
        this.query = query
        this.sourceTypes = sourceTypes.toTypedArray()
      }
    val resp = httpClient.post("search-external-sources", request)
    if (resp.status.value in 200..299) {
      val sourcesBody = resp.bodyAsText()
      return gson.fromJson(sourcesBody, SearchExternalSourcesResponse::class.java)
    }
    return null
  }

  suspend fun listRemoteTools(remoteToolIDs: List<Int>): ListRemoteToolsResult {
    val resp =
      httpClient.post(
        "agents/list-remote-tools",
        ListRemoteToolsRequest().apply {
          tool_id_list =
            ToolIDList().apply {
              tool_ids = remoteToolIDs.toSet()
            }
        },
      )
    if (resp.status.value in 200..299) {
      val toolsBody = resp.bodyAsText()
      return gson.fromJson(toolsBody, ListRemoteToolsResult::class.java)
    }
    return ListRemoteToolsResult()
  }

  suspend fun runRemoteTool(
    requestId: String,
    toolName: String,
    toolInputJson: String,
    toolId: RemoteToolId,
    extraToolInput: ExtraToolInput?,
  ): RunRemoteToolResult {
    val toolRequest =
      RunRemoteToolRequest().apply {
        tool_name = toolName
        tool_input_json = toolInputJson
        tool_id = toolId.value
      }
    if (extraToolInput != null) {
      when (toolId) {
        RemoteToolId.Jira,
        RemoteToolId.Confluence,
        -> {
          toolRequest.extra_tool_input =
            RemoteToolsInput().apply {
              atlassian_tool_extra_input = extraToolInput as AtlassianToolExtraInput
            }
        }

        RemoteToolId.Notion,
        -> {
          toolRequest.extra_tool_input =
            RemoteToolsInput().apply {
              notion_tool_extra_input = extraToolInput as NotionToolExtraInput
            }
        }

        RemoteToolId.Linear,
        -> {
          toolRequest.extra_tool_input =
            RemoteToolsInput().apply {
              linear_tool_extra_input = extraToolInput as LinearToolExtraInput
            }
        }

        RemoteToolId.GitHubApi -> {
          toolRequest.extra_tool_input =
            RemoteToolsInput().apply {
              github_tool_extra_input = extraToolInput as GitHubToolExtraInput
            }
        }

        else -> { // do nothing
        }
      }
    }

    val resp =
      httpClient.post(
        "agents/run-remote-tool",
        toolRequest,
        requestID = requestId,
      )
    if (resp.status.value in 200..299) {
      val resultBody = resp.bodyAsText()
      return gson.fromJson(resultBody, RunRemoteToolResult::class.java)
    }
    throw IllegalStateException("Failed to run remote tool: ${resp.status.value} ${resp.status.description}")
  }

  suspend fun revokeToolAccess(toolId: RemoteToolId): RevokeToolAccessResult {
    val resp = httpClient.post("agents/revoke-tool-access", RevokeToolAccessRequest(toolId))
    if (resp.status.value in 200..299) {
      logger.info("Tool revoked successfully")
      val resultBody = resp.bodyAsText()
      return gson.fromJson(resultBody, RevokeToolAccessResult::class.java)
    }
    logger.warn("Failed to revoke tool: ${resp.status.value} ${resp.status.description}")
    throw IllegalStateException("Failed to revoke tool: ${resp.status.value} ${resp.status.description}")
  }

  suspend fun checkToolSafety(
    toolId: Int,
    toolInputJson: String,
  ): CheckToolSafetyResponse {
    val resp =
      httpClient.post(
        "agents/check-tool-safety",
        CheckToolSafety().apply {
          tool_id = toolId
          tool_input_json = toolInputJson
        },
      )
    if (resp.status.value in 200..299) {
      val resultBody = resp.bodyAsText()
      return gson.fromJson(resultBody, CheckToolSafetyResponse::class.java)
    }
    throw IllegalStateException("Failed to run remote tool: ${resp.status.value} ${resp.status.description}")
  }

  suspend fun reportSmartPasteResolution(resolution: SmartPasteResolution) {
    val resp = httpClient.post("resolve-smart-paste", resolution)
    if (resp.status.value in 200..299) {
      logger.info("Smart paste resolution sent successfully")
    } else {
      logger.warn("Failed to send smart paste resolution: ${resp.status.value} ${resp.status.description}")
    }
  }

  suspend fun reportError(request: ReportErrorRequest) {
    try {
      val resp = httpClient.post("report-error", request)
      if (resp.status.value !in 200..299) {
        logger.warn("Failed to report error: ${resp.status.value} ${resp.status.description}")
      }
    } catch (e: Exception) {
      logger.warn("Failed to report error: ${e.message}, dropping error report")
    }
  }

  suspend fun agentCodebaseRetrieval(request: AgentCodebaseRetrievalRequest): AgentCodebaseRetrievalResponse {
    val resp = httpClient.post("agents/codebase-retrieval", request)
    if (resp.status.value in 200..299) {
      val sourcesBody = resp.bodyAsText()
      return gson.fromJson(sourcesBody, AgentCodebaseRetrievalResponse::class.java)
    }
    logger.warn("Failed to retrieve codebase: ${resp.status.value} ${resp.status.description}")
    throw IllegalStateException("Failed to retrieve codebase with status ${resp.status.value} ${resp.status.description}")
  }

  suspend fun recordRequestEvents(
    requestId: String,
    request: RecordAgentRequestBody,
  ) {
    val resp = httpClient.post("record-request-events", request, requestID = requestId)
    if (resp.status.value in 200..299) {
      // NOOP
    } else {
      logger.warn("Failed to record request events: ${resp.status.value} ${resp.status.description}")
    }
  }

  suspend fun recordSessionEvents(request: RecordAgentSessionBody) {
    val resp = httpClient.post("record-session-events", request)
    if (resp.status.value in 200..299) {
      // NOOP
    } else {
      logger.warn("Failed to record session events: ${resp.status.value} ${resp.status.description}")
    }
  }

  suspend fun recordPreferenceSample(sample: PreferenceSample): Result<Unit> {
    val resp = httpClient.post("record-preference-sample", sample)
    if (resp.status.value in 200..299) {
      return Result.success(Unit)
    } else {
      return Result.failure(AugmentAPIException("record-preference-sample", resp.status.value, resp.status.description))
    }
  }

  suspend fun getSubscriptionInfo(): Result<GetSubscriptionInfoResponse> {
    val emptyObject = object {}
    return postForResult("subscription-info", GetSubscriptionInfoResponse::class.java, emptyObject)
  }

  private suspend fun <T> postForResult(
    path: String,
    classType: Class<T>,
    body: Any?,
    requestID: String = UUID.randomUUID().toString(),
  ): Result<T> {
    try {
      val resp = httpClient.post(path, body, requestID = requestID)
      if (resp.status.value in 200..299) {
        return Result.success(gson.fromJson(resp.bodyAsText(), classType))
      }
      return Result.failure(AugmentAPIException(path, resp.status.value, resp.status.description))
    } catch (e: IllegalStateException) {
      logger.warn("Failed to make subscription-info request", e)
      return Result.failure(e)
    }
  }

  suspend fun logFeatureVector(featureVector: Map<String, String>): Result<Unit> {
    val request =
      object {
        @Suppress("unused")
        val client_name = "intellij"

        @Suppress("unused")
        val feature_vector = featureVector
      }

    val path = "report-feature-vector"
    val resp = httpClient.post(path, request)
    if (resp.status.value in 200..299) {
      logger.info("Feature vector sent successfully")
      return Result.success(Unit)
    } else {
      logger.warn("Failed to send feature vector: ${resp.status.value} ${resp.status.description}")
      return Result.failure(
        AugmentAPIException(
          path,
          resp.status.value,
          "Failed to send feature vector: ${resp.status.value} ${resp.status.description}",
        ),
      )
    }
  }

  private fun <T : com.google.protobuf.Message> bodyIntoProto(
    body: String,
    builder: com.google.protobuf.Message.Builder,
  ): Result<T> {
    if (body.isBlank()) {
      return Result.failure(IllegalArgumentException("Response body is empty"))
    }

    return try {
      builder.clear()
      jsonParser.merge(body, builder)
      @Suppress("UNCHECKED_CAST")
      Result.success(builder.build() as T)
    } catch (e: com.google.protobuf.InvalidProtocolBufferException) {
      Result.failure(e)
    } catch (e: Exception) {
      Result.failure(e)
    }
  }
}
