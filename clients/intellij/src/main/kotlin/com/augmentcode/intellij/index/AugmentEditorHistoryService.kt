package com.augmentcode.intellij.index

import com.augmentcode.intellij.workspacemanagement.coordination.BlobNameService
import com.augmentcode.intellij.workspacemanagement.utils.isV3IndexingEnabled
import com.intellij.openapi.application.readAction
import com.intellij.openapi.diagnostic.thisLogger
import com.intellij.openapi.fileEditor.FileEditor
import com.intellij.openapi.fileEditor.FileEditorManager
import com.intellij.openapi.fileEditor.FileEditorManagerListener
import com.intellij.openapi.project.Project
import com.intellij.openapi.util.Key
import com.intellij.openapi.util.TextRange
import com.intellij.openapi.vfs.VirtualFile
import com.intellij.psi.PsiManager
import com.intellij.util.diff.Diff
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import org.jetbrains.annotations.VisibleForTesting
import public_api.PublicApi.ReplacementText

internal data class HistoricalFileContent(
  val blobName: String, // at the time of the saving content
  val content: String, // content at the time of sync for remoteName
)

/**
 * Service to remember when we uploaded blobs so we can find modifications for recency information
 */
class AugmentEditorHistoryService(
  private val project: Project,
  private val cs: CoroutineScope,
) : FileEditorManagerListener {
  companion object {
    fun getInstance(project: Project): AugmentEditorHistoryService = project.getService(AugmentEditorHistoryService::class.java)

    // returns a list of partial replacements without blobName and path fields populated
    @VisibleForTesting
    fun findReplacements(
      before: String,
      after: String,
    ): List<ReplacementText.Builder> {
      val changes = Diff.buildChanges(before, after)?.toList() ?: return emptyList()
      if (changes.isEmpty()) {
        return emptyList()
      }
      val linesBefore = before.lines()
      val linesRangesBefore = lineRanges(linesBefore)

      val linesAfter = after.lines()
      val replacementBuilders =
        changes.map { change ->
          val replacementBuilder = ReplacementText.newBuilder()
          replacementBuilder.replacementText = ""
          replacementBuilder.presentInBlob = false
          replacementBuilder.charStart =
            if (change.line0 >= linesBefore.size) {
              before.length
            } else {
              linesRangesBefore[change.line0].startOffset
            }
          replacementBuilder.charEnd = replacementBuilder.charStart
          if (change.deleted > 0) {
            replacementBuilder.charEnd = linesRangesBefore[change.line0 + change.deleted - 1].endOffset
          }
          if (change.inserted > 0) {
            val linesToInsert = linesAfter.subList(change.line1, change.line1 + change.inserted)
            replacementBuilder.replacementText = linesToInsert.joinToString(separator = "\n", postfix = "\n")
          }
          replacementBuilder
        }
      // now we need to normalize replacements since every next one should use indexes like previous are applied
      var adjustment = 0
      for (replacement in replacementBuilders) {
        replacement.charStart += adjustment
        replacement.charEnd += adjustment
        // this is how our adjustment should change
        adjustment += replacement.replacementText.length - (replacement.charEnd - replacement.charStart)
      }
      return replacementBuilders
    }

    private fun lineRanges(lines: List<String>): List<TextRange> {
      var offset = 0
      val result = ArrayList<TextRange>(lines.size)
      for (line in lines) {
        result.add(TextRange(offset, offset + line.length + 1))
        offset += line.length
        offset += 1 // new line character
      }
      return result
    }
  }

  init {
    project.messageBus.connect().subscribe(
      FileEditorManagerListener.FILE_EDITOR_MANAGER,
      this,
    )
  }

  private val thisLogger = thisLogger()

  private val previousContentCacheKey: Key<HistoricalFileContent> = Key.create("AugmentPreviousContentCache")

  override fun fileOpened(
    source: FileEditorManager,
    file: VirtualFile,
  ) {
    val editor = source.getSelectedEditor(file) ?: return

    // Move read off the UI thread
    cs.launch(Dispatchers.IO) {
      if (isV3IndexingEnabled()) {
        // TODO: since this used to use localName (rather than synced blob name) in Index V2,
        // we might need to create expected blob name for the current file state
        // rather than using the last synced file state
        val blobName = BlobNameService.getInstance(project).getByPath(file.path)
        if (blobName == null) {
          return@launch
        }
        memorize(editor, blobName)
      } else {
        val state = AugmentBlobStateReader.read(project, file) ?: return@launch
        memorize(editor, state.localName)
      }
    }
  }

  private suspend fun memorize(
    editor: FileEditor,
    expectedBlobName: String,
  ) {
    val currentContent = latestContent(project, editor.file)
    memorizeContent(editor, expectedBlobName, currentContent)
  }

  /**
   * This can currently be called by Index V2 via AugmentLocalIndexer,
   * and Index V3 via WorkspaceCoordinatorService. In each service,
   * flags are checked to ensure we only call this method when appropriate.
   * In other words, we rely on the caller to check flags.
   */
  fun memorizeContent(
    editor: FileEditor,
    expectedBlobName: String,
    content: String,
  ) {
    editor.putUserData(previousContentCacheKey, HistoricalFileContent(expectedBlobName, content))
  }

  suspend fun findReplacementsSinceLastUpload(editor: FileEditor): List<ReplacementText> {
    val vFile = editor.file

    val blobName =
      if (isV3IndexingEnabled()) {
        val blobName = BlobNameService.getInstance(project).getByPath(vFile.path)
        if (blobName == null) {
          return emptyList()
        }
        blobName
      } else {
        val state = AugmentBlobStateReader.read(project, vFile) ?: return emptyList()
        if (state.synced) {
          return emptyList()
        }
        state.remoteName
      }

    if (vFile.fileType.isBinary) {
      thisLogger.warn("Binary file ${vFile.name}, skipping diff")
      return emptyList()
    }
    val previousContent = editor.getUserData(previousContentCacheKey)
    if (previousContent == null || previousContent.blobName != blobName) {
      return emptyList()
    }
    val path = AugmentRoot.findRelativePath(project, vFile) ?: return emptyList()
    val currentContent = latestContent(project, vFile)
    val result = findReplacements(previousContent.content, currentContent)
    for (replacement in result) {
      replacement.blobName = blobName
      replacement.path = path
    }
    return result.map { it.build() }
  }

  private suspend fun latestContent(
    project: Project,
    file: VirtualFile,
  ): String {
    // try to see if PSI is available since it will contain in-memory changes
    return readAction {
      PsiManager.getInstance(project).findFile(file)?.text
    } ?: file.contentsToByteArray().toString(Charsets.UTF_8)
  }
}
