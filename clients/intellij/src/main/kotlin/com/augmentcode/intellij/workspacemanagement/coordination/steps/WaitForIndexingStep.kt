package com.augmentcode.intellij.workspacemanagement.coordination.steps

import com.augmentcode.api.FindMissingRequest
import com.augmentcode.intellij.api.AugmentAPI
import com.augmentcode.intellij.settings.AugmentSettings
import com.augmentcode.intellij.workspacemanagement.checkpoint.BlobAddedEvent
import com.augmentcode.intellij.workspacemanagement.checkpoint.BlobChangeEvent
import com.augmentcode.intellij.workspacemanagement.checkpoint.BlobUpdatedEvent
import com.augmentcode.intellij.workspacemanagement.coordination.BlobNameService
import com.augmentcode.intellij.workspacemanagement.utils.RoughlySizedChannel
import com.intellij.openapi.Disposable
import com.intellij.openapi.diagnostic.thisLogger
import com.intellij.openapi.project.Project
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import kotlinx.coroutines.yield
import org.jetbrains.annotations.VisibleForTesting
import kotlin.coroutines.cancellation.CancellationException
import kotlin.time.Duration
import kotlin.time.Duration.Companion.seconds

@OptIn(ExperimentalCoroutinesApi::class)
class WaitForIndexingStep(
  private val project: Project,
  private val scope: CoroutineScope,
  private val intakeChannel: RoughlySizedChannel<CoordinationFileDetailsWithBlob>,
  private val changesChannel: RoughlySizedChannel<BlobChangeEvent>,
  private val uploadChannel: RoughlySizedChannel<FileToUpload>,
  private val batchSize: Int = 1000,
  private val maxWaitTime: Duration = 3.seconds, // duration between batches when progress is being made
  private val slowIndexingBackoffTime: Duration = 10.seconds, // duration to wait when indexing stuck
  private val backendHealthBackoffTime: Duration = 30.seconds, // duration to wait when indexing stuck
) : Disposable, BaseProcessingStep("WaitForIndexingStep") {
  private val logger = thisLogger()

  override fun createProcessingJob(): Job {
    return scope.launch {
      while (isActive) {
        try {
          processIntakeChannel()
        } catch (e: CancellationException) {
          throw e
        } catch (e: Exception) {
          logger.warn("Failed to process intake channel", e)
        }
        // Allow the dispatcher to execute another coroutine
        yield()
      }
    }
  }

  /**
   * Returns the set of blobs that are still not indexed.
   */
  @VisibleForTesting
  suspend fun processIntakeChannel() {
    // top the batch with new blob names
    val blobsToCheckBatch =
      mutableSetOf(
        // Wait for the first blob to be available
        intakeChannel.receive(),
      )

    while (blobsToCheckBatch.size < batchSize) {
      val additionalBlobName = intakeChannel.receiveWithTimeout(maxWaitTime)
      if (additionalBlobName == null) {
        thisLogger().debug("No more blobs received on channel so using batch size of ${blobsToCheckBatch.size}")
        break
      }
      blobsToCheckBatch.add(additionalBlobName)
    }

    val findMissingResult =
      AugmentAPI.Companion.instance.findMissingResult(
        FindMissingRequest().apply {
          model = AugmentSettings.instance.modelName ?: ""
          memObjectNames = blobsToCheckBatch.map { it.remoteBlobName }
        },
      )

    if (findMissingResult.isFailure) {
      logger.warn("Failed to check indexing status (Requeue-ing blobs): ${findMissingResult.exceptionOrNull()?.message}")
      for (blobState in blobsToCheckBatch) {
        intakeChannel.send(blobState)
      }
      // Since the backend returns an error, we should backoff before retrying
      delay(backendHealthBackoffTime)
      return
    }

    val response = findMissingResult.getOrThrow()
    val unknownSet = response.unknownMemoryNames.toMutableSet()
    val nonIndexSet = response.nonindexedBlobNames.toMutableSet()

    for (event in blobsToCheckBatch) when {
      event.remoteBlobName in unknownSet -> {
        // This should never happen in practice since files should be uploaded before being
        // added to the wait for indexing queue.
        thisLogger().warn("Blob ${event.fileDetails.relPath} (${event.remoteBlobName}) is unknown but expected to be uploaded")
        uploadChannel.send(FileToUpload(event.fileDetails, event.remoteBlobName))
      }

      event.remoteBlobName in nonIndexSet -> {
        logger.debug("Blob ${event.fileDetails.relPath} (${event.remoteBlobName}) is not indexed yet")
        intakeChannel.send(event)
      }

      else -> {
        // all good, send the event to the checkpoint manager
        val fileDetails = event.fileDetails
        val path = java.nio.file.Paths.get(fileDetails.rootPath, fileDetails.relPath)

        val blobName = event.remoteBlobName
        val blobNameService = BlobNameService.getInstance(project)
        val existingBlobName = blobNameService.getByPath(path.toString())
        val checkpointEvent =
          if (existingBlobName != null) {
            logger.trace("Blob name changed for ${fileDetails.relPath} from $existingBlobName to $blobName")
            BlobUpdatedEvent(path, existingBlobName, blobName)
          } else {
            logger.trace("New blob added for ${fileDetails.relPath} with blob name $blobName")
            BlobAddedEvent(path, blobName)
          }

        try {
          blobNameService.put(blobName, path.toString())
          changesChannel.send(checkpointEvent)
        } catch (e: IllegalStateException) {
          logger.warn("Failed to store blob name for ${fileDetails.relPath} ($blobName). Dropping change event: $e")
        }
      }
    }

    // If the majority of our blobs are not indexed, we should wait a while before continuing.
    // Rationale here is that a random set of blobs should be representative of all blobs
    // in the queue and if the vast majority of blobs are not indexed, we should wait
    // TODO: Verify this logic is what we want and that the final delays and percentage make sense
    val blobNames = blobsToCheckBatch.map { it.remoteBlobName }
    val nonIndexedBlobs = nonIndexSet.filter { it in blobNames }
    val nonIndexedPercentage = nonIndexedBlobs.size.toDouble() / blobNames.size
    if (nonIndexedPercentage > 0.9) {
      logger.warn("Non of the blobs were indexed, backing off and retrying...")
      delay(slowIndexingBackoffTime)
    }
  }
}
