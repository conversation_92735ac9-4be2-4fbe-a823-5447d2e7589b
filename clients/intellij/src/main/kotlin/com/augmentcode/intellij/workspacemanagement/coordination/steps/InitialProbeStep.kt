package com.augmentcode.intellij.workspacemanagement.coordination.steps

import com.augmentcode.api.FindMissingRequest
import com.augmentcode.intellij.api.AugmentAPI
import com.augmentcode.intellij.pluginstate.AugmentAppStateService
import com.augmentcode.intellij.utils.IndexUtil
import com.augmentcode.intellij.workspacemanagement.checkpoint.BlobAddedEvent
import com.augmentcode.intellij.workspacemanagement.checkpoint.BlobChangeEvent
import com.augmentcode.intellij.workspacemanagement.checkpoint.BlobUpdatedEvent
import com.augmentcode.intellij.workspacemanagement.coordination.BlobNameService
import com.augmentcode.intellij.workspacemanagement.coordination.mtimecache.MTimeCache
import com.augmentcode.intellij.workspacemanagement.coordination.mtimecache.MTimeCacheEntry
import com.augmentcode.intellij.workspacemanagement.utils.RoughlySizedChannel
import com.intellij.openapi.Disposable
import com.intellij.openapi.diagnostic.thisLogger
import com.intellij.openapi.project.Project
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import kotlinx.coroutines.yield
import kotlin.coroutines.cancellation.CancellationException
import kotlin.time.Duration.Companion.milliseconds

class InitialProbeStep(
  private val project: Project,
  private val scope: CoroutineScope,
  private val filteredFilesChannel: RoughlySizedChannel<CoordinationFileDetails>,
  private val uploadChannel: RoughlySizedChannel<FileToUpload>,
  private val waitForIndexChannel: RoughlySizedChannel<CoordinationFileDetailsWithBlob>,
  private val checkpointChannel: RoughlySizedChannel<BlobChangeEvent>,
) : Disposable, BaseProcessingStep("Initial Probe Step") {
  private val logger = thisLogger()

  companion object {
    // TODO: Use same batch size constant as WaitForIndexing job
    const val INITIAL_PROBE_BATCH_SIZE = 1000L
    const val API_FAILURE_DELAY_MS = 30_000L // placeholder. need to run this by repo scale team
    val MAX_WAIT_TIME = 100.milliseconds
  }

  override fun createProcessingJob(): Job {
    return scope.launch {
      logger.info("Starting initial probe job")
      while (isActive) {
        try {
          processInitialProbe()
        } catch (e: CancellationException) {
          throw e
        } catch (e: Exception) {
          logger.warn("Failed to process initial probe", e)
        }
        // Allow the dispatcher to execute another coroutine
        yield()
      }
    }
  }

  private suspend fun processInitialProbe() {
    logger.debug("Waiting for file for initial find-missing probe")

    // Wait for first item
    val firstFileDetails = filteredFilesChannel.receive()

    // Collect batch
    val batch = mutableListOf<Pair<CoordinationFileDetails, String>>()
    val firstBlobName =
      createBlobName(firstFileDetails) ?: run {
        logger.debug("Blob name could not be created because file could not be read. Dropping file: ${firstFileDetails.relPath}")
        return
      }
    batch.add(Pair(firstFileDetails, firstBlobName))
    while (batch.size < INITIAL_PROBE_BATCH_SIZE) {
      val fileDetails = filteredFilesChannel.receiveWithTimeout(MAX_WAIT_TIME) ?: break
      val blobName = createBlobName(fileDetails)
      if (blobName == null) {
        logger.warn("Blob name could not be created because file could not be read. Dropping file: ${fileDetails.relPath}")
        continue
      }
      batch.add(Pair(fileDetails, blobName))
    }

    logger.debug("Checking find-missing for ${batch.size} blobs")
    for ((fileDetails, blobName) in batch) {
      logger.trace("    - ${fileDetails.relPath} with blob name $blobName")
    }

    val blobNames = batch.map { it.second }
    val result =
      AugmentAPI.instance.findMissingResult(
        FindMissingRequest().apply {
          model = AugmentAppStateService.instance.context.model?.defaultModel ?: ""
          memObjectNames = blobNames
        },
      )

    if (result.isFailure) {
      logger.warn("Probe failed. Requeueing ${batch.size} blobs")
      batch.forEach { (item, _) ->
        filteredFilesChannel.send(item)
      }
      // TODO: Is this needed? Repo scale team didn't seem to care, but i'm being overly cautious
      delay(API_FAILURE_DELAY_MS)
      return
    }
    val response = result.getOrThrow()

    logger.debug(
      "Probe succeeded. ${response.unknownMemoryNames.size} unknown blobs, ${response.nonindexedBlobNames.size} non-indexed blobs",
    )
    for (blobName in response.unknownMemoryNames) {
      logger.trace("    - Unknown blob: $blobName")
    }
    for (blobName in response.nonindexedBlobNames) {
      logger.trace("    - Non-indexed blob: $blobName")
    }

    // Route files based on find-missing response
    for ((fileDetails, blobName) in batch) {
      when {
        response.unknownMemoryNames.contains(blobName) -> {
          // Enqueue unknown blobs to upload channel
          val fileToUpload =
            FileToUpload(
              fileDetails = fileDetails,
              expectedBlobName = blobName,
            )
          uploadChannel.send(fileToUpload)
        }
        response.nonindexedBlobNames.contains(blobName) -> {
          // Enqueue non-indexed blobs to wait for index channel
          val coordinationFileDetailsWithBlob =
            CoordinationFileDetailsWithBlob(
              fileDetails,
              blobName,
              System.currentTimeMillis(),
            )
          waitForIndexChannel.send(coordinationFileDetailsWithBlob)
        }
        else -> {
          // Enqueue known and indexed blobs to checkpoint channel
          val path = java.nio.file.Paths.get(fileDetails.rootPath, fileDetails.relPath)

          val blobNameService = BlobNameService.getInstance(project)
          val existingBlobName = blobNameService.getByPath(path.toString())
          val event =
            if (existingBlobName != null) {
              logger.trace("Blob name changed for ${fileDetails.relPath} from $existingBlobName to $blobName")
              BlobUpdatedEvent(path, existingBlobName, blobName)
            } else {
              logger.trace("New blob added for ${fileDetails.relPath} with blob name $blobName")
              BlobAddedEvent(path, blobName)
            }

          try {
            blobNameService.put(blobName, path.toString())
            checkpointChannel.send(event)
          } catch (e: IllegalStateException) {
            logger.warn("Failed to store blob name. Dropping checkpoint event", e)
          }
        }
      }
    }
  }

  private fun createBlobName(coordinationFileDetails: CoordinationFileDetails): String? {
    val cachedResult = MTimeCache.getInstance(project).get(coordinationFileDetails.virtualFile.path)
    if (cachedResult != null) {
      logger.debug("Found blob name in mtime cache for ${coordinationFileDetails.relPath}: ${cachedResult.blobName}")
      if (cachedResult.mtime == coordinationFileDetails.virtualFile.modificationStamp) {
        logger.debug("Mtime cache is up-to-date for ${coordinationFileDetails.relPath}. Using cached blob name")
        return cachedResult.blobName
      } else {
        logger.debug("Recomputing blob name for ${coordinationFileDetails.relPath} because modification time changed since mtime cache")
      }
    } else {
      logger.debug("No mtime cache found for ${coordinationFileDetails.relPath}")
    }

    val normalizedText = IndexUtil.normalizedText(coordinationFileDetails.virtualFile)
    if (normalizedText == null) {
      logger.warn("Failed to read file ${coordinationFileDetails.relPath}. Dropping file")
      return null
    }

    val expectedBlobName =
      IndexUtil.expectedBlobName(
        coordinationFileDetails.relPath,
        normalizedText,
      )

    // Cache expected blob name
    MTimeCache.getInstance(project).put(
      coordinationFileDetails.virtualFile.path,
      MTimeCacheEntry(coordinationFileDetails.virtualFile.modificationStamp, coordinationFileDetails.virtualFile.length, expectedBlobName),
    )

    return expectedBlobName
  }
}
