package com.augmentcode.intellij.settings

import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.components.BaseState
import kotlin.reflect.KMutableProperty

class AugmentSettingsState() : BaseState() {
  companion object {
    val messageBus = ApplicationManager.getApplication().messageBus
  }

  var completionURL by string()
  var apiToken by string()
  var inlineCompletionEnabled by property(true)
  var disableForFileTypes by stringSet()
  var modelName by string()

  // Call this method to update property value and trigger change notification
  fun <T> setProperty(
    property: KMutableProperty<T>,
    value: T,
  ) {
    val oldValue = property.getter.call()
    if (oldValue != value) {
      property.setter.call(value)
      messageBus.syncPublisher(SettingsChangeListener.TOPIC).onChange()
    }
  }
}
