package com.augmentcode.intellij.sidecar.tools

import com.augmentcode.intellij.api.AugmentAPI
import com.augmentcode.intellij.sidecarrpc.Connection
import com.augmentcode.sidecar.rpc.chat.ChatHistoryItem
import com.augmentcode.sidecar.rpc.tools.*
import com.google.protobuf.Empty
import com.google.protobuf.Struct
import com.intellij.openapi.project.Project
import kotlinx.coroutines.withTimeout
import kotlinx.coroutines.withTimeoutOrNull

class ToolsManager(
  private val project: Project,
  private val connection: Connection,
  private var toolsMode: ChatMode,
) {
  companion object {
    private const val TOOL_OPERATION_TIMEOUT_MS = 5_000L
  }

  val currentMode: ChatMode
    get() = toolsMode

  private val ideTools: Map<String, IdeTool>
    get() =
      when (toolsMode) {
        ChatMode.CHAT -> chatTools
        ChatMode.AGENT -> chatTools + agentTools
        else -> throw IllegalArgumentException("Unknown chat mode: $toolsMode")
      }

  private val chatTools: Map<String, IdeTool> = emptyMap()

  private val agentTools: Map<String, IdeTool> =
    listOf(
      LaunchProcessTool(project),
      ReadProcessTool(),
      KillProcessTool(),
      WriteProcessTool(),
      ListProcessesTool(),
    ).associateBy { it.name }

  suspend fun getToolState(): ToolsStateResponse? {
    val toolStateResponse =
      connection.sendRequest(
        "augmentcode/tools/state",
        Empty.getDefaultInstance(),
        ToolsStateResponse::class.java,
      )

    return withTimeout(TOOL_OPERATION_TIMEOUT_MS) {
      val response = toolStateResponse.await().unpack(ToolsStateResponse::class.java)
      ToolsStateResponse.newBuilder(response)
        // intentional due to hack in node-process/src/tools/client-tools-host.ts
        // .addAllTools(ideTools.values.map { it.protoDefinition })
        .build()
    }
  }

  suspend fun callTool(
    name: String,
    requestId: String,
    toolUseId: String,
    input: Struct,
    chatHistory: List<ChatHistoryItem>,
    conversationId: String,
  ): ToolCallResponse {
    if (ideTools.containsKey(name)) {
      return ideTools[name]!!.call(project, requestId, toolUseId, input, chatHistory, conversationId)
    }

    val callToolResponse =
      connection.sendRequest(
        "augmentcode/tools/call",
        ToolCallRequest.newBuilder()
          .setName(name)
          .setRequestId(requestId)
          .setToolUseId(toolUseId)
          .setInput(input)
          .addAllHistory(chatHistory)
          .setConversationId(conversationId)
          .build(),
        ToolCallResponse::class.java,
      )

    // Tool calls can be long running tasks, so we don't want to timeout
    return callToolResponse.await().unpack(ToolCallResponse::class.java)
  }

  suspend fun cancelToolRun(
    requestId: String,
    toolUseId: String,
  ) {
    for (tool in ideTools.values) {
      if (tool.cancelCall(toolUseId)) break
    }

    val cancelToolRunResponse =
      connection.sendRequest(
        "augmentcode/tools/cancel-run",
        ToolCancelRunRequest.newBuilder().setRequestId(requestId).setToolUseId(toolUseId).build(),
        Empty::class.java,
      )
    return withTimeout(TOOL_OPERATION_TIMEOUT_MS) {
      cancelToolRunResponse.await()
    }
  }

  suspend fun changeChatMode(newMode: ChatMode) {
    if (newMode == toolsMode) return
    toolsMode = newMode
    val changeChatModeResponse =
      connection.sendRequest(
        "augmentcode/tools/change-chat-mode",
        ChangeChatModeRequest.newBuilder().setMode(toolsMode).build(),
        Empty::class.java,
      )
    return withTimeout(TOOL_OPERATION_TIMEOUT_MS) {
      changeChatModeResponse.await()
    }
  }

  suspend fun getToolStatusForSettingsPanel(request: GetToolStatusForSettingsPanelRequest): GetToolStatusForSettingsPanelResponse {
    val getToolStatusForSettingsPanelResponse =
      connection.sendRequest(
        "augmentcode/tools/get-tool-status-for-settings-panel",
        request,
        GetToolStatusForSettingsPanelResponse::class.java,
      )

    return withTimeoutOrNull(TOOL_OPERATION_TIMEOUT_MS) {
      getToolStatusForSettingsPanelResponse.await().unpack(GetToolStatusForSettingsPanelResponse::class.java)
    } ?: GetToolStatusForSettingsPanelResponse.getDefaultInstance()
  }

  suspend fun retrieveRemoteTools(request: RetrieveRemoteToolsRequest): RetrieveRemoteToolsResponse {
    if (!AugmentAPI.instance.available()) {
      return RetrieveRemoteToolsResponse.getDefaultInstance()
    }

    val response = AugmentAPI.instance.listRemoteTools(request.supportedToolsList.map { it.number })

    return RetrieveRemoteToolsResponse.newBuilder().addAllTools(
      response.tools.map {
        val remoteToolId = RemoteToolId.forNumber(it.remote_tool_id) ?: return@map null
        RemoteToolDefinition.newBuilder()
          .setToolDefinition(
            ToolDefinition.newBuilder()
              .setName(it.tool_definition.name)
              .setDescription(it.tool_definition.description)
              .setInputSchemaJson(it.tool_definition.input_schema_json)
              .setToolSafety(ToolSafety.UNSAFE)
              .build(),
          )
          .setRemoteToolId(remoteToolId)
          .setAvailabilityStatus(ToolAvailabilityStatus.forNumber(it.availability_status))
          .setToolSafety(convertToolSafety(it.tool_safety))
          .setOauthUrl(it.oauth_url ?: "")
          .build()
      }.filterNotNull(),
    ).build()
  }

  suspend fun setMcpServers(mcpServers: List<McpServerConfig>) {
    val setMcpServersResponse =
      connection.sendRequest(
        "augmentcode/tools/set-mcp-servers",
        SetMcpServersRequest.newBuilder().addAllMcpServers(mcpServers).build(),
        Empty::class.java,
      )
    return withTimeout(TOOL_OPERATION_TIMEOUT_MS) {
      setMcpServersResponse.await()
    }
  }
}

fun convertToolSafety(value: Int): ToolSafety {
  return when (value) {
    0 -> ToolSafety.UNSAFE
    1 -> ToolSafety.SAFE
    2 -> ToolSafety.CHECK
    else -> ToolSafety.UNSAFE
  }
}
