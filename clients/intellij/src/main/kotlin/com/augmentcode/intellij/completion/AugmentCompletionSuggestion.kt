package com.augmentcode.intellij.completion

import com.augmentcode.intellij.api.AugmentAPI
import com.augmentcode.intellij.api.DEFAULT_PREFIX_SIZE
import com.augmentcode.intellij.api.DEFAULT_SUFFIX_SIZE
import com.augmentcode.intellij.history.AugmentHistoryEntry
import com.augmentcode.intellij.history.AugmentHistoryModel
import com.augmentcode.intellij.index.AugmentBlobStateReader
import com.augmentcode.intellij.index.AugmentEditorHistoryService
import com.augmentcode.intellij.index.AugmentRoot
import com.augmentcode.intellij.pluginstate.AugmentAppStateService
import com.augmentcode.intellij.settings.AugmentSettings
import com.augmentcode.intellij.status.StateDefinitions
import com.augmentcode.intellij.status.StateManager
import com.augmentcode.intellij.syncing.AugmentRemoteSyncingManager
import com.augmentcode.intellij.workspacemanagement.coordination.WorkspaceCoordinatorService
import com.augmentcode.intellij.workspacemanagement.utils.isV3IndexingEnabled
import com.intellij.codeInsight.inline.completion.InlineCompletionRequest
import com.intellij.codeInsight.inline.completion.elements.InlineCompletionElement
import com.intellij.codeInsight.inline.completion.elements.InlineCompletionSkipTextElement
import com.intellij.codeInsight.inline.completion.suggestion.InlineCompletionSuggestion
import com.intellij.codeInsight.inline.completion.suggestion.InlineCompletionVariant
import com.intellij.openapi.diagnostic.thisLogger
import com.intellij.openapi.editor.Document
import com.intellij.openapi.fileEditor.FileEditorManager
import com.intellij.openapi.project.Project
import com.intellij.openapi.util.Key
import com.intellij.openapi.util.TextRange
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.asFlow
import org.jetbrains.annotations.VisibleForTesting
import public_api.PublicApi
import public_api.PublicApi.CompletionRequest
import public_api.PublicApi.RecencyInfo
import java.nio.file.Path
import kotlin.collections.get

// How long we display a status that no completions were found
const val NO_COMPLETION_STATUS_DURATION_MS: Long = 2000

val AugmentLastCompletionElementKey = Key<AugmentCompletionElement>("AugmentLastCompletionElement")

class AugmentCompletionSuggestion(
  private val inlineCompletionRequest: InlineCompletionRequest,
) : InlineCompletionSuggestion {
  private val logger = thisLogger()
  private var stateDisposal: () -> Unit = {}
  private val project = inlineCompletionRequest.editor.project
  private val historyModel: AugmentHistoryModel? = if (project != null) AugmentHistoryModel.getInstance(project) else null

  override suspend fun getVariants(): List<InlineCompletionVariant> {
    try {
      stateDisposal()
      inlineCompletionRequest.editor.project?.let { project ->
        stateDisposal = StateManager.getInstance(project).setState(StateDefinitions.GeneratingCompletion)
      }
      val completionList = getCompletion()
      if (completionList.isEmpty()) {
        inlineCompletionRequest.editor.project?.let { project ->
          val zeroDisposal = StateManager.getInstance(project).setState(StateDefinitions.NoCompletions)
          val cs = CoroutineScope(Dispatchers.IO)
          cs.launch {
            delay(NO_COMPLETION_STATUS_DURATION_MS)
            zeroDisposal()
          }
        }
      }
      return completionList
    } finally {
      stateDisposal()
    }
  }

  private suspend fun getCompletion(): List<InlineCompletionVariant> {
    val api = AugmentAPI.instance
    if (!api.available()) {
      return emptyList()
    }

    try {
      logger.debug("Retrieving new inline completion")
      val completionRequest = buildAugmentCompletionRequest() ?: return emptyList()
      val resultPair = api.complete(completionRequest)
      val requestId = resultPair.first
      val completionResult = resultPair.second
      if (completionResult.isFailure) {
        logger.warn("Completion request failed: ${completionResult.exceptionOrNull()}")
        return emptyList()
      }
      val completionResponse = completionResult.getOrThrow()

      if (completionResponse.unknownMemoryNamesCount > 0) {
        // we can't run write actions here, so we first need to collect affected files in a read action
        // and then submit a reindexing job to the write thread for later.
        // once re-indexing is in process IntelliJ will stop calling this provider until re-indexing is done
        // this way we are avoiding an endless loop of re-indexing.
        logger.info("Server reported unknown blobs: ${completionResponse.unknownMemoryNamesCount}")
        AugmentBlobStateReader.requestInvalidation(
          inlineCompletionRequest.file.project,
          completionResponse.unknownMemoryNamesList.toSet(),
        )
      }
      val completionItem = completionResponse.completionItemsList?.firstOrNull() ?: return emptyList()

      if (completionItem.text.isBlank()) {
        return emptyList()
      }

      historyModel?.addCompletionAsync(
        AugmentHistoryEntry(
          completionRequest,
          completionResponse,
          System.currentTimeMillis(),
          requestId,
        ),
      )

      // Stash completion in user data to retrieve them if rejected and report when shown to the user
      inlineCompletionRequest.editor.putUserData(
        AugmentLastCompletionElementKey,
        AugmentCompletionElement(0, completionItem.text, requestId),
      )

      val textElement = AugmentCompletionElement(0, completionItem.text, requestId)
      val elementList = mutableListOf<InlineCompletionElement>(textElement)
      if (completionItem.skippedSuffix != null && completionItem.skippedSuffix.isNotEmpty()) {
        if (completionItem.suffixReplacementText.isEmpty() ||
          completionItem.suffixReplacementText.startsWith(
            completionItem.skippedSuffix,
          )
        ) {
          elementList += InlineCompletionSkipTextElement(completionItem.skippedSuffix)
          val replacementText = completionItem.suffixReplacementText.removePrefix(completionItem.skippedSuffix)
          if (replacementText.isNotEmpty()) {
            elementList += AugmentCompletionElement(0, replacementText, requestId)
          }
        }
      }
      val completeCompletionVariant =
        InlineCompletionVariant.build(
          elements = elementList.asFlow(),
        )

      return listOf(completeCompletionVariant)
    } catch (e: Exception) {
      val isCancellation = e is CancellationException || e.cause is CancellationException
      if (isCancellation) {
        // Completions can be cancelled by IntelliJ if the user types while we are waiting for a completion result.
        logger.debug("Completion request was cancelled: ${e.message}")
      } else {
        logger.warn("Completion request failed: ${e.message}", e)
      }
      return emptyList()
    }
  }

  @VisibleForTesting
  suspend fun buildAugmentCompletionRequest(): CompletionRequest? {
    val document = inlineCompletionRequest.document
    val file = inlineCompletionRequest.file

    // result will be pasted after this offset
    val offset = inlineCompletionRequest.endOffset

    val completionRequestBuilder = CompletionRequest.newBuilder()
    completionRequestBuilder.model = AugmentSettings.instance.modelName ?: ""

    // TextRange has end exclusive
    fun ensureDocumentRange(offset: Int): Int {
      if (document.textLength == 0) return 0
      if (offset < 0) return 0
      if (offset > document.textLength) return document.textLength
      return offset
    }

    val model = AugmentAppStateService.instance.context.model
    val characterOfContextPrefix = model?.suggestedPrefixCharCount ?: DEFAULT_PREFIX_SIZE
    val startPrefixOffset = ensureDocumentRange(offset - characterOfContextPrefix)
    // prompt is a prefix of the line
    val prefixWithRange = getTextFromDocument(document, startPrefixOffset, offset)
    completionRequestBuilder.prompt = prefixWithRange.text
    completionRequestBuilder.prefixBegin = prefixWithRange.range.startOffset

    val characterOfContextSuffix = model?.suggestedSuffixCharCount ?: DEFAULT_SUFFIX_SIZE
    val endSuffixOffset = ensureDocumentRange(inlineCompletionRequest.endOffset + characterOfContextSuffix)
    val suffixWithRange = getTextFromDocument(document, inlineCompletionRequest.endOffset, endSuffixOffset)
    completionRequestBuilder.suffix = suffixWithRange.text
    completionRequestBuilder.suffixEnd = suffixWithRange.range.endOffset

    completionRequestBuilder.cursorPosition = offset

    completionRequestBuilder.path = AugmentRoot.findRelativePath(file.project, file.virtualFile) ?: return null

    val remoteName =
      if (isV3IndexingEnabled()) {
        val workspaceCoordinator = WorkspaceCoordinatorService.getInstance(file.project)
        workspaceCoordinator.getSyncedBlobForFileNonBlocking(Path.of(file.virtualFile.path))
      } else {
        AugmentBlobStateReader.read(file)?.remoteName
      }
    remoteName?.let { completionRequestBuilder.blobName = it } // can be null

    AugmentAppStateService.instance.context.model?.extensionToLanguageLookup?.get(
      file.virtualFile.extension,
    )?.let { completionRequestBuilder.lang = it }

    val blobsPayload =
      if (isV3IndexingEnabled()) {
        WorkspaceCoordinatorService.getInstance(file.project).getCheckpoint()
      } else {
        AugmentRemoteSyncingManager.getInstance(file.project).synchronizedBlobsPayload()
      }
    completionRequestBuilder.blobs =
      PublicApi.Blobs.newBuilder()
        .setCheckpointId(blobsPayload.checkpointId ?: "")
        .addAllAddedBlobs(blobsPayload.addedBlobs)
        .addAllDeletedBlobs(blobsPayload.deletedBlobs)
        .build()
    completionRequestBuilder.recencyInfo = calculateRecencyInfo(file.project)

    completionRequestBuilder.sequenceId = AugmentAPI.instance.nextSequenceId()

    return completionRequestBuilder.build()
  }

  data class TextWithRange(val text: String, val range: TextRange)

  private fun getTextFromDocument(
    document: Document,
    startOffset: Int,
    endOffset: Int,
  ): TextWithRange {
    var textRange = TextRange(startOffset, endOffset)
    var text = document.getText(textRange)
    if (text.isEmpty()) return TextWithRange(text, textRange)

    // We are breaking a Unicode character at the start of the string
    if (text.first().isLowSurrogate()) {
      textRange = TextRange(startOffset + 1, endOffset)
      text = document.getText(textRange)
    }

    // We are breaking a Unicode character at the end of the string
    if (text.last().isHighSurrogate()) {
      textRange = TextRange(startOffset, endOffset - 1)
      text = document.getText(textRange)
    }

    return TextWithRange(text, textRange)
  }

  private suspend fun calculateRecencyInfo(project: Project): RecencyInfo {
    val recentChanges =
      coroutineScope {
        FileEditorManager.getInstance(project).allEditors.map { editor ->
          async {
            AugmentEditorHistoryService.getInstance(project).findReplacementsSinceLastUpload(editor)
          }
        }.awaitAll().flatten()
      }

    val recencyInfo =
      RecencyInfo.newBuilder()
        .addAllRecentChanges(recentChanges)
        .build()

    return recencyInfo
  }
}
