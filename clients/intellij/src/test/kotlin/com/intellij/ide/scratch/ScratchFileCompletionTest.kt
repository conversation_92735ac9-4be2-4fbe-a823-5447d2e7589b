package com.intellij.ide.scratch

import com.augmentcode.intellij.completion.AugmentCompletionProvider
import com.augmentcode.intellij.generateCompletionResult
import com.augmentcode.intellij.mock.PathFilterServiceMocks
import com.augmentcode.intellij.pluginstate.PluginState
import com.augmentcode.intellij.testutils.AugmentBasePlatformTestCase
import com.augmentcode.intellij.testutils.HttpUtil
import com.google.protobuf.util.JsonFormat
import com.intellij.codeInsight.inline.completion.InlineCompletionHandler
import com.intellij.codeInsight.inline.completion.testInlineCompletion
import com.intellij.lang.xml.XMLLanguage
import com.intellij.openapi.util.IntellijInternalApi
import com.intellij.testFramework.TestDataPath
import com.intellij.testFramework.runInEdtAndGet
import io.ktor.client.engine.mock.MockEngine
import io.ktor.client.engine.mock.respond
import io.ktor.http.HttpHeaders
import io.ktor.http.HttpStatusCode
import io.ktor.http.headersOf
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4

@OptIn(IntellijInternalApi::class)
@RunWith(JUnit4::class)
@Suppress("UnstableApiUsage")
@TestDataPath("\$CONTENT_ROOT/src/test/testData")
class ScratchFileCompletionTest : AugmentBasePlatformTestCase() {
  override fun getTestDataPath() = "src/test/testData/completion"

  override fun runInDispatchThread(): Boolean = false // to use testInlineCompletion

  override fun setUp() {
    super.setUp()
    augmentHelpers().forcePluginState(PluginState.ENABLED)
  }

  @Test
  fun testCanComplete() =
    myFixture.testInlineCompletion {
      val completionText = "<augment></augment>"
      val completionResult = generateCompletionResult(completionText)

      val mockEngine =
        MockEngine { request ->
          when (request.url.encodedPath) {
            "/get-models" -> HttpUtil.respondGetModels(this)
            "/completion" -> {
              respond(
                content = JsonFormat.printer().print(completionResult),
                status = HttpStatusCode.OK,
                headers = headersOf("Content-Type", "application/json"),
              )
            }
            "/resolve-completions" -> {
              respond(
                content = """{"success": true}""",
                headers = headersOf(HttpHeaders.ContentType, "application/json"),
              )
            }
            "/record-onboarding-session-event" -> {
              respond(
                content = """{"success": true}""",
                headers = headersOf(HttpHeaders.ContentType, "application/json"),
              )
            }
            else -> error("Unexpected request to ${request.url.encodedPath}")
          }
        }
      augmentHelpers().registerMockEngine(mockEngine)

      PathFilterServiceMocks.mockAlwaysAccept(project, testRootDisposable)

      val scratchFile =
        runInEdtAndGet {
          val context = ScratchFileCreationHelper.Context()
          context.language = XMLLanguage.INSTANCE
          ScratchFileActions.doCreateNewScratch(project, context)
        }
      assertNotNull(scratchFile)

      myFixture.configureFromExistingVirtualFile(scratchFile?.virtualFile!!)
      InlineCompletionHandler.registerTestHandler(AugmentCompletionProvider(), testRootDisposable)
      callInlineCompletion()
      delay() // so everything is processed
      insert()
      myFixture.checkResult(completionText)
    }
}
