package com.augmentcode.api

import org.junit.Assert.assertEquals
import org.junit.Assert.assertTrue
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4
import public_api.PublicApi

@RunWith(JUnit4::class)
class FeatureFlagsTest {
  @Test
  fun testAdditionalChatModelsMap_Empty() {
    val flags =
      PublicApi.GetModelsResponse.FeatureFlags.newBuilder()
        .setAdditionalChatModels("")
        .build()

    val result = flags.additionalChatModelsMap()
    assertTrue(result.isEmpty())
  }

  @Test
  fun testAdditionalChatModelsMap_EmptyMap() {
    val flags =
      PublicApi.GetModelsResponse.FeatureFlags.newBuilder()
        .setAdditionalChatModels("{}")
        .build()

    val result = flags.additionalChatModelsMap()
    assertTrue(result.isEmpty())
  }

  @Test
  fun testAdditionalChatModelsMap_ValidJson() {
    val flags =
      PublicApi.GetModelsResponse.FeatureFlags.newBuilder()
        .setAdditionalChatModels("""{"model1":"value1","model2":"value2"}""")
        .build()

    val result = flags.additionalChatModelsMap()
    assertEquals(2, result.size)
    assertEquals("value1", result["model1"])
    assertEquals("value2", result["model2"])
  }

  @Test
  fun testAdditionalChatModelsMap_NullValues() {
    val flags =
      PublicApi.GetModelsResponse.FeatureFlags.newBuilder()
        .setAdditionalChatModels("""{"model1":null,"model2":"value2"}""")
        .build()

    val result = flags.additionalChatModelsMap()
    assertEquals(2, result.size)
    assertEquals("null", result["model1"])
    assertEquals("value2", result["model2"])
  }

  @Test
  fun testAdditionalChatModelsMap_InvalidJson() {
    val flags =
      PublicApi.GetModelsResponse.FeatureFlags.newBuilder()
        .setAdditionalChatModels("invalid json")
        .build()

    val result = flags.additionalChatModelsMap()
    assertTrue(result.isEmpty())
  }

  @Test
  fun testAdditionalChatModelsMap_Array() {
    val flags =
      PublicApi.GetModelsResponse.FeatureFlags.newBuilder()
        .setAdditionalChatModels("[foo, bar]")
        .build()

    val result = flags.additionalChatModelsMap()
    assertTrue(result.isEmpty())
  }

  @Test
  fun testAdditionalChatModelsMap_StringNull() {
    val flags =
      PublicApi.GetModelsResponse.FeatureFlags.newBuilder()
        .setAdditionalChatModels("null")
        .build()

    val result = flags.additionalChatModelsMap()
    assertTrue(result.isEmpty())
  }

  @Test
  fun testAdditionalChatModelsMap_DuplicateKeys() {
    val flags =
      PublicApi.GetModelsResponse.FeatureFlags.newBuilder()
        .setAdditionalChatModels("""{"model1":"value1","model1":"value2"}""")
        .build()

    // Gson considers duplicate keys to be invalid json
    val result = flags.additionalChatModelsMap()
    assertTrue(result.isEmpty())
  }
}
