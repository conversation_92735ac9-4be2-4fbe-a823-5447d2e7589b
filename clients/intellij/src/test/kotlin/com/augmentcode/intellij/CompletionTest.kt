package com.augmentcode.intellij

import com.augmentcode.api.ResolveCompletionsRequest
import com.augmentcode.intellij.completion.AugmentCompletionProvider
import com.augmentcode.intellij.completion.AugmentLastCompletionElementKey
import com.augmentcode.intellij.index.AugmentBlobStateReader
import com.augmentcode.intellij.pluginstate.PluginState
import com.augmentcode.intellij.settings.AugmentSettings
import com.augmentcode.intellij.syncing.AugmentRemoteSyncingManager
import com.augmentcode.intellij.testutils.AugmentBasePlatformTestCase
import com.augmentcode.intellij.testutils.GsonUtil
import com.augmentcode.intellij.testutils.HttpUtil
import com.google.protobuf.util.JsonFormat
import com.intellij.codeInsight.inline.completion.InlineCompletionHandler
import com.intellij.codeInsight.inline.completion.session.InlineCompletionContext
import com.intellij.codeInsight.inline.completion.testInlineCompletion
import com.intellij.openapi.actionSystem.IdeActions
import com.intellij.testFramework.TestDataPath
import io.ktor.client.engine.mock.MockEngine
import io.ktor.client.engine.mock.respond
import io.ktor.client.engine.mock.toByteArray
import io.ktor.http.HttpHeaders
import io.ktor.http.HttpStatusCode
import io.ktor.http.headersOf
import org.junit.Assert.assertNotEquals
import org.junit.Rule
import org.junit.Test
import org.junit.rules.Timeout
import org.junit.runner.RunWith
import org.junit.runners.JUnit4
import public_api.PublicApi.CompletionItem
import public_api.PublicApi.CompletionRequest
import public_api.PublicApi.CompletionResponse

@RunWith(JUnit4::class)
@Suppress("UnstableApiUsage")
@TestDataPath("\$CONTENT_ROOT/src/test/testData")
class CompletionTest : AugmentBasePlatformTestCase() {
  @JvmField
  @Rule
  var globalTimeout: Timeout = Timeout.seconds(10) // 10s since CI is slow

  override fun getTestDataPath() = "src/test/testData/completion"

  override fun runInDispatchThread(): Boolean = false // to use testInlineCompletion

  override fun setUp() {
    super.setUp()
    augmentHelpers().forcePluginState(PluginState.ENABLED)
  }

  @Test
  fun testDefaultModel() =
    myFixture.testInlineCompletion {
      val completionText = "Hello from Augment!\")"
      val completionResult = generateCompletionResult(completionText)

      val mockEngine =
        MockEngine { request ->
          when (request.url.encodedPath) {
            "/get-models" -> HttpUtil.respondGetModels(this)
            "/completion" -> {
              respond(
                content = JsonFormat.printer().print(completionResult),
                status = HttpStatusCode.OK,
                headers = headersOf("Content-Type", "application/json"),
              )
            }

            "/resolve-completions" -> {
              respond(
                content = """{"success": true}""",
                headers = headersOf(HttpHeaders.ContentType, "application/json"),
              )
            }

            else -> error("Unexpected request to ${request.url.encodedPath}")
          }
        }
      augmentHelpers().registerMockEngine(mockEngine)

      myFixture.configureByFile("simple.txt")
      InlineCompletionHandler.registerTestHandler(AugmentCompletionProvider(), testRootDisposable)
      callInlineCompletion()
      delay() // so everything is processed
      insert()
      myFixture.checkResultByFile("simple.expected.txt")
    }

  @Test
  fun testAlternativeModel() =
    myFixture.testInlineCompletion {
      val mockEngine =
        MockEngine { request ->
          when (request.url.encodedPath) {
            "/get-models" -> HttpUtil.respondGetModels(this)
            "/completion" -> {
              val modelName =
                request.body.toByteArray().decodeToString().let {
                  val builder = CompletionRequest.newBuilder()
                  JsonFormat.parser().merge(it, builder)
                  builder.build().model
                }
              val completionResult =
                if (modelName == "alternative-model") {
                  generateCompletionResult("Alternative completion")
                } else {
                  generateCompletionResult("Default completion")
                }
              respond(
                content = JsonFormat.printer().print(completionResult),
                status = HttpStatusCode.OK,
                headers = headersOf("Content-Type", "application/json"),
              )
            }
            "/resolve-completions" -> {
              respond(
                content = """{"success": true}""",
                headers = headersOf(HttpHeaders.ContentType, "application/json"),
              )
            }
            else -> error("Unexpected request to ${request.url.encodedPath}")
          }
        }
      augmentHelpers().registerMockEngine(mockEngine)

      myFixture.configureByText("test.txt", "<caret>")
      InlineCompletionHandler.registerTestHandler(AugmentCompletionProvider(), testRootDisposable)

      callInlineCompletion()
      delay()
      var context = InlineCompletionContext.getOrNull(fixture.editor)
      assertEquals("Default completion", context?.textToInsert())
      escape()

      AugmentSettings.instance.modelName = "alternative-model"
      delay()
      callInlineCompletion()
      delay()
      context = InlineCompletionContext.getOrNull(fixture.editor)
      assertEquals("Alternative completion", context?.textToInsert())
    }

  @Test
  fun testUnknownMemories() =
    myFixture.testInlineCompletion {
      val completionText = "Hello from Augment!\")"
      val completionResult =
        generateCompletionResult(completionText).toBuilder()
          .addAllUnknownMemoryNames(listOf("hash(unknown.txt).v1"))
          .build()

      val mockEngine =
        MockEngine { request ->
          when (request.url.encodedPath) {
            "/get-models" -> HttpUtil.respondGetModels(this)
            "/completion" -> {
              respond(
                content = JsonFormat.printer().print(completionResult),
                status = HttpStatusCode.OK,
                headers = headersOf("Content-Type", "application/json"),
              )
            }
            "/resolve-completions" -> {
              respond(
                content = """{"success": true}""",
                headers = headersOf(HttpHeaders.ContentType, "application/json"),
              )
            }
            "/find-missing" -> {
              respond(
                content = """{"unknown_memory_names":["hash(unknown.txt).v1"],"nonindexed_blob_names":[]}""",
                headers = headersOf(HttpHeaders.ContentType, "application/json"),
              )
            }
            else -> error("Unexpected request to ${request.url.encodedPath}")
          }
        }
      augmentHelpers().registerMockEngine(mockEngine)

      val unknownFile = myFixture.addFileToProject("unknown.txt", "// unknown.txt")
      myFixture.configureByFile("simple.txt")

      // check that we initially indexed the unknown.txt file before calling the completion
      assertEquals("unknown.txt", AugmentBlobStateReader.read(unknownFile)?.relativePath)

      InlineCompletionHandler.registerTestHandler(AugmentCompletionProvider(), testRootDisposable)
      callInlineCompletion()
      delay() // so everything is processed
      insert()
      myFixture.checkResultByFile("simple.expected.txt")

      // wait and test that we reindexed the unknown file
      delay()
      assertEquals("unknown.txt", AugmentBlobStateReader.read(unknownFile)?.relativePath)
    }

  @Test
  fun testCompletionAcceptanceReported() =
    myFixture.testInlineCompletion {
      val completionResult =
        generateCompletionResult("Hello from Augment!\")")

      val gson = GsonUtil.createApiGson()
      var resolveCompletionsRequest: String? = null
      val mockEngine =
        MockEngine { request ->
          when (request.url.encodedPath) {
            "/get-models" -> HttpUtil.respondGetModels(this)
            "/completion" -> {
              respond(
                content = JsonFormat.printer().print(completionResult),
                status = HttpStatusCode.OK,
                headers = headersOf("Content-Type", "application/json"),
              )
            }
            "/resolve-completions" -> {
              resolveCompletionsRequest = request.body.toByteArray().decodeToString()
              respond(
                content = """{"success": true}""",
                headers = headersOf(HttpHeaders.ContentType, "application/json"),
              )
            }
            "/record-onboarding-session-event" -> {
              respond(
                content = """{"success": true}""",
                headers = headersOf(HttpHeaders.ContentType, "application/json"),
              )
            }
            else -> error("Unexpected request to ${request.url.encodedPath}")
          }
        }
      augmentHelpers().registerMockEngine(mockEngine)

      myFixture.configureByFile("simple.txt")
      InlineCompletionHandler.registerTestHandler(AugmentCompletionProvider(), testRootDisposable)
      callInlineCompletion()
      delay()
      insert()
      myFixture.checkResultByFile("simple.expected.txt")

      // Wait for completion to be processed
      delay()
      assertNotNull(resolveCompletionsRequest)
      val resolveRequest = gson.fromJson(resolveCompletionsRequest, ResolveCompletionsRequest::class.java)
      assertNotEmpty(resolveRequest.resolutions)
      val resolution = resolveRequest.resolutions.first()
      assertEquals(0, resolution.acceptedIdx)
      assertTrue(resolution.emitTimeSec <= resolution.resolveTimeSec)
    }

  @Test
  fun testCompletionRejectionReported() =
    myFixture.testInlineCompletion {
      val completionResult =
        generateCompletionResult("Hello from Augment!\")")

      val gson = GsonUtil.createApiGson()
      var resolveCompletionsRequest: String? = null
      val mockEngine =
        MockEngine { request ->
          when (request.url.encodedPath) {
            "/get-models" -> HttpUtil.respondGetModels(this)
            "/completion" -> {
              respond(
                content = JsonFormat.printer().print(completionResult),
                status = HttpStatusCode.OK,
                headers = headersOf("Content-Type", "application/json"),
              )
            }
            "/resolve-completions" -> {
              resolveCompletionsRequest = request.body.toByteArray().decodeToString()
              respond(
                content = """{"success": true}""",
                headers = headersOf(HttpHeaders.ContentType, "application/json"),
              )
            }
            "/record-onboarding-session-event" -> {
              respond(
                content = """{"success": true}""",
                headers = headersOf(HttpHeaders.ContentType, "application/json"),
              )
            }
            else -> error("Unexpected request to ${request.url.encodedPath}")
          }
        }
      augmentHelpers().registerMockEngine(mockEngine)

      myFixture.configureByFile("simple.txt")
      InlineCompletionHandler.registerTestHandler(AugmentCompletionProvider(), testRootDisposable)
      callInlineCompletion()
      delay()

      assertNotNull(myFixture.editor.getUserData(AugmentLastCompletionElementKey))
      escape() // to reject
      delay()
      assertNull(myFixture.editor.getUserData(AugmentLastCompletionElementKey))

      // Wait for completion to be processed
      delay()
      assertNotNull(resolveCompletionsRequest)
      val resolveRequest = gson.fromJson(resolveCompletionsRequest, ResolveCompletionsRequest::class.java)
      assertNotEmpty(resolveRequest.resolutions)
      val resolution = resolveRequest.resolutions.first()
      assertEquals(-1, resolution.acceptedIdx)
      assertTrue(resolution.emitTimeSec <= resolution.resolveTimeSec)
    }

  @Test
  fun testRecentInfo() =
    myFixture.testInlineCompletion {
      val completionText = "Hello from Augment!\")"
      val completionResult = generateCompletionResult(completionText)

      var completionRequest: CompletionRequest? = null
      val mockEngine =
        MockEngine { request ->
          when (request.url.encodedPath) {
            "/get-models" -> HttpUtil.respondGetModels(this)
            "/completion" -> {
              val completionRequestBuilder = CompletionRequest.newBuilder()
              JsonFormat.parser().merge(request.body.toByteArray().decodeToString(), completionRequestBuilder)
              completionRequest = completionRequestBuilder.build()
              respond(
                content = JsonFormat.printer().print(completionResult),
                status = HttpStatusCode.OK,
                headers = headersOf("Content-Type", "application/json"),
              )
            }
            "/resolve-completions" -> {
              respond(
                content = """{"success": true}""",
                headers = headersOf(HttpHeaders.ContentType, "application/json"),
              )
            }
            "/record-onboarding-session-event" -> {
              respond(
                content = """{"success": true}""",
                headers = headersOf(HttpHeaders.ContentType, "application/json"),
              )
            }
            "/find-missing" -> {
              respond(
                content = """{"unknown_memory_names":[],"nonindexed_blob_names":[]}""",
                headers = headersOf(HttpHeaders.ContentType, "application/json"),
              )
            }
            "/batch-upload" -> {
              respond(
                content = """{"blob_names":["644538744891fe381a6c85e4e1187b0287d7fae3b2abd98ca9b0136b66333c13"]}""",
                headers = headersOf(HttpHeaders.ContentType, "application/json"),
              )
            }
            "/checkpoint-blobs" -> {
              respond(
                content = """{"new_checkpoint_id": "checkpoint-1"}""",
                headers = headersOf(HttpHeaders.ContentType, "application/json"),
              )
            }
            else -> error("Unexpected request to ${request.url.encodedPath}")
          }
        }
      augmentHelpers().registerMockEngine(mockEngine)

      val file = myFixture.configureByFile("simple.txt")

      assertEquals("simple.txt", AugmentBlobStateReader.read(file)?.relativePath)
      val originalBlobName = AugmentBlobStateReader.read(file)?.remoteName
      val blobs = AugmentBlobStateReader.allUnfilteredIndexes(project)
      assertNotNull(blobs)
      AugmentRemoteSyncingManager.getInstance(project).findUnsynced(blobs!!)

      InlineCompletionHandler.registerTestHandler(AugmentCompletionProvider(), testRootDisposable)
      callInlineCompletion()
      delay() // so everything is processed
      insert()
      myFixture.checkResultByFile("simple.expected.txt")

      // check that we reindexed the file but not changed the remote name
      assertEquals("simple.txt", AugmentBlobStateReader.read(file)?.relativePath)
      assertNotEquals(originalBlobName, AugmentBlobStateReader.read(file)?.localName)
      assertEquals(originalBlobName, AugmentBlobStateReader.read(file)?.remoteName)

      typeChar('\n')
      callInlineCompletion()
      delay() // so everything is processed

      assertNotNull(completionRequest)
      assertEquals("644538744891fe381a6c85e4e1187b0287d7fae3b2abd98ca9b0136b66333c13", completionRequest?.blobName)
      val recentChanges = completionRequest?.recencyInfo?.recentChangesList ?: emptyList()
      assertNotEmpty(recentChanges)
      assertEquals(1, recentChanges.size)
      assertEquals("simple.txt", recentChanges.first().path)
      assertEquals("644538744891fe381a6c85e4e1187b0287d7fae3b2abd98ca9b0136b66333c13", recentChanges.first().blobName)
      assertFalse(recentChanges.first().presentInBlob)

      assertNotNull(completionRequest?.blobs)
      // TODO: checkpointing is broken in tests. Is that because we're mocking things differently somewhere or
      //       because it's actually broken? I genuinely think it's the former.
//      assertEquals("+ [644538744891fe381a6c85e4e1187b0287d7fae3b2abd98ca9b0136b66333c13] - []", completionRequest?.blobs?.checkpointId)
    }

  @Test
  fun testPartial() =
    myFixture.testInlineCompletion {
      val completionText = "package com.augment;\nimport java.util.*;"
      val completionResult = generateCompletionResult(completionText)

      val mockEngine =
        MockEngine { request ->
          when (request.url.encodedPath) {
            "/get-models" -> HttpUtil.respondGetModels(this)
            "/completion" -> {
              respond(
                content = JsonFormat.printer().print(completionResult),
                status = HttpStatusCode.OK,
                headers = headersOf("Content-Type", "application/json"),
              )
            }
            "/resolve-completions" -> {
              respond(
                content = """{"success": true}""",
                headers = headersOf(HttpHeaders.ContentType, "application/json"),
              )
            }
            "/record-onboarding-session-event" -> {
              respond(
                content = """{"success": true}""",
                headers = headersOf(HttpHeaders.ContentType, "application/json"),
              )
            }
            else -> error("Unexpected request to ${request.url.encodedPath}")
          }
        }
      augmentHelpers().registerMockEngine(mockEngine)

      myFixture.configureByText("partial.java", "<caret>")
      InlineCompletionHandler.registerTestHandler(AugmentCompletionProvider(), testRootDisposable)
      callInlineCompletion()
      delay() // so everything is processed

      val context1 = InlineCompletionContext.getOrNull(fixture.editor)
      assertEquals(completionText, context1?.textToInsert())

      callAction(IdeActions.ACTION_INSERT_INLINE_COMPLETION_WORD)
      val context2 = InlineCompletionContext.getOrNull(fixture.editor)
      assertEquals(" com.augment;\nimport java.util.*;", context2?.textToInsert())
      myFixture.checkResult("package<caret>")

      callAction(IdeActions.ACTION_INSERT_INLINE_COMPLETION_LINE)
      val context3 = InlineCompletionContext.getOrNull(fixture.editor)
      assertEquals("import java.util.*;", context3?.textToInsert())
      myFixture.checkResult("package com.augment;\n<caret>")
    }
}

fun generateCompletionResult(vararg variant: String): CompletionResponse =
  CompletionResponse.newBuilder()
    .addAllCompletionItems(
      variant.map {
        CompletionItem.newBuilder()
          .setText(it)
          .setSkippedSuffix("")
          .setSuffixReplacementText("")
          .setFilterScore(0.0f)
          .build()
      },
    )
    .build()
