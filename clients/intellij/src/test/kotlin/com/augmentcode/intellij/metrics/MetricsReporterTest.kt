package com.augmentcode.intellij.metrics

import com.augmentcode.intellij.testutils.AugmentBasePlatformTestCase
import com.augmentcode.intellij.testutils.waitForAssertion
import com.intellij.openapi.util.Disposer
import junit.framework.TestCase.*
import kotlinx.coroutines.delay
import kotlinx.coroutines.runBlocking
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4

@RunWith(JUnit4::class)
class MetricsReporterTest : AugmentBasePlatformTestCase() {
  private lateinit var reporter: TestMetricsReporter

  class TestMetricsReporter(
    private val maxRecords: Int,
    private val uploadBatchSize: Int,
    uploadIntervalMs: Long,
  ) : MetricsReporter<String>(maxRecords, uploadBatchSize, uploadIntervalMs) {
    val batchesUploaded: MutableList<List<String>> = mutableListOf()

    override suspend fun performUpload(batch: List<String>) {
      batchesUploaded.add(batch)
    }
  }

  override fun setUp() {
    super.setUp()
    reporter = TestMetricsReporter(maxRecords = 5, uploadBatchSize = 2, uploadIntervalMs = 100)
    reporter.enableUpload()
  }

  override fun tearDown() {
    Disposer.dispose(reporter)
    super.tearDown()
  }

  @Test
  fun testReportAndUpload() =
    runBlocking {
      reporter.report("1")
      reporter.report("2")
      reporter.report("3")

      waitForAssertion({
        assertEquals(3, reporter.batchesUploaded.flatten().size)
        assertEquals(listOf(listOf("1", "2"), listOf("3")), reporter.batchesUploaded)
      })
    }

  @Test
  fun testUploadTiming() =
    runBlocking {
      reporter.report("1")

      assertTrue(reporter.batchesUploaded.isEmpty())
    }

  @Test
  fun testMaxRecords() =
    runBlocking {
      for (i in 1..7) {
        reporter.report(i.toString())
      }

      waitForAssertion({
        assertEquals(5, reporter.batchesUploaded.flatten().size)
        assertEquals(listOf(listOf("3", "4"), listOf("5", "6"), listOf("7")), reporter.batchesUploaded)
      })
    }

  @Test
  fun testMaxRecordsAfterUpload() =
    runBlocking {
      for (i in 1..7) {
        reporter.report(i.toString())
      }

      delay(450) // Wait for one upload interval

      for (i in 11..17) {
        reporter.report(i.toString())
      }

      waitForAssertion({
        assertEquals(
          listOf(
            listOf("3", "4"),
            listOf("5", "6"),
            listOf("7"),
            listOf("13", "14"),
            listOf("15", "16"),
            listOf("17"),
          ),
          reporter.batchesUploaded,
        )
      })
    }
}
