package com.augmentcode.intellij.completion

import com.augmentcode.intellij.index.AugmentBlobState
import com.augmentcode.intellij.index.AugmentBlobStateReader
import com.augmentcode.intellij.index.AugmentRoot
import com.augmentcode.intellij.index.QualifiedPathName
import com.augmentcode.intellij.pluginstate.PluginState
import com.augmentcode.intellij.status.StateChangeListener
import com.augmentcode.intellij.status.StateDefinition
import com.augmentcode.intellij.status.StateDefinitions
import com.augmentcode.intellij.status.StateManager
import com.augmentcode.intellij.syncing.AugmentRemoteSyncingManager
import com.augmentcode.intellij.testutils.AugmentBasePlatformTestCase
import com.augmentcode.intellij.testutils.HttpUtil
import com.augmentcode.intellij.testutils.HttpUtil.PREFIX_CHAR_COUNT
import com.augmentcode.intellij.testutils.HttpUtil.SUFFIX_CHAR_COUNT
import com.augmentcode.intellij.testutils.waitForAssertion
import com.augmentcode.intellij.testutils.waitForAssertionAsync
import com.augmentcode.intellij.testutils.waitForAssertionSuspend
import com.augmentcode.intellij.workspacemanagement.coordination.WorkspaceCoordinatorService
import com.google.protobuf.util.JsonFormat
import com.intellij.codeInsight.inline.completion.InlineCompletionEvent
import com.intellij.codeInsight.inline.completion.InlineCompletionHandler
import com.intellij.codeInsight.inline.completion.testInlineCompletion
import com.intellij.openapi.command.WriteCommandAction
import com.intellij.openapi.vfs.VirtualFile
import com.intellij.openapi.vfs.readText
import com.intellij.psi.PsiFile
import com.intellij.testFramework.TestDataPath
import io.ktor.client.engine.mock.MockEngine
import io.ktor.client.engine.mock.respond
import io.ktor.client.engine.mock.toByteArray
import io.ktor.http.HttpHeaders
import io.ktor.http.HttpStatusCode
import io.ktor.http.headersOf
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import kotlinx.coroutines.*
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.test.runTest
import org.junit.Assert.assertNotEquals
import org.junit.Rule
import org.junit.Test
import org.junit.rules.Timeout
import org.junit.runner.RunWith
import org.junit.runners.JUnit4
import public_api.PublicApi
import public_api.PublicApi.CompletionItem
import public_api.PublicApi.CompletionRequest
import public_api.PublicApi.CompletionResponse
import java.nio.file.Path

@RunWith(JUnit4::class)
@TestDataPath("\$CONTENT_ROOT/src/test/testData")
class AugmentCompletionSuggestionIndexV2Test : AugmentBasePlatformTestCase() {
  @get:Rule
  val globalTimeout: Timeout = Timeout.seconds(10)

  override fun getTestDataPath() = "src/test/testData/suggestions"

  override fun runInDispatchThread(): Boolean = false // to use testInlineCompletion

  var mockEngine: MockEngine =
    MockEngine { _ ->
      respond(
        content = "Unexpected request",
        status = HttpStatusCode.InternalServerError,
      )
    }

  override fun setUp() {
    super.setUp()
    augmentHelpers().forcePluginState(PluginState.ENABLED)
  }

  private fun doTest(testFile: String) {
    myFixture.configureByFile(testFile)
    val currentCaret = myFixture.editor.caretModel.currentCaret

    var blobs: Collection<AugmentBlobState>? = null
    waitForAssertion({
      blobs = AugmentBlobStateReader.allUnfilteredIndexes(project)
      assertNotNull(blobs)
    })

    @Suppress("DEPRECATION")
    val completionEvent = InlineCompletionEvent.DirectCall(myFixture.editor, currentCaret)
    val completionRequest = completionEvent.toRequest()
    assertNotNull(completionRequest)
    val actualRequest =
      runBlocking {
        AugmentRemoteSyncingManager.getInstance(project).findUnsynced(blobs!!)
        AugmentCompletionSuggestion(completionRequest!!).buildAugmentCompletionRequest()
      }
    assertNotNull(actualRequest)

    val expectedRequestFileName = "${testFile.substringBeforeLast('.')}.json"
    val expectedRequestFile = myFixture.copyFileToProject(expectedRequestFileName)
    assertNotNull(expectedRequestFile)
    assertEquals(
      expectedRequestFile.readText().trim(),
      JsonFormat.printer().includingDefaultValueFields().print(actualRequest),
    )
  }

  @Test
  fun testEmptyFile() {
    doTest("emptyFile.txt")
  }

  @Test
  fun testEofEmpty() {
    doTest("eofEmpty.txt")
  }

  @Test
  fun testEofEnd() {
    doTest("eofEnd.txt")
  }

  @Test
  fun testEofMiddle() {
    doTest("eofMiddle.txt")
  }

  @Test
  fun testMarkdown() {
    doTest("readme.md")
  }

  @Test
  fun testSequenceIdIncrementsAcrossRequests() {
    myFixture.configureByText("test.txt", "hello <caret>")

    var blobs: Collection<AugmentBlobState>? = null
    waitForAssertion({
      blobs = AugmentBlobStateReader.allUnfilteredIndexes(project)
      assertNotNull(blobs)
    })

    @Suppress("DEPRECATION")
    val completionEvent = InlineCompletionEvent.DirectCall(myFixture.editor, myFixture.editor.caretModel.currentCaret)
    val completionRequest = completionEvent.toRequest()
    assertNotNull(completionRequest)

    val firstRequest =
      runBlocking {
        AugmentRemoteSyncingManager.getInstance(project).findUnsynced(blobs!!)
        AugmentCompletionSuggestion(completionRequest!!).buildAugmentCompletionRequest()
      }

    val secondRequest =
      runBlocking {
        AugmentCompletionSuggestion(completionRequest!!).buildAugmentCompletionRequest()
      }

    assertNotNull(firstRequest)
    assertNotNull(secondRequest)
    assertNotNull(firstRequest!!.sequenceId)
    assertNotNull(secondRequest!!.sequenceId)
    assertTrue(
      "Second sequence ID should be greater than first",
      secondRequest.sequenceId > firstRequest.sequenceId,
    )
  }
}

@RunWith(JUnit4::class)
@TestDataPath("\$CONTENT_ROOT/src/test/testData")
class AugmentCompletionSuggestionIndexV3Test : AugmentBasePlatformTestCase() {
//  @get:Rule
//  val globalTimeout: Timeout = Timeout.seconds(10)

  override fun getTestDataPath() = "src/test/testData/suggestions"

  override fun runInDispatchThread(): Boolean = false // to use testInlineCompletion

  override fun setUp() {
    super.setUp()
    augmentHelpers().forcePluginState(
      PluginState.ENABLED,
      augmentHelpers().createGetModelsResponse(
        PublicApi.GetModelsResponse.FeatureFlags.newBuilder()
          .setIntellijIndexingV3Enabled(true)
          .setBypassLanguageFilter(true) // This just makes it easier to test
          .build(),
      ),
    )
    augmentHelpers().registerMockEngine(HttpUtil.mockServerSync(testRootDisposable))

    // Mock AugmentRoot
    mockkObject(AugmentRoot)
    every { AugmentRoot.findFile(any<String>()) } answers {
      val file: VirtualFile = mockk(relaxed = true)
      every { file.path } returns arg<String>(0)
      file
    }
    every { AugmentRoot.findQualifiedPathName(any(), any()) } answers {
      QualifiedPathName("", arg<VirtualFile>(1).path)
    }
  }

  private suspend fun doTest(testFile: String) {
    // Make sure checkpoint is empty to start with
    val workspaceCoordinator = WorkspaceCoordinatorService.getInstance(project)
    val initialCheckpoint = workspaceCoordinator.getCheckpoint()
    assertEquals(0, initialCheckpoint.addedBlobs.size)
    assertEquals(0, initialCheckpoint.deletedBlobs.size)

    var psiFile: PsiFile? = null
    try {
      psiFile = myFixture.configureByFile(testFile)
      val indexedPath = augmentHelpers().forceIndexingForFile(psiFile.virtualFile)
      assertNotNull(indexedPath)

      val currentCaret = myFixture.editor.caretModel.currentCaret

      // Wait for full sync of file
      waitForAssertionAsync({
        val blobName = workspaceCoordinator.getSyncedBlobForFileNonBlocking(Path.of(psiFile.virtualFile.path))
        assertNotNull(blobName)
      }, 10000)
      waitForAssertionSuspend({
        val checkpoint = workspaceCoordinator.getCheckpoint()
        assertNotEquals(0, checkpoint.addedBlobs.size)
      }, 10000)

      @Suppress("DEPRECATION")
      val completionEvent = InlineCompletionEvent.DirectCall(myFixture.editor, currentCaret)
      val completionRequest = completionEvent.toRequest()
      assertNotNull(completionRequest)
      val actualRequest = AugmentCompletionSuggestion(completionRequest!!).buildAugmentCompletionRequest()
      assertNotNull(actualRequest)

      val expectedRequestFileName = "${testFile.substringBeforeLast('.')}IndexV3.json"
      val expectedRequestFile = myFixture.copyFileToProject(expectedRequestFileName)
      assertNotNull(expectedRequestFile)
      assertEquals(
        expectedRequestFile.readText().trim(),
        JsonFormat.printer()
          .includingDefaultValueFields()
          .print(actualRequest),
      )
    } finally {
      if (psiFile != null) {
        // Clean up file
        WriteCommandAction.runWriteCommandAction(project) {
          psiFile.virtualFile.delete(this)
        }
      }
    }
  }

  @Test
  fun testEmptyFile() =
    runTest {
      doTest("emptyFile.txt")
    }

  @Test
  fun testEofEmpty() =
    runTest {
      doTest("eofEmpty.txt")
    }

  @Test
  fun testEofEnd() =
    runTest {
      doTest("eofEnd.txt")
    }

  @Test
  fun testEofMiddle() =
    runTest {
      doTest("eofMiddle.txt")
    }

  @Test
  fun testMarkdown() =
    runTest {
      doTest("readme.md")
    }

  @Test
  fun testSequenceIdIncrementsAcrossRequests() =
    runTest {
      var psiFile: PsiFile? = null
      try {
        psiFile = myFixture.configureByText("test.txt", "hello <caret>")
        val indexedPath = augmentHelpers().forceIndexingForFile(psiFile.virtualFile)
        assertNotNull(indexedPath)

        // Wait for full sync of file
        val path = Path.of(psiFile.virtualFile.path)
        val workspaceCoordinator = WorkspaceCoordinatorService.getInstance(project)
        waitForAssertionAsync({
          val blobName = workspaceCoordinator.getSyncedBlobForFileNonBlocking(path)
          assertNotNull(blobName)
        }, 10000)

        @Suppress("DEPRECATION")
        val completionEvent = InlineCompletionEvent.DirectCall(myFixture.editor, myFixture.editor.caretModel.currentCaret)
        val completionRequest = completionEvent.toRequest()
        assertNotNull(completionRequest)

        val firstRequest = AugmentCompletionSuggestion(completionRequest!!).buildAugmentCompletionRequest()
        val secondRequest = AugmentCompletionSuggestion(completionRequest).buildAugmentCompletionRequest()

        assertNotNull(firstRequest)
        assertNotNull(secondRequest)
        assertNotNull(firstRequest!!.sequenceId)
        assertNotNull(secondRequest!!.sequenceId)
        assertTrue(
          "Second sequence ID should be greater than first",
          secondRequest.sequenceId > firstRequest.sequenceId,
        )
      } finally {
        if (psiFile != null) {
          WriteCommandAction.runWriteCommandAction(project) {
            psiFile.virtualFile.delete(this)
          }
        }
      }
    }
}

@Suppress("UnstableApiUsage")
@RunWith(JUnit4::class)
class AugmentCompletionSuggestionExceptions : AugmentBasePlatformTestCase() {
  override fun runInDispatchThread(): Boolean = false // to use testInlineCompletion

  var mockEngine: MockEngine =
    MockEngine { _ ->
      respond(
        content = "Unexpected request",
        status = HttpStatusCode.InternalServerError,
      )
    }

  override fun setUp() {
    super.setUp()
    augmentHelpers().forcePluginState(PluginState.ENABLED)
  }

  @Test
  @Suppress("UnstableApiUsage")
  fun testCancellationExceptionShouldBeCaught() {
    myFixture.testInlineCompletion {
      myFixture.configureByText("test.ts", "console.log('Hello <caret>'")

      mockEngine =
        MockEngine { request ->
          when (request.url.encodedPath) {
            "/get-models" -> HttpUtil.respondGetModels(this)
            "/completion" -> throw CancellationException("Injected exception")
            else -> error("Unexpected request to ${request.url.encodedPath}")
          }
        }
      augmentHelpers().registerMockEngine(mockEngine)

      InlineCompletionHandler.registerTestHandler(AugmentCompletionProvider(), testRootDisposable)
      callInlineCompletion()

      delay() // so everything is processed
      insert()
      myFixture.checkResult("console.log('Hello '")
    }
  }

  @Test
  @Suppress("UnstableApiUsage")
  fun testWrappedCancellationExceptionShouldBeCaught() {
    myFixture.testInlineCompletion {
      myFixture.configureByText("test.ts", "console.log('Hello <caret>'")

      mockEngine =
        MockEngine { request ->
          when (request.url.encodedPath) {
            "/get-models" -> HttpUtil.respondGetModels(this)
            "/completion" -> throw IllegalStateException("Wrapped", CancellationException("Injected exception"))
            else -> error("Unexpected request to ${request.url.encodedPath}")
          }
        }
      augmentHelpers().registerMockEngine(mockEngine)

      InlineCompletionHandler.registerTestHandler(AugmentCompletionProvider(), testRootDisposable)
      callInlineCompletion()

      delay() // so everything is processed
      insert()
      myFixture.checkResult("console.log('Hello '")
    }
  }

  @Test
  @Suppress("UnstableApiUsage")
  fun testIllegalStateExceptionShouldBeCaught() {
    myFixture.testInlineCompletion {
      myFixture.configureByText("test.ts", "console.log('Hello <caret>'")

      mockEngine =
        MockEngine { request ->
          when (request.url.encodedPath) {
            "/get-models" -> HttpUtil.respondGetModels(this)
            "/completion" -> throw IllegalStateException("Injected exception")
            else -> error("Unexpected request to ${request.url.encodedPath}")
          }
        }
      augmentHelpers().registerMockEngine(mockEngine)

      InlineCompletionHandler.registerTestHandler(AugmentCompletionProvider(), testRootDisposable)
      callInlineCompletion()

      delay() // so everything is processed
      insert()
      myFixture.checkResult("console.log('Hello '")
    }
  }

  @Test
  @Suppress("UnstableApiUsage")
  fun testExceptionShouldBeCaught() {
    myFixture.testInlineCompletion {
      myFixture.configureByText("test.ts", "console.log('Hello <caret>'")

      mockEngine =
        MockEngine { request ->
          when (request.url.encodedPath) {
            "/get-models" -> HttpUtil.respondGetModels(this)
            "/completion" -> throw Exception("Injected exception")
            else -> error("Unexpected request to ${request.url.encodedPath}")
          }
        }
      augmentHelpers().registerMockEngine(mockEngine)

      InlineCompletionHandler.registerTestHandler(AugmentCompletionProvider(), testRootDisposable)
      callInlineCompletion()

      delay() // so everything is processed
      insert()
      myFixture.checkResult("console.log('Hello '")
    }
  }
}

@Suppress("UnstableApiUsage")
@RunWith(JUnit4::class)
class AugmentCompletionSuggestionWithStatusTest : AugmentBasePlatformTestCase() {
  override fun runInDispatchThread(): Boolean = false // to use testInlineCompletion

  var mockEngine: MockEngine =
    MockEngine { _ ->
      respond(
        content = "Unexpected request",
        status = HttpStatusCode.InternalServerError,
      )
    }

  override fun setUp() {
    super.setUp()
    augmentHelpers().forcePluginState(PluginState.ENABLED)
  }

  @Test
  @Suppress("UnstableApiUsage")
  fun testSkipTokenCompletion() {
    myFixture.testInlineCompletion {
      myFixture.configureByText("test.ts", "console.log('Hello <caret>'")

      // Now allow the secondary inline completion call to proceed and accept it
      val completionResponse =
        CompletionResponse.newBuilder()
          .addCompletionItems(
            CompletionItem.newBuilder()
              .setText("Augment")
              .setSkippedSuffix("'")
              .setSuffixReplacementText("');")
              .build(),
          )
          .build()
      mockEngine =
        MockEngine { request ->
          when (request.url.encodedPath) {
            "/get-models" -> HttpUtil.respondGetModels(this)
            "/completion" -> {
              respond(
                content = JsonFormat.printer().print(completionResponse),
                status = HttpStatusCode.OK,
                headers = headersOf("Content-Type", "application/json"),
              )
            }
            "/resolve-completions" -> {
              respond(
                content = """{}""",
                headers = headersOf(HttpHeaders.ContentType, "application/json"),
              )
            }
            else -> error("Unexpected request to ${request.url.encodedPath}")
          }
        }
      augmentHelpers().registerMockEngine(mockEngine)

      InlineCompletionHandler.registerTestHandler(AugmentCompletionProvider(), testRootDisposable)
      callInlineCompletion()

      delay() // so everything is processed
      insert()
      myFixture.checkResult("console.log('Hello Augment');")
    }
  }

  @Test
  @Suppress("UnstableApiUsage")
  fun testStateManagerCompletionChanges() {
    myFixture.testInlineCompletion {
      myFixture.configureByText("test.ts", "console.log('Hello <caret>'")

      // Now allow the secondary inline completion call to proceed and accept it
      val completionResponse =
        CompletionResponse.newBuilder()
          .addCompletionItems(
            CompletionItem.newBuilder()
              .setText("Augment")
              .setSkippedSuffix("'")
              .setSuffixReplacementText("');")
              .build(),
          )
          .build()
      mockEngine =
        MockEngine { request ->
          when (request.url.encodedPath) {
            "/get-models" -> HttpUtil.respondGetModels(this)
            "/completion" -> {
              respond(
                content = JsonFormat.printer().print(completionResponse),
                status = HttpStatusCode.OK,
                headers = headersOf("Content-Type", "application/json"),
              )
            }
            "/resolve-completions" -> {
              respond(
                content = """{}""",
                headers = headersOf(HttpHeaders.ContentType, "application/json"),
              )
            }
            else -> error("Unexpected request to ${request.url.encodedPath}")
          }
        }
      augmentHelpers().registerMockEngine(mockEngine)

      InlineCompletionHandler.registerTestHandler(AugmentCompletionProvider(), testRootDisposable)
      assertEquals(StateDefinitions.Base, StateManager.getInstance(project).getPriorityState())

      val connection = myFixture.project.messageBus.connect(testRootDisposable)
      val states = mutableListOf<StateDefinition>()
      connection.subscribe(
        StateChangeListener.TOPIC,
        object : StateChangeListener {
          override fun onChange(state: StateDefinition) {
            states += state
          }
        },
      )

      // Call completion resulting in generating completion -> base state changes
      callInlineCompletion()

      delay() // so everything is processed
      insert()
      myFixture.checkResult("console.log('Hello Augment');")

      // Because we trigger a follow-on completion, we may get addition generation state changes
      // so just ensure we get a generating completion followed by a base state change
      assertTrue(states.size >= 2)
      assertEquals(StateDefinitions.GeneratingCompletion, states[0])
      assertEquals(StateDefinitions.Base, states[1])
    }
  }

  @Test
  @Suppress("UnstableApiUsage")
  fun testStateManagerZeroCompletionChanges() {
    myFixture.testInlineCompletion {
      myFixture.configureByText("test-no-completions.ts", "console.log('Hello <caret>'")

      val completionResponse = CompletionResponse.newBuilder().build()
      mockEngine =
        MockEngine { request ->
          when (request.url.encodedPath) {
            "/get-models" -> HttpUtil.respondGetModels(this)
            "/completion" -> {
              respond(
                content = JsonFormat.printer().print(completionResponse),
                status = HttpStatusCode.OK,
                headers = headersOf("Content-Type", "application/json"),
              )
            }
            "/resolve-completions" -> {
              respond(
                content = """{}""",
                headers = headersOf(HttpHeaders.ContentType, "application/json"),
              )
            }
            else -> error("Unexpected request to ${request.url.encodedPath}")
          }
        }
      augmentHelpers().registerMockEngine(mockEngine)

      InlineCompletionHandler.registerTestHandler(AugmentCompletionProvider(), testRootDisposable)
      assertEquals(StateDefinitions.Base, StateManager.getInstance(project).getPriorityState())

      val connection = myFixture.project.messageBus.connect(testRootDisposable)
      val states = mutableListOf<StateDefinition>()
      connection.subscribe(
        StateChangeListener.TOPIC,
        object : StateChangeListener {
          override fun onChange(state: StateDefinition) {
            states += state
          }
        },
      )
      // Trigger completion causing generating completion -> no completions -> base state changes
      callInlineCompletion()

      delay() // so everything is processed

      assertTrue(states.size >= 2)
      assertEquals(StateDefinitions.GeneratingCompletion, states[0])
      assertEquals(StateDefinitions.NoCompletions, states[1])
    }
  }

  @Test
  @Suppress("UnstableApiUsage")
  fun testPrefixAndSuffixInRequestForCompletion() {
    myFixture.testInlineCompletion {
      // The prefix should be all "b" and the suffix should be all "c"
      myFixture.configureByText("test.ts", "a${"b".repeat(PREFIX_CHAR_COUNT)}<caret>${"c".repeat(SUFFIX_CHAR_COUNT)}d")

      // Now allow the secondary inline completion call to proceed and accept it
      val completionResponse =
        CompletionResponse.newBuilder()
          .addCompletionItems(
            CompletionItem.newBuilder()
              .setText("Augment")
              .setSkippedSuffix("")
              .setSuffixReplacementText("")
              .build(),
          )
          .build()
      var completionRequest: CompletionRequest? = null
      mockEngine =
        MockEngine { request ->
          when (request.url.encodedPath) {
            "/get-models" -> HttpUtil.respondGetModels(this)
            "/completion" -> {
              // Only respond to the first completion request (a second completion request can be triggered by intellij
              // after the first completion is accepted)
              if (completionRequest == null) {
                val builder = CompletionRequest.newBuilder()
                JsonFormat.parser().merge(request.body.toByteArray().decodeToString(), builder)
                completionRequest = builder.build()
                respond(
                  content = JsonFormat.printer().print(completionResponse),
                  status = HttpStatusCode.OK,
                  headers = headersOf("Content-Type", "application/json"),
                )
              } else {
                respond(
                  content = "Unexpected request",
                  status = HttpStatusCode.InternalServerError,
                )
              }
            }
            "/resolve-completions" -> {
              respond(
                content = """{}""",
                headers = headersOf(HttpHeaders.ContentType, "application/json"),
              )
            }
            "/record-onboarding-session-event" -> HttpUtil.respondOK(this)
            else -> error("Unexpected request to ${request.url.encodedPath}")
          }
        }
      augmentHelpers().registerMockEngine(mockEngine)

      InlineCompletionHandler.registerTestHandler(AugmentCompletionProvider(), testRootDisposable)
      callInlineCompletion()

      delay() // so everything is processed
      insert()
      myFixture.checkResult("a${"b".repeat(PREFIX_CHAR_COUNT)}Augment${"c".repeat(SUFFIX_CHAR_COUNT)}d")

      assertNotNull(completionRequest)
      assertEquals("b".repeat(PREFIX_CHAR_COUNT), completionRequest?.prompt)
      assertEquals("c".repeat(SUFFIX_CHAR_COUNT), completionRequest?.suffix)

      // Check the offsets in the completion request are correct
      // Prefix begin is after "a" - so offset 1
      assertEquals(1, completionRequest?.prefixBegin)
      // Cursor position is after "a" and prefix of "b".repeat(PREFIX_CHAR_COUNT)
      assertEquals(1 + PREFIX_CHAR_COUNT, completionRequest?.cursorPosition)
      // Suffix end is after "a", prefix of "b".repeat(PREFIX_CHAR_COUNT), and suffix of "c".repeat(SUFFIX_CHAR_COUNT)
      assertEquals(1 + PREFIX_CHAR_COUNT + SUFFIX_CHAR_COUNT, completionRequest?.suffixEnd)
    }
  }

  @Test
  @Suppress("UnstableApiUsage")
  fun testUnicodePrefixAndSuffixInRequestForCompletion() {
    myFixture.testInlineCompletion {
      val multiByteUnicodeChar = "\uD83D\uDE00" // Grinning Face Emoji
      // The prefix should be "😀bbbb..." and the suffix should be "cccc...😀" where b and c satisfy the char count
      myFixture.configureByText(
        "test.ts",
        "a${multiByteUnicodeChar}${"b".repeat(PREFIX_CHAR_COUNT - 1)}<caret>${"c".repeat(SUFFIX_CHAR_COUNT - 1)}${multiByteUnicodeChar}d",
      )

      // Now allow the secondary inline completion call to proceed and accept it
      val completionResponse =
        CompletionResponse.newBuilder()
          .addCompletionItems(
            CompletionItem.newBuilder()
              .setText("Augment")
              .setSkippedSuffix("")
              .setSuffixReplacementText("")
              .build(),
          )
          .build()
      var completionRequest: CompletionRequest? = null
      mockEngine =
        MockEngine { request ->
          when (request.url.encodedPath) {
            "/get-models" -> HttpUtil.respondGetModels(this)
            "/completion" -> {
              // Only respond to the first completion request (a second completion request can be triggered by intellij
              // after the first completion is accepted)
              if (completionRequest == null) {
                val builder = CompletionRequest.newBuilder()
                JsonFormat.parser().merge(request.body.toByteArray().decodeToString(), builder)
                completionRequest = builder.build()
                respond(
                  content = JsonFormat.printer().print(completionResponse),
                  status = HttpStatusCode.OK,
                  headers = headersOf("Content-Type", "application/json"),
                )
              } else {
                respond(
                  content = "Unexpected request",
                  status = HttpStatusCode.InternalServerError,
                )
              }
            }
            "/resolve-completions" -> {
              respond(
                content = """{}""",
                headers = headersOf(HttpHeaders.ContentType, "application/json"),
              )
            }
            else -> error("Unexpected request to ${request.url.encodedPath}")
          }
        }
      augmentHelpers().registerMockEngine(mockEngine)

      InlineCompletionHandler.registerTestHandler(AugmentCompletionProvider(), testRootDisposable)
      callInlineCompletion()

      delay() // so everything is processed
      insert()
      myFixture.checkResult(
        "a${multiByteUnicodeChar}${"b".repeat(PREFIX_CHAR_COUNT - 1)}Augment${"c".repeat(SUFFIX_CHAR_COUNT - 1)}${multiByteUnicodeChar}d",
      )

      assertNotNull(completionRequest)
      assertEquals("b".repeat(PREFIX_CHAR_COUNT - 1), completionRequest?.prompt)
      assertEquals("c".repeat(SUFFIX_CHAR_COUNT - 1), completionRequest?.suffix)

      // Check the offsets in the completion request are correct
      // Prefix begin is after "a" and the multibyte Unicode char - so offset 3
      assertEquals(3, completionRequest?.prefixBegin)
      // Cursor position is after "a" + multibyte Unicode char and prefix of "b".repeat(PREFIX_CHAR_COUNT - 1)
      assertEquals(3 + PREFIX_CHAR_COUNT - 1, completionRequest?.cursorPosition)
      // Suffix end is after "a" + multibyte Unicode char and prefix of "b".repeat(PREFIX_CHAR_COUNT - 1), and suffix of "c".repeat(SUFFIX_CHAR_COUNT - 1)
      assertEquals(3 + PREFIX_CHAR_COUNT - 1 + SUFFIX_CHAR_COUNT - 1, completionRequest?.suffixEnd)
    }
  }
}
