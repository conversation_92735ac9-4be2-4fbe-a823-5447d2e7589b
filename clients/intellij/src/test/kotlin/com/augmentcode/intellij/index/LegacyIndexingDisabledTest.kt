package com.augmentcode.intellij.index

import com.augmentcode.intellij.index.utils.isLegacyIndexingDisabled
import com.augmentcode.intellij.pluginstate.PluginState
import com.augmentcode.intellij.testutils.AugmentBasePlatformTestCase
import com.augmentcode.intellij.utils.CustomPropertyReader
import com.intellij.testFramework.IndexingTestUtil
import com.intellij.testFramework.TestDataPath
import io.mockk.*
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4

@RunWith(JUnit4::class)
@TestDataPath("\$CONTENT_ROOT/src/test/testData")
class LegacyIndexingDisabledTest : AugmentBasePlatformTestCase() {
  private var originalPropertyValue: String? = null

  override fun getTestDataPath() = "src/test/testData/indexing"

  override fun setUp() {
    super.setUp()
    augmentHelpers().forcePluginState(PluginState.ENABLED)

    // Save the original property value
    originalPropertyValue = System.getProperty(CustomPropertyReader.LEGACY_INDEXING_DISABLED_PROPERTY)

    // Mock the isLegacyIndexingDisabled function
    mockkStatic("com.augmentcode.intellij.index.utils.LegacyIndexingFlagsKt")
  }

  override fun tearDown() {
    // Restore the original property value
    if (originalPropertyValue != null) {
      System.setProperty(CustomPropertyReader.LEGACY_INDEXING_DISABLED_PROPERTY, originalPropertyValue!!)
    } else {
      System.clearProperty(CustomPropertyReader.LEGACY_INDEXING_DISABLED_PROPERTY)
    }
    // Clear all mocks
    unmockkAll()
    super.tearDown()
  }

  @Test
  fun testIndexingSkippedWhenLegacyIndexingDisabled() {
    // Mock isLegacyIndexingDisabled to return true for disabled
    every { isLegacyIndexingDisabled() } returns true

    // Create test files
    val file1 = myFixture.addFileToProject("test1.txt", "content1")
    val file2 = myFixture.addFileToProject("test2.go", "package main")

    // Wait for indexing to complete
    IndexingTestUtil.waitUntilIndexesAreReady(project)

    // Verify that files are not indexed when legacy indexing is disabled
    assertNull("File should not be indexed when legacy indexing is disabled", AugmentBlobStateReader.read(file1))
    assertNull("File should not be indexed when legacy indexing is disabled", AugmentBlobStateReader.read(file2))

    // Also verify the input filter behavior
    val index = AugmentLocalIndex()
    val filter = index.inputFilter
    assertFalse("Filter should reject all files when legacy indexing is disabled", filter.acceptInput(file1.virtualFile))
    assertFalse("Filter should reject all files when legacy indexing is disabled", filter.acceptInput(file2.virtualFile))
  }

  @Test
  fun testIndexingWorksWhenLegacyIndexingEnabled() {
    // Mock isLegacyIndexingDisabled to return false for disabled (i.e., enabled)
    every { isLegacyIndexingDisabled() } returns false

    // Create test files
    val file1 = myFixture.addFileToProject("test1.txt", "content1")
    val file2 = myFixture.addFileToProject("test2.go", "package main")

    // Wait for indexing to complete
    IndexingTestUtil.waitUntilIndexesAreReady(project)

    // Verify that files are indexed when legacy indexing is enabled
    assertEquals("test1.txt", AugmentBlobStateReader.read(file1)?.relativePath)
    assertEquals("test2.go", AugmentBlobStateReader.read(file2)?.relativePath)

    // Also verify the input filter behavior
    val index = AugmentLocalIndex()
    val filter = index.inputFilter
    assertTrue("Filter should accept non-binary files when legacy indexing is enabled", filter.acceptInput(file1.virtualFile))
    assertTrue("Filter should accept non-binary files when legacy indexing is enabled", filter.acceptInput(file2.virtualFile))
  }

  @Test
  fun testInputFilterReturnsEmptyWhenLegacyIndexingDisabled() {
    // Mock isLegacyIndexingDisabled to return true for disabled
    every { isLegacyIndexingDisabled() } returns true

    val index = AugmentLocalIndex()
    val filter = index.inputFilter

    // Create real test files
    val file1 = myFixture.addFileToProject("test1.txt", "content1")
    val file2 = myFixture.addFileToProject("test2.go", "package main")

    // Verify that the filter rejects all files when legacy indexing is disabled
    assertFalse("Filter should reject all files when legacy indexing is disabled", filter.acceptInput(file1.virtualFile))
    assertFalse("Filter should reject all files when legacy indexing is disabled", filter.acceptInput(file2.virtualFile))
  }

  @Test
  fun testInputFilterWorksNormallyWhenLegacyIndexingEnabled() {
    // Mock isLegacyIndexingDisabled to return false for disabled (i.e., enabled)
    every { isLegacyIndexingDisabled() } returns false

    val index = AugmentLocalIndex()
    val filter = index.inputFilter

    // Create real test files
    val textFile = myFixture.addFileToProject("test.txt", "content")
    val goFile = myFixture.addFileToProject("test.go", "package main")

    // Verify that the filter works normally when legacy indexing is enabled
    assertTrue("Filter should accept non-binary files when legacy indexing is enabled", filter.acceptInput(textFile.virtualFile))
    assertTrue("Filter should accept non-binary files when legacy indexing is enabled", filter.acceptInput(goFile.virtualFile))
  }
}
