@file:Suppress("DEPRECATION") // We use MockAugmentAPI

package com.augmentcode.intellij.chat

import com.augmentcode.api.ChatRequest
import com.augmentcode.intellij.memories.MemoriesService
import com.augmentcode.intellij.pluginstate.PluginState
import com.augmentcode.intellij.settings.AugmentSettings
import com.augmentcode.intellij.testutils.AugmentBasePlatformTestCase
import com.augmentcode.intellij.testutils.GsonUtil
import com.augmentcode.intellij.testutils.HttpUtil
import com.augmentcode.intellij.testutils.waitForAssertion
import com.augmentcode.rpc.ChatUserMessageRequest
import com.intellij.testFramework.TestDataPath
import io.ktor.client.engine.mock.*
import io.ktor.http.*
import io.ktor.utils.io.*
import kotlinx.coroutines.flow.toList
import kotlinx.coroutines.test.runTest
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4

@RunWith(JUnit4::class)
@TestDataPath("\$CONTENT_ROOT/src/test/testData")
class ChatRequestTest : AugmentBasePlatformTestCase() {
  override fun getTestDataPath() = "src/test/testData/chat"

  override fun setUp() {
    super.setUp()
    augmentHelpers().forcePluginState(PluginState.ENABLED)
  }

  @Test
  fun testNoSelection() =
    runTest {
      val chatService = ChatWebviewMessageClient(project, backgroundScope)

      myFixture.configureByText("simple.txt", "line 1<caret>")

      val chatRequest = chatService.baseChatRequestFromSelection(myFixture.editor)
      assertEquals(null, chatRequest.selectedCode)
    }

  @Test
  fun testSelectionWholeFile() =
    runTest {
      val chatService = ChatWebviewMessageClient(project, backgroundScope)

      myFixture.configureByText("simple.txt", "<selection>line 1\nline 2\nline 3\n</selection>")

      val chatRequest = chatService.baseChatRequestFromSelection(myFixture.editor)
      assertEquals("Text", chatRequest.lang)
      assertEquals("simple.txt", chatRequest.path)
      assertEquals("line 1\nline 2\nline 3\n", chatRequest.selectedCode)
      assertEquals("", chatRequest.prefix)
      assertEquals("", chatRequest.suffix)
    }

  @Test
  fun testSelectionPrefix() =
    runTest {
      val chatService = ChatWebviewMessageClient(project, backgroundScope)

      myFixture.configureByText("simple.txt", "<selection>line 1</selection>\nline 2\nline 3\n")

      val chatRequest = chatService.baseChatRequestFromSelection(myFixture.editor)
      assertEquals("Text", chatRequest.lang)
      assertEquals("simple.txt", chatRequest.path)
      assertEquals("", chatRequest.prefix)
      assertEquals("line 1", chatRequest.selectedCode)
      assertEquals("\nline 2\nline 3\n", chatRequest.suffix)
    }

  @Test
  fun testSelectionMiddle() =
    runTest {
      val chatService = ChatWebviewMessageClient(project, backgroundScope)

      myFixture.configureByText("simple.txt", "line 1\n<selection>line 2</selection>\nline 3\n")

      val chatRequest = chatService.baseChatRequestFromSelection(myFixture.editor)
      assertEquals("Text", chatRequest.lang)
      assertEquals("simple.txt", chatRequest.path)
      assertEquals("line 1\n", chatRequest.prefix)
      assertEquals("line 2", chatRequest.selectedCode)
      assertEquals("\nline 3\n", chatRequest.suffix)
    }

  @Test
  fun testSelectionSuffix() =
    runTest {
      val chatService = ChatWebviewMessageClient(project, backgroundScope)

      myFixture.configureByText("simple.txt", "line 1\nline 2\n<selection>line 3\n</selection>")

      val chatRequest = chatService.baseChatRequestFromSelection(myFixture.editor)
      assertEquals("Text", chatRequest.lang)
      assertEquals("simple.txt", chatRequest.path)
      assertEquals("line 1\nline 2\n", chatRequest.prefix)
      assertEquals("line 3\n", chatRequest.selectedCode)
      assertEquals("", chatRequest.suffix)
    }

  @Test
  fun testSelectionLargeFile() =
    runTest {
      val chatService = ChatWebviewMessageClient(project, backgroundScope)

      val lineLength20 = "very important line\n"

      myFixture.configureByText(
        "simple.txt",
        lineLength20.repeat(1_000) + "<caret>" + lineLength20.repeat(1_000),
      )

      val chatRequest = chatService.baseChatRequestFromSelection(myFixture.editor)
      assertEquals("Text", chatRequest.lang)
      assertEquals(HttpUtil.PREFIX_CHAR_COUNT, chatRequest.prefix.length)
      assertEquals(HttpUtil.SUFFIX_CHAR_COUNT, chatRequest.suffix.length)
    }

  @Test
  fun testMemories() =
    runTest {
      // set api token and completion url
      AugmentSettings.instance.apiToken = "test-token"
      AugmentSettings.instance.completionURL = "http://test-server"

      // MockEngine
      val mockEngine =
        MockEngine { request ->
          when (request.url.encodedPath) {
            "/chat-stream" -> {
              respond(
                content = ByteReadChannel(""),
                status = HttpStatusCode.OK,
                headers = headersOf(HttpHeaders.ContentType, "application/json"),
              )
            }
            "/get-models" -> HttpUtil.respondGetModels(this)
            "/record-onboarding-session-event" -> HttpUtil.respondOK(this)
            else ->
              respond(
                content = "Other request",
                status = HttpStatusCode.OK,
                headers = headersOf(HttpHeaders.ContentType, "application/json"),
              )
          }
        }
      HttpUtil.registerMockHttpClient(mockEngine, testRootDisposable)

      MemoriesService.getInstance(project).updateMemories("foo")

      val chatService = ChatWebviewMessageClient(project, backgroundScope)
      chatService.chatUserMessage(
        ChatUserMessageRequest.newBuilder()
          .build(),
      ).toList()

      waitForAssertion({
        assert(mockEngine.requestHistory.count { it.url.encodedPath == "/chat-stream" } == 1)
      })
      val request = mockEngine.requestHistory.first { it.url.encodedPath == "/chat-stream" }
      val gson = GsonUtil.createApiGson()
      val chatRequest = gson.fromJson(request.body.toByteArray().decodeToString(), ChatRequest::class.java)
      assert(chatRequest.agent_memories == "foo")
    }
}
