package com.augmentcode.intellij.workspacemanagement.coordination.steps

import com.augmentcode.intellij.completion.AugmentCompletionSuggestion
import com.augmentcode.intellij.index.AugmentRoot
import com.augmentcode.intellij.index.QualifiedPathName
import com.augmentcode.intellij.pluginstate.PluginState
import com.augmentcode.intellij.testutils.AugmentBasePlatformTestCase
import com.augmentcode.intellij.testutils.waitForAssertion
import com.augmentcode.intellij.utils.IndexUtil.expectedBlobName
import com.augmentcode.intellij.utils.IndexUtil.normalizedText
import com.augmentcode.intellij.workspacemanagement.coordination.BlobNameService
import com.augmentcode.intellij.workspacemanagement.utils.RoughlySizedChannel
import com.intellij.codeInsight.inline.completion.InlineCompletionEvent
import com.intellij.openapi.fileEditor.FileEditorManager
import com.intellij.openapi.util.Disposer
import com.intellij.openapi.vfs.VirtualFile
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.test.runTest
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4
import public_api.PublicApi

@RunWith(JUnit4::class)
class MemorizeStepTest : AugmentBasePlatformTestCase() {
  private lateinit var memorizeChannel: RoughlySizedChannel<VirtualFile>
  private lateinit var memorizeStep: MemorizeStep

  override fun getTestDataPath() = "src/test/testData/suggestions"

  override fun runInDispatchThread(): Boolean = false // to use testInlineCompletion

  override fun setUp() {
    super.setUp()
    memorizeChannel = RoughlySizedChannel(Channel(Channel.UNLIMITED))
    memorizeStep = MemorizeStep(project, augmentHelpers().createCoroutineScope(Dispatchers.IO), memorizeChannel)

    augmentHelpers().forcePluginState(
      PluginState.ENABLED,
      augmentHelpers().createGetModelsResponse(
        PublicApi.GetModelsResponse.FeatureFlags.newBuilder()
          .setIntellijIndexingV3Enabled(true)
          .setBypassLanguageFilter(true) // This just makes it easier to test
          .build(),
      ),
    )

    mockkObject(AugmentRoot)
    every { AugmentRoot.findFile(any<String>()) } answers {
      val file: VirtualFile = mockk(relaxed = true)
      every { file.path } returns arg<String>(0)
      file
    }
    every { AugmentRoot.findQualifiedPathName(any(), any()) } answers {
      QualifiedPathName("/src", arg<VirtualFile>(1).path.removePrefix("/src/"))
    }
  }

  override fun tearDown() {
    Disposer.dispose(memorizeStep)
    super.tearDown()
  }

  @Test
  fun testProcessFilesFromChannelAndValidateRecencyInfo() =
    runTest {
      // open a file in the editor
      val psiFile = myFixture.configureByText("test.txt", "hello <caret>")

      // Mock that blob has already been uploaded
      val relPath = "test.txt"
      val content = normalizedText(psiFile.virtualFile)
      assertNotNull(content)
      content!!
      val expectedBlobName = expectedBlobName(relPath, content)
      BlobNameService.getInstance(project).put(expectedBlobName, psiFile.virtualFile.path)

      // Assert file is open in editor
      assertNotNull(FileEditorManager.getInstance(project).getSelectedEditor(psiFile.virtualFile))
      // assert with getEditors is not empty
      assertNotNull(FileEditorManager.getInstance(project).getEditors(psiFile.virtualFile).firstOrNull())

      // send the file to the channel
      memorizeChannel.send(psiFile.virtualFile)
      memorizeStep.startProcessing()

      // wait for the job to process the file
      waitForAssertion({
        assertTrue(memorizeChannel.isEmpty)
      })

      // Edit the file
      myFixture.type(" more content")

      // Get augment completion request
      @Suppress("DEPRECATION")
      val completionEvent = InlineCompletionEvent.DirectCall(myFixture.editor, myFixture.editor.caretModel.currentCaret)
      val completionRequest = completionEvent.toRequest()
      assertNotNull(completionRequest)
      val actualRequest = AugmentCompletionSuggestion(completionRequest!!).buildAugmentCompletionRequest()
      assertNotNull(actualRequest)
      actualRequest!!

      // Check that recency info matches expectation
      assertEquals(1, actualRequest.recencyInfo.recentChangesList.size)
      val recentChange = actualRequest.recencyInfo.recentChangesList[0]
      assertEquals(expectedBlobName, recentChange.blobName)
      assertEquals(relPath, recentChange.path)
      assertEquals(0, recentChange.charStart)
      assertEquals(7, recentChange.charEnd)
      assertEquals("hello  more content\n", recentChange.replacementText)
      assertEquals(false, recentChange.presentInBlob)
    }
}
