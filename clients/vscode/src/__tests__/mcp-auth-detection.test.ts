/* eslint-disable @typescript-eslint/naming-convention */

/**
 * Unit tests for MCP authentication detection flow.
 */
import { OAuthMetadata } from "@augment-internal/sidecar-libs/src/tools/oauth-types";

import { SettingsWebviewPanel } from "../webview-panels/settings-webview-panel";
import { ToolConfigStore } from "../webview-panels/stores/tool-config-store";
import { MCPServerHttp } from "../webview-providers/webview-messages";

// Mock the SettingsWebviewPanel
jest.mock("../webview-panels/settings-webview-panel");

describe("MCP Authentication Detection", () => {
    let mockToolConfigStore: ToolConfigStore;

    beforeEach(() => {
        // Mock the tool config store
        mockToolConfigStore = {
            getMCPServers: jest.fn(),
            saveMCPServers: jest.fn(),
        } as any;

        // Mock SettingsWebviewPanel
        const mockPanel = {
            refreshMCPServers: jest.fn(),
        };
        (SettingsWebviewPanel as any).currentPanel = mockPanel;
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    describe("Authentication Detection Logic", () => {
        it("should mark HTTP MCP server as requiring authentication", async () => {
            // Setup test data
            const testServerUrl = "https://example.com/mcp";
            const mockServers: MCPServerHttp[] = [
                {
                    id: "test-server-1",
                    type: "http",
                    name: "Test Server",
                    url: testServerUrl,
                    disabled: false,
                    tools: [],
                },
                {
                    id: "test-server-2",
                    type: "http",
                    name: "Other Server",
                    url: "https://other.com/mcp",
                    disabled: false,
                    tools: [],
                },
            ];

            // Mock the getMCPServers to return our test servers
            (mockToolConfigStore.getMCPServers as jest.Mock).mockResolvedValue(mockServers);
            (mockToolConfigStore.saveMCPServers as jest.Mock).mockResolvedValue(undefined);

            // Simulate the authentication detection logic
            const currentServers = await mockToolConfigStore.getMCPServers();
            const serverToUpdate = currentServers.find((server) =>
                server.type === "http" || server.type === "sse"
                    ? server.url === testServerUrl
                    : false
            );

            expect(serverToUpdate).toBeDefined();
            expect((serverToUpdate as MCPServerHttp)?.url).toBe(testServerUrl);

            // Update the server to mark authentication as required
            const updatedServers = currentServers.map((server) => {
                if (
                    server.id === serverToUpdate?.id &&
                    (server.type === "http" || server.type === "sse")
                ) {
                    return {
                        ...server,
                        authRequired: true,
                    };
                }
                return server;
            });

            await mockToolConfigStore.saveMCPServers(updatedServers);

            // Verify that saveMCPServers was called with the updated servers
            expect(mockToolConfigStore.saveMCPServers).toHaveBeenCalledWith([
                {
                    id: "test-server-1",
                    type: "http",
                    name: "Test Server",
                    url: testServerUrl,
                    disabled: false,
                    tools: [],
                    authRequired: true, // This should be added
                },
                {
                    id: "test-server-2",
                    type: "http",
                    name: "Other Server",
                    url: "https://other.com/mcp",
                    disabled: false,
                    tools: [],
                },
            ]);
        });

        it("should handle server not found gracefully", async () => {
            // Setup test data with no matching server
            const testServerUrl = "https://nonexistent.com/mcp";
            const mockServers: MCPServerHttp[] = [
                {
                    id: "test-server-1",
                    type: "http",
                    name: "Test Server",
                    url: "https://example.com/mcp",
                    disabled: false,
                    tools: [],
                },
            ];

            // Mock the getMCPServers to return servers without the target URL
            (mockToolConfigStore.getMCPServers as jest.Mock).mockResolvedValue(mockServers);

            // Simulate the authentication detection logic
            const currentServers = await mockToolConfigStore.getMCPServers();
            const serverToUpdate = currentServers.find((server) =>
                server.type === "http" || server.type === "sse"
                    ? server.url === testServerUrl
                    : false
            );

            // Verify that no server was found
            expect(serverToUpdate).toBeUndefined();
        });
    });

    describe("OAuth Metadata Discovery", () => {
        // Mock fetch globally for these tests
        const mockFetch = jest.fn();
        global.fetch = mockFetch;

        beforeEach(() => {
            mockFetch.mockClear();
        });

        it("should discover OAuth metadata from well-known endpoint", async () => {
            const testServerUrl = "https://example.com/mcp";
            const mockOAuthMetadata: OAuthMetadata = {
                // eslint-disable-next-line @typescript-eslint/naming-convention
                authorization_endpoint: "https://example.com/oauth/authorize",
                // eslint-disable-next-line @typescript-eslint/naming-convention
                token_endpoint: "https://example.com/oauth/token",
                issuer: "https://example.com",
                // eslint-disable-next-line @typescript-eslint/naming-convention
                scopes_supported: ["read", "write"],
                // eslint-disable-next-line @typescript-eslint/naming-convention
                response_types_supported: ["code"],
                // eslint-disable-next-line @typescript-eslint/naming-convention
                grant_types_supported: ["authorization_code"],
                // eslint-disable-next-line @typescript-eslint/naming-convention
                code_challenge_methods_supported: ["S256"],
            };

            // Mock successful response from the first discovery endpoint
            mockFetch.mockResolvedValueOnce({
                ok: true,
                json: jest.fn().mockResolvedValue(mockOAuthMetadata),
            });

            // Test the discovery logic (this would be part of the extension method)
            const baseUrl = new URL(testServerUrl);
            const discoveryEndpoint = "/.well-known/oauth-authorization-server";
            const metadataUrl = new URL(discoveryEndpoint, baseUrl).toString();

            const response = await fetch(metadataUrl);
            const metadata = await response.json();

            // Verify the fetch was called with correct URL
            expect(mockFetch).toHaveBeenCalledWith(metadataUrl);

            // Verify the metadata structure
            expect(metadata).toEqual(mockOAuthMetadata);
            expect(metadata.authorization_endpoint).toBe("https://example.com/oauth/authorize");
            expect(metadata.token_endpoint).toBe("https://example.com/oauth/token");
        });

        it("should return undefined when no OAuth metadata is found", async () => {
            const testServerUrl = "https://example.com/mcp";

            // Mock all endpoints failing
            mockFetch.mockResolvedValue({ ok: false, status: 404 });

            // Test discovery logic when all endpoints fail
            const baseUrl = new URL(testServerUrl);
            const discoveryEndpoints = [
                "/.well-known/oauth-authorization-server",
                "/.well-known/openid-configuration",
                "/oauth/.well-known/oauth-authorization-server",
            ];

            let discoveredMetadata: OAuthMetadata | undefined;

            for (const endpoint of discoveryEndpoints) {
                const metadataUrl = new URL(endpoint, baseUrl).toString();
                const response = await fetch(metadataUrl);

                if (response.ok) {
                    discoveredMetadata = await response.json();
                    break;
                }
            }

            // Verify all endpoints were tried
            expect(mockFetch).toHaveBeenCalledTimes(3);

            // Verify no metadata was discovered
            expect(discoveredMetadata).toBeUndefined();
        });
    });

    describe("OAuth Flow Implementation", () => {
        it("should generate OAuth URL for generic MCP server with discovered metadata", async () => {
            const mockOAuthMetadata = {
                authorization_endpoint: "https://example.com/oauth/authorize",
                token_endpoint: "https://example.com/oauth/token",
                registration_endpoint: "https://example.com/oauth/register",
                scopes_supported: ["read", "write"],
                response_types_supported: ["code"],
                grant_types_supported: ["authorization_code"],
                code_challenge_methods_supported: ["S256"],
            };

            // Test that the OAuth URL generation would work with discovered metadata
            const testUrl = `${mockOAuthMetadata.authorization_endpoint}?response_type=code&client_id=test-client&redirect_uri=vscode%3A%2F%2Faugment.vscode-augment%2Fauth%2Fmcp%2Ftest-server&scope=read+write&code_challenge=test-challenge&code_challenge_method=S256&state=mcp-test-server-123`;

            // Verify URL structure
            const url = new URL(testUrl);
            expect(url.origin + url.pathname).toBe(mockOAuthMetadata.authorization_endpoint);
            expect(url.searchParams.get("response_type")).toBe("code");
            expect(url.searchParams.get("code_challenge_method")).toBe("S256");
            expect(url.searchParams.get("redirect_uri")).toBe(
                "vscode://augment.vscode-augment/auth/mcp/test-server"
            );
        });

        it("should handle token exchange configuration for generic MCP servers", () => {
            const mockOAuthMetadata = {
                authorization_endpoint: "https://example.com/oauth/authorize",
                token_endpoint: "https://example.com/oauth/token",
                scopes_supported: ["read", "write"],
                response_types_supported: ["code"],
                grant_types_supported: ["authorization_code"],
                code_challenge_methods_supported: ["S256"],
            };

            // Test token exchange configuration structure
            const expectedConfig = {
                tokenUrl: mockOAuthMetadata.token_endpoint,
                additionalParams: {
                    client_id: "test-client-id",
                    redirect_uri: "vscode://augment.vscode-augment/auth/mcp/test-server",
                },
            };

            expect(expectedConfig.tokenUrl).toBe(mockOAuthMetadata.token_endpoint);
            expect(expectedConfig.additionalParams.redirect_uri).toContain("test-server");
        });

        it("should validate OAuth metadata structure", () => {
            const validMetadata = {
                authorization_endpoint: "https://example.com/oauth/authorize",
                token_endpoint: "https://example.com/oauth/token",
                scopes_supported: ["read", "write"],
                response_types_supported: ["code"],
                grant_types_supported: ["authorization_code"],
                code_challenge_methods_supported: ["S256"],
            };

            // Test required fields are present
            expect(validMetadata.authorization_endpoint).toBeDefined();
            expect(validMetadata.token_endpoint).toBeDefined();
            expect(validMetadata.response_types_supported).toContain("code");
            expect(validMetadata.grant_types_supported).toContain("authorization_code");
            expect(validMetadata.code_challenge_methods_supported).toContain("S256");
        });

        it("should handle OAuth metadata discovery in settings webview panel", () => {
            // Test the new approach where OAuth metadata discovery happens in the settings panel
            const mockServerUrl = "https://example.com/mcp";
            const mockOAuthMetadata = {
                authorization_endpoint: "https://example.com/oauth/authorize",
                token_endpoint: "https://example.com/oauth/token",
                scopes_supported: ["read", "write"],
                response_types_supported: ["code"],
                grant_types_supported: ["authorization_code"],
                code_challenge_methods_supported: ["S256"],
            };

            // Mock server configuration before OAuth discovery
            const serverBeforeDiscovery = {
                type: "http" as const,
                url: mockServerUrl,
                name: "test-server",
                authRequired: true,
                // No oauthMetadata yet
            };

            // Mock server configuration after OAuth discovery
            const serverAfterDiscovery = {
                ...serverBeforeDiscovery,
                oauthMetadata: mockOAuthMetadata,
            };

            // Verify that the server configuration is properly updated with OAuth metadata
            expect(serverAfterDiscovery.oauthMetadata).toBeDefined();
            expect(serverAfterDiscovery.oauthMetadata?.authorization_endpoint).toBe(
                mockOAuthMetadata.authorization_endpoint
            );
            expect(serverAfterDiscovery.oauthMetadata?.token_endpoint).toBe(
                mockOAuthMetadata.token_endpoint
            );
        });

        it("should provide clear error messages when OAuth metadata discovery fails", () => {
            const errorScenarios = [
                {
                    scenario: "Server not found",
                    expectedError: "MCP server not found: nonexistent-server",
                },
                {
                    scenario: "Non-HTTP server",
                    expectedError: "is not an HTTP server and cannot use OAuth",
                },
                {
                    scenario: "Missing OAuth metadata",
                    expectedError: "does not have OAuth metadata",
                },
                {
                    scenario: "Missing authorization endpoint",
                    expectedError: "OAuth metadata is missing authorization_endpoint",
                },
                {
                    scenario: "Missing token endpoint",
                    expectedError: "OAuth metadata is missing token_endpoint",
                },
            ];

            errorScenarios.forEach(({ expectedError }) => {
                // Test that appropriate error messages are generated for different failure scenarios
                expect(expectedError).toBeDefined();
                expect(expectedError.length).toBeGreaterThan(0);
            });
        });

        it("should support optional oauthMetadata parameter in generateMcpOAuthUrl", () => {
            const mcpName = "test-server";
            const mockOAuthMetadata = {
                authorization_endpoint: "https://example.com/oauth/authorize",
                token_endpoint: "https://example.com/oauth/token",
                scopes_supported: ["read", "write"],
                response_types_supported: ["code"],
                grant_types_supported: ["authorization_code"],
                code_challenge_methods_supported: ["S256"],
            };

            // Test the new method signature with optional oauthMetadata parameter
            const mockToolsModel = {
                generateMcpOAuthUrl: jest.fn(),
            };

            // Test 1: Call with oauthMetadata parameter (new functionality)
            mockToolsModel.generateMcpOAuthUrl(mcpName, mockOAuthMetadata);
            expect(mockToolsModel.generateMcpOAuthUrl).toHaveBeenCalledWith(
                mcpName,
                mockOAuthMetadata
            );

            // Test 2: Call without oauthMetadata parameter (existing functionality)
            mockToolsModel.generateMcpOAuthUrl(mcpName);
            expect(mockToolsModel.generateMcpOAuthUrl).toHaveBeenCalledWith(mcpName);

            // Verify both calls were made
            expect(mockToolsModel.generateMcpOAuthUrl).toHaveBeenCalledTimes(2);
        });

        it("should use provided oauthMetadata directly when available", () => {
            const providedMetadata = {
                authorization_endpoint: "https://direct.example.com/oauth/authorize",
                token_endpoint: "https://direct.example.com/oauth/token",
                scopes_supported: ["admin"],
                response_types_supported: ["code"],
                grant_types_supported: ["authorization_code"],
                code_challenge_methods_supported: ["S256"],
            };

            // When oauthMetadata is provided, it should be used directly
            // This bypasses the need to look up server configuration
            expect(providedMetadata.authorization_endpoint).toBe(
                "https://direct.example.com/oauth/authorize"
            );
            expect(providedMetadata.token_endpoint).toBe("https://direct.example.com/oauth/token");
            expect(providedMetadata.scopes_supported).toContain("admin");

            // This represents the improved workflow where settings webview panel
            // can pass discovered OAuth metadata directly to URL generation
            const workflowSteps = [
                "1. User clicks authenticate button",
                "2. Settings panel discovers OAuth metadata",
                "3. Settings panel calls generateMcpOAuthUrl(name, discoveredMetadata)",
                "4. OAuth URL generated using provided metadata (no server lookup needed)",
                "5. User redirected to OAuth provider",
            ];

            expect(workflowSteps).toHaveLength(5);
            expect(workflowSteps[2]).toContain("generateMcpOAuthUrl(name, discoveredMetadata)");
        });

        it("should handle OAuth callback for existing generic MCP servers", () => {
            const mcpName = "existing-generic-server";

            const mockExistingServer = {
                id: "server-123",
                name: mcpName,
                type: "http" as const,
                url: "https://existing-server.example.com/mcp",
                disabled: false,
                authRequired: true,
                oauthMetadata: {
                    authorization_endpoint: "https://existing-server.example.com/oauth/authorize",
                    token_endpoint: "https://existing-server.example.com/oauth/token",
                    scopes_supported: ["read", "write"],
                },
            };

            const mockCurrentServers = [mockExistingServer];

            // Test the OAuth callback flow for existing servers
            const existingServer = mockCurrentServers.find((server) => server.name === mcpName);
            expect(existingServer).toBeDefined();
            expect(existingServer?.name).toBe(mcpName);
            expect(existingServer?.type).toBe("http");

            // Simulate the update process
            const updatedServers = mockCurrentServers.map((server) => {
                if (server.name === mcpName) {
                    // For generic MCP servers, server object remains unchanged
                    // Access token is stored securely in VSCode secrets
                    return server;
                }
                return server;
            });

            // Verify the server configuration is maintained
            expect(updatedServers).toHaveLength(1);
            expect(updatedServers[0]).toEqual(mockExistingServer);

            // Verify security approach
            expect(mockExistingServer).not.toHaveProperty("accessToken");

            // Test the complete OAuth callback workflow
            const callbackWorkflow = [
                "1. Access token obtained from OAuth exchange",
                "2. Access token stored securely in VSCode secrets",
                "3. Server configuration updated (no changes to server object)",
                "4. Configuration saved to storage",
                "5. Sidecar updated with new configuration",
                "6. UI refresh triggered",
            ];

            expect(callbackWorkflow).toHaveLength(6);
            expect(callbackWorkflow[1]).toContain("stored securely");
            expect(callbackWorkflow[5]).toContain("UI refresh");
        });
    });
});
