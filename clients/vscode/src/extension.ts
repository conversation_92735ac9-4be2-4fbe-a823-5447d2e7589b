// Import init/logging.ts first to initialize the logger before anything else
// Manually changed the prettier-sort-order in .prettierrc to disable prettier re-ordering the inputs
import "./init/logging";

import { TextDocumentChangeEvent } from "@augment-internal/sidecar-libs/src/agent/agent-edit-types";
import { AgentShardStorage } from "@augment-internal/sidecar-libs/src/agent/agent-shard-storage";
import { AggregateCheckpointManager } from "@augment-internal/sidecar-libs/src/agent/checkpoint/aggregate-checkpoint-manager";
import { TaskManager } from "@augment-internal/sidecar-libs/src/agent/task/task-manager";
import { FileBackedTaskStorage } from "@augment-internal/sidecar-libs/src/agent/task/task-storage";
import { InvalidCompletionURLError } from "@augment-internal/sidecar-libs/src/api/types";
import { RulesService } from "@augment-internal/sidecar-libs/src/chat/rules-service";
import {
    ANALYTICS_EVENTS,
    identifyUser,
    trackEventWithTypes,
} from "@augment-internal/sidecar-libs/src/client-interfaces/analytics";
import {
    resetLibraryAPIClient,
    setLibraryAPIClient,
} from "@augment-internal/sidecar-libs/src/client-interfaces/api-client";
import {
    resetLibraryClientActions,
    setLibraryClientActions,
} from "@augment-internal/sidecar-libs/src/client-interfaces/client-actions";
import { setLibraryClientAuth } from "@augment-internal/sidecar-libs/src/client-interfaces/client-auth";
import { setLibraryClientConfig } from "@augment-internal/sidecar-libs/src/client-interfaces/client-config";
import {
    resetLibraryClientWorkspaces,
    setLibraryClientWorkspaces,
} from "@augment-internal/sidecar-libs/src/client-interfaces/client-workspaces";
import {
    resetLibraryClientFeatureFlags,
    setLibraryClientFeatureFlags,
} from "@augment-internal/sidecar-libs/src/client-interfaces/feature-flags";
import {
    getPluginFileStore,
    resetLibraryPluginFileStore,
    setLibraryPluginFileStore,
} from "@augment-internal/sidecar-libs/src/client-interfaces/plugin-file-store";
import {
    createDynamicLevelKvStore,
    resetLibraryPluginKvStore,
    setLibraryPluginKvStore,
} from "@augment-internal/sidecar-libs/src/client-interfaces/plugin-kv-store";
import {
    resetLibraryStateForSidecar,
    setLibraryStateForSidecar,
} from "@augment-internal/sidecar-libs/src/client-interfaces/plugin-state";
import {
    resetLibraryWebviewMessaging,
    setLibraryWebviewMessaging,
} from "@augment-internal/sidecar-libs/src/client-interfaces/webview-messaging";
import { APIError, getErrmsg } from "@augment-internal/sidecar-libs/src/exceptions";
import { ExchangeManager } from "@augment-internal/sidecar-libs/src/exchange-storage/exchange-manager";
import { GitOperationsService } from "@augment-internal/sidecar-libs/src/git/git-operations-service";
import type { AugmentLogger } from "@augment-internal/sidecar-libs/src/logging";
import { resetAgentRequestEventReporter } from "@augment-internal/sidecar-libs/src/metrics/agent-request-event-reporter";
import { resetAgentSessionEventReporter } from "@augment-internal/sidecar-libs/src/metrics/agent-session-event-reporter";
import { ToolUseRequestEventReporter } from "@augment-internal/sidecar-libs/src/metrics/tool-use-request-event-reporter";
import { IToolApprovalConfigManager } from "@augment-internal/sidecar-libs/src/tools/approval-config/i-tool-approval-config-manager";
import { ToolApprovalConfigManager } from "@augment-internal/sidecar-libs/src/tools/approval-config/tool-approval-config-manager";
import { SidecarToolType } from "@augment-internal/sidecar-libs/src/tools/sidecar-tools/sidecar-tool-types";
import { ToolStartupError } from "@augment-internal/sidecar-libs/src/tools/tool-types";
import { ToolsModel } from "@augment-internal/sidecar-libs/src/tools/tools-model";
import { ToolUseStateManager } from "@augment-internal/sidecar-libs/src/tooluse-storage/tooluse-manager";
import { delayMs } from "@augment-internal/sidecar-libs/src/utils/promise-utils";
import { APIStatus } from "@augment-internal/sidecar-libs/src/utils/types";
import { findUniqueGitRoots } from "@augment-internal/sidecar-libs/src/vcs/git-utils";
import { GitStateManager } from "@augment-internal/sidecar-libs/src/vcs/iso-git/workspace-manager";
import { WebviewMessaging } from "@augment-internal/sidecar-libs/src/webview-messages/webview-messaging";
import { BlobNameCalculator } from "@augment-internal/sidecar-libs/src/workspace/blob-name-calculator";
import { assert } from "console";
import { createHash } from "crypto";
import { debounce } from "lodash";
import isEqual from "lodash/isEqual";
import { addToGlobalAgent } from "mac-ca";
import os from "os";
import { v4 as uuidv4, validate as uuidValidate } from "uuid";
import * as vscode from "vscode";

import { AnalyticsManager } from "./analytics/analytics-manager";
import {
    APIServer,
    APIServerImplWithErrorReporting,
    CompletionResult,
    ExtensionSessionEventName,
    Language,
    Model,
    ModelConfig,
    RecencyInfo,
} from "./augment-api";
import {
    AugmentConfig,
    AugmentConfigKey,
    AugmentConfigListener,
    ConfigChanges,
} from "./augment-config-listener";
import { AugmentMemoriesEditorProvider } from "./augment-memories-editor";
import { AugmentRulesEditorProvider } from "./augment-rules-editor";
import { AuthActionsModel } from "./auth/auth-actions-model";
import { type AugmentSession, AuthSessionStore } from "./auth/auth-session-store";
import { handleMcpOAuthCallback } from "./auth/mcp-oauth-callback";
import { OAuthFlow } from "./auth/oauth-flow";
import {
    getAgentMemories,
    getMemoriesDecorationType,
    maybeIncrementMemoriesFileOpenCount,
    migrateAgentMemoriesToAugmentMemories,
    onMemoriesFileDidChange,
    updateMemoriesFileDecorations,
} from "./chat/agent-onboarding-orientation";
import ChatModel, { ChatRequest } from "./chat/chat-model";
import { FuzzyFsSearcher } from "./chat/fuzzy-fs-searcher";
import { FuzzySymbolSearcher } from "./chat/fuzzy-symbol-searcher";
import { GuidelinesWatcher } from "./chat/guidelines-watcher";
import { RulesWatcher } from "./chat/rules-watcher";
import { localToolHostFactory } from "./chat/tools-local-host";
import { VSCodeRemoteInfo } from "./chat/tools-remote-host";
import { AssetManager } from "./client-interfaces/asset-manager";
import { ClientActions } from "./client-interfaces/client-actions";
import { ClientAuth } from "./client-interfaces/client-auth";
import { ClientConfig } from "./client-interfaces/client-config";
import { ClientFeatureFlags } from "./client-interfaces/client-feature-flags";
import { ClientWorkspaces } from "./client-interfaces/client-workspace";
import { PluginStorageForSidecar } from "./client-interfaces/plugin-storage-for-sidecar";
import { SidecarAPIClient } from "./client-interfaces/sidecar-api-client";
import { SlashFixCommandProvider } from "./code-actions/slash-commands";
import { AugmentInstruction } from "./code-edit-types";
import { initCommandManager } from "./command-manager-init";
import { StartCodeInstructionCommand } from "./commands/diff-view";
import { FocusAugmentPanel } from "./commands/focus-augment-panel";
import { ShowSettingsPanelCommand } from "./commands/show-settings-panel";
import { StartNewChat } from "./commands/start-new-chat";
import { CompletionServer } from "./completion-server";
import { onCompletionRequest, onCompletionResolved } from "./completions/completion-events";
import { CompletionItemsProvider } from "./completions/completion-items-provider";
import { CompletionsModel } from "./completions/completions-model";
import { InlineCompletionProvider } from "./completions/inline-provider";
import { RecentCompletions } from "./completions/recent-completions";
import { ConflictingExtensions } from "./conflicting-extensions";
import { DataCollector } from "./data-collector";
import { DiagnosticsManager } from "./diagnostics";
import { NoModelsError, UnknownModelError } from "./exceptions";
import { FeatureFlagManager, type FeatureFlags } from "./feature-flags";
import { FeatureVectorReporter } from "./feature-vector/feature-vector-reporter";
import { EmptyLineHints, HotKeyHints } from "./hotkey-hints";
import { getLogger } from "./logging";
import { ActionsModel, DerivedState } from "./main-panel/action-cards/actions-model";
import { DerivedStateName } from "./main-panel/action-cards/types";
import { AwaitingSyncingPermissionApp } from "./main-panel/apps/awaiting-syncing-permission-app";
import { ChatApp, ChatExtensionMessage } from "./main-panel/apps/chat-webview-app";
import { FolderSelectionApp } from "./main-panel/apps/folder-selection-app";
import { SignInApp } from "./main-panel/apps/sign-in-webview-app";
import { WorkspaceContextApp } from "./main-panel/apps/workspace-context-app";
import { ClientMetricsReporter } from "./metrics/client-metrics-reporter";
import { CodeEditReporter } from "./metrics/code-edit-reporter";
import { CompletionAcceptanceReporter } from "./metrics/completion-acceptance-reporter";
import { ClientCompletionTimelineReporter } from "./metrics/completion-timeline-reporter";
import { ExtensionSessionEventReporter } from "./metrics/extension-session-event-reporter";
import { NextEditResolutionReporter } from "./metrics/next-edit-resolution-reporter";
import { NextEditSessionEventReporter } from "./metrics/next-edit-session-event-reporter";
import { OnboardingSessionEventReporter } from "./metrics/onboarding-session-event-reporter";
import { StallDetector } from "./metrics/stall-detector";
import { WorkTimer } from "./metrics/work-timer";
import { BackgroundNextEdits } from "./next-edit/background-edits";
import { CompletionVisibilityWatcher } from "./next-edit/completion-visibility-watcher";
import { EditorNextEdits, IEditorEditActions } from "./next-edit/editor-edits";
import { GlobalNextEdits } from "./next-edit/global-edits";
import { NextEditConfigManager } from "./next-edit/next-edit-config-manager";
import {
    NextEditMode,
    NextEditResultInfo,
    NextEditScope,
    NextEditSessionEventName,
    NextEditSessionEventSource,
} from "./next-edit/next-edit-types";
import { NextEditRequestManager } from "./next-edit/request-manager-impl";
import { SuggestionManagerImpl } from "./next-edit/suggestion-manager-impl";
import { isNextEditBackgroundEnabled, isNextEditEnabled } from "./next-edit/utils";
import { NotificationWatcher } from "./notification-watcher";
import { OpenChatHintManager } from "./open-chat-hint";
import { RemoteWorkspaceResolver } from "./remote-workspace-resolver";
import { ResolveFileService } from "./resolve-file-service";
import { StatusTrace } from "./status-trace";
import { StateController } from "./statusbar/state-controller";
import { StatusBarManager } from "./statusbar/status-bar-manager";
import * as statusbarStates from "./statusbar/status-bar-states";
import { AugmentGlobalState, GlobalContextKey } from "./utils/context";
import { DisposableInterval, DisposableService } from "./utils/disposable-service";
import { isExtensionVersionGte, isVsCodeVersionGte } from "./utils/environment";
import { EphemeralObservable } from "./utils/ephemeral-flag";
import { directoryExistsAsync, makeDirs, statFile, writeFileUtf8 } from "./utils/fs-utils";
import { KeybindingWatcher } from "./utils/keybindings";
import { dirName } from "./utils/path-utils";
import { toLineRange } from "./utils/ranges-vscode";
import { RecentItems } from "./utils/recent-items";
import { SystemStateName } from "./utils/types";
import { GitCommitIndexer } from "./vcs/git-commit-indexer";
import { NextEditSuggestionsPanel } from "./webview-panels/next-edit-suggestions-panel";
import { PreferenceWebviewPanel } from "./webview-panels/preference-panel";
import { SettingsWebviewPanel } from "./webview-panels/settings-webview-panel";
import { ToolConfigStore } from "./webview-panels/stores/tool-config-store";
import { MainPanelWebviewProvider } from "./webview-providers/main-panel-webview-provider";
import { NextEditSuggestionsWebviewProvider } from "./webview-providers/next-edit-suggestions-webview-provider";
import { registerWebviewManager } from "./webview-providers/webview-manager";
import {
    MainPanelApp,
    NextEditPanelFocusMessage,
    NextEditVSCodeToWebViewMessage,
    NextEditWebViewMessage,
    UserTier,
    WebViewMessageType,
} from "./webview-providers/webview-messages";
import { ExternalSourceFolderRecorderImpl } from "./workspace/external-source-folder-recorder";
import { OnboardingWorkspaceModel } from "./workspace/onboarding-workspace-model";
import { StatusBarSyncingReporter } from "./workspace/status-bar-syncing-reporter";
import { StatusBarSyncingStateListener } from "./workspace/status-bar-syncing-state-listener";
import { SyncingEnabledTracker } from "./workspace/syncing-enabled-tracker";
import { SyncingPermissionTrackerImpl } from "./workspace/syncing-permission-tracker";
import { SyncingStatusReporter } from "./workspace/syncing-status-reporter";
import { enableViewTextDocument } from "./workspace/view-text-document";
import { WorkspaceManager } from "./workspace/workspace-manager";
import { WorkspacePopulatedChecker } from "./workspace/workspace-populated-checker";

/**
 * URI paths for extension actions
 */
const ACTION_URI_PATHS = {
    /** Opens the chat panel with a new thread */
    openChat: "/actions/open/chat",
    /** Opens the Augment settings page */
    openAugmentSettings: "/actions/open/augment-settings",
    /** Opens/focuses the Augment panel where memories are visible */
    openMemories: "/actions/open/memories",
    /** Opens the Augment settings page to the guidelines section */
    openGuidelinesSettings: "/actions/open/guidelines-settings",
} as const;

export class AugmentExtension
    extends DisposableService
    implements vscode.TextDocumentContentProvider
{
    static readonly augmentRootName = ".augmentroot";

    static readonly contentScheme = "augment";
    static readonly displayStatusUri = vscode.Uri.from({
        scheme: this.contentScheme,
        path: "Augment Extension Status",
    });

    static readonly modelConfigBackoffMsecMax = 30000; // 30 seconds

    public keybindingWatcher: KeybindingWatcher | undefined = undefined;

    private _completionServer: CompletionServer | undefined = undefined;
    public workspaceManager: WorkspaceManager | undefined = undefined;
    public syncingStatusReporter: SyncingStatusReporter | undefined = undefined;
    public fuzzyFsSearcher: FuzzyFsSearcher | undefined = undefined;
    public fuzzySymbolSearcher: FuzzySymbolSearcher | undefined = undefined;
    public _toolsModel: ToolsModel | undefined = undefined;
    public toolApprovalConfigManager: IToolApprovalConfigManager | undefined = undefined;
    public _taskManager: TaskManager | undefined = undefined;
    public _exchangeManager: ExchangeManager | undefined = undefined;
    public _toolUseStateManager: ToolUseStateManager | undefined = undefined;
    public get toolsModel(): ToolsModel | undefined {
        return this._toolsModel;
    }

    private _toolConfigStore: ToolConfigStore | undefined = undefined;
    public get toolConfigStore(): ToolConfigStore | undefined {
        return this._toolConfigStore;
    }
    private _agentCheckpointManager: AggregateCheckpointManager | undefined = undefined;
    public guidelinesWatcher: GuidelinesWatcher;
    public rulesWatcher: RulesWatcher | undefined = undefined;
    private _statusBar: StatusBarManager;
    private _initState: vscode.Disposable | undefined;
    public workTimer: WorkTimer;

    // Cancellation token source to cancel an in-progress enable(). The presence or
    // absence of this token source indicates whether an enable() is in progress.
    private _enableCancel: vscode.CancellationTokenSource | undefined;

    private _defaultModel: string | undefined;
    private _modelInfo: Model | undefined;
    private _blobNameCalculator?: BlobNameCalculator;
    private _nextEditRequestManager: NextEditRequestManager | undefined;
    private _suggestionManager: SuggestionManagerImpl | undefined;
    public get modelInfo(): Readonly<Model> | undefined {
        return this._modelInfo;
    }

    public userTier: UserTier = "unknown";

    private _availableModels: string[] = [];

    private _languages: Language[] = [];
    // Test interface for external validation of languages
    public get languages(): ReadonlyArray<Language> {
        return this._languages;
    }
    // current feature flags
    readonly featureFlagManager: FeatureFlagManager;

    // For vscode.TextDocumentContentProvider.
    private readonly _onTextDocumentDidChange = new vscode.EventEmitter<vscode.Uri>();
    private _statusTrace: StatusTrace | undefined;

    // For enabling and disabling inline completions
    private _completionDisposables: vscode.Disposable[] = [];

    // For reporting completion accepts and rejects.
    private _completionAcceptanceReporter: CompletionAcceptanceReporter;
    private _codeEditReporter: CodeEditReporter;
    private _nextEditResolutionReporter: NextEditResolutionReporter;
    private _nextEditSessionEventReporter: NextEditSessionEventReporter;
    public readonly nextEditConfigManager: NextEditConfigManager;
    private _clientMetricsReporter: ClientMetricsReporter;
    private _completionTimelineReporter: ClientCompletionTimelineReporter;
    private _extensionEventReporter: ExtensionSessionEventReporter;
    private _featureVectorReporter: FeatureVectorReporter;

    private _dataCollector: DataCollector | undefined;

    private _editorNextEdit: EditorNextEdits | undefined;
    private _backgroundNextEdit: BackgroundNextEdits | undefined;
    private _globalNextEdit: GlobalNextEdits | undefined;
    private _diagnosticsManager: DiagnosticsManager | undefined;
    private _nextEditVSCodeToWebviewMessage = new vscode.EventEmitter<
        NextEditVSCodeToWebViewMessage | NextEditPanelFocusMessage
    >();

    private _openChatHintManager: OpenChatHintManager | undefined;
    private _remoteWorkspaceResolver: RemoteWorkspaceResolver | undefined;
    private _analyticsManager: AnalyticsManager | undefined;

    // For startup and shutdown.
    private enabled = false;
    private _enableState: vscode.Disposable | undefined;
    private disposeOnDisable: vscode.Disposable[] = [];
    private _inlineCompletionProvider: InlineCompletionProvider | undefined;

    private _completionsModel: CompletionsModel;

    private readonly _logger: AugmentLogger = getLogger("AugmentExtension");

    private _chatModel: ChatModel | undefined;
    private _currentChatExtensionEventDisposable: vscode.Disposable | undefined;

    private _toolUseRequestEventReporter: ToolUseRequestEventReporter;

    private _notificationWatcher: NotificationWatcher | undefined;

    public get sessionId(): string {
        return this._apiServer.sessionId;
    }

    public get chatModel(): ChatModel | undefined {
        return this._chatModel;
    }

    public get editorNextEdit(): IEditorEditActions | undefined {
        return this._editorNextEdit;
    }

    public get notificationWatcher(): NotificationWatcher | undefined {
        return this._notificationWatcher;
    }

    constructor(
        private _extensionContext: vscode.ExtensionContext,
        private readonly _globalState: AugmentGlobalState,
        private _augmentConfigListener: AugmentConfigListener,
        private _apiServer: APIServer,
        private _auth: AuthSessionStore,
        private _recentCompletions: RecentCompletions,
        private _recentInstructions: RecentItems<AugmentInstruction>,
        private _recentNextEditResults: RecentItems<NextEditResultInfo>,
        private _recentChats: RecentItems<ChatRequest>,
        private _nextEditWebViewEvent: vscode.EventEmitter<NextEditWebViewMessage>,
        private _onExtensionUpdateEvent: vscode.EventEmitter<void>,
        private _mainPanelProvider: MainPanelWebviewProvider,
        private _changeWebviewAppEvent: vscode.EventEmitter<MainPanelApp>,
        private _actionsModel: ActionsModel,
        private _syncingEnabledTracker: SyncingEnabledTracker,
        private _chatExtensionEvent: vscode.EventEmitter<ChatExtensionMessage>,
        private _onboardingSessionEventReporter: OnboardingSessionEventReporter,
        private _assetManager: AssetManager,
        private _gitOperationsService: GitOperationsService
    ) {
        super();
        this._statusBar = new StatusBarManager();
        _extensionContext.subscriptions.push(this._statusBar);
        this.workTimer = new WorkTimer();

        this.featureFlagManager = new FeatureFlagManager(
            {
                fetcher: this._fetchFeatureFlags.bind(this),
                refreshIntervalMSec: 30 * 60 * 1000, // 30 minutes
            },
            this._augmentConfigListener
        );

        this._completionAcceptanceReporter = new CompletionAcceptanceReporter(
            _apiServer,
            this._onboardingSessionEventReporter
        );
        this._codeEditReporter = new CodeEditReporter(_apiServer);
        this._nextEditResolutionReporter = new NextEditResolutionReporter(_apiServer);
        this._nextEditSessionEventReporter = new NextEditSessionEventReporter(_apiServer);
        this.nextEditConfigManager = new NextEditConfigManager(
            this._augmentConfigListener,
            this.featureFlagManager,
            this._globalState
        );
        this._clientMetricsReporter = new ClientMetricsReporter(_apiServer);
        this._completionTimelineReporter = new ClientCompletionTimelineReporter(_apiServer);
        this._extensionEventReporter = new ExtensionSessionEventReporter(_apiServer);
        this._featureVectorReporter = new FeatureVectorReporter(_apiServer, _extensionContext);
        this.guidelinesWatcher = new GuidelinesWatcher(
            this._augmentConfigListener,
            this.featureFlagManager,
            this._clientMetricsReporter
        );
        this.rulesWatcher = new RulesWatcher(this.workspaceManager!);
        this._toolUseRequestEventReporter = new ToolUseRequestEventReporter();

        this.disposeOnDisable.push(this.guidelinesWatcher);
        this.disposeOnDisable.push(this.rulesWatcher);

        // Arrange to disable the extension upon disposal.
        this.addDisposable(new vscode.Disposable(() => this.disable()));

        this._completionsModel = new CompletionsModel(
            this,
            this._augmentConfigListener,
            this._clientMetricsReporter
        );

        // See https://linear.app/augmentcode/issue/AU-5836/vscode-augment-extension-breaks-other-extensions-ability-to-connect-to
        if (!isVsCodeVersionGte("1.96.0")) {
            try {
                this._logger.info("Starting macCA");
                addToGlobalAgent();
                this._logger.info("macCa Done");
            } catch (e) {
                this._logger.error("Exception loading mac-ca certs:", e);
            }
        }
    }

    public get completionServer(): CompletionServer | undefined {
        return this._completionServer;
    }

    public get completionsModel(): CompletionsModel {
        return this._completionsModel;
    }

    public get agentCheckpointManager(): AggregateCheckpointManager | undefined {
        return this._agentCheckpointManager;
    }

    // enableInProgress indicates whether there is an in-progress enable task.
    public get enableInProgress(): boolean {
        return this._enableCancel !== undefined;
    }

    // ready() indicates whether the extension has finished starting up.
    public get ready(): boolean {
        return this.enabled && !this.enableInProgress;
    }

    // enable() starts a background task to enable the extension. It does not wait
    // for the task to finish.
    public async enable(): Promise<void> {
        if (this.enabled || this.enableInProgress) {
            return;
        }

        const enableCancel = new vscode.CancellationTokenSource();
        this._enableCancel = enableCancel;
        const startTime = Date.now();

        try {
            await this._enable(enableCancel.token);
        } catch (e: any) {
            this._logger.info(`Unable to enable extension: ${getErrmsg(e)}`);

            // Report startup failure
            const startupDuration = Date.now() - startTime;
            // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment, @typescript-eslint/no-unsafe-member-access
            const extensionVersion = this._extensionContext.extension.packageJSON.version as string;
            trackEventWithTypes(ANALYTICS_EVENTS.VSCODE_EXTENSION_STARTUP, {
                startupDurationMs: startupDuration,
                startupSuccess: false,
                vscodeVersion: vscode.version,
                extensionVersion: extensionVersion,
                extensionMode: vscode.ExtensionMode[this._extensionContext.extensionMode],
                startupError: getErrmsg(e),
            });

            if (process.env.JEST_WORKER_ID) {
                throw e;
            }
        } finally {
            // Uninstall this._enableCancel to indicate that there is no
            // "enable" in progress.
            enableCancel.dispose();
            this._enableCancel = undefined;
        }
    }

    // _syncLastEnabledExtensionVersion() updates the extension version in the global state.
    // It returns true if the version has changed, false otherwise.
    private _syncLastEnabledExtensionVersion(): boolean {
        // Do not update the extension version in development mode.
        if (this._extensionContext.extensionMode === vscode.ExtensionMode.Development) {
            return false;
        }

        // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment, @typescript-eslint/no-unsafe-member-access
        const jsonVersion = this._extensionContext.extension.packageJSON.version;
        if (this._extensionVersion === jsonVersion) {
            return false;
        }

        void this._globalState.update(GlobalContextKey.lastEnabledExtensionVersion, jsonVersion);
        this._onExtensionUpdateEvent.fire();
        return true;
    }

    private get _extensionVersion(): string {
        return this._globalState.get(GlobalContextKey.lastEnabledExtensionVersion) || "";
    }

    // _enable() enables the extension by querying the model configuration from the
    // back end, opening the current set of workspace folders, and installing the
    // listeners needed to maintain its state.
    private async _enable(cancelToken: vscode.CancellationToken): Promise<void> {
        assert(!this.enabled);
        this._initState?.dispose();
        this._enableState?.dispose();

        const enableStart = Date.now();
        const startupPhases: { phase: string; duration: number }[] = [];
        let phaseStart = Date.now();

        this.disposeOnDisable.push(
            new StallDetector(this._clientMetricsReporter, this.workTimer, {
                periodMs: 100,
                debugThresholdMs: 50,
                infoThresholdMs: 2000,
            })
        );

        let authSession: AugmentSession | null = null;
        if (this._auth.useOAuth) {
            // Use OAuth flow
            authSession = await this._auth.getSession();
            if (!authSession) {
                this._enableState = this._statusBar.setState(statusbarStates.signIn);
                return;
            }
            startupPhases.push({ phase: "oauth_auth", duration: Date.now() - phaseStart });
        } else {
            // Use API token
            if (!this._augmentConfigListener.config.apiToken) {
                this._logger.warn("No API token is configured");
                this._enableState = this._statusBar.setState(statusbarStates.noAPIToken);
                return;
            }

            if (!this._augmentConfigListener.config.completionURL) {
                this._logger.warn("No completion URL is configured");
                this._enableState = this._statusBar.setState(statusbarStates.noCompletionURL);
                return;
            }
        }

        const initializingStateDisposal = this._statusBar.setState(statusbarStates.initializing);

        let modelConfig: ModelConfig;
        let chatModelConfigs: Map<string, Model> = new Map<string, Model>();
        try {
            phaseStart = Date.now();
            modelConfig = await this._getModelConfig(cancelToken);
            startupPhases.push({ phase: "get_model_config", duration: Date.now() - phaseStart });

            if (modelConfig.models.length === 0) {
                throw new NoModelsError();
            }

            this.featureFlagManager.update(modelConfig.featureFlags);
            // Sentry config needs the latest feature flags to determine if it should be enabled
            // Update the MainPanelWebviewProvider after they are loaded to continue and unblock the thread for loadingHTML
            this._mainPanelProvider.setFeatureFlagManager(this.featureFlagManager);

            // Initialize analytics manager with tenant information
            if (!this._analyticsManager) {
                const sessionId = getSessionId(this._globalState);
                const tenantId = authSession
                    ? AnalyticsManager.extractTenantIdFromUrl(authSession.tenantURL ?? "")
                    : undefined;
                this._analyticsManager = new AnalyticsManager({
                    context: this._extensionContext,
                    configListener: this._augmentConfigListener,
                    globalState: this._globalState,
                    sessionId,
                    tenantId,
                });
                this._analyticsManager.initialize();
                this.addDisposable(this._analyticsManager);
            }

            // Identify user for analytics if we have user info
            if (modelConfig.user?.id) {
                identifyUser(modelConfig.user.id);
            }

            this.userTier = modelConfig.userTier;
            void vscode.commands.executeCommand("setContext", "augment.userTier", this.userTier);

            this._defaultModel = modelConfig.defaultModel;
            this._languages = modelConfig.languages;
            this._availableModels = modelConfig.models.map((m) => `${m.name} - ${m.internalName}`);
            // Extract the current model info from the config
            const config = this._augmentConfigListener.config;
            const modelName = config.modelName || modelConfig.defaultModel;
            this._modelInfo = modelConfig.models.find(
                (model) =>
                    [model.name, model.internalName].includes(modelName) ||
                    model.name === createHash("sha256").update(modelName).digest("hex")
            );
            if (this._modelInfo === undefined) {
                throw new UnknownModelError(modelName);
            }
            // Initialize the chat model configs
            for (const model of modelConfig.models) {
                chatModelConfigs.set(model.name, model);
            }
            this._initState?.dispose();
        } catch (e: any) {
            if (APIError.isAPIErrorWithStatus(e, APIStatus.unauthenticated)) {
                if (this._auth.useOAuth) {
                    this._enableState = this._statusBar.setState(statusbarStates.oauthFailed);
                } else {
                    this._enableState = this._statusBar.setState(statusbarStates.apiTokenFailed);
                }
                return;
            } else if (e instanceof InvalidCompletionURLError) {
                this._enableState = this._statusBar.setState(statusbarStates.invalidCompletionURL);
                return;
            } else if (e instanceof vscode.CancellationError) {
                // If we cancelled the enablement of the extension, do nothing.
                return;
            }

            const msg = getErrmsg(e);
            this._logger.error(`Failed to get model config: ${msg}`);
            this._initState = this._statusBar.setState(statusbarStates.getModelConfigFailed);
            throw e;
        } finally {
            initializingStateDisposal.dispose();
        }

        if (this.featureFlagManager.currentFlags.enableViewTextDocument) {
            // This flag is checked only at extension enablement time
            this._logger.debug("Enabling viewTextDocument background file scheme");
            this.disposeOnDisable.push(enableViewTextDocument());
        }

        const syncingPermissionTracker = new SyncingPermissionTrackerImpl(
            this._extensionContext.workspaceState
        );
        this.disposeOnDisable.push(syncingPermissionTracker);

        this._completionServer = new CompletionServer(
            this._apiServer,
            this._modelInfo.completionTimeoutMs,
            this._modelInfo.suggestedPrefixCharCount,
            this._modelInfo.suggestedSuffixCharCount
        );

        const maxUploadSizeBytes = this.featureFlagManager.currentFlags.maxUploadSizeBytes;
        this._blobNameCalculator = new BlobNameCalculator(maxUploadSizeBytes);

        phaseStart = Date.now();
        this.workspaceManager = new WorkspaceManager(
            this._actionsModel,
            new ExternalSourceFolderRecorderImpl(this._extensionContext.workspaceState),
            syncingPermissionTracker,
            this._extensionContext,
            this._apiServer,
            this._augmentConfigListener,
            this.featureFlagManager,
            this._clientMetricsReporter,
            this._completionServer,
            this._blobNameCalculator,
            maxUploadSizeBytes,
            this._syncingEnabledTracker,
            this._onboardingSessionEventReporter,
            modelConfig.languages
        );
        this.disposeOnDisable.push(this.workspaceManager);
        startupPhases.push({ phase: "workspace_manager_init", duration: Date.now() - phaseStart });

        // record information about source folders.
        // debounce is used because events are triggered consecutively.
        const recordSourceFolders = debounce(() => {
            const sourceFolderReportDetails =
                this.workspaceManager?.getSourceFoldersReportDetails();
            if (sourceFolderReportDetails !== undefined) {
                this._extensionEventReporter.reportSourceFolders(sourceFolderReportDetails);
            }
        }, 5000);

        this.disposeOnDisable.push(
            // records tracked source folders when they are enumerated
            this.workspaceManager.onDidEnumerateFolder(() => recordSourceFolders())
        );
        this.disposeOnDisable.push(
            // records source folders when external folders are added
            this.workspaceManager.onDidDetectEmptyWorkspace(() => recordSourceFolders())
        );
        this.disposeOnDisable.push(
            // records source folders when external folders are added
            this.workspaceManager.onDidChangeSourceFolders(() => recordSourceFolders())
        );

        this.syncingStatusReporter = new SyncingStatusReporter(
            this.featureFlagManager,
            this.workspaceManager
        );
        this.disposeOnDisable.push(this.syncingStatusReporter);

        this.disposeOnDisable.push(
            new StatusBarSyncingReporter(
                this._statusBar,
                this.syncingStatusReporter.onDidChangeSyncingStatus
            )
        );

        this.keybindingWatcher = new KeybindingWatcher(this._globalState);
        this.disposeOnDisable.push(this.keybindingWatcher);

        this._diagnosticsManager = new DiagnosticsManager();
        this.disposeOnDisable.push(this._diagnosticsManager);

        const stateController = new StateController(this._statusBar);
        this.disposeOnDisable.push(stateController);

        const completionJustAccepted: EphemeralObservable<boolean> = new EphemeralObservable();
        this.disposeOnDisable.push(
            onCompletionResolved((event) => {
                if (event.acceptedIdx >= 0) {
                    completionJustAccepted.set(true, event.document);
                }
            })
        );
        this.disposeOnDisable.push(completionJustAccepted);

        const completionVisibilityWatcher = new CompletionVisibilityWatcher();
        this.disposeOnDisable.push(completionVisibilityWatcher);

        this._suggestionManager = new SuggestionManagerImpl(
            this.workspaceManager,
            this._nextEditSessionEventReporter
        );
        this.disposeOnDisable.push(this._suggestionManager);

        this._nextEditRequestManager = new NextEditRequestManager(
            this._apiServer,
            this._augmentConfigListener,
            this.workspaceManager,
            this._diagnosticsManager,
            this._nextEditSessionEventReporter,
            this._clientMetricsReporter,
            this._blobNameCalculator,
            this._suggestionManager,
            this._recentNextEditResults,
            stateController,
            completionJustAccepted,
            this.featureFlagManager
        );
        this.disposeOnDisable.push(this._nextEditRequestManager);

        this._editorNextEdit = new EditorNextEdits(
            this._extensionContext,
            this.workspaceManager,
            this._nextEditSessionEventReporter,
            this.keybindingWatcher,
            this._augmentConfigListener,
            this._suggestionManager,
            this._nextEditRequestManager,
            this._globalState,
            this.nextEditConfigManager,
            completionVisibilityWatcher,
            (suggestion) => {
                this._nextEditWebViewEvent.fire({
                    type: WebViewMessageType.nextEditActiveSuggestionChanged,
                    data: suggestion,
                });
            }
        );
        this.disposeOnDisable.push(this._editorNextEdit);

        this._globalNextEdit = new GlobalNextEdits(
            this.workspaceManager,
            this._nextEditRequestManager,
            this._suggestionManager,
            this._augmentConfigListener,
            this._nextEditSessionEventReporter
        );
        this.disposeOnDisable.push(this._globalNextEdit);

        const openHints = [
            {
                text: this.featureFlagManager.currentFlags.enableInstructions
                    ? "Chat"
                    : "Open in Augment Chat",
                keyBindingId: FocusAugmentPanel.commandID,
            },
        ];
        if (this.featureFlagManager.currentFlags.enableInstructions) {
            openHints.push({
                text: "Instruct",
                keyBindingId: StartCodeInstructionCommand.commandID,
            });
        }
        this._openChatHintManager = new OpenChatHintManager(
            this._augmentConfigListener,
            this._extensionContext,
            this.keybindingWatcher,
            this.featureFlagManager,
            openHints
        );
        this._openChatHintManager.enable();
        this.disposeOnDisable.push(this._openChatHintManager);

        this.fuzzyFsSearcher = new FuzzyFsSearcher(
            this._globalState,
            this.workspaceManager,
            this.syncingStatusReporter.onDidChangeSyncingStatus
        );
        this.fuzzySymbolSearcher = new FuzzySymbolSearcher(
            this._globalState,
            this._augmentConfigListener,
            this.fuzzyFsSearcher,
            this.workspaceManager
        );
        this.disposeOnDisable.push(this.fuzzyFsSearcher);
        this.disposeOnDisable.push(this.fuzzySymbolSearcher);

        await migrateAgentMemoriesToAugmentMemories(
            this._extensionContext.storageUri,
            this._logger
        );
        const getAgentMemoriesAbsPath = () => {
            // Get the workspace-specific memories file path
            const baseUri = this._extensionContext.storageUri;
            if (baseUri) {
                return vscode.Uri.joinPath(baseUri, "Augment-Memories").fsPath;
            }
            return undefined;
        };

        setLibraryClientWorkspaces(new ClientWorkspaces(this.workspaceManager));
        this.disposeOnDisable.push(new vscode.Disposable(() => resetLibraryClientWorkspaces()));
        setLibraryAPIClient(new SidecarAPIClient(this._apiServer, this.workspaceManager));
        this.disposeOnDisable.push(new vscode.Disposable(() => resetLibraryAPIClient()));
        setLibraryPluginFileStore(this._assetManager);
        this.disposeOnDisable.push(new vscode.Disposable(() => resetLibraryPluginFileStore()));
        // Set up KV store with Level database
        const kvStorePath = this._extensionContext.storageUri
            ? vscode.Uri.joinPath(this._extensionContext.storageUri, "augment-kv-store").fsPath
            : "./augment-kv-store";

        const kvStore = createDynamicLevelKvStore(kvStorePath, {});
        setLibraryPluginKvStore(kvStore);
        this.disposeOnDisable.push(new vscode.Disposable(() => resetLibraryPluginKvStore()));
        setLibraryStateForSidecar(new PluginStorageForSidecar(this._extensionContext));
        this.disposeOnDisable.push(new vscode.Disposable(() => resetLibraryStateForSidecar()));
        setLibraryClientFeatureFlags(new ClientFeatureFlags(this.featureFlagManager));
        this.disposeOnDisable.push(new vscode.Disposable(() => resetLibraryClientFeatureFlags()));
        setLibraryClientActions(
            new ClientActions(
                this._extensionContext.extensionUri,
                this.workspaceManager,
                this._apiServer,
                this.keybindingWatcher,
                this.fuzzyFsSearcher,
                this.fuzzySymbolSearcher,
                this.workTimer
            )
        );
        this.disposeOnDisable.push(new vscode.Disposable(() => resetLibraryClientActions()));

        this._agentCheckpointManager = new AggregateCheckpointManager(
            new AgentShardStorage(),
            getAgentMemoriesAbsPath,
            (cb: (event: TextDocumentChangeEvent) => void) => {
                return vscode.workspace.onDidChangeTextDocument(
                    (vsEvent: vscode.TextDocumentChangeEvent) => {
                        // Convert VS Code event to the format expected by the checkpoint manager
                        const qualifiedPathName = this.workspaceManager?.safeResolvePathName(
                            vsEvent.document.uri
                        );
                        if (!qualifiedPathName) {
                            return; // Skip files outside the workspace
                        }

                        // Create the converted event
                        const convertedEvent: TextDocumentChangeEvent = {
                            document: {
                                qualifiedPathName,
                                getText: () => vsEvent.document.getText(),
                            },
                            contentChanges: vsEvent.contentChanges.map((change) => ({
                                text: change.text,
                                range: change.range
                                    ? {
                                          start: {
                                              line: change.range.start.line,
                                              character: change.range.start.character,
                                          },
                                          end: {
                                              line: change.range.end.line,
                                              character: change.range.end.character,
                                          },
                                      }
                                    : undefined,
                            })),
                        };

                        // Call the original callback with the converted event
                        cb(convertedEvent);
                    }
                );
            },
            this.workspaceManager.onFileDeleted,
            this.workspaceManager.onFileDidMove
        );
        this.disposeOnDisable.push(this._agentCheckpointManager);
        this._taskManager = new TaskManager(new FileBackedTaskStorage());
        this._exchangeManager = new ExchangeManager();
        this._toolUseStateManager = new ToolUseStateManager();

        // Initialize the git commit indexer
        const gitCommitIndexer = new GitCommitIndexer(
            this._apiServer,
            this._blobNameCalculator,
            this.featureFlagManager,
            this._globalState
        );
        this.disposeOnDisable.push(gitCommitIndexer);

        this.toolApprovalConfigManager = new ToolApprovalConfigManager(getPluginFileStore());
        this._toolsModel = new ToolsModel(
            [],
            localToolHostFactory(
                this._apiServer,
                this.workspaceManager,
                this._agentCheckpointManager,
                this.featureFlagManager,
                this._extensionContext.extensionUri,
                this._globalState,
                enableStart,
                this.featureFlagManager.currentFlags.enableCommitIndexing
                    ? gitCommitIndexer
                    : undefined,
                this._augmentConfigListener,
                this._assetManager
            ),
            new VSCodeRemoteInfo(this._apiServer, this._augmentConfigListener),
            (details: ToolStartupError) => {
                // Show the error message
                //
                // The error message dialog doesn't seem to support any formatting options
                // - not even newlines - so we format the structured info as JSON.
                this._logger.error("MCP tool startup error: %s", JSON.stringify(details));

                // Handle authentication detection for remote MCP servers
                if (details.isAuthRequired && details.command) {
                    void this._handleMcpAuthenticationRequired(details.command);
                }
            },
            new ClientFeatureFlags(this.featureFlagManager),
            this._agentCheckpointManager,
            () => getAgentMemories(getAgentMemoriesAbsPath),
            getAgentMemoriesAbsPath,
            () => this._toolUseRequestEventReporter,
            this.toolApprovalConfigManager,
            {
                // Note: this user-agent is for web-fetch and is different from
                // the user-agent we send to our backend when querying our own API
                userAgent: "Augment-VSCode/1.0",
                unsupportedSidecarTools: new Set([SidecarToolType.strReplaceEditor]),
                fileStore: this._assetManager,
            },
            this._taskManager
        );

        // Create RulesService instance
        const rulesService = new RulesService();
        this.disposeOnDisable.push(rulesService);

        // Use the injected GitOperationsService instance
        const gitOperationsService = this._gitOperationsService;
        this.disposeOnDisable.push(gitOperationsService);

        // Initialize git service with current workspace if available
        // We need to wait for the workspace manager to be ready and then initialize git
        if (this.workspaceManager) {
            // Try to get workspace from WorkspaceManager first
            const initializeGitService = async () => {
                if (!this.workspaceManager) {
                    return;
                }

                // Check if git tracking is enabled via feature flag
                if (!this.featureFlagManager.currentFlags.enableAgentGitTracker) {
                    this._logger.info(
                        "Git tracking disabled by enableAgentGitTracker feature flag"
                    );
                    return;
                }

                const sourceFolders = this.workspaceManager.listSourceFolders();

                // Get all workspace folder paths (both source folders and VSCode workspace folders)
                const allWorkspacePaths =
                    sourceFolders.length > 0
                        ? sourceFolders.map((folder) => folder.folderRoot)
                        : vscode.workspace.workspaceFolders?.map((folder) => folder.uri.fsPath) ||
                          [];

                if (allWorkspacePaths.length === 0) {
                    this._logger.warn(
                        "No workspace folders available for GitOperationsService initialization"
                    );
                    return;
                }

                // Find unique git repository roots from all workspace paths
                // This crawls up the directory tree to find the nearest ancestor git root
                // ONLY track actual git repositories - do not initialize workspaces that are not at a git root
                const gitRootsToTrack = await findUniqueGitRoots(allWorkspacePaths);

                if (gitRootsToTrack.length === 0) {
                    this._logger.debug(
                        "No git repositories found in workspace folders - skipping git tracking"
                    );
                    return;
                }

                this._logger.debug(
                    `Found ${gitRootsToTrack.length} unique git repositories to track: ${gitRootsToTrack.join(", ")}`
                );

                // Track ONLY actual git repositories - never track non-git workspace folders
                for (const gitRoot of gitRootsToTrack) {
                    this._logger.debug(
                        `Initializing GitOperationsService with git repository: ${gitRoot}`
                    );

                    try {
                        // Track the git repository for git operations
                        const repoInfo = await gitOperationsService.trackRepository({
                            repoRoot: gitRoot,
                        });
                        if (repoInfo.isValid) {
                            this._logger.info(
                                `GitOperationsService successfully tracking repository: ${gitRoot}`
                            );
                        } else {
                            this._logger.warn(
                                `Failed to track repository ${gitRoot}: ${repoInfo.lastError}`
                            );
                        }
                    } catch (error) {
                        this._logger.warn(
                            `Failed to initialize GitOperationsService for ${gitRoot}:`,
                            error
                        );
                    }
                }
            };

            // Try to initialize immediately, but also listen for workspace changes
            void initializeGitService();

            // Listen for source folder changes in case workspace isn't ready yet
            const disposable = this.workspaceManager.onDidChangeSourceFolders(initializeGitService);
            this.disposeOnDisable.push(disposable);
        } else {
            this._logger.warn(
                "WorkspaceManager not available for GitOperationsService initialization"
            );
        }

        setLibraryWebviewMessaging(
            new WebviewMessaging(
                this._agentCheckpointManager,
                this._toolsModel,
                this._taskManager,
                this._exchangeManager,
                this._toolUseStateManager,
                rulesService,
                gitOperationsService
            )
        );
        this.disposeOnDisable.push(new vscode.Disposable(() => resetLibraryWebviewMessaging()));

        // Initialize the tool config store
        this._toolConfigStore = new ToolConfigStore(
            this._globalState,
            this._toolsModel,
            () => this._augmentConfigListener.config.mcpServers,
            this._extensionContext
        );
        this.disposeOnDisable.push(
            new vscode.Disposable(() => {
                this._toolConfigStore = undefined;
            })
        );
        // Load MCP servers from storage and update the tools model
        void this._toolConfigStore.updateSidecarMCPServers();

        const maybeInitAgentMemoriesFile = async (toolsModel: ToolsModel) => {
            const memoriesAbsPath = toolsModel.memoriesAbsPath;
            if (memoriesAbsPath) {
                // Check if it exists
                const memoriesFileUri = vscode.Uri.file(memoriesAbsPath);
                try {
                    await statFile(memoriesFileUri.fsPath);
                } catch {
                    const memoryDir = dirName(memoriesFileUri.fsPath);
                    if (!(await directoryExistsAsync(memoryDir))) {
                        // Create the directory if it doesn't exist yet. This may occur
                        // if the user creates a new project and VS Code hasn't created
                        // the workspace directory yet.
                        await makeDirs(memoryDir);
                    }
                    await writeFileUtf8(memoriesFileUri.fsPath, "");
                }
            }

            this.addDisposable(
                vscode.window.onDidChangeActiveTextEditor(async (editor) => {
                    await maybeIncrementMemoriesFileOpenCount(
                        editor,
                        getAgentMemoriesAbsPath,
                        this._globalState
                    );
                    await updateMemoriesFileDecorations(
                        editor,
                        getAgentMemoriesAbsPath,
                        this._globalState
                    );
                })
            );

            this.addDisposable(
                vscode.workspace.onDidChangeTextDocument((event) => {
                    onMemoriesFileDidChange(
                        event,
                        vscode.window.activeTextEditor,
                        getAgentMemoriesAbsPath,
                        this._globalState,
                        toolsModel
                    );
                })
            );
            this.addDisposable(getMemoriesDecorationType());
        };
        void maybeInitAgentMemoriesFile(this._toolsModel);

        // Initialize the notification watcher
        this._notificationWatcher = new NotificationWatcher(
            this._apiServer,
            this._augmentConfigListener,
            this.featureFlagManager
        );
        this.disposeOnDisable.push(this._notificationWatcher);

        // Listen for changes to MCP servers in settings.json
        this.addDisposable(
            this._augmentConfigListener.onDidChange(({ newConfig, previousConfig }) => {
                if (!this._toolsModel) {
                    return;
                }

                // Check if MCP servers have changed
                if (!isEqual(newConfig.mcpServers, previousConfig.mcpServers)) {
                    // Update the tools model with the combined MCP servers
                    void this._toolConfigStore?.updateSidecarMCPServers();
                }
            })
        );

        const chatModel = new ChatModel(
            this._globalState,
            this._apiServer,
            this.workspaceManager,
            this._recentChats,
            this.fuzzySymbolSearcher,
            this._assetManager,
            this.featureFlagManager,
            this._agentCheckpointManager
        );
        this._chatModel = chatModel;
        const onboardingWorkspaceModel = new OnboardingWorkspaceModel(
            this._globalState,
            this.syncingStatusReporter
        );
        const resolveFileService = new ResolveFileService();

        const app = new ChatApp(
            chatModel,
            chatModelConfigs,
            this._apiServer,
            this.workspaceManager,
            this.keybindingWatcher,
            this._augmentConfigListener,
            this._extensionContext.extensionUri,
            this.featureFlagManager,
            this._clientMetricsReporter,
            this._actionsModel,
            this._syncingEnabledTracker,
            onboardingWorkspaceModel,
            this.syncingStatusReporter,
            this._onboardingSessionEventReporter,
            this.fuzzyFsSearcher,
            this.fuzzySymbolSearcher,
            this._toolsModel,
            resolveFileService,
            this._agentCheckpointManager,
            this.guidelinesWatcher,
            this.rulesWatcher!,
            this._assetManager,
            this._globalState,
            this.workTimer,
            this._toolConfigStore,
            this._extensionContext,
            this._notificationWatcher
        );
        this._currentChatExtensionEventDisposable = this._chatExtensionEvent.event(
            app.onChatExtensionMessage
        );
        this.disposeOnDisable.push(this._currentChatExtensionEventDisposable);

        this._mainPanelProvider.changeApp(app);

        const workspaceManager = this.workspaceManager;
        const keybindingWatcher = this.keybindingWatcher;
        this.disposeOnDisable.push(
            this._changeWebviewAppEvent.event((app) => {
                let appInstance;
                let chatExtensionEventDisposable;
                this._currentChatExtensionEventDisposable?.dispose();
                switch (app) {
                    case MainPanelApp.chat:
                        appInstance = new ChatApp(
                            chatModel,
                            chatModelConfigs,
                            this._apiServer,
                            workspaceManager,
                            keybindingWatcher,
                            this._augmentConfigListener,
                            this._extensionContext.extensionUri,
                            this.featureFlagManager,
                            this._clientMetricsReporter,
                            this._actionsModel,
                            this._syncingEnabledTracker,
                            onboardingWorkspaceModel,
                            this.syncingStatusReporter!,
                            this._onboardingSessionEventReporter,
                            this.fuzzyFsSearcher!,
                            this.fuzzySymbolSearcher!,
                            this._toolsModel!,
                            resolveFileService,
                            this._agentCheckpointManager!,
                            this.guidelinesWatcher,
                            this.rulesWatcher!,
                            this._assetManager,
                            this._globalState,
                            this.workTimer,
                            this._toolConfigStore!,
                            this._extensionContext,
                            this._notificationWatcher
                        );
                        this._mainPanelProvider.changeApp(appInstance);
                        chatExtensionEventDisposable = this._chatExtensionEvent.event(
                            appInstance.onChatExtensionMessage
                        );
                        this._currentChatExtensionEventDisposable = chatExtensionEventDisposable;
                        this.disposeOnDisable.push(this._currentChatExtensionEventDisposable);
                        break;
                    case MainPanelApp.loading:
                    case MainPanelApp.signIn:
                        break;
                    case MainPanelApp.workspaceContext:
                        this._mainPanelProvider.changeApp(
                            new WorkspaceContextApp(
                                workspaceManager,
                                this.featureFlagManager,
                                this.workTimer
                            )
                        );
                        break;
                    case MainPanelApp.awaitingSyncingPermission:
                        this._mainPanelProvider.changeApp(
                            new AwaitingSyncingPermissionApp(
                                this._actionsModel,
                                this._apiServer,
                                this._augmentConfigListener,
                                this._syncingEnabledTracker,
                                this._changeWebviewAppEvent,
                                this.featureFlagManager,
                                modelConfig.userTier
                            )
                        );
                        break;
                    case MainPanelApp.folderSelection:
                        this._mainPanelProvider.changeApp(
                            new FolderSelectionApp(this._onboardingSessionEventReporter)
                        );
                        break;
                    default: {
                        const exhaustiveCheck: never = app;
                        // eslint-disable-next-line @typescript-eslint/restrict-template-expressions
                        throw new Error(`Unhandled app case: ${exhaustiveCheck}`);
                    }
                }
            })
        );

        // Set up inline completions.
        {
            // Listen for changes to inline completions enabled. Then either enable
            // or disable inline completions as specified by the config.
            this.disposeOnDisable.push(
                this._augmentConfigListener.onDidChange(
                    this._checkInlineCompletionsEnabled.bind(this)
                )
            );
            this._checkInlineCompletionsEnabled();

            // Arrange to disable inline completions when the extension is disabled.
            this.disposeOnDisable.push(
                new vscode.Disposable(() => this._disableInlineCompletions.bind(this))
            );
        }

        // Respond to changes to the config.
        {
            let completionsState: vscode.Disposable | undefined;
            let autoCompletionsState: vscode.Disposable | undefined;
            let shortCutProviderState: vscode.Disposable | undefined;
            let hotkeyHintsState: vscode.Disposable | undefined;
            let emptyLineHintsState: vscode.Disposable | undefined;
            let slashFixCodeActionState: vscode.Disposable | undefined;
            let chatCodeActionState: vscode.Disposable | undefined;

            const processConfigChange = (
                newConfig: AugmentConfig,
                previousConfig?: AugmentConfig
            ) => {
                completionsState?.dispose();
                autoCompletionsState?.dispose();
                shortCutProviderState?.dispose();
                emptyLineHintsState?.dispose();
                hotkeyHintsState?.dispose();
                slashFixCodeActionState?.dispose();
                chatCodeActionState?.dispose();

                if (!newConfig.completions.enableAutomaticCompletions) {
                    autoCompletionsState = this._statusBar.setState(
                        statusbarStates.noAutoCompletions
                    );
                }

                // Empty Line Hints currently behind `debugFeatures` flag
                if (newConfig.enableDebugFeatures && this.keybindingWatcher) {
                    emptyLineHintsState = new EmptyLineHints(
                        this.keybindingWatcher,
                        this._inlineCompletionProvider
                    );
                    this.disposeOnDisable.push(emptyLineHintsState);
                    // Otherwise, load the empty file hotkey hints
                    // TODO: these classes can be merged when we remove the debug flag
                } else if (
                    newConfig.completions.enableAutomaticCompletions &&
                    this.keybindingWatcher
                ) {
                    hotkeyHintsState = new HotKeyHints(
                        this.keybindingWatcher,
                        this._inlineCompletionProvider
                    );
                    this.disposeOnDisable.push(hotkeyHintsState);
                }

                slashFixCodeActionState = vscode.languages.registerCodeActionsProvider(
                    "*",
                    new SlashFixCommandProvider()
                );
                this.disposeOnDisable.push(slashFixCodeActionState);

                if (!newConfig.enableUpload) {
                    completionsState = this._statusBar.setState(statusbarStates.uploadDisabled);
                }

                // Start or stop data collection.
                // TODO(rich): remove the user settings flag once the backend has fully deployed.
                const enableDataCollection =
                    newConfig.enableDataCollection || modelConfig.featureFlags.enableHindsight;
                if (this._dataCollector && !enableDataCollection) {
                    this._logger.debug("Disabling Hindsight Data");
                    this._dataCollector.dispose();
                    this._dataCollector = undefined;
                } else if (
                    !this._dataCollector &&
                    enableDataCollection &&
                    this.workspaceManager !== undefined
                ) {
                    this._logger.debug("Enabling Hindsight Data");
                    this._dataCollector = new DataCollector(
                        this._apiServer,
                        this.workspaceManager,
                        this._recentInstructions,
                        this._recentCompletions,
                        this._recentNextEditResults
                    );
                }

                // Set up background next edit generation events.
                // we don't want to needlessly trigger enable() here because it will clear
                // any suggestions we have currently displayed.
                const newNextEditBackgroundEnabled = isNextEditBackgroundEnabled(
                    newConfig,
                    this.featureFlagManager.currentFlags.vscodeNextEditMinVersion
                );
                const previousNextEditBackgroundEnabled =
                    previousConfig &&
                    isNextEditBackgroundEnabled(
                        previousConfig,
                        this.featureFlagManager.currentFlags.vscodeNextEditMinVersion
                    );
                if (newNextEditBackgroundEnabled && !previousNextEditBackgroundEnabled) {
                    if (
                        this.workspaceManager &&
                        this.keybindingWatcher &&
                        this._suggestionManager &&
                        this._nextEditRequestManager
                    ) {
                        try {
                            this._backgroundNextEdit = new BackgroundNextEdits(
                                this.workspaceManager,
                                this._nextEditSessionEventReporter,
                                this.keybindingWatcher,
                                this._augmentConfigListener,
                                this._suggestionManager,
                                this._nextEditRequestManager,
                                this._globalState,
                                this.nextEditConfigManager,
                                completionVisibilityWatcher
                            );
                            this.disposeOnDisable.push(this._backgroundNextEdit);
                            // Report successful initialization
                            this._nextEditSessionEventReporter.reportEventWithoutIds(
                                NextEditSessionEventName.InitializationSuccess,
                                NextEditSessionEventSource.ValidationExpected
                            );
                        } catch (error) {
                            // Report initialization failure with error stack
                            this._logger.error("Error initializing background next edit: ", error);
                            this._nextEditSessionEventReporter.reportEventWithoutIds(
                                NextEditSessionEventName.InitializationFailure,
                                NextEditSessionEventSource.Error
                            );

                            // Report detailed error to API
                            void this._apiServer.reportError(
                                null,
                                "background_next_edit_initialization_failure",
                                error instanceof Error
                                    ? error.stack || error.message
                                    : String(error),
                                []
                            );
                        }
                    } else {
                        // Report initialization skip due to missing components
                        this._logger.error("Failed to enable background next edit generation");
                        this._nextEditSessionEventReporter.reportEventWithoutIds(
                            NextEditSessionEventName.InitializationSkip,
                            NextEditSessionEventSource.ValidationUnexpected // we do not expect any component to be missing
                        );

                        const reasonForFailure = [
                            ["this.workspaceManager", this.workspaceManager],
                            ["this.keybindingWatcher", this.keybindingWatcher],
                            ["this._suggestionManager", this._suggestionManager],
                            ["this._nextEditRequestManager", this._nextEditRequestManager],
                        ]
                            .map((arr) => arr.join("="))
                            .join(", ");

                        void this._apiServer.reportError(
                            null,
                            "background_next_edit_initialization_failure",
                            `Background next edit initialization failed because ${reasonForFailure}`,
                            []
                        );
                    }
                } else if (this._backgroundNextEdit && !newNextEditBackgroundEnabled) {
                    this._backgroundNextEdit.dispose();
                    this._backgroundNextEdit = undefined;
                    this._nextEditSessionEventReporter.reportEventWithoutIds(
                        NextEditSessionEventName.Disposed,
                        NextEditSessionEventSource.ValidationExpected
                    );
                } else if (!newNextEditBackgroundEnabled && !previousNextEditBackgroundEnabled) {
                    // Report initialization skip due to feature being disabled
                    this._nextEditSessionEventReporter.reportEventWithoutIds(
                        NextEditSessionEventName.InitializationSkip,
                        NextEditSessionEventSource.ValidationExpected
                    );
                }

                // Log metrics for next edit related config changes.
                // Note that these catch changes through commands and settings.
                // These are available through settings even for people without the
                // feature enabled, so only report the events if the feature is enabled.
                const nextEditEnabled = isNextEditEnabled(
                    newConfig,
                    this.featureFlagManager.currentFlags.vscodeNextEditMinVersion ?? ""
                );
                if (
                    nextEditEnabled &&
                    previousConfig &&
                    newConfig.nextEdit.enableBackgroundSuggestions !==
                        previousConfig.nextEdit.enableBackgroundSuggestions
                ) {
                    this._nextEditSessionEventReporter.reportEventWithoutIds(
                        newConfig.nextEdit.enableBackgroundSuggestions
                            ? NextEditSessionEventName.BackgroundSuggestionsEnabled
                            : NextEditSessionEventName.BackgroundSuggestionsDisabled,
                        NextEditSessionEventSource.Unknown
                    );
                }
                if (
                    nextEditEnabled &&
                    previousConfig &&
                    newConfig.nextEdit.highlightSuggestionsInTheEditor !==
                        previousConfig.nextEdit.highlightSuggestionsInTheEditor
                ) {
                    this._nextEditSessionEventReporter.reportEventWithoutIds(
                        newConfig.nextEdit.highlightSuggestionsInTheEditor
                            ? NextEditSessionEventName.HighlightsEnabled
                            : NextEditSessionEventName.HighlightsDisabled,
                        NextEditSessionEventSource.Unknown
                    );
                }
            };

            this.disposeOnDisable.push(
                this._augmentConfigListener.onDidChange((change) => {
                    processConfigChange(change.newConfig, change.previousConfig);
                })
            );
            processConfigChange(this._augmentConfigListener.config);
        }

        // Start background tasks.
        {
            // Start background metrics uploading and arrange to stop it if we get disposed.
            const reporters = [
                this._completionAcceptanceReporter,
                this._codeEditReporter,
                this._nextEditResolutionReporter,
                this._nextEditSessionEventReporter,
                this._onboardingSessionEventReporter,
                this._clientMetricsReporter,
                this._completionTimelineReporter,
                this._extensionEventReporter,
                this._toolUseRequestEventReporter,
                this._featureVectorReporter,
            ];
            for (const r of reporters) {
                r.enableUpload();
                this.disposeOnDisable.push(r);
            }
            this.disposeOnDisable.push(
                new vscode.Disposable(() => {
                    resetAgentSessionEventReporter();
                    resetAgentRequestEventReporter();
                })
            );
        }

        {
            this.disposeOnDisable.push(
                vscode.window.registerWebviewViewProvider(
                    "augment-next-edit",
                    new NextEditSuggestionsWebviewProvider(
                        this._augmentConfigListener,
                        this.featureFlagManager,
                        (webview: vscode.WebviewView) =>
                            new NextEditSuggestionsPanel(
                                this._extensionContext.extensionUri,
                                webview,
                                webview.webview,
                                this._suggestionManager!,
                                this._globalNextEdit!,
                                this._editorNextEdit!,
                                this._nextEditSessionEventReporter,
                                resolveFileService,
                                this._nextEditVSCodeToWebviewMessage,
                                this.workTimer
                            )
                    ),
                    {
                        webviewOptions: {
                            retainContextWhenHidden: true,
                        },
                    }
                )
            );
        }

        this.enabled = true;
        this._statusBar.setState(statusbarStates.enabled);

        // Initialize the remote workspace resolver
        this._remoteWorkspaceResolver = RemoteWorkspaceResolver.initialize(
            this._apiServer,
            this.featureFlagManager
        );
        this.disposeOnDisable.push(this._remoteWorkspaceResolver);

        this.disposeOnDisable.push(
            new StatusBarSyncingStateListener(this._statusBar, this._syncingEnabledTracker)
        );

        // Since the Augment extension is enabled, check for
        // conflicting extensions
        const conflictingExtensions = new ConflictingExtensions(
            this._augmentConfigListener,
            this._actionsModel
        );
        this.addDisposable(conflictingExtensions);
        void conflictingExtensions.checkAndUpdateState();

        // Similarly, perform a check for workspace populated.
        const workspacePopulatedChecker = new WorkspacePopulatedChecker(
            this._actionsModel,
            this.workspaceManager
        );
        this.disposeOnDisable.push(workspacePopulatedChecker);

        /**
         * POST-INITIALIZATION CODE FOR _enable()
         *
         * Everything below here in _enable() relies on the extension being enabled
         * and subscribers to events having been installed. Do not perform any
         * initialization below this point.
         */

        this._syncLastEnabledExtensionVersion();

        // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
        const packageJson = this._extensionContext.extension.packageJSON;
        // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment, @typescript-eslint/no-unsafe-member-access
        const isPrerelease = "preRelease" in packageJson && packageJson.preRelease === true;

        this._logger.debug(`Is the extension in pre-release? ${isPrerelease}`);

        this._extensionEventReporter.reportConfiguration(
            ExtensionSessionEventName.ConfigurationSnapshot,
            this._augmentConfigListener.config,
            this.featureFlagManager.currentFlags
        );

        this._featureVectorReporter.reportVector().catch((error) => {
            this._logger.warn("Failed to report initial feature vector:", error);
        });
        this.addDisposable(
            new DisposableInterval(
                () => {
                    this._featureVectorReporter.reportVector().catch((error) => {
                        this._logger.warn("Failed to report feature vector:", error);
                    });
                },
                1000 * 60 * 15
            )
        );

        // Calculate and report startup time
        const startupDuration = Date.now() - enableStart;
        this._logger.info(`Extension startup completed in ${startupDuration}ms`);

        // Log phase durations for debugging
        for (const phase of startupPhases) {
            this._logger.debug(`Startup phase '${phase.phase}' took ${phase.duration}ms`);
        }

        // Report startup time as a metric
        // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment, @typescript-eslint/no-unsafe-member-access
        const extensionVersion = this._extensionContext.extension.packageJSON.version as string;

        // Build phase properties
        const phaseProperties: Partial<{
            phaseOauthAuthMs: number;
            phaseGetModelConfigMs: number;
            phaseWorkspaceManagerInitMs: number;
        }> = {};

        for (const phase of startupPhases) {
            if (phase.phase === "oauth_auth") {
                phaseProperties.phaseOauthAuthMs = phase.duration;
            } else if (phase.phase === "get_model_config") {
                phaseProperties.phaseGetModelConfigMs = phase.duration;
            } else if (phase.phase === "workspace_manager_init") {
                phaseProperties.phaseWorkspaceManagerInitMs = phase.duration;
            }
        }

        trackEventWithTypes(ANALYTICS_EVENTS.VSCODE_EXTENSION_STARTUP, {
            startupDurationMs: startupDuration,
            startupSuccess: true,
            vscodeVersion: vscode.version,
            extensionVersion: extensionVersion,
            extensionMode: vscode.ExtensionMode[this._extensionContext.extensionMode],
            ...phaseProperties,
        });
    }

    private async _fetchFeatureFlags(
        cancelToken: vscode.CancellationToken
    ): Promise<FeatureFlags | undefined> {
        try {
            const modelConfig = await this._getModelConfig(cancelToken);
            return modelConfig.featureFlags;
        } catch (e) {
            this._logger.error("Failed to fetch feature flags: ", e);
            return undefined;
        }
    }

    updateModelInfo(result: CompletionResult): void {
        if (!this._modelInfo) {
            throw new Error("Model info not set");
        }
        if (result.suggestedPrefixCharCount !== undefined) {
            this._modelInfo.suggestedPrefixCharCount = result.suggestedPrefixCharCount;
        }
        if (result.suggestedSuffixCharCount !== undefined) {
            this._modelInfo.suggestedSuffixCharCount = result.suggestedSuffixCharCount;
        }
        this._modelInfo.completionTimeoutMs = result.completionTimeoutMs;
    }

    // _getModelConfig returns the model configuration retrieved from the back end.
    // On error it retries, with backoff, until it succeeds or is cancelled by the
    // given cancel token. In the latter case, it throws vscode.CancellationError.
    private async _getModelConfig(cancelToken: vscode.CancellationToken): Promise<ModelConfig> {
        let backoffMsec = 1000;
        let modelConfig: ModelConfig | undefined;
        let failureCount = 0;
        const consecutiveFailureLimit = 6;
        const stateController = new StateController(this._statusBar);
        try {
            while (true) {
                if (cancelToken.isCancellationRequested) {
                    throw new vscode.CancellationError();
                }

                try {
                    this._logger.info("Retrieving model config");
                    modelConfig = await this._apiServer.getModelConfig();
                    this._logger.info("Retrieved model config");
                } catch (err) {
                    // Retry any error, after back-off, until we succeed or get cancelled.
                    this._logger.error("Failed to retrieve model config: ", err);
                    if (APIError.isAPIErrorWithStatus(err, APIStatus.unauthenticated)) {
                        // If the user is unauthenticated, we shouldn't retry.
                        throw err;
                    } else if (err instanceof InvalidCompletionURLError) {
                        // If we know the config is invalid, there is no point in
                        // retrying.
                        throw err;
                    }

                    failureCount++;
                }

                if (cancelToken.isCancellationRequested) {
                    this._logger.info("Model config retrieval cancelled");
                    throw new vscode.CancellationError();
                }
                if (modelConfig !== undefined) {
                    this._logger.info(`Returning model config`);
                    return modelConfig;
                }

                if (failureCount >= consecutiveFailureLimit) {
                    stateController.setState(statusbarStates.getModelConfigFailed);
                }

                this._logger.info(`Retrying model config retrieval in ${backoffMsec} msec`);
                await delayMs(backoffMsec);
                backoffMsec = Math.min(backoffMsec * 2, AugmentExtension.modelConfigBackoffMsecMax);
            }
        } finally {
            stateController.dispose();
        }
    }

    // disable() closes all existing roots and cancels all pending roots. New roots
    // cannot be added until the extension has been reenabled.
    public disable(): void {
        this.enabled = false;

        while (this.disposeOnDisable.length) {
            const disp = this.disposeOnDisable.pop();
            disp!.dispose();
        }
        this._currentChatExtensionEventDisposable = undefined;
        this.reset();
    }

    // reset() closes all context roots and cancels any in-progress completion. It
    // does not disable the extension.
    public reset(): void {
        this._enableCancel?.cancel();
        this._enableCancel?.dispose();
        this._enableCancel = undefined;

        this._statusBar.reset();

        this.workspaceManager?.dispose();
        this.workspaceManager = undefined;

        this._disableDataCollection();
    }

    // Respond to a change inlineCompletionsEnabled.
    private _checkInlineCompletionsEnabled(change?: ConfigChanges) {
        if (change) {
            // The config has changed, check if any of the completions-related settings changed.
            if (
                change.previousConfig.completions.addIntelliSenseSuggestions ===
                change.newConfig.completions.addIntelliSenseSuggestions
            ) {
                return;
            }
        }

        this._enableInlineCompletions();
    }

    // Register this extension as the inline completion provider for every
    // supported language in the model configuration.
    private _enableInlineCompletions() {
        // In case completions are already enabled
        this._disableInlineCompletions();

        this._logger.debug(`Registering inline completions  provider.`);
        this._inlineCompletionProvider = new InlineCompletionProvider(
            this._completionsModel,
            this._completionAcceptanceReporter,
            this._statusBar,
            this._augmentConfigListener,
            this._completionTimelineReporter
        );
        this._completionDisposables.push(this._inlineCompletionProvider);

        onCompletionRequest((completionRequest) => {
            if (!completionRequest) {
                return;
            }
            this._recentCompletions.addItem(completionRequest);
        });
        this._completionDisposables.push(
            vscode.languages.registerInlineCompletionItemProvider(
                "*",
                this._inlineCompletionProvider
            )
        );

        if (this._augmentConfigListener.config.completions.addIntelliSenseSuggestions) {
            this._logger.debug(`Registering completion items provider.`);
            const completionItemProvider = new CompletionItemsProvider(this._augmentConfigListener);
            this._completionDisposables.push(
                vscode.languages.registerCompletionItemProvider(
                    CompletionItemsProvider.languageSelector,
                    completionItemProvider,
                    ...CompletionItemsProvider.triggerCharacters
                )
            );
        }
    }

    // Unregister this extension as an inline completion provider.
    private _disableInlineCompletions() {
        for (const disposable of this._completionDisposables) {
            disposable.dispose();
        }
        this._completionDisposables = [];
    }

    private _disableDataCollection() {
        this._dataCollector?.dispose();
        this._dataCollector = undefined;
    }

    public getRecencyInfo(): RecencyInfo {
        // Populate recency fields
        let recencyInfo: RecencyInfo = {};

        let tabSwitchEvents = this.workspaceManager!.getTabSwitchEvents();
        if (tabSwitchEvents !== undefined) {
            recencyInfo.tab_switch_events = tabSwitchEvents.map((t) => ({
                path: t.relPathName,
                file_blob_name: t.blobName, // eslint-disable-line @typescript-eslint/naming-convention
            }));
        }

        const allViewedContent = this.workspaceManager?.getAllViewedContent();
        if (allViewedContent && allViewedContent.length > 0) {
            /* eslint-disable @typescript-eslint/naming-convention */
            recencyInfo.viewed_contents = allViewedContent.map((content) => ({
                path: content.relPathName,
                file_blob_name: content.blobName,
                visible_content: content.visibleContent,
                line_start: content.lineStart,
                line_end: content.lineEnd,
                char_start: content.charStart,
                char_end: content.charEnd,
                timestamp: content.timestamp.toISOString(),
            }));
            /* eslint-enable @typescript-eslint/naming-convention */
        }

        return recencyInfo;
    }

    public forceNextEditSuggestion(eventSource?: NextEditSessionEventSource) {
        const editor = vscode.window.activeTextEditor;
        if (!editor || !this.workspaceManager) {
            return;
        }
        const qualifiedPath = this.workspaceManager.safeResolvePathName(editor.document.uri);
        if (!qualifiedPath) {
            return;
        }
        this._nextEditSessionEventReporter.reportEventWithoutIds(
            NextEditSessionEventName.SuggestionForced,
            eventSource ?? NextEditSessionEventSource.Command
        );
        void this._nextEditRequestManager?.enqueueRequest(
            qualifiedPath,
            NextEditMode.Forced,
            NextEditScope.Cursor,
            toLineRange(editor.selection)
        );
    }

    public nextEditUpdate(eventSource?: NextEditSessionEventSource) {
        void this._globalNextEdit?.startGlobalQuery(eventSource);
    }

    public nextEditBackgroundSuggestionsEnabled() {
        return isNextEditBackgroundEnabled(
            this._augmentConfigListener.config,
            this.featureFlagManager.currentFlags.vscodeNextEditMinVersion ?? ""
        );
    }

    public noopClicked() {
        this._nextEditSessionEventReporter.reportEventWithoutIds(
            NextEditSessionEventName.NoopClicked,
            NextEditSessionEventSource.Command
        );
    }

    public nextEditTogglePanelHorizontalSplit(eventSource?: NextEditSessionEventSource) {
        this._nextEditVSCodeToWebviewMessage.fire({
            type: WebViewMessageType.nextEditToggleSuggestionTree,
        });
        this._nextEditSessionEventReporter.reportEventWithoutIds(
            NextEditSessionEventName.TogglePanelHorizontalSplit,
            eventSource ?? NextEditSessionEventSource.Command
        );
    }

    public openNextEditPanel(eventSource?: NextEditSessionEventSource) {
        this._nextEditSessionEventReporter.reportEventWithoutIds(
            NextEditSessionEventName.PanelFocusExecuted,
            eventSource ?? NextEditSessionEventSource.Command
        );
        void vscode.commands.executeCommand("augment-next-edit.focus");

        // Send a message to focus the container element in the webview
        this._nextEditVSCodeToWebviewMessage.fire({
            type: WebViewMessageType.nextEditPanelFocus,
        });
    }

    public nextEditLearnMore(eventSource?: NextEditSessionEventSource) {
        this._nextEditSessionEventReporter.reportEventWithoutIds(
            NextEditSessionEventName.LearnMoreClicked,
            eventSource ?? NextEditSessionEventSource.Command
        );
        void vscode.env.openExternal(
            vscode.Uri.parse("https://docs.augmentcode.com/using-augment/next-edit")
        );
    }

    public async updateStatusTrace() {
        this._statusTrace?.dispose();
        const trace = new StatusTrace(() =>
            this._onTextDocumentDidChange.fire(AugmentExtension.displayStatusUri)
        );
        this._statusTrace = trace;

        let savePoint = 0;

        if (this.enableInProgress) {
            trace.addLine("Augment extension is initializing");
            trace.publish();
            return;
        }

        if (!this.enabled) {
            trace.addLine("Augment is not enabled in this workspace");
            trace.publish();
            return;
        }

        trace.addSection("Extension version");
        const extension = vscode.extensions.getExtension("augment.vscode-augment");
        if (extension) {
            // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
            trace.addValue("Extension version", extension.packageJSON.version);
        } else {
            trace.addLine(`Cannot retrieve extension version`);
        }

        trace.addSection("Session ID");
        trace.addValue("Session ID", this._apiServer.sessionId);

        trace.addSection("Recent Completion Requests (oldest to newest)");
        const completions = this._recentCompletions.items
            .sort((a, b) => a.occuredAt.getTime() - b.occuredAt.getTime())
            .slice(0, 10);
        for (let { requestId } of completions) {
            trace.addLine(`${requestId}`);
        }
        if (completions.length === 0) {
            trace.addLine("No recent completion requests");
        }

        trace.addSection("Recent Instruction Requests (oldest to newest)");
        for (let { requestId } of this._recentInstructions.items) {
            trace.addLine(`${requestId}`);
        }
        if (this._recentInstructions.items.length === 0) {
            trace.addLine("No recent instruction requests");
        }

        trace.addSection("Recent Chat Requests (oldest to newest)");
        for (let { requestId } of this._recentChats.items) {
            trace.addLine(`${requestId}`);
        }
        if (this._recentChats.items.length === 0) {
            trace.addLine("No recent chat requests");
        }

        trace.addSection("Extension configuration");
        const config = this._augmentConfigListener.config;
        trace.addObject(config);
        trace.addValue("Using API token", !this._auth.useOAuth);
        let apiUrl = "";
        if (this._auth.useOAuth) {
            const session = await this._auth.getSession();
            trace.addValue("Tenant URL", session?.tenantURL);
            apiUrl = session?.tenantURL || "";
        } else {
            apiUrl = config.completionURL;
        }

        if (!this.ready) {
            trace.addLine("Augment extension is initializing");
            trace.publish();
            return;
        }

        trace.addSection("Back-end Configuration");
        trace.addValue(
            "MaxUploadSizeBytes",
            this.featureFlagManager.currentFlags.maxUploadSizeBytes
        );
        trace.addValue(
            "enableCompletionFileEditEvents",
            this.featureFlagManager.currentFlags.enableCompletionFileEditEvents
        );

        trace.addSection("Supported languages (Augment name / VSCode name):");
        for (const language of this._languages) {
            trace.addLine(`${language.name} / ${language.vscodeName}`);
        }

        trace.addSection("Available Models");
        for (const modelName of this._availableModels) {
            const isDefault = this._defaultModel && modelName.startsWith(this._defaultModel);
            const isCurrent = (!config.modelName && isDefault) || modelName === config.modelName;
            const line =
                modelName + (isDefault ? " (default)" : "") + (isCurrent ? " (current)" : "");
            trace.addLine(line);
        }
        if (this._availableModels.length === 0) {
            trace.addLine("No models available");
        }

        trace.addSection("Current Model");
        savePoint = trace.savePoint();
        trace.addLine("Querying current model");
        trace.addLine("(in progress...)");
        try {
            trace.publish();
            trace.rollback(savePoint);
            if (!config.modelName) {
                trace.addLine("(Using default model)");
            }
            trace.addObject(this.modelInfo);
        } catch (e: any) {
            trace.rollback(savePoint);
            if (e instanceof UnknownModelError) {
                trace.addLine(`Model "${config.modelName}" not known.`);
            } else {
                trace.addError(
                    "Unable to query info about model " + `"${config.modelName}": ${getErrmsg(e)}`
                );
            }
        }

        trace.addSection("Blob upload");
        if (config.enableUpload) {
            trace.addLine("Blob upload enabled in configuration settings");
        } else {
            trace.addLine(`Blob upload disabled in configuration settings`);
        }

        if (this.workspaceManager !== undefined) {
            await this.workspaceManager.updateStatusTrace(trace);
        }

        if (apiUrl !== "") {
            trace.addSection("Completion status");
            trace.addLine(`Attempting completion from ${apiUrl}`);
            const requestId = this._apiServer.createRequestId();
            trace.addValue("Request ID", requestId);
            savePoint = trace.savePoint();
            trace.addLine("(in progress...)");
            try {
                trace.publish();
                const startTime = Date.now();
                const completionResult = await this._apiServer.complete(
                    requestId,
                    "this is the prefix",
                    "this is the suffix",
                    "/this/is/the/path",
                    undefined,
                    { prefixBegin: 0, cursorPosition: 0, suffixEnd: 0 },
                    "python",
                    { checkpointId: undefined, addedBlobs: [], deletedBlobs: [] },
                    [],
                    []
                );
                trace.rollback(savePoint);
                trace.addLine(`Response received in ${Date.now() - startTime} ms`);
                if (completionResult.completionItems.length === 0) {
                    trace.addLine("No completion received");
                } else {
                    trace.addLine(
                        `${completionResult.completionItems.length} completion(s) received`
                    );
                }
            } catch (e: any) {
                trace.rollback(savePoint);
                trace.addError(`Completion request failed: ${e}`);
            }
        }

        trace.addSection("Feature Flags");
        trace.addObject(this.featureFlagManager.currentFlags);

        trace.publish();
    }

    /**
     * Handles authentication detection for remote MCP servers.
     * Updates the server configuration to mark it as requiring authentication.
     * OAuth metadata discovery is now handled in the settings webview panel when authentication is initiated.
     */
    private async _handleMcpAuthenticationRequired(serverUrl: string): Promise<void> {
        try {
            if (!this._toolConfigStore) {
                this._logger.warn("Tool config store not available for authentication detection");
                return;
            }

            // Get current MCP servers from storage
            const currentServers = await this._toolConfigStore.getMCPServers();

            // Find the server that failed with 401
            const serverToUpdate = currentServers.find((server) =>
                server.type === "http" || server.type === "sse" ? server.url === serverUrl : false
            );

            if (!serverToUpdate) {
                this._logger.warn(`Could not find MCP server with URL: ${serverUrl}`);
                return;
            }

            // Update the server to mark authentication as required
            // OAuth metadata discovery will happen later when user initiates authentication
            const updatedServers = currentServers.map((server) => {
                if (
                    server.id === serverToUpdate.id &&
                    (server.type === "http" || server.type === "sse")
                ) {
                    return {
                        ...server,
                        authRequired: true,
                    };
                }
                return server;
            });

            // Save the updated servers (this will also update the sidecar)
            await this._toolConfigStore.saveMCPServers(updatedServers);

            // Trigger UI refresh by notifying the settings webview panel
            const currentPanel = SettingsWebviewPanel.currentPanel;
            if (currentPanel && typeof currentPanel.refreshMCPServers === "function") {
                await currentPanel.refreshMCPServers();
            }

            this._logger.info(`Marked MCP server as requiring authentication: ${serverUrl}`);
        } catch (error) {
            this._logger.error(
                `Failed to handle MCP authentication detection: ${getErrmsg(error)}`
            );
        }
    }

    // eslint-disable-next-line @typescript-eslint/require-await
    public async provideTextDocumentContent(uri: vscode.Uri): Promise<string> {
        if (uri.toString() === AugmentExtension.displayStatusUri.toString()) {
            if (this._statusTrace === undefined) {
                return "Internal error. Cannot get Augment extension status.";
            }
            return this._statusTrace.content;
        }
        return "";
    }

    public getExtensionContext(): vscode.ExtensionContext {
        return this._extensionContext;
    }

    // Method for listening on our onDidChange event
    get onDidChange() {
        return this._onTextDocumentDidChange.event;
    }

    clearFileEdits() {
        this.workspaceManager?.clearFileEdits();
    }
}

/**
 * Get the session ID associated with a particular VSCode extension instance
 * @param context VSCode extension context
 * @returns Session ID associated with the vscode instance
 */
export function getSessionId(globalState: AugmentGlobalState): string {
    let sessionId = globalState.get<string>(GlobalContextKey.sessionId);
    if (sessionId === undefined || !uuidValidate(sessionId)) {
        sessionId = uuidv4();
        void globalState.update(GlobalContextKey.sessionId, sessionId);
    }
    return sessionId;
}

/**
 * activate is the entrypoint for the Augment extension.
 *
 * Do not make this an async function. Doing so could lead to a race in which
 * the user deactiveates the extension before this function completes.
 *
 * Note that we do not implement a `deactivate` function to deactivate the extension.
 * If there is no deactivate method then VSCode simply disposes all of the disposables
 * in `context.subscriptions` upon deactivation, hence we make sure that everything
 * we do here is undone via that set of disposables (in particular, see the disp
 * that calls `deactivateExtension`).
 */
export function activate(context: vscode.ExtensionContext) {
    const logger = getLogger("activate()");
    logger.debug("======== Activating extension ========");

    let extension: AugmentExtension | undefined;

    // enableExtension enables the extension in this workspace (only).
    function enableExtension(extension: AugmentExtension): void {
        void extension.enable();
    }

    // disableExtension disables the extension in this workspace (only), closing all
    // of its folders. This function does not dispose the extension object, hence
    // allowing the extension to be reenabled via enableExtension. This is in
    // contrast to deactivateExtension.
    function disableExtension(extension: AugmentExtension): void {
        extension.disable();
    }

    // deactivateExtension deactivates the extension. Deactivating the extension is
    // a global action that affects all workspaces and is different from disabling
    // in that (a) it requires this activate function to be called again in order to
    // reactivate it, and (b) it doesn't affect whether the extension is enabled in
    // any workspace -- it just shuts it off globally.
    function deactivateExtension(): void {
        if (extension) {
            logger.debug("======== Deactivating extension ========");
            disableExtension(extension);
        }
        extension = undefined;
    }

    function reloadExtension(): void {
        logger.info("======== Reloading extension ========");
        disableExtension(extension!);
        enableExtension(extension!);
    }

    // Arrange to shut down the extension when it is deactivated.
    context.subscriptions.push(
        new vscode.Disposable(() => {
            deactivateExtension();
        })
    );

    // An extension can only register a single uri handler in its entire activation lifetime
    context.subscriptions.push(
        vscode.window.registerUriHandler({
            handleUri(uri: vscode.Uri) {
                // Augment.vscode-augment may be passed in as augment.vscode-augment.
                if (uri.authority.toLowerCase() !== context.extension.id.toLowerCase()) {
                    logger.warn(`Ignoring URI ${uri.toString()}`);
                    return;
                }

                if (uri.path.startsWith("/auth/mcp")) {
                    logger.info(`Detected MCP OAuth callback, routing to handler`);
                    // Handle MCP OAuth callback
                    void handleMcpOAuthCallback(
                        uri,
                        extension?.toolsModel,
                        context,
                        extension?.toolConfigStore
                    );
                    return;
                }

                switch (uri.path) {
                    case oauthFlow.authRedirectURI.path:
                        void oauthFlow.handleAuthURI(uri);
                        break;
                    case ACTION_URI_PATHS.openChat: {
                        // Extract parameters from query
                        const params = new URLSearchParams(uri.query);
                        const mode = params.get("mode");
                        if (mode && !["agent", "chat"].includes(mode)) {
                            logger.error(`Invalid chat mode: ${mode}`);
                            break;
                        }
                        void vscode.commands.executeCommand(StartNewChat.commandID, mode);
                        break;
                    }
                    case ACTION_URI_PATHS.openAugmentSettings: {
                        void vscode.commands.executeCommand(ShowSettingsPanelCommand.commandID);
                        break;
                    }
                    case ACTION_URI_PATHS.openGuidelinesSettings: {
                        void vscode.commands.executeCommand(
                            ShowSettingsPanelCommand.commandID,
                            "userGuidelines"
                        );
                        break;
                    }
                    case ACTION_URI_PATHS.openMemories: {
                        const memoriesAbsPath = extension?.toolsModel?.memoriesAbsPath;
                        if (memoriesAbsPath) {
                            const memoriesFileUri = vscode.Uri.file(memoriesAbsPath);
                            void vscode.commands.executeCommand("vscode.open", memoriesFileUri);
                        } else {
                            logger.warn("Could not open memories: path not found.");
                        }
                        break;
                    }
                    default:
                        // Log the URI without the query or fragment parameters.
                        logger.error(
                            `Unhandled URI ${vscode.Uri.from({
                                scheme: uri.scheme,
                                authority: uri.authority,
                                path: uri.path,
                            }).toString()}`
                        );
                }
            },
        })
    );

    const userAgentOsString = `${os.platform()}; ${os.arch()}; ${os.release()}`;
    // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
    const userAgent = `${context.extension.id}/${context.extension.packageJSON.version} (${userAgentOsString}) ${vscode.env.uriScheme}/${vscode.version}`;

    const globalState = new AugmentGlobalState(context);
    context.subscriptions.push(globalState);
    const sessionId = getSessionId(globalState);

    const configListener = new AugmentConfigListener();
    void configListener.migrateLegacyConfig();

    const authSession = new AuthSessionStore(context, configListener);
    context.subscriptions.push(authSession);
    const syncingEnabledTracker = new SyncingEnabledTracker();
    context.subscriptions.push(syncingEnabledTracker);

    // The config and auth from the sidecar library are used by the API server
    // so need to be initialized before it
    setLibraryClientConfig(new ClientConfig(configListener));
    setLibraryClientAuth(new ClientAuth(authSession, configListener));

    const apiServer = new APIServerImplWithErrorReporting(
        configListener,
        authSession,
        sessionId,
        userAgent,
        global.fetch
    );
    const recentCompletions = new RecentCompletions();
    const recentInstructions = new RecentItems<AugmentInstruction>(10);
    const recentChats = new RecentItems<ChatRequest>(10);
    const recentNextEditResults = new RecentItems<NextEditResultInfo>(10);
    const nextEditWebViewEvent = new vscode.EventEmitter<NextEditWebViewMessage>();
    const chatExtensionEvent = new vscode.EventEmitter<ChatExtensionMessage>();
    const changeWebviewAppEvent = new vscode.EventEmitter<MainPanelApp>();
    const extensionUpdateEvent = new vscode.EventEmitter<void>();
    const assetManager = new AssetManager(context);
    const gitOperationsService = new GitOperationsService(
        new GitStateManager({
            defaultBackupDir: context.storageUri?.fsPath,
        })
    );

    const mainPanelProvider = new MainPanelWebviewProvider(context.extensionUri, authSession);
    mainPanelProvider.onVisibilityChange((visible) => {
        if (!visible) {
            PreferenceWebviewPanel.currentPanel?.dispose();
        }
    });

    const actionsModel = new ActionsModel(globalState);
    context.subscriptions.push(actionsModel);

    const onboardingSessionEventReporter = new OnboardingSessionEventReporter(apiServer);
    const oauthFlow = new OAuthFlow(
        context,
        configListener,
        apiServer,
        authSession,
        onboardingSessionEventReporter
    );
    const signInApp = new SignInApp(apiServer, configListener, oauthFlow, actionsModel);

    function onDerivedStates(derivedStates: DerivedState[]) {
        outerLoop: for (const derivedState of derivedStates) {
            switch (derivedState.name) {
                case DerivedStateName.signInRequired: {
                    mainPanelProvider.changeApp(signInApp);
                    showAugmentPanel(configListener.config);
                    break outerLoop;
                }
                case DerivedStateName.workspaceNotSelected: {
                    // If the workspace is not populated, show the folder selection app
                    // but only if the user is already authenticated
                    if (actionsModel.isSystemStateComplete(SystemStateName.authenticated)) {
                        changeWebviewAppEvent.fire(MainPanelApp.folderSelection);
                    }
                    break outerLoop;
                }
                case DerivedStateName.disableCopilot:
                case DerivedStateName.disableCodeium:
                case DerivedStateName.syncingPermissionNeeded:
                case DerivedStateName.uploadingHomeDir:
                case DerivedStateName.workspaceTooLarge:
                    showAugmentPanel(configListener.config);
                    break outerLoop;
            }
        }

        // If the user has already authenticated but has not given syncing permission, or
        // has either uploaded their home directory or has a workspace that is too large,
        // we should show the AwaitingSyncingPermission app
        if (
            actionsModel.isDerivedStateSatisfied(DerivedStateName.syncingPermissionNeeded) ||
            actionsModel.isDerivedStateSatisfied(DerivedStateName.uploadingHomeDir) ||
            actionsModel.isDerivedStateSatisfied(DerivedStateName.workspaceTooLarge)
        ) {
            changeWebviewAppEvent.fire(MainPanelApp.awaitingSyncingPermission);
        }
    }

    context.subscriptions.push(
        authSession.onDidChangeSession(() => {
            // If the auth state of the extension changes, reload the extension.
            reloadExtension();
        })
    );
    // Ensure the actions model sign in state is correct.
    context.subscriptions.push(new AuthActionsModel(actionsModel, authSession, configListener));

    // Register Augment Panel WebView Provider
    context.subscriptions.push(
        vscode.window.registerWebviewViewProvider("augment-chat", mainPanelProvider, {
            webviewOptions: {
                retainContextWhenHidden: true,
            },
        })
    );

    // Create the extension object, even if the extension is disabled in this
    // workspace, as doing so enables the quick-pick that allows the user to
    // easily enable the extension. Then, if the extension is supposed to be
    // enabled in this workspace, enable it.
    extension = new AugmentExtension(
        context,
        globalState,
        configListener,
        apiServer,
        authSession,
        recentCompletions,
        recentInstructions,
        recentNextEditResults,
        recentChats,
        nextEditWebViewEvent,
        extensionUpdateEvent,
        mainPanelProvider,
        changeWebviewAppEvent,
        actionsModel,
        syncingEnabledTracker,
        chatExtensionEvent,
        onboardingSessionEventReporter,
        assetManager,
        gitOperationsService
    );

    function showAugmentPanel(config: AugmentConfig) {
        if (!config.disableFocusOnAugmentPanel && !mainPanelProvider.isVisible()) {
            // We only want to show the panel and never want this call to trigger
            // the sign in flow.
            void vscode.commands.executeCommand(FocusAugmentPanel.commandID);
        }
    }

    context.subscriptions.push(actionsModel.onDerivedStatesSatisfied(onDerivedStates));
    onDerivedStates(actionsModel.satisfiedStates);

    configListener.onDidChange((event) => {
        if (event.newConfig.enableDebugFeatures === event.previousConfig.enableDebugFeatures) {
            return;
        }
    });
    context.subscriptions.push(
        configListener.onDidChange((change) => {
            let reloadNeeded = false;
            const keys: Array<AugmentConfigKey> = [
                "apiToken",
                "completionURL",
                "oauth",
                "modelName",
            ];
            for (const k of keys) {
                if (!isEqual(change.previousConfig[k], change.newConfig[k])) {
                    reloadNeeded = true;
                    break;
                }
            }
            if (reloadNeeded) {
                logger.info("Reloading extension due to config change");
                reloadExtension();
            }
        })
    );

    // Register this extension as a text document provider for our content scheme.
    context.subscriptions.push(
        vscode.workspace.registerTextDocumentContentProvider(
            AugmentExtension.contentScheme,
            extension
        )
    );

    registerWebviewManager(context);
    setupContextKeySync(extension, configListener, context);

    // Initialize the extension commands
    const commandManager = initCommandManager(
        context,
        extension,
        configListener,
        authSession,
        oauthFlow,
        apiServer,
        recentCompletions,
        recentInstructions,
        recentNextEditResults,
        changeWebviewAppEvent,
        chatExtensionEvent,
        syncingEnabledTracker,
        globalState,
        context.workspaceState
    );
    context.subscriptions.push(commandManager);

    context.subscriptions.push(extensionUpdateEvent);

    // Register custom editors
    context.subscriptions.push(AugmentRulesEditorProvider.register(context, configListener));
    context.subscriptions.push(
        AugmentMemoriesEditorProvider.register(context, configListener, extension)
    );

    // enable the extension
    enableExtension(extension);
}

// ================================================================
// Utility functions that are only used in this file. DO NOT EXPORT
// ================================================================
function setContextBatch(contextKeysToValues: { [key: string]: boolean }) {
    for (const [key, val] of Object.entries(contextKeysToValues)) {
        void vscode.commands.executeCommand("setContext", key, val);
    }
}

function setupContextKeySync(
    extension: AugmentExtension,
    configListener: AugmentConfigListener,
    context: vscode.ExtensionContext
) {
    const syncContextFromConfig = () => {
        const c: AugmentConfig = configListener.config;
        setContextBatch({
            /* eslint-disable @typescript-eslint/naming-convention */
            "vscode-augment.enableDebugFeatures": c.enableDebugFeatures,
            "vscode-augment.enableReviewerWorkflows": c.enableReviewerWorkflows,
            "vscode-augment.enableNextEdit": isNextEditEnabled(
                configListener.config,
                extension?.featureFlagManager.currentFlags.vscodeNextEditMinVersion ?? ""
            ),
            "vscode-augment.enableNextEditBackgroundSuggestions": isNextEditBackgroundEnabled(
                configListener.config,
                extension?.featureFlagManager.currentFlags.vscodeNextEditMinVersion ?? ""
            ),
            "vscode-augment.enableGenerateCommitMessage": isExtensionVersionGte(
                extension?.featureFlagManager.currentFlags.vscodeGenerateCommitMessageMinVersion ??
                    ""
            ),
            "vscode-augment.nextEdit.enablePanel":
                extension.nextEditConfigManager.config.enablePanel,
            "vscode-augment.featureFlags.enableRemoteAgents":
                isExtensionVersionGte(
                    extension?.featureFlagManager.currentFlags.vscodeBackgroundAgentsMinVersion ??
                        ""
                ) ?? false,
            /* eslint-enable @typescript-eslint/naming-convention */
        });
    };
    syncContextFromConfig();
    context.subscriptions.push(configListener.onDidChange(syncContextFromConfig));

    // Update MONITOR_FLAGS if you need to monitor flags other than the listed flags
    const MONITORED_FLAGS: Array<keyof FeatureFlags> = [
        "enableWorkspaceManagerUi",
        "enableSmartPaste",
        "enableSmartPasteMinVersion",
        "enableInstructions",
        "vscodeSourcesMinVersion",
        "vscodeChatHintDecorationMinVersion",
        "vscodeEnableCpuProfile",
        "vscodeNextEditMinVersion",
        "vscodeGenerateCommitMessageMinVersion",
    ];
    const syncContextFromFlags = () => {
        if (!extension) {
            return;
        }
        const f: FeatureFlags = extension.featureFlagManager.currentFlags;
        setContextBatch({
            /* eslint-disable @typescript-eslint/naming-convention */
            "vscode-augment.workspace-manager-ui.enabled": f.enableWorkspaceManagerUi,
            "vscode-augment.internal-new-instructions.enabled": f.enableInstructions,
            "vscode-augment.internal-dv.enabled":
                isExtensionVersionGte(f.enableSmartPasteMinVersion) || f.enableInstructions,
            "vscode-augment.sources-enabled":
                isExtensionVersionGte(f.vscodeSourcesMinVersion) ?? false,
            "vscode-augment.chat-hint.decoration":
                isExtensionVersionGte(f.vscodeChatHintDecorationMinVersion) ?? false,
            "vscode-augment.cpu-profile.enabled": f.vscodeEnableCpuProfile,
            "vscode-augment.enableGenerateCommitMessage":
                isExtensionVersionGte(f.vscodeGenerateCommitMessageMinVersion) ?? false,
            "vscode-augment.featureFlags.enableRemoteAgents":
                isExtensionVersionGte(f.vscodeBackgroundAgentsMinVersion) ?? false,
            "vscode-augment.nextEdit.enablePanel":
                extension.nextEditConfigManager.config.enablePanel,
            /* eslint-enable @typescript-eslint/naming-convention */
        });
    };
    syncContextFromFlags();
    context.subscriptions.push(
        extension.featureFlagManager.subscribe(MONITORED_FLAGS, syncContextFromFlags)
    );
    // We must ALSO update the config based keys since some of those depend on flags too.
    context.subscriptions.push(
        extension.featureFlagManager.subscribe(MONITORED_FLAGS, syncContextFromConfig)
    );
}

export const _exportedForTesting = {
    setupContextKeySync,
};
