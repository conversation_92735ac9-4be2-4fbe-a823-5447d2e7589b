import {
    disposeAnalytics,
    initializeAnalytics,
    InitializeAnalyticsCommand,
    trackEvent,
} from "@augment-internal/sidecar-libs/src/client-interfaces/analytics";
import * as os from "os";
import * as vscode from "vscode";

import { AugmentConfigListener } from "../augment-config-listener";
import { getLogger } from "../logging";
import { AugmentGlobalState, GlobalContextKey } from "../utils/context";
import { DisposableService } from "../utils/disposable-service";

export interface AnalyticsManagerConfig {
    context: vscode.ExtensionContext;
    configListener: AugmentConfigListener;
    globalState: AugmentGlobalState;
    sessionId: string;
    tenantId?: string;
}

/**
 * Manages Segment analytics for the VS Code extension.
 */
export class AnalyticsManager extends DisposableService {
    private readonly _logger = getLogger("AnalyticsManager");
    private readonly _segmentWriteKey: string;
    private readonly _extensionVersion: string;
    private readonly _sessionId: string;

    constructor(private readonly _config: AnalyticsManagerConfig) {
        super();

        switch (_config.context.extensionMode) {
            case vscode.ExtensionMode.Test:
                this._segmentWriteKey = "";
                break;
            case vscode.ExtensionMode.Development:
                this._segmentWriteKey = "eRx2DEU2tx5boyRfgRnotNsTDKhkykS8";
                break;
            case vscode.ExtensionMode.Production:
                this._segmentWriteKey = "ki6UFHBqWCiflWcrR6BWiVUeEIVXLdkg";
                break;
        }

        // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment, @typescript-eslint/no-unsafe-member-access
        this._extensionVersion = _config.context.extension.packageJSON.version ?? "unknown";

        this._sessionId = _config.sessionId;

        // Listen for config changes
        this.addDisposable(
            _config.configListener.onDidChange(() => {
                void this._reinitializeAnalytics();
            })
        );

        // Listen for VS Code telemetry changes
        this.addDisposable(
            vscode.env.onDidChangeTelemetryEnabled(() => {
                void this._reinitializeAnalytics();
            })
        );
    }

    private _shouldEnableAnalytics(): boolean {
        return vscode.env.isTelemetryEnabled;
    }

    /**
     * Extracts tenant identifier from a tenant URL.
     * @param tenantUrl The tenant URL (e.g., "https://tenant-name.api.augmentcode.com/")
     * @returns The tenant identifier or undefined if extraction fails
     */
    public static extractTenantIdFromUrl(tenantUrl: string): string | undefined {
        try {
            const url = new URL(tenantUrl);
            const host = url.hostname;
            const parts = host.split(".");

            // Handle formats like "tenant-name.api.augmentcode.com" or "tenant-name.us-central.api.augmentcode.com"
            if (parts.length >= 3 && parts.includes("augmentcode")) {
                return parts[0]; // First part is typically the tenant name
            }
            return host; // fallback to full hostname
        } catch (error) {
            return undefined;
        }
    }

    public initialize(): void {
        const shouldEnableAnalytics = this._shouldEnableAnalytics();

        if (!shouldEnableAnalytics) {
            void disposeAnalytics();
            return;
        }

        const command: InitializeAnalyticsCommand = {
            writeKey: this._segmentWriteKey,
            context: {
                clientType: "vscode",
                clientVersion: this._extensionVersion,
                platform: os.platform(),
                arch: os.arch(),
                tenantId: this._config.tenantId,
            },
            anonymousId: this._sessionId,
        };

        initializeAnalytics(command);

        this._logger.debug(
            `Analytics initialized and enabled with tenantId: ${this._config.tenantId}`
        );

        this._trackInstallEventIfNeeded();
    }

    private _trackInstallEventIfNeeded(): void {
        const hasTrackedInstall = this._config.globalState.get<boolean>(
            GlobalContextKey.hasTrackedInstall
        );

        if (hasTrackedInstall) {
            return;
        }

        if (!this._shouldEnableAnalytics()) {
            return;
        }

        const lastEnabledVersion = this._config.globalState.get<string>(
            GlobalContextKey.lastEnabledExtensionVersion
        );

        // Only track as "extension_installed" if there's no previous version record
        // This indicates a genuine first-time installation
        if (!lastEnabledVersion) {
            trackEvent("extension_installed");
        }

        void this._config.globalState.update(GlobalContextKey.hasTrackedInstall, true);
    }

    private async _reinitializeAnalytics(): Promise<void> {
        this._logger.debug("Reinitializing analytics due to config change");

        await disposeAnalytics();

        // Reinitialize with new settings
        this.initialize();
    }

    protected async onDispose(): Promise<void> {
        await disposeAnalytics();
        this._logger.debug("Analytics manager disposed");
    }
}
