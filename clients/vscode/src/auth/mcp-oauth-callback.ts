import { getErrmsg } from "@augment-internal/sidecar-libs/src/exceptions";
import { isStreamingServer } from "@augment-internal/sidecar-libs/src/tools/mcp-server-utils";
import {
    PARTNER_MCP_SERVERS,
    REMOTE_PARTNER_MCP_CONFIG,
    REMOTE_PARTNER_MCP_SERVERS,
} from "@augment-internal/sidecar-libs/src/tools/partner-remote-mcp";
import { ToolsModel } from "@augment-internal/sidecar-libs/src/tools/tools-model";
import * as vscode from "vscode";

import { getLogger } from "../logging";
import { SettingsWebviewPanel } from "../webview-panels/settings-webview-panel";
import { ToolConfigStore } from "../webview-panels/stores/tool-config-store";
import { MCPServer, MCPServerHttp } from "../webview-providers/webview-messages";

const logger = getLogger("McpOAuthCallback");

// Storage keys for MCP OAuth state
const MCP_ACCESS_TOKEN_KEY_PREFIX = "augment.mcp-access-token"; // pragma: allowlist secret

/**
 * Handles OAuth callback for MCP servers.
 * This function is called when the browser redirects back to VSCode after user authorization.
 *
 * @param uri - The callback URI containing the authorization code
 * @param toolsModel - The tools model to update with new MCP server configuration
 * @param context - The VSCode extension context for accessing secrets
 * @param toolConfigStore - The tool config store for updating UI storage
 */
export async function handleMcpOAuthCallback(
    uri: vscode.Uri,
    toolsModel: ToolsModel | undefined,
    context: vscode.ExtensionContext,
    toolConfigStore?: ToolConfigStore
): Promise<void> {
    try {
        logger.info(`Processing MCP OAuth callback: ${uri.toString()}`);

        // Extract MCP name from the path: /auth/mcp/{mcpName}
        const pathParts = uri.path.split("/");
        if (pathParts.length < 4 || pathParts[1] !== "auth" || pathParts[2] !== "mcp") {
            throw new Error(`Invalid MCP OAuth callback path: ${uri.path}`);
        }
        const extractedName = pathParts[3];
        const mcpName =
            Object.values(REMOTE_PARTNER_MCP_SERVERS).find((key) => key.includes(extractedName)) ??
            extractedName;
        logger.info(`Handling OAuth callback for MCP: ${mcpName}`);

        // Parse query parameters
        const query = new URLSearchParams(uri.query);
        const code = query.get("code");
        const error = query.get("error");

        // Check for OAuth errors
        if (error) {
            const errorDescription = query.get("error_description");
            const errorMessage = errorDescription ? `${error}: ${errorDescription}` : error;
            throw new Error(`OAuth authorization failed: ${errorMessage}`);
        }

        if (!code) {
            throw new Error("No authorization code received");
        }

        // Retrieve stored OAuth state from sidecar's ToolsModel
        if (!toolsModel) {
            throw new Error("ToolsModel is required for OAuth callback handling");
        }

        const partners: REMOTE_PARTNER_MCP_SERVERS[] = Object.values(REMOTE_PARTNER_MCP_SERVERS);
        const isPartnerMCP = partners.includes(mcpName as REMOTE_PARTNER_MCP_SERVERS);

        const storedName: REMOTE_PARTNER_MCP_SERVERS | undefined = partners.find((key) => {
            const storedRedirectUri =
                REMOTE_PARTNER_MCP_CONFIG[key].authDefinition.params.redirect_uri;
            try {
                const storedUri = vscode.Uri.parse(storedRedirectUri);
                return storedUri.path === uri.path;
            } catch {
                return storedRedirectUri === uri.toString();
            }
        });

        const storedState = await toolsModel.getMcpOAuthState(
            isPartnerMCP && storedName ? storedName : mcpName
        );
        if (!storedState) {
            throw new Error(
                `No OAuth state found for MCP: ${mcpName}. ` +
                    `This may happen if the OAuth flow wasn't properly initiated or the state expired (10 minutes). ` +
                    `Please try clicking the "Authenticate" button again to restart the OAuth flow.`
            );
        }

        // Check if the state is not too old (10 minutes timeout)
        const OAUTH_TIMEOUT_MS = 10 * 60 * 1000;
        if (Date.now() - storedState.creationTime > OAUTH_TIMEOUT_MS) {
            throw new Error(`OAuth state expired for MCP: ${mcpName}`);
        }

        // Exchange authorization code for access token
        const accessToken = await exchangeCodeForToken(
            mcpName,
            code,
            storedState.codeVerifier,
            storedState.clientId,
            toolConfigStore
        );

        // Store the access token securely
        const tokenKey = `${MCP_ACCESS_TOKEN_KEY_PREFIX}.${mcpName}`;
        await context.secrets.store(tokenKey, accessToken);

        // Verify the access token was stored correctly
        const storedToken = await context.secrets.get(tokenKey);
        if (!storedToken) {
            throw new Error(`Failed to store access token for MCP: ${mcpName}`);
        }

        // Clean up OAuth state from sidecar
        await toolsModel.clearMcpOAuthState(mcpName);

        logger.info(`Successfully stored access token for MCP: ${mcpName}`);

        // Create new MCP server with access token and add it to UI storage
        if (toolConfigStore) {
            logger.info(`Creating MCP server ${mcpName} with stored access token`);

            // Get current servers from UI storage
            const currentServers = await toolConfigStore.getMCPServers();

            // Check if server with same name already exists
            const existingServer = currentServers.find((server) => server.name === mcpName);
            if (existingServer) {
                await toolConfigStore.updateSidecarMCPServers();

                // Give the sidecar a moment to process the configuration update
                await new Promise((resolve) => setTimeout(resolve, 1000));

                // Notify the settings webview panel to refresh its server list
                const currentPanel = SettingsWebviewPanel.currentPanel;
                if (currentPanel && typeof currentPanel.refreshMCPServers === "function") {
                    currentPanel.refreshMCPServers().catch((refreshError: unknown) => {
                        logger.warn(
                            `Failed to refresh MCP servers in UI: ${getErrmsg(refreshError)}`
                        );
                    });
                }
            }

            if (isPartnerMCP) {
                // Create the new server in UI format (without accessToken field for security)
                const newUIServer: Omit<MCPServerHttp, "id"> = {
                    type: "http",
                    name: mcpName,
                    url: getMcpServerUrl(mcpName),
                    disabled: false,
                };

                // Add the new server to UI storage (this will also update the sidecar with access token)
                const updatedServers = [
                    ...currentServers,
                    { ...newUIServer, id: crypto.randomUUID() } as MCPServer,
                ];

                logger.info(
                    `Saving MCP server ${mcpName} to UI storage and updating sidecar with access token`
                );
                await toolConfigStore.saveMCPServers(updatedServers);
                logger.info(`Successfully saved MCP server ${mcpName} and updated sidecar`);

                // Give the sidecar a moment to process the configuration update
                await new Promise((resolve) => setTimeout(resolve, 1000));

                // Notify the settings webview panel to refresh its server list
                // Note: Temporarily commented out due to TypeScript issues
                const currentPanel = SettingsWebviewPanel.currentPanel;
                if (currentPanel && typeof currentPanel.refreshMCPServers === "function") {
                    currentPanel.refreshMCPServers().catch((refreshError: unknown) => {
                        logger.warn(
                            `Failed to refresh MCP servers in UI: ${getErrmsg(refreshError)}`
                        );
                    });
                }
            }
        }
    } catch (error) {
        const errorMessage = getErrmsg(error);
        logger.error(`MCP OAuth callback failed: ${errorMessage}`);

        // Show error notification to user
        void vscode.window.showErrorMessage(`Authentication failed: ${errorMessage}`);
    }
}

/**
 * Exchanges the authorization code for an access token using the appropriate OAuth endpoint.
 *
 * @param mcpName - The name of the MCP server
 * @param code - The authorization code from the OAuth callback
 * @param codeVerifier - The PKCE code verifier
 * @param clientId - The dynamically generated client ID (optional, falls back to hardcoded values)
 * @returns The access token
 */
async function exchangeCodeForToken(
    mcpName: string,
    code: string,
    codeVerifier: string,
    clientId: string,
    toolConfigStore?: ToolConfigStore
): Promise<string> {
    logger.info(
        `Exchanging authorization code for access token: ${mcpName}${clientId ? ` with client ID: ${clientId}` : ""}`
    );

    // Get the appropriate token endpoint and parameters based on MCP name
    const tokenConfig = await getTokenExchangeConfig(mcpName, clientId, toolConfigStore);

    // Prepare the token exchange request
    const tokenRequest = {
        method: "POST",
        headers: {
            // eslint-disable-next-line @typescript-eslint/naming-convention
            "Content-Type": "application/x-www-form-urlencoded",
            // eslint-disable-next-line @typescript-eslint/naming-convention
            Accept: "application/json",
        },
        body: new URLSearchParams({
            // eslint-disable-next-line @typescript-eslint/naming-convention
            grant_type: "authorization_code",
            code,
            // eslint-disable-next-line @typescript-eslint/naming-convention
            code_verifier: codeVerifier,
            ...tokenConfig.additionalParams,
        }).toString(),
    };

    // Add authentication headers if required
    if (tokenConfig.clientAuth) {
        tokenRequest.headers = {
            ...tokenRequest.headers,
            ...tokenConfig.clientAuth,
        };
    }

    try {
        const response = await fetch(tokenConfig.tokenUrl, tokenRequest);

        if (!response.ok) {
            const errorText = await response.text();
            throw new Error(`Token exchange failed (${response.status}): ${errorText}`);
        }

        const tokenResponse = (await response.json()) as Record<string, unknown>;

        const accessToken = tokenResponse["access_token"] as string;
        if (!accessToken) {
            throw new Error("No access token in response");
        }

        logger.info(`Successfully exchanged code for access token: ${mcpName}`);
        return accessToken;
    } catch (error) {
        logger.error(`Token exchange failed for ${mcpName}: ${getErrmsg(error)}`);
        throw error;
    }
}

/**
 * Gets the actual MCP server URL for a specific MCP server.
 * This should match the URLs used in the OAuth flow generation.
 *
 * @param mcpName - The name of the MCP server
 * @returns The actual MCP server URL
 */
function getMcpServerUrl(mcpName: string): string {
    switch (mcpName.toLowerCase()) {
        case PARTNER_MCP_SERVERS.STRIPE as string:
            return "https://mcp.stripe.com";
        case PARTNER_MCP_SERVERS.SENTRY as string:
            return "https://mcp.sentry.dev/mcp";
        default:
            throw new Error(`Unsupported MCP server for OAuth: ${mcpName}`);
    }
}

/**
 * Gets the token exchange configuration for a specific MCP server.
 *
 * @param mcpName - The name of the MCP server
 * @param clientId - The dynamically generated client ID (optional, falls back to hardcoded values)
 * @returns Token exchange configuration
 */
async function getTokenExchangeConfig(
    mcpName: string,
    clientId: string,
    toolConfigStore?: ToolConfigStore
): Promise<{
    tokenUrl: string;
    additionalParams: Record<string, string>;
    clientAuth?: Record<string, string>;
}> {
    // First check if this is a hardcoded partner MCP server
    const partnerMcpName = mcpName.toLowerCase();
    if (Object.values(PARTNER_MCP_SERVERS).includes(partnerMcpName as PARTNER_MCP_SERVERS)) {
        switch (partnerMcpName as PARTNER_MCP_SERVERS) {
            case PARTNER_MCP_SERVERS.STRIPE:
                return {
                    tokenUrl:
                        REMOTE_PARTNER_MCP_CONFIG[PARTNER_MCP_SERVERS.STRIPE].authDefinition
                            .tokenUrl,
                    additionalParams: {
                        // eslint-disable-next-line @typescript-eslint/naming-convention
                        client_id: clientId,
                        // eslint-disable-next-line @typescript-eslint/naming-convention
                        redirect_uri:
                            REMOTE_PARTNER_MCP_CONFIG[PARTNER_MCP_SERVERS.STRIPE].authDefinition
                                .params.redirect_uri,
                    },
                };

            case PARTNER_MCP_SERVERS.SENTRY:
                return {
                    tokenUrl:
                        REMOTE_PARTNER_MCP_CONFIG[PARTNER_MCP_SERVERS.SENTRY].authDefinition
                            .tokenUrl,
                    additionalParams: {
                        // eslint-disable-next-line @typescript-eslint/naming-convention
                        client_id: clientId,
                        // eslint-disable-next-line @typescript-eslint/naming-convention
                        redirect_uri:
                            REMOTE_PARTNER_MCP_CONFIG[PARTNER_MCP_SERVERS.SENTRY].authDefinition
                                .params.redirect_uri,
                    },
                };

            default:
                throw new Error(`Unsupported partner MCP server for OAuth: ${mcpName}`);
        }
    }

    // Handle generic MCP servers with discovered OAuth metadata
    if (!toolConfigStore) {
        throw new Error(
            `Cannot exchange tokens for generic MCP server ${mcpName}: no tool config store available`
        );
    }

    try {
        // Get the MCP server configuration from the tool config store
        const mcpServers = await toolConfigStore.getMCPServers();
        const mcpServer = mcpServers.find((server) => server.name === mcpName);

        if (!mcpServer) {
            throw new Error(`MCP server configuration not found: ${mcpName}`);
        }

        // Check if it's an HTTP server with OAuth metadata
        if (!isStreamingServer(mcpServer)) {
            throw new Error(`MCP server ${mcpName} is not an HTTP server and cannot use OAuth`);
        }

        // Type guard: we know it's an HTTP server now
        const httpServer = mcpServer;
        if (!httpServer.oauthMetadata) {
            throw new Error(
                `MCP server ${mcpName} does not have OAuth metadata. Please ensure OAuth metadata discovery has been completed before attempting authentication.`
            );
        }

        const oauthMetadata = httpServer.oauthMetadata;
        if (!oauthMetadata.token_endpoint) {
            throw new Error(`MCP server ${mcpName} OAuth metadata is missing token_endpoint`);
        }

        return {
            tokenUrl: oauthMetadata.token_endpoint,
            additionalParams: {
                // eslint-disable-next-line @typescript-eslint/naming-convention
                client_id: clientId,
                // eslint-disable-next-line @typescript-eslint/naming-convention
                redirect_uri: `vscode://augment.vscode-augment/auth/mcp/${mcpName}`,
            },
        };
    } catch (error) {
        logger.error(`Failed to get token exchange config for ${mcpName}: ${getErrmsg(error)}`);
        throw new Error(`Failed to get token exchange config for ${mcpName}: ${getErrmsg(error)}`);
    }
}

/**
 * Deletes stored access token for an MCP server.
 *
 * @param mcpName - The name of the MCP server
 * @param context - The VSCode extension context for accessing secrets
 * @returns Promise that resolves when the token is deleted
 */
export async function deleteMcpAccessToken(
    mcpName: string,
    context: vscode.ExtensionContext
): Promise<void> {
    const tokenKey = `${MCP_ACCESS_TOKEN_KEY_PREFIX}.${mcpName}`;

    try {
        // Check if token exists before attempting to delete
        const storedToken = await context.secrets.get(tokenKey);
        if (!storedToken) {
            logger.info(`No access token found to delete for MCP: ${mcpName}`);
            return;
        }

        // Delete the token from secrets storage
        await context.secrets.delete(tokenKey);
        logger.info(`Successfully deleted access token for MCP: ${mcpName}`);
    } catch (error) {
        const errorMessage = getErrmsg(error);
        logger.error(`Failed to delete access token for MCP ${mcpName}: ${errorMessage}`);
        throw new Error(`Failed to delete access token for MCP ${mcpName}: ${errorMessage}`);
    }
}
