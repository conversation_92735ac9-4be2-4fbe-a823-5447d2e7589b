import { fromVscodeDiagnosticMap } from "$vscode/src/chat/diagnostics-utils";

import { AggregateCheckpointManager } from "@augment-internal/sidecar-libs/src/agent/checkpoint/aggregate-checkpoint-manager";
import { ChatMode, Exchange } from "@augment-internal/sidecar-libs/src/chat/chat-types";
import { IPluginFileStore } from "@augment-internal/sidecar-libs/src/client-interfaces/plugin-file-store";
import { DiffViewDocument } from "@augment-internal/sidecar-libs/src/diff-view/document";
import { AgentEditTools } from "@augment-internal/sidecar-libs/src/tools/sidecar-tools/sidecar-tool-types";
import { StrReplaceEditorWithDiagnosticsTool } from "@augment-internal/sidecar-libs/src/tools/sidecar-tools/str-replace-editor-tool/str-replace-editor-with-diagnostics-tool";
import {
    errorToolResponse,
    successToolResponse,
} from "@augment-internal/sidecar-libs/src/tools/sidecar-tools/tool-use-response";
import { IToolHost } from "@augment-internal/sidecar-libs/src/tools/tool-host";
import { ITool, ToolHostBase } from "@augment-internal/sidecar-libs/src/tools/tool-host-base";
import {
    ToolBase,
    ToolHostName,
    ToolHostOptions,
    ToolSafety,
    ToolUseResponse,
} from "@augment-internal/sidecar-libs/src/tools/tool-types";
import { formatDiagnostics } from "@augment-internal/sidecar-libs/src/utils/diagnostics-utils";
import { delayMs } from "@augment-internal/sidecar-libs/src/utils/promise-utils";
import * as vscode from "vscode";

import { APIServer } from "../augment-api";
import { AugmentConfigListener } from "../augment-config-listener";
import { FeatureFlagManager } from "../feature-flags";
import { getLogger } from "../logging";
import { AugmentGlobalState } from "../utils/context";
import { GitCommitIndexer } from "../vcs/git-commit-indexer";
import { LocalToolType } from "../webview-providers/tool-types";
import { WorkspaceManager } from "../workspace/workspace-manager";
import {
    ApplyWorkerAgentEditsTool,
    DeleteWorkerAgentTool,
    ReadWorkerAgentEditsTool,
    ReadWorkerStateTool,
    SendInstructionToWorkerAgentTool,
    StartWorkerAgentTool,
    StopWorkerAgentTool,
    WaitForWorkerAgentTool,
} from "./agent-orchestration-tools";
import {
    TerminalKillProcessTool,
    TerminalLaunchProcessTool,
    TerminalListProcessesTool,
    TerminalProcessTools,
    TerminalReadProcessTool,
    TerminalWriteProcessTool,
} from "./terminal-tools";
import { _getQualifiedPath, _readFile } from "./tools-utils";

export const localToolHostFactory = (
    apiServer: APIServer,
    workspaceManager: WorkspaceManager,
    checkpointManager: AggregateCheckpointManager,
    featureFlagManager: FeatureFlagManager,
    extensionRoot: vscode.Uri,
    globalState: AugmentGlobalState,
    enableStart: number,
    gitCommitIndexer: GitCommitIndexer | undefined,
    configListener: AugmentConfigListener,
    assetManager?: IPluginFileStore
) => {
    return (options: ToolHostOptions) => {
        return new LocalToolHost(
            options.chatMode,
            workspaceManager,
            apiServer,
            checkpointManager,
            featureFlagManager,
            extensionRoot,
            globalState,
            enableStart,
            gitCommitIndexer,
            configListener,
            options.enableAgentSwarmMode,
            assetManager
        );
    };
};

/**
 * Get all current diagnostics from VSCode
 */
function getDiagnostics(): Map<string, vscode.Diagnostic[]> {
    const result = new Map<string, vscode.Diagnostic[]>();
    vscode.languages.getDiagnostics().forEach(([uri, diagnostics]) => {
        if (diagnostics.length > 0) {
            result.set(uri.fsPath, diagnostics);
        }
    });
    return result;
}

/**
 * A tool host for tools that run locally in the IDE.
 */
export class LocalToolHost extends ToolHostBase<LocalToolType> {
    private readonly _terminalProcessTools: TerminalProcessTools | undefined;

    constructor(
        private readonly _chatMode: ChatMode,
        private readonly _workspaceManager: WorkspaceManager,
        private readonly _apiServer: APIServer,
        private readonly _checkpointManager: AggregateCheckpointManager,
        private readonly _featureFlagManager: FeatureFlagManager,
        private readonly _extensionRoot: vscode.Uri,
        private readonly _globalState: AugmentGlobalState,
        private readonly _enableStart: number,
        private readonly _gitCommitIndexer: GitCommitIndexer | undefined,
        private readonly _configListener: AugmentConfigListener,
        private readonly _swarmModeEnabled: boolean,
        private readonly _assetManager?: IPluginFileStore
    ) {
        const tools: ITool<LocalToolType>[] = [];
        let terminalProcessTools: TerminalProcessTools | undefined = undefined;
        if (_chatMode === ChatMode.agent) {
            if (
                _featureFlagManager.currentFlags?.vscodeAgentEditTool !==
                AgentEditTools.strReplaceEditor
            ) {
                tools.push(new ReadFileTool(_workspaceManager));
                tools.push(new EditFileTool(_workspaceManager, _apiServer, _checkpointManager));
            } else {
                // Use the VSCode-specific implementation of the string replace tool with diagnostics support
                tools.push(
                    new StrReplaceEditorWithDiagnosticsTool(
                        _checkpointManager,
                        () => {
                            return fromVscodeDiagnosticMap(getDiagnostics());
                        },
                        (filePath: string) => {
                            return _readFile(filePath, _workspaceManager);
                        }
                    ) as unknown as ITool<LocalToolType>
                );
            }

            tools.push(new OpenBrowserTool());
            tools.push(new DiagnosticsTool(_workspaceManager));
            tools.push(new TerminalReadOutputTool());

            if (_gitCommitIndexer !== undefined) {
                tools.push(new GitCommitRetrievalTool(_apiServer, _gitCommitIndexer));
            }

            if (_swarmModeEnabled) {
                tools.push(new StartWorkerAgentTool(_apiServer, _globalState, _configListener));
                tools.push(new ReadWorkerStateTool(_apiServer));
                tools.push(new WaitForWorkerAgentTool(_apiServer));
                tools.push(new SendInstructionToWorkerAgentTool(_apiServer, _configListener));
                tools.push(new StopWorkerAgentTool(_apiServer));
                tools.push(new DeleteWorkerAgentTool(_apiServer));
                tools.push(new ReadWorkerAgentEditsTool(_apiServer));
                tools.push(new ApplyWorkerAgentEditsTool(_apiServer));
            }

            terminalProcessTools = new TerminalProcessTools(
                _extensionRoot,
                _globalState,
                _enableStart,
                _featureFlagManager.currentFlags?.enableUntruncatedContentStorage ?? false,
                _featureFlagManager.currentFlags?.maxLinesTerminalProcessOutput ?? 0,
                _featureFlagManager.currentFlags?.truncationFooterAdditionText ?? "",
                _featureFlagManager.currentFlags?.vscodeTerminalStrategy ?? "vscode_events",
                _assetManager
            );
            tools.push(new TerminalLaunchProcessTool(_workspaceManager, terminalProcessTools));
            tools.push(new TerminalKillProcessTool(terminalProcessTools));
            tools.push(new TerminalReadProcessTool(terminalProcessTools, _workspaceManager));
            tools.push(new TerminalWriteProcessTool(terminalProcessTools));
            tools.push(new TerminalListProcessesTool(terminalProcessTools));
        } else {
            tools.push(new ReadFileTool(_workspaceManager));
        }

        super(tools, ToolHostName.localToolHost);
        this._terminalProcessTools = terminalProcessTools;
    }

    /** Stops all running processes and disposes of the terminal listener. */
    public async close(_cancelledByUser: boolean = false): Promise<void> {
        await super.close();
        if (this._terminalProcessTools !== undefined) {
            this._terminalProcessTools.cleanup();
        }
    }

    /** Stops all running processes but keeps the terminal listener. */
    public closeAllToolProcesses(): Promise<void> {
        if (this._terminalProcessTools !== undefined) {
            this._terminalProcessTools.closeAllProcesses();
        }
        return Promise.resolve();
    }

    factory(_preconditionWait: Promise<void>): IToolHost {
        return new LocalToolHost(
            this._chatMode,
            this._workspaceManager,
            this._apiServer,
            this._checkpointManager,
            this._featureFlagManager,
            this._extensionRoot,
            this._globalState,
            this._enableStart,
            this._gitCommitIndexer,
            this._configListener,
            this._swarmModeEnabled,
            this._assetManager
        );
    }
}

/**
 * A tool that reads a file.
 */
class ReadFileTool extends ToolBase<LocalToolType> {
    constructor(private readonly _workspaceManager: WorkspaceManager) {
        super(LocalToolType.readFile, ToolSafety.Safe);
    }

    public readonly description: string = "Read a file.";

    public readonly inputSchemaJson: string = JSON.stringify({
        /* eslint-disable @typescript-eslint/naming-convention */
        type: "object",
        properties: {
            file_path: {
                type: "string",
                description: "The path of the file to read.",
            },
        },
        required: ["file_path"],
        /* eslint-enable @typescript-eslint/naming-convention */
    });

    public checkToolCallSafe(_toolInput: Record<string, any>): boolean {
        return true;
    }

    public async call(
        toolInput: Record<string, any>,
        _chatHistory: Exchange[],
        _abortSignal: AbortSignal
    ): Promise<ToolUseResponse> {
        const relPath = toolInput.file_path as string;
        try {
            const contents = await _readFile(relPath, this._workspaceManager);
            if (contents === undefined) {
                return errorToolResponse(`Cannot read file: ${relPath}`);
            }
            return successToolResponse(contents);
        } catch (e: any) {
            // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
            return errorToolResponse(`Failed to read file: ${relPath}: ${e.message ?? ""}`);
        }
    }
}

/**
 * A tool that edits a file.
 */
class EditFileTool extends ToolBase<LocalToolType> {
    private readonly _maxDiagnosticDelayMs = 5000;

    constructor(
        private readonly _workspaceManager: WorkspaceManager,
        private readonly _apiServer: APIServer,
        private readonly _checkpointManager: AggregateCheckpointManager
    ) {
        super(LocalToolType.editFile, ToolSafety.Safe);
    }

    public readonly description: string = `
Edit a file. Accepts a file path and a description of the edit.
This tool can edit whole files.
The description should be detailed and precise, and include all required information to perform the edit.
It can include both natural language and code. It can include multiple code snippets to described different
edits in the file. It can include descriptions of how to perform these edits precisely.

All the contents that should go in a file should be placed in a markdown code block, like this:

<begin-example>
Add a function called foo.

\`\`\`
def foo():
    ...
\`\`\`
</end-example>

This includes all contents, even if it's not code.

Be precise or I will take away your toys.

Prefer to use this tool when editing parts of a file.
`;

    public readonly inputSchemaJson: string = JSON.stringify({
        /* eslint-disable @typescript-eslint/naming-convention */
        type: "object",
        properties: {
            file_path: {
                type: "string",
                description: "The path of the file to edit.",
            },
            edit_summary: {
                type: "string",
                description: "A brief description of the edit to be made. 1-2 sentences.",
            },
            detailed_edit_description: {
                type: "string",
                description:
                    "A detailed and precise description of the edit. Can include natural language and code snippets.",
            },
        },
        required: ["file_path", "edit_summary", "detailed_edit_description"],
        /* eslint-enable @typescript-eslint/naming-convention */
    });

    public checkToolCallSafe(_toolInput: Record<string, any>): boolean {
        return true;
    }

    public async call(
        toolInput: Record<string, any>,
        chatHistory: Exchange[],
        abortSignal: AbortSignal,
        _toolUseId: string
    ): Promise<ToolUseResponse> {
        const filePath = toolInput.file_path as string;
        let requestId = undefined;
        try {
            const editSummary = toolInput.edit_summary as string;
            const detailedEditDescription = toolInput.detailed_edit_description as string;
            const fileContents = await _readFile(filePath, this._workspaceManager);
            if (fileContents === undefined) {
                return errorToolResponse(`Cannot read file: ${filePath}`);
            }
            const pathName = _getQualifiedPath(filePath, this._workspaceManager);
            if (pathName === undefined) {
                return errorToolResponse(`Cannot resolve path: ${filePath}`);
            }

            const startingDiagnostics = this._getDiagnostics();

            requestId = this._apiServer.createRequestId();
            const editResult = await this._apiServer.agentEditFile(
                requestId,
                filePath,
                editSummary,
                detailedEditDescription,
                fileContents,
                abortSignal
            );
            if (editResult.isError) {
                return errorToolResponse(`Failed to edit file: ${filePath}`, requestId);
            }

            const document = new DiffViewDocument(
                pathName,
                fileContents,
                editResult.modifiedFileContents,
                {}
            );

            // Do not create a checkpoint if the file contents are the same
            if (fileContents === editResult.modifiedFileContents) {
                document.dispose();
                return successToolResponse(`No changes made to file {${filePath}}`, requestId);
            }

            // Get the exchange ID that resulted in this tool call.
            const toolUseExchangeRequestId = chatHistory.at(-1)?.request_id ?? requestId;
            // Create a new checkpoint and update it to persist to disk.
            const conversationId = this._checkpointManager.currentConversationId ?? "";
            await this._checkpointManager.addCheckpoint(
                {
                    conversationId,
                    path: pathName,
                },
                {
                    sourceToolCallRequestId: toolUseExchangeRequestId,
                    timestamp: Date.now(),
                    document,
                    conversationId,
                }
            );

            // Show the model diagnostics since they are often errors.
            // First, let's wait for them to be computed, polling every second.
            const endingDiagnostics = await this._waitForNewDiagnostics(startingDiagnostics);
            const newDiagnostics = this._filterDiagnosticsMap(
                endingDiagnostics,
                startingDiagnostics
            );
            const newDiagnosticsString = Array.from(newDiagnostics.entries())
                .map(([path, diagnostics]) => {
                    return `${path}\n${diagnostics.map((d) => `${d.range.start.line}-${d.range.end.line}: ${d.message}`).join("\n")}`;
                })
                .join("\n\n");
            // Compute additional diagnostics for the current file, which may have been caused by a previous edit.
            const curFileDiagnostics = endingDiagnostics.get(pathName.absPath) ?? [];
            const additionalDiagnosticsForCurrentFile = this._filterDiagnostics(
                curFileDiagnostics,
                newDiagnostics.get(pathName.absPath) ?? []
            );
            const curFileDiagnosticsString = additionalDiagnosticsForCurrentFile
                .map((d) => `${d.range.start.line}-${d.range.end.line}: ${d.message}`)
                .join("\n");

            return successToolResponse(
                `File edited successfully.  Saved file {${filePath}}.  New diagnostics:\n${newDiagnosticsString}\n\nAdditional ${filePath} diagnostics:\n${curFileDiagnosticsString}`,
                requestId
            );
        } catch (e: any) {
            return errorToolResponse(
                // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
                `Failed to edit file: ${filePath}: ${e.message ?? ""}`,
                requestId
            );
        }
    }

    private _getDiagnostics(): Map<string, vscode.Diagnostic[]> {
        const result = new Map<string, vscode.Diagnostic[]>();
        vscode.languages.getDiagnostics().forEach(([uri, diagnostics]) => {
            if (diagnostics.length > 0) {
                result.set(uri.fsPath, diagnostics);
            }
        });
        return result;
    }

    private _filterDiagnosticsMap(
        diagnostics: Map<string, vscode.Diagnostic[]>,
        toRemove: Map<string, vscode.Diagnostic[]>
    ) {
        const result = new Map<string, vscode.Diagnostic[]>();
        diagnostics.forEach((diagnostics, path) => {
            const toRemoveForPath = toRemove.get(path) ?? [];
            const filteredDiagnostics = this._filterDiagnostics(diagnostics, toRemoveForPath);
            if (filteredDiagnostics.length > 0) {
                result.set(path, filteredDiagnostics);
            }
        });
        return result;
    }

    private _filterDiagnostics(diagnostics: vscode.Diagnostic[], toRemove: vscode.Diagnostic[]) {
        return diagnostics.filter((diagnostic) => {
            return !toRemove.some((d) => {
                return (
                    diagnostic.range.start.line === d.range.start.line &&
                    diagnostic.range.end.line === d.range.end.line &&
                    diagnostic.message === d.message &&
                    diagnostic.severity === d.severity
                );
            });
        });
    }

    private async _waitForNewDiagnostics(
        startingDiagnostics: Map<string, vscode.Diagnostic[]>
    ): Promise<Map<string, vscode.Diagnostic[]>> {
        const startTime = Date.now();
        let lastDiagnostics = this._getDiagnostics();

        while (Date.now() - startTime < this._maxDiagnosticDelayMs) {
            const currentDiagnostics = this._getDiagnostics();
            const newDiagnostics = this._filterDiagnosticsMap(
                currentDiagnostics,
                startingDiagnostics
            );

            // If we found new diagnostics that weren't in the last check, return them
            if (
                this._hasDifferentDiagnostics(
                    newDiagnostics,
                    this._filterDiagnosticsMap(lastDiagnostics, startingDiagnostics)
                )
            ) {
                return currentDiagnostics;
            }

            lastDiagnostics = currentDiagnostics;
            await delayMs(1000); // Poll every second
        }

        // Return the last known diagnostics if we timeout
        return lastDiagnostics;
    }

    private _hasDifferentDiagnostics(
        diag1: Map<string, vscode.Diagnostic[]>,
        diag2: Map<string, vscode.Diagnostic[]>
    ): boolean {
        if (diag1.size !== diag2.size) {
            return true;
        }

        for (const [path, diagnostics] of diag1) {
            const otherDiagnostics = diag2.get(path);
            if (!otherDiagnostics || diagnostics.length !== otherDiagnostics.length) {
                return true;
            }

            for (let i = 0; i < diagnostics.length; i++) {
                const d1 = diagnostics[i];
                const d2 = otherDiagnostics[i];
                if (
                    d1.range.start.line !== d2.range.start.line ||
                    d1.range.end.line !== d2.range.end.line ||
                    d1.message !== d2.message ||
                    d1.severity !== d2.severity
                ) {
                    return true;
                }
            }
        }
        return false;
    }
}

/**
 * Tool to open URLs in the default browser.
 */
class OpenBrowserTool extends ToolBase<LocalToolType> {
    constructor() {
        super(LocalToolType.openBrowser, ToolSafety.Safe);
    }

    public readonly description: string = `\
Open a URL in the default browser.

1. The tool takes in a URL and opens it in the default browser.
2. The tool does not return any content. It is intended for the user to visually inspect and interact with the page. You will not have access to it.
3. You should not use \`open-browser\` on a URL that you have called the tool on before in the conversation history, because the page is already open in the user's browser and the user can see it and refresh it themselves. Each time you call \`open-browser\`, it will jump the user to the browser window, which is highly annoying to the user.`;

    public readonly inputSchemaJson: string = JSON.stringify({
        /* eslint-disable @typescript-eslint/naming-convention */
        type: "object",
        properties: {
            url: {
                type: "string",
                description: "The URL to open in the browser.",
            },
        },
        required: ["url"],
        /* eslint-enable @typescript-eslint/naming-convention */
    });

    public checkToolCallSafe(_toolInput: Record<string, any>): boolean {
        return true;
    }

    public async call(
        toolInput: Record<string, any>,
        _chatHistory: Exchange[],
        _abortSignal: AbortSignal,
        _toolUseId: string
    ): Promise<ToolUseResponse> {
        try {
            const url = toolInput.url as string;
            const uri = vscode.Uri.parse(url);
            const success = await vscode.env.openExternal(uri);
            if (!success) {
                return errorToolResponse(
                    `Failed to open ${url} in browser: system denied the request`
                );
            }
            return successToolResponse(`Opened ${url} in browser`);
        } catch (e: any) {
            // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
            return errorToolResponse(`Failed to open URL in browser: ${e.message ?? ""}`);
        }
    }
}

/**
 * A tool that returns diagnostics from the IDE.
 */
class DiagnosticsTool extends ToolBase<LocalToolType> {
    constructor(private readonly _workspaceManager: WorkspaceManager) {
        super(LocalToolType.diagnostics, ToolSafety.Safe);
    }

    public readonly description: string =
        "Get issues (errors, warnings, etc.) from the IDE. You must provide the paths of the files for which you want to get issues.";

    public readonly inputSchemaJson: string = JSON.stringify({
        /* eslint-disable @typescript-eslint/naming-convention */
        type: "object",
        properties: {
            paths: {
                type: "array",
                items: {
                    type: "string",
                },
                description: "Required list of file paths to get issues for from the IDE.",
            },
        },
        required: ["paths"],
        /* eslint-enable @typescript-eslint/naming-convention */
    });

    public checkToolCallSafe(_toolInput: Record<string, any>): boolean {
        return true;
    }

    public async call(
        toolInput: Record<string, any>,
        _chatHistory: Exchange[],
        _abortSignal: AbortSignal
    ): Promise<ToolUseResponse> {
        try {
            const paths = toolInput.paths as string[];
            const allDiagnostics = this._getDiagnostics();

            if (!paths || paths.length === 0) {
                return errorToolResponse("No paths provided.");
            }

            // If paths are provided, filter diagnostics to only those paths
            const filteredDiagnostics = new Map<string, vscode.Diagnostic[]>();
            for (const path of paths) {
                // Try to find diagnostics for this path
                for (const [diagPath, diagnostics] of allDiagnostics.entries()) {
                    if (diagPath.includes(path)) {
                        filteredDiagnostics.set(diagPath, diagnostics);
                    }
                }
            }

            // Format the diagnostics
            const diagnosticsString = await formatDiagnostics(
                fromVscodeDiagnosticMap(filteredDiagnostics),
                (filePath: string) => _readFile(filePath, this._workspaceManager)
            );

            if (diagnosticsString.trim() === "") {
                return successToolResponse("No diagnostics found.");
            }

            return successToolResponse(
                `The IDE reports the following issues:\n${diagnosticsString}`
            );
        } catch (e: any) {
            // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
            return errorToolResponse(`Failed to get diagnostics: ${e.message ?? ""}`);
        }
    }

    /**
     * Get all current diagnostics from VSCode
     */
    private _getDiagnostics(): Map<string, vscode.Diagnostic[]> {
        const result = new Map<string, vscode.Diagnostic[]>();
        vscode.languages.getDiagnostics().forEach(([uri, diagnostics]) => {
            if (diagnostics.length > 0) {
                result.set(uri.fsPath, diagnostics);
            }
        });
        return result;
    }
}

/**
 * A tool that reads output from the most recently used VSCode terminal.
 */
/**
 * A tool that retrieves information from the codebase using git commit history.
 * This tool uses the GitCommitIndexer to get the checkpoint ID for git commits
 * and uses that for codebase retrieval.
 */
export class GitCommitRetrievalTool extends ToolBase<LocalToolType> {
    private readonly _logger = getLogger("GitCommitRetrievalTool");
    // Limiting to 80k characters, but this is actually controlled in the BE.
    private readonly _maxRetrievalSize: number = 80_000;

    constructor(
        private readonly _apiServer: APIServer,
        private readonly _gitCommitIndexer: GitCommitIndexer
    ) {
        super(LocalToolType.gitCommitRetrieval, ToolSafety.Safe);
    }

    public readonly description: string = `\
This tool is Augment's context engine with git commit history awareness. It:
1. Takes in a natural language description of the code you are looking for;
2. Uses the git commit history as the only context for retrieval;
3. Otherwise functions like the standard codebase-retrieval tool.`;

    public readonly inputSchemaJson: string = JSON.stringify({
        /* eslint-disable @typescript-eslint/naming-convention */
        type: "object",
        properties: {
            information_request: {
                type: "string",
                description: "A description of the information you need.",
            },
        },
        required: ["information_request"],
        /* eslint-enable @typescript-eslint/naming-convention */
    });

    public checkToolCallSafe(_toolInput: Record<string, any>): boolean {
        return true;
    }

    public async call(
        toolInput: Record<string, any>,
        chatHistory: Exchange[],
        abortSignal: AbortSignal
    ): Promise<ToolUseResponse> {
        const requestId = this._apiServer.createRequestId();
        try {
            const informationRequest = toolInput.information_request as string;

            // Get the checkpoint ID from the GitCommitIndexer
            const checkpointId = this._gitCommitIndexer.getCheckpointId();

            if (!checkpointId) {
                this._logger.debug(
                    "No git commit checkpoint ID available, using standard retrieval"
                );
            } else {
                this._logger.debug(`Using git commit checkpoint ID: ${checkpointId}`);
            }

            // Create a blobs object with the checkpoint ID
            const blobs = {
                checkpointId: checkpointId,
                addedBlobs: [],
                deletedBlobs: [],
            };

            // Call the API server with the blobs
            const result = await this._apiServer.agentCodebaseRetrieval(
                requestId,
                informationRequest,
                blobs,
                chatHistory,
                this._maxRetrievalSize,
                {
                    disableCodebaseRetrieval: true,
                    enableCommitRetrieval: true,
                },
                abortSignal
            );

            return successToolResponse(result.formattedRetrieval, requestId);
        } catch (e: any) {
            return errorToolResponse(
                // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
                `Failed to retrieve codebase information: ${e.message ?? ""}`,
                requestId
            );
        }
    }
}

export class TerminalReadOutputTool extends ToolBase<LocalToolType> {
    constructor() {
        super(LocalToolType.readTerminal, ToolSafety.Safe);
    }

    public readonly description: string = `\
Read output from the active or most-recently used VSCode terminal.

By default, it reads all of the text visible in the terminal, not just the output of the most recent command.

If you want to read only the selected text in the terminal, set \`only_selected=true\` in the tool input.
Only do this if you know the user has selected text that you want to read.

Note that this is unrelated to the ${LocalToolType.listProcesses} and ${LocalToolType.readProcess} tools, which interact with processes that were launched with the "launch-process" tool.`;

    public readonly inputSchemaJson: string = JSON.stringify({
        /* eslint-disable @typescript-eslint/naming-convention */
        type: "object",
        properties: {
            only_selected: {
                type: "boolean",
                description: "Whether to read only the selected text in the terminal.",
            },
        },
        required: [],
        /* eslint-enable @typescript-eslint/naming-convention */
    });

    public checkToolCallSafe(_toolInput: Record<string, any>): boolean {
        return true;
    }

    public async call(
        toolInput: Record<string, any>,
        _chatHistory: Exchange[],
        _abortSignal: AbortSignal
    ): Promise<ToolUseResponse> {
        try {
            const terminal = vscode.window.activeTerminal;
            if (!terminal) {
                return errorToolResponse("There are no open VSCode terminals.");
            }
            const onlySelected = toolInput.only_selected as boolean;
            const output = await this._getTerminalOutput(terminal, onlySelected);
            return successToolResponse(`\
Here is the output${onlySelected ? " (selected text only)" : ""} from the active or most-recently used terminal:

${output}`);
        } catch (e: any) {
            // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
            return errorToolResponse(`Failed to read terminal output: ${e.message ?? ""}`);
        }
    }

    /**
     * Gets the output from a terminal using clipboard commands
     */
    private async _getTerminalOutput(
        terminal: vscode.Terminal,
        onlySelected: boolean
    ): Promise<string> {
        const oldClipboard = await vscode.env.clipboard.readText();
        try {
            terminal.show(true);
            await vscode.env.clipboard.writeText("");
            if (!onlySelected) {
                await vscode.commands.executeCommand("workbench.action.terminal.selectAll");
            }
            await vscode.commands.executeCommand("workbench.action.terminal.copySelection");
            const output = await vscode.env.clipboard.readText();
            await vscode.commands.executeCommand("workbench.action.terminal.clearSelection");
            return output;
        } finally {
            await vscode.env.clipboard.writeText(oldClipboard);
        }
    }
}
