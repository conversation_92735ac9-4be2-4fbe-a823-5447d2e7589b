import * as vscode from "vscode";

import { AuthSessionStore } from "../auth/auth-session-store";
import { FeatureFlagManager } from "../feature-flags";
import { getLogger } from "../logging";
import { MainPanelAppController } from "../main-panel/main-panel-app-controller";
import { MainPanelWebview } from "../main-panel/main-panel-webview";
import { DisposableService } from "../utils/disposable-service";

export class MainPanelWebviewProvider
    extends DisposableService
    implements vscode.WebviewViewProvider
{
    private _logger = getLogger("MainPanelWebviewProvider");
    protected _webviewView: vscode.WebviewView | undefined = undefined;
    protected _mainPanelWebview: MainPanelWebview | undefined = undefined;
    private currentApp: MainPanelAppController | undefined = undefined;
    private visibilityEventEmitter: vscode.EventEmitter<boolean> =
        new vscode.EventEmitter<boolean>();

    // Promise that resolves when the webview is ready to load HTML
    private webviewReadyResolve?: () => void;
    /* eslint-disable-next-line @typescript-eslint/naming-convention */
    public static readonly FEATURE_FLAG_TIMEOUT_MS = 1000;

    constructor(
        private readonly _extensionUri: vscode.Uri,
        private authSessionStore?: AuthSessionStore
    ) {
        super();
        this.addDisposable(this.visibilityEventEmitter);
    }

    public setFeatureFlagManager(featureFlagManager: FeatureFlagManager) {
        this._mainPanelWebview?.setFeatureFlagManager(featureFlagManager);
        // If webview is waiting, let it continue with loading HTML
        if (this.webviewReadyResolve) {
            this.webviewReadyResolve();
            this.webviewReadyResolve = undefined;
        }
    }

    public get onVisibilityChange(): vscode.Event<boolean> {
        return this.visibilityEventEmitter.event;
    }

    public isVisible(): boolean {
        return !!this._webviewView?.visible;
    }

    public changeApp(app: MainPanelAppController | undefined) {
        this.currentApp?.dispose();

        if (app) {
            this.addDisposable(app);
        }

        this._setViewTitle(app?.title() || "");
        this.currentApp = app;
        this._mainPanelWebview?.changeApp(app);
    }

    private _setViewTitle(title: string) {
        if (!this._webviewView) {
            return;
        }
        this._webviewView.title = title;
    }

    async resolveWebviewView(
        webviewView: vscode.WebviewView,
        _context: vscode.WebviewViewResolveContext<unknown>,
        _token: vscode.CancellationToken
    ): Promise<void> {
        webviewView.onDidDispose(() => {
            if (this._webviewView === webviewView) {
                this._logger.debug("Disposing of main panel webview view");
                this._webviewView = undefined;
            }
        });

        this._webviewView = webviewView;

        this._setViewTitle(this.currentApp?.title() || "");

        // Dispose any existing panel webview
        this._mainPanelWebview?.dispose();

        // Start with auth if we need to sign in, otherwise, load chat
        this._mainPanelWebview = new MainPanelWebview(
            this._webviewView.webview,
            this.authSessionStore
        );
        this.addDisposable(this._mainPanelWebview);
        this._mainPanelWebview.changeApp(this.currentApp);
        this._mainPanelWebview.addDisposable(
            this._webviewView.onDidChangeVisibility(() => {
                this.visibilityEventEmitter.fire(!!this._webviewView?.visible);
            })
        );

        // Wait for feature flags to be resolved before loading HTML
        // This ensures SentryService is created with the correct backend flags
        await new Promise<void>((resolve) => {
            const timeoutId = setTimeout(() => {
                // If we're still waiting for feature flags, proceed with defaults
                if (this.webviewReadyResolve) {
                    this._logger.warn(
                        `Timeout waiting for feature flags after ${MainPanelWebviewProvider.FEATURE_FLAG_TIMEOUT_MS}ms, proceeding with default flags`
                    );
                    this.webviewReadyResolve = undefined;
                    resolve();
                }
            }, MainPanelWebviewProvider.FEATURE_FLAG_TIMEOUT_MS);

            // Clear timeout when resolved normally
            this.webviewReadyResolve = () => {
                clearTimeout(timeoutId);
                resolve();
            };
        });

        await this._mainPanelWebview.loadHTML(this._extensionUri);
    }
}
