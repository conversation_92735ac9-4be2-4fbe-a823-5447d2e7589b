import { <PERSON>PS<PERSON>r, MCPServerStdio } from "$vscode/src/webview-providers/webview-messages";

import { getErrmsg } from "@augment-internal/sidecar-libs/src/exceptions";
import { isStreamingServer } from "@augment-internal/sidecar-libs/src/tools/mcp-server-utils";
import {
    McpServerConfig,
    ToolDefinitionWithSettings,
} from "@augment-internal/sidecar-libs/src/tools/tool-types";
import { TerminalSettings } from "@augment-internal/sidecar-libs/src/tools/tool-types";
import type { ToolsModel } from "@augment-internal/sidecar-libs/src/tools/tools-model";
import * as vscode from "vscode";

import { getLogger } from "../../logging";
import { AugmentGlobalState } from "../../utils/context";
import { FileStorageKey } from "../../utils/context";
import { CONFIG_VERSION } from "../settings-panel-types";
import { ToolConfigError, type ToolConfigState } from "../settings-panel-types";

// Storage key prefix for MCP access tokens
const MCP_ACCESS_TOKEN_KEY_PREFIX = "augment.mcp-access-token"; // pragma: allowlist secret

/**
 * Manages the storage and retrieval of tool configurations, MCP server configurations, and terminal settings.
 */
export class ToolConfigStore {
    private readonly logger = getLogger("ToolConfigStore");

    constructor(
        private readonly storage: AugmentGlobalState,
        private readonly toolsModel?: ToolsModel,
        private readonly getSettingsMcpServers?: () => McpServerConfig[] | undefined,
        private readonly context?: vscode.ExtensionContext
    ) {}

    /**
     * Gets the tool configuration state from persistent storage.
     * If no configuration exists, returns a default empty state.
     *
     * This method doesn't throw exceptions - if loading fails, it logs the error
     * and returns a default empty state to ensure the application can continue.
     */
    public async get(): Promise<ToolConfigState> {
        try {
            const state = await this.storage.load<ToolConfigState>(
                FileStorageKey.toolsConfiguration
            );
            return (
                state ?? {
                    version: CONFIG_VERSION,
                    tools: [],
                }
            );
        } catch (error) {
            // Log the error but don't throw
            this.logger.error(`Failed to load tool configurations: ${getErrmsg(error)}`);

            // Return a default state so the application can continue
            return {
                version: CONFIG_VERSION,
                tools: [],
            };
        }
    }

    /**
     * Saves the tool configuration state to persistent storage.
     * @param state The tool configuration state to save
     * @throws If saving fails
     */
    public async save(state: ToolConfigState): Promise<void> {
        try {
            await this.storage.save(FileStorageKey.toolsConfiguration, state);
        } catch (error) {
            // Log the error before throwing
            const msg = `Failed to save tool configurations: ${getErrmsg(error)}`;
            this.logger.error(msg);
            throw new ToolConfigError(msg);
        }
    }

    /**
     * Gets the MCP server configurations from persistent storage.
     * If no configurations exist, returns an empty array.
     */
    public async getMCPServers(): Promise<MCPServer[]> {
        try {
            const mcpServerToolMapping = new Map<string, Set<ToolDefinitionWithSettings>>();
            let servers = await this.storage.load<MCPServer[]>(FileStorageKey.mcpServers);
            if (this.toolsModel) {
                const toolDefinitions = await this.toolsModel.getToolStatusForSettingsPanel();
                for (const toolDefinition of toolDefinitions) {
                    const mcpServerName = toolDefinition.definition.original_mcp_server_name;
                    if (!mcpServerName) {
                        continue;
                    }
                    if (!mcpServerToolMapping.has(mcpServerName)) {
                        mcpServerToolMapping.set(mcpServerName, new Set());
                    }
                    mcpServerToolMapping.get(mcpServerName)?.add(toolDefinition);
                }
            }
            servers = (servers ?? []).map((server) => {
                // Backward compatibility: treat servers without a type field as stdio
                const migratedServer: MCPServer = server.type
                    ? server
                    : ({
                          ...(server as any),
                          type: "stdio" as const,
                      } as MCPServer);

                return {
                    ...migratedServer,
                    tools: Array.from(mcpServerToolMapping.get(server.name) ?? []),
                };
            });
            return Array.isArray(servers) ? servers : [];
        } catch (error) {
            // Log the error but don't throw
            this.logger.error(`Failed to load MCP servers: ${getErrmsg(error)}`);
            return [];
        }
    }

    /**
     * Saves the MCP server configurations to persistent storage.
     * @param servers The MCP server configurations to save
     * @throws If saving fails
     */
    public async saveMCPServers(servers: MCPServer[]): Promise<void> {
        try {
            await this.storage.save(FileStorageKey.mcpServers, servers);

            // Update the tools model with the new MCP server configurations
            await this.updateSidecarMCPServers();
        } catch (error) {
            // Log the error before throwing
            const msg = `Failed to save MCP servers: ${getErrmsg(error)}`;
            this.logger.error(msg);
            throw new ToolConfigError(msg);
        }
    }

    /**
     * Update the sidecar with the current MCP server configurations
     *
     * This method combines MCP servers from two sources:
     * 1. MCP servers stored in the extension's global state (added through the UI)
     * 2. MCP servers configured in settings.json (if any)
     */
    public async updateSidecarMCPServers(): Promise<void> {
        try {
            // Get MCP servers from storage (added through the UI)
            const servers = await this.getMCPServers();

            // Convert to the format expected by the sidecar
            const mcpServerConfigs = await Promise.all(
                servers.map(async (server) => {
                    if (isStreamingServer(server)) {
                        // For HTTP/SSE servers, try to retrieve access token from secure storage
                        let accessToken: string | undefined;
                        if (this.context) {
                            const tokenKey = `${MCP_ACCESS_TOKEN_KEY_PREFIX}.${server.name}`;
                            accessToken = await this.context.secrets.get(tokenKey);
                            if (accessToken) {
                                this.logger.verbose(
                                    `Retrieved access token for MCP server: ${server.name}`
                                );
                            } else {
                                this.logger.verbose(
                                    `No access token found for MCP server: ${server.name}`
                                );
                            }
                        }

                        const config = {
                            type: server.type,
                            name: server.name,
                            url: server.url,
                            disabled: server.disabled,
                            accessToken, // Include access token if available
                            authRequired: server.authRequired, // Include authentication requirement flag
                        };

                        this.logger.verbose(
                            `MCP server config for ${server.name}: ${JSON.stringify({
                                ...config,
                                accessToken: accessToken ? "[REDACTED]" : undefined,
                            })}`
                        );

                        return config;
                    } else {
                        // TypeScript doesn't know this is a stdio server, so we need to cast it
                        const stdioServer = server as MCPServerStdio;
                        return {
                            type: "stdio" as const,
                            name: stdioServer.name,
                            command: stdioServer.command,
                            args: [], // Empty args array since we're using shell interpolation
                            useShellInterpolation: true, // Always true for UI-based servers
                            env: stdioServer.env, // Include environment variables if they exist
                            disabled: stdioServer.disabled,
                        };
                    }
                })
            );

            // Get MCP servers from settings.json (if available)
            const settingsMcpServers = this.getSettingsMcpServers
                ? this.getSettingsMcpServers()
                : [];

            // Combine MCP servers from both sources
            const combinedMcpServers: McpServerConfig[] = [...mcpServerConfigs];
            if (settingsMcpServers && settingsMcpServers.length > 0) {
                combinedMcpServers.push(...settingsMcpServers);
            }

            // Update the tools model with the combined MCP server configurations
            if (this.toolsModel) {
                this.toolsModel.setMcpServers(combinedMcpServers);
            }
        } catch (error) {
            this.logger.error(`Failed to update sidecar MCP servers: ${getErrmsg(error)}`);
        }
    }

    /**
     * Gets the terminal settings from persistent storage.
     * If no settings exist, returns a default empty state.
     */
    public async getTerminalSettings(): Promise<TerminalSettings> {
        try {
            const settings = await this.storage.load<TerminalSettings>(
                FileStorageKey.terminalSettings
            );
            return settings ?? { supportedShells: [], selectedShell: undefined };
        } catch (error) {
            // Log the error but don't throw
            this.logger.error(`Failed to load terminal settings: ${getErrmsg(error)}`);
            return { supportedShells: [], selectedShell: undefined };
        }
    }

    /**
     * Saves the terminal settings to persistent storage.
     * @param settings The terminal settings to save
     * @throws If saving fails
     */
    public async saveTerminalSettings(settings: TerminalSettings): Promise<void> {
        try {
            await this.storage.save(FileStorageKey.terminalSettings, settings);
        } catch (error) {
            const msg = `Failed to save terminal settings: ${getErrmsg(error)}`;
            this.logger.error(msg);
            throw new ToolConfigError(msg);
        }
    }

    /**
     * Updates the selected shell in the terminal settings.
     * @param shellName The name of the selected shell
     * @throws If saving fails
     */
    public async updateSelectedShell(shellName: string): Promise<void> {
        const settings = await this.getTerminalSettings();
        settings.selectedShell = shellName;
        await this.saveTerminalSettings(settings);
    }

    /**
     * Updates the startup script in the terminal settings.
     * @param script The startup script content
     * @throws If saving fails
     */
    public async updateStartupScript(script: string): Promise<void> {
        const settings = await this.getTerminalSettings();
        settings.startupScript = script;
        await this.saveTerminalSettings(settings);
    }
}
