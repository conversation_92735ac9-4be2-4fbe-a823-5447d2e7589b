/**
 * State shared between the home panel and chat webview
 */
import { type RemoteAgent } from "@augment-internal/sidecar-libs/src/remote-agent/remote-agent-types";

import { type RemoteAgentPinnedStatusContext } from "./types";

/**
 * State shared between the home panel and chat webview
 */
export interface ChatHomeWebviewState {
    agentOverviews: RemoteAgent[];
    selectedAgentId: string | undefined;
    activeWebviews: ("chat" | "home")[];
    pinnedAgents: RemoteAgentPinnedStatusContext;
}

export const SHARED_AGENT_STORE_NAME = "remoteAgentStore";
export const SHARED_AGENT_STORE_CONTEXT_KEY = "remoteAgentStore";

/**
 * Validates the state for the chat-home shared state
 */
export function validateChatHomeState(state: unknown): state is ChatHomeWebviewState {
    const typedState = state as Partial<ChatHomeWebviewState> | undefined;
    return (
        Array.isArray(typedState?.agentOverviews) &&
        Array.isArray(typedState?.activeWebviews) &&
        (typedState?.pinnedAgents === undefined || typeof typedState.pinnedAgents === "object")
    );
}
