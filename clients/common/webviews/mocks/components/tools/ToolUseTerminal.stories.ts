/* eslint-disable @typescript-eslint/naming-convention */
import type { <PERSON><PERSON>, StoryObj } from "@storybook/svelte-vite";
import ToolUse from "$common-webviews/src/apps/chat/components/conversation/blocks/tools/ToolUse.svelte";
import { SidecarToolType } from "@augment-internal/sidecar-libs/src/tools/sidecar-tools/sidecar-tool-types";
import mockSetupScript from "./mock-setup-script.json";
import mockScriptOutput from "./mock-script-output.json";
import { LocalToolType } from "$vscode/src/webview-providers/tool-types";
import { LONG_JS_FILE_CONTENT } from "./long_file_content";
import {
  AUGMENT_DIRECTORY_ROOT,
  AUGMENT_RULES_FOLDER,
} from "@augment-internal/sidecar-libs/src/utils/rules-parser";
import { ToolUsePhase } from "$common-webviews/src/apps/chat/types/tool-use-state";

// Long terminal command content for testing
const LONG_TERMINAL_COMMAND = `$ #!/bin/bash

# Long Bash Script Demonstration
echo "🚀 Starting Long Bash Script Demonstration..."
echo "================================================"

# Function definitions
log_message() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1"
}

create_test_files() {
    local dir=$1
    log_message "Creating test files in $dir"
    mkdir -p "$dir"
    for i in {1..5}; do
        echo "This is test file $i with some content" > "$dir/test_file_$i.txt"
        echo "Created on: $(date)" >> "$dir/test_file_$i.txt"
        echo "Random number: $RANDOM" >> "$dir/test_file_$i.txt"
    done
}

process_files() {
    local dir=$1
    log_message "Processing files in $dir"

    for file in "$dir"/*.txt; do
        if [[ -f "$file" ]]; then
            local word_count=$(wc -w < "$file")
            local line_count=$(wc -l < "$file")
            log_message "File $(basename "$file"): $line_count lines, $word_count words"
            sleep 0.5
        fi
    done
}

perform_calculations() {
    log_message "Performing mathematical calculations..."

    local sum=0
    for i in {1..50}; do
        sum=$((sum + i))
        if (( i % 10 == 0 )); then
            log_message "Progress: Calculated sum up to $i = $sum"
        fi
        sleep 0.1
    done

    log_message "Final sum of 1-50: $sum"
}`;

const meta = {
  title: "components/ToolUse",
} satisfies Meta<typeof ToolUse>;

export default meta;

type Story = StoryObj<typeof ToolUse>;

export const SaveFile = {
  name: "Save File",
  render(props) {
    return { Component: ToolUse, props } as any;
  },
  args: {
    toolUse: {
      tool_use_id: "js-file-example",
      tool_name: LocalToolType.saveFile,
      input_json: JSON.stringify({
        file_path: "/path/to/example-component.jsx",
        file_content: LONG_JS_FILE_CONTENT,
      }),
    },
    toolUseState: { phase: 5 },
    isLastTurn: true,
    requestId: "1",
  } as any,
} satisfies Story;
export const ReadFileToolComponent = {
  name: "Read File",
  render(props) {
    return { Component: ToolUse, props } as any;
  },
  args: {
    toolUse: {
      tool_use_id: "js-file-example",
      tool_name: LocalToolType.readFile,
      input_json: JSON.stringify({
        file_path: "/path/to/example-component.jsx",
        file_content: LONG_JS_FILE_CONTENT,
      }),
    },
    toolUseState: { phase: 5 },
    isLastTurn: true,
    requestId: "1",
  } as any,
} satisfies Story;
export const StrReplace = {
  name: "Str Replace Editor",
  render(props) {
    return { Component: ToolUse, props } as any;
  },
  args: {
    toolUse: {
      tool_use_id: "js-file-example",
      tool_name: LocalToolType.strReplaceEditor,
      input_json: JSON.stringify({
        command: "str_replace",
        file_path: "/path/to/example-component.jsx",
        file_content: LONG_JS_FILE_CONTENT,
        //these are here for testing, but generally will not be sent from the model.
        added: 13,
        removed: 7,
      }),
    },
    toolUseState: { phase: 5 },
    isLastTurn: true,
    requestId: "1",
  } as any,
} satisfies Story;
export const SaveFileCancelled = {
  name: "Save file Cancelled",
  render(props) {
    return { Component: ToolUse, props } as any;
  },
  args: {
    toolUse: {
      tool_use_id: "js-file-example",
      tool_name: LocalToolType.saveFile,
      input_json: JSON.stringify({
        file_path: "/path/to/example-component.jsx",
        file_content: LONG_JS_FILE_CONTENT,
      }),
    },
    toolUseState: { phase: 8 },
    isLastTurn: true,
    requestId: "1",
  } as any,
} satisfies Story;

export const SearchFile = {
  name: "Search File",
  render(props) {
    return { Component: ToolUse, props } as any;
  },
  args: {
    toolUse: {
      tool_use_id: "js-file-example",
      tool_name: SidecarToolType.view,
      input_json: JSON.stringify({
        path: "/path/to/example-component.jsx",
        type: "file",
        search_query_regex: "function.*Hello",
        case_sensitive: false,
      }),
    },
    toolUseState: { phase: 5 },
  } as any,
} satisfies Story;

export const ViewDirectory = {
  name: "View Directory",
  render(props) {
    return { Component: ToolUse, props } as any;
  },
  args: {
    toolUse: {
      tool_use_id: "directory-example",
      tool_name: SidecarToolType.view,
      input_json: JSON.stringify({
        path: "/path/to/project/src",
        type: "directory",
      }),
    },
    toolUseState: { phase: 5 },
  } as any,
} satisfies Story;

export const SearchInFile = {
  name: "Search in File with Regex",
  render(props) {
    return { Component: ToolUse, props } as any;
  },
  args: {
    toolUse: {
      tool_use_id: "search-regex-example",
      tool_name: SidecarToolType.view,
      input_json: JSON.stringify({
        path: "/path/to/example-component.jsx",
        type: "file",
        search_query_regex: "export.*Component",
        case_sensitive: true,
        context_lines_before: 3,
        context_lines_after: 3,
      }),
    },
    toolUseState: { phase: 5 },
  } as any,
} satisfies Story;

export const ViewFile = {
  name: "View File",
  render(props) {
    return { Component: ToolUse, props } as any;
  },
  args: {
    toolUse: {
      tool_use_id: "js-file-example",
      tool_name: SidecarToolType.view,
      input_json: JSON.stringify({
        path: "/path/to/example-component.jsx",
        type: "file",
      }),
    },
    toolUseState: { phase: 5 },
  } as any,
} satisfies Story;

export const ViewRulesFile = {
  name: "View Rules File (with Preview)",
  render(props) {
    return { Component: ToolUse, props } as any;
  },
  args: {
    toolUse: {
      tool_use_id: "rules-file-example",
      tool_name: SidecarToolType.view,
      input_json: JSON.stringify({
        path: `${AUGMENT_DIRECTORY_ROOT}/${AUGMENT_RULES_FOLDER}/frontend.md`,
        type: "file",
      }),
    },
    toolUseState: {
      phase: 5,
      result: {
        text: `Here's the result of running \`cat -n\` on ${AUGMENT_DIRECTORY_ROOT}/${AUGMENT_RULES_FOLDER}/frontend.md:
     1\t---
     2\ttype: always_apply
     3\tdescription: Frontend development guidelines and build commands
     4\t---
     5\t
     6\t# Frontend Development Rules
     7\t
     8\t## JavaScript/TypeScript
     9\t
    10\t- **npm/pnpm**:
    11\t  - Build: \`pnpm run build\`, \`pnpm run vscode:build-dev\`
    12\t  - Test: \`pnpm run test\`, \`pnpm run vscode:test\`
    13\t- **Bazel**:
    14\t  - Build: \`bazel build //clients/vscode:build_dev_to_workspace\`
    15\t  - Test: \`bazel test //clients/vscode:test\`
    16\t- **Tests**: Located in \`__tests__\` directories with \`.test.ts\` suffix
    17\t- **Frameworks**: Jest (VSCode), Vitest (webviews)
    18\t- **Style**: Avoid using any for types and prefer unknown
    19\t
    20\t## CSS
    21\t
    22\t- Avoid using !important to overwrite styles
    23\t
    24\t## Frontend (HTML)
    25\t
    26\t- **Svelte** (clients/common/webviews):
    27\t  - Build: \`pnpm build:vscode\`
    28\t  - Test: \`pnpm vitest\`
    29\t- **React** (services/*/frontend):
    30\t  - Build: \`pnpm build\`
    31\t  - Test: \`jest\`
    32\t- **Tests**: Located alongside components with \`.test.ts\` or \`.test.tsx\` suffix
    33\t
    34\t## Icons
    35\t
    36\t- Prefer importing icons with the following syntax: \`$common-webviews/src/design-system/icons/fontawesome/svgs/regular/chevron-left.svg?component\`
    37\t
    38\t## Testing
    39\t
    40\t- Use \`cd clients/vscode && pnpm vscode:jest\` to run tests for the VSCode extension
    41\t- Use \`cd clients/common/webviews && pnpm run vitest\` (for webviews)
Total lines in file: 41
`,
      },
    },
  } as any,
} satisfies Story;

export const ToolUseCli = {
  name: "Shell CLI",
  render(props) {
    return { Component: ToolUse, props } as any;
  },
  args: {
    toolUse: {
      tool_use_id: "2",
      tool_name: SidecarToolType.shell,
      input_json: '{"command": "ls"}',
    },
    toolUseState: { phase: 3 },
    isLastTurn: true,
    requestId: "1",
  } as any,
} satisfies Story;

export const ToolUseCli1 = {
  name: "Shell CLI - Loading - Phase 1",
  render(props) {
    return { Component: ToolUse, props } as any;
  },
  args: {
    toolUse: {
      tool_use_id: "2",
      tool_name: SidecarToolType.shell,
      input_json: '{"command": "ls"}',
    },
    toolUseState: { phase: 1 },
    isLastTurn: true,
    requestId: "1",
  } as any,
} satisfies Story;

export const ToolUseCli4 = {
  name: "Shell CLI - Running - Phase 4",
  render(props) {
    return { Component: ToolUse, props } as any;
  },
  args: {
    toolUse: {
      tool_use_id: "2",
      tool_name: SidecarToolType.shell,
      input_json: '{"command": "ls"}',
    },
    toolUseState: { phase: 4 },
    isLastTurn: true,
    requestId: "1",
  } as any,
} satisfies Story;

export const ToolUseCli5 = {
  name: "Shell CLI - Success - Phase 5",
  render(props) {
    return { Component: ToolUse, props } as any;
  },
  args: {
    toolUse: {
      tool_use_id: "2",
      tool_name: SidecarToolType.shell,
      input_json: '{"command": "ls"}',
    },
    toolUseState: { phase: 5 },
    isLastTurn: true,
    requestId: "1",
  } as any,
} satisfies Story;

export const ToolUseCli6 = {
  name: "Shell CLI - Error - Phase 6",
  render(props) {
    return { Component: ToolUse, props } as any;
  },
  args: {
    toolUse: {
      tool_use_id: "2",
      tool_name: SidecarToolType.shell,
      input_json: '{"command": "ls"}',
    },
    toolUseState: { phase: 6 },
    isLastTurn: true,
    requestId: "1",
  } as any,
} satisfies Story;

export const ToolUseCliLongCommand = {
  name: "Shell CLI - Long Command",
  render(props) {
    return { Component: ToolUse, props } as any;
  },
  args: {
    toolUse: {
      tool_use_id: "long-command",
      tool_name: SidecarToolType.shell,
      input_json: JSON.stringify({
        command_type: "complex",
        complex_command: LONG_TERMINAL_COMMAND,
      }),
    },
    toolUseState: { phase: 5 },
    isLastTurn: true,
    requestId: "1",
  } as any,
} satisfies Story;

export const ToolUseCliLongArgs = {
  name: "Shell CLI - Long Args",
  render(props) {
    return { Component: ToolUse, props } as any;
  },
  args: {
    toolUse: {
      tool_use_id: "long-args-command",
      tool_name: SidecarToolType.shell,
      input_json: JSON.stringify({
        command_type: "simple",
        simple_command: [
          "docker",
          "run",
          "--rm",
          "-it",
          "--volume",
          "/Users/<USER>/projects/my-awesome-project:/workspace",
          "--volume",
          "/Users/<USER>/.ssh:/root/.ssh:ro",
          "--volume",
          "/Users/<USER>/.gitconfig:/root/.gitconfig:ro",
          "--workdir",
          "/workspace",
          "--env",
          "NODE_ENV=development",
          "--env",
          "REDIS_URL=redis://localhost:6379/0",
          "--publish",
          "3000:3000",
          "--publish",
          "5432:5432",
          "--name",
          "my-development-container",
          "node:18-alpine",
          "sh",
          "-c",
          "npm install && npm run dev -- --host 0.0.0.0 --port 3000",
        ],
      }),
    },
    toolUseState: { phase: 5 },
    isLastTurn: true,
    requestId: "1",
  } as any,
} satisfies Story;

export const ToolUseCliLongOutput = {
  name: "Shell CLI - Long Command & Output",
  render(props) {
    return { Component: ToolUse, props } as any;
  },
  args: {
    toolUse: {
      tool_use_id: "long-output-command",
      tool_name: SidecarToolType.shell,
      input_json: JSON.stringify({
        command_type: "complex",
        complex_command: LONG_TERMINAL_COMMAND,
      }),
    },
    toolUseState: {
      phase: 5,
      result: {
        text: `Found 47 library files:
/usr/local/lib/libssl.3.dylib
/usr/local/lib/libcrypto.3.dylib
/usr/local/lib/libz.1.dylib
/usr/local/lib/libpng16.16.dylib
/usr/local/lib/libjpeg.8.dylib
/usr/local/lib/libfreetype.6.dylib
/usr/local/lib/libfontconfig.1.dylib
/usr/local/lib/libexpat.1.dylib
/usr/local/lib/libbrotlidec.1.dylib
/usr/local/lib/libbrotlienc.1.dylib
/usr/local/lib/libbrotlicommon.1.dylib
/usr/local/lib/libpcre2-8.0.dylib
/usr/local/lib/libgit2.1.7.dylib
/usr/local/lib/libssh2.1.dylib
/usr/local/lib/libcurl.4.dylib
/usr/local/lib/libnghttp2.14.dylib
/usr/local/lib/librtmp.1.dylib
/usr/local/lib/libidn2.0.dylib
/usr/local/lib/libunistring.5.dylib
/usr/local/lib/libpsl.5.dylib
/usr/local/lib/libzstd.1.dylib
/usr/local/lib/liblzma.5.dylib
/usr/local/lib/libbz2.1.0.dylib
/usr/local/lib/libreadline.8.dylib
/usr/local/lib/libhistory.8.dylib
/usr/local/lib/libncurses.6.dylib
/usr/local/lib/libform.6.dylib
/usr/local/lib/libmenu.6.dylib
/usr/local/lib/libpanel.6.dylib
/usr/local/lib/libsqlite3.0.dylib
/usr/local/lib/libxml2.2.dylib
/usr/local/lib/libxslt.1.dylib
/usr/local/lib/libexslt.0.dylib
/usr/local/lib/libyaml-0.2.dylib
/usr/local/lib/libffi.8.dylib
/usr/local/lib/libgmp.10.dylib
/usr/local/lib/libmpfr.6.dylib
/usr/local/lib/libmpc.3.dylib
/usr/local/lib/libisl.23.dylib
/usr/local/lib/libcloog-isl.4.dylib
/usr/local/lib/libgomp.1.dylib
/usr/local/lib/libgcc_s.1.dylib
/usr/local/lib/libstdc++.6.dylib
/usr/local/lib/libquadmath.0.dylib
/usr/local/lib/libgfortran.5.dylib
/usr/local/lib/libatomic.1.dylib
/usr/local/lib/libomp.dylib

Total size: 847.2 MB
Average file size: 18.0 MB
Largest file: libstdc++.6.dylib (156.3 MB)
Smallest file: libatomic.1.dylib (0.2 MB)

Library dependency analysis complete.
Scan completed in 2.34 seconds.`,
        isError: false,
      },
    },
    isLastTurn: true,
    requestId: "1",
  } as any,
} satisfies Story;

export const ToolUseSetupScript = {
  name: "Setup Script",
  render(props) {
    return { Component: ToolUse, props } as any;
  },
  args: {
    toolUse: mockSetupScript,
    toolUseState: {
      phase: 5,
      requestId: "1",
      toolUseId: mockSetupScript.tool_use_id,
      result: {
        text: JSON.stringify(mockScriptOutput),
        isError: false,
      },
    },
    isLastTurn: true,
    requestId: "1",
  } as any,
} satisfies Story;

export const GrepSearchRunning = {
  name: "Grep Search - Running",
  render(props) {
    return { Component: ToolUse, props } as any;
  },
  args: {
    toolUse: {
      tool_use_id: "grep-search-running",
      tool_name: SidecarToolType.grepSearch,
      input_json: JSON.stringify({
        directory_absolute_path: "/Users/<USER>/project",
        query: "function.*useState",
        files_include_glob_pattern: "**/*.{ts,tsx,js,jsx}",
        case_sensitive: false,
        context_lines_before: 2,
        context_lines_after: 2,
      }),
    },
    toolUseState: { phase: "running" },
    isLastTurn: true,
    requestId: "1",
  } as any,
} satisfies Story;

export const GrepSearchCompleted = {
  name: "Grep Search - Completed",
  render(props) {
    return { Component: ToolUse, props } as any;
  },
  args: {
    toolUse: {
      tool_use_id: "grep-search-completed",
      tool_name: SidecarToolType.grepSearch,
      input_json: JSON.stringify({
        directory_absolute_path: "/Users/<USER>/project",
        query: "function.*useState",
        files_include_glob_pattern: "**/*.{ts,tsx,js,jsx}",
        case_sensitive: false,
        context_lines_before: 2,
        context_lines_after: 2,
      }),
    },
    toolUseState: {
      phase: "completed",
      result: {
        text: `src/components/UserProfile.tsx:
12-  import React from 'react';
13-  import { User } from '../types';
14:  function UserProfile({ user }: { user: User }) {
15:    const [isEditing, setIsEditing] = useState(false);
16:    const [formData, setFormData] = useState(user);
17-
18-    return (

src/hooks/useCounter.ts:
8-   import { useCallback } from 'react';
9-
10:  function useCounter(initialValue: number = 0) {
11:    const [count, setCount] = useState(initialValue);
12:    const [history, setHistory] = useState<number[]>([initialValue]);
13-
14-    const increment = useCallback(() => {

src/pages/Dashboard.tsx:
23-  import { fetchUserData } from '../api';
24-  import { LoadingSpinner } from '../components';
25:  function Dashboard() {
26:    const [userData, setUserData] = useState(null);
27:    const [loading, setLoading] = useState(true);
28-    const [error, setError] = useState(null);
29-
30-    useEffect(() => {`,
        isError: false,
      },
    },
    isLastTurn: true,
    requestId: "1",
  } as any,
} satisfies Story;

export const GrepSearchNoResults = {
  name: "Grep Search - No Results",
  render(props) {
    return { Component: ToolUse, props } as any;
  },
  args: {
    toolUse: {
      tool_use_id: "grep-search-no-results",
      tool_name: SidecarToolType.grepSearch,
      input_json: JSON.stringify({
        directory_absolute_path: "/Users/<USER>/project",
        query: "nonexistentFunction",
        files_include_glob_pattern: "**/*.{ts,tsx,js,jsx}",
        case_sensitive: true,
      }),
    },
    toolUseState: {
      phase: "completed",
      result: {
        text: "",
        isError: false,
      },
    },
    isLastTurn: true,
    requestId: "1",
  } as any,
} satisfies Story;

export const ShellCommandLargeError = {
  name: "Shell Command - Large Error Block",
  render(props) {
    return { Component: ToolUse, props } as any;
  },
  args: {
    toolUse: {
      tool_use_id: "shell-large-error",
      tool_name: SidecarToolType.shell,
      input_json: JSON.stringify({
        command: "npm run build:production",
        cwd: "/Users/<USER>/complex-project",
      }),
    },
    toolUseState: {
      phase: ToolUsePhase.error,
      requestId: "shell-error-req",
      toolUseId: "shell-large-error",
      result: {
        text: `Build failed with critical errors:

ERROR in ./src/components/UserDashboard.tsx 45:23-67
Module not found: Error: Can't resolve '@/utils/dateHelpers' in '/Users/<USER>/complex-project/src/components'
Did you mean './utils/dateHelpers'?
BREAKING CHANGE: webpack < 5 used to include polyfills for node.js core modules by default.

ERROR in ./src/hooks/useAuthentication.ts 12:34-89
Module not found: Error: Can't resolve 'crypto' in '/Users/<USER>/complex-project/src/hooks'

BREAKING CHANGE: webpack < 5 used to include polyfills for node.js core modules by default.
This is no longer the case. Verify if you need this module and configure a polyfill for it.

If you want to include a polyfill, you need to:
	- add a fallback 'resolve.fallback: { "crypto": require.resolve("crypto-browserify") }'
	- install 'crypto-browserify'
If you don't want to include a polyfill, you can use an empty module like this:
	resolve.fallback: { "crypto": false }

ERROR in ./src/services/apiClient.ts 234:12-45
TypeError: Cannot read properties of undefined (reading 'baseURL')
    at Object.create (/Users/<USER>/complex-project/node_modules/axios/lib/axios.js:25:18)
    at createInstance (/Users/<USER>/complex-project/src/services/apiClient.ts:234:12)
    at Object.<anonymous> (/Users/<USER>/complex-project/src/services/apiClient.ts:245:23)
    at Module._compile (internal/modules/cjs/loader.js:1063:30)
    at Object.Module._extensions..js (internal/modules/cjs/loader.js:1092:10)

ERROR in ./src/components/DataTable.tsx 156:78-123
Type error: Property 'sortBy' does not exist on type 'TableProps<T>'.

  154 |   const handleSort = useCallback((column: string) => {
  155 |     if (props.sortable) {
> 156 |       props.onSort?.(column, props.sortBy === column ? 'desc' : 'asc');
      |                                    ^^^^^^
  157 |     }
  158 |   }, [props.sortable, props.sortBy, props.onSort]);

ERROR in ./src/utils/validation.ts 89:34-67
ReferenceError: regeneratorRuntime is not defined
    at validateAsync (/Users/<USER>/complex-project/src/utils/validation.ts:89:34)
    at processValidation (/Users/<USER>/complex-project/src/utils/validation.ts:123:12)
    at Object.<anonymous> (/Users/<USER>/complex-project/src/utils/validation.ts:145:23)

This error is likely caused by missing Babel configuration for async/await support.
Add @babel/plugin-transform-runtime to your Babel configuration.

MEMORY ERROR: JavaScript heap out of memory
FATAL ERROR: Ineffective mark-compacts near heap limit Allocation failed - JavaScript heap out of memory
 1: 0x10003c000 node::Abort() [/usr/local/bin/node]
 2: 0x10003c000 node::OnFatalError(char const*, char const*) [/usr/local/bin/node]
 3: 0x1001b4000 v8::Utils::ReportOOMFailure(v8::internal::Isolate*, char const*, bool) [/usr/local/bin/node]
 4: 0x1001b4000 v8::internal::V8::FatalProcessOutOfMemory(v8::internal::Isolate*, char const*, bool) [/usr/local/bin/node]

Build Summary:
- Total files processed: 1,247
- Failed files: 23
- Warnings: 156
- Errors: 5 critical, 18 non-critical
- Build time: 4m 32s
- Memory peak: 4.2 GB
- Node.js version: v18.17.0
- Webpack version: 5.88.2
- TypeScript version: 5.1.6

Suggested fixes:
1. Update import paths to use relative imports instead of absolute
2. Configure webpack polyfills for Node.js core modules
3. Fix TypeScript interface definitions
4. Add Babel runtime transformation plugin
5. Increase Node.js memory limit: node --max-old-space-size=8192

For detailed logs, check: /Users/<USER>/complex-project/build-logs/error-2023-10-15-14-30-45.log

Build process terminated with exit code 1.`,
        isError: true,
      },
    },
    isLastTurn: true,
    requestId: "shell-error-req",
  } as any,
} satisfies Story;
