/* eslint-disable @typescript-eslint/naming-convention */
import { buildCodeActions } from "$common-webviews/src/common/components/code-roll/code-roll-util";
import { type IEditSuggestion } from "$vscode/src/next-edit/next-edit-types";

import type { Meta, StoryObj } from "@storybook/svelte-vite";

import CodeRollSuggestionWindowMock from "./CodeRollSuggestionWindowMock.svelte";
import suggestion from "./samples/suggestion-0";
import originalCode from "./samples/suggestion-0.txt?raw";

const selectedSuggestion = suggestion as unknown as IEditSuggestion;
const meta = {
  title: "components/code-roll/CodeRollSuggestionWindow",
  component: CodeRollSuggestionWindowMock,
  tags: [],
} satisfies Meta<CodeRollSuggestionWindowMock>;

const STALE_ACTIONS = buildCodeActions("active", "|", "reject", "undo");

export default meta;
type Story = StoryObj<typeof meta>;

export const Unselected: Story = {
  name: "Unselected",
  args: {
    selectedSuggestion,
    originalCode,
  } as any,
};

export const SelectedFocused: Story = {
  name: "Selected / Focused",
  args: {
    selectedSuggestion,
    withinSuggestion: selectedSuggestion,
    originalCode,
  } as any,
};

export const AppliedUnfocused: Story = {
  name: "Applied / Unfocused",
  args: {
    selectedSuggestion: {
      ...selectedSuggestion,
      state: "stale" as any,
    },
    codeActions: STALE_ACTIONS,
    originalCode,
  } as any,
};

export const AppliedSelectedFocused: Story = {
  name: "Applied Selected / Focused",
  args: {
    selectedSuggestion: {
      ...selectedSuggestion,
      state: "stale" as any,
    },
    withinSuggestion: selectedSuggestion,
    originalCode,
    codeActions: STALE_ACTIONS,
  } as any,
};
