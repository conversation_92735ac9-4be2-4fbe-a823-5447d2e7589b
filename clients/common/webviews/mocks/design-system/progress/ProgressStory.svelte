<script lang="ts">
  import ProgressAugment from "$common-webviews/src/design-system/components/ProgressAugment/ProgressAugment.svelte";
  import ColumnLayout from "$common-webviews/mocks/storybook/ColumnLayout.svelte";
  import Fieldset from "$common-webviews/mocks/storybook/Fieldset.svelte";
  import {
    type ProgressColor,
    type ProgressSize,
    type ProgressVariant,
  } from "$common-webviews/src/design-system/components/ProgressAugment/progress-util";
  import ButtonAugment from "$common-webviews/src/design-system/components/ButtonAugment.svelte";

  const sizeVariants: ProgressSize[] = [1, 2, 3] as const;
  const colorVariants: ProgressColor[] = ["accent", "neutral", "error", "success"] as const;
  const variantVariants: ProgressVariant[] = ["soft", "surface", "outline"] as const;
  const progressValues = [25, 50, 75, 100] as const;
  let interval: ReturnType<typeof setInterval> | undefined;
  let value = 0;
  function onReset() {
    value = 0;
  }
  function onStop() {
    clearInterval(interval);
    interval = undefined;
  }
  function onPress() {
    clearInterval(interval);
    interval = setInterval(() => {
      // Deterministic increment for snapshot stability
      value += 7;
      if (value >= 100) {
        value = 100;
        clearInterval(interval);
      }
    }, 100);
  }
</script>

<ColumnLayout>
  <Fieldset title="Animation">
    <div class="c-progress-augment">
      <p>Value {value}</p>
      <ProgressAugment {value} />
      <div class="c-progress-augment__actions">
        <ButtonAugment on:click={interval ? onStop : onPress}>
          {#if interval}
            Stop
          {:else}
            Start
          {/if}
        </ButtonAugment>
        <ButtonAugment on:click={onReset}>Reset</ButtonAugment>
      </div>
    </div>
  </Fieldset>

  <Fieldset title="Sizes">
    {#each sizeVariants as size}
      <div class="c-progress-augment">
        <p>Size {size}</p>
        <ProgressAugment {size} value={75} max={100} />
      </div>
    {/each}
  </Fieldset>

  <Fieldset title="Colors">
    {#each colorVariants as color}
      <div class="c-progress-augment">
        <p>Color {color}</p>
        <ProgressAugment {color} value={75} max={100} />
      </div>
    {/each}
  </Fieldset>

  <Fieldset title="Variants">
    {#each variantVariants as variant}
      <div class="c-progress-augment">
        <p>Variant {variant}</p>
        <ProgressAugment {variant} value={75} max={100} />
      </div>
    {/each}
  </Fieldset>

  <Fieldset title="Progress Values">
    {#each progressValues as value}
      <div class="c-progress-augment">
        <p>Value {value}%</p>
        <ProgressAugment {value} max={100} />
      </div>
    {/each}
  </Fieldset>

  <Fieldset title="Combinations">
    <div class="c-progress-augment">
      <p>Accent + Solid + Size 3</p>
      <ProgressAugment color="accent" variant="surface" size={3} value={75} max={100} />
    </div>
    <div class="c-progress-augment">
      <p>Error + Soft + Size 2</p>
      <ProgressAugment color="error" variant="soft" size={2} value={50} max={100} />
    </div>
    <div class="c-progress-augment">
      <p>Success + Solid + Size 1</p>
      <ProgressAugment color="success" variant="surface" size={1} value={100} max={100} />
    </div>
  </Fieldset>
</ColumnLayout>

<style>
  .c-progress-augment {
    margin-bottom: var(--ds-spacing-2);
  }
  .c-progress-augment__actions {
    display: flex;
    flex-direction: row;
    gap: var(--ds-spacing-2);
    padding: var(--ds-spacing-2) 0;
  }
</style>
