/* eslint-disable @typescript-eslint/naming-convention */
import type { Meta, StoryObj } from "@storybook/svelte";
import NotificationBanner from "$common-webviews/src/apps/chat/components/warnings/NotificationBanner.svelte";

const meta = {
  title: "app/Chat/components/warnings/NotificationBanner",
  component: NotificationBanner,
  tags: ["autodocs"],
  argTypes: {
    notificationId: {
      control: { type: "text" },
      description: "Unique identifier for the notification.",
    },
    message: {
      control: { type: "text" },
      description: "The notification message to display.",
    },
    level: {
      control: { type: "select" },
      options: ["info", "warning", "error"],
      description: "The notification level (info, warning, or error).",
    },
    actionItems: {
      control: { type: "object" },
      description: "Array of action items with title and optional URL.",
    },
  },
} satisfies Meta<NotificationBanner>;

export default meta;

type Story = StoryObj<typeof meta>;

// Info notification without action
export const InfoWithoutAction: Story = {
  name: "Info - No Action",
  args: {
    notificationId: "info-no-action",
    message: "This is an informational notification without any actions.",
    level: "info",
    actionItems: [],
  },
};

// Info notification with action
export const InfoWithAction: Story = {
  name: "Info - With Action",
  args: {
    notificationId: "info-with-action",
    message: "This is an informational notification with an action button.",
    level: "info",
    actionItems: [
      {
        title: "Learn More",
        url: "https://example.com/learn-more",
      },
    ],
  },
};

// Warning notification without action
export const WarningWithoutAction: Story = {
  name: "Warning - No Action",
  args: {
    notificationId: "warning-no-action",
    message: "This is a warning notification without any actions.",
    level: "warning",
    actionItems: [],
  },
};

// Warning notification with action
export const WarningWithAction: Story = {
  name: "Warning - With Action",
  args: {
    notificationId: "warning-with-action",
    message: "This is a warning notification with an action button.",
    level: "warning",
    actionItems: [
      {
        title: "Fix Issue",
        url: "https://example.com/fix-issue",
      },
    ],
  },
};

// Error notification without action
export const ErrorWithoutAction: Story = {
  name: "Error - No Action",
  args: {
    notificationId: "error-no-action",
    message: "This is an error notification without any actions.",
    level: "error",
    actionItems: [],
  },
};

// Error notification with action
export const ErrorWithAction: Story = {
  name: "Error - With Action",
  args: {
    notificationId: "error-with-action",
    message: "This is an error notification with an action button.",
    level: "error",
    actionItems: [
      {
        title: "Report Bug",
        url: "https://example.com/report-bug",
      },
    ],
  },
};
