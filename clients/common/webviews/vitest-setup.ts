// Cleanup step to reset vitest before next iteration
import "vitest";
import "@testing-library/jest-dom";

import { beforeEach, vi } from "vitest";
import { resetAgentRequestEventReporter } from "@augment-internal/sidecar-libs/src/metrics/agent-request-event-reporter";
import { setLibraryLogger } from "@augment-internal/sidecar-libs/src/logging";

// Always use the mock for host
vi.mock("$common-webviews/src/common/hosts/host");

// Set up a mock logger for tests
const mockLogger = {
  child: vi.fn().mockReturnValue({
    log: vi.fn(),
    info: vi.fn(),
    debug: vi.fn(),
    warn: vi.fn(),
    error: vi.fn(),
    verbose: vi.fn(),
  }),
  log: vi.fn(),
  info: vi.fn(),
  debug: vi.fn(),
  warn: vi.fn(),
  error: vi.fn(),
  verbose: vi.fn(),
} as any;

setLibraryLogger(mockLogger);

beforeEach(() => {
  // Restoring all mocks will restore original implementations of spys
  vi.restoreAllMocks();

  // Reset metrics reporters to avoid logger initialization issues
  resetAgentRequestEventReporter();

  // Re-establish the Element.prototype.animate mock after restoreAllMocks
  Element.prototype.animate = vi.fn().mockImplementation(() => {
    // Create a simple object that can have properties set on it
    // Use a more direct approach to ensure all properties are accessible
    const animationMock = {
      // Animation properties
      currentTime: 0,
      effect: null,
      finished: Promise.resolve(),
      id: "",
      pending: false,
      playState: "running",
      playbackRate: 1,
      ready: Promise.resolve(),
      replaceState: "active",
      startTime: 0,
      timeline: null,

      // Event handlers - these are the critical ones that need to be writable
      oncancel: null,
      onfinish: null,
      onremove: null,

      // Methods
      cancel: vi.fn(),
      commitStyles: vi.fn(),
      finish: vi.fn(),
      pause: vi.fn(),
      persist: vi.fn(),
      play: vi.fn(),
      reverse: vi.fn(),
      updatePlaybackRate: vi.fn(),

      addEventListener: vi.fn(),
      removeEventListener: vi.fn(),
      dispatchEvent: vi.fn().mockReturnValue(true),
    };

    return animationMock;
  });
});

// Needed for tests referencing MonacoEditor to load
document.queryCommandSupported = vi.fn().mockImplementation(() => false);

// Mock ResizeObserver globally
class ResizeObserverMock {
  observe = vi.fn();
  unobserve = vi.fn();
  disconnect = vi.fn();
}

global.ResizeObserver = ResizeObserverMock;

// Mock IntersectionObserver globally
class IntersectionObserverMock implements IntersectionObserver {
  readonly root: Element | null = null;
  readonly rootMargin: string = "0px";
  readonly thresholds: ReadonlyArray<number> = [0];

  constructor(
    private callback: IntersectionObserverCallback,
    private options?: IntersectionObserverInit,
  ) {}

  observe = vi.fn();
  unobserve = vi.fn();
  disconnect = vi.fn();
  takeRecords = vi.fn().mockReturnValue([]);
}

global.IntersectionObserver = IntersectionObserverMock as unknown as typeof IntersectionObserver;

// Mock HTMLDialogElement methods for jsdom compatibility
Object.defineProperty(HTMLDialogElement.prototype, "show", {
  value: vi.fn(),
  writable: true,
});

Object.defineProperty(HTMLDialogElement.prototype, "showModal", {
  value: vi.fn(),
  writable: true,
});

Object.defineProperty(HTMLDialogElement.prototype, "close", {
  value: vi.fn(),
  writable: true,
});

Object.defineProperty(HTMLDialogElement.prototype, "open", {
  value: false,
  writable: true,
});
