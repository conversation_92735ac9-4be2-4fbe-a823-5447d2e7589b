<script lang="ts">
  import { type IEditSuggestion } from "$vscode/src/next-edit/next-edit-types";
  import type * as Monaco from "monaco-editor/esm/vs/editor/editor.api";
  import { onDestroy, onMount } from "svelte";
  import {
    applyChanges,
    cacheKeyForSuggestions,
    calculateVisibleLines,
    combineLineChangesBySuggestion,
    normalizeRange,
  } from "./diff-util";
  import { type OnCodeAction, type ActionConfig } from "../types";
  import CodeRollSuggestionWindow from "../CodeRollSuggestionWindow.svelte";
  import { countLines, noop } from "../code-roll-util";
  import { resize } from "../resize-observer";
  import { themeStore } from "../../../hosts/user-themes/theme-store";
  import { getMonacoTheme } from "../../../utils/monaco-theme";
  import SpinnerAugment from "../../../../design-system/components/SpinnerAugment.svelte";
  import { MonacoContext } from "$common-webviews/src/design-system/components/MonacoProvider";

  export let height = 500;
  export let language: string;
  export let originalCode: string;
  export let suggestions: IEditSuggestion[];
  export let codeActions: ActionConfig[] = [];
  export let onCodeAction: OnCodeAction = noop;
  export let path: string;
  export let busy: boolean = true;
  export let expanded: boolean = false;
  export let scrollContainer: HTMLElement | undefined = undefined;
  export let options: Monaco.editor.IStandaloneDiffEditorConstructionOptions = {
    enableSplitViewResizing: false,
    automaticLayout: true,
    readOnly: true,
    overviewRulerLanes: 0,
    lineHeight: 20,
    renderLineHighlight: "none",
    contextmenu: false,
    renderSideBySide: false,
    renderIndicators: true,
    renderMarginRevertIcon: false,
    originalEditable: false,
    diffCodeLens: false,
    renderOverviewRuler: false,
    ignoreTrimWhitespace: false,
    maxComputationTime: 3000,
    scrollBeyondLastColumn: 0,
    scrollBeyondLastLine: false,
    scrollPredominantAxis: false,
    scrollbar: {
      alwaysConsumeMouseWheel: false,
      vertical: "hidden",
      horizontal: "hidden",
    },
    cursorSurroundingLines: 0,
    cursorSurroundingLinesStyle: "all",
    hideUnchangedRegions: {
      enabled: !expanded,
      revealLineCount: 5,
      minimumLineCount: 3,
      contextLineCount: 5,
    },
    lineNumbers: String,
    hover: { enabled: false },
  };

  const monacoContext = MonacoContext.getContext();
  const monaco = monacoContext.monaco;

  let cacheKey = cacheKeyForSuggestions(suggestions);
  let lastOriginalCode = "";

  // Create a reactive editor options that includes the current theme
  $: currentOptions = {
    ...options,
    theme: getMonacoTheme($themeStore?.category, $themeStore?.intensity),
  };

  $: {
    diffEditor?.updateOptions({
      hideUnchangedRegions: {
        enabled: !expanded,
        revealLineCount: 5,
        minimumLineCount: 3,
        contextLineCount: 5,
      },
    });
  }

  let disposable: Monaco.IDisposable[] = [];
  let editorContainer: HTMLElement | undefined;
  let editorNode = document.createElement("div");
  editorNode.classList.add("c-diff-view__editor");

  let diffEditor: Monaco.editor.IStandaloneDiffEditor | undefined;
  // we just hold this listener so we can dispose it when we recreate the editor.
  let onDidUpdateDiffListener: Monaco.IDisposable | undefined;
  let changes: Monaco.editor.ILineChange[] = [];
  let hasTopDecorations: boolean = false;

  function computeHeight(diffEditor: Monaco.editor.IStandaloneDiffEditor | undefined) {
    const calc = calculateVisibleLines(
      suggestions,
      options.hideUnchangedRegions,
      countLines(originalCode),
      changes.length > 0 ? combineLineChangesBySuggestion(suggestions, changes) : undefined,
    );
    height =
      expanded && diffEditor
        ? diffEditor.getModifiedEditor().getContentHeight()
        : calc.lines * (options.lineHeight ?? 20) + calc.decorations * 24;
    hasTopDecorations = calc.hasTopDecorations;
  }

  function initializeEditor() {
    diffEditor?.getModel()?.original?.dispose();
    diffEditor?.getModel()?.modified?.dispose();
    if (!$monaco) {
      return;
    }

    // we reuse the diff editor if it exists, but rebuild the models.
    if (!diffEditor) {
      diffEditor = $monaco.editor.createDiffEditor(editorNode, currentOptions);
      disposable.push(diffEditor);
    }
    diffEditor.onDidDispose(() => (diffEditor = undefined));
    disposable.push(
      ...applyChanges(diffEditor, suggestions, originalCode, language, path, $monaco),
    );

    computeHeight(diffEditor);

    onDidUpdateDiffListener?.dispose();
    onDidUpdateDiffListener = diffEditor.onDidUpdateDiff(() => {
      //do this here so we don't see all the content before the diff has been figured out.
      busy = false;
      changes = diffEditor?.getLineChanges() ?? [];
      // recalculate height
      computeHeight(diffEditor);
    });
  }

  onMount(() => {
    editorContainer!.appendChild(editorNode);
    initializeEditor();
  });

  onDestroy(() => {
    disposable.forEach((d) => d?.dispose?.());
    editorNode.remove();
  });

  $: {
    const themeDetails = $themeStore;
    const monacoTheme = getMonacoTheme(themeDetails?.category, themeDetails?.intensity);
    $monaco?.editor.setTheme(monacoTheme);
    diffEditor?.getModifiedEditor().updateOptions({ theme: monacoTheme });
    diffEditor?.getOriginalEditor().updateOptions({ theme: monacoTheme });
    diffEditor?.layout();
  }
  $: {
    const newCacheKey = cacheKeyForSuggestions(suggestions);
    //This re-initializes the editor when the suggestions or original code change.
    if (cacheKey !== newCacheKey || originalCode !== lastOriginalCode) {
      cacheKey = newCacheKey;
      lastOriginalCode = originalCode;
      initializeEditor();
    }
  }
</script>

<div
  class="c-diff-view__container"
  style="--augment-codeblock-min-height:{height}px;"
  class:has-top-decorations={hasTopDecorations}
>
  <div
    class="c-diff-view"
    style:display={busy ? "none" : "flex"}
    use:resize={{ onResize: () => diffEditor?.layout() }}
    bind:this={editorContainer}
  ></div>
  {#if busy || !diffEditor}
    <div class="c-diff-view__loading">
      <SpinnerAugment />
    </div>
  {:else}
    {#each combineLineChangesBySuggestion(suggestions, changes) as [suggestion, change]}
      <CodeRollSuggestionWindow
        {diffEditor}
        {suggestion}
        {codeActions}
        {onCodeAction}
        {...normalizeRange(change)}
        {scrollContainer}
      />
    {/each}
  {/if}
</div>

<style>
  .c-diff-view :global(.cdr.line-insert),
  .c-diff-view :global(.cdr.line-delete),
  .c-diff-view :global(.monaco-diff-editor) :global(.line-delete),
  .c-diff-view :global(.monaco-diff-editor) :global(.line-insert),
  .c-diff-view :global(.monaco-diff-editor) :global(.cdr),
  .c-diff-view :global(.monaco-editor) :global(.line-delete),
  .c-diff-view :global(.monaco-editor) :global(.cdr),
  .c-diff-view :global(.monaco-editor) :global(.line-insert) {
    max-width: calc(100% - 5px);
  }

  .c-diff-view__container.has-top-decorations {
    margin-top: -16px; /* 8px - 24px for hidden decorations */
  }
  .c-diff-view__container {
    margin-top: 8px;
    position: relative;
    width: 100%;
    flex: 1;
    min-height: 300px;
  }
  .c-diff-view {
    position: relative;
    height: 50px;
    padding-top: 1px;
  }
  .c-diff-view__container :global(.c-diff-view__loading) {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    min-height: 20ch;
    align-self: center;
  }
  .c-diff-view,
  .c-diff-view :global(.c-diff-view___editor) {
    display: flex;
    position: relative;
    flex-direction: column;
    min-height: var(--augment-codeblock-min-height, 16ch);
    min-width: 0;
    height: 100%;
    width: calc(100% - 5px);
    overflow: hidden;
    flex: 1;
    transition: min-height 0.5s ease-in-out;
  }
  .c-diff-view :global(.diff-hidden-lines-widget) {
    visibility: hidden !important;
  }
  .c-diff-view :global(.monaco-diff-editor) {
    height: 100%;
    width: 100%;
    display: flex;
    flex: 1;
  }
  :global(.c-diff-view__editor) {
    display: flex;
    flex-direction: column;
    flex: 1;
    width: calc(100% + 50px);
    height: 100%;
    position: absolute;
    left: -36px;
  }
  .c-diff-view :global(.monaco-editor),
  .c-diff-view :global(.margin.margin),
  .c-diff-view :global(.monaco-editor-background),
  .c-diff-view :global(.monaco-diff-editor) {
    --vscode-focusBorder: transparent;
    --vscode-editor-background: transparent;
    --vscode-editorGutter-background: transparent;
    background-color: transparent;
  }
  .c-diff-view :global(.margin-view-overlays) {
    left: -5px;
  }
  .c-diff-view.c-diff-view :global(.core-guide-indent),
  .c-diff-view.c-diff-view :global(.view-lines) {
    box-shadow: none;
  }
  :global(.c-diff-view__editor) :global(.codicon-diff-insert)::before,
  :global(.c-diff-view__editor) :global(.codicon-diff-delete)::before {
    content: unset;
  }
  .c-diff-view :global(.monaco-scrollable-element > .scrollbar > .slider) {
    background-color: var(--augment-window-background);
  }
</style>
