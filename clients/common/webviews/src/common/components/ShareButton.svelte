<script lang="ts">
  import ShareIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/share-nodes.svg?component";
  import type {
    ButtonSize,
    ButtonVariant,
    ButtonColor,
  } from "$common-webviews/src/design-system/_primitives/BaseButton.svelte";
  import SuccessfulButton from "$common-webviews/src/apps/chat/components/buttons/SuccessfulButton.svelte";
  import { getChatModel } from "$common-webviews/src/apps/chat/chat-context";
  export let size: ButtonSize = 1;
  export let variant: ButtonVariant = "ghost-block";
  export let color: ButtonColor = "neutral";
  export let tooltip = "Share";
  export let stickyColor = false;

 const chatModel = getChatModel();

  export let onClick = async () => {
    const conversationModel = chatModel.currentConversationModel;
    if (!conversationModel?.id) {
      return;
    }

    const url = await chatModel?.getConversationUrl(conversationModel.id);
    if (url) {
      await navigator.clipboard.writeText(url);
      return 'success';
    }
  };
  export let tooltipNested: boolean | undefined = undefined;
  let loading = false;
</script>

<span class="c-share-button">
  <SuccessfulButton
    defaultColor={color}
    {size}
    {variant}
    {loading}
    {stickyColor}
    tooltip={{ neutral: tooltip, success: "Link copied!" }}
    stateVariant={{ success: "soft" }}
    {onClick}
    icon={!$$slots.text}
    {tooltipNested}
  >
    <svelte:fragment slot="iconLeft">
      {#if $$slots.icon}
        <slot name="icon" />
      {:else}
        <ShareIcon />
      {/if}
    </svelte:fragment>
    <slot name="text" />
  </SuccessfulButton>
</span>

<style>
  .c-share-button {
    display: contents;
  }
</style>
