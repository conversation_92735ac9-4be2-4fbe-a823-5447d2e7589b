<script lang="ts">
  import ButtonAugment from "$common-webviews/src/design-system/components/ButtonAugment.svelte";
  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";
  import CommitIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/code-commit.svg?component";

  // Props
  export let onCommit: () => void | Promise<void>;
  export let variant: "solid" | "soft" = "solid";
  export let stagedFilesCount: number = 0;
  export let isCommitting: boolean = false;
  export let canCommit: boolean = true;
  export let disabled: boolean = false;

  // Computed properties
  $: actuallyCanCommit = canCommit && stagedFilesCount > 0 && !disabled;
  $: commitButtonText = "Commit"; // Keep text consistent to prevent size changes
  $: tooltipText = !canCommit
    ? "Cannot commit at this time"
    : stagedFilesCount === 0
      ? "No staged files to commit"
      : disabled
        ? "Enter a commit message"
        : `Commit ${stagedFilesCount} staged file${stagedFilesCount === 1 ? "" : "s"}`;

  // Handle commit action
  async function handleCommit() {
    if (!actuallyCanCommit || isCommitting) return;

    try {
      await onCommit();
    } catch (error) {
      console.error("Failed to commit:", error);
    }
  }
</script>

<div class="c-git-commit-actions">
  <TextTooltipAugment content={tooltipText}>
    <ButtonAugment
      {variant}
      size={1}
      disabled={!actuallyCanCommit || isCommitting}
      loading={isCommitting}
      on:click={handleCommit}
      class="c-git-commit-actions__commit-button"
    >
      <CommitIcon slot="iconLeft" />
      {commitButtonText}
    </ButtonAugment>
  </TextTooltipAugment>
</div>

<style>
  .c-git-commit-actions {
    display: flex;
    align-items: center;
    gap: var(--ds-spacing-1);
  }

  .c-git-commit-actions :global(.c-git-commit-actions__commit-button) {
    font-weight: var(--ds-font-weight-medium);
    border-radius: var(--ds-radius-2);
    /* Prevent button expansion during loading state */
    min-width: max-content;
  }

  .c-git-commit-actions :global(.c-git-commit-actions__commit-button svg) {
    width: var(--icon-size);
    height: var(--icon-size);
    rotate: -90deg;
  }
</style>
