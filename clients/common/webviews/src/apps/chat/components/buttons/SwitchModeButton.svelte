<script lang="ts">
  import ButtonAugment from "$common-webviews/src/design-system/components/ButtonAugment.svelte";
  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";
  import { TooltipTriggerOn } from "$common-webviews/src/design-system/_primitives/TooltipAugment/types";
  import { getContext } from "svelte";
  import {
    SWAPPABLE_PANEL_CONTEXT_KEY,
    type SwappablePanelContext,
    PanelType,
  } from "../SwappablePanel.svelte";
  import { getTabGroupContext } from "$common-webviews/src/design-system/components/TabsAugment/context";

  // Import icons for tasks and code changes
  import TaskListIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/list-check.svg?component";
  import DiffIcon from "$common-webviews/src/design-system/icons/diff.svelte";
  import {
    CurrentConversationTaskStore,
    type ICurrentConversationTaskStore,
  } from "../../models/task-store";

  // Get both contexts - SwappablePanel (legacy) and TabsAugment (new)
  const swappablePanelContext = getContext<SwappablePanelContext | undefined>(
    SWAPPABLE_PANEL_CONTEXT_KEY,
  );
  const tabGroupContext = getTabGroupContext();
  const taskStore = getContext<ICurrentConversationTaskStore | undefined>(
    CurrentConversationTaskStore.key,
  );

  // Determine which context to use
  $: usingTabs = !!tabGroupContext;
  $: activePanel = swappablePanelContext?.activePanel;
  $: activeTab = tabGroupContext?.model.activeTab;

  // Determine current state based on context
  $: isTasksActive = usingTabs ? $activeTab === "tasks" : $activePanel === PanelType.tasks;
  $: isAgentEditsActive = usingTabs
    ? $activeTab === "agent-edits"
    : $activePanel === PanelType.agentEdits;

  // Determine the tooltip text based on the current active state
  $: tooltipText = isAgentEditsActive ? "Switch to Tasks view" : "Switch to Changes view";

  // Handle toggle with appropriate context
  async function togglePanel() {
    if (usingTabs && tabGroupContext) {
      // Use TabsAugment context
      const currentTab = $activeTab;
      if (currentTab === "agent-edits") {
        tabGroupContext.controller.setActiveTab("tasks");
      } else {
        tabGroupContext.controller.setActiveTab("agent-edits");
      }
    } else if (swappablePanelContext) {
      // Use SwappablePanel context (legacy)
      swappablePanelContext.togglePanel();
    }
  }
</script>

{#if swappablePanelContext && taskStore}
  <TextTooltipAugment content={tooltipText} triggerOn={[TooltipTriggerOn.Hover]} side="top">
    <div class="c-switch-mode-button">
      <div class="c-switch-mode-button__group">
        <ButtonAugment
          variant={isTasksActive ? "soft" : "ghost"}
          color={isTasksActive ? "accent" : "neutral"}
          size={1}
          class="c-switch-mode-button__tasks-btn"
          on:click={togglePanel}
        >
          <div class="c-switch-mode-button__content">
            <TaskListIcon />
          </div>
        </ButtonAugment>

        <ButtonAugment
          variant={isAgentEditsActive ? "soft" : "ghost"}
          color={isAgentEditsActive ? "accent" : "neutral"}
          size={1}
          class="c-switch-mode-button__changes-btn"
          on:click={togglePanel}
        >
          <div class="c-switch-mode-button__content">
            <DiffIcon />
          </div>
        </ButtonAugment>
      </div>
    </div>
  </TextTooltipAugment>
{/if}

<style>
  .c-switch-mode-button {
    display: flex;
    align-items: center;
  }

  .c-switch-mode-button__group {
    display: inline-flex;
    background-color: var(--ds-color-neutral-a3);
    border-radius: var(--ds-radius-2);
    padding: calc(0.25 * var(--ds-spacing-1));
    overflow: hidden;
    gap: calc(0.25 * var(--ds-spacing-1));
  }

  /* Style for the buttons */
  :global(.c-switch-mode-button__tasks-btn),
  :global(.c-switch-mode-button__changes-btn) {
    border-radius: var(--ds-radius-1);
    transition: all 0.2s ease-in-out;
  }

  /* Active button styling */
  :global(.c-switch-mode-button__tasks-btn.c-base-btn--variant-soft),
  :global(.c-switch-mode-button__changes-btn.c-base-btn--variant-soft) {
    background-color: var(--ds-color-neutral-2);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    min-width: 80px;
    font-weight: 500;
  }

  /* Inactive button styling */
  :global(.c-switch-mode-button__tasks-btn.c-base-btn--variant-ghost),
  :global(.c-switch-mode-button__changes-btn.c-base-btn--variant-ghost) {
    min-width: 32px;
    width: 32px;
    padding-left: var(--ds-spacing-1);
    padding-right: var(--ds-spacing-1);
  }

  /* Content layout */
  .c-switch-mode-button__content {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--ds-spacing-1);
    white-space: nowrap;
  }

  /* Ensure icons have consistent size */
  .c-switch-mode-button__content :global(svg) {
    width: 14px;
    height: 14px;
    flex-shrink: 0;
  }
</style>
