<script lang="ts">
  import {
    type ButtonVariant,
    type ButtonColor,
  } from "$common-webviews/src/design-system/_primitives/BaseButton.svelte";
  import { TooltipTriggerOn } from "$common-webviews/src/design-system/_primitives/TooltipAugment/types";
  import ButtonAugment from "$common-webviews/src/design-system/components/ButtonAugment.svelte";
  import IconButtonAugment from "$common-webviews/src/design-system/components/IconButtonAugment.svelte";
  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";
  import type { ButtonStateText, ButtonStateVariant, ButtonState } from "./types";

  /** The default (neutral state) color of the button */
  export let defaultColor: ButtonColor;
  /** The tooltip text to display for each state */
  export let tooltip: ButtonStateText | undefined = undefined;
  /** The variant to use for each state */
  export let stateVariant: ButtonStateVariant | undefined = undefined;
  /** The callback to call when the button is clicked.
   *  If it returns a promise it will wait for that promise
   *  if the onClick handler throws an error, it will set the state to failure.
   *  Returns the state to set the button to */
  export let onClick: (e: MouseEvent) => ButtonState | undefined | Promise<ButtonState | undefined>;
  /** The duration in ms to show the tooltip for when the state changes on click */
  export let tooltipDuration: number = 1500;
  /** Whether this button is an icon button */
  export let icon: boolean = false;
  /** Whether the success/error color should persist after tooltip closes */
  export let stickyColor: boolean = true;
  /** Whether success/error should persist after tooltip closes */
  export let persistOnTooltipClose: boolean = false;
  /** Whether the tooltip element should be nested */
  export let tooltipNested: boolean | undefined = undefined;

  $: ({ variant: defaultVariant, ...restProps } = $$restProps);
  $: variant = stateVariant?.[state] ?? (defaultVariant as ButtonVariant);

  let state: ButtonState = "neutral";
  let color: ButtonColor = defaultColor;

  $: {
    if (state === "success") {
      color = "success";
    } else if (state === "failure") {
      color = "error";
    } else {
      color = defaultColor;
    }
  }

  let requestClose: (() => void) | undefined = undefined;
  let tooltipTimer: ReturnType<typeof setTimeout> | undefined = undefined;
  let tooltipText = tooltip?.neutral;

  function onOpenChange(open: boolean) {
    if (!persistOnTooltipClose && !open) {
      clearTimeout(tooltipTimer);
      tooltipTimer = undefined;
      tooltipText = tooltip?.neutral;
      if (!stickyColor) {
        state = "neutral";
      }
    }
  }

  async function onButtonClick(e: MouseEvent) {
    try {
      state = (await onClick(e)) ?? "neutral";
    } catch (e) {
      console.error(e);
      state = "failure";
    }
    tooltipText = tooltip?.[state];
    clearTimeout(tooltipTimer);
    tooltipTimer = setTimeout(() => {
      requestClose?.();
      if (!stickyColor) {
        state = "neutral";
      }
    }, tooltipDuration);
  }
</script>

<div class="c-successful-button">
  <TextTooltipAugment
    bind:requestClose
    {onOpenChange}
    content={tooltipText}
    triggerOn={[TooltipTriggerOn.Hover]}
    nested={tooltipNested}
  >
    {#if icon}
      <IconButtonAugment
        {...restProps}
        {color}
        {variant}
        on:click={onButtonClick}
        on:keyup
        on:keydown
        on:mousedown
        on:mouseover
        on:focus
        on:mouseleave
        on:blur
        on:contextmenu
      >
        <slot name="iconLeft" />
        <slot />
        <slot name="iconRight" />
      </IconButtonAugment>
    {:else}
      <ButtonAugment
        {...restProps}
        {color}
        {variant}
        on:click={onButtonClick}
        on:keyup
        on:keydown
        on:mousedown
        on:mouseover
        on:focus
        on:mouseleave
        on:blur
        on:contextmenu
      >
        <slot name="iconLeft" slot="iconLeft" />
        <slot />
        <slot name="iconRight" slot="iconRight" />
      </ButtonAugment>
    {/if}
  </TextTooltipAugment>
</div>

<style>
  .c-successful-button {
    display: inline-flex;
  }
</style>
