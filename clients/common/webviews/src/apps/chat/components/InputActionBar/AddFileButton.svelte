<script lang="ts">
  import Paperclip from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/paperclip.svg?component";
  import CommandContent from "./CommandContent.svelte";
  import { getRichTextEditorContext } from "$common-webviews/src/design-system/components/RichTextEditorAugment/context";
  import IconButtonAugment from "$common-webviews/src/design-system/components/IconButtonAugment.svelte";
  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";
  import KeyboardShortcutHint from "$common-webviews/src/common/components/keybindings/KeyboardShortcutHint.svelte";
  import { TooltipTriggerOn } from "$common-webviews/src/design-system/_primitives/TooltipAugment/types";

  export let accept = "*";
  export let disabled: boolean = false;
  export let onAddFiles = handleAddFiles;

  const fileId = `file-${Date.now()}-${Math.floor(Math.random() * 10_000)}`;
  const editorContext = getRichTextEditorContext();

  function handleAddFiles(files: File[]) {
    editorContext.commandManager?.chain()?.insertFilesIntoEditor?.(files);
  }
</script>

<CommandContent>
  <TextTooltipAugment triggerOn={[TooltipTriggerOn.Hover]} delayDurationMs={300}>
    <div slot="content" class="c-add-file-button-tooltip">
      Hold <KeyboardShortcutHint icons={["shift"]} /> while dragging<br />to attach images
    </div>
    <IconButtonAugment
      size={1}
      variant="ghost-block"
      color="neutral"
      {disabled}
      class="c-input-action-bar__add-file-btn"
    >
      <label for={fileId} class="c-input-action-bar__label">
        <Paperclip />
      </label>
      <input
        type="file"
        hidden
        id={fileId}
        on:click={function () {
          this.value = null;
        }}
        on:input={function (e) {
          onAddFiles([...(e.currentTarget?.files ?? [])]);
        }}
        {accept}
      />
    </IconButtonAugment>
  </TextTooltipAugment>
</CommandContent>

<style>
  .c-input-action-bar__label {
    display: contents;
    position: relative;
    cursor: pointer;
  }

  .c-add-file-button-tooltip {
    --keyboard-shortcut-hint-color: var(--gray-1);
    --keyboard-shortcut-hint-border: var(--gray-1);
    text-align: center;
  }
</style>
