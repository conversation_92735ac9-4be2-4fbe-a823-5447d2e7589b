<script lang="ts">
  import ButtonAugment from "$common-webviews/src/design-system/components/ButtonAugment.svelte";
  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";
  import Reload from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/arrow-rotate-right.svg?component";

  export let resendAction: () => unknown;
  export let onAnalyticsTrack: () => void;

  function handleResend() {
    // Track analytics
    onAnalyticsTrack();
    // Call the original resend action
    resendAction();
  }
</script>

<div class="c-resend-button">
  <TextTooltipAugment content="Resend">
    <ButtonAugment
      size={1}
      variant="soft"
      color="neutral"
      on:click={handleResend}
      on:keyup={handleResend}
    >
      <Reload slot="iconLeft" />
    </ButtonAugment>
  </TextTooltipAugment>
</div>

<style>
  .c-resend-button {
    --cancel-stop-icon-size: 15px;
  }
  .c-resend-button :global(svg) {
    height: var(--cancel-stop-icon-size);
    width: var(--cancel-stop-icon-size);
  }
</style>
