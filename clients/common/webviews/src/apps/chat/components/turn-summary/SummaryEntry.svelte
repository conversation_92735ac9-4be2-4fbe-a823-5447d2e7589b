<script lang="ts">
  import MaterialIcon from "$common-webviews/src/common/components/MaterialIcon.svelte";
  import AnimatedNumberIndicatorAugment from "$common-webviews/src/design-system/components/AnimatedNumberIndicatorAugment.svelte";
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import type { SummaryEntry } from "./types";

  export let entry: SummaryEntry;
  export let index: number;
  export let animationResetKey: number;
  export let shouldStartNumbers: boolean;
  export let fadeInDelay: number;
</script>

{#snippet entryContent()}
  <MaterialIcon iconName={entry.icon} class="c-turn-summary__item__icon" />
  {#key `${animationResetKey}-${index}-${entry.value}`}
    <AnimatedNumberIndicatorAugment
      value={entry.value}
      size={1}
      autoStart={shouldStartNumbers}
      delay={index * fadeInDelay * 1.1}
    />
  {/key}
  <TextAugment size={1} color="secondary">
    {entry.label}
  </TextAugment>
{/snippet}

{#if entry.callback}
  <button class="c-turn-summary-item__link" on:click={entry.callback} type="button">
    {@render entryContent()}
  </button>
{:else}
  <span class="c-turn-summary-item__content">
    {@render entryContent()}
  </span>
{/if}

<style>
  .c-turn-summary-item__content,
  .c-turn-summary-item__link {
    display: flex;
    align-items: center;
    gap: var(--ds-spacing-1);
  }

  .c-turn-summary-item__link {
    cursor: pointer;
    transition: all 0.15s ease-in-out;
    background: none;
    border: none;
    padding: 0;
    font: inherit;
    color: inherit;

    &:hover {
      color: var(--ds-color-accent-a11);

      &:global(.c-animated-number-indicator) {
        color: var(--ds-color-accent-a11);
      }
    }

    &:active {
      transform: translateY(-1px);
    }
  }

  .c-turn-summary-item__link:hover :global(.c-text) {
    color: var(--ds-color-accent-a11);
  }
</style>
