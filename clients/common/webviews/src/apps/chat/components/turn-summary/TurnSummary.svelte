<script context="module" lang="ts">
  export type { SummaryEntry } from "./types";
</script>

<script lang="ts">
  import { onMount, onDestroy } from "svelte";
  import { derived, writable } from "svelte/store";
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import { visibilityObserverOnce } from "$common-webviews/src/apps/chat/components/actions/trackOnScreen";
  import type { SummaryEntry } from "./types";
  import SummaryEntryComponent from "./SummaryEntry.svelte";
  import ScrollNavigationButtons from "$common-webviews/src/design-system/components/ScrollNavigationButtons/ScrollNavigationButtons.svelte";

  export let title: string = "Summary";
  export let entries: SummaryEntry[] = [];

  // Filter out entries with zero values
  $: filteredEntries = entries.filter((entry) => entry.value > 0);
  $: secondColumnStartIndex = Math.ceil(filteredEntries.length / 2);

  // Animation timing constants
  const FADE_IN_DELAY = 100; // Delay between each item's fade-in animation

  // Animation orchestration stores
  const isVisible = writable(true);
  const shouldShowFooter = writable(false);

  // Simple approach: use a single store and handle delays in the component
  const shouldStartNumbers = derived(isVisible, ($isVisible) => $isVisible);

  let containerElement: HTMLElement;
  let listElement: HTMLElement;

  // Timeout tracking for cleanup
  let footerTimeoutId: ReturnType<typeof setTimeout> | undefined;
  let initialVisibilityTimeoutId: ReturnType<typeof setTimeout> | undefined;

  // Shared function to trigger animations and avoid code duplication
  function triggerAnimations() {
    $isVisible = true;
    // Clear any existing footer timeout
    if (footerTimeoutId !== undefined) {
      clearTimeout(footerTimeoutId);
    }
    // Trigger footer animation after all entries have finished animating
    const footerDelay = filteredEntries.length * FADE_IN_DELAY;
    footerTimeoutId = setTimeout(() => {
      $shouldShowFooter = true;
    }, footerDelay);
  }

  onMount(() => {
    // Set up visibility observer to trigger animations when component comes into view
    const visibilityAction = visibilityObserverOnce(containerElement, {
      scrollTarget: document.body,
      threshold: 0.25,
      visibilityDuration: 100,
      onVisible: triggerAnimations,
    });

    // Fallback for environments where the component is already visible
    const checkInitialVisibility = () => {
      // Guard against null containerElement to prevent TypeError
      if (!containerElement) {
        return;
      }

      const rect = containerElement.getBoundingClientRect();
      const isInViewport = rect.top < window.innerHeight && rect.bottom > 0;

      if (isInViewport && !$isVisible) {
        initialVisibilityTimeoutId = setTimeout(() => {
          if (!$isVisible) {
            triggerAnimations();
          }
        }, 150);
      }
    };

    // Check visibility after a brief delay to ensure DOM is ready
    setTimeout(checkInitialVisibility, 100);

    return () => {
      visibilityAction.destroy();
    };
  });

  onDestroy(() => {
    // Clean up any pending timeouts to prevent memory leaks
    if (footerTimeoutId !== undefined) {
      clearTimeout(footerTimeoutId);
    }
    if (initialVisibilityTimeoutId !== undefined) {
      clearTimeout(initialVisibilityTimeoutId);
    }
  });

  export let isCardOpen = false;

  // Animation reset key - increment this to force component recreation
  let animationResetKey = 0;

  // Function to toggle card mode and reset animations
  function toggleCardMode() {
    isCardOpen = !isCardOpen;

    // Reset animation states to replay animations
    $isVisible = false;
    $shouldShowFooter = false;

    // Increment key to force recreation of AnimatedNumberIndicator components
    animationResetKey++;

    // Use a small delay to ensure the DOM updates before restarting animations
    setTimeout(() => {
      triggerAnimations();
    }, 50);
  }
</script>

<div>
  <div
    bind:this={containerElement}
    class="c-turn-summary"
    class:animate={$isVisible}
    class:c-turn-summary-marquee-mode={!isCardOpen}
    class:c-turn-summary-card-mode={isCardOpen}
  >
    <div class="c-turn-summary__header">
      <span
        class="c-turn-summary__title"
        role="button"
        tabindex="0"
        on:click={toggleCardMode}
        on:keydown={(e) => e.key === "Enter" && toggleCardMode()}>{title}</span
      >
    </div>

    {#if filteredEntries.length > 0}
      <div class="c-turn-summary__list-container">
        <ul bind:this={listElement} class="c-turn-summary__list">
          {#each filteredEntries as entry, index (entry.label)}
            <li
              class="c-turn-summary__item"
              style={`grid-column: ${index >= secondColumnStartIndex ? 2 : 1}; grid-row: ${index >= secondColumnStartIndex ? index - secondColumnStartIndex + 1 : index + 1}; animation-delay: ${index * 1 * FADE_IN_DELAY}ms`}
            >
              <SummaryEntryComponent
                {entry}
                {index}
                {animationResetKey}
                shouldStartNumbers={$shouldStartNumbers}
                fadeInDelay={FADE_IN_DELAY}
              />
            </li>
          {/each}
        </ul>
        <ScrollNavigationButtons scrollableElement={listElement} />
      </div>
    {:else}
      <TextAugment size={1} color="secondary">No activity to report.</TextAugment>
    {/if}
  </div>
  {#if $$slots.footer}
    <footer class="c-turn-summary__footer" class:animate-drawer={$shouldShowFooter}>
      <slot name="footer" />
    </footer>
  {/if}
</div>

<style>
  .c-turn-summary {
    container: turn-summary / inline-size;
    border-top: 1px solid var(--ds-color-neutral-a6);
    padding: var(--ds-spacing-2) 0 0;
    margin: 0 var(--ds-spacing-2);
  }

  .c-turn-summary__header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    line-height: 1;
    height: 1rem;
  }

  .c-turn-summary__title {
    font-size: var(--ds-font-size-1);
    cursor: pointer;
    transition: color 0.15s ease-in-out;
    user-select: none;
  }

  .c-turn-summary__title:hover {
    color: var(--ds-color-accent-a11);
  }

  .c-turn-summary-marquee-mode {
    &.c-turn-summary {
      display: grid;
      grid-template-columns: auto 1fr;
      gap: var(--ds-spacing-3);
    }

    & .c-turn-summary__list {
      display: flex;
      flex-direction: row;
      white-space: nowrap;
      overflow: scroll;
      gap: var(--ds-spacing-2);

      list-style: none;
      padding: 0;
      margin: 0;

      /* Hide scrollbar */
      scrollbar-width: none; /* Firefox */
      -ms-overflow-style: none; /* IE and Edge */

      /* Scroll snap */
      scroll-snap-type: x mandatory;
    }

    & .c-turn-summary__list::-webkit-scrollbar {
      display: none; /* Chrome, Safari, Opera */
    }
  }

  .c-turn-summary-card-mode {
    &.c-turn-summary {
      display: grid;
      grid-template-rows: auto auto;
      gap: var(--ds-spacing-2);
      margin-bottom: var(--ds-spacing-4);
    }

    & .c-turn-summary__list {
      display: flex;
      flex-direction: column;
      gap: var(--ds-spacing-1);

      list-style: none;
      padding: 0;
      margin: 0;
    }
  }

  .c-turn-summary__list-container {
    display: grid;
    grid-template-columns: 1fr min-content;
    gap: var(--ds-spacing-1);
  }

  .c-turn-summary__item {
    /* Initial state: invisible and slightly offset */
    opacity: 0;
    user-select: none;

    /* Scroll snap */
    scroll-snap-align: end;
    flex-shrink: 0;
  }

  .c-turn-summary__footer {
    border-top: 1px solid transparent;
    /* Initial state: hidden and collapsed */
    opacity: 0;
    max-height: 0;
    overflow: hidden;
  }

  .c-turn-summary__footer:empty {
    display: none;
  }

  /* Drawer animation when animate-drawer class is applied */
  .c-turn-summary__footer.animate-drawer {
    animation: slideDownDrawer 300ms cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
  }

  /* Only animate when parent has the 'animate' class */
  .c-turn-summary.animate .c-turn-summary__item {
    animation: fadeInUp 250ms ease-out forwards;
  }

  @container turn-summary (min-width: 20rem) {
    .c-turn-summary-card-mode .c-turn-summary__list {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: var(--ds-spacing-1);
      grid-auto-flow: row;
      grid-template-areas: "left right";
    }
  }

  /* Keyframe animation for fade-in with upward movement */
  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateX(2rem);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* Keyframe animation for drawer slide-down effect */
  @keyframes slideDownDrawer {
    from {
      opacity: 0;
      border-top-color: transparent;
      max-height: 0;
    }
    to {
      opacity: 1;
      border-top-color: var(--ds-color-neutral-a6);
      max-height: 3rem; /* More reasonable height for typical footer content */
    }
  }
</style>
