<script lang="ts">
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import TextFieldAugment from "$common-webviews/src/design-system/components/TextFieldAugment.svelte";
  import ChevronLeft from "$common-webviews/src/design-system/icons/chevron-left.svelte";
  import ChevronRight from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/chevron-right.svg?component";
  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";
  import IconButtonAugment from "$common-webviews/src/design-system/components/IconButtonAugment.svelte";
  import { tick } from "svelte";

  export let totalCheckpoints: number;
  export let currentCheckpoint: number | undefined = undefined;

  let isEditing = false;
  let inputElement: HTMLInputElement | undefined;
  let counterElement: HTMLDivElement | undefined;

  // Derived values to reduce repetition
  $: currentIndex = currentCheckpoint ?? totalCheckpoints - 1;
  $: displayedCheckpoint = currentIndex + 1;
  let inputValue = "";
  $: inputValue = isEditing ? inputValue : displayedCheckpoint.toString();

  async function startEditing() {
    isEditing = true;
    await tick();
    inputElement?.focus();
  }

  function onStopEditing() {
    inputElement?.blur();
    isEditing = false;
    const newValue = parseInt(inputValue) - 1;
    if (!isNaN(newValue) && newValue >= 0 && newValue < totalCheckpoints) {
      currentCheckpoint = newValue;
    }
    inputValue = displayedCheckpoint.toString();
  }

  function handlePrevious() {
    if (currentIndex > 0) {
      currentCheckpoint = currentIndex - 1;
    }
  }

  function handleNext() {
    if (currentIndex < totalCheckpoints - 1) {
      currentCheckpoint = currentIndex + 1;
    }
  }

  function handleInputFocus(event: Event) {
    (event.target as HTMLInputElement).select();
  }

  function handleInputKeydown(event: KeyboardEvent) {
    if (event.key === "Enter" || event.key === "Escape") {
      onStopEditing();
    }
  }

  function handleWindowClick(event: MouseEvent) {
    if (!isEditing) return;
    const target = event.target as Node;
    if (!counterElement?.contains(target)) {
      onStopEditing();
    }
  }
</script>

<svelte:window on:click|capture={handleWindowClick} />
<div class="c-checkpoint-counter" bind:this={counterElement}>
  <TextTooltipAugment content="Previous Checkpoint">
    <IconButtonAugment
      class="c-checkpoint-counter__prev-btn"
      size={1}
      variant="ghost"
      disabled={currentIndex <= 0}
      on:click={handlePrevious}
    >
      <ChevronLeft />
    </IconButtonAugment>
  </TextTooltipAugment>

  <TextTooltipAugment content="Double-click to edit Checkpoint">
    <div
      class="c-checkpoint-counter__display"
      role="button"
      tabindex="-1"
      on:dblclick={startEditing}
    >
      {#if isEditing}
        <TextFieldAugment
          class="c-checkpoint-counter__input"
          size={1}
          variant="surface"
          bind:value={inputValue}
          bind:textInput={inputElement}
          on:blur={onStopEditing}
          on:keydown={handleInputKeydown}
          on:focus={handleInputFocus}
        />
      {:else}
        <TextAugment size={1} color="neutral" class="c-checkpoint-counter__text current">
          {displayedCheckpoint}
        </TextAugment>
      {/if}
      <TextAugment size={1} color="neutral" class="c-checkpoint-counter__text separator"
        >/</TextAugment
      >
      <TextAugment size={1} color="neutral" class="c-checkpoint-counter__text total">
        {totalCheckpoints}
      </TextAugment>
    </div>
  </TextTooltipAugment>

  <TextTooltipAugment content="Next Checkpoint">
    <IconButtonAugment
      class="c-checkpoint-counter__next-btn"
      size={1}
      variant="ghost"
      disabled={currentIndex >= totalCheckpoints - 1}
      on:click={handleNext}
    >
      <ChevronRight />
    </IconButtonAugment>
  </TextTooltipAugment>
</div>

<style>
  .c-checkpoint-counter {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: var(--ds-spacing-2);
    margin: 0 var(--ds-spacing-2);
    max-width: 100%;
  }

  .c-checkpoint-counter__display {
    display: flex;
    align-items: center;
    cursor: pointer;
    user-select: none;
    gap: var(--ds-spacing-1);

    & :global(.c-checkpoint-counter__input) {
      min-width: 2.5rem;
      width: 2.5rem;
      text-align: center;
      display: flex;
      align-items: center;
    }
  }
</style>
