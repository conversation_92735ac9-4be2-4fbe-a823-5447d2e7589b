<script lang="ts">
  import { ChatResultNodeType } from "@augment-internal/sidecar-libs/src/chat/chat-types";
  import {
    countUniquePaths,
    flattenGroupIntoNodes,
    isMemoryNode,
    isStrReplaceToolNode,
    isViewToolNode,
  } from "../../types/chat-message";
  import { getChatModel } from "../../chat-context";
  import MemoryModalContent from "../chat-thread-body/MemoryModalContent.svelte";
  import FullSizeOverlayAugment from "$common-webviews/src/design-system/components/FullSizeOverlayAugment/FullSizeOverlayAugment.svelte";
  import { type GroupedChatItem } from "../../utils/message-list-context";
  import TurnSummary from "../turn-summary/TurnSummary.svelte";
  import { memoryModel } from "../../models/memory-model";

  const chatModel = getChatModel();
  export let group: GroupedChatItem | undefined;

  const { pendingMemoriesCount } = memoryModel;

  $: allNodes = flattenGroupIntoNodes(group);
  $: strReplaceNodes = allNodes.filter(isStrReplaceToolNode);

  $: conversationMemoryCount = allNodes.filter(isMemoryNode).length;
  $: conversationToolUseCount = allNodes.filter(
    (node) => node.type === ChatResultNodeType.TOOL_USE,
  ).length;
  $: conversationStrReplaceFiles = countUniquePaths(strReplaceNodes);
  $: conversationViewFiles = countUniquePaths(allNodes.filter(isViewToolNode));

  function getLabel(count: number, singular: string, plural: string): string {
    return count === 1 ? singular : plural;
  }

  $: entries = [
    {
      label: getLabel($pendingMemoriesCount, "Pending Memory", "Pending Memories"),
      value: $pendingMemoriesCount,
      icon: "inventory_2",
      callback: () => navigateToMemoryTab(),
    },
    {
      label: getLabel(conversationMemoryCount, "Memory Created", "Memories Created"),
      value: conversationMemoryCount,
      icon: "inventory",
    },
    {
      label: getLabel(conversationStrReplaceFiles, "File Changed", "Files Changed"),
      value: conversationStrReplaceFiles,
      icon: "difference",
    },
    {
      label: getLabel(conversationViewFiles, "File Examined", "Files Examined"),
      value: conversationViewFiles,
      icon: "document_search",
    },
    {
      label: getLabel(conversationToolUseCount, "Tool Used", "Tools Used"),
      value: conversationToolUseCount,
      icon: "design_services",
    },
    // TODO: add in memories retrieved in when parsable
  ];

  // Hide the component if there are no memories to show
  $: shouldShowComponent = entries.some((entry) => entry.value > 0);

  let memoryModal: FullSizeOverlayAugment;
  function navigateToMemoryTab() {
    memoryModal.showModal();
  }
</script>

{#if shouldShowComponent}
  <FullSizeOverlayAugment bind:this={memoryModal}>
    <MemoryModalContent {chatModel} />
  </FullSizeOverlayAugment>

  {#key entries.reduce((sum, entry) => sum + entry.value, 0)}
    <TurnSummary title="Turn Summary" {entries} isCardOpen={false} />
  {/key}
{/if}
