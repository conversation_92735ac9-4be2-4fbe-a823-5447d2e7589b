<script lang="ts">
  import BaseToolComponent from "../BaseToolComponent.svelte";
  import ArchiveIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/box-archive.svg?component";
  import ToolUseHeader from "../ToolUseHeader.svelte";
  import {
    stringOrDefault,
    type ToolUseState,
  } from "$common-webviews/src/apps/chat/types/tool-use-state";
  import { getContext } from "svelte";
  import { type ChatModel } from "$common-webviews/src/apps/chat/models/chat-model";
  import MemoryContent from "$common-webviews/src/apps/chat/components/MemoryAugment/MemoryContent.svelte";

  export let toolUseState: ToolUseState;
  export let toolUseInput: Record<string, unknown> = {};

  const chatModel = getContext<ChatModel>("chatModel");
  $: flagsModel = chatModel?.flags;
</script>

{#if flagsModel?.enableMemoryRetrieval}
  <BaseToolComponent>
    <ToolUseHeader
      slot="header"
      toolName="Remember"
      formattedToolArgs={[stringOrDefault(toolUseInput.memory, "")]}
    >
      <ArchiveIcon slot="icon" />
    </ToolUseHeader>
    <!-- Hide the header because already showed in the tool -->
    <MemoryContent
      slot="details"
      memoryData={{
        // TODO: memories request ID not available in tool use state, get it if we need it
        memoriesRequestId: "",
        memory: stringOrDefault(toolUseInput.memory, ""),
        isFlushed: true,
      }}
      requestId={toolUseState.requestId}
    />
  </BaseToolComponent>
{:else}
  <BaseToolComponent>
    <ToolUseHeader
      slot="header"
      toolName="Remember"
      formattedToolArgs={[stringOrDefault(toolUseInput.memory, "")]}
    >
      <ArchiveIcon slot="icon" />
    </ToolUseHeader>
  </BaseToolComponent>
{/if}
