<script lang="ts">
  import BaseToolComponent from "../BaseToolComponent.svelte";
  import ToolUseHeader from "../ToolUseHeader.svelte";
  import ZoomInIcon from "$common-webviews/src/design-system/icons/zoom-in.svelte";
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import { getToolUseContext } from "../../tool-context";
  import { ToolUsePhase } from "$common-webviews/src/apps/chat/types/tool-use-state";

  const ctx = getToolUseContext();

  function getStartLine(toolUseInput: Record<string, unknown>): number | undefined {
    return typeof toolUseInput.start_line === "number" ? toolUseInput.start_line : undefined;
  }

  function getEndLine(toolUseInput: Record<string, unknown>): number | undefined {
    return typeof toolUseInput.end_line === "number" ? toolUseInput.end_line : undefined;
  }

  function getToolTypeFromResult(resultText: string | undefined): string | undefined {
    if (!resultText) return undefined;
    const match = resultText.match(/<!-- toolType: ([^>]+) -->/);
    return match ? match[1] : undefined;
  }

  function isTerminalTool(toolType: string | undefined): boolean {
    if (!toolType) return false;
    const terminalTools = [
      "launch-process",
      "kill-process",
      "read-process",
      "write-process",
      "list-processes",
      "wait-process",
    ];
    return terminalTools.includes(toolType);
  }

  function getDisplayText(toolType: string | undefined): string {
    if (isTerminalTool(toolType)) {
      return "View Terminal Output";
    }
    if (toolType === "web-fetch") {
      return "View Web Page";
    }
    return "View Full Output";
  }

  // Extract total lines from the tool result if available
  function getTotalLinesFromResult(resultText: string | undefined): number | undefined {
    if (!resultText) return undefined;

    // Look for patterns like "lines X-Y of Z total lines" in the result
    const match = resultText.match(/lines \d+-\d+ of (\d+) total lines/);
    if (match) {
      return parseInt(match[1], 10);
    }

    // Alternative pattern: "Viewing lines X-Y (Z total lines)"
    const altMatch = resultText.match(/Viewing lines \d+-\d+ \((\d+) total lines\)/);
    if (altMatch) {
      return parseInt(altMatch[1], 10);
    }

    return undefined;
  }
</script>

<BaseToolComponent>
  <ToolUseHeader
    slot="header"
    toolName={getDisplayText(getToolTypeFromResult($ctx.toolUseState.result?.text))}
  >
    <ZoomInIcon slot="icon" />
    <span slot="secondary">
      <TextAugment size={1} class="c-view-range-untruncated__secondary">
        {#if $ctx.toolUseState.phase <= ToolUsePhase.running}
          {isTerminalTool(getToolTypeFromResult($ctx.toolUseState.result?.text))
            ? "Reading terminal output range..."
            : "Reading full output range..."}
        {:else if getStartLine($ctx.toolUseInput) && getEndLine($ctx.toolUseInput)}
          {@const startLine = getStartLine($ctx.toolUseInput)}
          {@const endLine = getEndLine($ctx.toolUseInput)}
          {@const totalLines = getTotalLinesFromResult($ctx.toolUseState.result?.text)}
          <span class="range-info">
            View lines {startLine}-{endLine}
            {#if totalLines}
              <span class="total-info">of {totalLines} total lines</span>
            {/if}
          </span>
        {:else}
          {isTerminalTool(getToolTypeFromResult($ctx.toolUseState.result?.text))
            ? "View terminal output range"
            : "View full output range"}
        {/if}
      </TextAugment>
    </span>
  </ToolUseHeader>
</BaseToolComponent>

<style>
  :global(.c-view-range-untruncated__secondary.c-view-range-untruncated__secondary) {
    display: flex;
    align-items: center;
  }

  .range-info {
    display: flex;
    align-items: center;
    gap: var(--ds-spacing-1);
  }

  .total-info {
    color: var(--ds-color-neutral-600);
  }
</style>
