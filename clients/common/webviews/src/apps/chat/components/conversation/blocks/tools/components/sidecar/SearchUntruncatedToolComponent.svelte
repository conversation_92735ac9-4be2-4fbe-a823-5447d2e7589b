<script lang="ts">
  import BaseToolComponent from "../BaseToolComponent.svelte";
  import ToolUseHeader from "../ToolUseHeader.svelte";
  import MagnifyingGlassIcon from "$common-webviews/src/design-system/icons/magnifying-glass.svelte";
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import { getToolUseContext } from "../../tool-context";
  import { ToolUsePhase } from "$common-webviews/src/apps/chat/types/tool-use-state";

  const ctx = getToolUseContext();

  function getSearchTerm(toolUseInput: Record<string, unknown>): string | undefined {
    return typeof toolUseInput.search_term === "string" ? toolUseInput.search_term : undefined;
  }

  function getContextLines(toolUseInput: Record<string, unknown>): number {
    return typeof toolUseInput.context_lines === "number" ? toolUseInput.context_lines : 2;
  }

  function getToolTypeFromResult(resultText: string | undefined): string | undefined {
    if (!resultText) return undefined;
    const match = resultText.match(/<!-- toolType: ([^>]+) -->/);
    return match ? match[1] : undefined;
  }

  function isTerminalTool(toolType: string | undefined): boolean {
    if (!toolType) return false;
    const terminalTools = [
      "launch-process",
      "kill-process",
      "read-process",
      "write-process",
      "list-processes",
      "wait-process",
    ];
    return terminalTools.includes(toolType);
  }

  function getDisplayText(toolType: string | undefined): string {
    if (isTerminalTool(toolType)) {
      return "Search Terminal Output";
    }
    if (toolType === "web-fetch") {
      return "Search Web Page";
    }
    return "Search Full Output";
  }
</script>

<BaseToolComponent>
  <ToolUseHeader
    slot="header"
    toolName={getDisplayText(getToolTypeFromResult($ctx.toolUseState.result?.text))}
  >
    <MagnifyingGlassIcon slot="icon" />
    <span slot="secondary">
      <TextAugment size={1} class="c-search-untruncated__secondary">
        {#if $ctx.toolUseState.phase <= ToolUsePhase.running}
          {isTerminalTool(getToolTypeFromResult($ctx.toolUseState.result?.text))
            ? "Searching terminal output..."
            : "Searching full output..."}
        {:else if getSearchTerm($ctx.toolUseInput)}
          <span class="search-info">
            Pattern <code>{getSearchTerm($ctx.toolUseInput)}</code>
            {#if getContextLines($ctx.toolUseInput) !== 2}
              <span class="context-info">({getContextLines($ctx.toolUseInput)} context lines)</span>
            {/if}
          </span>
        {:else}
          {isTerminalTool(getToolTypeFromResult($ctx.toolUseState.result?.text))
            ? "Search terminal output"
            : "Search full output"}
        {/if}
      </TextAugment>
    </span>
  </ToolUseHeader>
</BaseToolComponent>

<style>
  :global(.c-search-untruncated__secondary.c-search-untruncated__secondary) {
    display: flex;
    align-items: center;
    white-space: nowrap;
  }

  .search-info {
    display: flex;
    align-items: center;
    gap: var(--ds-spacing-1);
  }

  code {
    font-family: var(--ds-font-family-mono);
    font-size: 0.9em;
    background-color: var(--ds-color-neutral-100);
    padding: 0 var(--ds-spacing-0-5);
    border-radius: var(--ds-radius-1);
  }

  .context-info {
    font-size: 0.85em;
    color: var(--ds-color-neutral-600);
  }
</style>
