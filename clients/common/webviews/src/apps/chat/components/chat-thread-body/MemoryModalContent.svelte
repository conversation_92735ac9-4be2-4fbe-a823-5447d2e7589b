<script lang="ts">
  import MemoryBlock from "../MemoryAugment/MemoryBlock.svelte";
  import { setChatModel } from "../../chat-context";
  import type { ChatModel } from "../../models/chat-model";
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import IconButtonAugment from "$common-webviews/src/design-system/components/IconButtonAugment.svelte";
  import XmarkIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/xmark.svg?component";
  import { getFullSizeOverlayContext } from "$common-webviews/src/design-system/components/FullSizeOverlayAugment/context";
  import { memoryModel } from "../../models/memory-model";

  // Accept chatModel as a prop
  export let chatModel: ChatModel | undefined = undefined;

  const { pendingMemoriesCount } = memoryModel;
  const title = `${$pendingMemoriesCount} memories pending review`;

  // If we have a chat model, set it in context for child components
  $: if (chatModel) {
    setChatModel(chatModel);
  }

  const overlayContext = getFullSizeOverlayContext();
  function handleClose() {
    overlayContext.close();
  }
</script>

<div class="c-memory-modal__header">
  <TextAugment size={2} color="primary" class="c-memory-modal__title">
    {title}
  </TextAugment>
  <IconButtonAugment
    size={1}
    variant="ghost-block"
    color="neutral"
    title="Close modal"
    on:click={handleClose}
  >
    <XmarkIcon />
  </IconButtonAugment>
</div>
<div class="c-memory-modal__content">
  {#if chatModel}
    <MemoryBlock onClose={handleClose} />
  {:else}
    <div class="c-memory-modal__error">Memory feature is not available in this context.</div>
  {/if}
</div>

<style>
  .c-memory-modal__content {
    padding: var(--ds-spacing-4);
    height: 100%;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
  }

  .c-memory-modal__error {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: var(--ds-color-text-secondary);
    font-size: var(--ds-font-size-2);
  }

  .c-memory-modal__header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--ds-spacing-4);
    border-bottom: 1px solid var(--ds-color-neutral-6);
    flex-shrink: 0;
    gap: var(--ds-spacing-4);
  }
</style>
