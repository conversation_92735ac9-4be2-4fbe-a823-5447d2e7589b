<script lang="ts">
  import { getContext, onD<PERSON>roy } from "svelte";
  import {
    TabsAugment,
    TabListAugment,
    TabAugment,
    TabPanelAugment,
  } from "$common-webviews/src/design-system/components/TabsAugment";
  import { ChatThreadBodyTab, ChatThreadBodyModel } from "./chat-thread-body-model";
  import AgentEditListTab from "./AgentEditListTab.svelte";
  import GitAgentEditTab from "../git-agent-edit/GitAgentEditTab.svelte";
  import TaskListTab from "./TaskListTab.svelte";
  import MessageHistoryTab from "./MessageHistoryTab.svelte";
  import type { ChatModel } from "../../models/chat-model";
  import type { OnboardingWorkspaceModel } from "../../models/onboarding-workspace-model";
  import { RemoteAgentsModel } from "../../../remote-agent-manager/models/remote-agents-model";
  import { MultiRepoGitModel } from "../git-agent-edit/context/multi-repo";
  import { get } from "svelte/store";

  import {
    CurrentConversationTaskStore,
    type ICurrentConversationTaskStore,
  } from "../../models/task-store";
  import type { CheckpointStore } from "../../models/checkpoint-store";
  import FilePlusMinusIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/file-plus-minus.svg?component";
  import ListCheckIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/list-check.svg?component";
  import AgentIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/wand-magic-sparkles.svg?component";
  import BadgeAugment from "$common-webviews/src/design-system/components/BadgeAugment";
  import type { BadgeColor } from "$common-webviews/src/design-system/components/BadgeAugment/badge-types";
  import NumberFlow, { NumberFlowGroup } from "@number-flow/svelte";

  // Get required context models
  const chatModel = getContext<ChatModel>("chatModel");
  const onboardingWorkspaceModel = getContext<OnboardingWorkspaceModel>("onboardingWorkspaceModel");
  const remoteAgentsModel = getContext<RemoteAgentsModel>(RemoteAgentsModel.key);

  const taskStore = getContext<ICurrentConversationTaskStore>(CurrentConversationTaskStore.key);
  const checkpointStore = getContext<CheckpointStore>("checkpointStore");
  const { taskProgress } = taskStore;
  const { targetCheckpointSummary } = checkpointStore;

  // Get the thread body model from context (created at Chat level)
  const threadBodyModel = ChatThreadBodyModel.getFromContext();
  const { activeTab, availableTabs, shouldShowTabs } = threadBodyModel;

  // Get git context for change statistics
  const gitContext = MultiRepoGitModel.getContext();
  $: multiRepoModel = gitContext?.model;
  $: repositories = multiRepoModel?.repositories;

  // Badge calculations (derived from checkpoint store)
  $: filesWithEdits = ($targetCheckpointSummary || []).filter(
    (file) =>
      file.changesSummary &&
      (file.changesSummary.totalAddedLines > 0 || file.changesSummary.totalRemovedLines > 0),
  );
  $: agentEditsCount = filesWithEdits.length;
  $: taskProgressData = $taskProgress;
  $: taskCount = taskProgressData ? taskProgressData.total : 0;
  $: taskBadgeColor = (
    taskProgressData && taskProgressData.completed === taskProgressData.total ? "success" : "accent"
  ) as BadgeColor;

  // Git changes statistics for tab chips - just total files edited
  $: gitChangesStats = (() => {
    if (!$repositories) return { totalFiles: 0 };

    const allFiles = new Set<string>();

    // Aggregate across all repositories
    for (const [repoPath, repoModel] of $repositories) {
      const fileCollections = repoModel.fileCollectionsModel;
      if (!fileCollections) continue;

      const allFilesStore = fileCollections.allFiles;

      // Get current values from stores - use allFiles which includes untracked
      const allRepoFiles = allFilesStore ? get(allFilesStore) : [];

      // Add all file paths to set for unique count
      allRepoFiles.forEach(file => {
        allFiles.add(`${repoPath}:${file.qualifiedPathName.relPath}`);
      });
    }

    return {
      totalFiles: allFiles.size
    };
  })();

  $: hasGitChanges = gitChangesStats.totalFiles > 0;

  // Handle tab change events
  function handleTabChange(event: CustomEvent<{ value: string }>) {
    const newTab = event.detail.value as ChatThreadBodyTab;
    threadBodyModel.setActiveTab(newTab);
  }

  // Add global keyboard listener for shortcuts
  onDestroy(() => threadBodyModel.destroy());
</script>

<!-- Chat Thread Body - manages the central panel with tabs -->
{#if $shouldShowTabs}
  <div class="c-chat-thread-body">
    <TabsAugment
      value={$activeTab}
      size={1}
      color="neutral"
      scrollable
      hideLabelOnNarrow
      on:change={handleTabChange}
    >
      <!-- Tab Panels -->
      {#if $activeTab === ChatThreadBodyTab.messageHistory}
        <TabPanelAugment value={ChatThreadBodyTab.messageHistory}>
          <MessageHistoryTab {chatModel} {onboardingWorkspaceModel} {remoteAgentsModel} />
        </TabPanelAugment>
      {/if}

      {#if $activeTab === ChatThreadBodyTab.tasks}
        <TabPanelAugment value={ChatThreadBodyTab.tasks}>
          <TaskListTab />
        </TabPanelAugment>
      {/if}

      {#if $activeTab === ChatThreadBodyTab.agentEdits}
        <TabPanelAugment value={ChatThreadBodyTab.agentEdits}>
          <AgentEditListTab />
        </TabPanelAugment>
      {/if}

      {#if $activeTab === ChatThreadBodyTab.agentGitEdits}
        <TabPanelAugment value={ChatThreadBodyTab.agentGitEdits}>
          <GitAgentEditTab />
        </TabPanelAugment>
      {/if}

      <!-- A bit of a hack, but we need the divider in the tab panel -->
      {#if $$slots["chat-divider"]}
        <div class="c-chat-thread-body__divider">
          <slot name="chat-divider" />
        </div>
      {/if}

      <!-- Tab List - positioned at the bottom -->
      <TabListAugment outline showSlidingBackground expandToFit>
        {#if $availableTabs.includes(ChatThreadBodyTab.messageHistory)}
          <TabAugment value={ChatThreadBodyTab.messageHistory}>
            <AgentIcon slot="leftIcon" />
            Agent
          </TabAugment>
        {/if}

        {#if $availableTabs.includes(ChatThreadBodyTab.tasks)}
          <TabAugment value={ChatThreadBodyTab.tasks}>
            <ListCheckIcon slot="leftIcon" />
            Tasks
            <svelte:fragment slot="rightIcon">
              {#if taskCount > 0}
                <BadgeAugment.Root color={taskBadgeColor} variant="soft" size={0}>
                  <div class="c-chat-thread-body__badge">
                    <NumberFlowGroup>
                      <NumberFlow value={taskProgressData.completed} willChange />
                      <span>/</span>
                      <NumberFlow value={taskProgressData.total} willChange />
                    </NumberFlowGroup>
                  </div>
                </BadgeAugment.Root>
              {/if}
            </svelte:fragment>
          </TabAugment>
        {/if}

        {#if $availableTabs.includes(ChatThreadBodyTab.agentEdits)}
          <TabAugment value={ChatThreadBodyTab.agentEdits}>
            <FilePlusMinusIcon slot="leftIcon" />
            Edits
            <svelte:fragment slot="rightIcon">
              {#if agentEditsCount > 0}
                <BadgeAugment.Root color="accent" variant="soft" size={0}>
                  <div class="c-chat-thread-body__badge">
                    <NumberFlow value={agentEditsCount} willChange />
                  </div>
                </BadgeAugment.Root>
              {/if}
            </svelte:fragment>
          </TabAugment>
        {/if}

        {#if $availableTabs.includes(ChatThreadBodyTab.agentGitEdits)}
          <TabAugment value={ChatThreadBodyTab.agentGitEdits}>
            <FilePlusMinusIcon slot="leftIcon" />
            Changes
            <svelte:fragment slot="rightIcon">
              {#if hasGitChanges}
                <!-- Files count chip -->
                <BadgeAugment.Root inset color="neutral" variant="soft" size={1}>
                  {gitChangesStats.totalFiles}
                </BadgeAugment.Root>
              {/if}
            </svelte:fragment>
          </TabAugment>
        {/if}
      </TabListAugment>
    </TabsAugment>
  </div>
{/if}

<style>
  .c-chat-thread-body {
    /* Take up all available space */
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;

    & .c-chat-thread-body__divider {
      padding-top: var(--ds-spacing-1);
    }
  }

  /* Override TabsAugment default layout to position tabs at bottom */
  .c-chat-thread-body :global(.ds-tabs) {
    display: flex;
    flex-direction: column;
    height: 100%;
  }

  /* Tab list container - positioned at bottom with spacing */
  .c-chat-thread-body :global(.ds-tab-list-container) {
    flex-shrink: 0;
    margin-top: var(--ds-spacing-1);
  }
  
  .c-chat-thread-body__badge {
    display: flex;
    align-items: center;
    gap: 1px;
    height: var(--ds-font-size-1);
  }
</style>
