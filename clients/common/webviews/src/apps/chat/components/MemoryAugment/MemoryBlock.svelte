<script lang="ts">
  import ButtonAugment from "$common-webviews/src/design-system/components/ButtonAugment.svelte";
  import BackIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/arrow-left.svg?component";
  import { memoryModel } from "../../models/memory-model";
  import SaveCard from "./SaveCard.svelte";

  export let onClose: () => void;

  // Subscribe to memory model stores
  const { memories, isLoading, hasMemories } = memoryModel;
</script>

<div class="c-memory-dropdown__content">
  {#if $isLoading}
    <div class="c-memory-dropdown__loading">Loading memories...</div>
  {:else if !$hasMemories}
    <div class="c-memory-dropdown__empty">No memories found</div>
  {:else}
    <div class="c-memory-dropdown__list">
      {#each $memories as memory (memory.id)}
        <SaveCard {memory} />
      {/each}
    </div>
  {/if}

  <!-- Back to Agent button -->
  <ButtonAugment size={1} variant="ghost" color="neutral" on:click={onClose}>
    <BackIcon slot="iconLeft" />
    Back to Agent Thread
  </ButtonAugment>
</div>

<style>
  .c-memory-dropdown__content :global(svg) {
    --icon-size: var(--ds-icon-size-0);
    --button-icon-size: var(--icon-size);
    flex-shrink: 0;
  }

  .c-memory-dropdown__content {
    display: flex;
    flex-direction: column;
    gap: var(--ds-spacing-2);
  }

  .c-memory-dropdown__list {
    display: flex;
    flex-direction: column;
    gap: var(--ds-spacing-2);
  }

  .c-memory-dropdown__loading,
  .c-memory-dropdown__empty {
    padding: var(--ds-spacing-4);
  }
</style>
