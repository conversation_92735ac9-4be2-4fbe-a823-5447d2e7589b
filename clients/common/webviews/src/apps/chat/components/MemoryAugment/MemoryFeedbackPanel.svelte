<script lang="ts">
  import FeedbackPanel from "../feedback/MemoryFeedbackPanel.svelte";
  import { MemoryUtils, type AgentMemoryData } from "../../models/memory-utils";
  import { type ChatModel } from "../../models/chat-model";
  import { ChatMode } from "@augment-internal/sidecar-libs/src/chat/chat-types";

  export let memoryData: AgentMemoryData;
  export let chatModel: ChatModel;
  export let requestId: string;
</script>

<div class="memory-meta">
  <FeedbackPanel
    {requestId}
    reponseText={memoryData.memory}
    feedbackState={undefined}
    onSendUserRating={async (requestId, rating, note) => {
      const noteOrEmpty = note || "";
      const noteWithVersion = `${noteOrEmpty}\n\nMemory version: ${MemoryUtils.getMemoryVersionFromFlags(chatModel)}`;
      // Include the created memory in the note when sending user rating (only if debug features are enabled)
      const noteWithMemory = `${noteWithVersion}\n\nCreated memory: ${memoryData.memory}`;
      const feedbackNote = $chatModel.flags.enableDebugFeatures ? noteWithMemory : noteWithVersion;

      await $chatModel.extensionClient.sendUserRating(
        requestId,
        ChatMode.memories,
        rating,
        feedbackNote,
      );
    }}
  />
</div>

<style>
  .memory-meta {
    display: flex;
    justify-content: flex-end;
  }
</style>
