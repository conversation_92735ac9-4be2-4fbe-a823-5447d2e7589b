<script lang="ts">
  import { tick } from "svelte";
  import ButtonAugment from "$common-webviews/src/design-system/components/ButtonAugment.svelte";
  import TextAreaAugment from "$common-webviews/src/design-system/components/TextAreaAugment.svelte";
  import Save from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/floppy-disk.svg?component";
  import Edit from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/pen-to-square.svg?component";
  import Discard from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/trash-can.svg?component";

  export let memoryContent: string;
  export let allowEditing: boolean = false;
  export let handleDiscard: () => void = () => {};
  export let handleSave: (scope: "user" | "repo") => void = () => {};
  export let handleUpdate: (editedContent: string) => void = () => {};

  // Local state for editing
  let isEditing = false;
  let editedContent = memoryContent;
  let textareaElement: HTMLTextAreaElement | undefined = undefined;

  async function startEdit() {
    isEditing = true;

    // Wait for DOM update and then focus the textarea
    await tick();
    textareaElement?.focus();
  }

  async function saveEdit() {
    try {
      handleUpdate(editedContent);
      cancelEdit();
    } catch (error) {
      console.error("Failed to save edited memory:", error);
    }
  }

  function cancelEdit() {
    isEditing = false;
  }
</script>

<div class="c-memory-save-card">
  {#if isEditing}
    <TextAreaAugment
      bind:value={editedContent}
      bind:textInput={textareaElement}
      on:keydown={(e) => {
        if (e.key === "Enter" && !e.shiftKey && !e.ctrlKey && !e.metaKey) {
          e.preventDefault();
          saveEdit();
        }
        if (e.key === "Escape") {
          e.preventDefault();
          cancelEdit();
        }
      }}
      class="c-memory-save-card__edit-textarea"
      rows={3}
    />
    <div class="c-memory-save-card__actions">
      <ButtonAugment size={1} color="accent" variant="solid" on:click={saveEdit}>Save</ButtonAugment
      >
      <ButtonAugment size={1} color="neutral" variant="solid" on:click={cancelEdit}
        >Cancel</ButtonAugment
      >
    </div>
  {:else}
    <slot />
    {#if allowEditing}
      <div class="c-memory-save-card__actions">
        <ButtonAugment
          size={1}
          variant="ghost"
          title="Save memory"
          on:click={() => handleSave("user")}
        >
          <Save slot="iconLeft" />
          Save
        </ButtonAugment>
        <ButtonAugment size={1} variant="ghost" title="Edit memory" on:click={startEdit}>
          <Edit slot="iconLeft" />
          Edit
        </ButtonAugment>
        <ButtonAugment size={1} variant="ghost" title="Discard memory" on:click={handleDiscard}>
          <Discard slot="iconLeft" />
          Discard
        </ButtonAugment>
      </div>
    {/if}
  {/if}
</div>

<style>
  .c-memory-save-card {
    display: flex;
    flex-direction: column;
    padding: var(--ds-spacing-2);
    gap: var(--ds-spacing-2);
  }

  .c-memory-save-card__actions {
    display: flex;
    gap: var(--ds-spacing-2);
  }
</style>
