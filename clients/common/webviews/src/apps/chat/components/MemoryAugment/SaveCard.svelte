<script lang="ts">
  import type { MemoryInfoWithState } from "@augment-internal/sidecar-libs/src/webview-messages/message-types/memory-messages";
  import { getChatModel } from "../../chat-context";
  import {
    AgentSessionEventName,
    MemoryAction,
    MemoryActionTrigger,
    type MemoryUsageData,
  } from "@augment-internal/sidecar-libs/src/metrics/types";
  import MemoryEditableContent from "./MemoryEditableContent.svelte";
  import MemoryContent from "./MemoryContent.svelte";
  import MemoryCollapsible from "./MemoryCollapsible.svelte";
  import { MemoryUtils } from "../../models/memory-utils";
  import { ChatMode } from "@augment-internal/sidecar-libs/src/chat";
  import { FeedbackRating } from "$vscode/src/types/feedback-rating";
  import { memoryModel } from "../../models/memory-model";

  export let memory: MemoryInfoWithState;

  const chatModel = getChatModel();
  $: memoryWithMetadata = {
    memoriesRequestId: "",
    memory: memory.content,
    isFlushed: true,
  };

  async function handleSave(_: "user" | "repo") {
    try {
      // TODO: saving to user vs. repo is not supported right now
      await memoryModel.updateMemoryState(memory.id, "user_accepted");

      // Emit memory metrics
      const memoryUsageData: MemoryUsageData = {
        action: MemoryAction.saveMemory,
        memoryId: memory.id,
        triggeredBy: MemoryActionTrigger.user,
        memoryState: "user_accepted",
        memoryVersion: memory.version,
      };
      onSendFeedback(FeedbackRating.positive);

      chatModel.currentConversationModel.extensionClient.reportAgentSessionEvent({
        eventName: AgentSessionEventName.memoryUsage,
        conversationId: chatModel.currentConversationModel.id,
        eventData: {
          memoryUsageData,
        },
      });
    } catch (error) {
      console.error("Failed to save memory:", error);
    }
  }

  async function handleDiscard() {
    try {
      await memoryModel.updateMemoryState(memory.id, "user_rejected");

      // Emit memory metrics
      const memoryUsageData: MemoryUsageData = {
        action: MemoryAction.discardMemory,
        memoryId: memory.id,
        triggeredBy: MemoryActionTrigger.user,
        memoryState: "user_rejected",
        memoryVersion: memory.version,
      };

      onSendFeedback(FeedbackRating.negative);

      chatModel.currentConversationModel.extensionClient.reportAgentSessionEvent({
        eventName: AgentSessionEventName.memoryUsage,
        conversationId: chatModel.currentConversationModel.id,
        eventData: {
          memoryUsageData,
        },
      });
    } catch (error) {
      console.error("Failed to discard memory:", error);
    }
  }

  async function handleUpdate(editedContent: string) {
    await memoryModel.updateMemoryState(memory.id, "pending", editedContent);

    // Emit memory metrics
    const memoryUsageData: MemoryUsageData = {
      action: MemoryAction.editMemory,
      memoryId: memory.id,
      triggeredBy: MemoryActionTrigger.user,
      memoryState: "user_edited",
      memoryVersion: memory.version,
    };

    chatModel.currentConversationModel.extensionClient.reportAgentSessionEvent({
      eventName: AgentSessionEventName.memoryUsage,
      conversationId: chatModel.currentConversationModel.id,
      eventData: {
        memoryUsageData,
      },
    });
  }

  const onSendFeedback = async (rating: FeedbackRating) => {
    const noteWithVersion = `\nMemory version: ${MemoryUtils.getMemoryVersionFromFlags(chatModel)}`;
    // Include the created memory in the note when sending user rating (only if debug features are enabled)
    const noteWithMemory = `${noteWithVersion}\n\nCreated memory: ${memory.content}`;
    const feedbackNote = $chatModel.flags.enableDebugFeatures ? noteWithMemory : noteWithVersion;

    await $chatModel.extensionClient.sendUserRating(
      memory.requestId || "",
      ChatMode.memories,
      rating,
      feedbackNote,
    );
  };
</script>

<MemoryCollapsible memoryContent={memory.content} headerText="Save Memory">
  <MemoryEditableContent
    memoryContent={memory.content}
    allowEditing
    {handleDiscard}
    {handleSave}
    {handleUpdate}
  >
    <MemoryContent memoryData={memoryWithMetadata} requestId={memory.requestId} />
  </MemoryEditableContent>
</MemoryCollapsible>
