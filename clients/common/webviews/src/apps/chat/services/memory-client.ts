import { AsyncMsgSender } from "$common-webviews/src/common/hosts/async-messaging";
import { host } from "$common-webviews/src/common/hosts/host";
import {
  MemoryWebViewMessageType,
  type GetMemoriesByStateMessage,
  type GetMemoriesByStateResponse,
  type UpdateMemoryStateMessage,
  type UpdateMemoryStateResponse,
  type MemoryInfoWithState,
  type MemoryState,
} from "@augment-internal/sidecar-libs/src/webview-messages/message-types/memory-messages";

/**
 * Client service for memory operations using sidecar messages
 */
export class MemoryClient {
  /* eslint-disable-next-line @typescript-eslint/naming-convention */
  private static readonly TIMEOUT_MS = 5000; // 5 seconds
  private _asyncMsgSender: AsyncMsgSender;

  constructor() {
    this._asyncMsgSender = new AsyncMsgSender(host.postMessage);
  }

  /**
   * Get memories filtered by state and optionally by version
   */
  async getMemoriesByState(state: MemoryState, version?: string): Promise<MemoryInfoWithState[]> {
    const request: GetMemoriesByStateMessage = {
      type: MemoryWebViewMessageType.getMemoriesByState,
      data: { state, version },
    };

    const response = await this._asyncMsgSender.sendToSidecar<
      GetMemoriesByStateMessage,
      GetMemoriesByStateResponse
    >(request, MemoryClient.TIMEOUT_MS);

    return response.data.memories;
  }

  /**
   * Update a memory's state
   */
  async updateMemoryState(
    memoryId: string,
    newState: MemoryState,
    editedContent?: string,
  ): Promise<void> {
    const request: UpdateMemoryStateMessage = {
      type: MemoryWebViewMessageType.updateMemoryState,
      data: {
        memoryId,
        newState,
        editedContent,
      },
    };

    const response = await this._asyncMsgSender.sendToSidecar<
      UpdateMemoryStateMessage,
      UpdateMemoryStateResponse
    >(request, MemoryClient.TIMEOUT_MS);

    if (!response.data.success) {
      throw new Error(response.data.error || "Failed to update memory state");
    }
  }
}

// Singleton instance
let memoryClient: MemoryClient | undefined;

/**
 * Get the singleton memory client instance
 */
export function getMemoryClient(): MemoryClient {
  if (!memoryClient) {
    memoryClient = new MemoryClient();
  }
  return memoryClient;
}
