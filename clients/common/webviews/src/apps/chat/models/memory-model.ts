import { writable, derived, type Readable, type Writable } from "svelte/store";
import type {
  MemoryInfoWithState,
  MemoryState,
} from "@augment-internal/sidecar-libs/src/webview-messages/message-types/memory-messages";
import { getMemoryClient } from "../services/memory-client";
import { memoryStore } from "./memory-store";

export type VersionValueType = string | undefined;
export type StateValueType = MemoryState | undefined;

/**
 * Memory model that manages shared memory state across components
 * Implements the singleton pattern to ensure only one instance exists
 */
export class MemoryModel {
  /* eslint-disable-next-line @typescript-eslint/naming-convention */
  POLLING_INTERVAL_MS = 3000;
  private _memoryClient = getMemoryClient();
  private _refreshTimer: NodeJS.Timeout | null = null;
  private _messageHandler: ((event: MessageEvent) => void) | null = null;
  private _memoryStoreUnsubscribe: (() => void) | null = null;

  // Core state stores
  private _memories: Writable<MemoryInfoWithState[]> = writable([]);
  private _isLoading: Writable<boolean> = writable(false);
  private _isRefreshing: Writable<boolean> = writable(false);
  private _selectedState: Writable<StateValueType> = writable(undefined);
  private _error: Writable<string | null> = writable(null);

  // Derived stores
  public readonly memories: Readable<MemoryInfoWithState[]>;
  public readonly isLoading: Readable<boolean>;
  public readonly isRefreshing: Readable<boolean>;
  public readonly error: Readable<string | null>;
  public readonly hasMemories: Readable<boolean>;
  public readonly pendingMemoriesCount: Readable<number>;

  // Singleton instance reference
  private static _instance: MemoryModel | null = null;

  private constructor() {
    // Determine default values using the same logic as MemoryBlock.svelte
    const defaultState = "pending";

    // Initialize state
    this._selectedState.set(defaultState);

    // Create derived stores
    this.memories = derived(this._memories, ($memories) => $memories);
    this.isLoading = derived(this._isLoading, ($isLoading) => $isLoading);
    this.isRefreshing = derived(this._isRefreshing, ($isRefreshing) => $isRefreshing);
    this.error = derived(this._error, ($error) => $error);
    this.hasMemories = derived(this._memories, ($memories) => $memories.length > 0);
    this.pendingMemoriesCount = derived(this._memories, ($memories) => $memories.length);

    // Load initial memories
    this.loadMemories();

    // Subscribe to memory update notifications from sidecar to auto-refresh list
    this._memoryStoreUnsubscribe = memoryStore.subscribe((notification) => {
      if (notification) {
        void this.refreshMemories();
      }
    });
  }

  /**
   * Load memories based on current filters
   */
  public async loadMemories(): Promise<void> {
    this._isLoading.set(true);
    this._error.set(null);

    try {
      const memoriesData = await this._memoryClient.getMemoriesByState("pending");
      this._memories.set(memoriesData);
    } catch (error) {
      console.error("Failed to load memories:", error);
      this._error.set(error instanceof Error ? error.message : "Failed to load memories");
    } finally {
      this._isLoading.set(false);
    }
  }

  /**
   * Refresh memories (same as load but with different loading state)
   */
  public async refreshMemories(): Promise<void> {
    this._isRefreshing.set(true);
    this._error.set(null);

    try {
      const memoriesData = await this._memoryClient.getMemoriesByState("pending");
      this._memories.set(memoriesData);
    } catch (error) {
      console.error("Failed to refresh memories:", error);
      this._error.set(error instanceof Error ? error.message : "Failed to refresh memories");
    } finally {
      this._isRefreshing.set(false);
    }
  }

  /**
   * Update memory state
   */
  public async updateMemoryState(
    memoryId: string,
    newState: MemoryState,
    editedContent?: string,
  ): Promise<void> {
    try {
      await this._memoryClient.updateMemoryState(memoryId, newState, editedContent);
      // Refresh memories after successful update
      await this.refreshMemories();
    } catch (error) {
      console.error("Failed to update memory state:", error);
      this._error.set(error instanceof Error ? error.message : "Failed to update memory state");
      throw error;
    }
  }

  /**
   * Clean up resources
   */
  public destroy(): void {
    if (this._refreshTimer) {
      clearInterval(this._refreshTimer);
      this._refreshTimer = null;
    }

    if (this._messageHandler) {
      window.removeEventListener("message", this._messageHandler);
      this._messageHandler = null;
    }

    if (this._memoryStoreUnsubscribe) {
      this._memoryStoreUnsubscribe();
      this._memoryStoreUnsubscribe = null;
    }
  }

  /**
   * Get the singleton instance of MemoryModel
   * @returns The singleton instance
   */
  public static getInstance(): MemoryModel {
    if (!MemoryModel._instance) {
      MemoryModel._instance = new MemoryModel();
    }
    return MemoryModel._instance;
  }

  /**
   * Reset the singleton instance (primarily for testing)
   * @internal
   */
  public static _resetInstance(): void {
    if (MemoryModel._instance) {
      MemoryModel._instance.destroy();
      MemoryModel._instance = null;
    }
  }
}

// Export the singleton instance using the getter
export const memoryModel = MemoryModel.getInstance();
