import { type ChatModel } from "./chat-model";
import { type IExtensionClient } from "../extension-client";
import { type ConversationModel } from "./conversation-model";
import { type RemoteAgentsModel } from "../../remote-agent-manager/models/remote-agents-model";
import { ToolsWebviewModelV1 } from "./tools-webview-model-v1";
import { ToolsWebviewModelV2 } from "./tools-webview-model-v2";
import { type Exchange } from "@augment-internal/sidecar-libs/src/chat/chat-types";
import { type IDisposable } from "monaco-editor";
import { type SoundModel } from "../../settings/models/sound-model";

export class ToolsWebviewModel implements IDisposable {
  private _toolsWebviewModelV1: ToolsWebviewModelV1;
  private _toolsWebviewModelV2: ToolsWebviewModelV2 | undefined;
  private _currToolsWebviewModel: ToolsWebviewModelV1 | ToolsWebviewModelV2;
  private _onToolSkippedCallbacks: Array<() => void> = [];

  constructor(
    private _conversationModel: ConversationModel,
    private _extensionClient: IExtensionClient,
    private _chatModel: ChatModel,
    private _soundModel: SoundModel,
    private _remoteAgentsModel?: RemoteAgentsModel,
  ) {
    this._toolsWebviewModelV1 = new ToolsWebviewModelV1(
      this._conversationModel,
      this._extensionClient,
      this._chatModel,
      this._soundModel,
      this._remoteAgentsModel,
    );
    this._toolsWebviewModelV2 = undefined;
    this._currToolsWebviewModel = this._toolsWebviewModelV1;
  }

  public addOnToolSkippedCallback = (callback: () => void) => {
    this._onToolSkippedCallbacks.push(callback);
  };

  public switchToV2 = () => {
    if (this._toolsWebviewModelV2 === undefined) {
      this._currToolsWebviewModel.dispose();
      this._toolsWebviewModelV1.dispose();
      this._toolsWebviewModelV2 = new ToolsWebviewModelV2(
        this._conversationModel,
        this._extensionClient,
        this._chatModel,
        this._soundModel,
        this._remoteAgentsModel,
      );
    }
    this._currToolsWebviewModel = this._toolsWebviewModelV2;
  };

  public approveTool = async (
    requestId: string,
    toolUseId: string,
    toolName: string,
    toolInput: any,
    chatHistory: Exchange[],
    conversationId: string,
  ) => {
    return this._currToolsWebviewModel.approveTool(
      requestId,
      toolUseId,
      toolName,
      toolInput,
      chatHistory,
      conversationId,
    );
  };

  public callTool = async (
    requestId: string,
    toolUseId: string,
    toolName: string,
    toolInput: any,
    chatHistory: Exchange[],
    conversationId: string,
  ) => {
    return this._currToolsWebviewModel.callTool(
      requestId,
      toolUseId,
      toolName,
      toolInput,
      chatHistory,
      conversationId,
    );
  };

  public cancelToolRun = async (requestId: string, toolUseId: string) => {
    return this._currToolsWebviewModel.cancelToolRun(requestId, toolUseId);
  };

  public skipToolRun = async (requestId: string, toolUseId: string) => {
    // Mark that the agent was interrupted by user skipping a tool
    this._onToolSkippedCallbacks.forEach((callback) => callback());
    return this._currToolsWebviewModel.skipToolRun(requestId, toolUseId);
  };

  public cancelActiveToolRun = async () => {
    return this._currToolsWebviewModel.cancelActiveToolRun();
  };

  public interruptAllTools = async () => {
    return this._currToolsWebviewModel.interruptAllTools();
  };

  public interruptToolsConversation = async () => {
    return this._currToolsWebviewModel.interruptToolsConversation();
  };

  public allToolsUsesResolved = (requestId: string) => {
    return this._currToolsWebviewModel.allToolUsesResolved(requestId);
  };

  public dispose = () => {
    this._currToolsWebviewModel.dispose();
    this._toolsWebviewModelV1.dispose();
    if (this._toolsWebviewModelV2) {
      this._toolsWebviewModelV2.dispose();
    }
  };
}
