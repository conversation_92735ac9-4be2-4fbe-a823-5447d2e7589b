<script lang="ts">
  import type { ConfigBlock } from "$common-webviews/src/apps/settings/models/tool-config-model";
  import ToolCategory from "./ToolCategory.svelte";
  import MCPCategory from "./MCPCategory.svelte";
  import TerminalCategory from "./TerminalCategory.svelte";
  import SoundCategory from "./SoundCategory.svelte";
  import AgentCategory from "./AgentCategory.svelte";
  import type { MCPServer } from "$vscode/src/webview-providers/webview-messages";
  import type { ShellConfig } from "@augment-internal/sidecar-libs/src/tools/tool-types";
  import type { OnToolApprovalConfigChange } from "../components/types";

  export let tools: ConfigBlock[] = [];
  export let isMCPEnabled = true;
  export let isMCPImportEnabled = true;
  export let isTerminalEnabled = true;
  export let isSoundCategoryEnabled = false;
  export let isAgentCategoryEnabled = false;
  export let isSwarmModeFeatureFlagEnabled = false;
  export let hasEverUsedRemoteAgent = false;
  export let onAuthenticate: (url: string) => void;
  export let onRevokeAccess: (config: ConfigBlock) => void;
  export let onToolApprovalConfigChange: OnToolApprovalConfigChange = () => {};
  export let onMCPServerAdd: (server: Omit<MCPServer, "id">) => void;

  export let onMCPServerSave: (server: MCPServer) => void;
  export let onMCPServerDelete: (server: MCPServer) => void;
  export let onMCPServerToggleDisable: (server: string) => void;
  export let onMCPServerJSONImport: (jsonString: string) => Promise<number>;
  export let onCancel: (() => void) | undefined = undefined;
  export let supportedShells: ShellConfig[] = [];
  export let selectedShell: string | undefined = undefined;
  export let startupScript: string | undefined = undefined;
  export let onShellSelect: (shell: string) => void = () => {};
  export let onStartupScriptChange: (script: string) => void = () => {};
</script>

<div class="c-settings-tools">
  <ToolCategory
    title="Services"
    {tools}
    {onAuthenticate}
    {onRevokeAccess}
    {onToolApprovalConfigChange}
  />
  {#if isMCPEnabled}
    <MCPCategory
      {onMCPServerAdd}
      {onMCPServerSave}
      {onMCPServerDelete}
      {onMCPServerToggleDisable}
      {onMCPServerJSONImport}
      {onCancel}
      {isMCPImportEnabled}
    />
  {/if}
  {#if isTerminalEnabled}
    <TerminalCategory
      {supportedShells}
      {selectedShell}
      {startupScript}
      {onShellSelect}
      {onStartupScriptChange}
    />
  {/if}
  {#if isSoundCategoryEnabled}
    <SoundCategory />
  {/if}
  {#if isAgentCategoryEnabled}
    <AgentCategory isSwarmModeEnabled={isSwarmModeFeatureFlagEnabled} {hasEverUsedRemoteAgent} />
  {/if}
</div>

<style>
  .c-settings-tools {
    display: flex;
    flex-direction: column;
    gap: var(--ds-spacing-3);
  }
</style>
