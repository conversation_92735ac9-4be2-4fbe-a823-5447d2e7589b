<script lang="ts">
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import MCPServerCard from "../components/MCPServerCard.svelte";
  import Plus from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/plus.svg?component";
  import Download from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/download.svg?component";
  import type { MCPServer } from "$vscode/src/webview-providers/webview-messages";
  import ButtonAugment from "$common-webviews/src/design-system/components/ButtonAugment.svelte";
  import { type MCPCardModeType } from "../components/types";
  import EasyMCPInstall from "../components/EasyMCPInstall.svelte";

  import { clientStrings } from "$common-webviews/src/utils/strings/client-strings";
  import { ToolConfigModel } from "../models/tool-config-model";
  import { getContext } from "svelte";
  import { getMCPServerModelContext } from "../models/mcp-server-model-context";
  import { MCPServerModel } from "../models/mcp-server-model";
  import { filterOutPartnerMCPTools } from "../components/partner-mcp-utils";

  export let onMCPServerAdd: (server: Omit<MCPServer, "id">) => unknown;

  export let onMCPServerSave: (server: MCPServer) => unknown;
  export let onMCPServerDelete: (server: MCPServer) => unknown;
  export let onMCPServerToggleDisable: (server: string) => unknown;
  export let onCancel: (() => unknown) | undefined = undefined;
  export let onMCPServerJSONImport: (jsonString: string) => Promise<number>;
  export let isMCPImportEnabled = true;

  // Import the extension client context for MCP OAuth flow
  import { getExtensionClientContext } from "$common-webviews/src/apps/chat/extension-client-context";
  import type { MCPServerHttp } from "$vscode/src/webview-providers/webview-messages";

  const extensionClient = getExtensionClientContext();

  // Create a specialized onAuthenticate handler for MCP servers
  function handleMCPAuthenticate(server: MCPServerHttp) {
    extensionClient.startRemoteMCPAuth(server.name);
  }

  let editServerId: string | null = null;
  let mode: MCPCardModeType | null = null;
  $: isAddFormDisabled =
    mode === "add" || mode === "addJson" || mode === "addRemote" || editServerId !== null;

  function cancelEdit() {
    editServerId = null;
    mode = null;
    onCancel?.();
  }

  let servers: MCPServer[] = [];

  const toolConfigModel = getContext<ToolConfigModel>(ToolConfigModel.key);
  const mcpServerModel = getMCPServerModelContext();

  const enableNativeRemoteMcp = toolConfigModel.getEnableNativeRemoteMcp();
  const allServers = mcpServerModel.getServers();

  $: {
    if (enableNativeRemoteMcp) {
      servers = filterOutPartnerMCPTools($allServers);
    } else {
      servers = $allServers;
    }
  }

  function onEdit(server: MCPServer) {
    editServerId = server.id;
  }
  function wrap<T extends (...args: any[]) => any>(fn: T) {
    return async function wrapped(...args: Parameters<T>) {
      const ret = await fn(...args);
      mode = null;
      editServerId = null;
      return ret;
    };
  }
  const onAdd = wrap(onMCPServerAdd);
  const onSave = wrap(onMCPServerSave);
  const onJSONImport = wrap(onMCPServerJSONImport);
  const onDelete = wrap(onMCPServerDelete);
  const onToggleDisableServer = wrap(onMCPServerToggleDisable);

  const mcpDocsURL = clientStrings.get("mcpDocsURL");
  $: serverValidationMessages = MCPServerModel.parseServerValidationMessages(servers);
</script>

<div class="mcp-servers">
  <div class="section-heading">
    <TextAugment size={1} weight="regular" color="secondary">
      <div class="section-heading-text">MCP</div>
    </TextAugment>
  </div>
  <div class="description-text">
    Configure a new Model Context Protocol server to connect Augment to custom tools. Find out more
    about MCP <a href={mcpDocsURL}>in the docs</a>.
  </div>
  <!-- Server List -->
  <EasyMCPInstall onMCPServerAdd={onAdd} {servers} />
  {#each servers as server (server.id)}
    <MCPServerCard
      mode={editServerId === server.id ? "edit" : "view"}
      {server}
      {onAdd}
      {onSave}
      {onDelete}
      {onToggleDisableServer}
      {onEdit}
      onCancel={cancelEdit}
      {onJSONImport}
      onAuthenticate={handleMCPAuthenticate}
      disabledText={serverValidationMessages.errors.get(server.id)}
      warningText={serverValidationMessages.warnings.get(server.id)}
    />
  {/each}
</div>
<!-- Add Server Form -->
{#if mode === "add" || mode === "addJson" || mode === "addRemote"}
  <MCPServerCard
    {mode}
    {onAdd}
    {onSave}
    {onDelete}
    {onToggleDisableServer}
    {onEdit}
    onCancel={cancelEdit}
    {onJSONImport}
    onAuthenticate={handleMCPAuthenticate}
  />
{/if}
<!-- Add MCP button -->
<div class="add-mcp-button-container">
  <ButtonAugment
    disabled={isAddFormDisabled}
    color="neutral"
    variant="soft"
    size={1}
    on:click={() => {
      mode = "add";
    }}
  >
    <Plus slot="iconLeft" />
    Add MCP
  </ButtonAugment>

  <ButtonAugment
    disabled={isAddFormDisabled}
    color="neutral"
    variant="soft"
    size={1}
    on:click={() => {
      mode = "addRemote";
    }}
  >
    <Plus slot="iconLeft" />
    Add remote MCP
  </ButtonAugment>

  {#if isMCPImportEnabled}
    <ButtonAugment
      disabled={isAddFormDisabled}
      color="neutral"
      variant="soft"
      size={1}
      title="Add MCP from JSON"
      on:click={() => {
        mode = "addJson";
      }}
    >
      <Download slot="iconLeft" />
      Import from JSON
    </ButtonAugment>
  {/if}
</div>

<style>
  .add-mcp-button-container :global(svg) {
    width: var(--ds-icon-size-1);
    height: var(--ds-icon-size-1);
    opacity: 0.8;
  }
  .mcp-servers {
    display: flex;
    flex-direction: column;
    gap: var(--ds-spacing-2);
  }
  .description-text {
    padding-bottom: var(--ds-spacing-2);
  }

  .section-heading {
    color: var(--ds-text);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex: 1;
    padding: var(--ds-spacing-1) 0;
  }

  .add-mcp-button-container {
    display: flex;
    flex-wrap: wrap;
    gap: var(--ds-spacing-2);
    margin-bottom: var(--ds-spacing-2);
  }

  .add-mcp-button-container :global(.c-button) {
    flex: 1 1 auto;
    min-width: 160px;
  }
</style>
