<script lang="ts">
  import ButtonAugment from "$common-webviews/src/design-system/components/ButtonAugment.svelte";
  import IconButtonAugment from "$common-webviews/src/design-system/components/IconButtonAugment.svelte";

  import SpinnerAugment from "$common-webviews/src/design-system/components/SpinnerAugment.svelte";

  import SettingsCard from "./SettingsCard.svelte";
  import BadgeAugment from "$common-webviews/src/design-system/components/BadgeAugment";
  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";
  import { TooltipTriggerOn } from "$common-webviews/src/design-system/_primitives/TooltipAugment/types";
  import Disconnect from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/disconnect.svg?component";

  import { type ConfigBlock } from "../models/tool-config-model";

  import { type MCPServer } from "$vscode/src/webview-providers/webview-messages";
  import { addPartnerMCPDisplayDetails } from "./partner-mcp-utils";

  import { getExtensionClientContext } from "$common-webviews/src/apps/chat/extension-client-context";
  import { getMCPServerModelContext } from "../models/mcp-server-model-context";

  export let config: ConfigBlock;
  export let mcpTool: MCPServer | undefined;
  const mcpServerModel = getMCPServerModelContext();
  const extensionClient = getExtensionClientContext();

  $: config = addPartnerMCPDisplayDetails(config);
  async function handleMcpOAuthAuthenticate() {
    if (isConnecting) {
      // Clear previous timeout and reset state
      if (timeoutId) {
        clearTimeout(timeoutId);
        timeoutId = null;
      }
      isConnecting = false;
      return;
    }

    // Generate OAuth URL using the MCP OAuth flow
    extensionClient.startRemoteMCPAuth(config.name);
    isConnecting = true;

    // Reset connecting state after a timeout (60 seconds) using Promise.race
    const MINUTE = 60 * 1000;
    const timeoutPromise = new Promise<void>((resolve) => {
      timeoutId = setTimeout(() => {
        resolve();
        timeoutId = null;
      }, MINUTE);
    });

    await Promise.race([timeoutPromise]);
    isConnecting = false;
  }

  async function onRevokeAccess() {
    const confirmed = await extensionClient.openConfirmationModal({
      title: "Revoke Access",
      message: `Are you sure you want to revoke access for ${config.displayName}? This will disconnect the tool and you'll need to reconnect it to use it again.`,
      confirmButtonText: "Revoke Access",
      cancelButtonText: "Cancel",
    });

    if (!confirmed) return;

    if (mcpTool) mcpServerModel.deleteServer(mcpTool);
    isConnecting = false;
  }

  let isConnecting = false;
  let disconnectButtonActive = false;
  let timeoutId: ReturnType<typeof setTimeout> | null = null;

  $: config.isConfigured = Boolean(mcpTool);
</script>

<div
  class="config-wrapper"
  role="group"
  aria-label="Connection status controls"
  on:mouseenter={() => (disconnectButtonActive = true)}
  on:mouseleave={() => (disconnectButtonActive = false)}
>
  <SettingsCard icon={config.icon} title={config.displayName}>
    <div slot="header-right">
      {#if !config.isConfigured}
        <ButtonAugment
          variant="ghost-block"
          color={isConnecting ? "neutral" : "accent"}
          size={1}
          on:click={handleMcpOAuthAuthenticate}
        >
          <div class="connect-button-content">
            {#if isConnecting}
              <div class="connect-button-spinner">
                <SpinnerAugment size={1} useCurrentColor={true} />
              </div>
              <span>Cancel</span>
            {:else}
              <span>Connect</span>
            {/if}
          </div>
        </ButtonAugment>
      {:else if config.isConfigured}
        <div class="status-controls">
          <div class="disconnect-button" class:active={disconnectButtonActive}>
            <TextTooltipAugment triggerOn={[TooltipTriggerOn.Hover]} content="Revoke Access">
              <IconButtonAugment color="neutral" variant="ghost" size={1} on:click={onRevokeAccess}>
                <Disconnect />
              </IconButtonAugment>
            </TextTooltipAugment>
          </div>
          <BadgeAugment.Root color="success" size={1} variant="soft">Connected</BadgeAugment.Root>
        </div>
      {/if}
    </div>
  </SettingsCard>

  {#if config.showStatus}
    <div class="status-message {config.statusType}">
      {config.statusMessage}
    </div>
  {/if}
</div>

<style>
  .disconnect-button :global(svg) {
    fill: var(--icon-color, currentColor);
    height: 16px;
    aspect-ratio: 1/1;
  }
  .connect-button-content {
    display: flex;
    align-items: center;
    gap: var(--ds-spacing-2);
  }

  .connect-button-spinner {
    display: flex;
    align-items: center;
  }

  .status-controls {
    display: flex;
    gap: var(--ds-spacing-2);
    align-items: center;
  }

  .disconnect-button {
    opacity: 0;
    transition: opacity 0.2s ease;
    padding-right: var(--ds-spacing-2);
  }

  .status-controls:hover .disconnect-button,
  .disconnect-button.active {
    opacity: 1;
  }

  .status-message {
    margin: var(--ds-spacing-2) 0;
    padding: var(--ds-spacing-2);
    border-radius: var(--ds-radius-2);
  }

  .status-message.error {
    background-color: var(--ds-color-error-3);
    color: var(--ds-color-error-11);
  }

  .status-message.info {
    background-color: var(--ds-color-info-3);
    color: var(--ds-color-info-11);
  }
</style>
