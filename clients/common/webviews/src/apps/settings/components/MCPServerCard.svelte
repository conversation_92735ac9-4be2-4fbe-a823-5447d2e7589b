<script lang="ts">
  import { isMCPServerError, MCPServerError, MCPServerModel } from "../models/mcp-server-model";
  import type {
    MCPServer,
    MCPServerStdio,
    MCPServerHttp,
  } from "$vscode/src/webview-providers/webview-messages";
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import ButtonAugment from "$common-webviews/src/design-system/components/ButtonAugment.svelte";
  import IconButtonAugment from "$common-webviews/src/design-system/components/IconButtonAugment.svelte";
  import TextFieldAugment from "$common-webviews/src/design-system/components/TextFieldAugment.svelte";
  import DropdownMenu from "$common-webviews/src/design-system/components/DropdownMenuAugment";
  import McpIcon from "$common-webviews/src/design-system/icons/mcp-logo.svelte";
  import CalloutAugment from "$common-webviews/src/design-system/components/CalloutAugment.svelte";
  import CircleXMark from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/circle-xmark.svg?component";
  import McpServerEnvironmentVariables from "./MCPServerEnvironmentVariables.svelte";
  import DotsHorizontalIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/ellipsis.svg?component";
  import TrashCan from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/trash-can.svg?component";
  import PenToSquare from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/pen-to-square.svg?component";
  import SettingsCard from "./SettingsCard.svelte";
  import TextAreaAugment from "$common-webviews/src/design-system/components/TextAreaAugment.svelte";
  import type { MCPCardModeType, MCPEnvVarEntry } from "./types";
  import ClipboardCopy from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/copy.svg?component";
  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";
  import ToggleAugment from "$common-webviews/src/design-system/components/ToggleAugment.svelte";
  import { isVSCodeHost } from "$common-webviews/src/common/hosts/vscode/vscode";

  // Import utility functions
  import {
    isStdioServer,
    isStreamingServer,
    getCommandOrUrl,
  } from "@augment-internal/sidecar-libs/src/tools/mcp-server-utils";
  import CollapsibleAugment from "$common-webviews/src/design-system/components/CollapsibleAugment/CollapsibleAugment.svelte";
  import ChevronDown from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/chevron-down.svg?component";
  import ChevronRight from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/chevron-right.svg?component";

  // Helper types for creating new servers
  type MCPServerStdioWithoutId = Omit<MCPServerStdio, "id">;
  type MCPServerHttpWithoutId = Omit<MCPServerHttp, "id">;

  // Props
  export let server: MCPServer | null = null;
  export let onDelete: (server: MCPServer) => unknown;
  export let onAdd: (server: Omit<MCPServer, "id">) => unknown;
  export let onSave: (server: MCPServer) => unknown;
  export let onEdit: (server: MCPServer) => unknown;
  export let onToggleDisableServer: (id: string) => unknown;
  export let onJSONImport: (jsonString: string) => Promise<number>;
  export let onCancel: () => unknown;
  export let onAuthenticate: ((server: MCPServerHttp) => unknown) | undefined = undefined;
  export let disabledText: string | undefined = undefined;
  export let warningText: string | undefined = undefined;

  export let mode: MCPCardModeType = "view";
  export let mcpServerError = "";

  $: serverTools = server?.tools ?? [];

  // Track when server was last toggled to avoid showing auth required immediately after restart
  let lastToggleTime = 0;
  let connectionGracePeriodMs = 3000; // 3 seconds grace period after server restart

  // Authentication state helpers
  $: isHttpServer = isStreamingServer(server);
  $: isWithinGracePeriod = Date.now() - lastToggleTime < connectionGracePeriodMs;
  $: authRequired =
    !server?.disabled &&
    isHttpServer &&
    (server as MCPServerHttp).authRequired === true &&
    serverTools.length === 0 &&
    !isWithinGracePeriod;

  let name = server?.name ?? "";
  let command = isStreamingServer(server) ? "" : isStdioServer(server) ? server.command : "";
  let url = isStreamingServer(server) ? server.url : "";
  let envVars: Record<string, string> = isStdioServer(server) ? (server.env ?? {}) : {};
  let jsonInput = "";
  // This is only meaningful for remote MCPs.
  let remoteType: "http" | "sse" = isStreamingServer(server) ? server.type : "http";

  // Local form state for environment variables that is bound/accessed in this component
  let envVarEntries: MCPEnvVarEntry[] = [];
  setLocalEnvVarFormState(); // initialize local form state

  // State for expanding/collapsing tools list
  let collapseTools = true;

  /*
   * Updates the local form state for environment variables with updated values.
   * with unique IDs to prevent reordering
   */
  export function setLocalEnvVarFormState() {
    envVarEntries = Object.entries(envVars).map(([key, value]) => ({
      id: crypto.randomUUID(),
      key,
      value,
    }));
  }

  // For dropdown menu
  let requestClose: () => void = () => {};
  $: {
    if (name && command) {
      mcpServerError = "";
    }
  }

  $: isFormInvalid =
    (mode === "add" && (!name.trim() || !command.trim())) ||
    (mode === "addRemote" && (!name.trim() || !url.trim()));
  $: isJsonInputInvalid = mode === "addJson" && !jsonInput.trim();
  $: isButtonDisabled = isFormInvalid || mode === "view" || isJsonInputInvalid;

  $: headerText = (() => {
    switch (mode) {
      case "add":
        return "New MCP Server";
      case "addRemote":
        return "New Remote MCP Server";
      case "addJson":
        return "Import MCP Server";
      default:
        return "Edit MCP Server";
    }
  })();

  function handleEnterEditMode() {
    if (server && mode === "view") {
      mode = "edit";
      onEdit(server);
      requestClose();
    }
  }

  export let busy = false;

  // Basic validation for env vars: filter out empty entries
  function validateEnvVars({ key, value }: MCPEnvVarEntry) {
    return key.trim() && value.trim();
  }

  function onCopyJSON() {
    if (server) {
      const jsonText = MCPServerModel.convertServerToJSON(server);
      navigator.clipboard.writeText(jsonText);
    }
  }

  async function onCommit() {
    // Clear the error first
    mcpServerError = "";
    busy = true;
    // Get the updated env vars from the child component to be saved and sanitize them
    // and then save to local form state
    const validEnvVarEntries = envVarEntries.filter(validateEnvVars);
    envVars = Object.fromEntries(
      validEnvVarEntries.map(({ key, value }) => [key.trim(), value.trim()]),
    );
    setLocalEnvVarFormState();
    try {
      if (mode === "add") {
        const newServer: MCPServerStdioWithoutId = {
          type: "stdio",
          name: name.trim(),
          command: command.trim(),
          arguments: "", // Keep empty for backward compatibility
          useShellInterpolation: true, // New servers use shell interpolation
          env: Object.keys(envVars).length > 0 ? envVars : undefined,
        };
        await onAdd(newServer);
      } else if (mode === "addRemote") {
        const newServer: MCPServerHttpWithoutId = {
          type: remoteType,
          name: name.trim(),
          url: url.trim(),
        };
        await onAdd(newServer);
      } else if (mode === "addJson") {
        try {
          JSON.parse(jsonInput);
        } catch (jsonError) {
          // Handle JSON parsing errors specifically
          const errorMessage = jsonError instanceof Error ? jsonError.message : String(jsonError);
          throw new MCPServerError(`Invalid JSON format: ${errorMessage}`);
        }
        await onJSONImport(jsonInput);
      } else if (mode === "edit" && server) {
        if (isStreamingServer(server)) {
          const updatedServer: MCPServerHttp = {
            ...server,
            type: remoteType,
            name: name.trim(),
            url: url.trim(),
          };
          await onSave(updatedServer);
        } else if (isStdioServer(server)) {
          const updatedServer: MCPServerStdio = {
            ...server,
            name: name.trim(),
            command: command.trim(),
            arguments: "", // Keep empty for backward compatibility
            env: Object.keys(envVars).length > 0 ? envVars : undefined,
          };
          await onSave(updatedServer);
        }
      }
    } catch (e) {
      mcpServerError = isMCPServerError(e) ? e.message : "Failed to save server";
      console.warn(e);
    } finally {
      busy = false;
    }
  }
  function handleCancel() {
    busy = false;
    mcpServerError = "";
    onCancel?.();
    // Reset the form state to original values
    jsonInput = "";
    name = server?.name ?? "";
    command = isStreamingServer(server) ? "" : isStdioServer(server) ? server.command : "";
    url = isStreamingServer(server) ? server.url : "";
    envVars = isStdioServer(server) ? (server.env ? { ...server.env } : {}) : {};
    remoteType = isStreamingServer(server) ? server.type : "http";
    setLocalEnvVarFormState();
  }
</script>

{@debug server, serverTools}
<!-- Collapsed single line view of MCP server name + command -->
{#if mode === "view" && server}
  <CollapsibleAugment bind:collapsed={collapseTools}>
    <SettingsCard slot="header">
      <div slot="header-left" class="l-header">
        {#if serverTools.length > 0}
          <IconButtonAugment
            size={1}
            variant="ghost"
            on:click={() => (collapseTools = !collapseTools)}
          >
            {#if collapseTools}
              <ChevronRight />
            {:else}
              <ChevronDown />
            {/if}
          </IconButtonAugment>
        {/if}
        <TextTooltipAugment
          content={disabledText || (authRequired ? "Authentication required" : warningText)}
        >
          <div
            class="c-dot"
            class:c-green={!disabledText && !authRequired}
            class:c-warning={authRequired || (!disabledText && !!warningText)}
            class:c-red={!!disabledText && !authRequired}
            class:c-disabled={server.disabled}
          ></div>
        </TextTooltipAugment>
        <TextTooltipAugment content={server.name} side="top" align="start">
          <div class="server-name">
            <TextAugment size={1} weight="medium">
              {server.name}
              {#if serverTools.length > 0}
                ({serverTools.length}) tools
              {/if}
            </TextAugment>
          </div>
        </TextTooltipAugment>

        <!-- Tools dropdown button next to server name -->
        <div class="command-text">
          <TextTooltipAugment content={getCommandOrUrl(server)} side="top" align="start">
            <TextAugment color="secondary" size={1} weight="regular">
              {getCommandOrUrl(server)}
            </TextAugment>
          </TextTooltipAugment>
        </div>
      </div>
      <div class="server-actions" slot="header-right">
        {#if authRequired}
          <ButtonAugment
            size={1}
            variant="surface"
            color="warning"
            on:click={() => {
              onAuthenticate?.(server as MCPServerHttp);
            }}
          >
            Authenticate
          </ButtonAugment>
        {/if}
        <div class="status-controls">
          {#if isVSCodeHost()}
            <ToggleAugment
              size={1}
              checked={!server.disabled}
              on:change={() => {
                if (server) {
                  // Set the toggle time to start grace period
                  lastToggleTime = Date.now();
                  onToggleDisableServer(server.id);
                }
                requestClose();
              }}
            />
          {/if}
          <DropdownMenu.Root bind:requestClose>
            <DropdownMenu.Trigger>
              <IconButtonAugment size={1} variant="ghost-block" color="neutral">
                <DotsHorizontalIcon />
              </IconButtonAugment>
            </DropdownMenu.Trigger>
            <DropdownMenu.Content side="bottom" align="end">
              <DropdownMenu.Item onSelect={handleEnterEditMode}>
                <div class="status-controls-button">
                  <PenToSquare />
                  <TextAugment size={1} weight="medium">Edit</TextAugment>
                </div>
              </DropdownMenu.Item>
              <DropdownMenu.Item
                onSelect={() => {
                  onCopyJSON();
                  requestClose();
                }}
              >
                <div class="status-controls-button">
                  <ClipboardCopy />
                  <TextAugment size={1} weight="medium">Copy JSON</TextAugment>
                </div>
              </DropdownMenu.Item>

              <DropdownMenu.Item
                color="error"
                onSelect={() => {
                  onDelete(server);
                  requestClose();
                }}
              >
                <div class="status-controls-button">
                  <TrashCan />
                  <TextAugment size={1} weight="medium">Delete</TextAugment>
                </div>
              </DropdownMenu.Item>
            </DropdownMenu.Content>
          </DropdownMenu.Root>
        </div>
      </div>
    </SettingsCard>
    <!-- Collapsible tools list -->
    <div slot="footer">
      {#each serverTools as tool}
        <div class="c-tool-item">
          <div class="c-tool-info">
            <div class="tool-status">
              <div
                class="tool-status-dot"
                class:enabled={tool.enabled}
                class:disabled={!tool.enabled}
              ></div>
              <TextAugment size={1} weight="medium"
                >{tool.definition.mcp_tool_name || tool.definition.name}</TextAugment
              >
            </div>
            <div class="c-tool-description">
              <TextTooltipAugment content={tool.definition.description} align="start">
                {#if tool.definition.description}
                  <TextAugment size={1} color="secondary">{tool.definition.description}</TextAugment
                  >
                {/if}
              </TextTooltipAugment>
            </div>
          </div>
        </div>
      {/each}
    </div>
  </CollapsibleAugment>
{:else}
  <form
    class="c-mcp-server-card {mode === 'add' || mode === 'addJson' || mode === 'addRemote'
      ? 'add-server-section'
      : 'server-item'}"
    on:submit|preventDefault={onCommit}
  >
    <div class="server-edit-form">
      <div class="server-header">
        <div class="server-title">
          <div class="server-icon">
            <McpIcon />
          </div>
          <TextAugment color="secondary" size={1} weight="medium">{headerText}</TextAugment>
        </div>
      </div>

      {#if mode === "addJson"}
        <div class="form-row">
          <div class="input-field">
            <TextAugment size={1} weight="medium">Code Snippet</TextAugment>
          </div>
        </div>
        <div class="form-row">
          <div class="input-field">
            <TextAreaAugment bind:value={jsonInput} size={1} placeholder="Paste JSON here..." />
          </div>
        </div>
      {:else if mode === "add" || mode === "addRemote" || mode === "edit"}
        {#if mode === "addRemote" || (mode === "edit" && (server?.type === "http" || server?.type === "sse"))}
          <div class="form-row">
            <div class="input-field">
              <TextAugment size={1} weight="medium">Connection Type</TextAugment>
              <div class="connection-type-buttons">
                <ButtonAugment
                  size={1}
                  variant={remoteType === "http" ? "solid" : "ghost"}
                  color={remoteType === "http" ? "accent" : "neutral"}
                  type="button"
                  on:click={() => (remoteType = "http")}
                >
                  HTTP
                </ButtonAugment>
                <ButtonAugment
                  size={1}
                  variant={remoteType === "sse" ? "solid" : "ghost"}
                  color={remoteType === "sse" ? "accent" : "neutral"}
                  type="button"
                  on:click={() => (remoteType = "sse")}
                >
                  SSE
                </ButtonAugment>
              </div>
            </div>
          </div>
        {/if}
        <div class="form-row">
          <div class="input-field">
            <TextFieldAugment
              size={1}
              bind:value={name}
              on:focus={handleEnterEditMode}
              placeholder="Enter a name for your MCP server (e.g., 'Server Memory')"
            >
              <TextAugment slot="label" size={1} weight="medium">Name</TextAugment>
            </TextFieldAugment>
          </div>
        </div>
        {#if mode === "addRemote" || server?.type === "http" || server?.type === "sse"}
          <div class="form-row">
            <div class="input-field">
              <TextFieldAugment
                size={1}
                bind:value={url}
                on:focus={handleEnterEditMode}
                placeholder="Enter the URL (e.g., 'https://api.example.com/mcp')"
              >
                <TextAugment slot="label" size={1} weight="medium">URL</TextAugment>
              </TextFieldAugment>
            </div>
          </div>
        {:else}
          <div class="form-row">
            <div class="input-field">
              <TextFieldAugment
                size={1}
                bind:value={command}
                on:focus={handleEnterEditMode}
                placeholder="Enter the MCP command (e.g., 'npx -y @modelcontextprotocol/server-memory')"
              >
                <TextAugment slot="label" size={1} weight="medium">Command</TextAugment>
              </TextFieldAugment>
            </div>
          </div>
        {/if}
      {/if}

      {#if (mode === "add" || mode === "edit") && !isStreamingServer(server)}
        <McpServerEnvironmentVariables bind:envVarEntries {handleEnterEditMode} />
      {/if}

      <div class="form-actions-row">
        <div class="error-container" class:is-error={!!mcpServerError}>
          <CalloutAugment variant="soft" color="error" size={1}>
            <CircleXMark slot="icon" />
            {mcpServerError}
          </CalloutAugment>
        </div>
        <div class="form-actions">
          <ButtonAugment
            size={1}
            variant="ghost"
            color="neutral"
            on:click={handleCancel}
            type="button"
          >
            Cancel
          </ButtonAugment>
          <ButtonAugment
            size={1}
            variant="solid"
            color="accent"
            loading={busy}
            type="submit"
            disabled={isButtonDisabled}
          >
            {#if mode === "addJson"}
              Import
            {:else if mode === "add"}
              Add
            {:else if mode === "addRemote"}
              Add
            {:else if mode === "edit"}
              Save
            {/if}
          </ButtonAugment>
        </div>
      </div>
    </div>
  </form>
{/if}

<style>
  .server-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--ds-spacing-3);
    background-color: var(--settings-section-background);
    border-radius: var(--ds-radius-2);
    margin-bottom: var(--ds-spacing-3);
  }

  .server-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--ds-spacing-3);
    padding-bottom: var(--ds-spacing-2);
  }

  .server-title {
    display: flex;
    align-items: center;
    gap: var(--ds-spacing-2);
  }

  .server-icon {
    display: flex;
    align-items: center;
  }

  .server-icon :global(svg) {
    width: 20px;
    height: 20px;
  }

  .server-actions {
    display: flex;
    gap: var(--ds-spacing-1);
  }

  .status-controls {
    display: flex;
    align-items: center;
    padding-left: var(--ds-spacing-2);
    gap: var(--ds-spacing-2);
  }

  .server-edit-form {
    width: 100%;
  }

  .add-server-section {
    padding: var(--ds-spacing-3);
    background-color: var(--settings-section-background);
    border-radius: var(--ds-radius-2);
  }

  .form-row {
    margin-bottom: var(--ds-spacing-3);
  }

  .input-field {
    display: flex;
    flex-direction: column;
    gap: var(--ds-spacing-1);
  }

  .form-actions {
    display: flex;
    justify-content: flex-end;
    gap: var(--ds-spacing-2);
  }

  .form-actions-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .error-container {
    flex-grow: 1;
    margin-right: var(--ds-spacing-2);
    opacity: 0;
    transition: opacity 0.2s ease;
  }
  .error-container.is-error {
    opacity: 1;
  }
  .status-controls :global(svg) {
    width: 14px;
    height: 14px;
    fill: var(--icon-color, currentColor);
  }
  .status-controls-button {
    display: flex;
    align-items: center;
    gap: var(--ds-spacing-2);
  }
  .l-header {
    display: flex;
    gap: var(--ds-spacing-1);
    flex-direction: row;
    align-items: center;
    justify-content: flex-start;
    flex: 1;
    min-width: 0;
  }
  .l-header :global(svg) {
    --icon-size: var(--ds-icon-size-0);
  }

  .c-green {
    background-color: var(--ds-color-success-a11);
  }
  .c-red {
    background-color: var(--ds-color-error-a11);
  }
  .c-warning {
    background-color: var(--ds-color-warning-a11);
  }
  .c-disabled {
    background-color: transparent;
    border: 1px solid var(--ds-color-neutral-a11);
  }
  .c-dot {
    margin-left: var(--ds-spacing-1);
    margin-right: var(--ds-spacing-1);
    width: 6px;
    height: 6px;
    border-radius: 50%;
  }

  .server-name {
    display: flex;
    align-items: center;
    gap: var(--ds-spacing-1);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .command-text {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .c-tool-item {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: var(--ds-spacing-2);
    background-color: var(--ds-color-neutral-a2);
    border-radius: var(--ds-radius-1);
    gap: var(--ds-spacing-2);
  }

  .c-tool-info {
    display: flex;
    flex-direction: column;
    gap: var(--ds-spacing-1);
    min-width: 0;
  }
  .c-tool-description {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .tool-status {
    display: flex;
    align-items: center;
    flex-shrink: 0;
    gap: var(--ds-spacing-2);
  }

  .tool-status-dot {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    margin-top: 2px;
  }

  .tool-status-dot.enabled {
    background-color: var(--ds-color-success-a11);
  }

  .tool-status-dot.disabled {
    background-color: var(--ds-color-error-a8);
  }

  .connection-type-buttons {
    display: flex;
    gap: var(--ds-spacing-1);
  }

  .connection-type-buttons :global(.c-button) {
    flex: 1;
  }
</style>
