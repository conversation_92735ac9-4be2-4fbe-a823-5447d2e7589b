import { get, writable, type Writable } from "svelte/store";
import type { HostInterface } from "$common-webviews/src/common/hosts/host-types";
import {
  type MCPServer,
  type MCPServerStdio,
  type WebViewMessage,
  WebViewMessageType,
} from "$vscode/src/webview-providers/webview-messages";
import type { MessageConsumer } from "$common-webviews/src/common/utils/message-broker";
import {
  isStreamingServer,
  parseServerConfigFromJSON as parseServerConfigFromJSONUtil,
  MCPServerError,
  type NormalizedMCPServer as ParsedNormalizedMCPServer,
} from "@augment-internal/sidecar-libs/src/tools/mcp-server-utils";

// Type definition for a normalized MCPServer without an ID (webview format)
type NormalizedMCPServer = Omit<MCPServer, "id">;

export function isMCPServerError(error: unknown): error is MCPServerError {
  return error instanceof MCPServerError;
}

// Re-export MCPServerError for backward compatibility
export { MCPServerError };

export class MCPServerModel implements MessageConsumer {
  private servers: Writable<MCPServer[]> = writable([]);

  constructor(private readonly host: HostInterface) {
    this.loadServersFromStorage();
  }

  handleMessageFromExtension(e: MessageEvent<WebViewMessage>): boolean {
    const message = e.data;
    if (message.type === WebViewMessageType.getStoredMCPServersResponse) {
      const storedServers: MCPServer[] = message.data;
      if (Array.isArray(storedServers)) {
        // Just set the servers without triggering a save
        this.servers.set(storedServers);
      }
      return true;
    }
    return false;
  }

  /**
   * Sends a request to import MCP servers from JSON
   * @param jsonString The JSON string containing MCP server configurations
   */
  async importServersFromJSON(jsonString: string): Promise<number> {
    return this.importFromJSON(jsonString);
  }

  private loadServersFromStorage() {
    try {
      // Request stored servers from the host
      this.host.postMessage({
        type: WebViewMessageType.getStoredMCPServers,
      });
    } catch (error) {
      console.error("Failed to load MCP servers:", error);
      // Initialize with empty array if loading fails
      this.servers.set([]);
    }
  }

  private saveServers(servers: MCPServer[]) {
    try {
      // Save servers to storage
      this.host.postMessage({
        type: WebViewMessageType.setStoredMCPServers,
        data: servers,
      });
    } catch (error) {
      console.error("Failed to save MCP servers:", error);
      throw new MCPServerError("Failed to save MCP servers");
    }
  }

  getServers() {
    return this.servers;
  }

  addServer(server: Omit<MCPServer, "id">) {
    this.checkExistingServerName(server.name);
    this.servers.update((servers) => {
      const newServer: MCPServer = {
        ...server,
        id: crypto.randomUUID(),
      } as MCPServer;

      const updatedServers = [...servers, newServer];
      // Save the updated servers
      this.saveServers(updatedServers);
      return updatedServers;
    });
  }

  addServers(serversToAdd: Omit<MCPServer, "id">[]) {
    // Check all server names first to avoid partial failures
    for (const server of serversToAdd) {
      this.checkExistingServerName(server.name);
    }

    this.servers.update((servers) => {
      const newServers = serversToAdd.map(
        (server): MCPServer =>
          ({
            ...server,
            id: crypto.randomUUID(),
          }) as MCPServer,
      );

      const updatedServers = [...servers, ...newServers];
      // Save the updated servers (this will trigger only one restart)
      this.saveServers(updatedServers);
      return updatedServers;
    });
  }

  checkExistingServerName(name: string, id?: string) {
    const existingServer = get(this.servers).find((server) => server.name === name);
    if (existingServer && existingServer?.id !== id) {
      throw new MCPServerError(`Server name '${name}' already exists`);
    }
  }

  updateServer(updatedServer: MCPServer) {
    this.checkExistingServerName(updatedServer.name, updatedServer.id);
    this.servers.update((servers) => {
      const updatedServers = servers.map((server) =>
        server.id === updatedServer.id ? updatedServer : server,
      );

      this.saveServers(updatedServers);
      return updatedServers;
    });
  }

  deleteServer(targetServer: MCPServer) {
    this.servers.update((servers) => {
      const newServers = servers.filter((server) => server.id !== targetServer.id);
      // Always save after deletion, even if the list is now empty
      this.saveServers(newServers);
      return newServers;
    });

    if (targetServer.type === "http" && targetServer.authRequired) {
      this.host.postMessage({
        type: WebViewMessageType.deleteOAuthSession,
        data: targetServer.name,
      });
    }
  }

  toggleDisabledServer(id: string) {
    this.servers.update((servers) => {
      const updatedServers = servers.map((server) => {
        if (server.id === id) {
          return {
            ...server,
            disabled: !server.disabled,
          };
        }
        return server;
      });

      this.saveServers(updatedServers);
      return updatedServers;
    });
  }

  static convertServerToJSON(server: MCPServer): string {
    if (isStreamingServer(server)) {
      return JSON.stringify(
        {
          mcpServers: {
            [server.name]: {
              url: server.url,
              type: server.type,
            },
          },
        },
        null,
        2,
      );
    } else {
      const stdioServer = server as MCPServerStdio;
      return JSON.stringify(
        {
          mcpServers: {
            [stdioServer.name]: {
              command: stdioServer.command.split(" ")[0],
              args: stdioServer.command.split(" ").slice(1),
              env: stdioServer.env,
            },
          },
        },
        null,
        2,
      );
    }
  }

  /*
   * Takes in an array of servers and returns a record of server IDs to error messages
   * For now the only error we check for is duplicate server names.
   */
  static parseServerValidationMessages(servers: MCPServer[]) {
    const serverErrorMap = new Map<string, string>();
    const serverWarningMap = new Map<string, string>();

    servers.forEach((server) => {
      const disabledTools = server.tools
        ?.filter((tool) => !tool.enabled)
        .map((tool) => tool.definition.mcp_tool_name);
      if (server.disabled) {
        serverErrorMap.set(server.id, "MCP server has been manually disabled");
      } else if (server.tools && server.tools.length === 0) {
        serverErrorMap.set(server.id, "No tools are available for this MCP server");
      } else if (disabledTools && disabledTools.length === server.tools?.length) {
        serverErrorMap.set(
          server.id,
          "All tools for this MCP server have validation errors: " + disabledTools.join(", "),
        );
      } else if (disabledTools && disabledTools.length > 0) {
        serverWarningMap.set(
          server.id,
          "MCP server has validation errors in the following tools which have been disabled: " +
            disabledTools.join(", "),
        );
      }
    });

    const duplicateServerMap = this.parseDuplicateServerIds(servers);
    const allErrorsMap = new Map([...serverErrorMap, ...duplicateServerMap]);

    return {
      errors: allErrorsMap,
      warnings: serverWarningMap,
    };
  }

  /*
   * Takes in an array of servers and returns a set of server IDs that have duplicate names
   */
  static parseDuplicateServerIds(servers: MCPServer[]): Map<string, string> {
    const nameMap = new Map<string, string[]>();

    // First pass: collect server IDs by name
    for (const server of servers) {
      if (!nameMap.has(server.name)) {
        nameMap.set(server.name, []);
      }
      nameMap.get(server.name)!.push(server.id);
    }

    // Second pass: add duplicate IDs to the result set
    const duplicateIds = new Map<string, string>();
    for (const [, ids] of nameMap) {
      if (ids.length > 1) {
        // Skip the first ID (keep it as the original) and mark the rest as duplicates
        for (let i = 1; i < ids.length; i++) {
          duplicateIds.set(ids[i], "MCP server is disabled due to duplicate server names");
        }
      }
    }

    return duplicateIds;
  }

  /**
   * Converts a parsed NormalizedMCPServer to webview NormalizedMCPServer
   * @param parsedServer The server from sidecar libs parsing
   * @returns The server compatible with webview types
   */
  private static convertParsedServerToWebview(
    parsedServer: ParsedNormalizedMCPServer,
  ): NormalizedMCPServer {
    // Convert tools from string[] to ToolDefinitionWithSettings[] if needed
    // For now, we'll set tools to undefined since the conversion is complex
    // and the parsing functionality doesn't actually populate tools anyway
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { tools, ...serverWithoutTools } = parsedServer;
    return {
      ...serverWithoutTools,
      tools: undefined, // Will be populated later by the actual MCP server discovery
    };
  }

  /**
   * Parses MCP server configurations from a JSON string
   * @param jsonString The JSON string containing MCP server configurations
   * @returns An array of normalized server configurations
   * @throws If the JSON is invalid or has an unsupported format
   */
  static parseServerConfigFromJSON(jsonString: string): NormalizedMCPServer[] {
    const parsedServers = parseServerConfigFromJSONUtil(jsonString);
    return parsedServers.map((server) => this.convertParsedServerToWebview(server));
  }

  /**
   * Imports MCP servers from a JSON string
   * @param jsonString The JSON string containing MCP server configurations
   * @throws If the JSON is invalid or if a server with the same name already exists
   */
  importFromJSON(jsonString: string) {
    try {
      const serversToImport = MCPServerModel.parseServerConfigFromJSON(jsonString);

      // Validate server names before importing
      const currentServers = get(this.servers);
      const existingNames = new Set(currentServers.map((s: MCPServer) => s.name));
      for (const server of serversToImport) {
        if (!server.name) {
          throw new MCPServerError("All servers must have a name.");
        }
        if (existingNames.has(server.name)) {
          throw new MCPServerError(`A server with the name '${server.name}' already exists.`);
        }
        existingNames.add(server.name);
      }

      // Import the servers
      this.servers.update((servers) => {
        const newServers = serversToImport.map(
          (server): MCPServer =>
            ({
              ...server,
              id: crypto.randomUUID(),
            }) as MCPServer,
        );

        const updatedServers = [...servers, ...newServers];
        this.saveServers(updatedServers);
        return updatedServers;
      });

      return serversToImport.length;
    } catch (error) {
      if (error instanceof MCPServerError) {
        throw error;
      }
      throw new MCPServerError("Failed to import MCP servers from JSON. Please check the format.");
    }
  }
}
