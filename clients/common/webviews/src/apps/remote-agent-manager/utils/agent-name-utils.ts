/**
 * Utility functions for generating and managing agent names
 */

/**
 * Generates a unique agent name based on existing agents
 * @param existingAgents Array of existing agent overviews
 * @param baseSessionName Base name to use for the agent
 * @returns A unique agent name with numbering if needed
 */
export function generateUniqueAgentName(
  existingAgents: Array<{ sessionSummary: string }> = [],
  baseSessionName: string = "New remote agent",
): string {
  // Find agents with the same base name or with numbered versions
  const regex = new RegExp(`^${baseSessionName}(\\s*\\((\\d+)\\))?$`);
  const matchingAgents = existingAgents.filter((agent) => regex.test(agent.sessionSummary));

  // Find the highest number used
  let highestNumber = 0;
  matchingAgents.forEach((agent) => {
    const match = agent.sessionSummary.match(/\((\d+)\)$/);
    if (match) {
      const num = parseInt(match[1], 10);
      if (num > highestNumber) highestNumber = num;
    }
  });

  // Create name with number if needed
  return matchingAgents.length > 0 ? `${baseSessionName} (${highestNumber + 1})` : baseSessionName;
}
