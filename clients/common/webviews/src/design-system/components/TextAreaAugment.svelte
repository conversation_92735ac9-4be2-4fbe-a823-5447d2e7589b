<script lang="ts" context="module">
  export type {
    TextFieldColor,
    TextFieldSize,
    TextFieldVariant,
  } from "../_primitives/BaseTextInput.svelte";
</script>

<script lang="ts">
  import "@radix-ui/colors/gray.css";
  import "@radix-ui/colors/gray-alpha.css";
  import "@radix-ui/colors/black-alpha.css";

  import BaseTextInput, {
    type TextFieldColor,
    type TextFieldSize,
    type TextFieldVariant,
  } from "../_primitives/BaseTextInput.svelte";
  import { onMount } from "svelte";
  import { type TextType } from "./TextAugment.svelte";

  export let label: string | undefined = undefined;
  export let variant: TextFieldVariant = "surface";
  export let size: TextFieldSize = 2;
  export let color: TextFieldColor | undefined = undefined;
  export let resize: "none" | "both" | "horizontal" | "vertical" = "none";

  export let textInput: HTMLTextAreaElement | undefined = undefined;
  export let type: TextType = "default";
  export let value: string = "";
  export let id: string | undefined = undefined;

  $: inputId = id || `text-field-${Math.random().toString(36).substring(2, 11)}`;

  $: ({ class: className, ...restProps } = $$restProps);

  // Function to auto-resize the textarea based on content with max height constraint
  function autoResizeTextArea() {
    if (!textInput) {
      return;
    }

    // Reset height to auto to get the correct scrollHeight
    textInput.style.height = "auto";

    // Calculate the maximum height (80% of viewport height)
    const maxHeight = window.innerHeight * 0.8;

    // Set the height to the scrollHeight, but cap it at maxHeight
    const newHeight = Math.min(textInput.scrollHeight, maxHeight);
    textInput.style.height = `${newHeight}px`;

    // Add scrolling if content exceeds the max height
    textInput.style.overflowY = textInput.scrollHeight > maxHeight ? "auto" : "hidden";
  }

  // Function to set up auto-resize
  function setupAutoResize(node: HTMLTextAreaElement) {
    requestAnimationFrame(autoResizeTextArea);
    // Add event listener for input changes
    const handleInput = () => autoResizeTextArea();
    node.addEventListener("input", handleInput);

    // Also use ResizeObserver as a fallback for when content changes
    const resizeObserver = new ResizeObserver(autoResizeTextArea);
    resizeObserver.observe(node);

    return {
      destroy() {
        // Clean up event listener on component destroy
        node.removeEventListener("input", handleInput);
        resizeObserver.disconnect();
      },
    };
  }

  // Initialize auto-resize on mount and handle window resize
  onMount(() => {
    if (textInput) {
      // Use requestAnimationFrame to ensure layout is complete
      requestAnimationFrame(autoResizeTextArea);

      // Add window resize event listener
      const handleResize = () => autoResizeTextArea();
      window.addEventListener("resize", handleResize);

      // Clean up event listener on component destroy
      return () => {
        window.removeEventListener("resize", handleResize);
      };
    }
  });
</script>

<div class="c-text-area">
  {#if label || $$slots.topRightAction}
    <div class="c-text-area-label-container">
      {#if label}
        <label class="c-text-area-label" for={inputId}>
          {label}
        </label>
      {/if}
      <slot name="topRightAction" />
    </div>
  {/if}
  <BaseTextInput {type} {variant} {size} {color}>
    <textarea
      id={inputId}
      spellCheck="false"
      class={`c-text-area__input c-base-text-input__input ${className}`}
      class:c-textarea--resize-none={resize === "none"}
      class:c-textarea--resize-both={resize === "both"}
      class:c-textarea--resize-horizontal={resize === "horizontal"}
      class:c-textarea--resize-vertical={resize === "vertical"}
      bind:this={textInput}
      bind:value
      {...restProps}
      use:setupAutoResize
      on:click
      on:focus
      on:keydown
      on:change
      on:input
      on:keyup
      on:blur
      on:select
      on:mouseup
    ></textarea>
  </BaseTextInput>
</div>

<style>
  .c-text-area-label {
    font-size: var(--text-area-label-font-size, var(--ds-font-size-1));
    display: block;
    text-align: left;
  }
  .c-text-area-label-container {
    display: flex;
    justify-content: space-between;
    margin-bottom: var(--ds-spacing-1);
  }
  .c-text-area :global(.c-text-area__input.c-base-text-input__input) {
    font-size: var(--text-area-font-size, var(--ds-font-size-1));
    text-indent: unset; /** unset text-indent from BaseTextInput */
    padding: var(--base-text-field-input-padding);
    overflow-y: auto; /* Enable vertical scrolling */
    max-height: 100vh;
  }

  .c-text-area :global(.c-textarea--resize-none) {
    resize: none;
  }
  .c-text-area :global(.c-textarea--resize-both) {
    resize: both;
  }
  .c-text-area :global(.c-textarea--resize-horizontal) {
    resize: horizontal;
  }
  .c-text-area :global(.c-textarea--resize-vertical) {
    resize: vertical;
  }

  .c-text-area :global(.c-base-text-input) {
    --base-text-field-height: auto;
  }
</style>
