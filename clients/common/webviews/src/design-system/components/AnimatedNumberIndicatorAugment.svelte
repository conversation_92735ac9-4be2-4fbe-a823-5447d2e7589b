<script lang="ts">
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import type {
    TextSize,
    TextColor,
  } from "$common-webviews/src/design-system/components/TextAugment.svelte";

  import { onD<PERSON>roy } from "svelte";
  const FRAME_DURATION = 16; // 60 FPS

  // Props that match TextAugment for consistency
  export let value: number = 0;
  export let size: TextSize = 1;
  export let color: TextColor = "neutral";
  export let maxDuration: number = 500; // Maximum animation duration in milliseconds
  export let easing: (t: number) => number = (t: number) => t * t * (3 - 2 * t); // Smooth step easing
  export let delay: number = 0; // Additional delay before animation starts in milliseconds
  export let autoStart: boolean = false; // If true, starts animation immediately when set to true

  let currentValue: number = 0;
  let hasStartedAnimation: boolean = false;
  let digits: number = 1;
  $: digits = String(currentValue).length;

  // Track timers/animation frame for cleanup
  let frameId: number | null = null;
  let delayTimeoutId: ReturnType<typeof setTimeout> | null = null;

  // Safe scheduling helpers for environments without rAF (e.g., tests)
  const hasRAF = typeof requestAnimationFrame !== "undefined";
  function now(): number {
    // Prefer performance.now() when available
    return typeof performance !== "undefined" && typeof performance.now === "function"
      ? performance.now()
      : Date.now();
  }
  function requestFrame(cb: (time: number) => void): number {
    if (hasRAF) {
      return requestAnimationFrame(cb as FrameRequestCallback);
    }
    return setTimeout(() => cb(now()), FRAME_DURATION) as unknown as number;
  }
  function cancelFrame(id: number) {
    if (hasRAF && typeof cancelAnimationFrame !== "undefined") {
      cancelAnimationFrame(id);
    } else {
      clearTimeout(id as unknown as ReturnType<typeof setTimeout>);
    }
  }

  // Calculate optimal animation duration based on the target value
  function calculateDuration(targetValue: number): number {
    if (targetValue === 0) return FRAME_DURATION;

    // Calculate duration based on max increment time
    // This ensures small numbers animate quickly (each increment takes at most maxIncrementTime)
    const incrementBasedDuration = targetValue * FRAME_DURATION;

    // Cap the duration at maxDuration to prevent overly long animations for large numbers
    // Apply minimum duration to prevent animations that are too fast
    const cappedDuration = Math.min(incrementBasedDuration, maxDuration);

    return Math.max(FRAME_DURATION, cappedDuration);
  }

  // Animation function using requestAnimationFrame (or fallback) for smooth performance
  function animateToValue(targetValue: number) {
    if (targetValue === 0) return;

    hasStartedAnimation = true;
    currentValue = 0; // Reset to 0 when starting animation

    // Calculate optimal duration for this specific value
    const animationDuration = calculateDuration(targetValue);

    // Apply delay before starting animation
    delayTimeoutId = setTimeout(() => {
      const startValue = 0;
      const startTime = now();

      function animate(currentTime: number) {
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / animationDuration, 1);

        // Apply easing function
        const easedProgress = easing(progress);

        // Calculate current value
        currentValue = Math.round(startValue + (targetValue - startValue) * easedProgress);

        // Continue animation if not complete
        if (progress < 1) {
          frameId = requestFrame(animate);
        } else {
          currentValue = targetValue; // Ensure we end exactly at target
        }
      }

      frameId = requestFrame(animate);
    }, delay);
  }

  // Reactive statement to start animation when autoStart becomes true
  $: if (autoStart) {
    // Reset animation state when autoStart changes from false to true
    if (!hasStartedAnimation) {
      animateToValue(value);
    }
  } else {
    // Reset animation state when autoStart becomes false
    hasStartedAnimation = false;
    currentValue = 0;
  }

  onDestroy(() => {
    if (frameId !== null) {
      cancelFrame(frameId);
      frameId = null;
    }
    if (delayTimeoutId !== null) {
      clearTimeout(delayTimeoutId);
      delayTimeoutId = null;
    }
  });
</script>

<span class="c-animated-number-indicator" style="--digits:{digits}">
  <TextAugment {size} {color}>
    {currentValue}
  </TextAugment>
</span>

<style>
  .c-animated-number-indicator :global(span) {
    display: block;
    font-variant-numeric: tabular-nums;
    /* This transition prevent jumping when digit count changes */
    transition: width 150ms;
    width: calc(var(--digits, 1) * 1ch);
    overflow: hidden;
  }
</style>
