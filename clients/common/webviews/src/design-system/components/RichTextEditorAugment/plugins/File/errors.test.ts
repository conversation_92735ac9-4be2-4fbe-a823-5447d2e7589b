import {
  FileError,
  FileSizeError,
  FileTypeError,
  validateFile,
  type ValidateFileOptions,
} from "./errors";
import { expect, describe, it } from "vitest";

const IMAGE_TEXT_OPTIONS = { supportedFileTypes: ["image", "text"] } as ValidateFileOptions;

describe("File errors", () => {
  describe("validateFile", () => {
    it("should return a FileTypeError for unsupported binary files", () => {
      const file = new File([""], "test.exe", { type: "application/octet-stream" });
      const result = validateFile(file, IMAGE_TEXT_OPTIONS);
      expect(result).toBeInstanceOf(FileTypeError);
    });

    it("should return the file itself for supported text files", () => {
      const file = new File([""], "test.txt", { type: "text/plain" });
      const result = validateFile(file, IMAGE_TEXT_OPTIONS);
      expect(result).toBe(file);
    });

    it("should return the file itself even for files exceeding the size limit", () => {
      // Create a 2KB file by repeating a character 2048 times (2 * 1024 bytes)
      const twoKbContent = "x".repeat(2048);
      const file = new File([twoKbContent], "test.png", { type: "image/png" });
      const result = validateFile(file, {
        maxImageSizeKb: 1,
        supportedFileTypes: ["image", "text"],
      });
      expect(result).toBe(file);
    });

    it("should return the file itself if it passes validation", () => {
      const file = new File([""], "test.png", { type: "image/png" });
      const result = validateFile(file, IMAGE_TEXT_OPTIONS);
      expect(result).toBe(file);
    });

    it("should support various programming language files", () => {
      const testFiles = [
        new File([""], "test.js", { type: "application/javascript" }),
        new File([""], "test.py", { type: "text/x-python" }),
        new File([""], "test.java", { type: "text/x-java-source" }),
        new File([""], "test.go", { type: "text/x-go" }),
        new File([""], "test.rs", { type: "text/x-rust" }),
      ];

      testFiles.forEach((file) => {
        const result = validateFile(file, IMAGE_TEXT_OPTIONS);
        expect(result).toBe(file);
      });
    });

    it("should support configuration files", () => {
      const testFiles = [
        new File([""], "config.json", { type: "application/json" }),
        new File([""], "config.yaml", { type: "application/yaml" }),
        new File([""], "Dockerfile", { type: "" }), // Files without extensions
        new File([""], ".gitignore", { type: "" }),
      ];

      testFiles.forEach((file) => {
        const result = validateFile(file, IMAGE_TEXT_OPTIONS);
        expect(result).toBe(file);
      });
    });

    it("should respect supportedFileTypes option - image only", () => {
      const imageFile = new File([""], "test.png", { type: "image/png" });
      const textFile = new File([""], "test.txt", { type: "text/plain" });

      // When only image is supported
      const imageResult = validateFile(imageFile, { supportedFileTypes: ["image"] });
      const textResult = validateFile(textFile, { supportedFileTypes: ["image"] });

      expect(imageResult).toBe(imageFile);
      expect(textResult).toBeInstanceOf(FileTypeError);
    });

    it("should respect supportedFileTypes option - text only", () => {
      const imageFile = new File([""], "test.png", { type: "image/png" });
      const textFile = new File([""], "test.txt", { type: "text/plain" });

      // When only text is supported
      const imageResult = validateFile(imageFile, { supportedFileTypes: ["text"] });
      const textResult = validateFile(textFile, { supportedFileTypes: ["text"] });

      expect(imageResult).toBeInstanceOf(FileTypeError);
      expect(textResult).toBe(textFile);
    });

    it("should respect supportedFileTypes option - both image and text", () => {
      const imageFile = new File([""], "test.png", { type: "image/png" });
      const textFile = new File([""], "test.txt", { type: "text/plain" });

      // When both are supported
      const imageResult = validateFile(imageFile, IMAGE_TEXT_OPTIONS);
      const textResult = validateFile(textFile, IMAGE_TEXT_OPTIONS);

      expect(imageResult).toBe(imageFile);
      expect(textResult).toBe(textFile);
    });

    it("should include supported image types in error message", () => {
      const file = new File([""], "test.exe", { type: "application/octet-stream" });

      // Test with image only
      const imageOnlyResult = validateFile(file, { supportedFileTypes: ["image"] });
      expect(imageOnlyResult).toBeInstanceOf(FileTypeError);
      if (imageOnlyResult instanceof FileTypeError) {
        expect(imageOnlyResult.message).toContain(`only attach image files.`);
      }
    });

    it("should include supported text types in error message", () => {
      const file = new File([""], "test.exe", { type: "application/octet-stream" });
      // Test with text only
      const textOnlyResult = validateFile(file, { supportedFileTypes: ["text"] });
      expect(textOnlyResult).toBeInstanceOf(FileTypeError);
      if (textOnlyResult instanceof FileTypeError) {
        expect(textOnlyResult.message).toContain(`only attach text files.`);
      }
    });

    it("should include supported image and file types in error message", () => {
      const file = new File([""], "test.exe", { type: "application/octet-stream" });
      // Test with both
      const bothResult = validateFile(file, IMAGE_TEXT_OPTIONS);
      expect(bothResult).toBeInstanceOf(FileTypeError);
      if (bothResult instanceof FileTypeError) {
        expect(bothResult.message).toContain(`only attach image and text files.`);
      }
    });

    it("should default to both image and text when supportedFileTypes is not specified", () => {
      const imageFile = new File([""], "test.png", { type: "image/png" });
      const textFile = new File([""], "test.txt", { type: "text/plain" });

      // When supportedFileTypes is not specified, should default to both
      const imageResult = validateFile(imageFile, IMAGE_TEXT_OPTIONS);
      const textResult = validateFile(textFile, IMAGE_TEXT_OPTIONS);

      expect(imageResult).toBe(imageFile);
      expect(textResult).toBe(textFile);
    });
  });

  describe("FileError", () => {
    it("should have the correct name and message", () => {
      const error = new FileError(
        [new File([""], "test.png", { type: "image/png" })],
        "Test error message",
      );
      expect(error.name).toBe("FileError");
      expect(error.message).toBe("Test error message");
    });
  });

  describe("FileSizeError", () => {
    it("should have the correct name and message", () => {
      const file = new File([""], "test.png", { type: "image/png" });
      const error = new FileSizeError(file, 100);
      expect(error.name).toBe("FileError");
      expect(error.message).toContain(`File "${file.name}" exceeds size limit of 100KB`);
    });
  });

  describe("FileTypeError", () => {
    it("should have the correct name and message", () => {
      const file = new File([""], "test.exe", { type: "application/octet-stream" });
      const error = new FileTypeError(file, ["image", "text"]);
      expect(error.name).toBe("FileError");
      expect(error.message).toContain(`only attach image and text files.`);
    });
  });
});
