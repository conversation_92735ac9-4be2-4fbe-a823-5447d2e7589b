<script lang="ts">
  import type { HTMLButtonAttributes } from "svelte/elements";
  import type {
    ButtonColor,
    ButtonVariant,
    ButtonRadius,
  } from "$common-webviews/src/design-system/_primitives/BaseButton.svelte";
  import ChildIconButton, {
    type IconButtonSize,
  } from "$common-webviews/src/design-system/_primitives/ChildIconButton.svelte";

  interface Props extends HTMLButtonAttributes {
    size?: IconButtonSize;
    variant?: ButtonVariant;
    color?: ButtonColor;
    highContrast?: boolean;
    radius?: ButtonRadius;
    class?: string;
    title?: string;
    loading?: boolean;
  }

  let {
    size = 2,
    variant = 'solid',
    color = 'neutral',
    highContrast = false,
    disabled = false,
    radius = 'medium',
    loading = false,
    children,
    ...restProps  
  }: Props = $props();
</script>

<ChildIconButton
  {size}
  {variant}
  {color}
  {highContrast}
  {disabled}
  {radius}
  {loading}
  on:click
  on:keyup
  on:keydown
  on:mousedown
  on:mouseover
  on:focus
  on:mouseleave
  on:blur
  on:contextmenu
  {...restProps}
>
  {@render children?.()}
</ChildIconButton>
