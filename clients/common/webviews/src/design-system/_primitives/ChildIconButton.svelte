<script lang="ts" module>
  export type IconButtonSize = 0 | ButtonSize;
</script>

<script lang="ts">
  import type { HTMLButtonAttributes } from "svelte/elements";
  import {
    sizeToClassSize,
    type ButtonColor,
    type ButtonRadius,
    type ButtonSize,
    type ButtonVariant,
  } from "$common-webviews/src/design-system/_primitives/BaseButton.svelte";
  import BaseButton from "$common-webviews/src/design-system/_primitives/BaseButton.svelte";
 

  interface Props extends HTMLButtonAttributes {
    size?: IconButtonSize;
    variant?: ButtonVariant;
    color?: ButtonColor;
    highContrast?: boolean;
    radius?: ButtonRadius;
    class?: string;
    title?: string;
    loading?: boolean;
  }

  let {
    size = 2,
    variant = 'solid',
    color = 'accent',
    highContrast = false,
    disabled = false,
    radius = 'medium',
    class: className,
    loading = false,
    children,
    ...restProps  
  }: Props = $props();

</script>

<div class={`c-icon-btn c-icon-btn--size-${sizeToClassSize(size)}`}>
  <BaseButton
    {size}
    {variant}
    {color}
    {highContrast}
    disabled={disabled || undefined}
    {radius}
    {loading}
    class={className}
    on:click
    on:keyup
    on:keydown
    on:mousedown
    on:mouseover
    on:focus
    on:mouseleave
    on:blur
    on:contextmenu
    {...restProps}
  >
    {@render children?.()}
  </BaseButton>
</div>

<style>
  .c-icon-btn {
    display: contents;
    --icon-color: var(--base-btn-color, currentColor);
    --icon-size: 16px;
  }
  .c-icon-btn :global(svg) {
    fill: var(--icon-color);
    width: var(--icon-size);
    height: var(--icon-size);
  }
  .c-icon-btn :global(.c-base-btn) {
    width: var(--icon-btn-size);
    height: var(--icon-btn-size);
  }

  .c-icon-btn :global(.c-base-btn.c-base-btn--ghost) {
    /* Position icon inline when ghost variant */
    padding: var(--icon-button-ghost-padding);
    margin: calc(var(--icon-button-ghost-padding) * -1);
  }
  .c-icon-btn--size-0 {
    --icon-btn-size: var(--ds-spacing-4);
    --icon-button-ghost-padding: calc(var(--ds-spacing-1) * 0.75);
  }
  .c-icon-btn--size-0 :global(svg) {
    --icon-size: var(--ds-spacing-3);
  }
  .c-icon-btn--size-0_5 {
    --icon-size: var(--ds-spacing-3);
    --icon-btn-size: var(--ds-spacing-4_5);
    --icon-button-ghost-padding: var(--ds-spacing-1);
  }
  .c-icon-btn--size-1 {
    --icon-btn-size: var(--ds-spacing-5);
    --icon-button-ghost-padding: var(--ds-spacing-1);
  }

  .c-icon-btn--size-2 {
    --icon-btn-size: var(--ds-spacing-6);
    --icon-button-ghost-padding: calc(var(--ds-spacing-1) * 1.5);
  }

  .c-icon-btn--size-3 {
    --icon-btn-size: var(--ds-spacing-7);
    --icon-button-ghost-padding: var(--ds-spacing-2);
  }

  .c-icon-btn--size-4 {
    --icon-btn-size: var(--ds-spacing-8);
    --icon-button-ghost-padding: var(--ds-spacing-3);
  }
</style>
