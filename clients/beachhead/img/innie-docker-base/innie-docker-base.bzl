#
# This file is auto-generated by rebuild.sh. DO NOT EDIT BY HAND.
#

load("@rules_oci//oci:pull.bzl", "oci_pull")

def docker_innie_base_setup():
    oci_pull(
        name = "remote-agents-innie-base",
        registry = "us-central1-docker.pkg.dev",
        repository = "system-services-dev/base-images/remote-agents-innie-base",
        digest = "sha256:20bdb17dcf4a31f434e8b6e48bcc4cf203776b83b68072400dd7a22318577340",
    )
