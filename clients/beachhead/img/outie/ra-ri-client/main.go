package main

import (
	"context"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/rs/zerolog"
	"github.com/rs/zerolog/log"
	"github.com/spf13/pflag"

	"github.com/augmentcode/augment/tools/bzl/buildinfo"
)

var (
	projectID        = pflag.String("project-id", "", "GCP project ID (required)")
	topicName        = pflag.String("topic", "", "Pub/Sub topic name (required)")
	maxMessageSize   = pflag.Int("max-message-size", 5*1024*1024, "Maximum size in bytes of the combined message before sending (default: 5MB)")
	batchTimeoutSecs = pflag.Int("batch-timeout", 5, "Maximum time in seconds to wait before sending a batch")
	sessionID        = pflag.String("session-id", "", "Session ID to use for the log processor (required)")
	tenantID         = pflag.String("tenant-id", "", "Tenant ID to use for the log processor (required)")
	tenantName       = pflag.String("tenant-name", "", "Tenant name to use for the log processor (required)")
	maxLines         = pflag.Int("number", 0, "Maximum number of log lines to process before exiting (0 = unlimited)")
	debug            = pflag.Bool("debug", false, "Enable debug logging")
	trace            = pflag.Bool("trace", false, "Enable trace logging (logs contents of every log entry)")
	systemdUnit      = pflag.String("systemd-unit", "<EMAIL>", "Systemd unit to monitor logs from")
)

func main() {
	// Parse flags
	pflag.Parse()

	augmentImgHash := buildinfo.Info.GitCommitShort()

	// Setup logging
	zerolog.TimeFieldFormat = time.RFC3339Nano
	zerolog.LevelFieldName = "severity"
	logLevel := zerolog.InfoLevel
	if *debug {
		logLevel = zerolog.DebugLevel
	}
	zerolog.SetGlobalLevel(logLevel)
	// Add a "component" field and augment image hash to all log entries
	log.Logger = log.With().Str("component", "ra-ri-client").Str("_augment_img", augmentImgHash).Caller().Logger()

	// Log all flag values (both default and provided)
	log.Debug().
		Int("max_message_size", *maxMessageSize).
		Int("batch_timeout_secs", *batchTimeoutSecs).
		Int("max_lines", *maxLines).
		Bool("debug", *debug).
		Bool("trace", *trace).
		Msg("Initial flag values")

	// Validate required flags
	if *projectID == "" || *topicName == "" || *sessionID == "" || *tenantID == "" || *tenantName == "" {
		log.Fatal().Msg("project-id, topic, session-id, tenant-id, and tenant-name are required")
	}

	// Log all final flag values after defaults are applied
	log.Info().
		Str("project_id", *projectID).
		Str("topic", *topicName).
		Str("session_id", *sessionID).
		Int("max_message_size", *maxMessageSize).
		Int("batch_timeout_secs", *batchTimeoutSecs).
		Int("max_lines", *maxLines).
		Bool("debug", *debug).
		Bool("trace", *trace).
		Str("systemd_unit", *systemdUnit).
		Msg("Final configuration values")

	// Create context that can be cancelled
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// Setup signal handling
	sigCh := make(chan os.Signal, 1)
	signal.Notify(sigCh, syscall.SIGINT, syscall.SIGTERM)
	go func() {
		sig := <-sigCh
		log.Info().Str("signal", sig.String()).Msg("Received signal, shutting down")
		cancel()
	}()

	// Create pub/sub client
	pubsubClient, err := NewPubSubClient(ctx, *projectID, *topicName)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to create pub/sub client")
	}
	defer pubsubClient.Close()

	// Create log processor
	processor := NewLogProcessor(pubsubClient, *maxMessageSize, time.Duration(*batchTimeoutSecs)*time.Second, *sessionID, *tenantID, *tenantName, *maxLines, *trace, augmentImgHash)

	// Start processing logs
	logInfo := log.Info().
		Int("max_message_size", *maxMessageSize).
		Int("batch_timeout_secs", *batchTimeoutSecs).
		Str("project_id", *projectID).
		Str("topic", *topicName).
		Str("session_id", processor.GetSessionID()).
		Str("tenant_id", *tenantID).
		Str("tenant_name", *tenantName).
		Str("systemd_unit", *systemdUnit).
		Bool("trace", *trace)

	// Add max lines if specified
	if *maxLines > 0 {
		logInfo = logInfo.Int("max_lines", *maxLines)
	}

	logInfo.Msg("Starting log processor")

	// TODO(marcmac) handle SIGTERM to flush remaining logs.
	if err := processor.ProcessLogs(ctx, *systemdUnit); err != nil {
		log.Fatal().Err(err).Msg("Log processing failed")
	}

	log.Info().Msg("Shutdown complete")
}
