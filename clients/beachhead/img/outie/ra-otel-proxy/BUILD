load("//tools/bzl:go.bzl", "go_binary")

go_binary(
    name = "ra-otel-proxy",
    srcs = [
        "main.go",
        "otel_proxy.go",
    ],
    visibility = ["//clients/beachhead/img/outie:__pkg__"],
    deps = [
        "//tools/bzl/buildinfo:buildinfo_go",
        "@com_github_google_uuid//:uuid",
        "@com_github_rs_zerolog//:zerolog",
        "@com_github_rs_zerolog//log",
        "@com_github_spf13_pflag//:pflag",
    ],
)
