package main

import (
	"context"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/rs/zerolog"
	"github.com/rs/zerolog/log"
	"github.com/spf13/pflag"

	"github.com/augmentcode/augment/tools/bzl/buildinfo"
)

var (
	debug       = pflag.Bool("debug", false, "Enable debug logging")
	trace       = pflag.Bool("trace", false, "Enable trace logging (logs contents of every otel payload)")
	systemdUnit = pflag.String("systemd-unit", "<EMAIL>", "Systemd unit to monitor logs from")
)

func main() {
	// Parse flags
	pflag.Parse()

	// Setup logging
	zerolog.TimeFieldFormat = time.RFC3339Nano
	zerolog.LevelFieldName = "severity"
	logLevel := zerolog.InfoLevel
	if *debug {
		logLevel = zerolog.DebugLevel
	}
	zerolog.SetGlobalLevel(logLevel)
	// Add a "component" field and augment image hash to all log entries
	log.Logger = log.With().Str("component", "ra-otel-proxy").Str("_augment_img", buildinfo.Info.GitCommitShort()).Caller().Logger()

	otelExporterOtlpEndpoint := os.Getenv("OTEL_EXPORTER_OTLP_ENDPOINT")

	// Log all flag values (both default and provided)
	log.Debug().
		Str("otel_exporter_otlp_endpoint", otelExporterOtlpEndpoint).
		Bool("debug", *debug).
		Bool("trace", *trace).
		Msg("Initial flag values")

	// Log all final flag values after defaults are applied
	log.Info().
		Str("otel_exporter_otlp_endpoint", otelExporterOtlpEndpoint).
		Bool("debug", *debug).
		Bool("trace", *trace).
		Str("systemd_unit", *systemdUnit).
		Msg("Final configuration values")

	proxy := NewOtelProxy(4318, otelExporterOtlpEndpoint)

	// Setup signal handling
	sigCh := make(chan os.Signal, 1)
	signal.Notify(sigCh, syscall.SIGINT, syscall.SIGTERM)
	go func() {
		sig := <-sigCh
		log.Info().Str("signal", sig.String()).Msg("Received signal, shutting down")
		proxy.Shutdown(context.Background())
	}()

	if err := proxy.Run(); err != nil {
		log.Fatal().Err(err).Msg("Proxy server exited with error")
	}

	log.Info().Msg("Shutdown complete")
}
