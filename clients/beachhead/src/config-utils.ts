// Configuration utilities shared between beachhead and CLI
import { IClientFeatureFlags } from "@augment-internal/sidecar-libs/src/client-interfaces/feature-flags";
import * as os from "os";
import * as path from "path";
import * as process from "process";

import { ModelConfig } from "./augment-api";
import { defaultFeatureFlags, FeatureFlags } from "./feature-flags";
import { type AugmentLogger, getLogger } from "./logging";
import { fileExists, readFileUtf8 } from "./utils/fs-utils";

/**
 * Converts ModelConfig to both FeatureFlags and IClientFeatureFlags objects
 * Uses live feature flags from the API where available, with sensible defaults for others.
 * Follows the same pattern as VSCode's ClientFeatureFlags class.
 */
export function convertToFeatureFlags(modelConfig: ModelConfig): {
    featureFlags: FeatureFlags;
    clientFeatureFlags: IClientFeatureFlags;
} {
    const apiFeatureFlags = modelConfig.featureFlags;

    // Create the full FeatureFlags object by merging API flags with defaults
    const featureFlags: FeatureFlags = {
        ...defaultFeatureFlags,
        vscodeAgentEditTool:
            apiFeatureFlags.vscodeAgentEditTool ?? defaultFeatureFlags.vscodeAgentEditTool,
        memoriesParams: apiFeatureFlags.memoriesParams ?? defaultFeatureFlags.memoriesParams,
        agentEditToolMinViewSize:
            apiFeatureFlags.agentEditToolMinViewSize ??
            defaultFeatureFlags.agentEditToolMinViewSize,
        agentEditToolSchemaType:
            apiFeatureFlags.agentEditToolSchemaType ?? defaultFeatureFlags.agentEditToolSchemaType,
        agentEditToolEnableFuzzyMatching:
            apiFeatureFlags.agentEditToolEnableFuzzyMatching ??
            defaultFeatureFlags.agentEditToolEnableFuzzyMatching,
        agentEditToolFuzzyMatchSuccessMessage:
            apiFeatureFlags.agentEditToolFuzzyMatchSuccessMessage ??
            defaultFeatureFlags.agentEditToolFuzzyMatchSuccessMessage,
        agentEditToolFuzzyMatchMaxDiff:
            apiFeatureFlags.agentEditToolFuzzyMatchMaxDiff ??
            defaultFeatureFlags.agentEditToolFuzzyMatchMaxDiff,
        agentEditToolFuzzyMatchMaxDiffRatio:
            apiFeatureFlags.agentEditToolFuzzyMatchMaxDiffRatio ??
            defaultFeatureFlags.agentEditToolFuzzyMatchMaxDiffRatio,
        agentEditToolFuzzyMatchMinAllMatchStreakBetweenDiffs:
            apiFeatureFlags.agentEditToolFuzzyMatchMinAllMatchStreakBetweenDiffs ??
            defaultFeatureFlags.agentEditToolFuzzyMatchMinAllMatchStreakBetweenDiffs,
        agentEditToolInstructionsReminder:
            apiFeatureFlags.agentEditToolInstructionsReminder ??
            defaultFeatureFlags.agentEditToolInstructionsReminder,
        agentEditToolShowResultSnippet:
            apiFeatureFlags.agentEditToolShowResultSnippet ??
            defaultFeatureFlags.agentEditToolShowResultSnippet,
        agentEditToolMaxLines:
            apiFeatureFlags.agentEditToolMaxLines ?? defaultFeatureFlags.agentEditToolMaxLines,
        agentSaveFileToolInstructionsReminder:
            apiFeatureFlags.agentSaveFileToolInstructionsReminder ??
            defaultFeatureFlags.agentSaveFileToolInstructionsReminder,
        vscodeTaskListMinVersion:
            apiFeatureFlags.vscodeTaskListMinVersion ??
            defaultFeatureFlags.vscodeTaskListMinVersion,
        grepSearchToolEnable:
            apiFeatureFlags.grepSearchToolEnable ?? defaultFeatureFlags.grepSearchToolEnable,
        grepSearchToolTimelimitSec:
            apiFeatureFlags.grepSearchToolTimelimitSec ??
            defaultFeatureFlags.grepSearchToolTimelimitSec,
        grepSearchToolOutputCharsLimit:
            apiFeatureFlags.grepSearchToolOutputCharsLimit ??
            defaultFeatureFlags.grepSearchToolOutputCharsLimit,
        grepSearchToolNumContextLines:
            apiFeatureFlags.grepSearchToolNumContextLines ??
            defaultFeatureFlags.grepSearchToolNumContextLines,
        agentReportStreamedChatEveryChunk:
            apiFeatureFlags.agentReportStreamedChatEveryChunk ??
            defaultFeatureFlags.agentReportStreamedChatEveryChunk,
        agentMaxTotalChangedFilesSizeBytes:
            apiFeatureFlags.agentMaxTotalChangedFilesSizeBytes ??
            defaultFeatureFlags.agentMaxTotalChangedFilesSizeBytes,
        agentMaxChangedFilesSkippedPaths:
            apiFeatureFlags.agentMaxChangedFilesSkippedPaths ??
            defaultFeatureFlags.agentMaxChangedFilesSkippedPaths,
        agentIdleStatusUpdateIntervalMs:
            apiFeatureFlags.agentIdleStatusUpdateIntervalMs ??
            defaultFeatureFlags.agentIdleStatusUpdateIntervalMs,
        agentMaxIterations:
            apiFeatureFlags.agentMaxIterations ?? defaultFeatureFlags.agentMaxIterations,
        agentSshConnectionCheckIntervalMs:
            apiFeatureFlags.agentSshConnectionCheckIntervalMs ??
            defaultFeatureFlags.agentSshConnectionCheckIntervalMs,
        agentSshConnectionCheckLogIntervalMs:
            apiFeatureFlags.agentSshConnectionCheckLogIntervalMs ??
            defaultFeatureFlags.agentSshConnectionCheckLogIntervalMs,
        beachheadEnableSentry:
            apiFeatureFlags.beachheadEnableSentry ?? defaultFeatureFlags.beachheadEnableSentry,
        enableUntruncatedContentStorage:
            apiFeatureFlags.enableUntruncatedContentStorage ??
            defaultFeatureFlags.enableUntruncatedContentStorage,
        maxLinesTerminalProcessOutput:
            apiFeatureFlags.maxLinesTerminalProcessOutput ??
            defaultFeatureFlags.maxLinesTerminalProcessOutput,
        truncationFooterAdditionText:
            apiFeatureFlags.truncationFooterAdditionText ??
            defaultFeatureFlags.truncationFooterAdditionText,
        beachheadEnableSubAgentTool:
            apiFeatureFlags.beachheadEnableSubAgentTool ??
            defaultFeatureFlags.beachheadEnableSubAgentTool,
        agentViewToolParams:
            apiFeatureFlags.agentViewToolParams ?? defaultFeatureFlags.agentViewToolParams,
    };

    // Create the IClientFeatureFlags object from the FeatureFlags object
    const clientFeatureFlags = createClientFeatureFlags(featureFlags);

    return { featureFlags, clientFeatureFlags };
}

/**
 * Creates an IClientFeatureFlags object from a FeatureFlags object
 */
export function createClientFeatureFlags(featureFlags: FeatureFlags): IClientFeatureFlags {
    return {
        flags: {
            agentEditTool: featureFlags.vscodeAgentEditTool,
            memoriesParams: featureFlags.memoriesParams,
            agentEditToolMinViewSize: featureFlags.agentEditToolMinViewSize,
            agentEditToolSchemaType: featureFlags.agentEditToolSchemaType,
            agentEditToolEnableFuzzyMatching: featureFlags.agentEditToolEnableFuzzyMatching,
            agentEditToolFuzzyMatchSuccessMessage:
                featureFlags.agentEditToolFuzzyMatchSuccessMessage,
            agentEditToolFuzzyMatchMaxDiff: featureFlags.agentEditToolFuzzyMatchMaxDiff,
            agentEditToolFuzzyMatchMaxDiffRatio: featureFlags.agentEditToolFuzzyMatchMaxDiffRatio,
            agentEditToolFuzzyMatchMinAllMatchStreakBetweenDiffs:
                featureFlags.agentEditToolFuzzyMatchMinAllMatchStreakBetweenDiffs,
            agentEditToolInstructionsReminder: featureFlags.agentEditToolInstructionsReminder,
            agentEditToolShowResultSnippet: featureFlags.agentEditToolShowResultSnippet,
            agentEditToolMaxLines: featureFlags.agentEditToolMaxLines,
            agentSaveFileToolInstructionsReminder:
                featureFlags.agentSaveFileToolInstructionsReminder,
            enableTaskList: featureFlags.vscodeTaskListMinVersion ? true : false,
            grepSearchToolEnable: featureFlags.grepSearchToolEnable,
            grepSearchToolTimelimitSec: featureFlags.grepSearchToolTimelimitSec,
            grepSearchToolOutputCharsLimit: featureFlags.grepSearchToolOutputCharsLimit,
            grepSearchToolNumContextLines: featureFlags.grepSearchToolNumContextLines,
            useHistorySummary: featureFlags.historySummaryMinVersion ? true : false,
            historySummaryParams: featureFlags.historySummaryParams,
            // Always enabled for beachhead
            enableChatWithTools: true,
            enableAgentMode: true,
            enableAgentSwarmMode: featureFlags.enableAgentSwarmMode ?? false,
            enableSwarmMode: featureFlags.enableAgentSwarmMode ?? false,
            enableNewThreadsList: featureFlags.enableNewThreadsList ?? false,
            enableSupportToolUseStart: false,
            enableUntruncatedContentStorage: featureFlags.enableUntruncatedContentStorage,
            maxLinesTerminalProcessOutput: featureFlags.maxLinesTerminalProcessOutput,
            truncationFooterAdditionText: featureFlags.truncationFooterAdditionText,
            enableCommitIndexing: featureFlags.enableCommitIndexing,
            maxCommitsToIndex: featureFlags.maxCommitsToIndex,
            enableExchangeStorage: false,
            enableToolUseStateStorage: false,
            enableAgentGitTracker: featureFlags.enableAgentGitTracker,
            agentViewToolParams: featureFlags.agentViewToolParams,
        },
    };
}

export async function readApiToken(cacheDir?: string, tokenFile?: string): Promise<string> {
    const logger = getLogger("Beachhead");

    // First priority: token file specified via --token-file
    if (tokenFile) {
        try {
            if (fileExists(tokenFile)) {
                logger.info("Reading token from file: %s", tokenFile);
                return (await readFileUtf8(tokenFile)).trim();
            } else {
                throw new Error(`Token file not found: ${tokenFile}`);
            }
        } catch (error) {
            logger.error("Failed to read token file %s: %s", tokenFile, error);
            throw error;
        }
    }

    // Second priority: environment variable
    const apiToken = process.env.AUGMENT_API_TOKEN;
    delete process.env.AUGMENT_API_TOKEN;
    if (apiToken) {
        return apiToken;
    }

    // Third priority: default token file in cache directory
    try {
        const augmentDir = cacheDir || path.join(os.homedir(), ".augment");
        const tokenPath = path.join(augmentDir, "token");
        if (fileExists(tokenPath)) {
            return (await readFileUtf8(tokenPath)).trim();
        }
    } catch (error) {
        logger.error("Failed to read token file: %s", error);
    }
    return "";
}

export async function readInstructionFromFile(
    instructionFile: string,
    logger: AugmentLogger,
    cliMode: boolean = false
): Promise<string> {
    try {
        const instruction = (await readFileUtf8(instructionFile)).trim();
        if (!instruction) {
            if (cliMode) {
                /* eslint-disable-next-line no-console */
                console.error(`\n❌ Error: Instruction file '${instructionFile}' is empty`);
                process.exit(1);
            } else {
                throw new Error(`Instruction file '${instructionFile}' is empty`);
            }
        }
        logger.info("Read instruction from file: %s", instructionFile);
        return instruction;
    } catch (error) {
        if (cliMode) {
            /* eslint-disable-next-line no-console */
            console.error(`\n❌ Error: Failed to read instruction file '${instructionFile}'`);
            /* eslint-disable-next-line no-console */
            console.error(`Details: ${error instanceof Error ? error.message : String(error)}`);
            process.exit(1);
        } else {
            throw new Error(
                `Failed to read instruction file '${instructionFile}': ${error instanceof Error ? error.message : String(error)}`
            );
        }
    }
}
