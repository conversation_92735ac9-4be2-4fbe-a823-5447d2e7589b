import { TextEventListener } from "../agent_loop/text-event-listener";
import { OutputConfig, OutputMode, OutputVerbosity, LogLevel } from "../cli/options/output";
import { TestEventListener } from "../agent_loop/test-event-listener";
import { TUIAgentEventListener } from "../tui/utils/agent-event-listener";

describe("Max Iterations Event", () => {
    test("TestEventListener logs maxIterationsExceeded event", () => {
        const listener = new TestEventListener();

        listener.onMaxIterationsExceeded?.(200);

        const events = listener.getEventSummary();
        expect(events).toContain("maxIterationsExceeded");

        const maxIterEvent = listener.events.find((e) => e.type === "maxIterationsExceeded");
        expect(maxIterEvent?.data).toEqual({ maxIterations: 200 });
    });

    test("TextEventListener has onMaxIterationsExceeded method", () => {
        const config: OutputConfig = {
            mode: OutputMode.TEXT,
            verbosity: OutputVerbosity.QUIET,
            notification_settings: { bell: false },
            log_settings: { file: "", level: LogLevel.INFO }
        };
        const listener = new TextEventListener(config);

        expect(typeof listener.onMaxIterationsExceeded).toBe("function");

        // Test that it doesn't throw when called
        expect(() => {
            listener.onMaxIterationsExceeded?.(200);
        }).not.toThrow();
    });

    test("TUIAgentEventListener forwards onMaxIterationsExceeded call", () => {
        const mockCallback = jest.fn();
        const listener = new TUIAgentEventListener({
            onMaxIterationsExceeded: mockCallback,
        });

        listener.onMaxIterationsExceeded?.(150);

        expect(mockCallback).toHaveBeenCalledWith(150);
    });

    test("TUIAgentEventListener handles missing callback gracefully", () => {
        const listener = new TUIAgentEventListener({});

        expect(() => {
            listener.onMaxIterationsExceeded?.(100);
        }).not.toThrow();
    });
});
