import { SerializedStore } from "@augment-internal/sidecar-libs/src/agent/agent-edit-types";
import { AggregateCheckpointManager } from "@augment-internal/sidecar-libs/src/agent/checkpoint/aggregate-checkpoint-manager";
import { ShardedStorage } from "@augment-internal/sidecar-libs/src/agent/sharding/storage";
import {
    ChatMode,
    ChatRequestNode,
    Rule,
    RuleType,
} from "@augment-internal/sidecar-libs/src/chat/chat-types";
import { setLibraryAPIClient } from "@augment-internal/sidecar-libs/src/client-interfaces/api-client";
import { setLibraryClientWorkspaces } from "@augment-internal/sidecar-libs/src/client-interfaces/client-workspaces";
import {
    IClientFeatureFlags,
    setLibraryClientFeatureFlags,
} from "@augment-internal/sidecar-libs/src/client-interfaces/feature-flags";
import { APIError } from "@augment-internal/sidecar-libs/src/exceptions";
import { ToolUseRequestEventReporter } from "@augment-internal/sidecar-libs/src/metrics/tool-use-request-event-reporter";
import { DummyToolApprovalConfigManager } from "@augment-internal/sidecar-libs/src/tools/approval-config/dummy-tool-approval-config-manager";
import { SidecarToolType } from "@augment-internal/sidecar-libs/src/tools/sidecar-tools/sidecar-tool-types";
import {
    McpServerConfig,
    ToolStartupError,
} from "@augment-internal/sidecar-libs/src/tools/tool-types";
import { ToolsModel } from "@augment-internal/sidecar-libs/src/tools/tools-model";
import {
    AUGMENT_DIRECTORY_ROOT,
    AUGMENT_GUIDELINES_FILE,
    AUGMENT_RULES_FOLDER,
    RulesParser,
} from "@augment-internal/sidecar-libs/src/utils/rules-parser";
import { APIStatus } from "@augment-internal/sidecar-libs/src/utils/types";
import * as fs from "fs";
import * as os from "os";
import * as path from "path";
import * as process from "process";
import * as readline from "readline";
import { v4 as uuidv4 } from "uuid";
import yargs from "yargs";
import { hideBin } from "yargs/helpers";

import { AgentLoop, AgentLoopEventListener, makeMessageNodes } from "./agent_loop/agent_loop";
import { AgentState } from "./agent_loop/state";
import { TextEventListener } from "./agent_loop/text-event-listener";
import { APIServerImplWithErrorReporting as APIServerImpl, AuthSessionStore } from "./augment-api";
import { AugmentConfigListener } from "./augment-config-listener";
import { FileBasedAuthSessionStore } from "./auth/auth-session-store";
import { OAuthFlow } from "./auth/oauth-flow";
import { BeachheadRemoteInfo } from "./beachhead-remote-info";
import { beachheadToolHostFactory } from "./beachhead-tool-host";
import {
    addAuthenticationOptions,
    addConfigurationOptions,
    addInputOptions,
    addOutputOptions,
    addSessionOptions,
    addToolsOptions,
    AuthenticationArgs,
    buildOutputConfig,
    ConfigurationArgs,
    InputArgs,
    OutputArgs,
    OutputConfig,
    OutputMode,
    SessionArgs,
    ToolsArgs,
} from "./cli/options";
import { parseMcpServerConfigurations } from "./cli/utils/mcp-utils";
import { detectWorkspaceRoot } from "./cli/utils/workspace-root";
import { BeachheadClientWorkspaces } from "./client-interfaces/client-workspaces";
import { SidecarAPIClient } from "./client-interfaces/sidecar-api-client";
import {
    convertToFeatureFlags,
    createClientFeatureFlags,
    readApiToken,
    readInstructionFromFile,
} from "./config-utils";
import { defaultFeatureFlags, FeatureFlags } from "./feature-flags";
import { readGitHubApiToken } from "./github-token";
import { type AugmentLogger, configureLogging, getLogger } from "./logging";
import { SessionManager } from "./session-manager";
import { tui } from "./tui/index";
import { openBrowser } from "./utils/open-browser";
import { getVersionWithGitInfo } from "./utils/version";
import { BeachheadWorkspaceManagerImpl } from "./workspace-manager";

const CLI_VERSION = getVersionWithGitInfo();

// Type for parsed command line arguments
interface ParsedArgs
    extends AuthenticationArgs,
        ConfigurationArgs,
        InputArgs,
        OutputArgs,
        SessionArgs,
        ToolsArgs {
    _: (string | number)[];
}

async function handleLogoutCommand(cacheDir?: string): Promise<void> {
    try {
        // Create auth session store
        const authSession = new FileBasedAuthSessionStore(cacheDir);

        // Check if logged in
        if (!authSession.isLoggedIn) {
            /* eslint-disable-next-line no-console */
            console.log("You are not currently logged in to Augment.");
            return;
        }

        /* eslint-disable-next-line no-console */
        console.log("🔓 Logging out of Augment...");

        // Remove the session
        await authSession.removeSession();

        /* eslint-disable-next-line no-console */
        console.log("✅ Successfully logged out of Augment.");
    } catch (error) {
        /* eslint-disable-next-line no-console */
        console.error(
            "❌ Failed to logout:",
            error instanceof Error ? error.message : String(error)
        );
        process.exit(1);
    }
}

async function handleDeleteSavedSessionsCommand(cacheDir?: string): Promise<void> {
    try {
        const sessionManager = new SessionManager(cacheDir || path.join(os.homedir(), ".augment"));

        /* eslint-disable-next-line no-console */
        console.log("🗑️  Deleting all saved sessions...");

        // Get all sessions metadata first to show what we're deleting
        const allSessions = await sessionManager.getAllSessionsMetadata();

        if (allSessions.length === 0) {
            /* eslint-disable-next-line no-console */
            console.log("No saved sessions found.");
            return;
        }

        /* eslint-disable-next-line no-console */
        console.log(`Found ${allSessions.length} session(s) to delete:`);

        // Delete each session
        for (const session of allSessions) {
            try {
                await sessionManager.deleteSession(session.sessionId);
                /* eslint-disable-next-line no-console */
                console.log(
                    `  ✅ Deleted session from ${new Date(session.created).toLocaleString()} (${session.exchangeCount} exchanges)`
                );
            } catch (error) {
                /* eslint-disable-next-line no-console */
                console.error(
                    `  ❌ Failed to delete session ${session.sessionId}: ${error instanceof Error ? error.message : String(error)}`
                );
            }
        }

        /* eslint-disable-next-line no-console */
        console.log("✅ Session deletion completed.");
    } catch (error) {
        /* eslint-disable-next-line no-console */
        console.error(
            "❌ Failed to delete saved sessions:",
            error instanceof Error ? error.message : String(error)
        );
        process.exit(1);
    }
}

async function handleRevokeTokensCommand(
    authURL: string,
    userAgent: string,
    cacheDir?: string
): Promise<void> {
    try {
        // Create auth session store
        const authSession = new FileBasedAuthSessionStore(cacheDir);

        // Check if logged in
        if (!authSession.isLoggedIn) {
            /* eslint-disable-next-line no-console */
            console.log("You are not currently logged in to Augment.");
            return;
        }

        /* eslint-disable-next-line no-console */
        console.log("🔐 Revoking all authentication tokens...");

        // Get session to determine API URL
        const session = await authSession.getSession();
        if (!session) {
            /* eslint-disable-next-line no-console */
            console.log("No active session found.");
            return;
        }

        // Create a minimal config for API calls
        const config = {
            config: {
                apiToken: "",
                completionURL: session.tenantURL,
                oauth: {
                    clientID: "v",
                    url: authURL,
                },
            },
        } as AugmentConfigListener;

        // Create API server instance
        // TODO(guy) maybe share the APIServerImpl object (and the related boilerplate code)
        const apiServer = new APIServerImpl(
            config,
            authSession,
            "revoke-tokens-session",
            userAgent,
            global.fetch
        );

        // Call the revoke tokens API
        const result = await apiServer.revokeCurrentUserTokens();

        /* eslint-disable-next-line no-console */
        console.log(`✅ Successfully revoked ${result.tokens_deleted} authentication token(s).`);

        // Also remove the local session
        await authSession.removeSession();
        /* eslint-disable-next-line no-console */
        console.log("✅ Local session removed.");
        /* eslint-disable-next-line no-console */
        console.log("⏱️  Note: It may take up to 5 minutes for token revocation to take effect.");
    } catch (error) {
        /* eslint-disable-next-line no-console */
        console.error(
            "❌ Failed to revoke tokens:",
            error instanceof Error ? error.message : String(error)
        );
        process.exit(1);
    }
}

async function handlePrintTokenCommand(cacheDir?: string): Promise<void> {
    try {
        // Create auth session store
        const authSession = new FileBasedAuthSessionStore(cacheDir);

        // Check if logged in
        if (!authSession.isLoggedIn) {
            /* eslint-disable-next-line no-console */
            console.log("❌ You are not currently logged in to Augment.");
            /* eslint-disable-next-line no-console */
            console.log("   Run 'auggie --login' to authenticate first.");
            process.exit(1);
        }

        // Get session to retrieve token
        const session = await authSession.getSession();
        if (!session) {
            /* eslint-disable-next-line no-console */
            console.log("❌ No active session found.");
            process.exit(1);
        }

        const sessionJson = JSON.stringify({
            accessToken: session.accessToken,
            tenantURL: session.tenantURL,
            scopes: session.scopes,
        });

        /* eslint-disable-next-line no-console */
        console.log(`🔑 Augment authentication token:

TOKEN=${sessionJson}

💡 Usage for automation:

export AUGMENT_SESSION_AUTH=$TOKEN
auggie --print 'your instruction here'

Or save to file and use --augment-token-file:

echo $TOKEN > my_token
auggie --augment-token-file my_token --print 'your instruction here'

To revoke all tokens, run: auggie --revoke-all-augment-tokens`);
    } catch (error) {
        /* eslint-disable-next-line no-console */
        console.error(
            "❌ Failed to retrieve token:",
            error instanceof Error ? error.message : String(error)
        );
        process.exit(1);
    }
}

async function handleLoginCommand(
    authURL: string,
    userAgent: string,
    cacheDir?: string
): Promise<void> {
    try {
        // Create a minimal config for OAuth (similar to main function)
        const config = {
            config: {
                apiToken: "",
                // TODO(guy) set the correct URL here
                completionURL: "https://api.augmentcode.com",
                oauth: {
                    clientID: "v",
                    url: authURL,
                },
            },
        } as AugmentConfigListener;

        // Create auth session store and OAuth flow
        const authSession = new FileBasedAuthSessionStore(cacheDir);
        const apiServer = new APIServerImpl(
            config,
            authSession,
            "login-session",
            userAgent,
            global.fetch
        );
        const oauthFlow = new OAuthFlow(config, apiServer, authSession, cacheDir);

        // Check if already logged in
        if (authSession.isLoggedIn) {
            /* eslint-disable-next-line no-console */
            console.log("⚠️  You are already logged in to Augment.");

            /* eslint-disable-next-line no-console */
            console.log("Re-authenticating will replace your current session.\n");

            // Prompt user to confirm re-authentication
            const rl = readline.createInterface({
                input: process.stdin,
                output: process.stdout,
            });

            const shouldContinue = await new Promise<boolean>((resolve) => {
                rl.question(
                    "Do you want to continue with re-authentication? This will invalidate your existing session. (y/N): ",
                    (answer: string) => {
                        rl.close();
                        const response = answer.trim().toLowerCase();
                        resolve(response === "y" || response === "yes");
                    }
                );
            });

            if (!shouldContinue) {
                /* eslint-disable-next-line no-console */
                console.log("Authentication cancelled. Your existing session remains active.");
                return;
            }

            // Remove existing session before proceeding
            /* eslint-disable-next-line no-console */
            console.log("Removing existing session...");
            await authSession.removeSession();
        }

        /* eslint-disable-next-line no-console */
        console.log("🔐 Starting Augment authentication...\n");

        // Start OAuth flow and get URL
        const authUrl = oauthFlow.startFlow();

        /* eslint-disable-next-line no-console */
        console.log("🌐 Opening authentication page in your browser...");

        // Automatically open the browser
        openBrowser(authUrl);

        /* eslint-disable-next-line no-console */
        console.log("Please complete authentication in your browser:");
        /* eslint-disable-next-line no-console */
        console.log(`\n${authUrl}\n`);
        /* eslint-disable-next-line no-console */
        console.log("After authenticating, you will receive a JSON response.");
        /* eslint-disable-next-line no-console */
        console.log("Copy the entire JSON response and paste it below.\n");

        // Prompt for JSON input
        const rl = readline.createInterface({
            input: process.stdin,
            output: process.stdout,
        });

        const jsonResponse = await new Promise<string>((resolve) => {
            rl.question("Paste the JSON response here: ", (answer: string) => {
                rl.close();
                resolve(answer.trim());
            });
        });

        // Process the JSON response
        await oauthFlow.handleAuthJson(jsonResponse);

        /* eslint-disable-next-line no-console */
        console.log("\n✅ Successfully authenticated with Augment!");
    } catch (error) {
        /* eslint-disable-next-line no-console */
        console.error("\n❌ Authentication failed:");
        /* eslint-disable-next-line no-console */
        console.error(error instanceof Error ? error.message : String(error));
        process.exit(1);
    }
}

/**
 * Loads rules from a single file path.
 * @param filePath - Path to the rules file
 * @param isOptional - If true, returns empty string if file doesn't exist. If false, throws error.
 * @returns The content of the rules file, trimmed
 */
function loadRulesFile(filePath: string, isOptional: boolean = false): string {
    const logger = getLogger("loadRulesFile");

    // Check if file exists
    if (!fs.existsSync(filePath)) {
        if (isOptional) {
            return "";
        } else {
            logger.error("Rules file not found: %s", filePath);
            throw new Error(`Rules file not found: ${filePath}`);
        }
    }

    try {
        // Read file content
        const content = fs.readFileSync(filePath, "utf8").trim();

        if (content) {
            logger.info("Loaded rules from %s (%d characters)", filePath, content.length);
        }

        return content;
    } catch (error) {
        if (isOptional) {
            // Log error but don't fail - optional files are allowed to fail
            logger.warn("Could not read optional rules file %s: %s", filePath, error);
            return "";
        } else {
            // Re-throw file reading errors for required files
            logger.error("Could not read rules file %s: %s", filePath, error);
            throw new Error(`Could not read rules file ${filePath}: ${error}`);
        }
    }
}

/**
 * Loads rules from the .augment/rules directory
 * @param workspaceRoot - Root directory of the workspace
 * @returns Array of parsed rules
 */
async function loadRulesFromDirectory(workspaceRoot: string): Promise<Rule[]> {
    const logger = getLogger("loadRulesFromDirectory");
    const rules: Rule[] = [];

    const rulesDir = path.join(workspaceRoot, AUGMENT_DIRECTORY_ROOT, AUGMENT_RULES_FOLDER);

    try {
        // Check if rules directory exists
        const stats = await fs.promises.stat(rulesDir);
        if (!stats.isDirectory()) {
            return rules;
        }

        // Read all .md files in the rules directory
        const files = await fs.promises.readdir(rulesDir);
        const markdownFiles = files.filter((file) => file.endsWith(".md"));

        for (const file of markdownFiles) {
            try {
                const filePath = path.join(rulesDir, file);
                const content = await fs.promises.readFile(filePath, "utf8");

                // Parse the rule file using RulesParser
                const parsedRule = RulesParser.parseRuleFile(content, file);
                rules.push(parsedRule);

                logger.info("Loaded rule from %s (type: %s)", file, RuleType[parsedRule.type]);
            } catch (error) {
                logger.warn("Failed to load rule file %s: %s", file, error);
            }
        }
    } catch (error) {
        // Rules directory doesn't exist or can't be read - this is fine
        logger.info("Rules directory not found or not accessible: %s", rulesDir);
    }

    return rules;
}

/**
 * Loads workspace guidelines and additional rules files, combining them into a single string.
 * Supports both legacy .augment-guidelines format and new .augment/rules/ format.
 * @param workspaceRoot - Root directory of the workspace
 * @param rulesFiles - Optional array of additional rules file paths
 * @returns Combined guidelines string
 */
async function loadAllRules(workspaceRoot: string, rulesFiles?: string[]): Promise<string> {
    const logger = getLogger("loadAllRules");
    const allRulesContent: string[] = [];

    // Load legacy workspace guidelines (optional)
    const guidelinesPath = path.join(workspaceRoot, AUGMENT_GUIDELINES_FILE);
    const workspaceGuidelines = loadRulesFile(guidelinesPath, true);
    if (workspaceGuidelines) {
        allRulesContent.push(workspaceGuidelines);
        logger.info("Loaded legacy workspace guidelines from %s", AUGMENT_GUIDELINES_FILE);
    }

    // Load new rules format from .augment/rules/ directory
    try {
        const rules = await loadRulesFromDirectory(workspaceRoot);

        // Filter to only include ALWAYS_ATTACHED rules for CLI (similar to VSCode behavior)
        const alwaysAttachedRules = rules.filter((rule) => rule.type === RuleType.ALWAYS_ATTACHED);

        for (const rule of alwaysAttachedRules) {
            allRulesContent.push(rule.content);
            logger.info("Loaded always-attached rule: %s", rule.path);
        }

        if (alwaysAttachedRules.length > 0) {
            logger.info(
                "Loaded %d always-attached rules from .augment/rules/",
                alwaysAttachedRules.length
            );
        }
    } catch (error) {
        logger.warn("Failed to load rules from .augment/rules/ directory: %s", error);
    }

    // Load additional rules files (required)
    if (rulesFiles && rulesFiles.length > 0) {
        for (const rulesFile of rulesFiles) {
            // Resolve the path relative to current working directory
            const rulesPath = path.resolve(rulesFile);
            const rulesContent = loadRulesFile(rulesPath, false);
            if (rulesContent) {
                allRulesContent.push(rulesContent);
            }
        }
    }

    // Combine all rules content
    return allRulesContent.join("\n\n");
}

/**
 * Check if stdin has data available and read it if present.
 * This is used to support piped input like: echo "data" | augment "process this"
 *
 * IMPORTANT: This function should only be called when we're sure we won't need
 * interactive readline later, as it modifies the stdin stream state.
 */
async function readStdinIfAvailable(): Promise<string | null> {
    // Check if stdin is a TTY (interactive terminal)
    // If it's a TTY, there's no piped data
    // Note: In some environments, isTTY might be undefined, so check explicitly
    if (process.stdin.isTTY === true) {
        return null;
    }

    // For text mode, we can safely read all stdin
    // For interactive mode, we should be more careful
    return new Promise((resolve) => {
        let stdinData = "";
        let hasData = false;

        // Set a timeout to detect if there's actually piped data
        // If no data arrives quickly, assume it's interactive mode
        const timeout = setTimeout(() => {
            if (!hasData) {
                cleanup();
                resolve(null);
            }
        }, 100); // Give enough time for piped data to arrive

        const cleanup = () => {
            clearTimeout(timeout);
            // Remove the data handler to avoid interfering with future readline usage
            process.stdin.removeListener("data", dataHandler);
        };

        process.stdin.setEncoding("utf8");

        const dataHandler = (chunk: any) => {
            hasData = true;
            clearTimeout(timeout); // Clear the "no data" timeout since we got data
            stdinData += chunk;
            // Don't resolve here - wait for 'end' event to get all chunks
        };

        const endHandler = () => {
            cleanup();
            resolve(hasData ? stdinData.trim() : null);
        };

        const errorHandler = () => {
            cleanup();
            resolve(null);
        };

        // Use once() for end and error to avoid permanent listeners
        process.stdin.once("end", endHandler);
        process.stdin.once("error", errorHandler);
        process.stdin.on("data", dataHandler);

        // Resume stdin to start reading
        process.stdin.resume();
    });
}

async function parseArgs(): Promise<ParsedArgs> {
    let yargsInstance = yargs(hideBin(process.argv)).usage(
        "Auggie CLI Agent by Augment Code\n\n$0 [instruction] [options]"
    ).epilogue(`Examples:
  $0 "Fix the login bug"
    Runs the agent in interactive mode with an initial instruction.
  $0 --print "Run tests"
    Automation mode.
  echo 'data' | $0 --ni 'process this'
    Piped inputs.

Environment Variables:
  AUGMENT_SESSION_AUTH  OAuth session data (same format as ~/.augment/session.json)
  AUGMENT_API_URL       Backend API endpoint
  AUGMENT_API_TOKEN     Authentication token
  GITHUB_API_TOKEN      GitHub API token

Quick Start:
  1. Get token: $0 --print-augment-token
  2. Set env: export AUGMENT_SESSION_AUTH='...'
  3. Use file: $0 --augment-token-file ~/.augment/token
  4. Revoke tokens: $0 --revoke-all-augment-tokens`);

    // Apply option modules
    yargsInstance = addAuthenticationOptions(yargsInstance);
    yargsInstance = addConfigurationOptions(yargsInstance);
    yargsInstance = addInputOptions(yargsInstance);
    yargsInstance = addOutputOptions(yargsInstance);
    yargsInstance = addSessionOptions(yargsInstance);
    yargsInstance = addToolsOptions(yargsInstance);

    const result = await yargsInstance
        .strict()
        .check((argv) => {
            // Skip validation for login, logout, delete-saved-sessions, or revoke-tokens flags
            if (
                argv.login ||
                argv.logout ||
                argv["delete-saved-sessions"] ||
                argv["revoke-tokens"]
            ) {
                return true;
            }

            // Validate MCP server options are mutually exclusive
            if (argv["mcp-server"] && argv["mcp-config"]) {
                throw new Error(
                    "--mcp-server and --mcp-config are mutually exclusive. Use one or the other."
                );
            }

            // If interactive mode is explicitly set, instruction/config is optional (will prompt for first instruction)
            if (argv.interactive) {
                return true;
            }

            // Check if any instruction is provided (positional or file)
            const optionsCount = [argv.instruction, argv["instruction-file"]].filter(
                Boolean
            ).length;

            // If non-interactive mode is explicitly set, we need some form of instruction
            // This can be from command line, file, or stdin (which we'll check later)
            if (argv["non-interactive"]) {
                if (optionsCount > 1) {
                    throw new Error("Cannot specify both instruction and --instruction-file");
                }
                // Note: We allow optionsCount === 0 here because stdin might provide the instruction
                // The actual validation for having some instruction will happen in runAgent after checking stdin
                return true;
            }

            // Interactive mode is the default - instruction is optional (will prompt for first instruction if not provided)
            return true;
        })
        .parserConfiguration({
            "boolean-negation": false, // Disable automatic --no- negation
        })
        .version("version", "Show version number", CLI_VERSION)
        .alias("version", "v")
        .help()
        .alias("help", "h")
        .parse();

    return result as unknown as ParsedArgs;
}

// Core agent runner function that can be called with parsed arguments
async function runAgent(parsedArgs: ParsedArgs, userAgent: string, outputConfig: OutputConfig) {
    // Use provided arguments instead of parsing
    const args = parsedArgs;

    // Now create the logger after configuration
    const logger: AugmentLogger = getLogger("Beachhead");

    // Get options
    let apiToken = await readApiToken(args["augment-cache-dir"], args["augment-token-file"]);
    const githubApiToken = await readGitHubApiToken(args["github-api-token"]);
    const workspaceInfo = await detectWorkspaceRoot(args["workspace-root"]);
    const folderRoot = workspaceInfo.path;
    const isTemporaryWorkspace = workspaceInfo.isTemporary;

    // Show temporary workspace warning if applicable
    if (isTemporaryWorkspace) {
        /* eslint-disable-next-line no-console */
        console.log(`
⚠️  No workspace root found. Created temporary workspace at:
   ${folderRoot}

   This temporary workspace will be deleted when the agent exits.
   For persistent work, specify a workspace with --workspace-root or run from a git repository.
`);
    }

    // Load workspace guidelines early (before authentication checks)
    let workspaceGuidelines = "";
    if (folderRoot) {
        workspaceGuidelines = await loadAllRules(folderRoot, args.rules);
    }

    // Get instruction from command line or file
    let instruction = args["instruction"];
    const instructionFile = args["instruction-file"];

    if (instructionFile) {
        instruction = await readInstructionFromFile(instructionFile, logger, true);
    }

    // Check for stdin input
    let stdinContent: string | null = null;
    // Only read stdin in text-mode mode
    if (outputConfig.mode === OutputMode.TEXT) {
        stdinContent = await readStdinIfAvailable();
    } else {
        // In interactive mode, check if user is trying to pipe input and show helpful message
        const hasPipedInput = await readStdinIfAvailable();
        if (hasPipedInput) {
            /* eslint-disable-next-line no-console */
            console.log(`
❌ Piped input is not supported in interactive mode.
Please use one of these options:
  • Print mode: echo "data" | auggie --print "additional instruction"
  • Save to file: echo "data" > input.txt && auggie "process the file input.txt"
  • Interactive mode: auggie (then type your instruction)
`);
            process.exit(1);
        }
    }

    // Combine stdin content with instruction if both are present
    if (stdinContent) {
        if (instruction) {
            // Combine stdin content with the instruction
            //
            // A comment on the order:
            // Expect the instruction to be something like "process the data from stdin"
            // and the stdin content to be the data to process, so instruction should
            // probably come first. This way, the model will be able to spend its
            // compute more effectively when processing the data, because it will be
            // able to attend to the instruction. (The implicit assumption here is
            // that the data will typically have many more tokens than the instruction.)
            instruction = `${instruction}\n\n${stdinContent}`;
            logger.info(
                "Combined stdin input (%d chars) with command line instruction",
                stdinContent.length
            );
        } else {
            // Use stdin content as the instruction
            instruction = stdinContent;
            logger.info("Using stdin input as instruction (%d chars)", stdinContent.length);
        }
    }

    // For CLI we use a client-generated remote agent ID
    const remoteAgentId = uuidv4();
    let apiUrl = args["api-url"] || process.env.AUGMENT_API_URL;

    // Check if OAuth session is available and get tenantURL from it
    let oauthAuth: FileBasedAuthSessionStore | undefined;
    oauthAuth = new FileBasedAuthSessionStore(args["augment-cache-dir"]);
    if (oauthAuth.isLoggedIn && !apiUrl) {
        try {
            const session = await oauthAuth.getSession();
            if (session?.tenantURL) {
                apiUrl = session.tenantURL;
                logger.info("Using API URL from OAuth session: %s", apiUrl);
            }
        } catch (error) {
            logger.error("Failed to read OAuth session: %s", error);
        }
    }

    // Validate authentication - we need either OAuth login OR (API token + API URL)
    const hasOAuthAuth = oauthAuth?.isLoggedIn;
    const hasApiTokenAuth = apiToken && apiUrl;

    // If we don't have any authentication yet, set a default API URL to create
    // the config object that will be updated after login through the TUI
    if (!hasOAuthAuth && !hasApiTokenAuth) {
        apiUrl = "";
        apiToken = "";
    }

    let startingNodes: ChatRequestNode[] = [];
    let agentMemories = "";
    let userGuidelines = "";
    let modelId = "";

    // Set env vars (required for sub agents tool)
    process.env.AUGMENT_API_URL = apiUrl;
    process.env.AUGMENT_API_TOKEN = apiToken;

    // Parse MCP server configurations from command line arguments
    let mcpServers: McpServerConfig[] = [];
    try {
        mcpServers = await parseMcpServerConfigurations(args["mcp-config"]);
        if (mcpServers.length > 0) {
            logger.info("Loaded %d MCP servers from command line", mcpServers.length);
            for (const server of mcpServers) {
                logger.info(
                    "  - %s (%s): %s",
                    server.name || "unnamed",
                    server.type,
                    server.type === "stdio" ? server.command : server.url
                );
            }
        }
    } catch (error) {
        logger.error("Failed to parse MCP server configurations: %s", error);
        /* eslint-disable-next-line no-console */
        console.error(
            "\n❌ MCP server configuration error:",
            error instanceof Error ? error.message : String(error)
        );
        process.exit(1);
    }

    if (instruction) {
        startingNodes = makeMessageNodes(instruction);
        logger.info("Using instruction from command line: %s", instruction);
    } else if (outputConfig.mode === OutputMode.TUI) {
        // In TUI mode without initial instruction, start with empty nodes
        startingNodes = [];
        logger.info("Interactive mode: will prompt for first instruction");
    } else {
        throw new Error(
            "No instruction provided. In text mode, provide an instruction via command line argument, --instruction-file, or stdin."
        );
    }
    logger.info("Starting process at root: %s", folderRoot);

    // Create config object
    const config = {
        config: {
            apiToken: apiToken,
            completionURL: apiUrl,
            chat: {
                url: apiUrl,
            },
            // NOTE(mpauly): Though we don't use nextEdit, we need to provide these urls fields as
            // they are expected by the APIServer for some endpoints (e.g. checkpointBlobs)
            nextEdit: {
                url: "",
                locationUrl: "",
                generationUrl: "",
            },
        },
    } as AugmentConfigListener;

    // Set up authentication
    let auth: AuthSessionStore;

    // Try OAuth first, then fall back to API token
    const fileBasedAuth = new FileBasedAuthSessionStore(args["augment-cache-dir"]);

    if (fileBasedAuth.isLoggedIn) {
        // Use OAuth session if available
        auth = fileBasedAuth;
        logger.info("Using OAuth authentication");
    } else if (apiToken) {
        // Fall back to API token
        if (!apiUrl) {
            throw new Error("apiUrl must be set when initializing config");
        }
        auth = {
            useOAuth: false,
            getSession: () => {
                return Promise.resolve({
                    accessToken: apiToken,
                    scopes: [],
                    tenantURL: apiUrl,
                });
            },
            saveSession: () => Promise.resolve(),
            removeSession: () => Promise.resolve(),
        };
        logger.info("Using API token authentication");
    } else if (outputConfig.mode === OutputMode.TUI) {
        // In TUI mode, we can prompt for authentication
        auth = fileBasedAuth;
        logger.info("Using interactive authentication");
    } else {
        // No authentication available
        throw new Error(
            "No authentication available. Please run 'auggie --login' or set AUGMENT_SESSION_AUTH or AUGMENT_API_TOKEN environment variable."
        );
    }

    // Use the remote agent ID as the session ID
    const sessionId = remoteAgentId;
    logger.info("Using remote agent ID as session ID: %s", sessionId);

    // Log the user agent that was passed in
    const isOutputToTerminal = process.stdout.isTTY === true;
    logger.info("Using user agent: %s (output to terminal: %s)", userAgent, isOutputToTerminal);

    // Initialize the API server
    const apiServer = new APIServerImpl(config, auth, sessionId, userAgent, global.fetch);

    // Get live feature flags from the API
    let featureFlags: FeatureFlags;
    let clientFeatureFlags: IClientFeatureFlags;
    try {
        logger.info("Fetching live feature flags from get-models API...");
        const modelConfig = await apiServer.getModelConfig();
        const result = convertToFeatureFlags(modelConfig);
        featureFlags = result.featureFlags;
        clientFeatureFlags = result.clientFeatureFlags;
        logger.info("Successfully loaded live feature flags");
    } catch (error: unknown) {
        // Fail early if we don't have permission
        if (error instanceof APIError && error.status === APIStatus.permissionDenied) {
            /* eslint-disable-next-line no-console */
            console.error(
                "\n❌ Auggie CLI is in closed beta. If you're part of an Enterprise organization and would like to get access, contact: <EMAIL>. For non-enterprise users, sign up for the waitlist at augment.new."
            );
            process.exit(1);
        }

        logger.warn("Failed to fetch live feature flags, using defaults: %s", error);
        // Fallback to default feature flags if API call fails
        featureFlags = defaultFeatureFlags;
        clientFeatureFlags = createClientFeatureFlags(featureFlags);
    }

    const workspaceManager = new BeachheadWorkspaceManagerImpl(folderRoot, apiServer, featureFlags);

    // Initialize the client interfaces
    setLibraryClientWorkspaces(new BeachheadClientWorkspaces(workspaceManager));
    setLibraryAPIClient(new SidecarAPIClient(apiServer, workspaceManager));
    setLibraryClientFeatureFlags(clientFeatureFlags);

    // Setup checkpoint manager
    // TODO(AU-8675): Integrate the checkpointing implementation from the beachhead workspace
    // manager to work with the sidecar aggregate checkpoint manager
    const mockShardStorage: ShardedStorage<SerializedStore> = {
        save: () => Promise.resolve(),
        load: () => Promise.resolve(undefined),
        saveShard: () => Promise.resolve(),
        loadShard: () => Promise.resolve(undefined),
        saveManifest: () => Promise.resolve(),
        loadManifest: () => Promise.resolve(undefined),
        deleteShard: () => Promise.resolve(),
    } as unknown as ShardedStorage<SerializedStore>;
    const checkpointManager = new AggregateCheckpointManager(
        mockShardStorage,
        () => undefined, // getAgentMemoriesAbsPath
        () => ({ dispose: () => {} }), // onDocumentChange
        () => ({ dispose: () => {} }), // onFileDeleted
        () => ({ dispose: () => {} }) // onFileDidMove
    );

    // Setup tools model
    const beachheadUnsupportedSidecarTools = new Set([SidecarToolType.remember]);

    // Create a tool use request event reporter
    const toolUseRequestEventReporter = new ToolUseRequestEventReporter();
    toolUseRequestEventReporter.enableUpload(); // Enable upload
    logger.info("Tool use request event reporter initialized");

    // Initialize session manager
    const sessionManager = new SessionManager(
        args["augment-cache-dir"],
        !args["dont-save-session"]
    );

    let initialState: AgentState | undefined = undefined;

    // Handle session resumption
    if (args["continue"]) {
        try {
            const lastSessionMetadata = await sessionManager.getLastSessionMetadata();
            if (lastSessionMetadata) {
                initialState = await sessionManager.restoreAgentState(
                    lastSessionMetadata.sessionId
                );
                logger.info(
                    "Resumed session: %s (created: %s, %d exchanges)",
                    lastSessionMetadata.sessionId,
                    lastSessionMetadata.created,
                    lastSessionMetadata.exchangeCount
                );
                /* eslint-disable-next-line no-console */
                console.log(
                    `📂 Resumed session from ${new Date(lastSessionMetadata.created).toLocaleString()} (${lastSessionMetadata.exchangeCount} exchanges)`
                );
            } else {
                /* eslint-disable-next-line no-console */
                console.log("⚠️  No previous session found. Starting a new session.");
            }
        } catch (error) {
            logger.error("Failed to resume session: %s", error);
            /* eslint-disable-next-line no-console */
            console.error(
                "❌ Failed to resume session:",
                error instanceof Error ? error.message : String(error)
            );
            /* eslint-disable-next-line no-console */
            console.log("Starting a new session instead.");
        }
    }

    if (!initialState) {
        initialState = new AgentState(
            remoteAgentId,
            userGuidelines,
            workspaceGuidelines,
            agentMemories,
            modelId
        );
    }

    // Track logged errors to prevent duplicates
    const loggedErrors = new Set<string>();

    const toolsModel = new ToolsModel(
        mcpServers,
        beachheadToolHostFactory(
            workspaceManager.workspaceRoot,
            false,
            parsedArgs["enable-sub-agent-tool"] && !parsedArgs["sub-agent-mode"],
            outputConfig.mode === OutputMode.TUI,
            false,
            initialState
        ),
        new BeachheadRemoteInfo(apiServer, false, githubApiToken),
        (details: ToolStartupError) => {
            // Filter out harmless "Client is closing" errors during startup
            const errorMessage = details.error || details.stderr || "Unknown error";
            if (errorMessage.includes("Client is closing")) {
                logger.debug(
                    "MCP client restart during startup (harmless): %s",
                    JSON.stringify(details)
                );
                return;
            }

            // Create a unique key for this error to prevent duplicates
            const errorKey = `${details.command || "unknown"}:${errorMessage}`;
            if (loggedErrors.has(errorKey)) {
                // Already logged this exact error, skip duplicate
                return;
            }
            loggedErrors.add(errorKey);

            logger.error("MCP tool startup error: %s", JSON.stringify(details));
            // Also log to console for immediate user feedback
            /* eslint-disable-next-line no-console */
            console.error(`⚠️  MCP server startup error: ${errorMessage}`);
            if (details.command) {
                /* eslint-disable-next-line no-console */
                console.error(`   Command: ${details.command} ${details.args?.join(" ") || ""}`);
            }
        },
        clientFeatureFlags,
        checkpointManager,
        () => Promise.resolve(agentMemories),
        () => undefined, // getMemoriesAbsPath
        () => toolUseRequestEventReporter, // Return the tool use request event reporter
        new DummyToolApprovalConfigManager(),
        {
            unsupportedSidecarTools: beachheadUnsupportedSidecarTools,
        }
    );
    toolsModel.setMode(ChatMode.cliAgent);

    // Log successful MCP server initialization
    if (mcpServers.length > 0) {
        logger.info("ToolsModel initialized with %d MCP servers", mcpServers.length);
    }

    // Create appropriate event listener based on mode
    let eventListener: AgentLoopEventListener | undefined;
    if (outputConfig.mode === OutputMode.TUI) {
        // TUI mode: create TUI event listener upfront
        const { createTuiEventListener } = await import("./tui");
        eventListener = createTuiEventListener();
    } else {
        // Text mode: create text event listener
        eventListener = new TextEventListener(outputConfig);
    }

    const agentLoop = new AgentLoop(
        apiServer,
        toolsModel,
        workspaceManager,
        true,
        remoteAgentId,
        featureFlags,
        undefined,
        initialState,
        true,
        undefined, // sshConnectionFinder (not needed in CLI mode)
        eventListener,
        args["sub-agent-mode"]
    );

    // Run at the the workspace root. This isn't critical but will be more obvious if the agent creates
    // unintentional files relative to itself.
    process.chdir(folderRoot);

    // Start the workspace manager and wait for initial indexing to complete
    await workspaceManager.initialize();
    logger.info("Initial workspace indexing complete");

    logger.info("Startup complete");

    try {
        if (outputConfig.mode === OutputMode.TUI) {
            const session = await auth.getSession();
            const isLoggedIn = !!session;

            // TUI mode: interactive full-screen interface
            await tui({
                workspaceRoot: folderRoot,
                agentLoop: agentLoop,
                apiServer: apiServer,
                sessionManager: sessionManager,
                instruction: instruction,
                version: CLI_VERSION,
                toolsModel: toolsModel,
                isLoggedIn: isLoggedIn,
                onUpdateAuth: async (accessToken, tenantURL) => {
                    await auth.saveSession(accessToken, tenantURL);

                    // Update the config with the new tenant URL
                    (config.config as any).completionURL = tenantURL;
                    (config.config.chat as any).url = tenantURL;
                },
                onLogout: () => handleLogoutCommand(args["augment-cache-dir"]),
            });
        } else {
            // Text mode: run once and exit
            await agentLoop.run(startingNodes);
            logger.info("Agent loop completed one iteration");

            // Save session after agent loop completes
            try {
                await sessionManager.saveSession(agentLoop.agentState);
                logger.debug("Session saved successfully after agent loop completion");
            } catch (error) {
                logger.error("Failed to save session after agent loop completion: %s", error);
                // Don't fail the entire operation if session saving fails
            }
        }
    } catch (error) {
        logger.error("Agent loop failed: %s", error);

        // For interactive modes (TUI and interactive CLI), let them handle their own errors
        // Only handle errors here for text mode
        if (outputConfig.mode === OutputMode.TEXT) {
            // Special handling for different types of API errors in CLI
            if (error instanceof APIError) {
                if (error.status === APIStatus.permissionDenied) {
                    /* eslint-disable-next-line no-console */
                    console.error(
                        "\n❌ Auggie CLI is in closed beta. If you're part of an Enterprise organization and would like to get access, contact: <EMAIL>. For non-enterprise users, sign up for the waitlist at augment.new."
                    );
                } else if (error.status === APIStatus.invalidArgument) {
                    /* eslint-disable-next-line no-console */
                    console.error(`\n❌ Invalid request (400 Bad Request): ${error.message}`);
                } else if (error.status === APIStatus.unauthenticated) {
                    /* eslint-disable-next-line no-console */
                    console.error(`\n❌ Authentication failed: ${error.message}`);
                    /* eslint-disable-next-line no-console */
                    console.error("   Run 'auggie --login' to authenticate first.");
                } else if (error.status === APIStatus.augmentUpgradeRequired) {
                    /* eslint-disable-next-line no-console */
                    console.error(
                        "\n❌ Client upgrade required. Please update to the latest version to continue."
                    );
                } else if (error.status === APIStatus.resourceExhausted) {
                    /* eslint-disable-next-line no-console */
                    console.error("\n❌ Rate limit exceeded: ", error.message);
                } else {
                    /* eslint-disable-next-line no-console */
                    console.error(`\n❌ API error (${APIStatus[error.status]}):`, error.message);
                }
            } else {
                /* eslint-disable-next-line no-console */
                console.error(
                    "\n❌ Agent execution failed:",
                    error instanceof Error ? error.message : String(error)
                );
            }

            // Save session even when agent loop fails, as there might be partial progress
            try {
                await sessionManager.saveSession(agentLoop.agentState);
                logger.debug("Session saved successfully after agent loop failure");
            } catch (sessionError) {
                logger.error("Failed to save session after agent loop failure: %s", sessionError);
                // Don't fail the entire operation if session saving fails
            }

            // Clean up temporary workspace if it was created
            if (isTemporaryWorkspace) {
                try {
                    await fs.promises.rm(folderRoot, { recursive: true, force: true });
                    logger.info("Cleaned up temporary workspace: %s", folderRoot);
                } catch (cleanupError) {
                    logger.warn("Failed to clean up temporary workspace: %s", cleanupError);
                }
            }

            process.exit(1);
        } else {
            // For interactive modes, re-throw the error so the interactive loop can handle it
            throw error;
        }
    }

    // TODO(AU-11815): Determine the root cause of workspaceManager.dispose() taking 1 minute plus
    // on large repos
    // workspaceManager.dispose();

    // Clean up temporary workspace if it was created
    if (isTemporaryWorkspace) {
        try {
            await fs.promises.rm(folderRoot, { recursive: true, force: true });
            logger.info("Cleaned up temporary workspace: %s", folderRoot);
        } catch (error) {
            logger.warn("Failed to clean up temporary workspace: %s", error);
        }
    }
}

// Main async function to allow awaiting
async function main() {
    const args = await parseArgs();

    const outputConfig = buildOutputConfig(args);

    // Determine user agent early for all flows
    const isOutputToTerminal =
        process.stdout.isTTY === true && outputConfig.mode === OutputMode.TUI;
    const userAgentSuffix = isOutputToTerminal ? "interactive" : "noninteractive";
    const userAgent = `augment.cli/${CLI_VERSION}/${userAgentSuffix}`;

    configureLogging(outputConfig.log_settings.file, outputConfig.log_settings.level);

    // Handle delete-saved-sessions flag
    if (args["delete-saved-sessions"]) {
        await handleDeleteSavedSessionsCommand(args["augment-cache-dir"]);
        process.exit(0);
    }

    // Compute auth URL once for all authentication operations
    const authURL = args["login-url"] || "https://auth.augmentcode.com";

    // Handle logout flag
    if (args.logout) {
        await handleLogoutCommand(args["augment-cache-dir"]);
        process.exit(0);
    }

    // Handle revoke-all-augment-tokens flag
    if (args["revoke-all-augment-tokens"]) {
        await handleRevokeTokensCommand(authURL, userAgent, args["augment-cache-dir"]);
        process.exit(0);
    }

    // Handle print-augment-token flag
    if (args["print-augment-token"]) {
        await handlePrintTokenCommand(args["augment-cache-dir"]);
        process.exit(0);
    }

    // Handle login flag
    if (args.login) {
        await handleLoginCommand(authURL, userAgent, args["augment-cache-dir"]);

        // After successful login, continue to interactive mode (which is the default)
        /* eslint-disable-next-line no-console */
        console.log("Starting interactive session...\n");

        // Remove login flag - interactive mode is already the default
        args.login = false;
    }

    // Run the agent with parsed arguments (skip logging config since it's already done)
    try {
        await runAgent(args, userAgent, outputConfig);
    } catch (error) {
        // eslint-disable-next-line no-console
        console.error(`Error: ${error instanceof Error ? error.message : String(error)}`);
        process.exit(1);
    }
}

// Export main for use by the entry point file
export { main, loadAllRules };
