import { APIError } from "@augment-internal/sidecar-libs/src/exceptions";
import { APIStatus } from "@augment-internal/sidecar-libs/src/utils/types";
import { useCallback, useEffect, useRef, useState } from "react";

import { AgentLoop, makeMessageNodes } from "../../agent_loop/agent_loop";
import { SessionManager } from "../../session-manager";
import { TUIAgentEventListener } from "../utils/agent-event-listener";
import { splitContent } from "../utils/streaming-utils";
import { useTranscript } from "./useTranscript";

export enum ProcessingState {
    Idle = "idle",
    Requesting = "requesting",
    Responding = "responding",
    Waiting = "waiting",
}

interface UseAgentReturn {
    processingState: ProcessingState;
    isInitialized: boolean;
    initializationError: string | null;
    submitQuery: (query: string) => Promise<void>;
    interruptAgent: () => void;
    transcript: ReturnType<typeof useTranscript>["transcript"];
    addSystemEntry: (message: string, hasBorder: boolean) => void;
    requestStartTime: number | undefined;
    hideInput: boolean;
}

export function useAgent(
    agentLoop: AgentLoop,
    sessionManager: SessionManager,
    onExit?: () => void
): UseAgentReturn {
    const [processingState, setProcessingState] = useState<ProcessingState>(ProcessingState.Idle);
    const [requestStartTime, setRequestStartTime] = useState<number | undefined>(undefined);
    const [hideInput, setHideInput] = useState<boolean>(false);
    const { transcript, addUserEntry, addAgentEntry, addToolEntry, addErrorEntry, addSystemEntry } =
        useTranscript();

    // Track streaming state across entire response
    const isStreamingRef = useRef(false);
    const streamingBufferRef = useRef("");

    // Update ref when state changes
    useEffect(() => {
        // TODO(mpauly): Updating the callbacks like this doesn't seem right. Can we instead
        // construct the agent loop inside the TUI such that the correct callbacks can be
        // provided on initialization?

        // Get the existing event listener from the agent loop
        // It should already be a TUIAgentEventListener if we're in TUI mode
        const existingListener = agentLoop.getEventListener();

        if (!existingListener || !(existingListener instanceof TUIAgentEventListener)) {
            throw new Error(
                "TUI event listener not properly initialized. Expected TUIAgentEventListener to be set on AgentLoop."
            );
        }

        // Update the existing TUI event listener with our callbacks
        existingListener.updateCallbacks({
            onThinkingStart: () => {
                setProcessingState(ProcessingState.Requesting);
                setRequestStartTime(Date.now());
            },
            onThinkingStop: () => {
                // Don't set to idle here - let other states handle it
            },
            onAssistantResponseStart: () => {
                setProcessingState(ProcessingState.Responding);
                streamingBufferRef.current = "";
                isStreamingRef.current = false; // First chunk will not be streaming
            },
            onAssistantResponseChunk: (text) => {
                // Add the new text to any remaining text in the buffer
                const currentBuffer = streamingBufferRef.current + text;

                const [completeChunk, remaining] = splitContent(currentBuffer);

                if (completeChunk.length > 0) {
                    // Found a split point - add the completed chunk to the transcript
                    addAgentEntry(completeChunk, isStreamingRef.current);
                    streamingBufferRef.current = remaining;
                    if (remaining.length > 0) {
                        isStreamingRef.current = true;
                    }
                } else {
                    // No split point found - keep buffering
                    streamingBufferRef.current = remaining; // which is the full content
                }
            },
            onAssistantResponseEnd: () => {
                // Flush any remaining content in the buffer
                if (streamingBufferRef.current.length > 0) {
                    addAgentEntry(streamingBufferRef.current, isStreamingRef.current);
                    streamingBufferRef.current = "";
                }
                isStreamingRef.current = false;
            },
            onToolCallStart: (toolName, toolInput) => {
                addToolEntry({
                    toolName,
                    phase: "start",
                    input: toolInput,
                });
                setProcessingState(ProcessingState.Waiting);
            },
            onToolCallResult: (toolName, toolResult) => {
                addToolEntry({
                    toolName,
                    phase: "result",
                    output: {
                        text: toolResult.text,
                        isError: toolResult.isError,
                    },
                });
            },
            onAgentLoopComplete: () => {
                setProcessingState(ProcessingState.Idle);
                setRequestStartTime(undefined);

                // Save session automatically after successful agent run (fire and forget)
                sessionManager.saveSession(agentLoop.agentState).catch((error) => {
                    console.error("Failed to save session after agent loop completion:", error);
                });
            },
            onError: (error, context) => {
                const errorMessage = error instanceof Error ? error.message : String(error);
                addErrorEntry(errorMessage, context);
                setProcessingState(ProcessingState.Idle);
                setRequestStartTime(undefined);

                // Helper function to handle fatal errors that should cause TUI to exit
                const handleFatalError = (message: string) => {
                    console.error(message);
                    setHideInput(true); // Hide the input area before exiting
                    if (onExit) {
                        // Use Ink's exit mechanism for proper cleanup
                        setTimeout(() => onExit(), 100); // Small delay to ensure message is displayed
                    } else {
                        process.exit(1);
                    }
                };

                // Check for fatal errors that should cause the TUI to exit
                if (error instanceof APIError) {
                    if (error.status === APIStatus.unauthenticated) {
                        handleFatalError(
                            "❌ Authentication failed. Please run 'augment --login' to authenticate."
                        );
                        return;
                    } else if (error.status === APIStatus.augmentUpgradeRequired) {
                        handleFatalError(
                            "❌ Client upgrade required. Please update to the latest version of Augment to continue."
                        );
                        return;
                    } else if (error.status === APIStatus.permissionDenied) {
                        handleFatalError(
                            "❌ Auggie CLI is in closed beta. If you're part of an Enterprise organization and would like to get access, contact: <EMAIL>. For non-enterprise users, sign up for the waitlist at augment.new."
                        );
                        return;
                    } else if (error.status === APIStatus.resourceExhausted) {
                        handleFatalError("❌ Rate limit exceeded: " + error.message);
                        return;
                    }
                }

                // Save session automatically even after errors, as there might be partial progress (fire and forget)
                sessionManager.saveSession(agentLoop.agentState).catch((sessionError) => {
                    console.error("Failed to save session after agent loop error:", sessionError);
                });
            },
            onMaxIterationsExceeded: (maxIterations) => {
                // Add a message to the transcript explaining what happened
                addAgentEntry(
                    `⚠️ The conversation has been paused because maximum iterations reached (${maxIterations}). You can continue the conversation by sending another message.`
                );
                setProcessingState(ProcessingState.Idle);
            },
        });
    }, [addAgentEntry, addToolEntry, addErrorEntry, sessionManager, agentLoop]);

    const submitQuery = useCallback(
        async (query: string): Promise<void> => {
            try {
                // Add user query to transcript
                addUserEntry(query);

                // Convert query to message nodes and run agent
                const messageNodes = makeMessageNodes(query);

                // TODO(mpauly): This probably isn't how we should be sending follow up messages,
                // since it makes it possible to have multiple instances of the run function
                // executing simultaneously, which isn't supported behavior.
                await agentLoop.run(messageNodes);
            } catch (error) {
                if (
                    error instanceof APIError &&
                    (error.status === APIStatus.unauthenticated ||
                        error.status === APIStatus.augmentUpgradeRequired ||
                        error.status === APIStatus.permissionDenied)
                ) {
                    // Just reset the UI state and let the agent loop error handler deal with the exit
                    setProcessingState(ProcessingState.Idle);
                    setRequestStartTime(undefined);
                    return; // Don't add error entry here, let onError callback handle it
                }

                // For other errors, handle them normally
                const errorMessage = error instanceof Error ? error.message : String(error);
                console.error("Error submitting query:", errorMessage);
                addErrorEntry(errorMessage, "Failed to submit query");
                setProcessingState(ProcessingState.Idle);
                setRequestStartTime(undefined);
            }
        },
        [agentLoop, addUserEntry, addErrorEntry]
    );

    const interruptAgent = useCallback(async () => {
        await agentLoop.interrupt();
        addErrorEntry(
            "Interrupted by the user",
            "Send 'continue' to keep going, or send a new message."
        );
        setProcessingState(ProcessingState.Idle);
        setRequestStartTime(undefined);

        // Save session automatically after interruption
        sessionManager.saveSession(agentLoop.agentState).catch((error) => {
            console.error("Failed to save session after interruption:", error);
        });
    }, [agentLoop, addErrorEntry, sessionManager]);

    return {
        processingState,
        isInitialized: true,
        initializationError: null,
        submitQuery,
        interruptAgent,
        transcript,
        addSystemEntry,
        requestStartTime,
        hideInput,
    };
}
