import { TUIAgentEventListener } from "../../utils/agent-event-listener";

describe("useAgent - Max Iterations Integration", () => {
    test("TUIAgentEventListener can handle onMaxIterationsExceeded callback", () => {
        const mockCallback = jest.fn();
        const listener = new TUIAgentEventListener({
            onMaxIterationsExceeded: mockCallback,
        });

        // Test that the method exists and can be called
        expect(typeof listener.onMaxIterationsExceeded).toBe("function");

        // Call the method
        listener.onMaxIterationsExceeded?.(200);

        // Verify the callback was called with correct parameters
        expect(mockCallback).toHaveBeenCalledWith(200);
    });

    test("TUIAgentEventListener handles missing onMaxIterationsExceeded callback", () => {
        const listener = new TUIAgentEventListener({});

        // Should not throw when callback is missing
        expect(() => {
            listener.onMaxIterationsExceeded?.(150);
        }).not.toThrow();
    });

    test("TUIAgentEventListener updateCallbacks can add onMaxIterationsExceeded", () => {
        const listener = new TUIAgentEventListener({});
        const mockCallback = jest.fn();

        // Update callbacks to include onMaxIterationsExceeded
        listener.updateCallbacks({
            onMaxIterationsExceeded: mockCallback,
        });

        // Call the method
        listener.onMaxIterationsExceeded?.(100);

        // Verify the callback was called
        expect(mockCallback).toHaveBeenCalledWith(100);
    });
});
