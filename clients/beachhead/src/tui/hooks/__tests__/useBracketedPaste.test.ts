import { InputBufferActions } from "../../types/InputBufferTypes";
import { KeyProcessor, ProcessingContext } from "../../types/KeyProcessorTypes";
import { bracketedPasteProcessor } from "../useBracketedPaste";

describe("useBracketedPaste", () => {
    let processor: KeyProcessor;
    let pasteBufferRef: any;
    let isPastingRef: any;

    beforeEach(() => {
        processor = bracketedPasteProcessor();
        pasteBufferRef = { current: "" };
        isPastingRef = { current: false };
    });

    describe("canHandle", () => {
        it("should handle bracketed paste start sequences", () => {
            expect(processor.canHandle({ input: "[200~", key: {} }, {})).toBe(true);
            expect(processor.canHandle({ input: "\x1b[200~", key: {} }, {})).toBe(true);
        });

        it("should handle bracketed paste end sequences", () => {
            expect(processor.canHandle({ input: "[201~", key: {} }, {})).toBe(true);
            expect(processor.canHandle({ input: "\x1b[201~", key: {} }, {})).toBe(true);
        });

        it("should handle any input when in paste mode", () => {
            isPastingRef.current = true;
            const context: ProcessingContext = {
                isPastingRef,
                pasteBufferRef,
            };
            expect(processor.canHandle({ input: "any content", key: {} }, context)).toBe(true);
        });

        it("should not handle regular input when not in paste mode", () => {
            expect(processor.canHandle({ input: "regular text", key: {} }, {})).toBe(false);
        });
    });

    describe("process", () => {
        describe("complete paste in single input", () => {
            it("should handle complete paste with escape sequence", () => {
                const context: ProcessingContext = {
                    isPastingRef,
                    pasteBufferRef,
                };
                const result = processor.process(
                    { input: "\x1b[200~Hello World\x1b[201~", key: {} },
                    context
                );

                expect(result.handled).toBe(true);
                expect(result.actions).toHaveLength(2);
                expect(result.actions[0]).toEqual(InputBufferActions.startPaste());
                expect(result.actions[1].type).toBe("END_PASTE");
                expect((result.actions[1] as any).finalContent).toEqual("Hello World");
            });

            it("should handle complete paste without escape sequence", () => {
                const context: ProcessingContext = {
                    isPastingRef,
                    pasteBufferRef,
                };
                const result = processor.process(
                    { input: "[200~Hello World[201~", key: {} },
                    context
                );

                expect(result.handled).toBe(true);
                expect(result.actions).toHaveLength(2);
                expect(result.actions[0]).toEqual(InputBufferActions.startPaste());
                expect(result.actions[1].type).toBe("END_PASTE");
                expect((result.actions[1] as any).finalContent).toEqual("Hello World");
            });
        });

        describe("chunked paste handling", () => {
            it("should handle paste start with initial content", () => {
                const context: ProcessingContext = {
                    isPastingRef,
                    pasteBufferRef,
                };
                const result = processor.process(
                    { input: "\x1b[200~First chunk", key: {} },
                    context
                );

                expect(result.handled).toBe(true);
                expect(result.actions).toHaveLength(1);
                expect(result.actions[0]).toEqual(InputBufferActions.startPaste());
                expect(pasteBufferRef.current).toBe("First chunk");
                expect(isPastingRef.current).toBe(true);
            });

            it("should append content when in paste mode", () => {
                isPastingRef.current = true;
                pasteBufferRef.current = "First chunk";
                const context: ProcessingContext = {
                    isPastingRef,
                    pasteBufferRef,
                };
                const result = processor.process({ input: " second chunk", key: {} }, context);

                expect(result.handled).toBe(true);
                expect(result.actions).toHaveLength(0);
                expect(pasteBufferRef.current).toBe("First chunk second chunk");
            });

            it("should handle paste end with final content", () => {
                isPastingRef.current = true;
                pasteBufferRef.current = "First chunk second chunk";
                const context: ProcessingContext = {
                    isPastingRef,
                    pasteBufferRef,
                };
                const result = processor.process(
                    { input: " final chunk\x1b[201~", key: {} },
                    context
                );

                expect(result.handled).toBe(true);
                expect(result.actions).toHaveLength(1);
                expect(result.actions[0].type).toBe("END_PASTE");
                expect((result.actions[0] as any).finalContent).toEqual(
                    "First chunk second chunk final chunk"
                );
                expect(isPastingRef.current).toBe(false);
                expect(pasteBufferRef.current).toEqual("");
            });

            it("should handle paste end marker arriving alone", () => {
                isPastingRef.current = true;
                pasteBufferRef.current = "All content already buffered";
                const context: ProcessingContext = {
                    isPastingRef,
                    pasteBufferRef,
                };
                const result = processor.process({ input: "\x1b[201~", key: {} }, context);

                expect(result.handled).toBe(true);
                expect(result.actions).toHaveLength(1);
                expect(result.actions[0].type).toBe("END_PASTE");
                expect((result.actions[0] as any).finalContent).toEqual(
                    "All content already buffered"
                );
                expect(isPastingRef.current).toBe(false);
                expect(pasteBufferRef.current).toEqual("");
            });

            it("should handle very long text that gets chunked", () => {
                const context: ProcessingContext = {
                    isPastingRef,
                    pasteBufferRef,
                };

                // Create a very long text (10KB worth of content)
                const longText = "Lorem ipsum dolor sit amet, consectetur adipiscing elit. ".repeat(
                    200
                );

                // First chunk: start marker + first part of content
                const firstChunk = "\x1b[200~" + longText.substring(0, 1000);
                const result1 = processor.process({ input: firstChunk, key: {} }, context);

                expect(result1.handled).toBe(true);
                expect(result1.actions).toHaveLength(1);
                expect(result1.actions[0]).toEqual(InputBufferActions.startPaste());
                expect(isPastingRef.current).toBe(true);
                expect(pasteBufferRef.current).toEqual(longText.substring(0, 1000));

                // Second chunk: middle content
                const secondChunk = longText.substring(1000, 2000);
                const result2 = processor.process({ input: secondChunk, key: {} }, context);

                expect(result2.handled).toBe(true);
                expect(result2.actions).toHaveLength(0); // Just buffering
                expect(isPastingRef.current).toBe(true);
                expect(pasteBufferRef.current).toEqual(longText.substring(0, 2000));

                // Third chunk: more middle content
                const thirdChunk = longText.substring(2000, 3000);
                const result3 = processor.process({ input: thirdChunk, key: {} }, context);

                expect(result3.handled).toBe(true);
                expect(result3.actions).toHaveLength(0); // Just buffering
                expect(isPastingRef.current).toBe(true);
                expect(pasteBufferRef.current).toEqual(longText.substring(0, 3000));

                // Final chunk: remaining content + end marker
                const finalChunk = longText.substring(3000) + "\x1b[201~";
                const result4 = processor.process({ input: finalChunk, key: {} }, context);

                expect(result4.handled).toBe(true);
                expect(result4.actions).toHaveLength(1);
                expect(result4.actions[0].type).toBe("END_PASTE");
                expect((result4.actions[0] as any).finalContent).toEqual(longText);
                expect(isPastingRef.current).toBe(false);
                expect(pasteBufferRef.current).toEqual("");
            });
        });

        describe("edge cases", () => {
            it("should handle content after paste end marker", () => {
                isPastingRef.current = true;
                pasteBufferRef.current = "Paste content";
                const context: ProcessingContext = {
                    isPastingRef,
                    pasteBufferRef,
                };
                const result = processor.process(
                    { input: " final\x1b[201~extra", key: {} },
                    context
                );

                expect(result.handled).toBe(true);
                expect(result.actions).toHaveLength(1);
                expect(result.actions[0].type).toBe("END_PASTE");
                expect((result.actions[0] as any).finalContent).toEqual("Paste content final");
                expect(isPastingRef.current).toBe(false);
                expect(pasteBufferRef.current).toEqual("");
                // Note: "extra" content after end marker is not processed
            });

            it("should handle empty paste", () => {
                const context: ProcessingContext = {
                    isPastingRef,
                    pasteBufferRef,
                };
                const result = processor.process({ input: "\x1b[200~\x1b[201~", key: {} }, context);

                expect(result.handled).toBe(true);
                expect(result.actions).toHaveLength(2);
                expect(result.actions[0]).toEqual(InputBufferActions.startPaste());
                expect(result.actions[1].type).toBe("END_PASTE");
                expect((result.actions[1] as any).finalContent).toEqual("");
            });

            it("should not handle input without paste markers when not in paste mode", () => {
                const context: ProcessingContext = {
                    isPastingRef,
                    pasteBufferRef,
                };
                const result = processor.process({ input: "regular text", key: {} }, context);

                expect(result.handled).toBe(false);
                expect(result.actions).toHaveLength(0);
            });
        });
    });
});
