import {
    ChatRequestNode,
    ChatRequestNodeType,
} from "@augment-internal/sidecar-libs/src/chat/chat-types";

import { ExchangeState } from "../../../agent_loop/state";
import {
    extractUserMessagesFromChatHistory,
    extractUserMessagesFromRequestNodes,
} from "../useInputHistory";

describe("extractUserMessagesFromChatHistory", () => {
    it("should extract messages from request_message field", () => {
        const chatHistory: ExchangeState[] = [
            {
                exchange: {
                    request_message: "Hello world",
                    response_text: "Hi there",
                    request_id: "1",
                },
                completed: true,
                sequenceId: 1,
                finishedAt: "2023-01-01T00:00:00Z",
                changedFiles: [],
                changedFilesSkipped: [],
                changedFilesSkippedCount: 0,
            },
            {
                exchange: {
                    request_message: "How are you?",
                    response_text: "I'm fine",
                    request_id: "2",
                },
                completed: true,
                sequenceId: 2,
                finishedAt: "2023-01-01T00:01:00Z",
                changedFiles: [],
                changedFilesSkipped: [],
                changedFilesSkippedCount: 0,
            },
        ];

        const result = extractUserMessagesFromChatHistory(chatHistory);
        expect(result).toEqual(["Hello world", "How are you?"]);
    });

    it("should extract messages from request_nodes TEXT nodes", () => {
        const chatHistory: ExchangeState[] = [
            {
                exchange: {
                    request_message: "",
                    response_text: "Response",
                    request_id: "1",
                    request_nodes: [
                        {
                            id: 0,
                            type: ChatRequestNodeType.TEXT,
                            text_node: {
                                content: "Message from node",
                            },
                        } as ChatRequestNode,
                        {
                            id: 1,
                            type: ChatRequestNodeType.IDE_STATE,
                        } as ChatRequestNode,
                    ],
                },
                completed: true,
                sequenceId: 1,
                finishedAt: "2023-01-01T00:00:00Z",
                changedFiles: [],
                changedFilesSkipped: [],
                changedFilesSkippedCount: 0,
            },
        ];

        const result = extractUserMessagesFromChatHistory(chatHistory);
        expect(result).toEqual(["Message from node"]);
    });

    it("should prefer request_message over request_nodes", () => {
        const chatHistory: ExchangeState[] = [
            {
                exchange: {
                    request_message: "Message from field",
                    response_text: "Response",
                    request_id: "1",
                    request_nodes: [
                        {
                            id: 0,
                            type: ChatRequestNodeType.TEXT,
                            text_node: {
                                content: "Message from node",
                            },
                        } as ChatRequestNode,
                    ],
                },
                completed: true,
                sequenceId: 1,
                finishedAt: "2023-01-01T00:00:00Z",
                changedFiles: [],
                changedFilesSkipped: [],
                changedFilesSkippedCount: 0,
            },
        ];

        const result = extractUserMessagesFromChatHistory(chatHistory);
        expect(result).toEqual(["Message from field"]);
    });

    it("should skip empty messages and trim whitespace", () => {
        const chatHistory: ExchangeState[] = [
            {
                exchange: {
                    request_message: "  Valid message  ",
                    response_text: "Response",
                    request_id: "1",
                },
                completed: true,
                sequenceId: 1,
                finishedAt: "2023-01-01T00:00:00Z",
                changedFiles: [],
                changedFilesSkipped: [],
                changedFilesSkippedCount: 0,
            },
            {
                exchange: {
                    request_message: "",
                    response_text: "Response",
                    request_id: "2",
                },
                completed: true,
                sequenceId: 2,
                finishedAt: "2023-01-01T00:01:00Z",
                changedFiles: [],
                changedFilesSkipped: [],
                changedFilesSkippedCount: 0,
            },
        ];

        const result = extractUserMessagesFromChatHistory(chatHistory);
        expect(result).toEqual(["Valid message"]);
    });

    it("should skip non-TEXT nodes", () => {
        const chatHistory: ExchangeState[] = [
            {
                exchange: {
                    request_message: "",
                    response_text: "Response",
                    request_id: "1",
                    request_nodes: [
                        {
                            id: 0,
                            type: ChatRequestNodeType.TOOL_RESULT,
                        } as ChatRequestNode,
                        {
                            id: 1,
                            type: ChatRequestNodeType.TEXT,
                            text_node: {
                                content: "User message",
                            },
                        } as ChatRequestNode,
                    ],
                },
                completed: true,
                sequenceId: 1,
                finishedAt: "2023-01-01T00:00:00Z",
                changedFiles: [],
                changedFilesSkipped: [],
                changedFilesSkippedCount: 0,
            },
        ];

        const result = extractUserMessagesFromChatHistory(chatHistory);
        expect(result).toEqual(["User message"]);
    });

    it("should handle empty chat history", () => {
        const result = extractUserMessagesFromChatHistory([]);
        expect(result).toEqual([]);
    });
});

describe("extractUserMessagesFromRequestNodes", () => {
    it("should extract messages from TEXT nodes", () => {
        const requestNodes: ChatRequestNode[] = [
            {
                id: 0,
                type: ChatRequestNodeType.TEXT,
                text_node: {
                    content: "Current user message",
                },
            } as ChatRequestNode,
            {
                id: 1,
                type: ChatRequestNodeType.IDE_STATE,
            } as ChatRequestNode,
        ];

        const result = extractUserMessagesFromRequestNodes(requestNodes);
        expect(result).toEqual(["Current user message"]);
    });

    it("should skip non-TEXT nodes", () => {
        const requestNodes: ChatRequestNode[] = [
            {
                id: 0,
                type: ChatRequestNodeType.TOOL_RESULT,
            } as ChatRequestNode,
            {
                id: 1,
                type: ChatRequestNodeType.TEXT,
                text_node: {
                    content: "User message",
                },
            } as ChatRequestNode,
        ];

        const result = extractUserMessagesFromRequestNodes(requestNodes);
        expect(result).toEqual(["User message"]);
    });

    it("should handle empty request nodes", () => {
        const result = extractUserMessagesFromRequestNodes([]);
        expect(result).toEqual([]);
    });

    it("should trim whitespace and skip empty messages", () => {
        const requestNodes: ChatRequestNode[] = [
            {
                id: 0,
                type: ChatRequestNodeType.TEXT,
                text_node: {
                    content: "  Valid message  ",
                },
            } as ChatRequestNode,
        ];

        const result = extractUserMessagesFromRequestNodes(requestNodes);
        expect(result).toEqual(["Valid message"]);
    });
});
