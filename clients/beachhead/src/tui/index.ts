import { ToolsModel } from "@augment-internal/sidecar-libs/src/tools/tools-model";
import { render } from "ink";
import React from "react";

import { AgentLoop, AgentLoopEventListener } from "../agent_loop/agent_loop";
import { APIServer } from "../augment-api";
import { SessionManager } from "../session-manager";
import { loadContext } from "./context";
import { App } from "./ui/App";
import { TUIAgentEventListener } from "./utils/agent-event-listener";

export interface TuiOptions {
    sessionManager: SessionManager;
    agentLoop: AgentLoop;
    apiServer: APIServer;
    instruction?: string;
    workspaceRoot: string;
    version: string;
    toolsModel: ToolsModel;
    isLoggedIn: boolean;
    onUpdateAuth: (accessToken: string, tenantURL: string) => Promise<void>;
    onLogout: () => Promise<void>;
}

/**
 * Creates a TUI event listener that can be passed to the AgentLoop constructor.
 * The returned listener will have its callbacks updated by the TUI components.
 */
export function createTuiEventListener(): AgentLoopEventListener {
    return new TUIAgentEventListener();
}

export function tui(options: TuiOptions): Promise<void> {
    const ctx = loadContext(options);

    return new Promise<void>((resolve) => {
        const { waitUntilExit } = render(React.createElement(App, ctx), { exitOnCtrlC: false });

        // Wait for the app to exit naturally (e.g., when user presses Ctrl+C or app decides to exit)
        waitUntilExit().then(() => {
            resolve();
        });
    });
}
