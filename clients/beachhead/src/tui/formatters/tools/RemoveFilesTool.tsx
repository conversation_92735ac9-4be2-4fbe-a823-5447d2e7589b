import { Box, Text } from "ink";
import React from "react";

import { ToolEntry } from "../../types/transcript-types";
import { ResultItem, ResultSummary, ToolResult, ToolStart } from "../components";
import { pluralizeWithCount } from "../utils/pluralize";

const parseOutput = (outputText: string): {
    removedFiles: string[];
} => {
    // Extract the removed files from the output text
    const removedFilesMatch = outputText.match(/File\(s\) removed: (.+)/);
    const removedFilesStr = removedFilesMatch?.[1] || "";
    const removedFiles = removedFilesStr.split(", ").filter(Boolean);
    
    return { removedFiles };
};

export const RemoveFilesTool = React.memo<{ entry: ToolEntry }>(({ entry }) => {
    if (entry.phase === "start") {
        const filePaths = entry.input?.file_paths || [];
        const fileCount = filePaths.length;
        const label = `Removing ${pluralizeWithCount("file", fileCount)}`;

        return <ToolStart label={label} description="remove files" />;
    }

    // Result phase
    const hasError = entry.output?.isError || false;
    const outputText = entry.output?.text || "";

    // Parse the output to get removed files
    const { removedFiles } = parseOutput(outputText);

    return (
        <ToolResult>
            <ResultSummary
                text={
                    hasError
                        ? "Failed to remove"
                        : removedFiles.length === 1
                        ? `Removed ${removedFiles[0]}`
                        : `Removed ${pluralizeWithCount("file", removedFiles.length)}`
                }
                color={hasError ? "red" : undefined}
            />
            {hasError && outputText && (
                <ResultItem>
                    <Text color="red">{outputText}</Text>
                </ResultItem>
            )}
            {!hasError && removedFiles.length > 1 && (
                <Box flexDirection="column" marginLeft={2}>
                    {removedFiles.map((file, index) => (
                        <ResultItem key={index}>
                            <Text color="white">{file}</Text>
                        </ResultItem>
                    ))}
                </Box>
            )}
        </ToolResult>
    );
});
