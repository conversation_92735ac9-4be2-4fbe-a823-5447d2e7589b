import { Box, Text } from "ink";
import React from "react";

import { ToolEntry } from "../../types/transcript-types";
import { ResultSummary, ToolResult, ToolStart } from "../components";

// Interface for render-mermaid tool start payload
export interface RenderMermaidStart extends ToolEntry {
    toolName: "render-mermaid";
    phase: "start";
    input: {
        title: string;
        diagram_definition: string;
    };
}

const parseOutput = (
    outputText: string
): {
    diagramDefinition: string | null;
    hasError: boolean;
} => {
    try {
        const parsed = JSON.parse(outputText);
        if (parsed.type === "mermaid_diagram" && parsed.diagram_definition) {
            // Replace \n with actual newlines and <br/> with spaces for proper rendering
            const diagramDefinition = parsed.diagram_definition
                .replace(/\\n/g, "\n")
                .replace(/<br\/>/g, " ");
            return {
                diagramDefinition,
                hasError: false,
            };
        }
        return {
            diagramDefinition: null,
            hasError: true,
        };
    } catch (error) {
        return {
            diagramDefinition: null,
            hasError: true,
        };
    }
};

export const RenderMermaidTool = React.memo<{ entry: ToolEntry }>(({ entry }) => {
    if (entry.phase === "start") {
        const startEntry = entry as RenderMermaidStart;
        const title = startEntry.input.title;
        return <ToolStart label={title} description="render mermaid diagram" />;
    }

    // Result phase
    const hasError = entry.output?.isError || false;
    const outputText = entry.output?.text || "";

    if (hasError) {
        return (
            <ToolResult>
                <ResultSummary text={outputText || "Failed to render diagram"} color="red" />
            </ToolResult>
        );
    }

    // Parse the output
    const { diagramDefinition, hasError: parseError } = parseOutput(outputText);

    if (parseError || !diagramDefinition) {
        return (
            <ToolResult>
                <ResultSummary text="Failed to parse diagram output" color="red" />
            </ToolResult>
        );
    }

    return (
        <ToolResult>
            <ResultSummary text="Mermaid diagram rendered" />

            <Box flexDirection="column" marginLeft={2} marginTop={1}>
                <Box flexDirection="column" marginTop={1}>
                    {diagramDefinition.split("\n").map((line, index) => (
                        <Text key={index} color="white" dimColor>
                            {line}
                        </Text>
                    ))}
                </Box>
            </Box>
        </ToolResult>
    );
});
