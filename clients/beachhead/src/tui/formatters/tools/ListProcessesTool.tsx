import { Text } from "ink";
import React from "react";

import { ToolEntry } from "../../types/transcript-types";
import { ResultItem, ResultSummary, ToolResult, ToolStart } from "../components";

interface ProcessInfo {
    terminalNumber: string;
    command: string;
}

const parseOutput = (outputText: string): {
    summaryLine: string | null;
    processes: ProcessInfo[];
} => {
    const lines = outputText.split("\n").filter((line) => line.trim());
    const summaryLine = lines.find((line) => line.includes("process") && line.includes("running")) || null;
    
    const processes: ProcessInfo[] = [];
    const processLines = lines.filter((line) => line.startsWith("Terminal"));
    
    processLines.forEach((processLine) => {
        const match = processLine.match(/Terminal (\d+) \[[^\]]+\]: (.+)/);
        if (match) {
            const [, terminalNumber, command] = match;
            processes.push({ terminalNumber, command });
        }
    });
    
    return { summaryLine, processes };
};

export const ListProcessesTool = React.memo<{ entry: ToolEntry }>(({ entry }) => {
    if (entry.phase === "start") {
        return <ToolStart label="Current processes" description="list processes" />;
    }

    // Result phase
    const hasError = entry.output?.isError || false;
    const outputText = entry.output?.text || "";

    if (hasError) {
        return (
            <ToolResult>
                <ResultSummary text={outputText || "Failed to list processes"} color="red" />
            </ToolResult>
        );
    }

    // Parse the output text to extract process information
    const { summaryLine, processes } = parseOutput(outputText);

    return (
        <ToolResult>
            {summaryLine && <ResultSummary text={summaryLine} />}
            {processes.map((process, index) => (
                <ResultItem key={index}>
                    <Text color="white" dimColor>
                        [{process.terminalNumber}]: {process.command}
                    </Text>
                </ResultItem>
            ))}
        </ToolResult>
    );
});
