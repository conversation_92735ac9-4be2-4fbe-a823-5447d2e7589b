import { Text } from "ink";
import React from "react";

import { ToolEntry } from "../../types/transcript-types";
import { ResultItem, ResultSummary, ToolResult, ToolStart } from "../components";

interface SearchResult {
    title: string;
    url: string;
    snippet?: string;
}

const parseWebSearchOutput = (outputText: string): SearchResult[] => {
    const results: SearchResult[] = [];
    const lines = outputText.split("\n");

    let currentResult: Partial<SearchResult> | null = null;

    for (const line of lines) {
        const trimmedLine = line.trim();

        // Match markdown link format: - [title](url)
        const linkMatch = trimmedLine.match(/^-\s*\[([^\]]+)\]\(([^)]+)\)$/);
        if (linkMatch) {
            // Save previous result if exists
            if (currentResult && currentResult.title && currentResult.url) {
                results.push(currentResult as SearchResult);
            }

            // Start new result
            currentResult = {
                title: linkMatch[1],
                url: linkMatch[2],
            };
        } else if (trimmedLine && currentResult && !trimmedLine.startsWith("-")) {
            // This is likely a snippet line
            if (!currentResult.snippet) {
                currentResult.snippet = trimmedLine;
            } else {
                currentResult.snippet += " " + trimmedLine;
            }
        }
    }

    // Add the last result
    if (currentResult && currentResult.title && currentResult.url) {
        results.push(currentResult as SearchResult);
    }

    return results;
};

export const WebSearchTool = React.memo<{ entry: ToolEntry }>(({ entry }) => {
    if (entry.phase === "start") {
        const query = entry.input?.query || "unknown query";
        return <ToolStart label={`Searching ${query}`} description="web search" />;
    }

    // Result phase
    const hasError = entry.output?.isError || false;
    const outputText = entry.output?.text || "";

    if (hasError) {
        return (
            <ToolResult>
                <ResultSummary text={outputText} color="red" />
            </ToolResult>
        );
    }

    // Parse search results
    const searchResults = parseWebSearchOutput(outputText);

    return (
        <ToolResult>
            <ResultSummary
                text={`Found ${searchResults.length} result${searchResults.length !== 1 ? "s" : ""}`}
            />
            {searchResults.map((result, index) => (
                <ResultItem key={index}>
                    <Text color="gray">{result.url}</Text>
                </ResultItem>
            ))}
        </ToolResult>
    );
});
