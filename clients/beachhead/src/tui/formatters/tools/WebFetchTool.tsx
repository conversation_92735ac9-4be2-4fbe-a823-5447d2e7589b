import React from "react";

import { ToolEntry } from "../../types/transcript-types";
import { ResultSummary, ToolResult, ToolStart } from "../components";

const parseOutput = (
    outputText: string
): {
    firstLine: string;
} => {
    const lines = outputText.split("\n");
    const firstLine = lines[0]?.trim() || "";
    return { firstLine };
};

export const WebFetchTool = React.memo<{ entry: ToolEntry }>(({ entry }) => {
    if (entry.phase === "start") {
        const url = entry.input?.url || "unknown URL";
        return <ToolStart label={url} description="retrieving content" />;
    }

    // Result phase
    const hasError = entry.output?.isError || false;
    const outputText = entry.output?.text || "";

    if (hasError) {
        return (
            <ToolResult>
                <ResultSummary text={outputText} color="red" />
            </ToolResult>
        );
    }

    // Success case - show first line as summary
    const { firstLine } = parseOutput(outputText);

    return (
        <ToolResult>
            <ResultSummary text={`Retrieved ${firstLine}`} />
        </ToolResult>
    );
});
