import { Box } from "ink";
import React from "react";

import { ToolEntry } from "../../types/transcript-types";
import { OutputPreview, ResultSummary, ToolResult, ToolStart } from "../components";

interface ParsedViewOutput {
    lines: string[];
    totalLines: number;
}

function parseOutput(text: string): ParsedViewOutput {
    const lines = text
        .split("\n")
        .filter((line) => line.trim() !== "")
        .slice(1);
    const totalLines = lines.length;

    return {
        lines,
        totalLines,
    };
}

export const ViewTool: React.FC<{ entry: ToolEntry }> = ({ entry }) => {
    if (entry.phase === "start") {
        const path = entry.input?.path || "unknown";
        return <ToolStart label={path} description="read file" />;
    }

    // Result phase
    const outputText = entry.output?.text || "";
    const isError = entry.output?.isError || false;

    // For errors, show the error message inline
    if (isError) {
        return (
            <ToolResult>
                <ResultSummary text={outputText} color="red" />
            </ToolResult>
        );
    }

    // For successful reads, show the content
    const parsed = parseOutput(outputText);

    return (
        <ToolResult>
            <ResultSummary
                text={`Read ${parsed.totalLines} line${parsed.totalLines === 1 ? "" : "s"}`}
            />
            <Box flexDirection="column" marginLeft={2}>
                <OutputPreview lines={parsed.lines} />
            </Box>
        </ToolResult>
    );
};
