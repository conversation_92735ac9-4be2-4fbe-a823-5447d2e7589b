import { Box, Text } from "ink";
import React from "react";

import { ToolEntry } from "../../types/transcript-types";
import { ResultItem, ResultSummary, ToolResult, ToolStart } from "../components";

export const SaveFileTool = React.memo<{ entry: ToolEntry }>(({ entry }) => {
    const path = entry.input?.path || "unknown";
    const fileName = path.split("/").pop() || "unknown";

    if (entry.phase === "start") {
        return (
            <>
                <ToolStart label={fileName} description="save file" />
                <Box>
                    <ResultItem>
                        <Text dimColor>Saving at </Text>
                        <Text color="white">{path}</Text>
                    </ResultItem>
                </Box>
            </>
        );
    }

    // Result phase
    const hasError = entry.output?.isError || false;
    const outputText = entry.output?.text || "";

    // For errors, show the error message inline
    if (hasError) {
        return (
            <ToolResult>
                <ResultSummary text={outputText} color="red" />
            </ToolResult>
        );
    }

    return (
        <ToolResult>
            <ResultSummary text="Saved successfully." />
        </ToolResult>
    );
});
