import { Box, Text } from "ink";
import React from "react";

import { ToolEntry } from "../../types/transcript-types";
import { OutputPreview, ResultSummary, ToolResult, ToolStart } from "../components";

// Interface for read-process tool start payload
export interface ReadProcessStart extends ToolEntry {
    toolName: "read-process";
    phase: "start";
    input: {
        terminal_id: number;
        wait: boolean;
        max_wait_seconds: number;
    };
}

const parseOutput = (
    outputText: string
): {
    output: string;
    status: string;
} => {
    // Extract status from the first line
    const statusMatch = outputText.match(/status:\s*([^)]+)\)/);
    const status = statusMatch ? statusMatch[1] : "unknown";

    // The output might be wrapped in tags or be plain text
    const outputMatch = outputText.match(/<output>([\s\S]*?)<\/output>/);
    let output = outputMatch ? outputMatch[1].trim() : "";

    // Remove the first line if it exists (similar to LaunchProcessTool)
    if (output) {
        const lines = output.split("\n");
        if (lines.length > 1) {
            output = lines.slice(1).join("\n");
        }
    }

    return { output, status };
};

export const ReadProcessTool = React.memo<{ entry: ToolEntry }>(({ entry }) => {
    if (entry.phase === "start") {
        const startEntry = entry as ReadProcessStart;
        const terminalId = startEntry.input.terminal_id;
        return <ToolStart label={`Reading process [${terminalId}]`} description="read process" />;
    }

    // Result phase
    const hasError = entry.output?.isError || false;
    const outputText = entry.output?.text || "";

    if (hasError) {
        return (
            <ToolResult>
                <ResultSummary text={outputText || "Failed to read process"} color="red" />
            </ToolResult>
        );
    }

    // Parse the output
    const { output, status } = parseOutput(outputText);

    return (
        <ToolResult>
            <ResultSummary text={`Process ${status}`} />

            {output && (
                <Box flexDirection="column" marginLeft={2} marginTop={1}>
                    <Text color="white" dimColor>
                        Parsed output:
                    </Text>
                    <OutputPreview lines={output.split("\n")} />
                </Box>
            )}
        </ToolResult>
    );
});
