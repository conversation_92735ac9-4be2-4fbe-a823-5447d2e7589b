import { Box, Text } from "ink";
import React from "react";

interface DiffGapProps {
    hiddenLines: number;
}

export const DiffGap: React.FC<DiffGapProps> = ({ hiddenLines }) => {
    if (hiddenLines <= 0) {
        return null;
    }

    return (
        <Box
            marginLeft={2}
            paddingLeft={1}
            borderStyle="single"
            borderColor="gray"
            borderDimColor
            borderRight={false}
            borderLeft={false}
            borderTop={true}
            borderBottom={true}
        >
            <Text color="gray">
                {`(${hiddenLines} unchanged line${hiddenLines !== 1 ? "s" : ""})`}
            </Text>
        </Box>
    );
};
