import { Box, Text } from "ink";
import React from "react";

import { DiffWords } from "./DiffWords";

interface DiffLineProps {
    type: "addition" | "removal";
    lineNumber: number;
    content: string;
    maxLineNumberWidth: number;
    compareContent?: string; // Content to compare against for word-level diff
}

export const DiffLine: React.FC<DiffLineProps> = ({
    type,
    lineNumber,
    content,
    maxLineNumberWidth,
    compareContent,
}) => {
    const prefix = type === "addition" ? "+" : "-";

    const leftColumn =
        type === "removal"
            ? lineNumber.toString().padStart(maxLineNumberWidth)
            : " ".repeat(maxLineNumberWidth);
    const rightColumn =
        type === "addition"
            ? lineNumber.toString().padStart(maxLineNumberWidth)
            : " ".repeat(maxLineNumberWidth);

    return (
        <Box marginLeft={2}>
            <Text>
                <Text dimColor>
                    <Text color="red">{leftColumn} </Text>
                    <Text color="green">{rightColumn}</Text>
                </Text>
                <Text color={type === "addition" ? "green" : "red"}> {prefix} </Text>
                <DiffWords content={content} compareContent={compareContent} type={type} />
            </Text>
        </Box>
    );
};
