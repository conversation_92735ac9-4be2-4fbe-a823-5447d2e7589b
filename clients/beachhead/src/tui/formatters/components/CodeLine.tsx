import { Box, Text } from "ink";
import React from "react";

import { highlightCode } from "../utils/syntax-highlighter";

interface CodeLineProps {
    language?: string;
    children: string;
}

export const CodeLine = ({ language, children }: CodeLineProps) => {
    const highlightedCode = highlightCode(children, language);

    return (
        <Box paddingRight={2}>
            <Text>{highlightedCode}</Text>
        </Box>
    );
};
