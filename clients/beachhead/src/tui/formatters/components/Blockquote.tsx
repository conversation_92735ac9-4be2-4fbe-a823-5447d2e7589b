import { Box } from "ink";
import React from "react";

import { MarkdownText } from "./MarkdownText";

interface BlockquoteProps {
    children: string;
}

export const Blockquote = ({ children }: BlockquoteProps) => (
    <Box
        marginLeft={2}
        borderStyle="single"
        borderLeft
        borderRight={false}
        borderTop={false}
        borderBottom={false}
        borderColor="gray"
    >
        <Box paddingLeft={1}>
            <MarkdownText>{children}</MarkdownText>
        </Box>
    </Box>
);
