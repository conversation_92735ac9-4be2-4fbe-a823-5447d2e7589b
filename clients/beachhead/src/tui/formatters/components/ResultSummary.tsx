import { Box, Text } from "ink";
import React from "react";

import { ICONS } from "../utils/constants";

interface ResultSummaryProps {
    text: string;
    color?: string;
}

export const ResultSummary: React.FC<ResultSummaryProps> = ({ text, color = "white" }) => {
    return (
        <Box>
            <Text color="gray">{ICONS.CORNER} </Text>
            <Text color={color}>{text}</Text>
        </Box>
    );
};