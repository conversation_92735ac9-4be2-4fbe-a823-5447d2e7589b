import { Box } from "ink";
import React from "react";

import { MarkdownText } from "./MarkdownText";

interface MarkdownHeaderProps {
    level: number;
    children: string;
}

const HEADER_COLORS = ["cyan", "blue", "yellow", "magenta"];

export const MarkdownHeader = ({ level, children }: MarkdownHeaderProps) => {
    const color = HEADER_COLORS[Math.min(level - 1, HEADER_COLORS.length - 1)];

    return (
        <Box flexDirection="column">
            <MarkdownText bold color={color}>
                {children}
            </MarkdownText>
        </Box>
    );
};
