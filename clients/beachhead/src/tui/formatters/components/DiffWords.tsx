import { Text } from "ink";
import React from "react";

import { computeWordDiffSegments } from "../utils/diff";

interface DiffWordsProps {
    content: string;
    compareContent?: string;
    type: "addition" | "removal";
}

// Component to render content with word-level highlighting
export const DiffWords: React.FC<DiffWordsProps> = ({ content, compareContent, type }) => {
    const segments = computeWordDiffSegments(content, compareContent, type);
    const baseColor = type === "addition" ? "green" : "red";
    const backgroundColor = type === "addition" ? "green" : "red";

    return (
        <Text>
            {segments.map((segment, index) => (
                <Text
                    key={index}
                    color={segment.isHighlighted ? "black" : baseColor}
                    backgroundColor={segment.isHighlighted ? backgroundColor : undefined}
                    dimColor={segment.isHighlighted}
                >
                    {segment.text}
                </Text>
            ))}
        </Text>
    );
};
