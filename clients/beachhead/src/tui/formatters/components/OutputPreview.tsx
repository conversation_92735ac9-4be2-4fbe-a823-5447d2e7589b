import { Box, Text } from "ink";
import React from "react";

interface OutputPreviewProps {
    /** Array of lines to display */
    lines: string[];
    /** Maximum number of lines to show before truncating (default: 10) */
    maxLines?: number;
    /** Color for the output text (default: "gray") */
    color?: string;
}

/**
 * OutputPreview component for displaying text output with automatic truncation.
 *
 * This component is designed to handle potentially long output from tools like
 * launch-process and read-process. It provides a clean, consistent way to display
 * output while preventing the terminal from being overwhelmed by very long results.
 *
 * **Truncation Behavior:**
 * - Shows the first `maxLines` lines of output (default: 10)
 * - If there are more lines than `maxLines`, displays a yellow truncation indicator
 * - The truncation indicator shows "+X more lines" where X is the number of hidden lines
 * - Uses a dimmed yellow color to make the indicator noticeable but not intrusive
 *
 * **Usage Examples:**
 * ```tsx
 * // Basic usage with default 10-line limit
 * <OutputPreview lines={output.split("\n")} />
 *
 * // Custom line limit and color
 * <OutputPreview lines={lines} maxLines={20} color="white" />
 * ```
 *
 * @param props - Component props
 * @returns JSX element displaying the output with optional truncation
 */
export const OutputPreview: React.FC<OutputPreviewProps> = ({
    lines,
    maxLines = 10,
    color = "gray",
}) => {
    const displayLines = lines.slice(0, maxLines);
    const remainingLines = lines.length - maxLines;

    return (
        <Box flexDirection="column">
            {displayLines.map((line, index) => (
                <Text key={index} color={color}>
                    {line}
                </Text>
            ))}

            {remainingLines > 0 && (
                <Text color="yellow" dimColor>
                    +{remainingLines} more line{remainingLines === 1 ? "" : "s"}
                </Text>
            )}
        </Box>
    );
};
