import { Box, Text } from "ink";
import React from "react";

import { MarkdownText } from "./MarkdownText";

interface BulletListProps {
    items: string[];
}

export const BulletList = ({ items }: BulletListProps) => (
    <Box marginLeft={2}>
        <Text color="yellow">• </Text>
        <MarkdownText>{items[0]}</MarkdownText>
    </Box>
);

interface NumberedListProps {
    items: string[];
    startIndex?: number;
}

export const NumberedList = ({ items, startIndex = 1 }: NumberedListProps) => (
    <Box marginLeft={2}>
        <Text color="yellow">{startIndex}. </Text>
        <MarkdownText>{items[0]}</MarkdownText>
    </Box>
);
