import {
    InsertLineEntryRaw,
    StrReplaceEntryRaw,
} from "@augment-internal/sidecar-libs/src/tools/sidecar-tools/str-replace-editor-tool/utils";
import { Box } from "ink";
import React from "react";

import { detectGaps, processDiffEntries, sortDiffLines } from "../utils/diff";
import { DiffGap } from "./DiffGap";
import { DiffLine } from "./DiffLine";
import { DiffSummary } from "./DiffSummary";

interface DiffProps {
    entries: StrReplaceEntryRaw[] | InsertLineEntryRaw[];
    path: string;
    type: "str_replace" | "insert";
}

export const Diff: React.FC<DiffProps> = ({ entries, path, type }) => {
    // Process diff entries using utility function
    const {
        diffLines: diffData,
        totalAdditions,
        totalRemovals,
        maxLineNumber,
    } = processDiffEntries(entries, type);
    const maxLineNumberWidth = maxLineNumber.toString().length;

    // Sort diff data and components together
    const sortedDiffData = sortDiffLines(diffData);
    const sortedDiffLineComponents = sortedDiffData.map((data) => (
        <DiffLine
            key={data.key}
            type={data.type}
            lineNumber={data.lineNumber}
            content={data.content}
            maxLineNumberWidth={maxLineNumberWidth}
            compareContent={data.compareContent}
        />
    ));

    // Detect gaps and insert gap indicators
    const gaps = detectGaps(sortedDiffData);
    const diffLinesWithGaps: React.ReactElement[] = [];

    sortedDiffLineComponents.forEach((diffLine, index) => {
        // Check if we need a gap indicator before this line
        const gapInfo = gaps.get(index);
        if (gapInfo) {
            diffLinesWithGaps.push(
                <DiffGap
                    key={`gap-${gapInfo.afterLineNumber}-${gapInfo.beforeLineNumber}`}
                    hiddenLines={gapInfo.hiddenLines}
                />
            );
        }

        diffLinesWithGaps.push(diffLine);
    });

    return (
        <Box flexDirection="column">
            <DiffSummary
                path={path}
                totalAdditions={totalAdditions}
                totalRemovals={totalRemovals}
            />
            {diffLinesWithGaps.length > 0 && (
                <Box flexDirection="column" marginLeft={2}>
                    {diffLinesWithGaps}
                </Box>
            )}
        </Box>
    );
};
