import { Text } from "ink";
import React from "react";

import { ICONS } from "../utils/constants";

interface StatusIndicatorProps {
    success: boolean;
    returnCode?: number;
}

export const StatusIndicator: React.FC<StatusIndicatorProps> = ({ success, returnCode }) => (
    <>
        <Text color={success ? "green" : "red"}> {ICONS.STATUS}</Text>
        {returnCode !== undefined && returnCode !== 0 && (
            <Text color="red"> (exit code {returnCode})</Text>
        )}
    </>
);
