import { Box, Text } from "ink";
import React from "react";

import { highlightCode } from "../utils/syntax-highlighter";

interface CodeBlockProps {
    code: string;
    language?: string;
    path?: string;
    mode?: string;
}

export const CodeBlock = React.memo<CodeBlockProps>(({ code, language, path, mode }) => {
    const highlightedCode = React.useMemo(() => highlightCode(code, language), [code, language]);

    return (
        <Box flexDirection="column" paddingRight={2} marginBottom={1}>
            {path && (
                <Text color="grey" italic bold>
                    {path} {mode && `(${mode})`}
                </Text>
            )}
            <Box paddingRight={2}>
                <Text>{highlightedCode}</Text>
            </Box>
        </Box>
    );
});
