import { Box, Text } from "ink";
import React from "react";

import {
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON>et<PERSON>ist,
    CodeLine,
    CodeMetadata,
    HorizontalRule,
    <PERSON><PERSON>Header,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    NumberedList,
} from "../components";

export interface CodeFenceAttributes {
    lang?: string;
    path?: string;
    mode?: string;
}

export interface MarkdownTextSegment {
    type: "text" | "strong" | "emphasis" | "code" | "link";
    content: string;
    url?: string;
}

// Parse code fence attributes from a line like ```typescript path=file.ts mode=EXCERPT
export const parseCodeFenceAttributes = (line: string): CodeFenceAttributes => {
    const match = line.match(/^`{3,}(\w+)?\s*(.*)?$/);
    if (!match) {
        return {};
    }

    const lang = match[1];
    const attrs = match[2] || "";

    const attributes: CodeFenceAttributes = {};
    if (lang) {
        attributes.lang = lang;
    }

    // Parse attributes like path=value mode=value
    const attrRegex = /(\w+)=([^\s]+)/g;
    let attrMatch;
    while ((attrMatch = attrRegex.exec(attrs)) !== null) {
        const [, key, value] = attrMatch;
        if (key === "path") {
            attributes.path = value;
        }
        if (key === "mode") {
            attributes.mode = value;
        }
    }

    return attributes;
};

// Regex to match code fences (3 or more backticks, optionally followed by text)
const CODE_FENCE = /^`{3,}/;

// Render a single markdown segment
function formatTextSegment(segment: MarkdownTextSegment, key: string | number): React.ReactNode {
    switch (segment.type) {
        case "strong":
            return (
                <Text key={key} bold>
                    {segment.content}
                </Text>
            );
        case "emphasis":
            return (
                <Text key={key} italic>
                    {segment.content}
                </Text>
            );
        case "code":
            return (
                <Text key={key} color="magenta">
                    {segment.content}
                </Text>
            );
        case "link":
            return (
                <Text key={key} color="blue" underline>
                    {segment.content}
                </Text>
            );
        case "text":
        default:
            return <Text key={key}>{segment.content}</Text>;
    }
}

// Extract inline markdown matches with their positions and types
export function extractInlineMarkdownMatches(text: string): Array<{
    start: number;
    end: number;
    type: string;
    content: string;
    url?: string;
}> {
    // Regex patterns for inline formatting
    // Note: We need to be careful about overlapping patterns
    const patterns = [
        { regex: /(?<!\w)\*\*(?!\s)(.+?)(?<!\s)\*\*(?!\w)/g, type: "strong" as const },
        { regex: /(?<!\w)__(?!\s)(.+?)(?<!\s)__(?!\w)/g, type: "strong" as const },
        { regex: /(?<!\w|\*)\*(?!\*|\s)(.+?)(?<!\s)\*(?!\*|\w)/g, type: "emphasis" as const },
        { regex: /(?<!\w)_(?!_|\s)(.+?)(?<!\s)_(?!\w)/g, type: "emphasis" as const },
        { regex: /`([^`]+)`/g, type: "code" as const },
        { regex: /\[([^\]]+)\]\(([^)]+)\)/g, type: "link" as const },
    ];

    // Collect all matches with their positions
    const matches: Array<{
        start: number;
        end: number;
        type: string;
        content: string;
        url?: string;
    }> = [];

    for (const pattern of patterns) {
        const regex = new RegExp(pattern.regex);
        let match;
        while ((match = regex.exec(text)) !== null) {
            if (pattern.type === "link") {
                matches.push({
                    start: match.index,
                    end: match.index + match[0].length,
                    type: pattern.type,
                    content: match[1], // Link text
                    url: match[2], // Link URL
                });
            } else {
                matches.push({
                    start: match.index,
                    end: match.index + match[0].length,
                    type: pattern.type,
                    content: match[1], // Content inside the formatting
                });
            }
        }
    }

    // Sort matches by start position
    matches.sort((a, b) => a.start - b.start);
    return matches;
}

// Parse a line into segments with inline formatting
export function parseInlineMarkdown(text: string): MarkdownTextSegment[] {
    const segments: MarkdownTextSegment[] = [];
    const matches = extractInlineMarkdownMatches(text);

    // Build segments, handling overlaps by taking the first (leftmost) match
    let currentIndex = 0;
    const usedRanges: Array<{ start: number; end: number }> = [];

    for (const match of matches) {
        // Check if this match overlaps with any used range
        const overlaps = usedRanges.some(
            (range) => match.start < range.end && match.end > range.start
        );

        if (overlaps) {
            continue; // Skip overlapping matches
        }

        // Add any plain text before this match
        if (match.start > currentIndex) {
            const plainText = text.substring(currentIndex, match.start);
            if (plainText) {
                segments.push({ type: "text", content: plainText });
            }
        }

        // Add the formatted segment
        segments.push({
            type: match.type as MarkdownTextSegment["type"],
            content: match.content,
            url: match.url,
        });

        // Mark this range as used
        usedRanges.push({ start: match.start, end: match.end });
        currentIndex = match.end;
    }

    // Add any remaining plain text
    if (currentIndex < text.length) {
        const remainingText = text.substring(currentIndex);
        if (remainingText) {
            segments.push({ type: "text", content: remainingText });
        }
    }

    // If no formatting was found, return the entire text as a single segment
    if (segments.length === 0) {
        segments.push({ type: "text", content: text });
    }

    return segments;
}

// Parse a line into segments with inline formatting and render them
export function formatInlineMarkdown(text: string): React.ReactNode[] {
    const segments = parseInlineMarkdown(text);
    return segments.map((segment, index) => formatTextSegment(segment, index));
}

/**
 * Formats content with markdown support
 * @param content - The content to format
 * @param currentLanguage - The current code block language (null if not in code block)
 * @returns Tuple [formatted React elements, current language or null]
 */
export function formatMarkdown(
    content: string,
    currentLanguage: string | null = null
): [React.ReactNode, string | null] {
    // Trim just one trailing newline if present
    const processedContent = content.endsWith("\n") ? content.slice(0, -1) : content;
    const lines = processedContent.split("\n");
    const elements: React.ReactNode[] = [];
    let lineIndex = 0;
    let isOpenCodeBlock = currentLanguage !== null;

    // Process each line
    while (lineIndex < lines.length) {
        const currentLine = lines[lineIndex];

        // Check for code fence
        if (currentLine.trim().match(CODE_FENCE)) {
            // Code fence found - flip the state
            if (isOpenCodeBlock) {
                // Closing fence
                currentLanguage = null;
                isOpenCodeBlock = false;
            } else {
                // Opening fence - enter code block
                const attributes = parseCodeFenceAttributes(currentLine.trim());
                currentLanguage = attributes.lang || "";
                isOpenCodeBlock = true;

                if (attributes.path) {
                    elements.push(
                        <CodeMetadata
                            key={`code-metadata-${lineIndex}`}
                            path={attributes.path}
                            mode={attributes.mode}
                        />
                    );
                }
            }

            lineIndex++;
            continue;
        }

        // If we're inside a code block, treat this line as code
        if (isOpenCodeBlock) {
            elements.push(
                <CodeLine key={`code-line-${lineIndex}`} language={currentLanguage || undefined}>
                    {currentLine}
                </CodeLine>
            );
            lineIndex++;
            continue;
        }

        // Check for horizontal rules (3 or more *, -, or _ with optional spaces, no other characters)
        const trimmedLine = currentLine.trim();
        if (
            trimmedLine.match(/^(\*\s*){3,}$/) ||
            trimmedLine.match(/^(-\s*){3,}$/) ||
            trimmedLine.match(/^(_\s*){3,}$/)
        ) {
            elements.push(<HorizontalRule key={`hr-${lineIndex}`} />);
            lineIndex++;
            continue;
        }

        // Check for bullet points - render individual bullet items
        const bulletMatch = currentLine.trim().match(/^[-*•]\s+(.*)$/);
        if (bulletMatch) {
            elements.push(<BulletList key={`bullet-${lineIndex}`} items={[bulletMatch[1]]} />);
            lineIndex++;
            continue;
        }

        // Check for numbered lists - render individual numbered items
        const numberedMatch = currentLine.trim().match(/^(\d+)\.\s(.*)$/);
        if (numberedMatch) {
            const startIndex = parseInt(numberedMatch[1]);
            elements.push(
                <NumberedList
                    key={`numbered-${lineIndex}`}
                    items={[numberedMatch[2]]}
                    startIndex={startIndex}
                />
            );
            lineIndex++;
            continue;
        }

        // Check for headers
        const headerMatch = currentLine.trim().match(/^(#+)\s+(.*)$/);
        if (headerMatch) {
            const headerLevel = headerMatch[1].length;
            const headerText = headerMatch[2];
            elements.push(
                <MarkdownHeader key={`h-${lineIndex}`} level={headerLevel}>
                    {headerText}
                </MarkdownHeader>
            );
            lineIndex++;
            continue;
        }

        // Check for blockquotes
        const blockquoteMatch = currentLine.trim().match(/^>\s(.*)$/);
        if (blockquoteMatch) {
            const blockquoteText = blockquoteMatch[1];
            elements.push(
                <Blockquote key={`blockquote-${lineIndex}`}>{blockquoteText}</Blockquote>
            );
            lineIndex++;
            continue;
        }

        // Regular text with inline markdown formatting
        if (currentLine.trim()) {
            elements.push(<MarkdownText key={`text-${lineIndex}`}>{currentLine}</MarkdownText>);
        } else {
            // Empty line - always preserve it as a spacing element
            elements.push(
                <Box key={`empty-${lineIndex}`}>
                    <Text> </Text>
                </Box>
            );
        }
        lineIndex++;
    }

    return [<>{elements}</>, currentLanguage];
}
