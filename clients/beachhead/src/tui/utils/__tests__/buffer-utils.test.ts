import { buildLineMapping, calculateWrappedText } from "../buffer-utils";

describe("buffer-utils", () => {
    describe("buildLineMapping", () => {
        it("should handle empty text", () => {
            const result = buildLineMapping("", 10);
            expect(result.lines).toEqual([""]);
            expect(result.lineStartPositions).toEqual([0]);
            expect(result.lineLengths).toEqual([0]);
        });

        it("should handle zero width", () => {
            const result = buildLineMapping("hello", 0);
            expect(result.lines).toEqual([""]);
            expect(result.lineStartPositions).toEqual([0]);
            expect(result.lineLengths).toEqual([0]);
        });

        it("should handle negative width", () => {
            const result = buildLineMapping("hello", -5);
            expect(result.lines).toEqual([""]);
            expect(result.lineStartPositions).toEqual([0]);
            expect(result.lineLengths).toEqual([0]);
        });

        it("should handle single line shorter than width", () => {
            const result = buildLineMapping("hello", 10);
            expect(result.lines).toEqual(["hello"]);
            expect(result.lineStartPositions).toEqual([0]);
            expect(result.lineLengths).toEqual([5]);
        });

        it("should wrap single line longer than width", () => {
            const result = buildLineMapping("hello world", 5);
            expect(result.lines).toEqual(["hello", " worl", "d"]);
            expect(result.lineStartPositions).toEqual([0, 5, 10]);
            expect(result.lineLengths).toEqual([5, 5, 1]);
        });

        it("should handle multiple hard lines", () => {
            const result = buildLineMapping("line1\nline2\nline3", 10);
            expect(result.lines).toEqual(["line1", "line2", "line3"]);
            expect(result.lineStartPositions).toEqual([0, 6, 12]);
            expect(result.lineLengths).toEqual([5, 5, 5]);
        });

        it("should handle empty lines", () => {
            const result = buildLineMapping("line1\n\nline3", 10);
            expect(result.lines).toEqual(["line1", "", "line3"]);
            expect(result.lineStartPositions).toEqual([0, 6, 7]);
            expect(result.lineLengths).toEqual([5, 0, 5]);
        });

        it("should wrap multiple hard lines", () => {
            const result = buildLineMapping("very long line1\nshort\nvery long line3", 8);
            expect(result.lines).toEqual(["very lon", "g line1", "short", "very lon", "g line3"]);
            expect(result.lineStartPositions).toEqual([0, 8, 16, 22, 30]);
            expect(result.lineLengths).toEqual([8, 7, 5, 8, 7]);
        });

        it("should handle exact width matches", () => {
            const result = buildLineMapping("12345\n67890", 5);
            expect(result.lines).toEqual(["12345", "67890"]);
            expect(result.lineStartPositions).toEqual([0, 6]);
            expect(result.lineLengths).toEqual([5, 5]);
        });
    });

    describe("calculateWrappedText", () => {
        it("should handle empty text", () => {
            const result = calculateWrappedText("", 10, 0);
            expect(result.lines).toEqual([""]);
            expect(result.cursorLine).toBe(0);
            expect(result.cursorColumn).toBe(0);
        });

        it("should handle zero width", () => {
            const result = calculateWrappedText("hello", 0, 2);
            expect(result.lines).toEqual([""]);
            expect(result.cursorLine).toBe(0);
            expect(result.cursorColumn).toBe(0);
        });

        it("should calculate cursor position in single line", () => {
            const result = calculateWrappedText("hello world", 20, 6);
            expect(result.lines).toEqual(["hello world"]);
            expect(result.cursorLine).toBe(0);
            expect(result.cursorColumn).toBe(6);
        });

        it("should calculate cursor position in wrapped line", () => {
            const result = calculateWrappedText("hello world test", 5, 8);
            expect(result.lines).toEqual(["hello", " worl", "d tes", "t"]);
            expect(result.cursorLine).toBe(1);
            expect(result.cursorColumn).toBe(3); // Position 8 is 3rd char in " worl"
        });

        it("should handle cursor at end of text", () => {
            const result = calculateWrappedText("hello", 10, 5);
            expect(result.lines).toEqual(["hello"]);
            expect(result.cursorLine).toBe(0);
            expect(result.cursorColumn).toBe(5);
        });

        it("should handle cursor beyond text length", () => {
            const result = calculateWrappedText("hello", 10, 10);
            expect(result.lines).toEqual(["hello"]);
            expect(result.cursorLine).toBe(0);
            expect(result.cursorColumn).toBe(0); // Falls back to default when beyond bounds
        });

        it("should handle multi-line text", () => {
            const result = calculateWrappedText("line1\nline2\nline3", 10, 8);
            expect(result.lines).toEqual(["line1", "line2", "line3"]);
            expect(result.cursorLine).toBe(1);
            expect(result.cursorColumn).toBe(2); // Position 8 is 2nd char in "line2"
        });

        it("should handle cursor at line boundary", () => {
            const result = calculateWrappedText("hello\nworld", 10, 5);
            expect(result.lines).toEqual(["hello", "world"]);
            expect(result.cursorLine).toBe(0);
            expect(result.cursorColumn).toBe(5); // At end of first line
        });
    });

    describe("Integration with wrapping", () => {
        it("should handle complex mixed content", () => {
            const text = "short\nvery long line that wraps\nend";
            // Position 25: "short\n" (6) + "very long " (10) + "line that " (10) = 26, so position 25 is in "line that "
            const wrapped = calculateWrappedText(text, 10, 25);
            expect(wrapped.cursorLine).toBe(2); // Third line
            expect(wrapped.cursorColumn).toBe(9); // 9th position in "line that "
        });

        it("should handle wrapping with newlines", () => {
            const text = "line1\nvery long line that should wrap\nline3";
            const mapping = buildLineMapping(text, 10);

            expect(mapping.lines).toEqual([
                "line1",
                "very long ",
                "line that ",
                "should wra",
                "p",
                "line3",
            ]);

            expect(mapping.lineStartPositions).toEqual([0, 6, 16, 26, 36, 38]);
        });

        it("should handle very long lines with small width", () => {
            const longText = "a".repeat(100);
            const result = buildLineMapping(longText, 3);
            expect(result.lines.length).toBe(34); // 100/3 = 33.33 -> 34 lines
            expect(result.lines[0]).toBe("aaa");
            expect(result.lines[33]).toBe("a");
        });
    });
});
