import {
    clampCursorPosition,
    cleanPastedText,
    deleteTextRange,
    findWordEnd,
    findWordStart,
    insertTextAt,
    transposeChars,
} from "../text-utils";

describe("text-utils", () => {
    describe("findWordStart", () => {
        it("should find word start from middle of word", () => {
            const text = "hello world test";
            const position = 8; // Middle of "world"
            const result = findWordStart(text, position);
            expect(result).toBe(6); // Start of "world"
        });

        it("should find word start when at word boundary", () => {
            const text = "hello world test";
            const position = 6; // Start of "world"
            const result = findWordStart(text, position);
            expect(result).toBe(0); // Goes back to start of previous word "hello"
        });

        it("should skip spaces before finding word start", () => {
            const text = "hello   world";
            const position = 8; // In the spaces
            const result = findWordStart(text, position);
            expect(result).toBe(0); // Should go back to start of "hello"
        });

        it("should return 0 for position at start", () => {
            const text = "hello world";
            const position = 0;
            const result = findWordStart(text, position);
            expect(result).toBe(0);
        });

        it("should return 0 for negative position", () => {
            const text = "hello world";
            const position = -1;
            const result = findWordStart(text, position);
            expect(result).toBe(0);
        });

        it("should handle single character words", () => {
            const text = "a b c";
            const position = 2; // At "b"
            const result = findWordStart(text, position);
            expect(result).toBe(0); // Goes back to start of "a"
        });

        it("should handle text with only spaces", () => {
            const text = "   ";
            const position = 2;
            const result = findWordStart(text, position);
            expect(result).toBe(0);
        });
    });

    describe("findWordEnd", () => {
        it("should find word end from middle of word", () => {
            const text = "hello world test";
            const position = 8; // Middle of "world"
            const result = findWordEnd(text, position);
            expect(result).toBe(12); // Start of "test"
        });

        it("should find word end when at word start", () => {
            const text = "hello world test";
            const position = 6; // Start of "world"
            const result = findWordEnd(text, position);
            expect(result).toBe(12); // Start of "test"
        });

        it("should skip spaces after word", () => {
            const text = "hello   world";
            const position = 0; // Start of "hello"
            const result = findWordEnd(text, position);
            expect(result).toBe(8); // Start of "world"
        });

        it("should return text length for position at end", () => {
            const text = "hello world";
            const position = 11; // At end
            const result = findWordEnd(text, position);
            expect(result).toBe(11);
        });

        it("should return text length for position beyond end", () => {
            const text = "hello world";
            const position = 20;
            const result = findWordEnd(text, position);
            expect(result).toBe(11);
        });

        it("should handle single character words", () => {
            const text = "a b c";
            const position = 0; // At "a"
            const result = findWordEnd(text, position);
            expect(result).toBe(2); // Start of "b"
        });

        it("should handle last word without trailing space", () => {
            const text = "hello world";
            const position = 6; // Start of "world"
            const result = findWordEnd(text, position);
            expect(result).toBe(11); // End of text
        });
    });

    describe("insertTextAt", () => {
        it("should insert text at beginning", () => {
            const original = "world";
            const insert = "hello ";
            const position = 0;
            const result = insertTextAt(original, insert, position);
            expect(result).toBe("hello world");
        });

        it("should insert text in middle", () => {
            const original = "hello world";
            const insert = "beautiful ";
            const position = 6;
            const result = insertTextAt(original, insert, position);
            expect(result).toBe("hello beautiful world");
        });

        it("should insert text at end", () => {
            const original = "hello";
            const insert = " world";
            const position = 5;
            const result = insertTextAt(original, insert, position);
            expect(result).toBe("hello world");
        });

        it("should handle empty string insertion", () => {
            const original = "hello world";
            const insert = "";
            const position = 5;
            const result = insertTextAt(original, insert, position);
            expect(result).toBe("hello world");
        });

        it("should handle insertion into empty string", () => {
            const original = "";
            const insert = "hello";
            const position = 0;
            const result = insertTextAt(original, insert, position);
            expect(result).toBe("hello");
        });

        it("should handle newline insertion", () => {
            const original = "hello world";
            const insert = "\n";
            const position = 5;
            const result = insertTextAt(original, insert, position);
            expect(result).toBe("hello\n world");
        });
    });

    describe("deleteTextRange", () => {
        it("should delete text from beginning", () => {
            const text = "hello world";
            const start = 0;
            const end = 6;
            const result = deleteTextRange(text, start, end);
            expect(result).toBe("world");
        });

        it("should delete text from middle", () => {
            const text = "hello beautiful world";
            const start = 6;
            const end = 16;
            const result = deleteTextRange(text, start, end);
            expect(result).toBe("hello world");
        });

        it("should delete text from end", () => {
            const text = "hello world";
            const start = 5;
            const end = 11;
            const result = deleteTextRange(text, start, end);
            expect(result).toBe("hello");
        });

        it("should handle deleting entire text", () => {
            const text = "hello world";
            const start = 0;
            const end = 11;
            const result = deleteTextRange(text, start, end);
            expect(result).toBe("");
        });

        it("should handle zero-length deletion", () => {
            const text = "hello world";
            const start = 5;
            const end = 5;
            const result = deleteTextRange(text, start, end);
            expect(result).toBe("hello world");
        });

        it("should handle deletion with swapped start/end", () => {
            const text = "hello world";
            const start = 8;
            const end = 5;
            const result = deleteTextRange(text, start, end);
            expect(result).toBe("hello wo world"); // substring() handles swapped indices
        });

        it("should handle out-of-bounds ranges", () => {
            const text = "hello";
            const start = 0;
            const end = 20;
            const result = deleteTextRange(text, start, end);
            expect(result).toBe("");
        });
    });

    describe("transposeChars", () => {
        it("should transpose characters at cursor position", () => {
            const text = "hello";
            const cursorPosition = 2; // Between 'e' and 'l'
            const result = transposeChars(text, cursorPosition);
            expect(result.newText).toBe("hlelo");
            expect(result.newCursorPosition).toBe(3);
        });

        it("should transpose at end of text", () => {
            const text = "hello";
            const cursorPosition = 5; // At end
            const result = transposeChars(text, cursorPosition);
            expect(result.newText).toBe("hello"); // No change at boundary
            expect(result.newCursorPosition).toBe(5);
        });

        it("should not transpose at beginning", () => {
            const text = "hello";
            const cursorPosition = 0;
            const result = transposeChars(text, cursorPosition);
            expect(result.newText).toBe("hello");
            expect(result.newCursorPosition).toBe(0);
        });

        it("should not transpose with single character", () => {
            const text = "a";
            const cursorPosition = 1;
            const result = transposeChars(text, cursorPosition);
            expect(result.newText).toBe("a");
            expect(result.newCursorPosition).toBe(1);
        });

        it("should not transpose with empty text", () => {
            const text = "";
            const cursorPosition = 0;
            const result = transposeChars(text, cursorPosition);
            expect(result.newText).toBe("");
            expect(result.newCursorPosition).toBe(0);
        });

        it("should transpose with special characters", () => {
            const text = "a\nb";
            const cursorPosition = 2; // Between '\n' and 'b'
            const result = transposeChars(text, cursorPosition);
            expect(result.newText).toBe("ab\n");
            expect(result.newCursorPosition).toBe(3);
        });

        it("should handle cursor beyond text length", () => {
            const text = "hello";
            const cursorPosition = 10;
            const result = transposeChars(text, cursorPosition);
            expect(result.newText).toBe("hello");
            expect(result.newCursorPosition).toBe(10);
        });
    });

    describe("clampCursorPosition", () => {
        it("should return position within valid range", () => {
            const position = 5;
            const textLength = 10;
            const result = clampCursorPosition(position, textLength);
            expect(result).toBe(5);
        });

        it("should clamp negative position to 0", () => {
            const position = -5;
            const textLength = 10;
            const result = clampCursorPosition(position, textLength);
            expect(result).toBe(0);
        });

        it("should clamp position beyond text length", () => {
            const position = 15;
            const textLength = 10;
            const result = clampCursorPosition(position, textLength);
            expect(result).toBe(10);
        });

        it("should handle zero text length", () => {
            const position = 5;
            const textLength = 0;
            const result = clampCursorPosition(position, textLength);
            expect(result).toBe(0);
        });

        it("should handle exact boundaries", () => {
            const textLength = 10;

            expect(clampCursorPosition(0, textLength)).toBe(0);
            expect(clampCursorPosition(10, textLength)).toBe(10);
        });
    });

    describe("cleanPastedText", () => {
        it("should normalize Windows line endings", () => {
            const text = "line1\r\nline2\r\nline3";
            const result = cleanPastedText(text);
            expect(result).toBe("line1\nline2\nline3");
        });

        it("should normalize Mac line endings", () => {
            const text = "line1\rline2\rline3";
            const result = cleanPastedText(text);
            expect(result).toBe("line1\nline2\nline3");
        });

        it("should preserve Unix line endings", () => {
            const text = "line1\nline2\nline3";
            const result = cleanPastedText(text);
            expect(result).toBe("line1\nline2\nline3");
        });

        it("should remove control characters", () => {
            const text = "hello\x00\x07\x08world\x7F";
            const result = cleanPastedText(text);
            expect(result).toBe("helloworld");
        });

        it("should preserve tab and newline characters", () => {
            const text = "hello\tworld\ntest";
            const result = cleanPastedText(text);
            expect(result).toBe("hello\tworld\ntest");
        });

        it("should handle mixed line endings and control characters", () => {
            const text = "line1\x00\r\nline2\x08\rline3\x7F";
            const result = cleanPastedText(text);
            expect(result).toBe("line1\nline2\nline3");
        });

        it("should handle empty string", () => {
            const text = "";
            const result = cleanPastedText(text);
            expect(result).toBe("");
        });

        it("should handle string with only control characters", () => {
            const text = "\x00\x01\x02\x7F";
            const result = cleanPastedText(text);
            expect(result).toBe("");
        });

        it("should preserve Unicode characters", () => {
            const text = "Hello 世界\nBonjour 🌍";
            const result = cleanPastedText(text);
            expect(result).toBe("Hello 世界\nBonjour 🌍");
        });

        it("should handle complex mixed content", () => {
            const text = "Hello\x00 世界\r\n\x08Test\t\x7FContent\r";
            const result = cleanPastedText(text);
            expect(result).toBe("Hello 世界\nTest\tContent\n");
        });
    });

    describe("Edge cases and integration", () => {
        it("should handle operations on empty strings", () => {
            expect(findWordStart("", 0)).toBe(0);
            expect(findWordEnd("", 0)).toBe(0);
            expect(insertTextAt("", "test", 0)).toBe("test");
            expect(deleteTextRange("", 0, 0)).toBe("");
            expect(transposeChars("", 0)).toEqual({ newText: "", newCursorPosition: 0 });
            expect(cleanPastedText("")).toBe("");
        });

        it("should handle single character strings", () => {
            const text = "a";
            expect(findWordStart(text, 1)).toBe(0);
            expect(findWordEnd(text, 0)).toBe(1);
            expect(insertTextAt(text, "b", 0)).toBe("ba");
            expect(deleteTextRange(text, 0, 1)).toBe("");
        });

        it("should handle strings with only whitespace", () => {
            const text = "   ";
            expect(findWordStart(text, 2)).toBe(0);
            expect(findWordEnd(text, 0)).toBe(3);
            expect(insertTextAt(text, "a", 1)).toBe(" a  ");
        });

        it("should handle strings with mixed content", () => {
            const text = "hello\tworld\n\ntest  end";
            const wordStart = findWordStart(text, 15); // Middle of "test"
            const wordEnd = findWordEnd(text, 15);

            expect(wordStart).toBe(0); // Goes back to start of "hello"
            expect(wordEnd).toBe(19); // Start of "end"
        });
    });
});
