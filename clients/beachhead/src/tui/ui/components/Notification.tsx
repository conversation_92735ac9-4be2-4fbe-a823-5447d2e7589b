import { Box, Text } from "ink";
import React from "react";

import { NotificationState } from "../../types/NotificationTypes";

interface NotificationProps {
    notification: NotificationState;
}

export const Notification: React.FC<NotificationProps> = ({ notification }) => {
    if (!notification) {
        return (
            <Box paddingX={1}>
                <Text> </Text>
            </Box>
        );
    }

    const getColor = () => {
        switch (notification.type) {
            case "error":
                return "red";
            case "success":
                return "green";
            case "info":
                return "blue";
            default:
                return "red";
        }
    };

    return (
        <Box paddingX={1} marginTop={1}>
            <Text color={getColor()}>{notification.message}</Text>
        </Box>
    );
};
