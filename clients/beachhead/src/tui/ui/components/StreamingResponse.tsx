import { Box, Text } from "ink";
import React from "react";

import { ICONS } from "../../formatters/utils/constants";
import { formatMarkdown } from "../../formatters/utils/markdown";

interface StreamingResponseProps {
    content: string;
}

export const StreamingResponse: React.FC<StreamingResponseProps> = ({ content }) => {
    // Format the content with markdown support
    const formattedContent = formatMarkdown(content);

    return (
        <Box marginTop={0} marginBottom={1}>
            <Text color="white" bold>
                {ICONS.RESPONSE}
            </Text>
            <Box flexDirection="column" marginLeft={2}>
                {formattedContent}
            </Box>
        </Box>
    );
};
