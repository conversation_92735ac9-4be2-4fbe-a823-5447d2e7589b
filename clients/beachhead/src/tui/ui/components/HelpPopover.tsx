import { Box, Text } from "ink";
import React from "react";

import { VERSION } from "../../context";
import { Popover } from "./Popover";

export interface HelpPopoverProps {
    show: boolean;
    onDismiss: () => void;
    shortcutsText: string;
}

export const HelpPopover: React.FC<HelpPopoverProps> = ({ show, onDismiss, shortcutsText }) => {
    return (
        <Popover show={show} onDismiss={onDismiss} height={31} shortcutsText={shortcutsText}>
            <Box flexDirection="column" gap={1}>
                <Box flexDirection="row" justifyContent="space-between">
                    <Text bold color="yellow">
                        Auggie by Augment Code v{VERSION}
                    </Text>
                </Box>

                <Box width="50%">
                    <Text dimColor>
                        Auggie can edit files, execute tests and other commands, and
                        integrate with GitHub and other external systems. Use it to develop features
                        and tests, create and review PRs, address tickets, and more.
                    </Text>
                </Box>

                <Box>
                    <Text dimColor>
                        💡 Integrate Auggie: Use 'auggie --print "your task"'
                    </Text>
                </Box>

                <Box flexDirection="column" gap={1}>
                    <Text bold>Example prompts:</Text>
                    <Box flexDirection="column" paddingLeft={1}>
                        <Text color="gray">
                            <Text color="cyan">Make a change</Text> &gt; Update the Account type
                            with an active field
                        </Text>
                        <Text color="gray">
                            <Text color="cyan">Fix an error</Text> &gt; Fix the error in
                            @SelectMenu.tsx
                        </Text>
                        <Text color="gray">
                            <Text color="cyan">Ask a question</Text> &gt; How do i configure the
                            logger?
                        </Text>
                    </Box>
                </Box>

                <Box flexDirection="column" gap={1}>
                    <Text bold>Commands:</Text>
                    <Box flexDirection="column" paddingLeft={1}>
                        <Text color="gray">
                            <Text color="cyan">/exit</Text> - Exit the interactive session
                        </Text>
                        <Text color="gray">
                            <Text color="cyan">/help</Text> - Show this help message
                        </Text>
                    </Box>
                </Box>

                <Box flexDirection="column" gap={1} width="50%">
                    <Text bold>New line support:</Text>
                    <Box paddingLeft={1}>
                        <Text>
                            <Text dimColor>
                                Supoprt for entering new lines in the input box varies depending on
                                your terminal. All terminals can use
                            </Text>{" "}
                            <Text color="cyanBright">Ctrl+J</Text>{" "}
                            <Text dimColor>
                                to enter a new line. Some modern terminals will support
                            </Text>{" "}
                            <Text color="cyanBright">Shift+Return</Text>{" "}
                            <Text dimColor>to enter a new line automatically.</Text>
                        </Text>
                    </Box>
                </Box>

                <Box
                    flexDirection="column"
                    borderStyle="single"
                    borderLeft={false}
                    borderRight={false}
                    borderTop={true}
                    borderBottom={false}
                    borderColor="grey"
                    borderDimColor
                >
                    <Text color="gray">Read the docs at https://docs.augmentcode.com</Text>
                </Box>
            </Box>
        </Popover>
    );
};
