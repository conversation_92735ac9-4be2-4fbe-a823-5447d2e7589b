import { Box, Text } from "ink";
import Spinner from "ink-spinner";
import React, { useEffect, useState } from "react";

import { ProcessingState } from "../../hooks/useAgent";

interface ActivityIndicatorProps {
    state: ProcessingState;
    startTime?: number;
}

export const ActivityIndicator: React.FC<ActivityIndicatorProps> = ({ state, startTime }) => {
    const [seconds, setSeconds] = useState(0);

    useEffect(() => {
        if (!startTime) {
            setSeconds(0);
            return;
        }

        // Update seconds immediately
        const updateSeconds = () => {
            const elapsed = Math.floor((Date.now() - startTime) / 1000);
            setSeconds(elapsed);
        };

        updateSeconds(); // Set initial value

        // Set up interval to update every second
        const id = setInterval(updateSeconds, 1000);

        // Cleanup function
        return () => {
            clearInterval(id);
        };
    }, [startTime]);

    const getIndicatorText = (state: ProcessingState): string => {
        switch (state) {
            case ProcessingState.Requesting:
                return "Sending request...";
            case ProcessingState.Responding:
                return "Processing response...";
            case ProcessingState.Waiting:
                return "Executing tools...";
            case ProcessingState.Idle:
                return startTime ? "Processing..." : "";
            default:
                return "";
        }
    };

    const indicatorText = getIndicatorText(state);

    if (state === ProcessingState.Idle && !startTime) {
        return (
            <Box paddingLeft={1} paddingTop={1}>
                <Text> </Text>
            </Box>
        );
    }

    return (
        <Box paddingLeft={1} paddingTop={1}>
            <Text color="cyan">
                <Spinner type="dots3" /> {indicatorText}
                <Text color="gray"> ({seconds}s • esc to interrupt)</Text>
            </Text>
        </Box>
    );
};
