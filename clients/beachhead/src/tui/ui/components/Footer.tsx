import { Box, Text } from "ink";
import React from "react";

import { tildeContraction } from "../utils/paths";

interface FooterProps {
    workspaceRoot?: string;
    helpText: string;
}

export const Footer: React.FC<FooterProps> = ({ workspaceRoot, helpText }) => {
    const workspaceDisplay = workspaceRoot ? tildeContraction(workspaceRoot) : "No workspace set";

    return (
        <Box justifyContent="space-between" width="100%" paddingLeft={1} paddingRight={1}>
            <Text color="magenta" dimColor>
                {helpText}
            </Text>
            <Text color="cyan" dimColor>
                {workspaceDisplay}
            </Text>
        </Box>
    );
};
