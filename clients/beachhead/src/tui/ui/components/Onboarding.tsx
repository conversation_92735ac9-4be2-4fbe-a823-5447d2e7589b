import { Box, Text } from "ink";
import { useApp } from "ink";
import React, { useState } from "react";

import { useKeypress } from "../../hooks/useKeypress";

interface OnboardingProps {
    onComplete: () => void;
}

type OnboardingStep = "confirmation" | "tips";

export const Onboarding: React.FC<OnboardingProps> = ({ onComplete }) => {
    const [currentStep, setCurrentStep] = useState<OnboardingStep>("confirmation");
    const { exit } = useApp();

    useKeypress({
        onKeypress: (input, key) => {
            // Handle Ctrl+C for exit
            if (key.ctrl && input === "c") {
                exit();
                return;
            }

            if (key.return) {
                if (currentStep === "confirmation") {
                    setCurrentStep("tips");
                } else {
                    onComplete();
                }
            } else if (key.escape) {
                onComplete();
            }
        },
    });

    return (
        <Box flexDirection="column" paddingRight={1} paddingLeft={1} paddingTop={1}>
            {currentStep === "confirmation" && (
                <>
                    <Box
                        marginBottom={2}
                        borderStyle="round"
                        paddingX={1}
                        borderColor="magenta"
                        borderDimColor
                        width={80}
                        flexGrow={0}
                    >
                        <Text>
                            ⚡️ Welcome to Augment Code! A bit of housekeeping before we get started
                        </Text>
                    </Box>

                    <Box flexDirection="column" gap={1} width={72}>
                        <Box>
                            <Text color="magenta">⏺</Text>
                            <Box flexDirection="column">
                                <Text>Your codebase will be indexed automatically</Text>
                                <Text dimColor>
                                    Indexing allows Augment to make tailored code suggestions and
                                    explain common practices or patterns. Your data always stays
                                    secure, private and anonymized.
                                </Text>
                            </Box>
                        </Box>
                        <Box>
                            <Text color="magenta">⏺</Text>
                            <Box flexDirection="column">
                                <Text>Commands run automatically</Text>
                                <Text dimColor wrap="wrap">
                                    The agent will run commands automatically, including shell
                                    commands and MCP servers. We recommend using source control to
                                    manage code changes and using MCP servers from providers you
                                    trust.
                                </Text>
                            </Box>
                        </Box>
                    </Box>
                </>
            )}

            {currentStep === "tips" && (
                <>
                    <Box
                        marginBottom={2}
                        borderStyle="round"
                        paddingX={1}
                        borderColor="cyan"
                        borderDimColor
                        width={80}
                        flexGrow={0}
                    >
                        <Text>💡 Tips for using Augment</Text>
                    </Box>

                    <Box flexDirection="column" gap={1} width={72}>
                        <Box>
                            <Text color="cyan">⏺</Text>
                            <Box flexDirection="column">
                                <Text>Multi-line input</Text>
                                <Text dimColor wrap="wrap">
                                    All terminals can use Ctrl+J to enter a new line. To use
                                    Ctrl+Return or Shift+Return to enter a new line, you'll need to
                                    configure your terminal. See https://docs.augmentcode.com for
                                    more information.
                                </Text>
                            </Box>
                        </Box>
                        <Box>
                            <Text color="cyan">⏺</Text>
                            <Box flexDirection="column">
                                <Text>Natural language commands</Text>
                                <Text dimColor wrap="wrap">
                                    Ask questions, request edits, or run commands in plain English.
                                    Augment understands context and can help with complex tasks.
                                </Text>
                            </Box>
                        </Box>
                        <Box>
                            <Text color="cyan">⏺</Text>
                            <Box flexDirection="column">
                                <Text>Keyboard shortcuts</Text>
                                <Text dimColor wrap="wrap">
                                    Press esc to cancel operations and to dismiss menus, Ctrl+C to
                                    exit, and type / for slash commands.
                                </Text>
                            </Box>
                        </Box>
                    </Box>
                </>
            )}

            <Box flexDirection="column" gap={1} marginTop={2}>
                <Text>→ Continue</Text>
                <Box paddingLeft={2}>
                    <Text color="grey">Press return to continue, or esc to skip</Text>
                </Box>
            </Box>
        </Box>
    );
};
