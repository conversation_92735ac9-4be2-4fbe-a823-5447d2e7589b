/**
 * Setup Credentials Step - Help users find their Augment API credentials
 */

import { Box, Text, useInput } from "ink";
import React, { useState, useEffect } from "react";
import * as fs from "fs";
import * as path from "path";
import * as os from "os";
import { BaseStepProps } from "../../../types/BaseStepTypes";

export interface SetupCredentialsStepProps extends BaseStepProps {}

interface AugmentCredentials {
    sessionContent?: string;
    found: boolean;
    error?: string;
}

export const SetupCredentialsStep: React.FC<SetupCredentialsStepProps> = ({
    onNext,
    onPrevious,
    isFirstStep,
}) => {
    const [credentials, setCredentials] = useState<AugmentCredentials>({ found: false });
    const [copyFeedback, setCopyFeedback] = useState<string>("");

    // Auto-detect Augment credentials
    useEffect(() => {
        const detectCredentials = async () => {
            try {
                const sessionPath = path.join(os.homedir(), '.augment', 'session.json');

                if (!fs.existsSync(sessionPath)) {
                    setCredentials({
                        found: false,
                        error: "Session file not found at ~/.augment/session.json"
                    });
                    return;
                }

                const sessionContent = fs.readFileSync(sessionPath, 'utf8');

                setCredentials({
                    sessionContent,
                    found: true
                });
            } catch (error) {
                setCredentials({
                    found: false,
                    error: error instanceof Error ? error.message : "Failed to read session file"
                });
            }
        };

        detectCredentials();
    }, []);

    // Copy to clipboard functionality
    const copyToClipboard = async (text: string, label: string) => {
        try {
            // Use process.stdout.write to copy to clipboard (works in terminal environments)
            process.stdout.write(`\x1b]52;c;${Buffer.from(text).toString('base64')}\x1b\\`);
            setCopyFeedback(`✅ ${label} copied!`);
            setTimeout(() => setCopyFeedback(""), 2000);
        } catch (error) {
            setCopyFeedback(`❌ Copy failed`);
            setTimeout(() => setCopyFeedback(""), 2000);
        }
    };

    // Helper function to mask the access token in session content for display
    const maskSessionContent = (content: string): string => {
        try {
            const sessionData = JSON.parse(content);
            if (sessionData.accessToken) {
                const maskedData = {
                    accessToken: "**********",
                    tenantURL: "https://*******.api.augmentcode.com",
                };
                return JSON.stringify(maskedData, null, 2);
            }
            return content;
        } catch {
            return content;
        }
    };

    useInput((input, key) => {
        if (input === 'c' && credentials.found && credentials.sessionContent) {
            copyToClipboard(credentials.sessionContent, "Session file");
        } else if (key.return) {
            onNext();
        } else if ((key.backspace || key.delete) && !isFirstStep) {
            onPrevious();
        }
    });

    return (
        <Box flexDirection="column" gap={1}>
            <Text color="blue" bold>🔍 Step 1: Get Your Augment Session File</Text>

            <Box flexDirection="column" paddingLeft={2} gap={1}>
                {credentials.found ? (
                    <>
                        <Box paddingTop={1} flexDirection="column">
                            <Text color="cyan">📋 Session content (with token masked):</Text>
                            <Box paddingLeft={2} paddingTop={1}>
                                <Text color="gray">{credentials.sessionContent ? maskSessionContent(credentials.sessionContent) : 'Not found'}</Text>
                            </Box>
                        </Box>

                        <Box paddingTop={1}>
                            <Text color="yellow">💡 Tip: Press 'C' to copy the full session file</Text>
                        </Box>
                    </>
                ) : (
                    <>
                        <Text color="red">❌ Could not auto-detect credentials.</Text>
                        {credentials.error && (
                            <Text color="gray">   Error: {credentials.error}</Text>
                        )}
                        
                        <Box paddingTop={1}>
                            <Text color="yellow" bold>Manual steps:</Text>
                        </Box>
                        
                        <Box flexDirection="column" paddingLeft={2}>
                            <Text>1. Open <Text color="cyan">~/.augment/session.json</Text> in a text editor</Text>
                            <Text>2. Copy the entire file content</Text>
                            <Text>3. You'll need this content for the GitHub secret</Text>
                        </Box>
                    </>
                )}
                
                {copyFeedback && (
                    <Box paddingTop={1}>
                        <Text color={copyFeedback.startsWith('✅') ? 'green' : 'red'}>
                            {copyFeedback}
                        </Text>
                    </Box>
                )}
            </Box>
        </Box>
    );
};
