import { Box, Text } from "ink";
import React from "react";
import { BaseStepProps } from "../../../types/BaseStepTypes";
import { ToggleStep } from "./ToggleStep";
import { InputStep } from "./InputStep";

export const OptionsStep: React.FC<BaseStepProps> = (props) => {
    const { config } = props;
    const trigger = config.trigger;
    const configOptions = trigger?.getConfigurationOptions() || [];

    if (!trigger) {
        return (
            <Box>
                <Text color="red">No trigger selected</Text>
            </Box>
        );
    }

    // Separate boolean and text fields
    const booleanFields = configOptions.filter(opt => opt.type === "boolean");
    const textFields = configOptions.filter(opt => opt.type === "text");

    // If we have boolean fields, use ToggleStep
    if (booleanFields.length > 0 && textFields.length === 0) {
        return (
            <ToggleStep
                {...props}
                title={`Configure options for ${trigger.displayName.toLowerCase()}:`}
                fields={booleanFields.map(field => ({
                    key: field.key,
                    label: field.label,
                    description: field.description,
                    defaultValue: field.defaultValue || false
                }))}
                values={config.triggerConfig || {}}
                onValuesChange={(triggerConfig) => props.onUpdateConfig({ triggerConfig })}
            />
        );
    }

    // If we have text fields, use InputStep
    if (textFields.length > 0 && booleanFields.length === 0) {
        return (
            <InputStep
                {...props}
                title={`Configure options for ${trigger.displayName.toLowerCase()}:`}
                fields={textFields.map(field => ({
                    key: field.key,
                    label: field.label,
                    description: field.description,
                    required: field.required || false,
                    placeholder: field.placeholder,
                    defaultValue: field.defaultValue || ""
                }))}
                values={config.triggerConfig || {}}
                onValuesChange={(triggerConfig) => props.onUpdateConfig({ triggerConfig })}
            />
        );
    }

    // If we have mixed field types, fall back to a simple display
    // (This could be enhanced in the future to handle mixed types better)
    return (
        <Box flexDirection="column" gap={1}>
            <Text>Configure options for {trigger.displayName.toLowerCase()}:</Text>
            <Text color="yellow">Mixed field types not yet supported in new step system</Text>
            <Text color="gray">Please use the legacy options step for this trigger</Text>
        </Box>
    );
};
