import { Box, Text } from "ink";
import React from "react";

import { <PERSON> } from "./Banner";

interface WelcomeProps {}

export const Welcome: React.FC<WelcomeProps> = React.memo(() => {
    return (
        <Box flexDirection="column" paddingLeft={1} paddingTop={1}>
            <Banner />
            <Box flexDirection="column" marginBottom={2}>
                <Text bold>Getting started with Auggie by Augment Code</Text>

                <Text dimColor>1. You can ask questions, edit files, or run commands</Text>
                <Text dimColor>2. Your workspace is automatically indexed for best results</Text>
                <Text dimColor>3. Commands will run automatically</Text>
            </Box>
            <Box>
                <Text dimColor>💡 For automation, use 'auggie --print "your task"'</Text>
            </Box>
        </Box>
    );
});
