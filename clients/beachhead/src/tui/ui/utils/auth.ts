import { APIServerImplWithErrorReporting as APIServerImpl } from "../../../augment-api";
import { AugmentConfigListener } from "../../../augment-config-listener";
import { FileBasedAuthSessionStore } from "../../../auth/auth-session-store";
import { OAuthFlow } from "../../../auth/oauth-flow";
import { VERSION } from "../../context";

export interface AuthSetup {
    loginUrl: string;
    oauthFlow: OAuthFlow;
}

export function createOAuthFlow(cacheDir?: string): AuthSetup {
    // Create minimal config for OAuth (similar to CLI)
    const config = {
        config: {
            apiToken: "",
            completionURL: "https://api.augmentcode.com",
            oauth: {
                clientID: "v",
                url: "https://auth.augmentcode.com",
            },
        },
    } as AugmentConfigListener;

    // Create auth session store and OAuth flow
    const authSession = new FileBasedAuthSessionStore(cacheDir);
    const apiServer = new APIServerImpl(
        config,
        authSession,
        "login-session",
        `augment.cli/${VERSION}/interactive`,
        global.fetch
    );
    const oauthFlow = new OAuthFlow(config, apiServer, authSession, cacheDir);

    // Start the flow to get the URL
    const loginUrl = oauthFlow.startFlow();

    return {
        loginUrl,
        oauthFlow,
    };
}

export async function handleAuthResponse(
    oauthFlow: OAuthFlow,
    jsonResponse: string
): Promise<string> {
    return await oauthFlow.handleAuthJson(jsonResponse);
}
