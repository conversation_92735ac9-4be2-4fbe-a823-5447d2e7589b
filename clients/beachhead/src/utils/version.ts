// Base version
const BASE_VERSION = "0.1.1";

// Import build-time version information
let BUILD_GIT_COMMIT = "unknown";
let BUILD_GIT_DIRTY = false;
try {
    // This import will be resolved at build time by Bazel
    // eslint-disable-next-line @typescript-eslint/no-var-requires
    const buildInfo = require("../generated/build-info");
    BUILD_GIT_COMMIT = buildInfo.BUILD_GIT_COMMIT;
    BUILD_GIT_DIRTY = buildInfo.BUILD_GIT_DIRTY;
} catch {
    // Fallback if build info is not available (e.g., in development)
    BUILD_GIT_COMMIT = "unknown";
    BUILD_GIT_DIRTY = false;
}

/**
 * Gets the CLI version with build-time git commit information.
 * Returns a version string in the format: base-version+commit-hash
 *
 * Examples:
 * - "0.1.3+abc1234"
 * - "0.1.3" (if build info not available)
 */
export function getVersionWithGitInfo(): string {
    try {
        // Use build-time injected git commit hash
        if (typeof BUILD_GIT_COMMIT !== "undefined" && BUILD_GIT_COMMIT !== "unknown") {
            const dirtyFlag = BUILD_GIT_DIRTY ? " dirty" : "";
            return `${BASE_VERSION} (commit ${BUILD_GIT_COMMIT}${dirtyFlag})`;
        }
    } catch {
        // If build-time constants are not available, fall back to base version
    }

    // Fallback to just base version if build info is not available
    return BASE_VERSION;
}

/**
 * Gets just the base version without git information.
 */
export function getBaseVersion(): string {
    return BASE_VERSION;
}
