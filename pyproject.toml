# Configure Python tools and setup base+research packages through pip.
#
# This file configures common python tools (isort, pylint) that are used by pre-commit.
#
# The dependencies and packages set up here are solely for research use; package
# requirements and dependencies in staging and production are managed by <PERSON><PERSON>.
# See `tools/python_deps/Readme.md` for more details.

[build-system]
build-backend = "setuptools.build_meta"
requires = ["setuptools", "setuptools-scm==8"]

[project]
name = "augment_research"
version = "0.0.0"
authors = [{ name = "Augment Computing Inc." }]
description = "Augment's training and research code."
readme = "Readme.md"
requires-python = "==3.11.*"
dynamic = ["dependencies"]

[project.scripts]
hydra = "research.eval.hydra.cli_main:main"
patch_viewer = "research.tools.patch_viewer.patch_viewer:main"

[tool.setuptools.package-data]
"research.eval.hydra" = ["templates/lang_py/*", "templates/lang_go/*"]

[tool.setuptools.dynamic]
dependencies = { file = ["research/requirements.txt"] }

[tool.setuptools.package-dir]
"base" = "base"
"experimental" = "experimental"
"research" = "research"
# NOTE(arun): research users should never depend on libraries in services.
# NOTE(jeff): We limit the files pulled in from services to only protos using
# the MANIFEST.in file.
"services" = "services"
# Third-party libraries
"jobs" = "research/gpt-neox/jobs"
"megatron" = "research/gpt-neox/megatron"
"tests" = "research/gpt-neox/tests"

[tool.ruff]
force-exclude = true

[tool.ruff.format]
exclude = ["third_party", "testdata", "prompting_experiments"]

[tool.ruff.lint]
# third_party: because we do not actively change the code
# models/services/tools/base: pylint is handled by Bazel
exclude = [
  "third_party/**",
  "models/**",
  "services/**",
  "tools/**",
  "base/**",
  "testdata/**",
  "experimental/**/*.ipynb",
  "experimental/guy/**",
  "experimental/dxy/**",
]

# https://docs.astral.sh/ruff/rules
# In addition to the defaults ["E4", "E7", "E9", "F"]
extend-select = ["W", "I", "Q", "B", "A"]
# I'd like to add these for a bit more aggressive linting.
# ["NPY", "RUF", "PERF", "FURB"]
#
# B019 cached-instance-method
# B028 No explicit `stacklevel` keyword argument found
ignore = ["B019", "B028"]

[tool.ruff.lint.per-file-ignores]
"__init__.py" = ["F401"]
"research/fastbackward/configs/*" = ["D100"]

[tool.ruff.lint.isort]
# This makes sure that these packages are sorted after third-party imports.
known-first-party = ["base", "experimental", "services", "research", "tests" ]

[tool.ruff.lint.pylint]
max-locals = 25

[tool.uv]
cache-keys = [
  { file = "pyproject.toml" },
  { file = "research/requirements.txt" },
]

no-build-isolation-package = [
  "deepspeed",  # requires torch
]

[tool.uv.sources]
torch = { index = "pytorch-cu124" }

apex = { index = "system-services-dev" }
flash-attn = { index = "system-services-dev" }
flashattn-hopper = { index = "system-services-dev" }
megablocks = { index = "system-services-dev" }
mpi4py = { index = "system-services-dev" }
transformer-engine = { index = "system-services-dev" }
#triton = { index = "system-services-dev" }

[[tool.uv.index]]
name = "pytorch-cu124"
url = "https://download.pytorch.org/whl/cu124"
explicit = true

[[tool.uv.index]]
name = "system-services-dev"
url = "https://us-central1-python.pkg.dev/system-services-dev/pypi-public/simple"
explicit = true
