load("//tools/bzl:go.bzl", "go_library", "go_test")

package(default_visibility = ["//infra:internal"])

go_library(
    name = "logger",
    srcs = [
        "logger.go",
        "logger_flags.go",
    ],
    importpath = "github.com/augmentcode/augment/infra/lib/logger",
    visibility = ["//visibility:public"],
    deps = [
        "@com_github_mattn_go_isatty//:go-isatty",
        "@com_github_rs_zerolog//:zerolog",
        "@com_github_rs_zerolog//log",
        "@com_github_spf13_pflag//:pflag",
    ],
)

go_test(
    name = "logger_test",
    size = "small",
    srcs = ["logger_test.go"],
    embed = [":logger"],
    deps = [
        "@com_github_google_go_cmp//cmp",
        "@com_github_google_go_cmp//cmp/cmpopts",
        "@com_github_rs_zerolog//:zerolog",
        "@com_github_spf13_pflag//:pflag",
    ],
)
