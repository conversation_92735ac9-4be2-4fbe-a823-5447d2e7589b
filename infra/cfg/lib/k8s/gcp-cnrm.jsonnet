local k8s_base = import 'k8s-base.jsonnet';

k8s_base + {

  //////////////////////////////////////////////////////////////////////////////
  //
  // Core ConfigConnector Objects
  //

  CC_MODE:: {
    CLUSTER: 'cluster',
    NAMESPACED: 'namespaced',
  },

  CC_STATE_INTO_SPEC:: {
    ABSENT: 'Absent',
    MERGE: 'Merge',
  },

  ConfigConnector:: $.ClusterObject + {
    local o = self,
    apiVersion: 'core.cnrm.cloud.google.com/v1beta1',
    kind: 'ConfigConnector',
    name:: 'configconnector.core.cnrm.cloud.google.com',

    mode:: $.CC_MODE.CLUSTER,
    googleServiceAccount:: null,
    spec+: std.prune({
      mode: o.mode,
      stateIntoSpec: $.CC_STATE_INTO_SPEC.ABSENT,
      googleServiceAccount: o.googleServiceAccount,
    }),
  },

  ConfigConnectorContext:: $.Object + {
    local o = self,
    apiVersion: 'core.cnrm.cloud.google.com/v1beta1',
    kind: 'ConfigConnectorContext',
    name:: 'configconnectorcontext.core.cnrm.cloud.google.com',
    namespace:: error 'ConfigConnectorContext.namespace required.',
    googleServiceAccount:: error 'ConfigConnectorContext.googleServiceAccount required.',
    spec+: {
      googleServiceAccount: o.googleServiceAccount,
      stateIntoSpec: $.CC_STATE_INTO_SPEC.ABSENT,
    },
  },

  //////////////////////////////////////////////////////////////////////////////
  //
  // GCP API Objects
  // https://cloud.google.com/config-connector/docs/reference/overview
  //

  GCP:: {
    local gcp = self,
    Object:: $.Object + {
      local o = self,

      project_id:: error 'GCP.Object.project_id not set.',
      ref:: error 'GCP.Object.ref not defined.',
      deletion_policy:: null,

      DELETION_POLICY:: {
        NONE: 'none',
        ABANDON: 'abandon',
      },

      metadata+: std.prune({
        annotations+: {
          'cnrm.cloud.google.com/deletion-policy': o.deletion_policy,
        },
      }),

      externalRef:: {
        kind: o.kind,
        external: o.ref,
      },
      localRef:: {
        namespace: o.metadata.namespace,
        name: o.metadata.name,
      },
      localKindRef:: {
        namespace: o.metadata.namespace,
        name: o.metadata.name,
        kind: o.kind,
      },
    },

    ArtifactRegistry:: {
      local api = self,
      Object:: gcp.Object + {
        apiVersion: 'artifactregistry.cnrm.cloud.google.com/v1beta1',
      },

      Repository:: api.Object + {
        kind: 'ArtifactRegistryRepository',
        ref:: 'projects/' + self.project_id + '/locations/' + self.spec.location + '/repositories/' + self.metadata.name,

        spec+: {
          FORMAT:: {
            DOCKER: 'Docker',
            PYTHON: 'Python',
          },
          format: error 'GCP.ArtifactRegistry.Repository.spec.format required.',
          location: error 'GCP.ArtifactRegistry.Repository.spec.location required.',

          MODE:: {
            STANDARD: 'STANDARD_REPOSITORY',
            VIRTUAL: 'VIRTUAL_REPOSITORY',
            REMOTE: 'REMOTE_REPOSITORY',
          },
          mode: self.MODE.STANDARD,

          assert (self.mode == self.MODE.REMOTE) == (std.get(self, 'remoteRepositoryConfig') != null) : 'remoteRepositoryConfig required and only valid for mode REMOTE',
          assert (self.mode == self.MODE.VIRTUAL) == (std.get(self, 'virtualRepositoryConfig') != null) : 'virtualRepositoryConfig required and only valid for mode VIRTUAL',
        },
      },
    },

    DNS:: {
      local api = self,
      Object:: gcp.Object + {
        apiVersion: 'dns.cnrm.cloud.google.com/v1beta1',
      },

      ManagedZone:: api.Object + {
        kind: 'DNSManagedZone',
        name:: std.strReplace(std.stripChars(self.spec.dnsName, '.'), '.', '-'),
        spec+: {
          VISIBILITY:: {
            PUBLIC: 'public',
            PRIVATE: 'private',
          },
        },
      },

      RecordSet:: api.Object + {
        kind: 'DNSRecordSet',
        name:: std.strReplace(std.stripChars(self.spec.name, '.'), '.', '-') + '-' + std.asciiLower(self.spec.type),

        local o = self,
        spec+: std.prune({
          managedZoneRef: {
            // Either this
            external: null,
            // Or these
            name: null,
            namespace: null,
          },
          ttl: null,
          rrdatas: [],
        }) + {
          name: o.metadata.name,
          type: error 'DNSRecordSet.spec.type required.',
          zoneRefFrom(z):: {
            name: z.metadata.name,
            namespace: z.metadata.namespace,
          },
        },
      },
    },

    IAM: {
      local api = self,
      Object:: gcp.Object + {
        apiVersion: 'iam.cnrm.cloud.google.com/v1beta1',
      },

      PartialPolicy:: api.Object + {
        kind: 'IAMPartialPolicy',
      },

      Policy:: api.Object + {
        kind: 'IAMPolicy',
      },

      PolicyMember:: api.Object + {
        kind: 'IAMPolicyMember',
        spec+: {
          memberFromSA(sa):: {
            serviceAccountRef: sa.localRef,
          },
        },
      },

      ServiceAccount:: api.Object + {
        local o = self,
        kind: 'IAMServiceAccount',
        email:: '%s@%s.iam.gserviceaccount.com' % [self.metadata.name, self.project_id],
        description:: null,
        spec+: std.prune({
          description: o.description,
          displayName: o.metadata.name,
        }),

        NAME_LIMIT:: 30,
        name_length:: std.length(self.metadata.name),
        assert self.name_length <= self.NAME_LIMIT : 'IAM ServiceAccount name %s too long: %d > %d.' % [self.metadata.name, self.name_length, self.NAME_LIMIT],
      },

      ServiceAccountKey:: api.Object + {
        kind: 'IAMServiceAccountKey',
        // NOTE: metadata.annotations.cnrm.cloud.google.com/create-gsa-key-secret = false to disable key create.
      },

      ServiceAccountKeyFor(sa):: api.ServiceAccountKey + {
        name:: sa.name + '-sa',
        spec+: {
          serviceAccountRef: sa.localRef,
        },
      },
    },

    CloudIdentity:: {
      local api = self,
      Object:: gcp.Object + {
        apiVersion: 'cloudidentity.cnrm.cloud.google.com/v1beta1',
      },

      Group:: api.Object + {
        local o = self,
        kind: 'CloudIdentityGroup',
        email:: error 'CloudIdentityGroup.email required.',
        name:: std.strReplace(o.email, '@', '.'),
        spec+: {
          INITIAL_GROUP_CONFIG:: {
            EMPTY: 'EMPTY',
            WITH_INITIAL_OWNER: 'WITH_INITIAL_OWNER',
          },
          initialGroupConfig: self.INITIAL_GROUP_CONFIG.WITH_INITIAL_OWNER,
          parent: error 'CloudIdentityGroup.spec.parent required.',
          groupKey+: {
            id: o.email,
          },
          labels+: {
            'cloudidentity.googleapis.com/groups.discussion_forum': '',
          },
        },
      },

      Membership:: api.Object + {
        local o = self,
        kind: 'CloudIdentityMembership',
        name:: if self.group_obj != null then self.group_obj.metadata.name + '-' + std.asciiLower(self.role) + '-' + std.strReplace(self.member_email, '@', '.') else error 'CloudIdentityMembership.name required.',
        ROLE:: {
          OWNER: 'OWNER',
          MANAGER: 'MANAGER',
          MEMBER: 'MEMBER',
        },
        group_id:: null,
        group_obj:: null,
        assert (self.group_id == null) != (self.group_obj == null) : 'One of group_id or group_obj must be set.',
        member_email:: error 'CloudIdentityMembership.member_email required.',
        role:: self.ROLE.MEMBER,
        spec+: {
          groupRef+: std.prune({
            external: if o.group_id != null then 'groups/' + o.group_id,
            name: if o.group_obj != null then o.group_obj.metadata.name,
            namespace: if o.group_obj != null then o.group_obj.metadata.namespace,
          }),
          preferredMemberKey+: {
            id: o.member_email,
          },
          roles: if o.role == o.ROLE.OWNER || o.role == o.ROLE.MANAGER then [
            { name: o.role },
            { name: o.ROLE.MEMBER },
          ] else [{ name: o.role }],
        },
      },
    },

    Storage:: {
      local api = self,
      Object:: gcp.Object + {
        apiVersion: 'storage.cnrm.cloud.google.com/v1beta1',
      },

      Bucket:: api.Object + {
        kind: 'StorageBucket',
      },

      BucketAccessControl:: api.Object + {
        kind: 'StorageBucketAccessControl',
      },

      DefaultObjectAccessControl:: api.Object + {
        kind: 'StorageDefaultObjectAccessControl',
      },

      BucketNotification:: api.Object + {
        kind: 'StorageBucketNotification',
      },
    },

    Compute:: {
      local api = self,
      Object:: gcp.Object + {
        apiVersion: 'compute.cnrm.cloud.google.com/v1beta1',
      },

      Network:: api.Object + {
        local n = self,
        kind: 'ComputeNetwork',
        ref:: 'projects/%s/global/networks/%s' % [n.project_id, n.name],
      },

      Subnetwork:: api.Object + {
        local sn = self,
        kind: 'ComputeSubnetwork',
        ref:: 'projects/%s/regions/%s/subnetworks/%s' % [sn.project_id, sn.spec.region, sn.name],
      },

      NetworkPeering:: api.Object + {
        kind: 'ComputeNetworkPeering',
        spec+: {
          // These four booleans match the defaults when creating in the GCP UI.
          importCustomRoutes: false,
          exportCustomRoutes: false,
          importSubnetRoutesWithPublicIp: false,
          exportSubnetRoutesWithPublicIp: true,

          networkRef: error 'NetworkPeering.networkRef required',
          peerNetworkRef: error 'NetworkPeering.peerNetworkRef required',
        },
      },

      Reservation:: api.Object + {
        local r = self,
        kind: 'ComputeReservation',
      },

      Address:: api.Object + {
        local a = self,
        kind: 'ComputeAddress',
        ref:: 'projects/%s/regions/%s/addresses/%s' % [a.project_id, a.spec.location, a.name],
      },

      Router:: api.Object + {
        local r = self,
        kind: 'ComputeRouter',
        ref:: 'projects/%s/regions/%s/routers/%s' % [r.project_id, r.spec.region, r.name],
      },

      RouterNAT:: api.Object + {
        local rn = self,
        kind: 'ComputeRouterNAT',
        ref:: 'projects/%s/regions/%s/routers/%s/nats/%s' % [rn.project_id, rn.spec.region, rn.spec.routerRef.name, rn.name],
      },
    },

    GKE:: {
      local api = self,
      Object:: gcp.Object + {
        apiVersion: 'container.cnrm.cloud.google.com/v1beta1',
      },

      Cluster:: api.Object + {
        kind: 'ContainerCluster',
        spec+: {
          releaseChannel+: {
            UNSPECIFIED:: 'UNSPECIFIED',
            REGULAR:: 'REGULAR',
            STABLE:: 'STABLE',
          },
          NET_MODE:: {
            VPC_NATIVE:: 'VPC_NATIVE',
          },
          DATAPATH_PROVIDER:: {
            ADVANCED_DATAPATH: 'ADVANCED_DATAPATH',
          },
        },
      },

      NodePool:: api.Object + {
        kind: 'ContainerNodePool',
        spec+: {
          upgradeSettings+: {
            STRATEGY:: {
              // https://cloud.google.com/kubernetes-engine/docs/reference/rest/v1/UpgradeSettings#NodePoolUpdateStrategy
              NODE_POOL_UPDATE_STRATEGY_UNSPECIFIED: null,
              BLUE_GREEN: 'BLUE_GREEN',
              SURGE: 'SURGE',
            },
          },
        },
      },
    },

    Filestore:: {
      local api = self,
      Object:: gcp.Object + {
        apiVersion: 'filestore.cnrm.cloud.google.com/v1beta1',
      },

      Instance:: api.Object + {
        kind: 'FilestoreInstance',
        spec+: {
          TIER:: {
            BASIC_HDD: 'BASIC_HDD',
            BASIC_SSD: 'BASIC_SSD',
            REGIONAL: 'REGIONAL',
            ZONAL: 'ZONAL',
          },
        },
      },
    },
  },
}
