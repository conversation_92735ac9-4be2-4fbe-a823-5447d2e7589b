<script lang="ts">
  import { getFilename } from "$common-webviews/src/common/utils/file-paths";
  import type { ChatModel } from "../../models/chat-model";
  import { ContextStatus, type IContextInfo } from "../../models/types";
  import {
    type IChatMentionable,
    type IChatMentionableItem,
    isItemSourceFolder,
    isItemAgentMemories,
    isItemGuidelines,
    isItemSelection,
  } from "../../types/mention-option";
  import ContextItemBadge from "./ContextItemBadge.svelte";
  import IconButtonAugment from "$common-webviews/src/design-system/components/IconButtonAugment.svelte";
  import UpDownIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/angles-up-down.svg?component";
  import { onMount, tick } from "svelte";
  import { fade } from "svelte/transition";

  const COLLAPSED_CONTEXT_BAR_HEIGHT = 20;

  export let chatModel: ChatModel;

  // Get the context model as a reactive store
  $: contextModel = chatModel.specialContextInputModel;

  // State for collapse/expand functionality
  let isCollapsed = true;
  let hasOverflow = false;
  let contentElement: HTMLElement;
  let containerElement: HTMLElement;

  // State for scroll-based mask visibility
  let showLeftMask = false;
  let showRightMask = false;

  // Function to get sort priority for items
  const getSortPriority = (item: IChatMentionable & IContextInfo): number => {
    if (isItemAgentMemories(item)) return 1; // memories first
    if (isItemGuidelines(item)) return 2; // guidelines second
    if (isItemSelection(item)) return 3; // selection third
    return 4; // everything else last
  };

  // Render source folders at the end
  let allItems = [] as (IChatMentionable & IContextInfo)[];
  $: {
    const items = $contextModel.recentActiveItems;
    const sourceFolders = [] as IChatMentionableItem<"sourceFolder">[];
    const filteredItems = items.filter((item) => {
      if (isItemSourceFolder(item)) {
        sourceFolders.push(item);
        return false;
      }
      return true;
    });
    if (sourceFolders.length > 1) {
      // if this workspace has multiple repos, group them together into one chip
      filteredItems.push({
        id: "sourceFoldersGroup",
        label: "Repos",
        status: ContextStatus.active,
        sourceFolderGroup: sourceFolders.sort((a, b) => {
          const aName = getFilename(a.sourceFolder.folderRoot);
          const bName = getFilename(b.sourceFolder.folderRoot);
          return aName.localeCompare(bName);
        }),
        referenceCount: 1,
      });
      allItems = filteredItems.reverse();
    } else {
      allItems = items.reverse();
    }
    if (!allItems.find((item) => isItemSelection(item))) {
      allItems.push({
        selection: { repoRoot: "", pathName: "" },
        label: "No Code Selected",
        name: "empty-selection",
        id: "empty-selection",
        referenceCount: 0,
        status: 0,
      });
    }
    allItems.sort((a, b) => getSortPriority(a) - getSortPriority(b));
  }

  // Function to update mask visibility based on scroll position
  function updateMaskVisibility() {
    if (!containerElement || !isCollapsed) {
      showLeftMask = false;
      showRightMask = false;
      return;
    }

    const { scrollLeft, scrollWidth, clientWidth } = containerElement;
    const maxScrollLeft = scrollWidth - clientWidth - 2;

    // Show left mask if scrolled right (scrollLeft > 0)
    showLeftMask = scrollLeft > 0;

    // Show right mask if not scrolled to the right limit (and there's content to scroll)
    showRightMask = maxScrollLeft > 0 && scrollLeft < maxScrollLeft;
  }

  // Function to check if content overflows (doesn't fit in one row)
  async function checkOverflow() {
    if (!containerElement) return;

    await tick(); // Wait for DOM updates

    // Check if content is horizontally scrollable when collapsed
    const { scrollHeight, scrollWidth, clientWidth } = containerElement;

    // Consider it overflow if content is horizontally scrollable (scrollWidth > clientWidth)
    hasOverflow = isCollapsed
      ? scrollWidth > clientWidth
      : (hasOverflow = scrollHeight > COLLAPSED_CONTEXT_BAR_HEIGHT);
    if (!hasOverflow) isCollapsed = true;
    // Update mask visibility after checking overflow
    updateMaskVisibility();
  }

  // Function to toggle collapsed state
  function toggleCollapsed() {
    isCollapsed = !isCollapsed;
    // Update mask visibility after state change
    setTimeout(updateMaskVisibility, 0);
  }

  // Check overflow on mount and resize
  onMount(() => {
    checkOverflow();
    const containerResizeObserver = new ResizeObserver(() => checkOverflow());
    containerResizeObserver.observe(containerElement);
    const contentResizeObserver = new ResizeObserver(() => checkOverflow());
    contentResizeObserver.observe(contentElement);

    containerElement.addEventListener("scroll", updateMaskVisibility);

    return () => {
      containerResizeObserver.disconnect();
      contentResizeObserver.disconnect();
      containerElement.removeEventListener("scroll", updateMaskVisibility);
    };
  });
</script>

<div class="c-action-bar-context">
  <div
    class="c-action-bar-context__container"
    class:is-collapsed={isCollapsed}
    class:show-left-mask={showLeftMask}
    class:show-right-mask={showRightMask}
    style="--collapsed-context-bar-height: {COLLAPSED_CONTEXT_BAR_HEIGHT}px;"
    bind:this={containerElement}
  >
    <div
      class="c-action-bar-context__content"
      class:is-collapsed={isCollapsed}
      bind:this={contentElement}
    >
      <slot name="prepend" selectedItems={allItems} />
      {#each allItems as item}
        <div transition:fade={{ duration: 100 }}>
          <ContextItemBadge {item} {chatModel} />
        </div>
      {/each}
    </div>
  </div>

  {#if hasOverflow}
    <div class="c-action-bar-context-expand-button">
      <IconButtonAugment
        variant="ghost-block"
        color="neutral"
        size={0.5}
        title={isCollapsed ? "Expand context" : "Collapse context"}
        on:click={toggleCollapsed}
      >
        <UpDownIcon />
      </IconButtonAugment>
    </div>
  {/if}
</div>

<style>
  .c-action-bar-context {
    display: flex;
    flex-direction: row;
    align-items: flex-start;
    gap: var(--ds-spacing-1);
    width: 100%;
    justify-content: space-between;
    position: relative;
  }
  .c-action-bar-context :global(svg[data-ds-icon="fa"]) {
    opacity: 1;
  }
  .c-action-bar-context :global(.c-badge[data-ds-color="premium"]) {
    border-radius: 25%/50%;
    padding: var(--ds-spacing-1) var(--ds-spacing-2);
    align-self: flex-start;
  }
  .c-action-bar-context__container {
    --fade-size: 8px;
    scrollbar-width: none;
    user-select: none;
    width: 100%;
    flex: 1;
    overflow: auto;
    position: relative;
    transform: translateZ(0);
  }

  .c-action-bar-context__content {
    width: fit-content;
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    align-items: center;
    gap: var(--ds-spacing-1);
    transition: height 0.2s ease-in-out;
    height: calc-size(auto, size);
  }

  .c-action-bar-context__content.is-collapsed {
    height: var(--collapsed-context-bar-height);
    flex-wrap: nowrap;
  }

  /* Left side only - no left fade, right fade */
  .c-action-bar-context__container.is-collapsed {
    &.show-right-mask {
      mask-image: linear-gradient(
        to right,
        black 0%,
        black calc(100% - var(--fade-size)),
        transparent 100%
      );
    }
    &.show-left-mask {
      mask-image: linear-gradient(to right, transparent 0%, black var(--fade-size), black 100%);
    }
    &.show-left-mask.show-right-mask {
      mask-image: linear-gradient(
        to right,
        transparent 0%,
        black var(--fade-size),
        black calc(100% - var(--fade-size)),
        transparent 100%
      );
    }
  }

  .c-action-bar-context-expand-button {
    display: flex;
    align-items: flex-start;
  }
</style>
