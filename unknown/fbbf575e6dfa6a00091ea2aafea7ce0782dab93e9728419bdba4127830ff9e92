<script lang="ts">
  import { getContext } from "svelte";
  import { get } from "svelte/store";
  import type { ICurrentConversationTaskStore } from "../../../models/task-store";
  import type { ChatModel } from "../../../models/chat-model";
  import IconButtonAugment from "$common-webviews/src/design-system/components/IconButtonAugment.svelte";
  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";
  import DropdownMenuAugment from "$common-webviews/src/design-system/components/DropdownMenuAugment";
  import { TooltipTriggerOn } from "$common-webviews/src/design-system/_primitives/TooltipAugment/types";
  import Trash from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/trash.svg?component";
  import Play from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/play.svg?component";
  import DotsVertical from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/ellipsis-vertical.svg?component";
  import Branch from "$common-webviews/src/design-system/icons/branch.svelte";
  import {
    TaskState,
    TaskUpdatedBy,
  } from "@augment-internal/sidecar-libs/src/agent/task/task-types";
  import {
    TaskListAction,
    TaskListActionTrigger,
    AgentSessionEventName,
  } from "@augment-internal/sidecar-libs/src/metrics/types";
  import { getTaskProgress } from "../utils/task-status-utils";

  // Props
  export let taskUuid: string;
  export let taskStore: ICurrentConversationTaskStore;
  export let editable: boolean = true;

  // Get chat model from context
  const chatModel = getContext<ChatModel>("chatModel");

  $: uuidToTask = taskStore.uuidToTask;
  $: task = $uuidToTask.get(taskUuid);

  // Get swarm mode state from chat flags
  $: enableAgentSwarmMode = chatModel.flags.enableAgentSwarmMode;

  async function handleDeleteTask() {
    if (!editable) return;

    // Emit user-triggered deleteTask event
    const rootTaskValue = get(taskStore.rootTask);
    const taskProgress = getTaskProgress(rootTaskValue);
    const taskListUsageData = {
      action: TaskListAction.deleteTask,
      totalTasksCount: taskProgress.total,
      triggeredBy: TaskListActionTrigger.user,
    };

    // Get conversation ID from the latest exchange, fallback to conversation model ID
    const chatHistory = chatModel.currentConversationModel.chatHistory;
    const conversationId =
      chatHistory.length > 0
        ? chatHistory[chatHistory.length - 1].request_id || chatModel.currentConversationModel.id
        : chatModel.currentConversationModel.id;

    chatModel.currentConversationModel.extensionClient.reportAgentSessionEvent({
      eventName: AgentSessionEventName.taskListUsage,
      conversationId,
      eventData: {
        taskListUsageData,
      },
    });

    await taskStore.deleteTask(taskUuid);
  }

  // Handle play action for not started tasks
  async function handlePlayTask() {
    if (!task || !editable || task.state !== TaskState.NOT_STARTED) return;

    // Emit user-triggered runSingleTask event
    const rootTaskValue = get(taskStore.rootTask);
    const taskProgress = getTaskProgress(rootTaskValue);
    const taskListUsageData = {
      action: TaskListAction.runSingleTask,
      totalTasksCount: taskProgress.total,
      triggeredBy: TaskListActionTrigger.user,
    };

    // Get conversation ID from the latest exchange, fallback to conversation model ID
    const chatHistory2 = chatModel.currentConversationModel.chatHistory;
    const conversationId2 =
      chatHistory2.length > 0
        ? chatHistory2[chatHistory2.length - 1].request_id || chatModel.currentConversationModel.id
        : chatModel.currentConversationModel.id;

    chatModel.currentConversationModel.extensionClient.reportAgentSessionEvent({
      eventName: AgentSessionEventName.taskListUsage,
      conversationId: conversationId2,
      eventData: {
        taskListUsageData,
      },
    });

    await taskStore.updateTask(taskUuid, { state: TaskState.IN_PROGRESS }, TaskUpdatedBy.USER);
    await taskStore.runHydratedTask(task);
  }

  // Show play button only for not started tasks
  $: showPlayButton = task?.state === TaskState.NOT_STARTED;

  // Dropdown state
  let dropdownOpen = false;

  async function handleRunWithSubAgent() {
    try {
      await taskStore.runTaskInSubAgent(taskUuid);
    } catch (error) {
      console.error("Error running task with sub-agent:", error);
    }
    dropdownOpen = false;
  }
</script>

<div class="c-task-action-buttons">
  <!-- Play Button - only shown for not started tasks -->
  {#if showPlayButton}
    <TextTooltipAugment content="Run Task" triggerOn={[TooltipTriggerOn.Hover]}>
      <IconButtonAugment
        size={1}
        variant="ghost"
        color="neutral"
        disabled={!editable}
        on:click={handlePlayTask}
        class="c-task-action-button c-task-action-button--play"
      >
        <Play />
      </IconButtonAugment>
    </TextTooltipAugment>
  {/if}

  <!-- Delete Task Button, always shown -->
  <TextTooltipAugment content="Delete Task" triggerOn={[TooltipTriggerOn.Hover]}>
    <IconButtonAugment
      size={1}
      variant="ghost"
      color="error"
      disabled={!editable}
      on:click={handleDeleteTask}
      class="c-task-action-button c-task-action-button--delete"
    >
      <Trash />
    </IconButtonAugment>
  </TextTooltipAugment>

  <!-- More Actions Dropdown - only shown when swarm mode is enabled -->
  {#if enableAgentSwarmMode}
    <DropdownMenuAugment.Root
      triggerOn={[TooltipTriggerOn.Hover, TooltipTriggerOn.Click]}
      open={dropdownOpen}
      onOpenChange={(open) => (dropdownOpen = open)}
      onHoverEnd={() => (dropdownOpen = false)}
    >
      <DropdownMenuAugment.Trigger>
        <TextTooltipAugment content="More actions" triggerOn={[TooltipTriggerOn.Hover]}>
          <IconButtonAugment
            size={1}
            variant="ghost"
            color="neutral"
            disabled={!editable}
            class="c-task-action-button c-task-action-button--more"
            on:click={() => (dropdownOpen = !dropdownOpen)}
          >
            <DotsVertical />
          </IconButtonAugment>
        </TextTooltipAugment>
      </DropdownMenuAugment.Trigger>
      <DropdownMenuAugment.Content size={1} side="bottom" align="end">
        <DropdownMenuAugment.Item onSelect={handleRunWithSubAgent}>
          <Branch slot="iconLeft" />
          Run with sub-agent
        </DropdownMenuAugment.Item>
      </DropdownMenuAugment.Content>
    </DropdownMenuAugment.Root>
  {/if}
</div>

<style>
  .c-task-action-buttons {
    display: flex;
    align-items: center;
    gap: var(--ds-spacing-1);
    margin-right: var(--ds-spacing-1);
  }
</style>
