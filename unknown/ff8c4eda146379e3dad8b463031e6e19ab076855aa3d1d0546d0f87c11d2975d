package main

import (
	"context"
	"encoding/json"
	"flag"
	"fmt"
	"io"
	"log"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"time"

	"cloud.google.com/go/storage"
)

// BlobMetadata represents the metadata for a single blob
type BlobMetadata struct {
	BlobID      string            `json:"blob_id"`
	Filename    string            `json:"filename"`
	Size        int64             `json:"size"`
	ContentType string            `json:"content_type"`
	Metadata    map[string]string `json:"metadata"`
	Created     string            `json:"created"`
	Updated     string            `json:"updated"`
}

// TenantConfig represents the tenant configuration
type TenantConfig struct {
	ProjectID        string `json:"project_id"`
	BlobBucketName   string `json:"blob_bucket_name"`
	BlobBucketPrefix string `json:"blob_bucket_prefix"`
}

// Config represents the command line configuration
type Config struct {
	BlobIDs      []string
	TenantName   string
	OutputDir    string
	MaxWorkers   int
	MetadataOnly bool
	TenantConfig TenantConfig
}

// Result represents the operation result
type Result struct {
	TotalRequested int            `json:"total_requested"`
	Successful     int            `json:"successful"`
	Failed         int            `json:"failed"`
	Duration       float64        `json:"duration"`
	Speed          float64        `json:"speed"`
	BlobMetadata   []BlobMetadata `json:"blob_metadata"`
	Errors         []string       `json:"errors,omitempty"`
}

func main() {
	var (
		blobIDsFile  = flag.String("blob-ids", "", "File containing blob IDs (one per line)")
		tenantName   = flag.String("tenant", "", "Tenant name")
		outputDir    = flag.String("output", "", "Output directory")
		maxWorkers   = flag.Int("workers", 50, "Maximum number of concurrent workers")
		metadataOnly = flag.Bool("metadata-only", false, "Only fetch metadata, don't download blobs")
	)
	flag.Parse()

	if *blobIDsFile == "" || *tenantName == "" || *outputDir == "" {
		log.Fatal("blob-ids, tenant, and output are required")
	}

	// Read blob IDs
	blobIDs, err := readBlobIDs(*blobIDsFile)
	if err != nil {
		log.Fatalf("Failed to read blob IDs: %v", err)
	}

	// Get tenant config
	tenantConfig, err := getTenantConfig(*tenantName)
	if err != nil {
		log.Fatalf("Failed to get tenant config: %v", err)
	}

	config := Config{
		BlobIDs:      blobIDs,
		TenantName:   *tenantName,
		OutputDir:    *outputDir,
		MaxWorkers:   *maxWorkers,
		MetadataOnly: *metadataOnly,
		TenantConfig: tenantConfig,
	}

	// Create output directory
	if err := os.MkdirAll(config.OutputDir, 0755); err != nil {
		log.Fatalf("Failed to create output directory: %v", err)
	}

	// Run the operation
	result, err := processBlobsWithGCS(config)
	if err != nil {
		log.Fatalf("Failed to process blobs: %v", err)
	}

	// Output result as JSON
	resultJSON, err := json.MarshalIndent(result, "", "  ")
	if err != nil {
		log.Fatalf("Failed to marshal result: %v", err)
	}

	fmt.Println(string(resultJSON))
}

func readBlobIDs(filename string) ([]string, error) {
	content, err := os.ReadFile(filename)
	if err != nil {
		return nil, err
	}

	// Try to decode as simple JSON array first
	var jsonArray []string
	if err := json.Unmarshal(content, &jsonArray); err == nil {
		return jsonArray, nil
	}

	// Try to decode as nested structure with "initial" key
	var nestedBlobIDs struct {
		Initial []string `json:"initial"`
	}
	if err := json.Unmarshal(content, &nestedBlobIDs); err == nil {
		return nestedBlobIDs.Initial, nil
	}

	// If JSON parsing fails, try to read as newline-separated text
	lines := string(content)
	var blobIDs []string
	for _, line := range strings.Split(lines, "\n") {
		line = strings.TrimSpace(line)
		if line != "" {
			blobIDs = append(blobIDs, line)
		}
	}

	return blobIDs, nil
}

func getTenantConfig(tenantName string) (TenantConfig, error) {
	// This is a simplified mapping - in production this would integrate
	// with the existing tenant configuration system
	tenantConfigs := map[string]TenantConfig{
		"i0-vanguard0": {
			ProjectID:        "system-services-prod",
			BlobBucketName:   "us-prod-blobs-nonenterprise",
			BlobBucketPrefix: "789b1a18a6970fc4de4cf3aa89a35827/blobs",
		},
		"i0-vanguard1": {
			ProjectID:        "system-services-prod",
			BlobBucketName:   "us-prod-blobs-nonenterprise",
			BlobBucketPrefix: "ce26583a2f0a0d8130e698bb1f651f27/blobs",
		},
		"i0-vanguard2": {
			ProjectID:        "system-services-prod",
			BlobBucketName:   "us-prod-blobs-nonenterprise",
			BlobBucketPrefix: "74fee6f8b118d38476247c3206dc9c31/blobs",
		},
		"i0-vanguard3": {
			ProjectID:        "system-services-prod",
			BlobBucketName:   "us-prod-blobs-nonenterprise",
			BlobBucketPrefix: "51fefac672e825104ea261d63418d070/blobs",
		},
		"i0-vanguard4": {
			ProjectID:        "system-services-prod",
			BlobBucketName:   "us-prod-blobs-nonenterprise",
			BlobBucketPrefix: "e2dc5f8565810210d35d64826ba22120/blobs",
		},
		"i0-vanguard5": {
			ProjectID:        "system-services-prod",
			BlobBucketName:   "us-prod-blobs-nonenterprise",
			BlobBucketPrefix: "a2a834210447eb987d1eff9b11384ed/blobs",
		},
		"i0-vanguard6": {
			ProjectID:        "system-services-prod",
			BlobBucketName:   "us-prod-blobs-nonenterprise",
			BlobBucketPrefix: "b2e745e73d8d01ccda38498d1a4de5e7/blobs",
		},
		"i0-vanguard7": {
			ProjectID:        "system-services-prod",
			BlobBucketName:   "us-prod-blobs-nonenterprise",
			BlobBucketPrefix: "a94be078d83a082a8b1b30ecc12dc173/blobs",
		},
		"i1-vanguard0": {
			ProjectID:        "system-services-prod",
			BlobBucketName:   "us-prod-blobs-nonenterprise",
			BlobBucketPrefix: "eac8863de00e749b73eae23300f69e92/blobs",
		},
		"i1-vanguard1": {
			ProjectID:        "system-services-prod",
			BlobBucketName:   "us-prod-blobs-nonenterprise",
			BlobBucketPrefix: "7521771141a76e2a9e686a3d89390fb5/blobs",
		},
		"i1-vanguard2": {
			ProjectID:        "system-services-prod",
			BlobBucketName:   "us-prod-blobs-nonenterprise",
			BlobBucketPrefix: "ba2cda8f56ce724d5b2f32a1641471e1/blobs",
		},
		"i1-vanguard3": {
			ProjectID:        "system-services-prod",
			BlobBucketName:   "us-prod-blobs-nonenterprise",
			BlobBucketPrefix: "49f0bdc475cce55ac6e802ad69ed19a5/blobs",
		},
		"i1-vanguard4": {
			ProjectID:        "system-services-prod",
			BlobBucketName:   "us-prod-blobs-nonenterprise",
			BlobBucketPrefix: "a71d9d7889482b73100a45c8ce3953c6/blobs",
		},
		"i1-vanguard5": {
			ProjectID:        "system-services-prod",
			BlobBucketName:   "us-prod-blobs-nonenterprise",
			BlobBucketPrefix: "2cf7cd507edd34c1c05a954544474084/blobs",
		},
		"i1-vanguard6": {
			ProjectID:        "system-services-prod",
			BlobBucketName:   "us-prod-blobs-nonenterprise",
			BlobBucketPrefix: "4900799c06e1299aed1e2d0707417f15/blobs",
		},
		"i1-vanguard7": {
			ProjectID:        "system-services-prod",
			BlobBucketName:   "us-prod-blobs-nonenterprise",
			BlobBucketPrefix: "c0041ab726a7096a56a21d7009419600/blobs",
		},
	}

	config, exists := tenantConfigs[tenantName]
	if !exists {
		return TenantConfig{}, fmt.Errorf("unknown tenant: %s", tenantName)
	}

	return config, nil
}

// normalizeFilePath converts Windows-style paths to Unix-style and handles problematic characters
func normalizeFilePath(path string) string {
	// Convert Windows backslashes to forward slashes
	normalized := strings.ReplaceAll(path, "\\", "/")

	// Remove or replace problematic characters that might cause issues on Unix systems
	// Replace multiple consecutive slashes with single slash
	for strings.Contains(normalized, "//") {
		normalized = strings.ReplaceAll(normalized, "//", "/")
	}

	// Remove leading slash if present (we want relative paths)
	normalized = strings.TrimPrefix(normalized, "/")

	// Handle special characters that might cause issues
	// Replace characters that are problematic in filenames
	problematicChars := map[string]string{
		":":  "_", // Windows drive letters, time separators
		"*":  "_", // Wildcards
		"?":  "_", // Wildcards
		"\"": "_", // Quotes
		"<":  "_", // Redirects
		">":  "_", // Redirects
		"|":  "_", // Pipes
	}

	for old, new := range problematicChars {
		normalized = strings.ReplaceAll(normalized, old, new)
	}

	// Handle template variables like ${bussiPackage} by keeping them as-is
	// They're already properly escaped in the path

	return normalized
}

func processBlobsWithGCS(config Config) (*Result, error) {
	ctx := context.Background()

	// Create GCS client
	client, err := storage.NewClient(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to create storage client: %v", err)
	}
	defer client.Close()

	bucket := client.Bucket(config.TenantConfig.BlobBucketName)

	startTime := time.Now()

	// Create channels for work distribution
	blobChan := make(chan string, len(config.BlobIDs))
	resultChan := make(chan BlobMetadata, len(config.BlobIDs))
	errorChan := make(chan error, len(config.BlobIDs))

	// Send blob IDs to work channel
	for _, blobID := range config.BlobIDs {
		blobChan <- blobID
	}
	close(blobChan)

	// Start workers
	var wg sync.WaitGroup
	for i := 0; i < config.MaxWorkers; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			for blobID := range blobChan {
				metadata, err := processSingleBlob(ctx, bucket, blobID, config)
				if err != nil {
					errorChan <- fmt.Errorf("blob %s: %v", blobID, err)
				} else if metadata != nil {
					resultChan <- *metadata
				}
			}
		}()
	}

	// Close result channels when all workers are done
	go func() {
		wg.Wait()
		close(resultChan)
		close(errorChan)
	}()

	// Collect results
	var blobMetadata []BlobMetadata
	var errors []string

	for {
		select {
		case metadata, ok := <-resultChan:
			if !ok {
				resultChan = nil
			} else {
				blobMetadata = append(blobMetadata, metadata)
			}
		case err, ok := <-errorChan:
			if !ok {
				errorChan = nil
			} else {
				errors = append(errors, err.Error())
			}
		}

		if resultChan == nil && errorChan == nil {
			break
		}
	}

	duration := time.Since(startTime).Seconds()
	successful := len(blobMetadata)
	failed := len(errors)

	result := &Result{
		TotalRequested: len(config.BlobIDs),
		Successful:     successful,
		Failed:         failed,
		Duration:       duration,
		Speed:          float64(successful) / duration,
		BlobMetadata:   blobMetadata,
		Errors:         errors,
	}

	return result, nil
}

func processSingleBlob(ctx context.Context, bucket *storage.BucketHandle, blobID string, config Config) (*BlobMetadata, error) {
	blobPath := fmt.Sprintf("%s/%s", config.TenantConfig.BlobBucketPrefix, blobID)
	obj := bucket.Object(blobPath)

	// Get object attributes
	attrs, err := obj.Attrs(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get attributes: %v", err)
	}

	// Extract filename from metadata
	filename := "unknown/" + blobID
	if attrs.Metadata != nil {
		if path, exists := attrs.Metadata["path"]; exists {
			filename = path
		}
	}

	metadata := &BlobMetadata{
		BlobID:      blobID,
		Filename:    filename,
		Size:        attrs.Size,
		ContentType: attrs.ContentType,
		Metadata:    attrs.Metadata,
		Created:     attrs.Created.Format(time.RFC3339),
		Updated:     attrs.Updated.Format(time.RFC3339),
	}

	// Download blob if not metadata-only
	if !config.MetadataOnly {
		err := downloadBlob(ctx, obj, blobID, config.OutputDir, filename)
		if err != nil {
			return nil, fmt.Errorf("failed to download blob: %v", err)
		}
	}

	return metadata, nil
}

func downloadBlob(ctx context.Context, obj *storage.ObjectHandle, blobID, outputDir, filename string) error {
	// Create reader
	reader, err := obj.NewReader(ctx)
	if err != nil {
		return fmt.Errorf("failed to create reader: %v", err)
	}
	defer reader.Close()

	// Determine output path
	var outputPath string
	if filename != "" && !strings.HasPrefix(filename, "unknown/") {
		// Normalize the filename to handle Windows/Linux path differences
		normalizedFilename := normalizeFilePath(filename)
		outputPath = filepath.Join(outputDir, normalizedFilename)
		// Create directory structure
		if err := os.MkdirAll(filepath.Dir(outputPath), 0755); err != nil {
			return fmt.Errorf("failed to create directory: %v", err)
		}
	} else {
		// Use blob ID as filename
		outputPath = filepath.Join(outputDir, blobID)
	}

	// Create output file
	file, err := os.Create(outputPath)
	if err != nil {
		return fmt.Errorf("failed to create file: %v", err)
	}
	defer file.Close()

	// Copy data
	_, err = io.Copy(file, reader)
	if err != nil {
		return fmt.Errorf("failed to copy data: %v", err)
	}

	return nil
}
