# Merged PRs by cmsflash

This document lists all merged pull requests authored by cmsflash (<EMAIL>) in the augmentcode/augment repository, ordered by merge date from most recent to earliest.

Total merged PRs: 212 (as of June 16, 2025)

## Merged PRs

## 2025 PRs (June)

### 1. More Gemini Explorations
- **PR #**: [#28039](https://github.com/augmentcode/augment/pull/28039)
- **Date Opened**: 2025-06-15
- **Date Merged**: 2025-06-16
- **Title**: More Gemini Explorations

### 2. Re-enable Grok SWE
- **PR #**: [#28031](https://github.com/augmentcode/augment/pull/28031)
- **Date Opened**: 2025-06-14
- **Date Merged**: 2025-06-16
- **Title**: Re-enable Grok SWE

### 3. Fix a bug in small-view test case in AgentReplayEval
- **PR #**: [#28033](https://github.com/augmentcode/augment/pull/28033)
- **Date Opened**: 2025-06-15
- **Date Merged**: 2025-06-15
- **Title**: Fix a bug in small-view test case in AgentReplayEval

### 4. Honor the default for agent replay eval dataset
- **PR #**: [#28014](https://github.com/augmentcode/augment/pull/28014)
- **Date Opened**: 2025-06-14
- **Date Merged**: 2025-06-15
- **Title**: Honor the default for agent replay eval dataset

### 5. Support OpenAI and xAI in Edit Agent Eval
- **PR #**: [#28013](https://github.com/augmentcode/augment/pull/28013)
- **Date Opened**: 2025-06-14
- **Date Merged**: 2025-06-15
- **Title**: Support OpenAI and xAI in Edit Agent Eval

### 6. Fix OpenAI/xAI MCP patching
- **PR #**: [#28029](https://github.com/augmentcode/augment/pull/28029)
- **Date Opened**: 2025-06-14
- **Date Merged**: 2025-06-15
- **Title**: Fix OpenAI/xAI MCP patching

### 7. Fix OpenAI max token setting (take 2)
- **PR #**: [#28034](https://github.com/augmentcode/augment/pull/28034)
- **Date Opened**: 2025-06-15
- **Date Merged**: 2025-06-15
- **Title**: Fix OpenAI max token setting (take 2)

### 8. Add Edit Eval Analysis Notebook
- **PR #**: [#28018](https://github.com/augmentcode/augment/pull/28018)
- **Date Opened**: 2025-06-14
- **Date Merged**: 2025-06-15
- **Title**: Add Edit Eval Analysis Notebook

### 9. Update Gemini experimental code with example pickling
- **PR #**: [#28015](https://github.com/augmentcode/augment/pull/28015)
- **Date Opened**: 2025-06-14
- **Date Merged**: 2025-06-15
- **Title**: Update Gemini experimental code with example pickling

### 10. Update Augment QA evaluation scripts and model trial scripts
- **PR #**: [#28016](https://github.com/augmentcode/augment/pull/28016)
- **Date Opened**: 2025-06-14
- **Date Merged**: 2025-06-15
- **Title**: Update Augment QA evaluation scripts and model trial scripts

### 11. Add README for AugmentQA
- **PR #**: [#27998](https://github.com/augmentcode/augment/pull/27998)
- **Date Opened**: 2025-06-13
- **Date Merged**: 2025-06-14
- **Title**: Add README for AugmentQA

### 12. Unflag Grok SWE for now
- **PR #**: [#27954](https://github.com/augmentcode/augment/pull/27954)
- **Date Opened**: 2025-06-13
- **Date Merged**: 2025-06-13
- **Title**: Unflag Grok SWE for now

### 13. Test GPT-4.1-2025-04-14 model in OpenAI experiments
- **PR #**: [#27950](https://github.com/augmentcode/augment/pull/27950)
- **Date Opened**: 2025-06-13
- **Date Merged**: 2025-06-13
- **Title**: Test GPT-4.1-2025-04-14 model in OpenAI experiments

### 14. Notebook changes for OpenAI max token setting
- **PR #**: [#27859](https://github.com/augmentcode/augment/pull/27859)
- **Date Opened**: 2025-06-12
- **Date Merged**: 2025-06-13
- **Title**: Notebook changes for OpenAI max token setting

### 15. Revert "Fix OpenAI max token setting"
- **PR #**: [#27929](https://github.com/augmentcode/augment/pull/27929)
- **Date Opened**: 2025-06-13
- **Date Merged**: 2025-06-13
- **Title**: Revert "Fix OpenAI max token setting"

### 16. Fix OpenAI max token setting
- **PR #**: [#27848](https://github.com/augmentcode/augment/pull/27848)
- **Date Opened**: 2025-06-12
- **Date Merged**: 2025-06-13
- **Title**: Fix OpenAI max token setting

### 17. Give the model picker and Opus back to SHV
- **PR #**: [#27858](https://github.com/augmentcode/augment/pull/27858)
- **Date Opened**: 2025-06-12
- **Date Merged**: 2025-06-13
- **Title**: Give the model picker and Opus back to SHV

### 18. Release Grok SWE Agent to Dogfood
- **PR #**: [#27871](https://github.com/augmentcode/augment/pull/27871)
- **Date Opened**: 2025-06-12
- **Date Merged**: 2025-06-12
- **Title**: Release Grok SWE Agent to Dogfood

### 19. Support and deploy xAI experimental SWE model to dogfood
- **PR #**: [#27751](https://github.com/augmentcode/augment/pull/27751)
- **Date Opened**: 2025-06-10
- **Date Merged**: 2025-06-11
- **Title**: Support and deploy xAI experimental SWE model to dogfood

### 20. Revert "Support and deploy xAI experimental SWE model to dogfood"
- **PR #**: [#27734](https://github.com/augmentcode/augment/pull/27734)
- **Date Opened**: 2025-06-10
- **Date Merged**: 2025-06-10
- **Title**: Revert "Support and deploy xAI experimental SWE model to dogfood"

### 21. Support and deploy xAI experimental SWE model to dogfood
- **PR #**: [#27601](https://github.com/augmentcode/augment/pull/27601)
- **Date Opened**: 2025-06-09
- **Date Merged**: 2025-06-10
- **Title**: Support and deploy xAI experimental SWE model to dogfood

### 22. Do not fail for unkownn tool name
- **PR #**: [#27128](https://github.com/augmentcode/augment/pull/27128)
- **Date Opened**: 2025-06-01
- **Date Merged**: 2025-06-09
- **Title**: Do not fail for unkownn tool name

### 23. Update new model eval code
- **PR #**: [#27154](https://github.com/augmentcode/augment/pull/27154)
- **Date Opened**: 2025-06-02
- **Date Merged**: 2025-06-09
- **Title**: Update new model eval code

### 24. Systems changes for xAI
- **PR #**: [#27598](https://github.com/augmentcode/augment/pull/27598)
- **Date Opened**: 2025-06-09
- **Date Merged**: 2025-06-09
- **Title**: Systems changes for xAI

### 25. Improve AugmentQA report coloration
- **PR #**: [#27153](https://github.com/augmentcode/augment/pull/27153)
- **Date Opened**: 2025-06-02
- **Date Merged**: 2025-06-09
- **Title**: Improve AugmentQA report coloration

### 26. Additional Gemini replay changes
- **PR #**: [#27152](https://github.com/augmentcode/augment/pull/27152)
- **Date Opened**: 2025-06-02
- **Date Merged**: 2025-06-09
- **Title**: Additional Gemini replay changes

### 27. Gemini laziness experiments
- **PR #**: [#27132](https://github.com/augmentcode/augment/pull/27132)
- **Date Opened**: 2025-06-01
- **Date Merged**: 2025-06-09
- **Title**: Gemini laziness experiments

### 28. Update ReplayEval for Gemini
- **PR #**: [#27131](https://github.com/augmentcode/augment/pull/27131)
- **Date Opened**: 2025-06-01
- **Date Merged**: 2025-06-02
- **Title**: Update ReplayEval for Gemini

### 29. More XML experiments
- **PR #**: [#27127](https://github.com/augmentcode/augment/pull/27127)
- **Date Opened**: 2025-06-01
- **Date Merged**: 2025-06-01
- **Title**: More XML experiments

### 30. Claude 3.7 experiments
- **PR #**: [#27126](https://github.com/augmentcode/augment/pull/27126)
- **Date Opened**: 2025-06-01
- **Date Merged**: 2025-06-01
- **Title**: Claude 3.7 experiments

## 2025 PRs (May)

### 31. Support Gemini in EditAgentEval
- **PR #**: [#25557](https://github.com/augmentcode/augment/pull/25557)
- **Date Opened**: 2025-05-10
- **Date Merged**: 2025-05-31
- **Title**: Support Gemini in EditAgentEval

## 2025 PRs (March)

### 32. Deploy Binks Gemini 2.0 Flash models
- **PR #**: [#17141](https://github.com/augmentcode/augment/pull/17141)
- **Date Opened**: 2024-12-26
- **Date Merged**: 2025-03-06
- **Title**: Deploy Binks Gemini 2.0 Flash models

### 33. Update Binks Claude 128K models to Chatanol 1.18.3
- **PR #**: [#17150](https://github.com/augmentcode/augment/pull/17150)
- **Date Opened**: 2024-12-26
- **Date Merged**: 2025-03-06
- **Title**: Update Binks Claude 128K models to Chatanol 1.18.3

## 2025 PRs (January)

### 34. AU-5810 Add /document
- **PR #**: [#16605](https://github.com/augmentcode/augment/pull/16605)
- **Date Opened**: 2024-12-11
- **Date Merged**: 2025-01-15
- **Title**: AU-5810 Add /document

### 35. Experiments for custom Chat guidelines
- **PR #**: [#17219](https://github.com/augmentcode/augment/pull/17219)
- **Date Opened**: 2024-12-31
- **Date Merged**: 2025-01-07
- **Title**: Experiments for custom Chat guidelines

### 36. Support evaluating Chat systems with different user or workspace guidelines
- **PR #**: [#17218](https://github.com/augmentcode/augment/pull/17218)
- **Date Opened**: 2024-12-31
- **Date Merged**: 2025-01-07
- **Title**: Support evaluating Chat systems with different user or workspace guidelines

### 37. Experimental changes for o1 (non-preview)
- **PR #**: [#17190](https://github.com/augmentcode/augment/pull/17190)
- **Date Opened**: 2024-12-30
- **Date Merged**: 2025-01-07
- **Title**: Experimental changes for o1 (non-preview)

### 38. Add Binks o1 V6
- **PR #**: [#16952](https://github.com/augmentcode/augment/pull/16952)
- **Date Opened**: 2024-12-19
- **Date Merged**: 2025-01-07
- **Title**: Add Binks o1 V6

### 39. Add Binks Claude V10, V11, V11.1 to AI Tutor battles
- **PR #**: [#17191](https://github.com/augmentcode/augment/pull/17191)
- **Date Opened**: 2024-12-30
- **Date Merged**: 2025-01-06
- **Title**: Add Binks Claude V10, V11, V11.1 to AI Tutor battles

### 40. Add Binks o1 and o1 mini V1.1 to V5.1
- **PR #**: [#17184](https://github.com/augmentcode/augment/pull/17184)
- **Date Opened**: 2024-12-29
- **Date Merged**: 2025-01-06
- **Title**: Add Binks o1 and o1 mini V1.1 to V5.1

### 41. Add support for o1 (non-preview) for Chat
- **PR #**: [#16951](https://github.com/augmentcode/augment/pull/16951)
- **Date Opened**: 2024-12-19
- **Date Merged**: 2025-01-06
- **Title**: Add support for o1 (non-preview) for Chat

### 42. Use integer version parameter, not factory injection, to handle retrieval section versions
- **PR #**: [#17179](https://github.com/augmentcode/augment/pull/17179)
- **Date Opened**: 2024-12-28
- **Date Merged**: 2025-01-02
- **Title**: Use integer version parameter, not factory injection, to handle retrieval section versions

### 43. Return raw outputs in dev-zhuoran
- **PR #**: [#17220](https://github.com/augmentcode/augment/pull/17220)
- **Date Opened**: 2024-12-31
- **Date Merged**: 2025-01-01
- **Title**: Return raw outputs in dev-zhuoran

## 2024 PRs (December)

### 44. Automatic Bazel changes
- **PR #**: [#17180](https://github.com/augmentcode/augment/pull/17180)
- **Date Opened**: 2024-12-28
- **Date Merged**: 2024-12-28
- **Title**: Automatic Bazel changes

### 45. Deploy Binks Claude V11 to Dogfood and AI Tutors
- **PR #**: [#17008](https://github.com/augmentcode/augment/pull/17008)
- **Date Opened**: 2024-12-19
- **Date Merged**: 2024-12-27
- **Title**: Deploy Binks Claude V11 to Dogfood and AI Tutors

### 46. Add deployment config for Binks Claude V11
- **PR #**: [#16889](https://github.com/augmentcode/augment/pull/16889)
- **Date Opened**: 2024-12-18
- **Date Merged**: 2024-12-27
- **Title**: Add deployment config for Binks Claude V11

### 47. Add Binks Claude prompt formatter V11, which addresses the unclosed XML code block issue
- **PR #**: [#16887](https://github.com/augmentcode/augment/pull/16887)
- **Date Opened**: 2024-12-18
- **Date Merged**: 2024-12-27
- **Title**: Add Binks Claude prompt formatter V11, which addresses the unclosed XML code block issue

### 48. Add system prompts to separate files for easy diffing
- **PR #**: [#17151](https://github.com/augmentcode/augment/pull/17151)
- **Date Opened**: 2024-12-26
- **Date Merged**: 2024-12-26
- **Title**: Add system prompts to separate files for easy diffing

### 49. Update Binks Claude 128K models to Chatanol 1.18.3
- **PR #**: [#17150](https://github.com/augmentcode/augment/pull/17150)
- **Date Opened**: 2024-12-26
- **Date Merged**: 2024-12-26
- **Title**: Update Binks Claude 128K models to Chatanol 1.18.3

### 50. Update deploy-undeploy scripts to trunk models
- **PR #**: [#17143](https://github.com/augmentcode/augment/pull/17143)
- **Date Opened**: 2024-12-26
- **Date Merged**: 2024-12-26
- **Title**: Update deploy-undeploy scripts to trunk models

### 51. Add market analysis notebook
- **PR #**: [#17084](https://github.com/augmentcode/augment/pull/17084)
- **Date Opened**: 2024-12-21
- **Date Merged**: 2024-12-21
- **Title**: Add market analysis notebook

### 52. Release custom Chat guidelines to Vanguard.
- **PR #**: [#17080](https://github.com/augmentcode/augment/pull/17080)
- **Date Opened**: 2024-12-21
- **Date Merged**: 2024-12-21
- **Title**: Release custom Chat guidelines to Vanguard.

### 53. Remove token counter from guidelines prompt builder since we no longer truncate guidelines
- **PR #**: [#17014](https://github.com/augmentcode/augment/pull/17014)
- **Date Opened**: 2024-12-20
- **Date Merged**: 2024-12-20
- **Title**: Remove token counter from guidelines prompt builder since we no longer truncate guidelines

### 54. Fix raw output for /chat
- **PR #**: [#16989](https://github.com/augmentcode/augment/pull/16989)
- **Date Opened**: 2024-12-19
- **Date Merged**: 2024-12-19
- **Title**: Fix raw output for /chat

### 55. Add trial notebook for OpenAI models
- **PR #**: [#16915](https://github.com/augmentcode/augment/pull/16915)
- **Date Opened**: 2024-12-18
- **Date Merged**: 2024-12-19
- **Title**: Add trial notebook for OpenAI models

### 56. Release "Fix using Augment" to non-debug users
- **PR #**: [#16825](https://github.com/augmentcode/augment/pull/16825)
- **Date Opened**: 2024-12-17
- **Date Merged**: 2024-12-17
- **Title**: Release "Fix using Augment" to non-debug users

### 57. Add 128K variants for all Binks Claude/o1 16 models.
- **PR #**: [#16554](https://github.com/augmentcode/augment/pull/16554)
- **Date Opened**: 2024-12-10
- **Date Merged**: 2024-12-13
- **Title**: Add 128K variants for all Binks Claude/o1 16 models.

### 58. AU-5505 Backend for custom Chat prompts
- **PR #**: [#16417](https://github.com/augmentcode/augment/pull/16417)
- **Date Opened**: 2024-12-06
- **Date Merged**: 2024-12-11
- **Title**: AU-5505 Backend for custom Chat prompts

### 59. AU-5670 Add Binks GPT-4o V2, V3 and deploy V3 to Dogfood and AI Tutors
- **PR #**: [#16579](https://github.com/augmentcode/augment/pull/16579)
- **Date Opened**: 2024-12-11
- **Date Merged**: 2024-12-11
- **Title**: AU-5670 Add Binks GPT-4o V2, V3 and deploy V3 to Dogfood and AI Tutors

### 60. Add generation time metric in AugmentQA
- **PR #**: [#16424](https://github.com/augmentcode/augment/pull/16424)
- **Date Opened**: 2024-12-07
- **Date Merged**: 2024-12-10
- **Title**: Add generation time metric in AugmentQA

### 61. Add a default eval config
- **PR #**: [#16468](https://github.com/augmentcode/augment/pull/16468)
- **Date Opened**: 2024-12-09
- **Date Merged**: 2024-12-10
- **Title**: Add a default eval config

### 62. Remove accidentally merged experimental code in AugmentQA comparer
- **PR #**: [#16421](https://github.com/augmentcode/augment/pull/16421)
- **Date Opened**: 2024-12-06
- **Date Merged**: 2024-12-07
- **Title**: Remove accidentally merged experimental code in AugmentQA comparer

### 63. Allow environment variables with "="s in Determined jobs
- **PR #**: [#15875](https://github.com/augmentcode/augment/pull/15875)
- **Date Opened**: 2024-11-24
- **Date Merged**: 2024-12-05
- **Title**: Allow environment variables with "="s in Determined jobs

### 64. Front-load generation indicator and add error handling for commit message generation on the VS Code source control panel
- **PR #**: [#15186](https://github.com/augmentcode/augment/pull/15186)
- **Date Opened**: 2024-11-10
- **Date Merged**: 2024-12-05
- **Title**: Front-load generation indicator and add error handling for commit message generation on the VS Code source control panel

### 65. Tombstone Binks Claude V4.1
- **PR #**: [#15631](https://github.com/augmentcode/augment/pull/15631)
- **Date Opened**: 2024-11-20
- **Date Merged**: 2024-12-05
- **Title**: Tombstone Binks Claude V4.1

## 2024 PRs (November)

### 66. Add option to automatically generate a comparison report for AugmentQA
- **PR #**: [#15867](https://github.com/augmentcode/augment/pull/15867)
- **Date Opened**: 2024-11-23
- **Date Merged**: 2024-11-26
- **Title**: Add option to automatically generate a comparison report for AugmentQA

### 67. Minor codestyle improvements in Augment client and remote Chat systems
- **PR #**: [#15941](https://github.com/augmentcode/augment/pull/15941)
- **Date Opened**: 2024-11-26
- **Date Merged**: 2024-11-26
- **Title**: Minor codestyle improvements in Augment client and remote Chat systems

### 68. Amend Binks Claude V6 to support commit message generation
- **PR #**: [#15730](https://github.com/augmentcode/augment/pull/15730)
- **Date Opened**: 2024-11-21
- **Date Merged**: 2024-11-26
- **Title**: Amend Binks Claude V6 to support commit message generation

### 69. Commit message generation experiments
- **PR #**: [#15885](https://github.com/augmentcode/augment/pull/15885)
- **Date Opened**: 2024-11-25
- **Date Merged**: 2024-11-25
- **Title**: Commit message generation experiments

### 70. Improve AugmentQA comparison report visual formatting
- **PR #**: [#15866](https://github.com/augmentcode/augment/pull/15866)
- **Date Opened**: 2024-11-23
- **Date Merged**: 2024-11-24
- **Title**: Improve AugmentQA comparison report visual formatting

### 71. Amend Binks Claude V7 to support commit message generation
- **PR #**: [#15705](https://github.com/augmentcode/augment/pull/15705)
- **Date Opened**: 2024-11-21
- **Date Merged**: 2024-11-21
- **Title**: Amend Binks Claude V7 to support commit message generation

### 72. Add namespace flags for dev-zhuoran
- **PR #**: [#15513](https://github.com/augmentcode/augment/pull/15513)
- **Date Opened**: 2024-11-18
- **Date Merged**: 2024-11-21
- **Title**: Add namespace flags for dev-zhuoran

### 73. Deploy Binks Claude V4.1
- **PR #**: [#15514](https://github.com/augmentcode/augment/pull/15514)
- **Date Opened**: 2024-11-18
- **Date Merged**: 2024-11-19
- **Title**: Deploy Binks Claude V4.1

### 74. AU-5086 Add Binks Claude V4.1, V5.1, V6.1 (V4-6 with commit message generation support)
- **PR #**: [#15131](https://github.com/augmentcode/augment/pull/15131)
- **Date Opened**: 2024-11-08
- **Date Merged**: 2024-11-18
- **Title**: AU-5086 Add Binks Claude V4.1, V5.1, V6.1 (V4-6 with commit message generation support)

### 75. Update /generate-commit-message-stream endpoint to use the new prompt formatter
- **PR #**: [#15378](https://github.com/augmentcode/augment/pull/15378)
- **Date Opened**: 2024-11-14
- **Date Merged**: 2024-11-18
- **Title**: Update /generate-commit-message-stream endpoint to use the new prompt formatter

### 76. Remove V1 of commit message prompt formatter and update V2
- **PR #**: [#15377](https://github.com/augmentcode/augment/pull/15377)
- **Date Opened**: 2024-11-14
- **Date Merged**: 2024-11-18
- **Title**: Remove V1 of commit message prompt formatter and update V2

### 77. Temp
- **PR #**: [#15503](https://github.com/augmentcode/augment/pull/15503)
- **Date Opened**: 2024-11-16
- **Date Merged**: 2024-11-16
- **Title**: Temp

### 78. Temp: Remove V1 of commit message prompt formatter and update V2
- **PR #**: [#15379](https://github.com/augmentcode/augment/pull/15379)
- **Date Opened**: 2024-11-14
- **Date Merged**: 2024-11-14
- **Title**: Temp: Remove V1 of commit message prompt formatter and update V2

### 79. AU-5086 Add commit message generation prompt formatter V2
- **PR #**: [#15081](https://github.com/augmentcode/augment/pull/15081)
- **Date Opened**: 2024-11-07
- **Date Merged**: 2024-11-13
- **Title**: AU-5086 Add commit message generation prompt formatter V2

## 2024 PRs (October)

### 80. AU-5061 Remove "Explain issues using Augment"
- **PR #**: [#14344](https://github.com/augmentcode/augment/pull/14344)
- **Date Opened**: 2024-10-23
- **Date Merged**: 2024-10-23
- **Title**: AU-5061 Remove "Explain issues using Augment"

### 81. AU-4658 Sort and organize /command diagnostics by line ranges
- **PR #**: [#14271](https://github.com/augmentcode/augment/pull/14271)
- **Date Opened**: 2024-10-22
- **Date Merged**: 2024-10-23
- **Title**: AU-4658 Sort and organize /command diagnostics by line ranges

### 82. Pass diagnostics, not just messages, to /command web views
- **PR #**: [#14232](https://github.com/augmentcode/augment/pull/14232)
- **Date Opened**: 2024-10-21
- **Date Merged**: 2024-10-22
- **Title**: Pass diagnostics, not just messages, to /command web views

### 83. Extract diagnostic fetching from /fix and /explain_issues prompt building
- **PR #**: [#14231](https://github.com/augmentcode/augment/pull/14231)
- **Date Opened**: 2024-10-21
- **Date Merged**: 2024-10-21
- **Title**: Extract diagnostic fetching from /fix and /explain_issues prompt building

### 84. Add terminal wrapper
- **PR #**: [#14151](https://github.com/augmentcode/augment/pull/14151)
- **Date Opened**: 2024-10-18
- **Date Merged**: 2024-10-19
- **Title**: Add terminal wrapper

### 85. Further cleanup of old "Fix using Augment"
- **PR #**: [#14124](https://github.com/augmentcode/augment/pull/14124)
- **Date Opened**: 2024-10-18
- **Date Merged**: 2024-10-18
- **Title**: Further cleanup of old "Fix using Augment"

### 86. Fix "Fix using Augment" and "Explain issues using Augment" when current selection is outside of the triggering diagnostics' lines
- **PR #**: [#13962](https://github.com/augmentcode/augment/pull/13962)
- **Date Opened**: 2024-10-16
- **Date Merged**: 2024-10-18
- **Title**: Fix "Fix using Augment" and "Explain issues using Augment" when current selection is outside of the triggering diagnostics' lines

### 87. Add "Explain using Augment" to quick fix menu
- **PR #**: [#13887](https://github.com/augmentcode/augment/pull/13887)
- **Date Opened**: 2024-10-16
- **Date Merged**: 2024-10-18
- **Title**: Add "Explain using Augment" to quick fix menu

### 88. Fix "Fix using Augment" appearing without a diagnostic
- **PR #**: [#13997](https://github.com/augmentcode/augment/pull/13997)
- **Date Opened**: 2024-10-17
- **Date Merged**: 2024-10-17
- **Title**: Fix "Fix using Augment" appearing without a diagnostic

### 89. Add invisible command /explain_issues
- **PR #**: [#13885](https://github.com/augmentcode/augment/pull/13885)
- **Date Opened**: 2024-10-16
- **Date Merged**: 2024-10-17
- **Title**: Add invisible command /explain_issues

### 90. Add option to hide a /command in UI
- **PR #**: [#13884](https://github.com/augmentcode/augment/pull/13884)
- **Date Opened**: 2024-10-16
- **Date Merged**: 2024-10-17
- **Title**: Add option to hide a /command in UI

### 91. Add OpenAI Chat model evaluation configs
- **PR #**: [#12763](https://github.com/augmentcode/augment/pull/12763)
- **Date Opened**: 2024-09-27
- **Date Merged**: 2024-10-17
- **Title**: Add OpenAI Chat model evaluation configs

### 92. Remove extra new conversation for Quick Fix
- **PR #**: [#13868](https://github.com/augmentcode/augment/pull/13868)
- **Date Opened**: 2024-10-15
- **Date Merged**: 2024-10-15
- **Title**: Remove extra new conversation for Quick Fix

### 93. Move Quick Fix `when` condition from command definition to pallete definition
- **PR #**: [#13865](https://github.com/augmentcode/augment/pull/13865)
- **Date Opened**: 2024-10-15
- **Date Merged**: 2024-10-15
- **Title**: Move Quick Fix `when` condition from command definition to pallete definition

### 94. AU-4447 Add editor diagnostics to /fix message
- **PR #**: [#12915](https://github.com/augmentcode/augment/pull/12915)
- **Date Opened**: 2024-09-30
- **Date Merged**: 2024-10-03
- **Title**: AU-4447 Add editor diagnostics to /fix message

### 95. Handle OpenAI policy rejections
- **PR #**: [#12589](https://github.com/augmentcode/augment/pull/12589)
- **Date Opened**: 2024-09-25
- **Date Merged**: 2024-10-02
- **Title**: Handle OpenAI policy rejections

### 96. Update Chat replay notebook
- **PR #**: [#12764](https://github.com/augmentcode/augment/pull/12764)
- **Date Opened**: 2024-09-27
- **Date Merged**: 2024-10-02
- **Title**: Update Chat replay notebook

## 2024 PRs (September)

### 97. Move diagnostic types from augment-api to utils/types
- **PR #**: [#12772](https://github.com/augmentcode/augment/pull/12772)
- **Date Opened**: 2024-09-27
- **Date Merged**: 2024-09-28
- **Title**: Move diagnostic types from augment-api to utils/types

### 98. Gemini Pro now uses Pro formatter, not Flash
- **PR #**: [#12709](https://github.com/augmentcode/augment/pull/12709)
- **Date Opened**: 2024-09-26
- **Date Merged**: 2024-09-26
- **Title**: Gemini Pro now uses Pro formatter, not Flash

### 99. Fix Chat prompt formatting
- **PR #**: [#12708](https://github.com/augmentcode/augment/pull/12708)
- **Date Opened**: 2024-09-26
- **Date Merged**: 2024-09-26
- **Title**: Fix Chat prompt formatting

### 100. Deploy OpenAI o1 to Dogfood and AI Tutors
- **PR #**: [#12462](https://github.com/augmentcode/augment/pull/12462)
- **Date Opened**: 2024-09-23
- **Date Merged**: 2024-09-25
- **Title**: Deploy OpenAI o1 to Dogfood and AI Tutors

### 101. Add OpenAI o1 and GPT-4o into chat model mix
- **PR #**: [#12157](https://github.com/augmentcode/augment/pull/12157)
- **Date Opened**: 2024-09-17
- **Date Merged**: 2024-09-25
- **Title**: Add OpenAI o1 and GPT-4o into chat model mix

### 102. Support BF16 LLaMa 3/3.1
- **PR #**: [#11941](https://github.com/augmentcode/augment/pull/11941)
- **Date Opened**: 2024-09-12
- **Date Merged**: 2024-09-14
- **Title**: Support BF16 LLaMa 3/3.1

### 103. Fix StarCoder 2 conversion script
- **PR #**: [#11908](https://github.com/augmentcode/augment/pull/11908)
- **Date Opened**: 2024-09-11
- **Date Merged**: 2024-09-13
- **Title**: Fix StarCoder 2 conversion script

### 104. Support user-guided retrieval and context exchange ID in remote eval
- **PR #**: [#11401](https://github.com/augmentcode/augment/pull/11401)
- **Date Opened**: 2024-08-29
- **Date Merged**: 2024-09-13
- **Title**: Support user-guided retrieval and context exchange ID in remote eval

### 105. Fix chat replay's retrieval, massively reduce cost, and support UG
- **PR #**: [#10673](https://github.com/augmentcode/augment/pull/10673)
- **Date Opened**: 2024-08-13
- **Date Merged**: 2024-09-13
- **Title**: Fix chat replay's retrieval, massively reduce cost, and support UG

### 106. Make AugmentClient.chat_stream() yield ChatResponse's, not str's
- **PR #**: [#11047](https://github.com/augmentcode/augment/pull/11047)
- **Date Opened**: 2024-08-21
- **Date Merged**: 2024-09-11
- **Title**: Make AugmentClient.chat_stream() yield ChatResponse's, not str's

### 107. Add new eval configs
- **PR #**: [#11112](https://github.com/augmentcode/augment/pull/11112)
- **Date Opened**: 2024-08-22
- **Date Merged**: 2024-09-03
- **Title**: Add new eval configs

## 2024 PRs (August)

### 108. Release Binks V12 to all customers
- **PR #**: [#11232](https://github.com/augmentcode/augment/pull/11232)
- **Date Opened**: 2024-08-24
- **Date Merged**: 2024-08-28
- **Title**: Release Binks V12 to all customers

### 109. Use embedder retrieval limit (256) for Binks V11's UG retrieval
- **PR #**: [#11113](https://github.com/augmentcode/augment/pull/11113)
- **Date Opened**: 2024-08-22
- **Date Merged**: 2024-08-27
- **Title**: Use embedder retrieval limit (256) for Binks V11's UG retrieval

### 110. Undeploy and tombstone Binks V11
- **PR #**: [#11231](https://github.com/augmentcode/augment/pull/11231)
- **Date Opened**: 2024-08-24
- **Date Merged**: 2024-08-27
- **Title**: Undeploy and tombstone Binks V11

### 111. Update Dogfood default to Binks V12
- **PR #**: [#11230](https://github.com/augmentcode/augment/pull/11230)
- **Date Opened**: 2024-08-24
- **Date Merged**: 2024-08-27
- **Title**: Update Dogfood default to Binks V12

### 112. Deploy Binks V12 to all tenants
- **PR #**: [#11229](https://github.com/augmentcode/augment/pull/11229)
- **Date Opened**: 2024-08-24
- **Date Merged**: 2024-08-27
- **Title**: Deploy Binks V12 to all tenants

### 113. Add Binks V12
- **PR #**: [#11114](https://github.com/augmentcode/augment/pull/11114)
- **Date Opened**: 2024-08-22
- **Date Merged**: 2024-08-24
- **Title**: Add Binks V12

### 114. Update Augment QA configs
- **PR #**: [#10722](https://github.com/augmentcode/augment/pull/10722)
- **Date Opened**: 2024-08-13
- **Date Merged**: 2024-08-22
- **Title**: Update Augment QA configs

### 115. Move Binks V11 to 4 GPUs
- **PR #**: [#10822](https://github.com/augmentcode/augment/pull/10822)
- **Date Opened**: 2024-08-15
- **Date Merged**: 2024-08-16
- **Title**: Move Binks V11 to 4 GPUs

### 116. Update LLaMa 3.1 evals
- **PR #**: [#10782](https://github.com/augmentcode/augment/pull/10782)
- **Date Opened**: 2024-08-14
- **Date Merged**: 2024-08-14
- **Title**: Update LLaMa 3.1 evals

### 117. Deploy Binks V11 as Dogfood default
- **PR #**: [#10754](https://github.com/augmentcode/augment/pull/10754)
- **Date Opened**: 2024-08-14
- **Date Merged**: 2024-08-14
- **Title**: Deploy Binks V11 as Dogfood default

### 118. Fix FastForwardLlama3Instruct70b16kModelFp8 checkpoint hash
- **PR #**: [#10772](https://github.com/augmentcode/augment/pull/10772)
- **Date Opened**: 2024-08-14
- **Date Merged**: 2024-08-14
- **Title**: Fix FastForwardLlama3Instruct70b16kModelFp8 checkpoint hash

### 119. Rename Binks V10 LLaMa 3.1 16K as Binks V11 (next trunk)
- **PR #**: [#10752](https://github.com/augmentcode/augment/pull/10752)
- **Date Opened**: 2024-08-14
- **Date Merged**: 2024-08-14
- **Title**: Rename Binks V10 LLaMa 3.1 16K as Binks V11 (next trunk)

### 120. Update Binks V10 LLaMa 3.1 16K's tokenizer from legacy to new
- **PR #**: [#10681](https://github.com/augmentcode/augment/pull/10681)
- **Date Opened**: 2024-08-13
- **Date Merged**: 2024-08-13
- **Title**: Update Binks V10 LLaMa 3.1 16K's tokenizer from legacy to new

### 121. Undeploy and tombstone Binks V10 YaRN 16K from staging
- **PR #**: [#10619](https://github.com/augmentcode/augment/pull/10619)
- **Date Opened**: 2024-08-10
- **Date Merged**: 2024-08-13
- **Title**: Undeploy and tombstone Binks V10 YaRN 16K from staging

### 122. Change LLaMa 3.1 reference RoPE scaling implementation links to permalinks
- **PR #**: [#10631](https://github.com/augmentcode/augment/pull/10631)
- **Date Opened**: 2024-08-12
- **Date Merged**: 2024-08-12
- **Title**: Change LLaMa 3.1 reference RoPE scaling implementation links to permalinks

### 123. Add Binks LLaMa 3.1 (initially with 16K context)
- **PR #**: [#9876](https://github.com/augmentcode/augment/pull/9876)
- **Date Opened**: 2024-07-24
- **Date Merged**: 2024-08-09
- **Title**: Add Binks LLaMa 3.1 (initially with 16K context)

### 124. Add multi-variant execution to InstructHumanEval test script for YaRN
- **PR #**: [#10197](https://github.com/augmentcode/augment/pull/10197)
- **Date Opened**: 2024-08-01
- **Date Merged**: 2024-08-01
- **Title**: Add multi-variant execution to InstructHumanEval test script for YaRN

### 125. Reinstate SHA check in weight loading
- **PR #**: [#10075](https://github.com/augmentcode/augment/pull/10075)
- **Date Opened**: 2024-07-30
- **Date Merged**: 2024-08-01
- **Title**: Reinstate SHA check in weight loading

## 2024 PRs (July)

### 126. Fix 16K Binks model name
- **PR #**: [#10067](https://github.com/augmentcode/augment/pull/10067)
- **Date Opened**: 2024-07-29
- **Date Merged**: 2024-07-30
- **Title**: Fix 16K Binks model name

### 127. Merge and improve LLaMa and DSv1 FB-FF converter
- **PR #**: [#9885](https://github.com/augmentcode/augment/pull/9885)
- **Date Opened**: 2024-07-25
- **Date Merged**: 2024-07-29
- **Title**: Merge and improve LLaMa and DSv1 FB-FF converter

### 128. Add assertion to prevent double fixing of LLaMa checkpoints
- **PR #**: [#9886](https://github.com/augmentcode/augment/pull/9886)
- **Date Opened**: 2024-07-25
- **Date Merged**: 2024-07-26
- **Title**: Add assertion to prevent double fixing of LLaMa checkpoints

### 129. Fix StarCoder 2 max pos and FBWD-FFWD checkpoint conversion
- **PR #**: [#9368](https://github.com/augmentcode/augment/pull/9368)
- **Date Opened**: 2024-07-11
- **Date Merged**: 2024-07-25
- **Title**: Fix StarCoder 2 max pos and FBWD-FFWD checkpoint conversion

### 130. Extract RoPE frequency calculation as a function
- **PR #**: [#9865](https://github.com/augmentcode/augment/pull/9865)
- **Date Opened**: 2024-07-24
- **Date Merged**: 2024-07-24
- **Title**: Extract RoPE frequency calculation as a function

### 131. Add HF download script
- **PR #**: [#9861](https://github.com/augmentcode/augment/pull/9861)
- **Date Opened**: 2024-07-24
- **Date Merged**: 2024-07-24
- **Title**: Add HF download script

### 132. Experiments
- **PR #**: [#3833](https://github.com/augmentcode/augment/pull/3833)
- **Date Opened**: 2024-01-30
- **Date Merged**: 2024-07-24
- **Title**: Experiments

### 133. Canonicalize `AugmentModelClient`'s chat return types and add new feature support to non-streaming chat interface
- **PR #**: [#9213](https://github.com/augmentcode/augment/pull/9213)
- **Date Opened**: 2024-07-08
- **Date Merged**: 2024-07-23
- **Title**: Canonicalize `AugmentModelClient`'s chat return types and add new feature support to non-streaming chat interface

### 134. Update ModelSpec subclass post-init assertions
- **PR #**: [#9635](https://github.com/augmentcode/augment/pull/9635)
- **Date Opened**: 2024-07-18
- **Date Merged**: 2024-07-20
- **Title**: Update ModelSpec subclass post-init assertions

### 135. Deploy Binks LLaMa 3 16K (Chatanol 1.16.3) to staging
- **PR #**: [#9537](https://github.com/augmentcode/augment/pull/9537)
- **Date Opened**: 2024-07-17
- **Date Merged**: 2024-07-17
- **Title**: Deploy Binks LLaMa 3 16K (Chatanol 1.16.3) to staging

### 136. Add deployment config for 16K-context Binks LLaMa 3
- **PR #**: [#8981](https://github.com/augmentcode/augment/pull/8981)
- **Date Opened**: 2024-07-01
- **Date Merged**: 2024-07-17
- **Title**: Add deployment config for 16K-context Binks LLaMa 3

### 137. Support YaRN in FastForward (un-revert)
- **PR #**: [#9349](https://github.com/augmentcode/augment/pull/9349)
- **Date Opened**: 2024-07-11
- **Date Merged**: 2024-07-16
- **Title**: Support YaRN in FastForward (un-revert)

### 138. Revert "Pull the non-None requirement for `max_position_embeddings` into the config class `ModelArch`"
- **PR #**: [#9441](https://github.com/augmentcode/augment/pull/9441)
- **Date Opened**: 2024-07-13
- **Date Merged**: 2024-07-13
- **Title**: Revert "Pull the non-None requirement for `max_position_embeddings` into the config class `ModelArch`"

### 139. Fix FastBackward training loop
- **PR #**: [#9445](https://github.com/augmentcode/augment/pull/9445)
- **Date Opened**: 2024-07-13
- **Date Merged**: 2024-07-13
- **Title**: Fix FastBackward training loop

### 140. Pull the non-None requirement for `max_position_embeddings` into the config class `ModelArch`
- **PR #**: [#9387](https://github.com/augmentcode/augment/pull/9387)
- **Date Opened**: 2024-07-12
- **Date Merged**: 2024-07-12
- **Title**: Pull the non-None requirement for `max_position_embeddings` into the config class `ModelArch`

### 141. Support YaRN in FastForward
- **PR #**: [#8980](https://github.com/augmentcode/augment/pull/8980)
- **Date Opened**: 2024-07-01
- **Date Merged**: 2024-07-10
- **Title**: Support YaRN in FastForward

### 142. Fix minor issues and code style before the YaRN stack
- **PR #**: [#9158](https://github.com/augmentcode/augment/pull/9158)
- **Date Opened**: 2024-07-06
- **Date Merged**: 2024-07-09
- **Title**: Fix minor issues and code style before the YaRN stack

### 143. Add LLaMa 3 70B Instruct in FFWD to research models
- **PR #**: [#9157](https://github.com/augmentcode/augment/pull/9157)
- **Date Opened**: 2024-07-06
- **Date Merged**: 2024-07-09
- **Title**: Add LLaMa 3 70B Instruct in FFWD to research models

### 144. Add MoE debug notebooks
- **PR #**: [#9252](https://github.com/augmentcode/augment/pull/9252)
- **Date Opened**: 2024-07-09
- **Date Merged**: 2024-07-09
- **Title**: Add MoE debug notebooks

### 145. Add summarization for InstructHumanEval
- **PR #**: [#9035](https://github.com/augmentcode/augment/pull/9035)
- **Date Opened**: 2024-07-02
- **Date Merged**: 2024-07-03
- **Title**: Add summarization for InstructHumanEval

### 146. Remove 1 extra blank line
- **PR #**: [#9034](https://github.com/augmentcode/augment/pull/9034)
- **Date Opened**: 2024-07-02
- **Date Merged**: 2024-07-02
- **Title**: Remove 1 extra blank line

### 147. Add regression test jupyter for 16K-context Binks LLaMa 3
- **PR #**: [#9036](https://github.com/augmentcode/augment/pull/9036)
- **Date Opened**: 2024-07-02
- **Date Merged**: 2024-07-02
- **Title**: Add regression test jupyter for 16K-context Binks LLaMa 3

### 148. Evaluation for 16K-context Binks LLaMa 3
- **PR #**: [#8930](https://github.com/augmentcode/augment/pull/8930)
- **Date Opened**: 2024-06-28
- **Date Merged**: 2024-07-02
- **Title**: Evaluation for 16K-context Binks LLaMa 3

### 149. Add new token for Zhuoran (post rotation)
- **PR #**: [#9019](https://github.com/augmentcode/augment/pull/9019)
- **Date Opened**: 2024-07-01
- **Date Merged**: 2024-07-02
- **Title**: Add new token for Zhuoran (post rotation)

## 2024 PRs (June)

### 150. Fix FBWD sample config and README
- **PR #**: [#8115](https://github.com/augmentcode/augment/pull/8115)
- **Date Opened**: 2024-06-08
- **Date Merged**: 2024-06-09
- **Title**: Fix FBWD sample config and README

## 2024 PRs (May)

### 151. Update dMoE forward diff notebook
- **PR #**: [#7592](https://github.com/augmentcode/augment/pull/7592)
- **Date Opened**: 2024-05-24
- **Date Merged**: 2024-05-25
- **Title**: Update dMoE forward diff notebook

### 152. Add dMoE forward diff reproduction notebook
- **PR #**: [#7540](https://github.com/augmentcode/augment/pull/7540)
- **Date Opened**: 2024-05-24
- **Date Merged**: 2024-05-24
- **Title**: Add dMoE forward diff reproduction notebook

### 153. Remove resolved TODO on discussion of FP32 support in FBWD
- **PR #**: [#7448](https://github.com/augmentcode/augment/pull/7448)
- **Date Opened**: 2024-05-22
- **Date Merged**: 2024-05-23
- **Title**: Remove resolved TODO on discussion of FP32 support in FBWD

### 154. Reduce FBWD example eval length to 8 examples (2 batches)
- **PR #**: [#6678](https://github.com/augmentcode/augment/pull/6678)
- **Date Opened**: 2024-05-01
- **Date Merged**: 2024-05-07
- **Title**: Reduce FBWD example eval length to 8 examples (2 batches)

### 155. Reformat FBWD args module
- **PR #**: [#6630](https://github.com/augmentcode/augment/pull/6630)
- **Date Opened**: 2024-04-29
- **Date Merged**: 2024-05-03
- **Title**: Reformat FBWD args module

## 2024 PRs (April)

### 156. Limit eval iters of the example commands in FBWD README
- **PR #**: [#6590](https://github.com/augmentcode/augment/pull/6590)
- **Date Opened**: 2024-04-29
- **Date Merged**: 2024-04-29
- **Title**: Limit eval iters of the example commands in FBWD README

### 157. Fix Zhuoran's experimental MoE imports
- **PR #**: [#6589](https://github.com/augmentcode/augment/pull/6589)
- **Date Opened**: 2024-04-29
- **Date Merged**: 2024-04-29
- **Title**: Fix Zhuoran's experimental MoE imports

### 158. AU-2389 Move checkpoint download into evluation Determined jobs
- **PR #**: [#6247](https://github.com/augmentcode/augment/pull/6247)
- **Date Opened**: 2024-04-17
- **Date Merged**: 2024-04-26
- **Title**: AU-2389 Move checkpoint download into evluation Determined jobs

### 159. Move all non-argparse eval logic to eval_lib
- **PR #**: [#5720](https://github.com/augmentcode/augment/pull/5720)
- **Date Opened**: 2024-03-30
- **Date Merged**: 2024-04-25
- **Title**: Move all non-argparse eval logic to eval_lib

### 160. Make eval.evaluate() accept Pythonic arguments
- **PR #**: [#5635](https://github.com/augmentcode/augment/pull/5635)
- **Date Opened**: 2024-03-28
- **Date Merged**: 2024-04-25
- **Title**: Make eval.evaluate() accept Pythonic arguments

### 161. AU-2389 Temporarily fix unexpected blocking by eval.py when wait_for_completion=False
- **PR #**: [#6277](https://github.com/augmentcode/augment/pull/6277)
- **Date Opened**: 2024-04-18
- **Date Merged**: 2024-04-24
- **Title**: AU-2389 Temporarily fix unexpected blocking by eval.py when wait_for_completion=False

### 162. Quick fix to unblock FBWD training after TensorFlow removal
- **PR #**: [#6460](https://github.com/augmentcode/augment/pull/6460)
- **Date Opened**: 2024-04-24
- **Date Merged**: 2024-04-24
- **Title**: Quick fix to unblock FBWD training after TensorFlow removal

### 163. Add checkpoint deletion notebook
- **PR #**: [#6266](https://github.com/augmentcode/augment/pull/6266)
- **Date Opened**: 2024-04-17
- **Date Merged**: 2024-04-17
- **Title**: Add checkpoint deletion notebook

### 164. AU-2207 Deduplicate fastbackward HuggingFace checkpoint loading code
- **PR #**: [#5677](https://github.com/augmentcode/augment/pull/5677)
- **Date Opened**: 2024-03-29
- **Date Merged**: 2024-04-17
- **Title**: AU-2207 Deduplicate fastbackward HuggingFace checkpoint loading code

### 165. Add Ampere (small) podspec and switch pre-train evals to it
- **PR #**: [#6107](https://github.com/augmentcode/augment/pull/6107)
- **Date Opened**: 2024-04-12
- **Date Merged**: 2024-04-17
- **Title**: Add Ampere (small) podspec and switch pre-train evals to it

### 166. Debug DBRX-dMoE disparity
- **PR #**: [#6151](https://github.com/augmentcode/augment/pull/6151)
- **Date Opened**: 2024-04-15
- **Date Merged**: 2024-04-15
- **Title**: Debug DBRX-dMoE disparity

### 167. Add 1- and 5- shot HumanEval to pre-training eval suite
- **PR #**: [#6041](https://github.com/augmentcode/augment/pull/6041)
- **Date Opened**: 2024-04-10
- **Date Merged**: 2024-04-10
- **Title**: Add 1- and 5- shot HumanEval to pre-training eval suite

### 168. wait_for_completion now defaults to summary_path's truth value
- **PR #**: [#5928](https://github.com/augmentcode/augment/pull/5928)
- **Date Opened**: 2024-04-08
- **Date Merged**: 2024-04-08
- **Title**: wait_for_completion now defaults to summary_path's truth value

### 169. Fix issue that eval.py does not serialize running summaries
- **PR #**: [#5890](https://github.com/augmentcode/augment/pull/5890)
- **Date Opened**: 2024-04-05
- **Date Merged**: 2024-04-05
- **Title**: Fix issue that eval.py does not serialize running summaries

### 170. Add assertion on summary format version
- **PR #**: [#5889](https://github.com/augmentcode/augment/pull/5889)
- **Date Opened**: 2024-04-05
- **Date Merged**: 2024-04-05
- **Title**: Add assertion on summary format version

### 171. AU-2234 Implement continuous evaluation for FastBackward
- **PR #**: [#5283](https://github.com/augmentcode/augment/pull/5283)
- **Date Opened**: 2024-03-19
- **Date Merged**: 2024-04-03
- **Title**: AU-2234 Implement continuous evaluation for FastBackward

### 172. Refactor eval summary on generation, saving, and synchronous wait
- **PR #**: [#5491](https://github.com/augmentcode/augment/pull/5491)
- **Date Opened**: 2024-03-23
- **Date Merged**: 2024-04-03
- **Title**: Refactor eval summary on generation, saving, and synchronous wait

## 2024 PRs (March)

### 173. Fix incorrect assertion in fastbackward checkpointing
- **PR #**: [#5676](https://github.com/augmentcode/augment/pull/5676)
- **Date Opened**: 2024-03-29
- **Date Merged**: 2024-03-30
- **Title**: Fix incorrect assertion in fastbackward checkpointing

### 174. Tiny docstring fix in fastbackward model parallelism
- **PR #**: [#5674](https://github.com/augmentcode/augment/pull/5674)
- **Date Opened**: 2024-03-29
- **Date Merged**: 2024-03-29
- **Title**: Tiny docstring fix in fastbackward model parallelism

### 175. AU-2323 Improve fastbackward error for non-existent dataset paths
- **PR #**: [#5678](https://github.com/augmentcode/augment/pull/5678)
- **Date Opened**: 2024-03-29
- **Date Merged**: 2024-03-29
- **Title**: AU-2323 Improve fastbackward error for non-existent dataset paths

### 176. Expose evaluation interface in Python
- **PR #**: [#5478](https://github.com/augmentcode/augment/pull/5478)
- **Date Opened**: 2024-03-22
- **Date Merged**: 2024-03-28
- **Title**: Expose evaluation interface in Python

### 177. Add Determined to research dependencies
- **PR #**: [#5357](https://github.com/augmentcode/augment/pull/5357)
- **Date Opened**: 2024-03-20
- **Date Merged**: 2024-03-22
- **Title**: Add Determined to research dependencies

### 178. Fix bugs in FastBackward launch.py and train.py
- **PR #**: [#5332](https://github.com/augmentcode/augment/pull/5332)
- **Date Opened**: 2024-03-20
- **Date Merged**: 2024-03-21
- **Title**: Fix bugs in FastBackward launch.py and train.py

### 179. Fix eval run serialization
- **PR #**: [#5334](https://github.com/augmentcode/augment/pull/5334)
- **Date Opened**: 2024-03-20
- **Date Merged**: 2024-03-20
- **Title**: Fix eval run serialization

### 180. AU-2232 Fix evaluation with empty checkpoint location
- **PR #**: [#5325](https://github.com/augmentcode/augment/pull/5325)
- **Date Opened**: 2024-03-19
- **Date Merged**: 2024-03-20
- **Title**: AU-2232 Fix evaluation with empty checkpoint location

### 181. Add example usage to eval.py docstring
- **PR #**: [#5137](https://github.com/augmentcode/augment/pull/5137)
- **Date Opened**: 2024-03-14
- **Date Merged**: 2024-03-19
- **Title**: Add example usage to eval.py docstring

### 182. Use Determined run name as summary label
- **PR #**: [#5135](https://github.com/augmentcode/augment/pull/5135)
- **Date Opened**: 2024-03-14
- **Date Merged**: 2024-03-19
- **Title**: Use Determined run name as summary label

### 183. Fix eval checkpoint reading and sample configs
- **PR #**: [#5184](https://github.com/augmentcode/augment/pull/5184)
- **Date Opened**: 2024-03-15
- **Date Merged**: 2024-03-19
- **Title**: Fix eval checkpoint reading and sample configs

### 184. Implement Determined checkpointing
- **PR #**: [#4638](https://github.com/augmentcode/augment/pull/4638)
- **Date Opened**: 2024-02-28
- **Date Merged**: 2024-03-15
- **Title**: Implement Determined checkpointing

### 185. Support eval for Determined checkpoints of FastBackward
- **PR #**: [#4601](https://github.com/augmentcode/augment/pull/4601)
- **Date Opened**: 2024-02-27
- **Date Merged**: 2024-03-15
- **Title**: Support eval for Determined checkpoints of FastBackward

### 186. Save all weights & only the last opt states in FB
- **PR #**: [#4419](https://github.com/augmentcode/augment/pull/4419)
- **Date Opened**: 2024-02-20
- **Date Merged**: 2024-03-15
- **Title**: Save all weights & only the last opt states in FB

### 187. Make FastBackward sample config more realistic
- **PR #**: [#4644](https://github.com/augmentcode/augment/pull/4644)
- **Date Opened**: 2024-02-28
- **Date Merged**: 2024-03-14
- **Title**: Make FastBackward sample config more realistic

## 2024 PRs (February)

### 188. Small cleanup of FastBackward
- **PR #**: [#4367](https://github.com/augmentcode/augment/pull/4367)
- **Date Opened**: 2024-02-16
- **Date Merged**: 2024-02-24
- **Title**: Small cleanup of FastBackward

### 189. Ignore Cargo targets for Determined
- **PR #**: [#4521](https://github.com/augmentcode/augment/pull/4521)
- **Date Opened**: 2024-02-23
- **Date Merged**: 2024-02-24
- **Title**: Ignore Cargo targets for Determined

### 190. Support example limit in API test for debugging
- **PR #**: [#4370](https://github.com/augmentcode/augment/pull/4370)
- **Date Opened**: 2024-02-16
- **Date Merged**: 2024-02-19
- **Title**: Support example limit in API test for debugging

### 191. Support separate eval launch and summarization
- **PR #**: [#4369](https://github.com/augmentcode/augment/pull/4369)
- **Date Opened**: 2024-02-16
- **Date Merged**: 2024-02-18
- **Title**: Support separate eval launch and summarization

### 192. Fix crash when no API/Hydra test cases pass.
- **PR #**: [#4368](https://github.com/augmentcode/augment/pull/4368)
- **Date Opened**: 2024-02-16
- **Date Merged**: 2024-02-16
- **Title**: Fix crash when no API/Hydra test cases pass.

### 193. Fix Determined service account setting
- **PR #**: [#4163](https://github.com/augmentcode/augment/pull/4163)
- **Date Opened**: 2024-02-09
- **Date Merged**: 2024-02-09
- **Title**: Fix Determined service account setting

### 194. Speed up CC-Eval execution by requiring A5000
- **PR #**: [#3995](https://github.com/augmentcode/augment/pull/3995)
- **Date Opened**: 2024-02-05
- **Date Merged**: 2024-02-08
- **Title**: Speed up CC-Eval execution by requiring A5000

### 195. Fix determined_common.launch_eval
- **PR #**: [#3994](https://github.com/augmentcode/augment/pull/3994)
- **Date Opened**: 2024-02-05
- **Date Merged**: 2024-02-06
- **Title**: Fix determined_common.launch_eval

### 196. Remove unreachable logic in research eval
- **PR #**: [#3903](https://github.com/augmentcode/augment/pull/3903)
- **Date Opened**: 2024-02-01
- **Date Merged**: 2024-02-05
- **Title**: Remove unreachable logic in research eval

### 197. Reduce eval GPU grade for cost
- **PR #**: [#3899](https://github.com/augmentcode/augment/pull/3899)
- **Date Opened**: 2024-02-01
- **Date Merged**: 2024-02-03
- **Title**: Reduce eval GPU grade for cost

### 198. Surface exec-based metrics in result JSON for API tests
- **PR #**: [#3810](https://github.com/augmentcode/augment/pull/3810)
- **Date Opened**: 2024-01-30
- **Date Merged**: 2024-02-01
- **Title**: Surface exec-based metrics in result JSON for API tests

### 199. Add non-hard Hydra 2-3L
- **PR #**: [#3887](https://github.com/augmentcode/augment/pull/3887)
- **Date Opened**: 2024-01-31
- **Date Merged**: 2024-02-01
- **Title**: Add non-hard Hydra 2-3L

### 200. Add CrossCode-Eval to pre-training evals
- **PR #**: [#3852](https://github.com/augmentcode/augment/pull/3852)
- **Date Opened**: 2024-01-31
- **Date Merged**: 2024-02-01
- **Title**: Add CrossCode-Eval to pre-training evals

### 201. Refactor API task metric aggregation
- **PR #**: [#3850](https://github.com/augmentcode/augment/pull/3850)
- **Date Opened**: 2024-01-31
- **Date Merged**: 2024-02-01
- **Title**: Refactor API task metric aggregation

## 2024 PRs (January)

### 202. Expose execution-based metrics from Hydra tests in the result JSON
- **PR #**: [#3558](https://github.com/augmentcode/augment/pull/3558)
- **Date Opened**: 2024-01-22
- **Date Merged**: 2024-01-31
- **Title**: Expose execution-based metrics from Hydra tests in the result JSON

### 203. Fix Hydra summarization keys
- **PR #**: [#3809](https://github.com/augmentcode/augment/pull/3809)
- **Date Opened**: 2024-01-30
- **Date Merged**: 2024-01-31
- **Title**: Fix Hydra summarization keys

### 204. Support HumanEval-FIM in summarization
- **PR #**: [#3796](https://github.com/augmentcode/augment/pull/3796)
- **Date Opened**: 2024-01-29
- **Date Merged**: 2024-01-30
- **Title**: Support HumanEval-FIM in summarization

### 205. Improve research eval script usability
- **PR #**: [#3683](https://github.com/augmentcode/augment/pull/3683)
- **Date Opened**: 2024-01-25
- **Date Merged**: 2024-01-27
- **Title**: Improve research eval script usability

### 206. Add template configs for pretrain eval
- **PR #**: [#3684](https://github.com/augmentcode/augment/pull/3684)
- **Date Opened**: 2024-01-25
- **Date Merged**: 2024-01-26
- **Title**: Add template configs for pretrain eval

### 207. Support result summarization in research eval
- **PR #**: [#3506](https://github.com/augmentcode/augment/pull/3506)
- **Date Opened**: 2024-01-18
- **Date Merged**: 2024-01-22
- **Title**: Support result summarization in research eval

### 208. Add prompt length assertion to StarCoder formatter
- **PR #**: [#3475](https://github.com/augmentcode/augment/pull/3475)
- **Date Opened**: 2024-01-18
- **Date Merged**: 2024-01-18
- **Title**: Add prompt length assertion to StarCoder formatter

### 209. Fix Determined ignore file for non-root ignorance and node_modules
- **PR #**: [#3399](https://github.com/augmentcode/augment/pull/3399)
- **Date Opened**: 2024-01-16
- **Date Merged**: 2024-01-16
- **Title**: Fix Determined ignore file for non-root ignorance and node_modules

### 210. Update and fix research model notebooks
- **PR #**: [#3320](https://github.com/augmentcode/augment/pull/3320)
- **Date Opened**: 2024-01-12
- **Date Merged**: 2024-01-13
- **Title**: Update and fix research model notebooks

### 211. Add Zhuoran to internal users
- **PR #**: [#3192](https://github.com/augmentcode/augment/pull/3192)
- **Date Opened**: 2024-01-09
- **Date Merged**: 2024-01-09
- **Title**: Add Zhuoran to internal users

### 212. Grant GCP access to Zhuoran
- **PR #**: [#3190](https://github.com/augmentcode/augment/pull/3190)
- **Date Opened**: 2024-01-09
- **Date Merged**: 2024-01-09
- **Title**: Grant GCP access to Zhuoran
