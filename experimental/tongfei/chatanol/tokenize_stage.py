import argparse
import base64
import logging
from typing import Any, Dict, List

import numpy as np
from base.tokenizers import create_tokenizer_by_name
from base.prompt_format_retrieve import (
    DocumentRetrieverPromptInput,
    ChatRetrieverPromptInput,
    get_retrieval_prompt_formatter_by_name,
)
from research.data.ray.ray_utils import AbstractRayActor, Ray<PERSON><PERSON>ner

from experimental.tongfei.data.binks_schemas import (
    SilverRetrievalTrainingInstance,
    TokenizedTrainingInstance,
)


class TokenizeStage(
    AbstractRayActor[SilverRetrievalTrainingInstance, TokenizedTrainingInstance]
):
    def __init__(
        self,
        tokenizer_name: str,
        dtype: str = "uint32",
        query_prompt_formatter: str = "chatanol6",
        key_prompt_formatter: str = "chatanol6-embedding-with-path-key",
        max_seq_length: int = 4096,
        max_retrieved_docs: int = 128,
    ):
        super().__init__(
            input_cls=SilverRetrievalTrainingInstance,
            output_cls=TokenizedTrainingInstance,
        )

        # Pull in registrations
        import research.core.prompt_formatters  # noqa: F401
        import research.retrieval.query_formatters  # noqa: F401
        import research.retrieval.chunk_formatters  # noqa: F401

        self.max_seq_length = max_seq_length
        self.max_retrieved_docs = max_retrieved_docs
        self.tokenizer = create_tokenizer_by_name(tokenizer_name)
        self.dtype = {"uint16": np.uint16, "uint32": np.uint32}[dtype]
        self.query_prompt_formatter = get_retrieval_prompt_formatter_by_name(
            query_prompt_formatter, self.tokenizer
        )
        self.key_prompt_formatter = get_retrieval_prompt_formatter_by_name(
            key_prompt_formatter, self.tokenizer
        )

        self.eok_token = self.tokenizer.special_tokens.end_of_key
        self.eod_token = self.tokenizer.special_tokens.eos
        self.pad_token = self.tokenizer.special_tokens.padding

    def _pack_prompt(
        self, prompt: List[int], pad_token: int, should_pad: bool = True
    ) -> str:
        if should_pad:
            prompt_arr = np.pad(
                prompt,
                (0, 1 + self.max_seq_length - len(prompt)),
                constant_values=pad_token,
            )
        else:
            prompt_arr = np.array(prompt)
        byte_data = prompt_arr.astype(self.dtype).newbyteorder("<").tobytes()
        # Convert to bytes and then base64 encode for JSON serialization
        return base64.b64encode(byte_data).decode("utf-8")

    def process(
        self, sample: SilverRetrievalTrainingInstance
    ) -> List[TokenizedTrainingInstance]:
        """
        Process a single SilverRetrievalTrainingInstance to create tokenized prompts.
        """
        try:
            question = sample.question
            chunks = sample.chunks
            perplexities = sample.perplexities
            doc_prompts = []

            for chunk, ppl in zip(chunks, perplexities):
                prompt_input = DocumentRetrieverPromptInput(
                    text=chunk.text,
                    path=chunk.parent_doc.path,
                )
                prompt = self.key_prompt_formatter.format_prompt(prompt_input).tokens()
                ppl_info_tokens = self.key_prompt_formatter.tokenizer.tokenize_safe(
                    f"{ppl}"
                )
                prompt.extend(ppl_info_tokens + [self.eok_token])

                if len(prompt) > self.max_seq_length:
                    raise ValueError(
                        f"Prompt too long: {len(prompt)} > {self.max_seq_length}"
                    )
                doc_prompts.append(prompt)

            doc_prompts = [
                prompt
                for _ppl_score, prompt in sorted(
                    zip(perplexities, doc_prompts), key=lambda x: x[0], reverse=True
                )
            ][: self.max_retrieved_docs]

            if len(question) == 0:
                question = "?"  # Hack to avoid empty questions

            retrieval_query_prompt = self.query_prompt_formatter.format_prompt(
                ChatRetrieverPromptInput(
                    prefix="",
                    suffix="",
                    path="",
                    message=question,
                    selected_code="",
                ),
            ).tokens()  # <eoq> already included in the query prompt formatter

            all_tokens = retrieval_query_prompt + sum(doc_prompts, [])
            result = TokenizedTrainingInstance(
                question=question,
                prompt_tokens=self._pack_prompt(
                    all_tokens, self.pad_token, should_pad=False
                ),
            )
            return [result]
        except Exception as e:
            logging.error(f"Error processing sample: {e}")
            return []


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("--mode", type=str, default="ray", choices=["local", "ray"])
    parser.add_argument("--input", type=str, help="Input dataset path")
    parser.add_argument("--output", type=str, help="Output dataset path")
    parser.add_argument("--tokenizer", type=str, help="Tokenizer name", default="qwen3")
    parser.add_argument(
        "--query-prompt-formatter",
        type=str,
        help="Query prompt formatter name",
        default="chatanol6",
    )
    parser.add_argument(
        "--key-prompt-formatter",
        type=str,
        help="Key prompt formatter name",
        default="chatanol6-embedding-with-path-key",
    )
    parser.add_argument("--dtype", type=str, help="uint16 or uint32", default="uint32")
    parser.add_argument("--num-workers", type=int, default=1, help="Number of workers")
    parser.add_argument(
        "--cpus-per-worker", type=int, default=8, help="Number of CPUs per worker"
    )
    args = parser.parse_args()

    with RayRunner(
        actor_cls=TokenizeStage,
        actor_args={
            "tokenizer_name": args.tokenizer,
            "dtype": args.dtype,
            "query_prompt_formatter": args.query_prompt_formatter,
            "key_prompt_formatter": args.key_prompt_formatter,
        },
        num_workers=args.num_workers,
        num_cpu_per_worker=args.cpus_per_worker,
        num_gpu_per_worker=0,  # Tokenization doesn't need GPUs
        local=args.mode == "local",
    ) as runner:
        runner.process_jsonl(args.input, args.output)
