#%%
import json
from research.eval.harness.utils import read_jsonl
import pandas as pd
import matplotlib.pyplot as plt


# i1_file = "/home/<USER>/augment/experimental/pranay/BQ Results May 29 2025 - I1.json"
# prod_file = "/home/<USER>/augment/experimental/pranay/BQ Results May 29 2025 - Not I0:I1.json"

new_i1_file = (
    "/home/<USER>/augment/experimental/pranay/BQ Results Apr 1 - Jun 1 2025 - i1.json"
)
new_prod_file = "/home/<USER>/augment/experimental/pranay/BQ Results Apr 1 - Jun 1 2025 - prod.json"

# df_i1 = pd.read_json(i1_file, lines=True)
# df_prod = pd.read_json(prod_file, lines=True)

df_new_i1 = pd.read_json(new_i1_file, lines=True)
df_new_prod = pd.read_json(new_prod_file, lines=True)

# prod_file = "/home/<USER>/augment/experimental/pranay/BQ Results June 5 2025.json"
# df_prod = pd.read_json(prod_file, lines=True)
#%%
def plot_data(df, title):
    def extract_char_count_midpoint(bin_str):
        """Extract midpoint of character count bin for plotting"""
        if bin_str == "All":
            return None
        start, end = map(int, bin_str.split("-"))
        return (start + end) / 2

    df_plot = df[df["character_count_bin"] != "All"].copy()
    df_plot["char_count_midpoint"] = df_plot["character_count_bin"].apply(
        extract_char_count_midpoint
    )
    df_overall = df[df["character_count_bin"] == "All"].copy()

    df_plot["num_requests"] = pd.to_numeric(df_plot["num_requests"])
    df_overall["num_requests"] = pd.to_numeric(df_overall["num_requests"])

    df_plot["percentage_of_total"] = df_plot.groupby(["user_agent", "model_name"])[
        "num_requests"
    ].transform(lambda x: (x / x.sum()) * 100)

    min_acceptance = min(df["acceptance_rate"])
    max_acceptance = max(df["acceptance_rate"])
    y_margin = (max_acceptance - min_acceptance) * 0.1
    y_min = max(0, min_acceptance - y_margin)
    y_max = max_acceptance + y_margin

    user_agents = df_plot["user_agent"].unique()

    fig, axes = plt.subplots(2, len(user_agents), figsize=(15, 10))
    if len(user_agents) == 1:
        axes = axes.reshape(-1, 1)

    for i, user_agent in enumerate(user_agents):
        ax_main = axes[0, i]
        ax_diff = axes[1, i]

        agent_data = df_plot[df_plot["user_agent"] == user_agent]
        agent_overall = df_overall[df_overall["user_agent"] == user_agent]

        models = sorted(agent_data["model_name"].unique())

        for model in models:
            model_data = agent_data[agent_data["model_name"] == model].sort_values(
                "char_count_midpoint"
            )
            marker_sizes = model_data["percentage_of_total"] * 10

            scatter = ax_main.scatter(
                model_data["char_count_midpoint"],
                model_data["acceptance_rate"],
                s=marker_sizes,
                alpha=0.7,
                label=f"{model}",
            )

            ax_main.plot(
                model_data["char_count_midpoint"],
                model_data["acceptance_rate"],
                color=scatter.get_facecolors()[0],
                linewidth=2,
                alpha=0.8,
            )

            for _, row in model_data.iterrows():
                ax_main.annotate(
                    f'{row["percentage_of_total"]:.1f}%',
                    (row["char_count_midpoint"], row["acceptance_rate"]),
                    xytext=(5, 5),
                    textcoords="offset points",
                    fontsize=8,
                    alpha=0.8,
                )

            overall_data = agent_overall[agent_overall["model_name"] == model]
            if len(overall_data) > 0:
                overall_rate = overall_data["acceptance_rate"].iloc[0]
                ax_main.axhline(
                    y=overall_rate,
                    linestyle="--",
                    alpha=0.7,
                    linewidth=1.5,
                    label=f"{model} (Overall avg: {overall_rate:.3f})",
                )

        if len(models) == 2:
            v1_1_model = next((m for m in models if "v1-1" in m), models[0])
            v3_2_model = next((m for m in models if "v3-2" in m), models[1])

            v1_1_data = agent_data[agent_data["model_name"] == v1_1_model].set_index(
                "char_count_midpoint"
            )
            v3_2_data = agent_data[agent_data["model_name"] == v3_2_model].set_index(
                "char_count_midpoint"
            )

            common_bins = v1_1_data.index.intersection(v3_2_data.index)

            differences = []
            bin_points = []
            for bin_midpoint in sorted(common_bins):
                rate_v1_1 = v1_1_data.loc[bin_midpoint, "acceptance_rate"]
                rate_v3_2 = v3_2_data.loc[bin_midpoint, "acceptance_rate"]
                diff = rate_v3_2 - rate_v1_1
                differences.append(diff)
                bin_points.append(bin_midpoint)

            ax_diff.plot(bin_points, differences, "o-", linewidth=2, markersize=6)
            ax_diff.axhline(y=0, color="black", linestyle="-", alpha=0.3)
            ax_diff.set_xlabel("Character Count (bin midpoint)")
            ax_diff.set_ylabel(f"Difference\n({v3_2_model} - {v1_1_model})")
            ax_diff.set_title(f"Acceptance Rate Difference\n{user_agent}")
            ax_diff.grid(True, alpha=0.3)

        ax_main.set_xlabel("Character Count (bin midpoint)")
        ax_main.set_ylabel("Acceptance Rate")
        ax_main.set_title(f"Acceptance Rate vs Character Count\n{user_agent} - {title}")
        ax_main.legend()
        ax_main.grid(True, alpha=0.3)
        ax_main.set_ylim(y_min, y_max)

    plt.tight_layout()
    plt.show()
#%%
plot_data(df_new_prod, "new prod data")
#%%
plot_data(df_new_i1, "new i1 data")
#%%
# plot_data(df_prod, "i1 data")
#%%
# plot_data(df_prod, "prod data")
#%%
