#%%
%load_ext autoreload
%autoreload 2


import json

from colorama import Fore, Style
from tqdm import tqdm

from base.tokenizers.qwen25coder_tokenizer import Qwen25CoderTokenizer
from experimental.pranay.utils.common_utils import (
    ExecuteModel,
    setup_qwen25_tokenizer,
    get_common_checkpoints,
    break_apart_prompt,
    annotate_chunks,
    to_prompt_chunk,
)
from experimental.pranay.viewed_content.viewed_content_utils import process_datum
from research.core.utils_for_file import read_jsonl_zst
from research.data.rag.hindsight_common import (
    EnderDataPromptFormattingConfig,
    HindsightCompletionDatum,
)
from research.eval.harness.metrics import (
    DatumTag,
    MetricsAggregator,
    compute_code_complete_metrics,
    metrics_to_str_dict,
)

tokenizer: Qwen25CoderTokenizer = setup_qwen25_tokenizer()
st = tokenizer.special_tokens
#%%
results = read_jsonl_zst(
    "/mnt/efs/augment/user/pranay/hindsight/2025-07-24_2025-07-29/dogfood-shard/data.jsonl.zst"
)
print(len(results))
#%%
MAX = 10
if MAX == -1:
    MAX = len(results)

datums: list[HindsightCompletionDatum] = []
for data in tqdm(results[:MAX]):
    recency_info = data["completion"]["request"]["recency_info"]
    assert recency_info is not None, "Recency info is required."
    viewed_content = recency_info.get("viewed_contents", [])
    if len(viewed_content) > 0:
        datums.append(HindsightCompletionDatum.from_dict(data))

rid_to_datum = {datum.completion.request_id: datum for datum in datums}

print(len(datums))
#%% md
## Run recency retriever without having to regenerate line/sig chunks
#%%
no_viewed_content_config = EnderDataPromptFormattingConfig(
    max_content_len=8352,
    input_fraction=0.25,
    prefix_fraction=0.75,
    max_path_tokens=50,
    max_dense_signature_tokens=1024,
    max_recency_retriever_tokens=1024,
    max_recency_retriever_viewed_content_tokens=0,
    max_diff_retriever_tokens=2048,
    include_diff_retriever=True,
    max_target_tokens=256,
)

viewed_content_config = EnderDataPromptFormattingConfig(
    max_content_len=8352,
    input_fraction=0.25,
    prefix_fraction=0.75,
    max_path_tokens=50,
    max_dense_signature_tokens=1024,
    max_recency_retriever_tokens=1024,
    max_recency_retriever_viewed_content_tokens=1024,
    max_diff_retriever_tokens=2048,
    include_diff_retriever=True,
    max_target_tokens=256,
)
#%%
PROMPT_NAME = "VC_last_prompt"
OUTPUT_NAME = "VC_last_output"
FILE_NAME = "VC_last_file.json"
CONFIG = viewed_content_config

rid_to_prompts = {}
for datum in tqdm(datums):
    rid_to_prompts[datum.completion.request_id] = process_datum(
        datum, PROMPT_NAME, CONFIG, tokenizer
    )
#%% md
### Other
#%%
checkpoints = get_common_checkpoints()
base_fp8_checkpoint = checkpoints["base_fp8"]

base_fp8_model = ExecuteModel(
    base_fp8_checkpoint[0],
    base_fp8_checkpoint[1],
    tokenizer,
    use_fp8=True,
)
#%%
for rid in tqdm(rid_to_prompts):
    VC_output = base_fp8_model.simple_call(rid_to_prompts[rid][PROMPT_NAME]).tokens
    rid_to_prompts[rid][OUTPUT_NAME] = VC_output
#%%
with open(FILE_NAME, "w") as f:
    json.dump(rid_to_prompts, f)
#%% md
### Analyze the quality of the VC prompts and outputs.
#%%
import pandas as pd
from collections import defaultdict
from experimental.pranay.utils.common_utils import print_data_summary

BASE = "/mnt/efs/augment/user/pranay/viewed_content/"
files = ["V3_2_no_VC_2k.json", "V3_2_VC_middle_2k.json", "V3_2_VC_last_2k.json"]

final_rid_to_prompt = defaultdict(dict)
for file in files:
    read_file = BASE + file
    with open(read_file, "r") as f:
        rid_to_prompts = json.load(f)

    for rid in rid_to_prompts:
        final_rid_to_prompt[rid].update(rid_to_prompts[rid])

print(len(final_rid_to_prompt))

# remove row if v3_2_no_VC_prompt is the same as other prompts
final_rid_to_prompt_clean = {}
for rid in final_rid_to_prompt:
    no_VC_prompt = final_rid_to_prompt[rid]["v3_2_no_VC_prompt"]
    VC_middle_prompt = final_rid_to_prompt[rid]["v3_2_VC_middle_prompt"]
    VC_last_prompt = final_rid_to_prompt[rid]["v3_2_VC_last_prompt"]
    if no_VC_prompt != VC_middle_prompt and no_VC_prompt != VC_last_prompt:
        final_rid_to_prompt_clean[rid] = final_rid_to_prompt[rid]

final_rid_to_prompt = final_rid_to_prompt_clean
print(len(final_rid_to_prompt))
#%%
agg = MetricsAggregator()
rows = ["v3_2_no_VC_output", "v3_2_VC_middle_output", "v3_2_VC_last_output"]

for rid in final_rid_to_prompt:
    ground_truth = final_rid_to_prompt[rid]["ground_truth"]
    for row in rows:
        output_tokens = final_rid_to_prompt[rid][row]
        response_tokens, _ = base_fp8_model.extract_generation_before_stop_tokens(
            output_tokens, [st.eos, st.pause]
        )
        response_str = tokenizer.detokenize(response_tokens)
        metrics = compute_code_complete_metrics(
            response_str, ground_truth
        ).to_float_dict()
        tags = [DatumTag("prompt", row)]
        agg.add(metrics, tags)

aggregated = agg.compute()
# metrics_to_str_dict(aggregated)
#%%
import re
from collections import defaultdict


def parse_metrics_dict(metrics_dict):
    """Parse metrics dictionary into structured format"""
    parsed_data = defaultdict(lambda: defaultdict(dict))

    for key, value in metrics_dict.items():
        # Extract components using regex
        match = re.match(r"prompt\.(.+)\.([^.]+)\.([^.]+)$", key)
        if not match:
            continue

        model_type, metric_name, stat_type = match.groups()
        parsed_data[metric_name][model_type][stat_type] = value

    return parsed_data


def format_avg_stderr(avg, stderr):
    """Format average and stderr as 'avg ± stderr'"""
    if avg is None or stderr is None:
        return "N/A"
    return f"{avg:.3f} ± {stderr:.3f}"


def create_metrics_table(metrics_dict):
    """Create formatted table from metrics dictionary"""
    parsed_data = parse_metrics_dict(metrics_dict)

    if not parsed_data:
        raise ValueError("No valid metrics found in dictionary")

    # Get all model types
    all_models = set()
    for metric_data in parsed_data.values():
        all_models.update(metric_data.keys())
    all_models = sorted(list(all_models))

    # Build table data
    table_data = []
    best_models = []

    for metric_name in sorted(parsed_data.keys()):
        row = {"Metric": metric_name}
        metric_avgs = {}

        # Add formatted values for each model
        for model in all_models:
            model_data = parsed_data[metric_name].get(model, {})
            avg = model_data.get("avg")
            stderr = model_data.get("stderr")

            row[model] = format_avg_stderr(avg, stderr)
            if avg is not None:
                metric_avgs[model] = avg

        # Find best model for this metric
        if metric_avgs:
            best_model = max(metric_avgs.keys(), key=lambda x: metric_avgs[x])
            row["Best"] = best_model
            best_models.append(best_model)
        else:
            row["Best"] = "N/A"
            best_models.append(None)

        table_data.append(row)

    # Create DataFrame
    df = pd.DataFrame(table_data)

    # Set Metric as index
    df.set_index("Metric", inplace=True)

    return df


def display_metrics_table(metrics_dict, highlight_best=True):
    """Display formatted metrics table with optional highlighting"""
    df = create_metrics_table(metrics_dict)

    if highlight_best:

        def highlight_best_values(row):
            best_model = row["Best"]
            styles = [""] * len(row)

            if best_model != "N/A" and best_model in row.index:
                best_idx = list(row.index).index(best_model)
                styles[best_idx] = "font-weight: bold; background-color: #90EE90"

            return styles

        styled_df = df.style.apply(highlight_best_values, axis=1)
        return styled_df

    return df


def print_metrics_table(metrics_dict):
    """Print metrics table to console"""
    df = create_metrics_table(metrics_dict)
    # print(df.to_string())
    return df


print_metrics_table(metrics_to_str_dict(aggregated))