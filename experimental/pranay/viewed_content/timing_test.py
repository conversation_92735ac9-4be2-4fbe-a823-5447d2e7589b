"""Granular timing tests for recency_retriever viewed content processing functions.

This module provides timing tests for individual functions in the recency processing
pipeline to identify performance bottlenecks.
"""

import time
import statistics
import random
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional

from base.prompt_format_completion.recency_utils import (
    limit_viewed_content_chunk_size,
    deduplicate_viewed_content_against_replacements,
)
from base.prompt_format.recency_info import ReplacementText, ViewedContentEvent


# ============================================================================
# DATA GENERATION HELPER FUNCTIONS
# ============================================================================


def create_viewed_content(
    path: str = "test/file.py",
    content: str = "default content",
    char_start: int = 0,
    line_start: int = 1,
    timestamp: Optional[datetime] = None,
    file_blob_name: Optional[str] = None,
) -> ViewedContentEvent:
    """Create a ViewedContentEvent with specified parameters."""
    if timestamp is None:
        timestamp = datetime.now()
    if file_blob_name is None:
        file_blob_name = f"blob_{hash(path) % 10000}"

    return ViewedContentEvent(
        path=path,
        file_blob_name=file_blob_name,
        visible_content=content,
        char_start=char_start,
        char_end=char_start + len(content),
        line_start=line_start,
        line_end=line_start + content.count("\n"),
        timestamp=timestamp,
    )


def create_replacement_text(
    path: str = "test/file.py",
    replacement_text: str = "new code",
    char_start: int = 0,
    timestamp: Optional[datetime] = None,
    blob_name: Optional[str] = None,
    present_in_blob: bool = True,
) -> ReplacementText:
    """Create a ReplacementText with specified parameters."""
    if timestamp is None:
        timestamp = datetime.now()
    if blob_name is None:
        blob_name = f"replacement_blob_{hash(path) % 10000}"

    return ReplacementText(
        path=path,
        blob_name=blob_name,
        char_start=char_start,
        char_end=char_start + len(replacement_text),
        replacement_text=replacement_text,
        timestamp=timestamp,
        present_in_blob=present_in_blob,
    )


# ============================================================================
# CONTENT GENERATION STRATEGIES
# ============================================================================


def generate_random_text_content(size_kb: int) -> str:
    """Generate random text content of specified size."""
    import string

    target_size = size_kb * 1024

    # Create random words
    words = []
    current_size = 0
    word_index = 0

    while current_size < target_size:
        # Generate a random word
        word_length = random.randint(3, 12)
        word = "".join(random.choices(string.ascii_lowercase, k=word_length))
        words.append(word)
        current_size += len(word) + 1  # +1 for space

        # Add newlines occasionally
        if word_index % 15 == 0:
            words.append("\n")
            current_size += 1

        word_index += 1

    return " ".join(words)[:target_size]


def generate_real_code_content(size_kb: int, index: int = 0) -> str:
    """Generate realistic Python code content of specified size."""
    target_size = size_kb * 1024

    base_pattern = """class DataProcessor{index}_{func_index}:
    '''A class for processing data with various methods.

    This class handles complex data transformations including
    filtering, mapping, and aggregation operations.
    '''

    def __init__(self, config_path: str = None):
        self.config = self._load_config(config_path)
        self.data_cache = {{}}
        self.processing_stats = {{
            'items_processed': 0,
            'errors_encountered': 0,
            'processing_time': 0.0
        }}

    def _load_config(self, path: str) -> dict:
        '''Load configuration from file or use defaults.'''
        if path and os.path.exists(path):
            with open(path, 'r') as f:
                return json.load(f)
        return {{
            'batch_size': 1000,
            'timeout': 30,
            'retry_count': 3,
            'enable_caching': True
        }}

    def process_batch(self, items: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        '''Process a batch of items with error handling.'''
        results = []
        start_time = time.time()

        for item in items:
            try:
                processed_item = self._process_single_item(item)
                if processed_item:
                    results.append(processed_item)
                    self.processing_stats['items_processed'] += 1
            except Exception as e:
                self.processing_stats['errors_encountered'] += 1
                logger.error(f"Error processing item {{item.get('id', 'unknown')}}: {{e}}")

        self.processing_stats['processing_time'] += time.time() - start_time
        return results

    def _process_single_item(self, item: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        '''Process a single item with validation and transformation.'''
        # Validation
        required_fields = ['id', 'type', 'data']
        for field in required_fields:
            if field not in item:
                raise ValueError(f"Missing required field: {{field}}")

        # Transformation
        processed = {{
            'id': item['id'],
            'type': item['type'].lower().strip(),
            'processed_data': self._transform_data(item['data']),
            'timestamp': datetime.now().isoformat(),
            'version': '1.0'
        }}

        return processed

    def _transform_data(self, data: Any) -> Any:
        '''Transform data based on type and configuration.'''
        if isinstance(data, str):
            return data.strip().lower()
        elif isinstance(data, (int, float)):
            return data * self.config.get('multiplier', 1.0)
        elif isinstance(data, list):
            return [self._transform_data(item) for item in data]
        elif isinstance(data, dict):
            return {{k: self._transform_data(v) for k, v in data.items()}}
        else:
            return str(data)

"""

    # Generate content by repeating patterns
    content_parts = []
    current_size = 0
    func_index = 0

    while current_size < target_size:
        func_content = base_pattern.format(index=index, func_index=func_index)
        content_parts.append(func_content)
        current_size += len(func_content)
        func_index += 1

    return "".join(content_parts)[:target_size]


# ============================================================================
# TIMING HELPER FUNCTIONS
# ============================================================================


def _calculate_timing_stats(
    times_ms: List[float], config: Dict[str, Any]
) -> Dict[str, Any]:
    """Calculate timing statistics from a list of times in milliseconds."""
    avg_time_ms = statistics.mean(times_ms)
    median_time_ms = statistics.median(times_ms)
    min_time_ms = min(times_ms)
    max_time_ms = max(times_ms)
    std_dev_ms = statistics.stdev(times_ms) if len(times_ms) > 1 else 0

    results = {
        "test_config": config,
        "timing_stats": {
            "avg_time_ms": avg_time_ms,
            "median_time_ms": median_time_ms,
            "min_time_ms": min_time_ms,
            "max_time_ms": max_time_ms,
            "std_dev_ms": std_dev_ms,
        },
        "raw_times_ms": times_ms,
    }

    print(f"\nTiming Results for {config.get('function', 'unknown')}:")
    print(f"  Average time: {avg_time_ms:.3f}ms")
    print(f"  Median time: {median_time_ms:.3f}ms")
    print(f"  Min time: {min_time_ms:.3f}ms")
    print(f"  Max time: {max_time_ms:.3f}ms")
    print(f"  Std deviation: {std_dev_ms:.3f}ms")

    return results


# ============================================================================
# INDIVIDUAL FUNCTION TIMING TESTS
# ============================================================================


def test_limit_viewed_content_performance(
    num_viewed_content: int = 10,
    content_size_kb: int = 5,
    max_chunk_size: int = 1024,
    content_strategy: str = "real_code",  # "random_text", "real_code", "mixed"
    num_runs: int = 100,
) -> Dict[str, Any]:
    """Test performance of limit_viewed_content_chunk_size function with different content strategies."""
    print(
        f"Testing limit_viewed_content_chunk_size with {num_viewed_content} items of {content_size_kb}KB each..."
    )
    print(f"  Content strategy: {content_strategy}")
    print(f"  Max chunk size: {max_chunk_size}")

    # Generate content based on strategy
    viewed_contents = []
    for i in range(num_viewed_content):
        if content_strategy == "random_text":
            content = generate_random_text_content(content_size_kb)
        elif content_strategy == "real_code":
            content = generate_real_code_content(content_size_kb, i)
        else:
            raise ValueError(f"Unknown content strategy: {content_strategy}")

        viewed_content = create_viewed_content(
            path=f"test/file_{i}.py",
            content=content,
            char_start=0,
            line_start=1,
            timestamp=datetime.now() - timedelta(milliseconds=i * 30000),
        )
        viewed_contents.append(viewed_content)

    print(f"  Created {len(viewed_contents)} viewed content items")
    print(
        f"  Average content size: {statistics.mean([len(vc.visible_content) for vc in viewed_contents]):.0f} chars"
    )

    # Run timing tests
    times_ms = []
    result_lengths = []

    for i in range(num_runs):
        start_time = time.perf_counter()
        result = limit_viewed_content_chunk_size(
            viewed_contents, max_chunk_size=max_chunk_size
        )
        end_time = time.perf_counter()

        times_ms.append((end_time - start_time) * 1000)  # Convert to milliseconds
        result_lengths.append(len(result))

        if (i + 1) % 10 == 0:
            print(f"  Completed {i + 1}/{num_runs} runs...")

    stats = _calculate_timing_stats(
        times_ms,
        {
            "num_viewed_content": num_viewed_content,
            "content_size_kb": content_size_kb,
            "max_chunk_size": max_chunk_size,
            "content_strategy": content_strategy,
            "num_runs": num_runs,
            "function": "limit_viewed_content_chunk_size",
        },
    )

    # Add result statistics
    stats["result_stats"] = {
        "avg_result_length": statistics.mean(result_lengths),
        "all_result_lengths": result_lengths,
    }

    print(
        f"  Average result length: {stats['result_stats']['avg_result_length']:.1f} chunks"
    )

    return stats


def test_deduplicate_viewed_content_performance(
    num_viewed_content: int = 10, num_replacements: int = 150, num_runs: int = 100
) -> Dict[str, Any]:
    """Test performance of deduplicate_viewed_content_against_replacements with worst-case scenario.

    Creates N viewed_content at various positions from the same file (non-overlapping).
    Creates M replacement texts where the first M-1 have no overlap and only the last one overlaps.
    This forces the algorithm to iterate through many replacements before finding the overlap.
    """
    print(
        "Testing deduplicate_viewed_content_against_replacements (worst-case scenario)..."
    )
    print(f"  {num_viewed_content} viewed content, {num_replacements} replacements")
    print("  Only the LAST replacement overlaps (worst case for iteration)")

    # Create viewed content from the same file at different positions (non-overlapping)
    viewed_contents = []
    file_path = "test.py"

    for i in range(num_viewed_content):
        content = f"""def function_{i}():
    '''Function {i} documentation.
    This function performs operation {i}.
    '''
    result = []
    for j in range({i + 10}):
        if j % 2 == 0:
            result.append(j * {i})
        else:
            result.append(j + {i})
    return result

"""

        viewed_content = create_viewed_content(
            path=file_path,
            content=content,
            char_start=1,
            line_start=1,
            timestamp=datetime.now() - timedelta(milliseconds=i * 30000),
        )
        viewed_contents.append(viewed_content)

    print(f"  Created {len(viewed_contents)} viewed content items from same file")

    # Create replacement texts where first M-1 have NO overlap, only the last one overlaps
    replacement_texts = []

    # First M-1 replacements: NO overlap (different files or different positions)
    for i in range(num_replacements - 1):
        replacement = create_replacement_text(
            path="test.py",
            replacement_text=f"no_overlap_replacement_{i} = {i}",
            char_start=9999,
            timestamp=datetime.now() - timedelta(milliseconds=i * 60000),
        )
        replacement_texts.append(replacement)

    # Last replacement: OVERLAPS with all viewed content (worst case)
    if viewed_contents:
        overlapping_viewed = viewed_contents[0]  # First viewed content
        overlap_start = overlapping_viewed.char_start + 5  # Overlap in middle

        overlapping_replacement = create_replacement_text(
            path=overlapping_viewed.path,  # Same file
            replacement_text="# This replacement overlaps!\\noverlappping_code = True\\n",
            char_start=overlap_start,
            timestamp=datetime.now() - timedelta(milliseconds=num_replacements * 60000),
        )
        replacement_texts.append(overlapping_replacement)

    print(f"  Created {len(replacement_texts)} replacement texts")
    print(f"  First {num_replacements - 1} replacements: NO overlap")
    print("  Last replacement: OVERLAPS (forces full iteration)")

    # Run timing tests
    times_ms = []
    result_lengths = []

    for i in range(num_runs):
        start_time = time.perf_counter()
        result = deduplicate_viewed_content_against_replacements(
            viewed_contents, replacement_texts
        )
        end_time = time.perf_counter()

        times_ms.append((end_time - start_time) * 1000)  # Convert to milliseconds
        result_lengths.append(len(result))

        if (i + 1) % 20 == 0:
            print(f"  Completed {i + 1}/{num_runs} runs...")

    stats = _calculate_timing_stats(
        times_ms,
        {
            "num_viewed_content": num_viewed_content,
            "num_replacements": num_replacements,
            "overlap_position": "last",  # Worst case
            "num_runs": num_runs,
            "function": "deduplicate_viewed_content_against_replacements",
        },
    )

    # Add result statistics
    stats["result_stats"] = {
        "avg_result_length": statistics.mean(result_lengths),
        "all_result_lengths": result_lengths,
        "avg_filtered_out": num_viewed_content - statistics.mean(result_lengths),
    }

    return stats
