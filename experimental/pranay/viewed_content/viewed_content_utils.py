from base.prompt_format.chunk_origin import Chunk<PERSON><PERSON><PERSON>
from base.tokenizers import tokenizer as prod_tokenizer
from experimental.pranay.utils.common_utils import generate_prompt, to_prompt_chunk
from research.data.rag.hindsight_common import (
    EnderDataPromptFormattingConfig,
    HindsightCompletionDatum,
)
from research.data.rag.hindsight_common_utils import (
    process_recency_chunks,
)


def process_datum(
    datum: HindsightCompletionDatum,
    prompt_name: str,
    config: EnderDataPromptFormattingConfig,
    tokenizer: prod_tokenizer.Tokenizer,
):
    data = datum.completion
    recency_info = data.request.recency_info
    assert data.request.position, "Position is required."

    recency_chunks = process_recency_chunks(32, data.request)
    # remove any chunks from recency retriever or viewed content
    retrieved_chunks = [
        chunk
        for chunk in data.response.retrieved_chunks
        if chunk.origin
        not in [
            ChunkOrigin.RECENCY_RETRIEVER.value,
            ChunkOrigin.RECENCY_RETRIEVER_VIEWED_CONTENT.value,
        ]
    ]
    prompt_chunks = list(map(to_prompt_chunk, retrieved_chunks))
    all_prompt_chunks = prompt_chunks + recency_chunks

    generated_prompt = generate_prompt(
        data.request.prefix,
        data.request.position.prefix_begin,
        data.request.suffix,
        data.request.path,
        all_prompt_chunks,
        recency_info,
        tokenizer,
        config,
    )

    return {
        "request_id": datum.completion.request_id,
        "ground_truth": datum.ground_truth,
        prompt_name: generated_prompt,
    }
