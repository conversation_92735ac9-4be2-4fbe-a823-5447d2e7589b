import { describe, it, expect } from 'vitest';
import { detectPRInfo } from './pr-detection';
import type { CleanRemoteAgent, CleanChatExchangeData } from '$lib/api/unified-client';
import type { UnifiedEntity } from '$lib/utils/entity-conversion';

describe('detectPRInfo', () => {
	const mockAgent: CleanRemoteAgent = {
		id: 'test-agent',
		remoteAgentId: 'remote-123',
		title: 'Test Agent',
		status: 'COMPLETED',
		sessionSummary: 'Test summary',
		githubUrl: 'https://github.com/test/repo',
		createdAt: '2024-01-01T00:00:00Z',
		updatedAt: '2024-01-01T00:00:00Z'
	};

	it('should return PR info from linked GitHub PR entity', () => {
		const linkedEntity: UnifiedEntity = {
			id: '123',
			entityType: 'GITHUB_PULL_REQUEST',
			title: 'Test PR',
			url: 'https://github.com/test/repo/pull/123',
			description: 'Test PR description',
			metadata: {
				state: 'open',
				author: { login: 'testuser' },
				created_at: '2024-01-01T00:00:00Z',
				updated_at: '2024-01-01T01:00:00Z',
				draft: false,
				mergeable: true,
				review_decision: 'approved'
			}
		};

		const result = detectPRInfo(mockAgent, null, linkedEntity);

		expect(result).toEqual({
			prNumber: 123,
			prUrl: 'https://github.com/test/repo/pull/123',
			repoUrl: 'https://github.com/test/repo',
			entity: linkedEntity,
			source: 'linked_entity',
			title: 'Test PR',
			state: 'open',
			description: 'Test PR description',
			author: 'testuser',
			createdAt: '2024-01-01T00:00:00Z',
			updatedAt: '2024-01-01T01:00:00Z',
			mergeable: true,
			draft: false,
			reviewDecision: 'approved'
		});
	});

	it('should return PR info from chat history when no linked entity', () => {
		const mockExchanges: CleanChatExchangeData[] = [
			{
				exchange: {
					responseNodes: [
						{
							type: 5,
							toolUse: {
								toolUseId: 'test-tool-use-id',
								toolName: 'github-api',
								inputJson: JSON.stringify({
									method: 'POST',
									path: '/repos/test/repo/pulls',
									data: {
										title: 'Test PR from chat',
										head: 'feature-branch',
										base: 'main'
									}
								})
							}
						}
					],
					requestNodes: [
						{
							type: 1,
							toolResultNode: {
								toolUseId: 'test-tool-use-id',
								content: `number: 456
url: https://api.github.com/repos/test/repo/pulls/456
state: open
title: Test PR from chat`
							}
						}
					]
				}
			}
		];

		const result = detectPRInfo(mockAgent, mockExchanges, null);

		expect(result).toEqual({
			prNumber: 456,
			prUrl: 'https://github.com/test/repo/pull/456',
			repoUrl: 'https://github.com/test/repo',
			source: 'chat_history'
		});
	});

	it('should prioritize linked entity over chat history', () => {
		const linkedEntity: UnifiedEntity = {
			id: '123',
			entityType: 'GITHUB_PULL_REQUEST',
			title: 'Linked PR',
			url: 'https://github.com/test/repo/pull/123'
		};

		const mockExchanges: CleanChatExchangeData[] = [
			{
				exchange: {
					responseNodes: [
						{
							type: 5,
							toolUse: {
								toolUseId: 'test-tool-use-id',
								toolName: 'github-api',
								inputJson: JSON.stringify({
									method: 'POST',
									path: '/repos/test/repo/pulls',
									data: { title: 'Chat PR' }
								})
							}
						}
					],
					requestNodes: [
						{
							type: 1,
							toolResultNode: {
								toolUseId: 'test-tool-use-id',
								content: 'number: 456'
							}
						}
					]
				}
			}
		];

		const result = detectPRInfo(mockAgent, mockExchanges, linkedEntity);

		expect(result?.source).toBe('linked_entity');
		expect(result?.prNumber).toBe(123);
		expect(result?.title).toBe('Linked PR');
	});

	it('should return null when no PR is found', () => {
		const result = detectPRInfo(mockAgent, null, null);
		expect(result).toBeNull();
	});

	it('should return null for non-PR linked entity', () => {
		const linkedEntity: UnifiedEntity = {
			id: '123',
			entityType: 'GITHUB_ISSUE',
			title: 'Test Issue',
			url: 'https://github.com/test/repo/issues/123'
		};

		const result = detectPRInfo(mockAgent, null, linkedEntity);
		expect(result).toBeNull();
	});
});
