/**
 * Utility for parsing structured responses from agents
 */

import type {
	ParsedStructuredResponse,
	StructuredResponseSection,
	StructuredSectionData,
	QuestionField,
	QuestionResponseData,
	BulletListItem,
	NotebookNote,
	NoteCategory
} from '$lib/types/structured-response';

/**
 * Extract notebook notes from XML note tags in the response text
 */
function extractNotebookNotes(responseText: string): NotebookNote[] {
	const notes: NotebookNote[] = [];

	// Regex to match <note category="...">content</note> tags
	const noteRegex = /<note\s+category="([^"]+)">([^<]+)<\/note>/gi;
	let match;

	while ((match = noteRegex.exec(responseText)) !== null) {
		const category = match[1] as NoteCategory;
		const content = match[2].trim();

		// Validate category
		const validCategories: NoteCategory[] = [
			'code_architecture',
			'implementation_details',
			'user_preferences',
			'technical_constraints',
			'business_logic',
			'integration_points'
		];

		if (validCategories.includes(category) && content) {
			notes.push({
				content,
				category
			});
		}
	}

	return notes;
}

/**
 * Extract the ending summary from a section's content
 * Looks for the last sentence that could serve as a summary
 */
function extractEndingSummary(content: string): string | undefined {
	// if the text is short, just return the entire content as the summary
	if (content.length < 250) return content;

	const lastLine = content.split('\n').pop()?.trim() || '';
	if (lastLine.length < 200) return lastLine;

	// Split content into sentences, but avoid splitting on periods in file extensions, URLs, or code
	// Use a simpler regex that looks for sentence-ending punctuation followed by whitespace and capital letter
	// but not when preceded by common abbreviations or single letters (like "Let's")
	const sentences = content
		.split(/(?<!(?:etc|vs|e\.g|i\.e|Mr|Mrs|Dr|Prof|Inc|Ltd|Co)\s*)[.!?]+(?=\s+[A-Z])/)
		.map((s) => s.trim())
		.filter((s) => s.length > 0);

	if (sentences.length === 0) return undefined;

	return sentences.pop();
}

/**
 * Convert raw section content to StructuredSectionData
 */
function createSectionData(content: string): StructuredSectionData {
	return {
		content: content,
		rawText: content,
		endingSummary: extractEndingSummary(content)
	};
}

/**
 * Parse bullet-formatted content into simple string array (for next steps)
 * Handles formats like:
 * - Item 1
 * * Item 2
 * • Item 3
 * Also handles multi-line bullet points and quoted strings for backward compatibility
 */
function parseSimpleBulletList(contentArray: string[]): string[] {
	const allItems: string[] = [];

	for (const content of contentArray) {
		// Check if content has bullet points - if so, parse as bullets, not quotes
		const hasBulletPoints = /^[-*•]\s+/m.test(content) || /^\d+\.\s+/m.test(content);

		if (!hasBulletPoints) {
			// Only try quoted string extraction if there are no bullet points
			const quotedMatches = content.match(/"([^"]+)"/g);

			if (quotedMatches && quotedMatches.length > 0) {
				// Extract content from quotes and clean up
				const quotedItems = quotedMatches
					.map(
						(match) => match.slice(1, -1).trim() // Remove quotes and trim
					)
					.filter((item) => item.length > 0);

				allItems.push(...quotedItems);
				continue; // Skip bullet parsing for this content
			}
		}

		// Parse bullet-formatted content, handling multi-line items
		const lines = content.split('\n');
		let currentItem = '';
		let inBulletItem = false;

		for (let i = 0; i < lines.length; i++) {
			const line = lines[i];
			const trimmedLine = line.trim();

			if (!trimmedLine) {
				// Empty line - if we're in a bullet item, end it
				if (inBulletItem && currentItem.trim()) {
					allItems.push(currentItem.trim());
					currentItem = '';
					inBulletItem = false;
				}
				continue;
			}

			// Check if this line starts a new bullet point (ignore indentation for simple parsing)
			const bulletMatch =
				trimmedLine.match(/^[-*•]\s+(.+)$/) || trimmedLine.match(/^\d+\.\s+(.+)$/);

			if (bulletMatch) {
				// If we were already in a bullet item, save it first
				if (inBulletItem && currentItem.trim()) {
					allItems.push(currentItem.trim());
				}

				// Start new bullet item
				currentItem = bulletMatch[1];
				inBulletItem = true;
			} else if (inBulletItem) {
				// This line is a continuation of the current bullet item
				currentItem += ' ' + trimmedLine;
			} else {
				// Line without bullet marker and not in a bullet item
				// Treat as a standalone item (for backward compatibility)
				if (trimmedLine) {
					allItems.push(trimmedLine);
				}
			}
		}

		// Don't forget the last item if we were in one
		if (inBulletItem && currentItem.trim()) {
			allItems.push(currentItem.trim());
		}
	}

	return allItems;
}

/**
 * Parse bullet-formatted content into individual items with nesting levels
 * Handles formats like:
 * - Item 1
 * * Item 2
 * • Item 3
 *     - Nested item
 * Also handles multi-line bullet points and quoted strings for backward compatibility
 */
function parseBulletList(contentArray: string[]): Array<{ text: string; level: number }> {
	const allItems: Array<{ text: string; level: number }> = [];

	for (const content of contentArray) {
		// Check if content has bullet points - if so, parse as bullets, not quotes
		const hasBulletPoints = /^[-*•]\s+/m.test(content) || /^\d+\.\s+/m.test(content);

		if (!hasBulletPoints) {
			// Only try quoted string extraction if there are no bullet points
			const quotedMatches = content.match(/"([^"]+)"/g);

			if (quotedMatches && quotedMatches.length > 0) {
				// Extract content from quotes and clean up
				const quotedItems = quotedMatches
					.map(
						(match) => match.slice(1, -1).trim() // Remove quotes and trim
					)
					.filter((item) => item.length > 0);

				// Add quoted items with level 0
				quotedItems.forEach((item) => {
					allItems.push({ text: item, level: 0 });
				});
				continue; // Skip bullet parsing for this content
			}
		}

		// Parse bullet-formatted content, handling multi-line items and nesting
		const lines = content.split('\n');
		let currentItem = '';
		let currentLevel = 0;
		let inBulletItem = false;

		for (let i = 0; i < lines.length; i++) {
			const line = lines[i];
			const trimmedLine = line.trim();

			if (!trimmedLine) {
				// Empty line - if we're in a bullet item, end it
				if (inBulletItem && currentItem.trim()) {
					allItems.push({ text: currentItem.trim(), level: currentLevel });
					currentItem = '';
					inBulletItem = false;
				}
				continue;
			}

			// Count leading spaces/tabs to determine nesting level
			const match = line.match(/^(\s*)/);
			const indentLevel = match ? Math.floor(match[1].length / 4) : 0; // 4 spaces = 1 level

			// Check if this line starts a new bullet point
			const bulletMatch =
				trimmedLine.match(/^[-*•]\s+(.+)$/) || trimmedLine.match(/^\d+\.\s+(.+)$/);

			if (bulletMatch) {
				// If we were already in a bullet item, save it first
				if (inBulletItem && currentItem.trim()) {
					allItems.push({ text: currentItem.trim(), level: currentLevel });
				}

				// Start new bullet item
				currentItem = bulletMatch[1];
				currentLevel = indentLevel;
				inBulletItem = true;
			} else if (inBulletItem) {
				// This line is a continuation of the current bullet item
				currentItem += ' ' + trimmedLine;
			} else {
				// Line without bullet marker and not in a bullet item
				// Treat as a standalone item (for backward compatibility)
				if (trimmedLine) {
					allItems.push({ text: trimmedLine, level: indentLevel });
				}
			}
		}

		// Don't forget the last item if we were in one
		if (inBulletItem && currentItem.trim()) {
			allItems.push({ text: currentItem.trim(), level: currentLevel });
		}
	}

	return allItems;
}

/**
 * Parse questions from content that contains JSON question definitions
 * Each line should be a valid JSON object representing a question
 */
function parseQuestions(contentArray: string[]): QuestionField[] {
	const questions: QuestionField[] = [];

	for (const content of contentArray) {
		// Split by lines and try to parse each as JSON
		const lines = content
			.split('\n')
			.map((line) => line.trim())
			.filter((line) => line.length > 0);

		for (const line of lines) {
			try {
				const questionData = JSON.parse(line);

				// Validate required fields
				if (questionData.id && questionData.label && questionData.type) {
					// Validate type
					const validTypes = ['text', 'textarea', 'select', 'boolean', 'number'];
					if (validTypes.includes(questionData.type)) {
						questions.push({
							id: questionData.id,
							label: questionData.label,
							type: questionData.type,
							required: questionData.required || false,
							placeholder: questionData.placeholder,
							options: questionData.options,
							defaultValue: questionData.defaultValue
						});
					}
				}
			} catch (error) {
				// Skip invalid JSON lines
				console.warn('Failed to parse question JSON:', line, error);
			}
		}
	}

	return questions;
}

/**
 * Parse question responses from content
 * Expects format like:
 * **Question Label:** Response Value
 */
function parseQuestionResponses(contentArray: string[]): QuestionResponseData[] {
	const responses: QuestionResponseData[] = [];

	for (const content of contentArray) {
		// Split by lines and parse each response
		const lines = content
			.split('\n')
			.map((line) => line.trim())
			.filter((line) => line.length > 0);

		for (const line of lines) {
			// Match format: **Question:** Response
			const match = line.match(/^\*\*([^*]+):\*\*\s*(.+)$/);
			if (match) {
				const [, question, response] = match;
				responses.push({
					question: question.trim(),
					response: response.trim()
				});
			}
		}
	}

	return responses;
}

/**
 * Extract structured sections from text using header format like =thinking=
 * Ensures all content is captured in a section, defaulting to "thinking" if no header is present
 */
function extractStructuredSections(text: string): {
	sections: Record<string, StructuredSectionData[]>;
	sectionOrder: Array<{ type: string; index: number }>;
} {
	const sections: Record<string, StructuredSectionData[]> = {};
	const sectionOrder: Array<{ type: string; index: number }> = [];

	// Split text by section headers while preserving the headers
	// Only match valid section types, avoiding file extensions and other patterns
	const headerRegex =
		/^=(thinking|plan|researching|doing|noting|summary|user_responses|questions|question_responses|user_request|error|status|warning)=\s*$/gm;
	const parts: Array<{ type: string | null; content: string }> = [];

	let lastIndex = 0;
	let currentType: string | null = null;
	let match;

	// Find all headers and split content accordingly
	while ((match = headerRegex.exec(text)) !== null) {
		// Add content before this header (if any)
		if (match.index > lastIndex) {
			const contentBeforeHeader = text.slice(lastIndex, match.index).trim();
			if (contentBeforeHeader) {
				parts.push({
					type: currentType,
					content: contentBeforeHeader
				});
			}
		}

		// Update current type for subsequent content
		currentType = match[1].toLowerCase();
		lastIndex = match.index + match[0].length;
	}

	// Add remaining content after the last header
	if (lastIndex < text.length) {
		const remainingContent = text.slice(lastIndex).trim();
		if (remainingContent) {
			parts.push({
				type: currentType,
				content: remainingContent
			});
		}
	}

	// If no headers were found, treat entire text as thinking
	if (parts.length === 0 && text.trim()) {
		parts.push({
			type: null,
			content: text.trim()
		});
	}

	// Process parts and assign to sections, tracking order
	const seenTypes = new Set<string>();
	parts.forEach((part, index) => {
		if (part.content) {
			// Default to "thinking" if no type is specified
			const sectionType = part.type || 'thinking';

			if (!sections[sectionType]) {
				sections[sectionType] = [];
			}
			sections[sectionType].push(createSectionData(part.content));

			// Track the first occurrence of each section type for ordering
			if (!seenTypes.has(sectionType)) {
				seenTypes.add(sectionType);
				sectionOrder.push({ type: sectionType, index });
			}
		}
	});

	return { sections, sectionOrder };
}

/**
 * Parse a structured response from agent text using header format
 */
export function parseStructuredResponse(responseText: string): ParsedStructuredResponse {
	const result: ParsedStructuredResponse = {
		isComplete: true,
		rawResponse: responseText
	};

	// Extract structured sections from the response
	const { sections, sectionOrder } = extractStructuredSections(responseText);

	// Extract notebook notes from XML tags
	const notebookNotes = extractNotebookNotes(responseText);
	if (notebookNotes.length > 0) {
		result.notebook_notes = notebookNotes;
	}

	// If no content at all, mark as incomplete
	if (Object.keys(sections).length === 0 || !responseText.trim()) {
		return {
			...result,
			isComplete: false
		};
	}

	// Add section order to result
	result.sectionOrder = sectionOrder;

	// Map sections to result properties
	if (sections.thinking) {
		result.thinking = sections.thinking;
	}

	if (sections.plan) {
		result.plan = parseBulletList(sections.plan.map((section) => section.content));
	}

	if (sections.researching) {
		result.researching = sections.researching;
	}

	if (sections.doing) {
		result.doing = sections.doing;
	}

	if (sections.noting) {
		result.noting = parseBulletList(sections.noting.map((section) => section.content));
	}

	if (sections.summary && sections.summary.length > 0) {
		result.summary = sections.summary[0].content; // Take the first summary content
	}

	if (sections.user_responses) {
		result.user_responses = parseSimpleBulletList(
			sections.user_responses.map((section) => section.content)
		);
	}

	if (sections.questions) {
		result.questions = parseQuestions(sections.questions.map((section) => section.content));
	}

	if (sections.question_responses) {
		result.question_responses = parseQuestionResponses(
			sections.question_responses.map((section) => section.content)
		);
	}

	if (sections.user_request && sections.user_request.length > 0) {
		result.user_request = sections.user_request[0].content; // Take the first user_request content
	}

	if (sections.error && sections.error.length > 0) {
		result.error = sections.error[0].content; // Take the first error content
	}

	if (sections.status && sections.status.length > 0) {
		result.status = sections.status[0].content.trim(); // Take the first status content
	}

	if (sections.warning && sections.warning.length > 0) {
		result.warning = sections.warning[0].content.trim(); // Take the first warning content
	}

	// Check if the response seems complete
	const hasAnyStructuredContent = !!(
		result.thinking?.length ||
		result.plan?.length ||
		result.researching?.length ||
		result.doing?.length ||
		result.noting?.length ||
		result.notebook_notes?.length ||
		result.summary ||
		result.user_responses?.length ||
		result.questions?.length ||
		result.error
	);

	result.isComplete = hasAnyStructuredContent;

	return result;
}

/**
 * Convert parsed structured response to sections array for UI rendering
 * Preserves the original order of sections as they appeared in the response
 */
export function structuredResponseToSections(
	parsed: ParsedStructuredResponse | undefined | null
): StructuredResponseSection[] {
	const sections: StructuredResponseSection[] = [];

	// Return empty array if parsed is null or undefined
	if (!parsed) {
		return sections;
	}

	// Helper function to add sections for a given type
	const addSectionsForType = (type: string) => {
		switch (type) {
			case 'thinking':
				if (parsed.thinking?.length) {
					parsed.thinking.forEach((sectionData) => {
						sections.push({
							type: 'thinking',
							content: sectionData.content,
							timestamp: new Date().toISOString()
						});
					});
				}
				break;

			case 'plan':
				if (parsed.plan?.length) {
					console.log(parsed.plan);
					const planContent = parsed.plan
						.map((item) => {
							const indent = '    '.repeat(item.level);
							return `${indent}- ${item.text}`;
						})
						.join('\n');
					sections.push({
						type: 'plan',
						content: planContent,
						timestamp: new Date().toISOString()
					});
				}
				break;

			case 'researching':
				if (parsed.researching?.length) {
					parsed.researching.forEach((sectionData) => {
						sections.push({
							type: 'researching',
							content: sectionData.content,
							timestamp: new Date().toISOString()
						});
					});
				}
				break;

			case 'doing':
				if (parsed.doing?.length) {
					parsed.doing.forEach((sectionData) => {
						sections.push({
							type: 'doing',
							content: sectionData.content,
							timestamp: new Date().toISOString()
						});
					});
				}
				break;

			case 'noting':
				if (parsed.noting?.length) {
					const notingContent = parsed.noting
						.map((item) => {
							const indent = '    '.repeat(item.level);
							return `${indent}- ${item.text}`;
						})
						.join('\n');
					sections.push({
						type: 'noting',
						content: notingContent,
						timestamp: new Date().toISOString()
					});
				}
				break;

			case 'summary':
				if (parsed.summary) {
					sections.push({
						type: 'summary',
						content: parsed.summary,
						timestamp: new Date().toISOString()
					});
				}
				break;

			case 'user_responses':
				if (parsed.user_responses?.length) {
					const userResponsesContent = parsed.user_responses.map((item) => `- ${item}`).join('\n');
					sections.push({
						type: 'user_responses',
						content: userResponsesContent,
						timestamp: new Date().toISOString()
					});
				}
				break;

			case 'question_responses':
				if (parsed.question_responses?.length) {
					const content = parsed.question_responses
						.map(({ question, response }) => `**${question}:** ${response}`)
						.join('\n');
					sections.push({
						type: 'question_responses',
						content,
						timestamp: new Date().toISOString()
					});
				}
				break;

			case 'user_request':
				if (parsed.user_request) {
					sections.push({
						type: 'user_request',
						content: parsed.user_request,
						timestamp: new Date().toISOString()
					});
				}
				break;

			case 'error':
				if (parsed.error) {
					sections.push({
						type: 'error',
						content: parsed.error,
						timestamp: new Date().toISOString()
					});
				}
				break;

			case 'status':
				if (parsed.status) {
					sections.push({
						type: 'status',
						content: parsed.status,
						timestamp: new Date().toISOString()
					});
				}
				break;

			case 'warning':
				if (parsed.warning) {
					sections.push({
						type: 'warning',
						content: parsed.warning,
						timestamp: new Date().toISOString()
					});
				}
				break;
		}
	};

	// Use section order if available, otherwise fall back to default order
	if (parsed.sectionOrder?.length) {
		// Sort by original index to maintain order
		const sortedOrder = [...parsed.sectionOrder].sort((a, b) => a.index - b.index);
		sortedOrder.forEach(({ type }) => {
			addSectionsForType(type);
		});
	} else {
		// Fallback to original fixed order for backward compatibility
		const defaultOrder = [
			'thinking',
			'plan',
			'researching',
			'doing',
			'noting',
			'warning',
			'summary',
			'status',
			'user_responses',
			'question_responses',
			'user_request',
			'error'
		];
		defaultOrder.forEach((type) => {
			addSectionsForType(type);
		});
	}

	return sections;
}

/**
 * Check if a response text contains structured content
 * Now returns true for any non-empty text since we ensure all content is structured
 */
export function hasStructuredContent(responseText: string): boolean {
	return responseText.trim().length > 0;
}

/**
 * Get structured section data with raw text and ending summaries
 */
export function getStructuredSectionData(
	parsed: ParsedStructuredResponse,
	sectionType: 'thinking' | 'researching' | 'doing' | 'noting'
): StructuredSectionData[] | BulletListItem[] {
	if (sectionType === 'noting') {
		return parsed[sectionType] || [];
	}
	return parsed[sectionType] || [];
}

/**
 * Get all ending summaries from a parsed structured response
 */
export function getAllEndingSummaries(parsed: ParsedStructuredResponse): Record<string, string[]> {
	const summaries: Record<string, string[]> = {};

	const sectionTypes: Array<'thinking' | 'researching' | 'doing'> = [
		'thinking',
		'researching',
		'doing'
	];

	sectionTypes.forEach((type) => {
		const sections = parsed[type];
		if (sections?.length) {
			summaries[type] = sections
				.map((section) => section.endingSummary)
				.filter((summary): summary is string => !!summary);
		}
	});

	return summaries;
}

/**
 * Extract a concise 2-sentence summary from a parsed structured response
 * Prioritizes summary section, then ending summaries from other sections
 */
export function extractTwoSentenceSummary(parsed: ParsedStructuredResponse): string {
	// First, try the explicit summary section
	if (parsed.summary) {
		return limitToTwoSentences(parsed.summary);
	}

	// Collect potential summary sources in priority order
	const potentialSummaries: string[] = [];

	// Add ending summaries from sections in priority order
	const priorityOrder: Array<'doing' | 'researching' | 'thinking'> = [
		'doing',
		'researching',
		'thinking'
	];

	priorityOrder.forEach((sectionType) => {
		const sections = parsed[sectionType];
		if (sections?.length) {
			// Get the last section's ending summary (most recent activity)
			const lastSection = sections[sections.length - 1];
			if (lastSection.endingSummary) {
				potentialSummaries.push(lastSection.endingSummary);
			}
		}
	});

	// If we have potential summaries, use the first (highest priority) one
	if (potentialSummaries.length > 0) {
		return limitToTwoSentences(potentialSummaries[0]);
	}

	// Fallback: try to extract from the first substantial content
	const fallbackSources = [
		parsed.doing?.[0]?.content,
		parsed.researching?.[0]?.content,
		parsed.thinking?.[0]?.content
	].filter(Boolean);

	if (fallbackSources.length > 0) {
		return limitToTwoSentences(fallbackSources[0]!);
	}

	// Final fallback
	return 'Working on the task.';
}

/**
 * Limit text to exactly 2 sentences, handling various sentence endings
 */
function limitToTwoSentences(text: string): string {
	if (!text || text.trim().length === 0) {
		return 'Working on the task.';
	}

	// Clean up the text
	const cleanText = text.trim();

	// If text is very short (less than 100 chars), likely already concise
	if (cleanText.length < 100) {
		return cleanText;
	}

	// Split into sentences, being careful about abbreviations and code
	const sentences = cleanText
		.split(
			/(?<!(?:etc|vs|e\.g|i\.e|Mr|Mrs|Dr|Prof|Inc|Ltd|Co|\.js|\.ts|\.py|\.java|\.cpp)\s*)[.!?]+(?=\s+[A-Z])/
		)
		.map((s) => s.trim())
		.filter((s) => s.length > 0);

	if (sentences.length === 0) {
		return 'Working on the task.';
	}

	if (sentences.length === 1) {
		// If only one sentence, check if it's too long and try to split differently
		const singleSentence = sentences[0];
		if (singleSentence.length > 200) {
			// Try to find a natural break point
			const commaBreak = singleSentence.indexOf(', ');
			if (commaBreak > 50 && commaBreak < 150) {
				return (
					singleSentence.substring(0, commaBreak + 1).trim() +
					' ' +
					singleSentence.substring(commaBreak + 2).trim()
				);
			}
			// Truncate if too long
			return singleSentence.substring(0, 197) + '...';
		}
		return singleSentence;
	}

	// Take first two sentences
	const twoSentences = sentences.slice(0, 2).join('. ');

	// Ensure it ends with proper punctuation
	if (!twoSentences.match(/[.!?]$/)) {
		return twoSentences + '.';
	}

	return twoSentences;
}
