import type { ChangedFile, CleanChangedFile } from '$lib/api/unified-client';

/** Enum for File Change Type */
export enum FileChangeType {
	added = 0,
	deleted = 1,
	modified = 2,
	renamed = 3
}

/**
 * Calculates net changes across all file operations, filtering out add+delete cycles.
 * This is used for aggregate conversation views to show only files with net changes.
 */
const calculateNetFileChanges = (changedFiles: ChangedFile[]): CleanChangedFile[] => {
	// Track the evolution of each file through all changes
	const fileEvolution: Record<
		string,
		{
			originalExists: boolean;
			originalContent: string;
			finalExists: boolean;
			finalContent: string;
			finalPath: string;
			latestChange: ChangedFile;
		}
	> = {};

	for (const file of changedFiles) {
		// Skip invalid entries (both path and oldPath are empty)
		if (!file.path && !file.oldPath) {
			continue;
		}

		// Determine which file path this change affects
		// In the new structure: path = current/new path, oldPath = previous path
		const currentPath = file.path || '';
		const previousPath = file.oldPath || '';

		// Use the most specific path available to track this file
		// Priority: oldPath (for tracking file identity) > currentPath (for new files)
		const trackingPath = previousPath || currentPath;

		if (!fileEvolution[trackingPath]) {
			// First time seeing this file - establish original state
			// File originally existed if oldPath is not empty
			const originalExists = previousPath !== '';
			fileEvolution[trackingPath] = {
				originalExists,
				originalContent: originalExists ? file.oldContent || '' : '',
				finalExists: currentPath !== '',
				finalContent: currentPath !== '' ? file.content || '' : '',
				finalPath: currentPath, // Don't fall back to previousPath for deletions
				latestChange: file
			};
		} else {
			// Update the final state based on this change
			fileEvolution[trackingPath].finalExists = currentPath !== '';
			fileEvolution[trackingPath].finalContent = currentPath !== '' ? file.content || '' : '';
			fileEvolution[trackingPath].finalPath = currentPath; // Don't fall back to previousPath for deletions
			fileEvolution[trackingPath].latestChange = file;

			// Note: We intentionally do NOT update originalContent here
			// The originalContent should remain the very first content we saw for this file
		}
	}

	// Filter to only include files with net changes or special patterns
	const netChanges: CleanChangedFile[] = [];

	for (const [trackingPath, evolution] of Object.entries(fileEvolution)) {
		// Check for different types of changes
		const existenceChanged = evolution.originalExists !== evolution.finalExists;
		const contentChanged =
			evolution.originalExists &&
			evolution.finalExists &&
			evolution.originalContent !== evolution.finalContent;

		// Special case: file was added then deleted (temporary file)
		const wasAddedThenDeleted =
			!evolution.originalExists &&
			!evolution.finalExists &&
			(evolution.finalContent !== '' || evolution.originalContent !== '');

		const hasNetChange = existenceChanged || contentChanged || wasAddedThenDeleted;

		if (hasNetChange) {
			// Determine the change type based on the new data structure
			let changeType: 'added' | 'deleted' | 'modified' | 'renamed' | 'added_then_deleted';

			if (wasAddedThenDeleted) {
				changeType = 'added_then_deleted';
			} else if (!evolution.originalExists && evolution.finalExists) {
				changeType = 'added';
			} else if (evolution.originalExists && !evolution.finalExists) {
				changeType = 'deleted';
			} else if (trackingPath !== evolution.finalPath) {
				changeType = 'renamed';
			} else {
				changeType = 'modified';
			}

			// Construct a proper net change representation
			const netChange: CleanChangedFile = {
				path: evolution.finalPath || (changeType === 'deleted' ? '' : trackingPath),
				oldPath: evolution.originalExists ? trackingPath : undefined,
				oldContent: evolution.originalContent || undefined,
				content: evolution.finalContent || undefined,
				changeType: changeType
			};
			netChanges.push(netChange);
		}
	}

	return netChanges;
};

/**
 * Aggregates changed files from multiple exchanges into a single coherent view.
 * Similar to snapshot-based comparison, this function tracks file states across
 * multiple exchanges and generates aggregate change information.
 *
 * @param exchangeChanges Array of CleanChangedFile arrays, each representing changes from one exchange
 * @returns Array of aggregated CleanChangedFile objects representing the overall changes
 */
export function aggregateChangedFiles(exchangeChanges: CleanChangedFile[][]): CleanChangedFile[] {
	return calculateNetFileChanges(exchangeChanges.flat());
}

/**
 * Helper function to get a simple file path for display
 */
export function getAggregatedFilePath(file: CleanChangedFile): string {
	return file.path || file.oldPath || '';
}

/**
 * Helper function to determine if aggregated changes represent meaningful code changes
 */
export function hasAggregatedCodeChanges(aggregatedFiles: CleanChangedFile[]): boolean {
	if (!aggregatedFiles || aggregatedFiles.length === 0) {
		return false;
	}

	// Filter out non-meaningful changes
	const meaningfulChanges = aggregatedFiles.filter((file) => {
		const filePath = getAggregatedFilePath(file);

		// Skip empty files or files without content changes
		if (!file.content && !file.oldContent) {
			return false;
		}

		// Skip certain file types that might not be meaningful code changes
		const skipPatterns = [
			/\.log$/,
			/\.tmp$/,
			/\.cache$/,
			/node_modules\//,
			/\.git\//,
			/package-lock\.json$/,
			/yarn\.lock$/
		];

		if (skipPatterns.some((pattern) => pattern.test(filePath))) {
			return false;
		}

		return true;
	});

	return meaningfulChanges.length > 0;
}
