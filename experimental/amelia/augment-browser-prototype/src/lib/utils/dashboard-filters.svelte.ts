import { debounce } from '$lib/utils/timing';
import {
	searchUnifiedEntities,
	filterUnifiedEntitiesByProvider,
	filterUnifiedEntitiesByType,
	sortUnifiedEntities,
	type UnifiedEntity
} from '$lib/utils/entity-conversion';
import {
	type ConditionFilters,
	createEmptyConditionFilters,
	buildConditionsFromFilters,
	populateConditionFilters,
	generateConditionPills
} from '$lib/utils/condition-filters';
import { providerEntityTypes } from '$lib/utils/entity-types';

export interface DashboardFilters {
	searchQuery: string;
	selectedProvider: string;
	selectedEntityType: string;
	selectedConditions: Record<string, any>;
	showLinkedOnly: boolean;
	showUnlinkedOnly: boolean;
	conditionFilters: ConditionFilters;
}

export function createEmptyDashboardFilters(): DashboardFilters {
	return {
		searchQuery: '',
		selectedProvider: 'all',
		selectedEntityType: 'all',
		selectedConditions: {},
		showLinkedOnly: false,
		showUnlinkedOnly: false,
		conditionFilters: createEmptyConditionFilters()
	};
}

export function generateFilterKey(filters: DashboardFilters): string {
	return JSON.stringify({
		search: filters.searchQuery.trim(),
		provider: filters.selectedProvider,
		entityType: filters.selectedEntityType,
		conditions: filters.selectedConditions,
		conditionFilters: filters.conditionFilters,
		linkedOnly: filters.showLinkedOnly,
		unlinkedOnly: filters.showUnlinkedOnly
	});
}

export function applyFilters(
	entities: UnifiedEntity[],
	filters: DashboardFilters
): UnifiedEntity[] {
	let result = entities;

	// Apply search filter
	if (filters.searchQuery.trim()) {
		result = searchUnifiedEntities(result, filters.searchQuery.trim());
	}

	// Apply provider filter
	if (filters.selectedProvider !== 'all') {
		result = filterUnifiedEntitiesByProvider(result, filters.selectedProvider);
	}

	// Apply entity type filter
	if (filters.selectedEntityType !== 'all') {
		result = filterUnifiedEntitiesByType(result, filters.selectedEntityType);
	}

	// Apply condition filters
	if (Object.keys(filters.selectedConditions).length > 0) {
		console.log('Applying condition filters:', filters.selectedConditions);
		const beforeCount = result.length;
		// result = result.filter((entity) => applyConditionFilters(entity, filters.selectedConditions));
		console.log(`Condition filtering: ${beforeCount} -> ${result.length} entities`);
	}

	// Apply linked/unlinked filters
	if (filters.showLinkedOnly) {
		result = result.filter((entity) => entity.metadata.hasWorkflow);
	}
	if (filters.showUnlinkedOnly) {
		result = result.filter((entity) => !entity.metadata.hasWorkflow);
	}

	// Sort by updated date (most recent first)
	return sortUnifiedEntities(result, 'updatedAt', 'desc');
}

/**
 * Check if current dashboard filters match a trigger's conditions
 */
function matchesTriggerConditions(filters: DashboardFilters, trigger: any): boolean {
	// If no filters are applied (all are default/empty), don't select any trigger
	if (
		filters.selectedProvider === 'all' &&
		filters.selectedEntityType === 'all' &&
		Object.keys(filters.selectedConditions).length === 0
	) {
		return false;
	}

	// Extract provider and entity type from trigger
	const triggerConditions =
		trigger.configuration?.conditions || trigger.originalTrigger?.configuration?.conditions;
	if (!triggerConditions) return false;

	// Get provider and entity type from trigger conditions
	let triggerProvider = '';
	let triggerEntityType = '';

	if (triggerConditions.github) {
		triggerProvider = 'github';
		if (triggerConditions.github.pull_request) {
			triggerEntityType = 'pull_request';
		} else if (triggerConditions.github.workflow_run) {
			triggerEntityType = 'workflow_run';
		}
	} else if (triggerConditions.linear) {
		triggerProvider = 'linear';
		if (triggerConditions.linear.issue) {
			triggerEntityType = 'issue';
		}
	}

	// Check if provider and entity type match
	if (
		filters.selectedProvider !== triggerProvider ||
		filters.selectedEntityType !== triggerEntityType
	) {
		return false;
	}

	// Check if condition filters match
	// This is a simplified comparison - you might want to make it more sophisticated
	const currentConditions = filters.selectedConditions;

	// For GitHub pull requests
	if (triggerProvider === 'github' && triggerEntityType === 'pull_request') {
		const triggerPR = triggerConditions.github.pull_request;
		const currentPR = currentConditions.github?.pull_request;

		if (!currentPR && Object.keys(triggerPR || {}).length > 0) return false;
		if (currentPR) {
			if (triggerPR.author && triggerPR.author !== currentPR.author) return false;
			if (triggerPR.assignee && triggerPR.assignee !== currentPR.assignee) return false;
			if (triggerPR.reviewer && triggerPR.reviewer !== currentPR.reviewer) return false;
			if (triggerPR.base_branch && triggerPR.base_branch !== currentPR.base_branch) return false;
			if (triggerPR.head_branch && triggerPR.head_branch !== currentPR.head_branch) return false;
			if (triggerPR.repository && triggerPR.repository !== currentPR.repository) return false;
		}
	}

	// For GitHub workflow runs
	if (triggerProvider === 'github' && triggerEntityType === 'workflow_run') {
		const triggerWR = triggerConditions.github.workflow_run;
		const currentWR = currentConditions.github?.workflow_run;

		if (!currentWR && Object.keys(triggerWR || {}).length > 0) return false;
		if (currentWR) {
			if (triggerWR.actor && triggerWR.actor !== currentWR.actor) return false;
			if (triggerWR.event && triggerWR.event !== currentWR.event) return false;
			if (triggerWR.status && triggerWR.status !== currentWR.status) return false;
			if (triggerWR.conclusion && triggerWR.conclusion !== currentWR.conclusion) return false;
			if (triggerWR.repository && triggerWR.repository !== currentWR.repository) return false;
		}
	}

	// For Linear issues
	if (triggerProvider === 'linear' && triggerEntityType === 'issue') {
		const triggerIssue = triggerConditions.linear.issue;
		const currentIssue = currentConditions.linear?.issue;

		if (!currentIssue && Object.keys(triggerIssue || {}).length > 0) return false;
		if (currentIssue) {
			if (triggerIssue.team && triggerIssue.team !== currentIssue.team) return false;
			if (triggerIssue.creator && triggerIssue.creator !== currentIssue.creator) return false;
			if (triggerIssue.assignee && triggerIssue.assignee !== currentIssue.assignee) return false;
			if (triggerIssue.project && triggerIssue.project !== currentIssue.project) return false;
			if (
				triggerIssue.title_contains &&
				triggerIssue.title_contains !== currentIssue.title_contains
			)
				return false;
		}
	}

	return true;
}

export function createFilterManager(
	onFiltersChange: (filters: DashboardFilters) => void,
	onFetchMore: () => void
) {
	let currentFilters = $state(createEmptyDashboardFilters());
	let lastFilterKey = '';

	// Debounced update function
	const debouncedUpdate = debounce(() => {
		// Build conditions from condition filters
		const conditions = buildConditionsFromFilters(
			currentFilters.conditionFilters,
			currentFilters.selectedProvider,
			currentFilters.selectedEntityType
		);

		console.log('Built conditions from filters:', {
			conditionFilters: currentFilters.conditionFilters,
			provider: currentFilters.selectedProvider,
			entityType: currentFilters.selectedEntityType,
			builtConditions: conditions
		});

		// Update the state with the built conditions
		currentFilters.selectedConditions = conditions;

		// Notify of filter changes
		onFiltersChange(currentFilters);

		// Check if we need to fetch more entities after a small delay
		// setTimeout(() => onFetchMore(), 100);
	}, 150);

	// Watch for filter changes using a key string
	const filterKey = $derived(generateFilterKey(currentFilters));
	$effect(() => {
		// Always trigger on first run (when lastFilterKey is empty) or when filters change
		if (lastFilterKey === '' || filterKey !== lastFilterKey) {
			console.log('Filters updated:', lastFilterKey, '->', filterKey);
			lastFilterKey = filterKey;
			debouncedUpdate();
		}
	});

	return {
		get filters() {
			return currentFilters;
		},
		updateSearch: (query: string) => {
			currentFilters.searchQuery = query;
		},
		updateProvider: (provider: string) => {
			// Batch the updates to prevent multiple effect triggers
			const newFilters = { ...currentFilters };
			newFilters.selectedProvider = provider;

			if (provider !== 'all') {
				// Get available entity types for this provider
				const availableEntityTypes = providerEntityTypes[provider] || [];

				// If there's only one entity type, select it automatically
				if (availableEntityTypes.length === 1) {
					newFilters.selectedEntityType = availableEntityTypes[0];
				} else {
					newFilters.selectedEntityType = 'all'; // Reset to 'all' when multiple options
				}
			}
			currentFilters = newFilters;
		},
		updateEntityType: (entityType: string) => {
			currentFilters.selectedEntityType = entityType;
		},
		updateLinkedFilter: (showLinked: boolean, showUnlinked: boolean) => {
			// Batch the updates to prevent multiple effect triggers
			const newFilters = { ...currentFilters };
			newFilters.showLinkedOnly = showLinked;
			newFilters.showUnlinkedOnly = showUnlinked;
			currentFilters = newFilters;
		},
		updateConditionFilters: (filters: ConditionFilters) => {
			// Create a new object to ensure reactivity triggers
			const newFilters = { ...currentFilters };
			newFilters.conditionFilters = { ...filters };
			currentFilters = newFilters;
			console.log('Condition filters updated:', currentFilters.conditionFilters);
		},
		clearFilters: () => {
			currentFilters = createEmptyDashboardFilters();
		},
		applyTriggerFilters: (
			provider: string,
			entityType: string,
			conditions: Record<string, any>
		) => {
			// Batch all the updates to prevent multiple effect triggers
			const newFilters = { ...currentFilters };
			newFilters.selectedProvider = provider;
			newFilters.selectedEntityType = entityType;
			newFilters.selectedConditions = conditions;
			newFilters.conditionFilters = populateConditionFilters(conditions);
			currentFilters = newFilters;
		},
		getConditionPills: () => {
			return generateConditionPills(
				currentFilters.conditionFilters,
				currentFilters.selectedProvider,
				currentFilters.selectedEntityType
			);
		},
		removeConditionPill: (pillText = '') => {
			// Parse the pill text to determine which filter to clear
			const newFilters = { ...currentFilters.conditionFilters };

			// Extract field from pill text (format: "Field: Value")
			const [field] = pillText.split(': ');

			// Map display names back to filter field names
			const fieldMappings: Record<string, string> = {
				Author: 'prAuthor',
				Assignee: 'prAssignee',
				Reviewer: 'prReviewer',
				Base: 'prBaseBranch',
				Head: 'prHeadBranch',
				Repo: 'prRepository',
				Actor: 'workflowActor',
				Event: 'workflowEvent',
				Status: 'workflowStatus',
				Conclusion: 'workflowConclusion',
				Branch: 'workflowBranch',
				Team: 'linearTeam',
				Creator: 'linearCreator',
				Project: 'linearProject',
				Title: 'linearTitleContains',
				Activity: 'linearActivityTypes',
				'Min Est': 'linearMinEstimate',
				'Max Est': 'linearMaxEstimate',
				States: 'linearStates',
				'State Types': 'linearStateTypes',
				Labels: 'linearLabels',
				Priorities: 'linearPriorities'
			};

			const filterField = fieldMappings[field];
			if (filterField && filterField in newFilters) {
				delete (newFilters as any)[filterField];
				currentFilters.conditionFilters = newFilters;
			}
		},
		matchesTrigger: (trigger: any) => {
			return matchesTriggerConditions(currentFilters, trigger);
		}
	};
}

export interface CachedEntityData {
	entities: UnifiedEntity[];
	timestamp: number;
	filters: DashboardFilters;
}

export function createEntityCache() {
	let cache = $state(new Map<string, CachedEntityData>());
	const MAX_CACHE_SIZE = 50;
	const CACHE_TTL_MS = 5 * 60 * 1000; // 5 minutes

	return {
		get: (key: string) => {
			const cached = cache.get(key);
			if (!cached) return undefined;

			// Check if cache is still fresh
			const isExpired = Date.now() - cached.timestamp > CACHE_TTL_MS;
			if (isExpired) {
				cache.delete(key);
				return undefined;
			}

			return cached.entities;
		},
		set: (key: string, entities: UnifiedEntity[], filters: DashboardFilters) => {
			cache.set(key, {
				entities,
				timestamp: Date.now(),
				filters: { ...filters }
			});

			// Clean up cache if it gets too large
			if (cache.size > MAX_CACHE_SIZE) {
				const entries = Array.from(cache.entries());
				const recentEntries = entries.slice(-Math.floor(MAX_CACHE_SIZE / 2));
				cache.clear();
				recentEntries.forEach(([k, v]) => cache.set(k, v));
			}
		},
		has: (key: string) => {
			const cached = cache.get(key);
			if (!cached) return false;

			// Check if cache is still fresh
			const isExpired = Date.now() - cached.timestamp > CACHE_TTL_MS;
			if (isExpired) {
				cache.delete(key);
				return false;
			}

			return true;
		},
		clear: () => cache.clear(),
		get size() {
			return cache.size;
		},
		// Get cache info for debugging
		getCacheInfo: (key: string) => {
			const cached = cache.get(key);
			if (!cached) return null;

			const age = Date.now() - cached.timestamp;
			const isExpired = age > CACHE_TTL_MS;

			return {
				age,
				isExpired,
				entityCount: cached.entities.length,
				timestamp: cached.timestamp
			};
		}
	};
}
