/**
 * Condition Filters Utilities
 *
 * Centralized logic for handling entity condition filtering in the dashboard.
 * Supports GitHub (pull_request, workflow_run) and Linear (issue) entities.
 */

import type { UnifiedEntity } from './entity-conversion';
import { getCurrentRepository } from '$lib/utils/project-repository';
import { TriggerConditionType, GitHubEntityType, LinearEntityType } from '$lib/types/trigger-enums';

// Define all possible condition filter fields
export interface ConditionFilters {
	// GitHub Pull Request filters
	prAuthor: string;
	prAssignee: string;
	prReviewer: string;
	prBaseBranch: string;
	prHeadBranch: string;
	prRepository: string;
	prTitleContains: string;

	// GitHub Workflow Run filters
	workflowActor: string;
	workflowEvent: string;
	workflowStatus: string;
	workflowConclusion: string;
	workflowBranch: string;
	workflowRepository: string;

	// Linear Issue filters
	linearTeam: string;
	linearCreator: string;
	linearAssignee: string;
	linearProject: string;
	linearTitleContains: string;
	linearActivityTypes: string;
	linearMinEstimate: string;
	linearMaxEstimate: string;
	linearStates: string;
	linearStateTypes: string;
	linearLabels: string;
	linearPriorities: string;
}

// Create empty condition filters
export function createEmptyConditionFilters(): ConditionFilters {
	return {
		// GitHub Pull Request filters
		prAuthor: '',
		prAssignee: '',
		prReviewer: '',
		prBaseBranch: '',
		prHeadBranch: '',
		prRepository: '',
		prTitleContains: '',

		// GitHub Workflow Run filters
		workflowActor: '',
		workflowEvent: '',
		workflowStatus: '',
		workflowConclusion: '',
		workflowBranch: '',
		workflowRepository: '',

		// Linear Issue filters
		linearTeam: '',
		linearCreator: '',
		linearAssignee: '',
		linearProject: '',
		linearTitleContains: '',
		linearActivityTypes: '',
		linearMinEstimate: '',
		linearMaxEstimate: '',
		linearStates: '',
		linearStateTypes: '',
		linearLabels: '',
		linearPriorities: ''
	};
}

// Build API conditions object from filter inputs
export function buildConditionsFromFilters(
	filters: ConditionFilters,
	provider: string,
	entityType: string,
	githubRepository = getCurrentRepository()
): any {
	const conditions: any = {};

	if (provider === 'github' && entityType === 'pull_request') {
		conditions.type = TriggerConditionType.TRIGGER_CONDITION_GITHUB;
		conditions.github = {
			entity_type: GitHubEntityType.GITHUB_ENTITY_TYPE_PULL_REQUEST,
			pull_request: {
				repository: githubRepository
			}
		};
		const pr = conditions.github.pull_request;

		if (filters.prAuthor) pr.author = filters.prAuthor;
		if (filters.prAssignee) pr.assignee = filters.prAssignee;
		if (filters.prReviewer) pr.reviewer = filters.prReviewer;
		if (filters.prBaseBranch) pr.base_branch = filters.prBaseBranch;
		if (filters.prHeadBranch) pr.head_branch = filters.prHeadBranch;
		if (filters.prRepository) pr.repository = filters.prRepository;
		if (filters.prTitleContains) pr.title_contains = filters.prTitleContains;
	} else if (provider === 'github' && entityType === 'workflow_run') {
		conditions.type = TriggerConditionType.TRIGGER_CONDITION_GITHUB;
		conditions.github = {
			entity_type: GitHubEntityType.GITHUB_ENTITY_TYPE_WORKFLOW_RUN,
			workflow_run: {
				repository: githubRepository
			}
		};
		const wf = conditions.github.workflow_run;

		if (filters.workflowActor) wf.actor = filters.workflowActor;
		if (filters.workflowEvent) wf.event = filters.workflowEvent;
		if (filters.workflowStatus) wf.status = filters.workflowStatus;
		if (filters.workflowConclusion) wf.conclusion = filters.workflowConclusion;
		if (filters.workflowBranch) wf.branch = filters.workflowBranch;
		if (filters.workflowRepository) wf.repository = filters.workflowRepository;
	} else if (provider === 'linear' && entityType === 'issue') {
		conditions.type = TriggerConditionType.TRIGGER_CONDITION_LINEAR;
		conditions.linear = {
			entity_type: LinearEntityType.LINEAR_ENTITY_TYPE_ISSUE,
			issue: {}
		};
		const issue = conditions.linear.issue;

		if (filters.linearTeam) issue.team = filters.linearTeam;
		if (filters.linearCreator) issue.creator = filters.linearCreator;
		if (filters.linearAssignee) issue.assignee = filters.linearAssignee;
		if (filters.linearProject) issue.project = filters.linearProject;
		if (filters.linearTitleContains) issue.title_contains = filters.linearTitleContains;
		if (filters.linearActivityTypes) {
			issue.activity_types = filters.linearActivityTypes
				.split(',')
				.map((s) => s.trim())
				.filter((s) => s);
		}
		if (filters.linearMinEstimate) issue.min_estimate = parseInt(filters.linearMinEstimate);
		if (filters.linearMaxEstimate) issue.max_estimate = parseInt(filters.linearMaxEstimate);
		if (filters.linearStates) {
			issue.states = filters.linearStates
				.split(',')
				.map((s) => s.trim())
				.filter((s) => s);
		}
		if (filters.linearStateTypes) {
			issue.state_types = filters.linearStateTypes
				.split(',')
				.map((s) => s.trim())
				.filter((s) => s);
		}
		if (filters.linearLabels) {
			issue.labels = filters.linearLabels
				.split(',')
				.map((s) => s.trim())
				.filter((s) => s);
		}
		if (filters.linearPriorities) {
			issue.priorities = filters.linearPriorities
				.split(',')
				.map((s) => parseInt(s.trim()))
				.filter((n) => !isNaN(n));
		}
	}

	return conditions;
}

// Populate filter inputs from API conditions object
export function populateConditionFilters(conditions: any): ConditionFilters {
	const filters = createEmptyConditionFilters();

	if (!conditions) return filters;

	// Populate GitHub PR conditions
	if (conditions.github?.pull_request) {
		const pr = conditions.github.pull_request;
		filters.prAuthor = pr.author || '';
		filters.prAssignee = pr.assignee || '';
		filters.prReviewer = pr.reviewer || '';
		filters.prBaseBranch = pr.base_branch || '';
		filters.prHeadBranch = pr.head_branch || '';
		filters.prRepository = pr.repository || '';
		filters.prTitleContains = pr.title_contains || '';
	}

	// Populate GitHub Workflow conditions
	if (conditions.github?.workflow_run) {
		const wf = conditions.github.workflow_run;
		filters.workflowActor = wf.actor || '';
		filters.workflowEvent = wf.event || '';
		filters.workflowStatus = wf.status || '';
		filters.workflowConclusion = wf.conclusion || '';
		filters.workflowBranch = wf.branch || '';
		filters.workflowRepository = wf.repository || '';
	}

	// Populate Linear Issue conditions
	if (conditions.linear?.issue) {
		const issue = conditions.linear.issue;
		filters.linearTeam = issue.team || '';
		filters.linearCreator = issue.creator || '';
		filters.linearAssignee = issue.assignee || '';
		filters.linearProject = issue.project || '';
		filters.linearTitleContains = issue.title_contains || '';
		filters.linearActivityTypes = Array.isArray(issue.activity_types)
			? issue.activity_types.join(', ')
			: issue.activity_types || '';
		filters.linearMinEstimate = issue.min_estimate?.toString() || '';
		filters.linearMaxEstimate = issue.max_estimate?.toString() || '';
		filters.linearStates = Array.isArray(issue.states)
			? issue.states.join(', ')
			: issue.states || '';
		filters.linearStateTypes = Array.isArray(issue.state_types)
			? issue.state_types.join(', ')
			: issue.state_types || '';
		filters.linearLabels = Array.isArray(issue.labels)
			? issue.labels.join(', ')
			: issue.labels || '';
		filters.linearPriorities = Array.isArray(issue.priorities)
			? issue.priorities.join(', ')
			: issue.priorities || '';
	}

	return filters;
}

// Generate condition pills for display
export function generateConditionPills(
	filters: ConditionFilters,
	provider: string,
	entityType: string
): string[] {
	const pills: string[] = [];

	if (provider === 'github' && entityType === 'pull_request') {
		if (filters.prAuthor) pills.push(`Author: ${filters.prAuthor}`);
		if (filters.prAssignee) pills.push(`Assignee: ${filters.prAssignee}`);
		if (filters.prReviewer) pills.push(`Reviewer: ${filters.prReviewer}`);
		if (filters.prBaseBranch) pills.push(`Base: ${filters.prBaseBranch}`);
		if (filters.prHeadBranch) pills.push(`Head: ${filters.prHeadBranch}`);
		if (filters.prRepository) pills.push(`Repo: ${filters.prRepository}`);
	} else if (provider === 'github' && entityType === 'workflow_run') {
		if (filters.workflowActor) pills.push(`Actor: ${filters.workflowActor}`);
		if (filters.workflowEvent) pills.push(`Event: ${filters.workflowEvent}`);
		if (filters.workflowStatus) pills.push(`Status: ${filters.workflowStatus}`);
		if (filters.workflowConclusion) pills.push(`Conclusion: ${filters.workflowConclusion}`);
		if (filters.workflowBranch) pills.push(`Branch: ${filters.workflowBranch}`);
		if (filters.workflowRepository) pills.push(`Repo: ${filters.workflowRepository}`);
	} else if (provider === 'linear' && entityType === 'issue') {
		if (filters.linearTeam) pills.push(`Team: ${filters.linearTeam}`);
		if (filters.linearCreator) pills.push(`Creator: ${filters.linearCreator}`);
		if (filters.linearAssignee) pills.push(`Assignee: ${filters.linearAssignee}`);
		if (filters.linearProject) pills.push(`Project: ${filters.linearProject}`);
		if (filters.linearTitleContains) pills.push(`Title: ${filters.linearTitleContains}`);
		if (filters.linearActivityTypes) pills.push(`Activity: ${filters.linearActivityTypes}`);
		if (filters.linearMinEstimate) pills.push(`Min Est: ${filters.linearMinEstimate}`);
		if (filters.linearMaxEstimate) pills.push(`Max Est: ${filters.linearMaxEstimate}`);
		if (filters.linearStates) pills.push(`States: ${filters.linearStates}`);
		if (filters.linearStateTypes) pills.push(`State Types: ${filters.linearStateTypes}`);
		if (filters.linearLabels) pills.push(`Labels: ${filters.linearLabels}`);
		if (filters.linearPriorities) pills.push(`Priorities: ${filters.linearPriorities}`);
	}

	return pills;
}

// Apply condition filters to a unified entity
export function applyConditionFilters(entity: UnifiedEntity, conditions: any): boolean {
	// If no conditions, include the entity
	if (!conditions || Object.keys(conditions).length === 0) {
		return true;
	}

	// GitHub Pull Request filtering
	if (
		conditions.github?.pull_request &&
		entity.providerId === 'github' &&
		entity.entityType === 'pull_request'
	) {
		const pr = conditions.github.pull_request;
		const metadata = entity.metadata;

		console.log('Filtering GitHub PR:', {
			entityTitle: entity.title,
			prConditions: pr,
			metadata: {
				author: metadata?.user?.login,
				assignees: metadata?.assignees?.map((a: any) => a.login),
				reviewers: metadata?.requested_reviewers?.map((r: any) => r.login),
				baseBranch: metadata?.base?.ref,
				headBranch: metadata?.head?.ref
			}
		});

		if (pr.author && metadata?.user?.login !== pr.author && pr.author !== '@me') {
			console.log(`Author filter failed: ${metadata?.user?.login} !== ${pr.author}`);
			return false;
		}
		if (
			pr.assignee &&
			!metadata?.assignees?.some((a: any) => a.login === pr.assignee) &&
			pr.assignee !== '@me'
		) {
			console.log(`Assignee filter failed: ${pr.assignee} not in assignees`);
			return false;
		}
		if (
			pr.reviewer &&
			!metadata?.requested_reviewers?.some((r: any) => r.login === pr.reviewer) &&
			pr.reviewer !== '@me'
		) {
			console.log(`Reviewer filter failed: ${pr.reviewer} not in reviewers`);
			return false;
		}
		if (pr.base_branch && metadata?.base?.ref !== pr.base_branch) {
			console.log(`Base branch filter failed: ${metadata?.base?.ref} !== ${pr.base_branch}`);
			return false;
		}
		if (pr.head_branch && metadata?.head?.ref !== pr.head_branch) {
			console.log(`Head branch filter failed: ${metadata?.head?.ref} !== ${pr.head_branch}`);
			return false;
		}
		// if (pr.repository && !entity.metadata.repository.includes(pr.repository)) {
		// 	console.log(`Repository filter failed: ${entity.title} does not include ${pr.repository}`);
		// 	return false;
		// }
		if (
			pr.title_contains &&
			!entity.title.toLowerCase().includes(pr.title_contains.toLowerCase())
		) {
			console.log(`Title filter failed: ${entity.title} does not contain ${pr.title_contains}`);
			return false;
		}

		console.log('GitHub PR passed all filters');
	}

	// GitHub Workflow Run filtering
	if (
		conditions.github?.workflow_run &&
		entity.providerId === 'github' &&
		entity.entityType === 'workflow_run'
	) {
		const wf = conditions.github.workflow_run;
		const metadata = entity.metadata;

		if (wf.actor && metadata?.actor?.login !== wf.actor && wf.actor !== '@me') return false;
		if (wf.event && metadata?.event !== wf.event) return false;
		if (wf.status && metadata?.status !== wf.status) return false;
		if (wf.conclusion && metadata?.conclusion !== wf.conclusion) return false;
		if (wf.branch && metadata?.head_branch !== wf.branch) return false;
		// if (wf.repository && !entity.title.includes(wf.repository)) return false;
	}

	// Linear Issue filtering
	if (conditions.linear?.issue && entity.providerId === 'linear' && entity.entityType === 'issue') {
		const issue = conditions.linear.issue;
		const metadata = entity.metadata;

		if (issue.team && metadata.issue?.team?.key !== issue.team) return false;
		if (
			issue.creator &&
			metadata.issue?.creator?.email !== issue.creator &&
			issue.creator !== '@me'
		)
			return false;
		if (
			issue.assignee &&
			metadata.issue?.assignee?.email !== issue.assignee &&
			issue.assignee !== '@me'
		)
			return false;
		if (issue.project && metadata.issue?.project?.name !== issue.project) return false;
		if (
			issue.title_contains &&
			!entity.title.toLowerCase().includes(issue.title_contains.toLowerCase())
		)
			return false;
		if (
			issue.states &&
			!issue.states
				.split?.(',')
				.map((s: string) => s.trim())
				.includes(metadata.issue?.state?.name)
		)
			return false;
		if (
			issue.labels &&
			!issue.labels
				.split?.(',')
				.some((label: string) => metadata.issue?.labels?.some((l: any) => l.name === label.trim()))
		)
			return false;
		if (
			issue.priorities &&
			!issue.priorities
				.split?.(',')
				.map((p: string) => parseInt(p.trim()))
				.includes(metadata.issue?.priority)
		)
			return false;
		if (issue.min_estimate && (metadata.issue?.estimate || 0) < issue.min_estimate) return false;
		if (issue.max_estimate && (metadata.issue?.estimate || 0) > issue.max_estimate) return false;
	}

	return true;
}
