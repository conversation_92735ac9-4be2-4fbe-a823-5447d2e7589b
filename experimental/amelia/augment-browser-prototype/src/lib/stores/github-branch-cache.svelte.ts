import { githubAPI, GitHubUtils, type GitHubRepo, type GitHubBranch } from '$lib/api/github-api';
import fuzzysort from 'fuzzysort';

interface BranchCacheEntry {
	branches: GitHubBranch[];
	isLoading: boolean;
	isComplete: boolean; // All pages have been loaded
	lastFetch: number;
	error: string | null;
	hasNextPage: boolean;
	nextPage: number;
}

interface BranchCache {
	[repoKey: string]: BranchCacheEntry;
}

// Cache expires after 5 minutes
const CACHE_EXPIRY_MS = 5 * 60 * 1000;

// Create the cache store using Svelte 5 runes
const createBranchCacheStore = () => {
	let cache = $state<BranchCache>({});

	return {
		// Get cached branches for a repository
		getBranches: (repo: GitHubRepo): GitHubBranch[] => {
			const repoKey = GitHubUtils.getRepoKey(repo);
			const entry = cache[repoKey];
			return entry?.branches || [];
		},

		// Check if branches are currently loading
		isLoading: (repo: GitHubRepo): boolean => {
			const repoKey = GitHubUtils.getRepoKey(repo);
			const entry = cache[repoKey];
			return entry?.isLoading || false;
		},

		// Check if all branches have been loaded
		isComplete: (repo: GitHubRepo): boolean => {
			const repoKey = GitHubUtils.getRepoKey(repo);
			const entry = cache[repoKey];
			return entry?.isComplete || false;
		},

		// Check if cache is fresh (not expired)
		isFresh: (repo: GitHubRepo): boolean => {
			const repoKey = GitHubUtils.getRepoKey(repo);
			const entry = cache[repoKey];
			if (!entry) return false;

			return Date.now() - entry.lastFetch < CACHE_EXPIRY_MS;
		},

		// Load initial page of branches
		async loadBranches(repo: GitHubRepo): Promise<GitHubBranch[]> {
			const repoKey = GitHubUtils.getRepoKey(repo);

			// Initialize cache entry
			cache[repoKey] = {
				branches: [],
				isLoading: true,
				isComplete: false,
				lastFetch: Date.now(),
				error: null,
				hasNextPage: false,
				nextPage: 1
			};

			try {
				const response = await githubAPI.listBranches(repo.owner, repo.name, 1);

				// Update cache entry
				const entry = cache[repoKey];
				if (entry) {
					entry.branches = response.branches;
					entry.isLoading = false;
					entry.hasNextPage = response.has_next_page;
					entry.nextPage = response.next_page;
					entry.isComplete = !response.has_next_page;
					entry.error = null;
				}

				return response.branches;
			} catch (error) {
				const entry = cache[repoKey];
				if (entry) {
					entry.isLoading = false;
					entry.error = error instanceof Error ? error.message : 'Failed to load branches';
				}
				throw error;
			}
		},

		// Load all remaining pages for a repository
		async loadAllBranches(repo: GitHubRepo): Promise<GitHubBranch[]> {
			const repoKey = GitHubUtils.getRepoKey(repo);

			const entry = cache[repoKey];
			if (!entry || entry.isComplete) {
				return entry?.branches || [];
			}

			let currentPage = entry.nextPage;
			let stillHasNextPage = entry.hasNextPage;
			let allBranches = [...entry.branches];
			let consecutiveErrors = 0;
			const maxConsecutiveErrors = 3;

			while (stillHasNextPage && consecutiveErrors < maxConsecutiveErrors) {
				try {
					const response = await githubAPI.listBranches(repo.owner, repo.name, currentPage);
					allBranches = [...allBranches, ...response.branches];

					stillHasNextPage = response.has_next_page;
					currentPage = response.next_page;
					consecutiveErrors = 0; // Reset error count on success
				} catch (error) {
					consecutiveErrors++;
					console.error(
						`Error loading page ${currentPage} for ${repoKey} (attempt ${consecutiveErrors}/${maxConsecutiveErrors}):`,
						error
					);

					if (consecutiveErrors >= maxConsecutiveErrors) {
						console.error(
							`Too many consecutive errors loading branches for ${repoKey}, stopping at page ${currentPage}`
						);
						break;
					}

					// Wait a bit before retrying
					await new Promise((resolve) => setTimeout(resolve, 1000 * consecutiveErrors));
				}
			}

			// Update cache entry
			const cacheEntry = cache[repoKey];
			if (cacheEntry) {
				cacheEntry.branches = allBranches;
				cacheEntry.isComplete = !stillHasNextPage || consecutiveErrors >= maxConsecutiveErrors;
				cacheEntry.hasNextPage = stillHasNextPage && consecutiveErrors < maxConsecutiveErrors;
				cacheEntry.nextPage = currentPage;
			}

			return allBranches;
		},

		// Load more branches for search (load several more pages to get better results)
		async loadMoreForSearch(
			repo: GitHubRepo,
			searchTerm: string,
			currentMatches: GitHubBranch[]
		): Promise<GitHubBranch[]> {
			const repoKey = GitHubUtils.getRepoKey(repo);

			const entry = cache[repoKey];
			if (!entry || entry.isComplete) {
				return currentMatches;
			}

			// Set loading state
			entry.isLoading = true;

			try {
				let currentPage = entry.nextPage;
				let stillHasNextPage = entry.hasNextPage;
				let allBranches = [...entry.branches];
				let pagesLoaded = 0;
				const maxPagesToLoad = 100; // Load up to 10 more pages to get better search results

				// Load multiple pages to get better search results, don't stop early
				while (stillHasNextPage && pagesLoaded < maxPagesToLoad) {
					try {
						const response = await githubAPI.listBranches(repo.owner, repo.name, currentPage);
						allBranches = [...allBranches, ...response.branches];

						stillHasNextPage = response.has_next_page;
						currentPage = response.next_page;
						pagesLoaded++;
					} catch (error) {
						console.error('Error loading more branches for search:', error);
						break;
					}
				}

				// Update cache entry
				const cacheEntry = cache[repoKey];
				if (cacheEntry) {
					cacheEntry.branches = allBranches;
					cacheEntry.hasNextPage = stillHasNextPage;
					cacheEntry.nextPage = currentPage;
					cacheEntry.isComplete = !stillHasNextPage;
					cacheEntry.isLoading = false;
				}

				// Re-run fuzzy search on all loaded branches
				const fuzzyResults = fuzzysort.go(searchTerm, allBranches, {
					key: 'name',
					threshold: 0.3, // Match the threshold used in Combobox
					limit: 100
				});
				const matches = fuzzyResults.map((result) => result.obj);

				return matches;
			} catch (error) {
				// Make sure to clear loading state on error
				entry.isLoading = false;
				throw error;
			}
		},

		// Find and load the main/default branch specifically
		async findMainBranch(repo: GitHubRepo): Promise<GitHubBranch | null> {
			const repoKey = GitHubUtils.getRepoKey(repo);
			const entry = cache[repoKey];

			if (!entry) {
				// Load initial branches first
				await this.loadBranches(repo);
			}

			const currentBranches = this.getBranches(repo);

			// Priority order: main -> master -> repository.default_branch
			let mainBranch = currentBranches.find((b) => b.name === 'main');
			if (mainBranch) return mainBranch;

			mainBranch = currentBranches.find((b) => b.name === 'master');
			if (mainBranch) return mainBranch;

			if (repo.default_branch) {
				mainBranch = currentBranches.find((b) => b.name === repo.default_branch);
				if (mainBranch) return mainBranch;
			}

			// If not found in current branches and there are more pages, load all branches
			const cacheEntry = cache[repoKey];
			if (cacheEntry && !cacheEntry.isComplete) {
				const allBranches = await this.loadAllBranches(repo);

				// Try again with all branches
				mainBranch = allBranches.find((b) => b.name === 'main');
				if (mainBranch) return mainBranch;

				mainBranch = allBranches.find((b) => b.name === 'master');
				if (mainBranch) return mainBranch;

				if (repo.default_branch) {
					mainBranch = allBranches.find((b) => b.name === repo.default_branch);
					if (mainBranch) return mainBranch;
				}

				// If still not found, return the first branch
				return allBranches.length > 0 ? allBranches[0] : null;
			}

			// Return first branch if no main/master/default found
			return currentBranches.length > 0 ? currentBranches[0] : null;
		},

		// Clear cache for a specific repository
		clearRepo(repo: GitHubRepo) {
			const repoKey = GitHubUtils.getRepoKey(repo);
			delete cache[repoKey];
		},

		// Clear all cache
		clearAll() {
			cache = {};
		}
	};
};

export const branchCache = createBranchCacheStore();
