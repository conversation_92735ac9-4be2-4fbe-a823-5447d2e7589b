/**
 * Agent Preview Store
 *
 * Provides efficient preview data for agents including last messages and streaming content.
 * This store combines chat session data, streaming state, and agent information to provide
 * a unified view for displaying agent previews in lists.
 */

import { derived } from 'svelte/store';
import { globalState } from './global-state.svelte';
import { RemoteAgentStatus } from '$lib/api/unified-client';
import { extractLastMessageFromExchange } from '$lib/utils/exchange-content-utils';
import { getBestExchangeSummary } from '$lib/utils/exchange-summary-utils';
import { prepareTextForPreview } from '$lib/utils/text-preview-utils';

export interface AgentPreviewData {
	/** The last message content to display */
	lastMessage: string | null;
	/** Whether the agent is currently streaming */
	isStreaming: boolean;
	/** Current streaming content if streaming */
	streamingContent: string;
	/** Timestamp of the last message */
	lastMessageTime: number | null;
	/** Type of the last message content */
	contentType: 'user-message' | 'message' | 'streaming' | 'summary' | 'tool' | 'none';
	/** Whether the last message was a tool call */
	isToolCall: boolean;
	/** Tool name if the last message was a tool call */
	toolName?: string;
}

/**
 * Get preview data for a specific agent
 */
export function getAgentPreview(agentId: string) {
	return derived(globalState, (state): AgentPreviewData => {
		const agent = state.agents[agentId];
		if (!agent) {
			return {
				lastMessage: null,
				isStreaming: false,
				streamingContent: '',
				lastMessageTime: null,
				contentType: 'none',
				isToolCall: false
			};
		}

		// Find existing chat session for this agent
		const chatSession = Object.values(state.chatSessions).find(
			(session) => session.agentId === agentId
		);

		// Check if agent is currently running based on status and activity
		// Don't use hasUpdates here as it's about viewing status, not activity
		const isCurrentlyRunning =
			agent.status === RemoteAgentStatus.AGENT_RUNNING ||
			agent.status === RemoteAgentStatus.AGENT_STARTING ||
			// Also consider if there's an optimistic message or active streaming
			chatSession?.optimisticMessage !== undefined ||
			chatSession?.isStreaming === true ||
			(chatSession?.streamingMessages && chatSession.streamingMessages.size > 0);

		// Get current content from latest exchange instead of streaming messages
		let currentContent = '';
		if (chatSession?.exchanges && chatSession.exchanges.length > 0) {
			const lastExchange = chatSession.exchanges[chatSession.exchanges.length - 1];
			const extractedContent = extractLastMessageFromExchange(lastExchange);
			currentContent = extractedContent.content || '';
		}

		// Priority 1: If agent is NOT running, show general summary first
		if (!isCurrentlyRunning) {
			// Try session summary first
			// if (agent.sessionSummary) {
			// 	return {
			// 		lastMessage: extractLastSentence(agent.sessionSummary),
			// 		isStreaming: false,
			// 		streamingContent: '',
			// 		lastMessageTime: agent.updatedAt?.getTime() || null,
			// 		contentType: 'summary',
			// 		isToolCall: false
			// 	};
			// }

			// Try to get summary from exchanges using utility functions
			if (chatSession?.exchanges && chatSession.exchanges.length > 0) {
				const exchangeSummary = getBestExchangeSummary(chatSession.exchanges);
				if (exchangeSummary) {
					return {
						lastMessage: prepareTextForPreview(exchangeSummary),
						isStreaming: false,
						streamingContent: '',
						lastMessageTime: null,
						contentType: 'summary',
						isToolCall: false
					};
				}

				// Fallback to last exchange content if no summary
				const lastExchange = chatSession.exchanges[chatSession.exchanges.length - 1];
				const extractedContent = extractLastMessageFromExchange(lastExchange);
				if (extractedContent.content) {
					return {
						lastMessage: prepareTextForPreview(extractedContent.content),
						isStreaming: false,
						streamingContent: '',
						lastMessageTime: lastExchange.exchange.createdAt?.getTime() || null,
						contentType: extractedContent.type,
						isToolCall: extractedContent.isToolCall,
						toolName: extractedContent.toolName
					};
				}
			}

			// Final fallback for non-running agents: chat messages
			if (chatSession?.messages && chatSession.messages.length > 0) {
				const lastMessage = chatSession.messages[chatSession.messages.length - 1];
				return {
					lastMessage: prepareTextForPreview(lastMessage.content),
					isStreaming: false,
					streamingContent: '',
					lastMessageTime: lastMessage.timestamp,
					contentType: 'message',
					isToolCall: false
				};
			}
		}

		// Priority 2: If agent IS running, show last sentence or tool call
		if (isCurrentlyRunning) {
			// if the last message is a user message, just show it
			if (
				chatSession?.messages &&
				chatSession.messages.length > 0 &&
				// last message content !== last exchange requestMessage
				chatSession.messages[chatSession.messages.length - 1].content !==
					chatSession.exchanges[chatSession.exchanges.length - 1].exchange.requestMessage
			) {
				const lastMessage = chatSession.messages[chatSession.messages.length - 1];
				if (lastMessage.role === 'user') {
					return {
						lastMessage: prepareTextForPreview(lastMessage.content),
						isStreaming: false,
						streamingContent: '',
						lastMessageTime: lastMessage.timestamp,
						contentType: 'user-message',
						isToolCall: false
					};
				}
			}

			// Show last exchange content (could be tool call or text)
			if (chatSession?.exchanges && chatSession.exchanges.length > 0) {
				const lastExchange = chatSession.exchanges[chatSession.exchanges.length - 1];
				const extractedContent = extractLastMessageFromExchange(lastExchange);
				if (extractedContent.content) {
					return {
						lastMessage: prepareTextForPreview(extractedContent.content),
						isStreaming: true,
						streamingContent: currentContent,
						lastMessageTime: lastExchange.exchange.createdAt?.getTime() || null,
						contentType: extractedContent.type,
						isToolCall: extractedContent.isToolCall,
						toolName: extractedContent.toolName
					};
				}
			}

			// Fallback to last chat message for running agents
			if (chatSession?.messages && chatSession.messages.length > 0) {
				const lastMessage = chatSession.messages[chatSession.messages.length - 1];
				return {
					lastMessage: prepareTextForPreview(lastMessage.content),
					isStreaming: true,
					streamingContent: currentContent,
					lastMessageTime: lastMessage.timestamp,
					contentType: 'message',
					isToolCall: false
				};
			}
		}

		// No content available
		return {
			lastMessage: null,
			isStreaming: isCurrentlyRunning,
			streamingContent: '',
			lastMessageTime: null,
			contentType: 'none',
			isToolCall: false
		};
	});
}

/**
 * Get preview data for all agents
 */
export const allAgentPreviews = derived(globalState, (state) => {
	const previews: Record<string, AgentPreviewData> = {};

	for (const agentId of Object.keys(state.agents)) {
		const agent = state.agents[agentId];
		if (!agent) continue;

		// Find existing chat session for this agent
		const chatSession = Object.values(state.chatSessions).find(
			(session) => session.agentId === agentId
		);

		// Check if agent is currently running based on status and activity
		// Don't use hasUpdates here as it's about viewing status, not activity
		const isCurrentlyRunning =
			agent.status === RemoteAgentStatus.AGENT_RUNNING ||
			agent.status === RemoteAgentStatus.AGENT_STARTING ||
			// Also consider if there's an optimistic message or active streaming
			chatSession?.optimisticMessage !== undefined ||
			chatSession?.isStreaming === true ||
			(chatSession?.streamingMessages && chatSession.streamingMessages.size > 0);

		// Get current content from latest exchange instead of streaming messages
		let currentContent = '';
		if (chatSession?.exchanges && chatSession.exchanges.length > 0) {
			const lastExchange = chatSession.exchanges[chatSession.exchanges.length - 1];
			const extractedContent = extractLastMessageFromExchange(lastExchange);
			currentContent = extractedContent.content || '';
		}

		// Priority 1: If agent is NOT running, show general summary first
		if (!isCurrentlyRunning) {
			// Try session summary first
			if (agent.sessionSummary) {
				previews[agentId] = {
					lastMessage: prepareTextForPreview(agent.sessionSummary),
					isStreaming: false,
					streamingContent: '',
					lastMessageTime: agent.updatedAt?.getTime() || null,
					contentType: 'summary',
					isToolCall: false
				};
				continue;
			}

			// Try to get summary from exchanges using utility functions
			if (chatSession?.exchanges && chatSession.exchanges.length > 0) {
				const exchangeSummary = getBestExchangeSummary(chatSession.exchanges);
				if (exchangeSummary) {
					previews[agentId] = {
						lastMessage: prepareTextForPreview(exchangeSummary),
						isStreaming: false,
						streamingContent: '',
						lastMessageTime: null,
						contentType: 'summary',
						isToolCall: false
					};
					continue;
				}

				// Fallback to last exchange content if no summary
				const lastExchange = chatSession.exchanges[chatSession.exchanges.length - 1];
				const extractedContent = extractLastMessageFromExchange(lastExchange);
				if (extractedContent.content) {
					previews[agentId] = {
						lastMessage: prepareTextForPreview(extractedContent.content),
						isStreaming: false,
						streamingContent: '',
						lastMessageTime: lastExchange.exchange.createdAt?.getTime() || null,
						contentType: extractedContent.type,
						isToolCall: extractedContent.isToolCall,
						toolName: extractedContent.toolName
					};
					continue;
				}
			}

			// Final fallback for non-running agents: chat messages
			if (chatSession?.messages && chatSession.messages.length > 0) {
				const lastMessage = chatSession.messages[chatSession.messages.length - 1];
				previews[agentId] = {
					lastMessage: prepareTextForPreview(lastMessage.content),
					isStreaming: false,
					streamingContent: '',
					lastMessageTime: lastMessage.timestamp,
					contentType: 'message',
					isToolCall: false
				};
				continue;
			}
		}

		// Priority 2: If agent IS running, show last sentence or tool call
		if (isCurrentlyRunning) {
			// Show last exchange content (could be tool call or text)
			if (chatSession?.exchanges && chatSession.exchanges.length > 0) {
				const lastExchange = chatSession.exchanges[chatSession.exchanges.length - 1];
				const extractedContent = extractLastMessageFromExchange(lastExchange);
				if (extractedContent.content) {
					previews[agentId] = {
						lastMessage: prepareTextForPreview(extractedContent.content),
						isStreaming: true,
						streamingContent: currentContent,
						lastMessageTime: lastExchange.exchange.createdAt?.getTime() || null,
						contentType: extractedContent.type,
						isToolCall: extractedContent.isToolCall,
						toolName: extractedContent.toolName
					};
					continue;
				}
			}

			// Fallback to last chat message for running agents
			if (chatSession?.messages && chatSession.messages.length > 0) {
				const lastMessage = chatSession.messages[chatSession.messages.length - 1];
				previews[agentId] = {
					lastMessage: prepareTextForPreview(lastMessage.content),
					isStreaming: true,
					streamingContent: currentContent,
					lastMessageTime: lastMessage.timestamp,
					contentType: 'message',
					isToolCall: false
				};
				continue;
			}
		}

		// No content available
		previews[agentId] = {
			lastMessage: null,
			isStreaming: isCurrentlyRunning,
			streamingContent: '',
			lastMessageTime: null,
			contentType: 'none',
			isToolCall: false
		};
	}

	return previews;
});
