import {
	EventSource,
	TriggerConditionType,
	GitHubEntityType,
	LinearEntityType,
	type TriggerConfiguration
} from '$lib/types/trigger-enums';
import { repositoryToUrl } from '$lib/utils/project-repository';

// Simplified interface - no complex processing needed
export interface DefaultTrigger {
	id: string;
	name: string;
	description: string;
	explanation?: string;
	icon: string;
	provider: 'github' | 'linear' | 'jira';
	configuration: TriggerConfiguration;
	/** Optional prefix version to use for this trigger (defaults to latest) */
	prefixVersion?: string;
}

// Direct trigger definitions - no complex processing needed
export const myPRAssistant: DefaultTrigger = {
	id: 'my-pr-assistant',
	name: 'Review my own PRs',
	description: 'Context-aware review and assistance for your pull requests',
	explanation:
		'Automatically reviews your PRs to provide helpful feedback, catch potential issues, and suggest improvements. Acts as your coding partner to help you ship better code faster.',
	icon: '👨‍💻',
	provider: 'github',
	prefixVersion: 'v2', // Use simplified prefix without notebook instructions
	configuration: {
		name: 'Review my own PRs',
		description: 'Context-aware review and assistance for your pull requests',
		event_source: EventSource.EVENT_SOURCE_GITHUB,
		conditions: {
			type: TriggerConditionType.TRIGGER_CONDITION_GITHUB,
			github: {
				entity_type: GitHubEntityType.GITHUB_ENTITY_TYPE_PULL_REQUEST,
				pull_request: {
					repository: 'augmentcode/augment',
					author: '@me',
					activity_types: ['opened', 'synchronize', 'ready_for_review']
				}
			}
		},
		agent_config: {
			user_guidelines: `I'm working on this pull request. Please help me by:

1. **If just opened/updated**: Review my changes for obvious issues, suggest improvements
2. **If ready for review**: Help me prepare a clear description and anticipate reviewer questions
3. **If has review comments**: Help me understand and address the feedback
4. **Always**: Check for missing tests, documentation, or security concerns

Focus on being a helpful coding partner who catches things I might miss.`,
			workspace_guidelines: '',
			workspace_setup: {
				starting_files: {
					github_ref: {
						url: '@{pr_head_repo_url}',
						ref: '@{pr_head_ref}'
					}
				}
			}
		},
		enabled: false
	}
};

export const reviewHelper: DefaultTrigger = {
	id: 'review-helper',
	name: 'Review PRs assigned to me',
	description: 'Intelligent assistance when reviewing others pull requests',
	explanation:
		"Triggers when you're requested as a reviewer to help you provide thorough, actionable feedback by analyzing scope, complexity, and potential issues.",
	icon: '🔍',
	provider: 'github',
	prefixVersion: 'v2', // Use simplified prefix without notebook instructions
	configuration: {
		name: 'Review PRs assigned to me',
		description: "Intelligent assistance when reviewing others' pull requests",
		event_source: EventSource.EVENT_SOURCE_GITHUB,
		conditions: {
			type: TriggerConditionType.TRIGGER_CONDITION_GITHUB,
			github: {
				entity_type: GitHubEntityType.GITHUB_ENTITY_TYPE_PULL_REQUEST,
				pull_request: {
					repository: 'augmentcode/augment',
					reviewer: '@me',
					activity_types: ['review_requested', 'ready_for_review']
				}
			}
		},
		agent_config: {
			user_guidelines: `I've been requested to review this PR. Please help me by:

1. **Initial assessment**: What's the scope and complexity of changes?
2. **Key areas to focus**: What are the most important things to check?
3. **Potential issues**: What could go wrong with these changes?
4. **Testing considerations**: Are the tests adequate?

Provide specific, actionable feedback I can give to the author.`,
			workspace_guidelines: '',
			workspace_setup: {
				starting_files: {
					github_ref: {
						url: '@{pr_head_repo_url}',
						ref: '@{pr_head_ref}'
					}
				}
			}
		},
		enabled: false
	}
};

export const ciFailureFixer: DefaultTrigger = {
	id: 'ci-failure-fixer',
	name: 'Fix CI failures',
	description: 'Automatically address CI failures with targeted fixes',
	explanation:
		'Analyzes failed CI checks, creates focused fixes for actionable failures, and presents proposed changes for your review before applying them.',
	icon: '🔧',
	provider: 'github',
	configuration: {
		name: 'Fix CI failures',
		description: 'Automatically address CI failures with targeted fixes',
		event_source: EventSource.EVENT_SOURCE_GITHUB,
		conditions: {
			type: TriggerConditionType.TRIGGER_CONDITION_GITHUB,
			github: {
				entity_type: GitHubEntityType.GITHUB_ENTITY_TYPE_PULL_REQUEST,
				pull_request: {
					repository: 'augmentcode/augment',
					author: '@me',
					activity_types: ['pr_bot_check_suite_failure', 'pr_bot_status_failure']
				}
			}
		},
		agent_config: {
			user_guidelines: `Fix CI Failures with Sub-Agents
Create sub-agents for actionable CI failures, then view their proposed diffs, and present a summary to the user for approval, without applying any changes locally.

Process:
1. Analyze all failed workflow runs, check suites, and commit statuses from the latest commit
2. For each failure, determine if it's actionable (can be fixed with code changes)
3. Create sub-agents for actionable failures with specific failure context and logs
4. View diffs from completed sub-agents
5. Present summary to user for approval without applying any changes locally

Sub-Agent Template
\`\`\`
Fix CI Failure: {failure_type} - {failure_name}
CI Failure Details
- Type: {failure_type} (check_suite/status/workflow_run)
- Name: {failure_name}
- Conclusion: {conclusion}
- Context: {context}
- Description: {description}
- Error Details: {error_details}

Task
Make MINIMAL change to fix this specific CI failure only.

Constraints
- No branches, commits, pushes, or GitHub interactions
- No unrelated improvements or optimizations
- Only modify code directly related to this CI failure
- Focus on the root cause indicated by the failure details
\`\`\`

Create sub-agents for actionable failures like:
- Test failures: "Tests failed due to assertion errors"
- Build failures: "Compilation failed due to syntax errors"
- Linting failures: "Code style violations detected"
- Type checking failures: "Type errors found"
- Security scan failures: "Vulnerabilities detected in dependencies"
- Coverage failures: "Code coverage below threshold"

Don't create sub-agents for non-actionable failures:
- Infrastructure issues: "Runner out of disk space"
- External service failures: "Docker registry unavailable"
- Timeout issues: "Job cancelled due to timeout"
- Permission issues: "Access denied to external resource"
- Flaky test failures: "Intermittent network connectivity issues"

Failure Analysis Process:
1. Check failed check suites in _augment_failed_check_suites
2. Check failed statuses in _augment_failed_statuses
3. For each failure, fetch detailed logs if available
4. Analyze failure type and determine actionability
5. Extract specific error messages and context
6. Create targeted sub-agents with failure-specific instructions`,
			workspace_guidelines: '',
			workspace_setup: {
				starting_files: {
					github_ref: {
						url: '@{pr_head_repo_url}',
						ref: '@{pr_head_ref}'
					}
				}
			}
		},
		enabled: false
	}
};

export const reviewedPRAssistant: DefaultTrigger = {
	id: 'reviewed-pr-assistant',
	name: 'Address PR comments',
	description: 'Automatically address review feedback and implement requested changes',
	explanation:
		'Help with PRs with comments by implementing the requested changes, fixing identified issues, and addressing reviewer feedback directly in the code.',
	icon: '🔄',
	provider: 'github',
	configuration: {
		name: 'Address PR comments',
		description: 'Automatically address review feedback and implement requested changes',
		event_source: EventSource.EVENT_SOURCE_GITHUB,
		conditions: {
			type: TriggerConditionType.TRIGGER_CONDITION_GITHUB,
			github: {
				entity_type: GitHubEntityType.GITHUB_ENTITY_TYPE_PULL_REQUEST,
				pull_request: {
					repository: 'augmentcode/augment',
					author: '@me',
					event: 'pull_request_review_comment',
					actions: ['created', 'edited']
				}
			}
		},
		agent_config: {
			user_guidelines: `Address PR Review Comments with Sub-Agents
Create sub-agents for review comments that request specific changes, then view their proposed diffs, and present a summary to the user for approval, without applying any changes locally.
Process:
1. Check if any inline comments exist in the PR - if none exist, do nothing and exit
2. Analyze comments and only create sub-agents for actionable requests (not questions/discussions)
3. Create sub-agents named by comment ID with comment content, file path, and line number
4. View diffs from completed sub-agents
5. Present summary to user for approval without applying any changes locally
Sub-Agent Template
\`\`\`
Address Review Comment {comment_id}
Review Comment Details
- File: {file_path}
- Line: {line_number}
- Comment: {comment_content}
Task
Make MINIMAL change to address this specific comment only.
Constraints
- No branches, commits, pushes, or GitHub interactions
- No unrelated improvements or optimizations
- Only modify code directly related to the comment
\`\`\`
Create sub-agents for:
- Direct requests: "Add error handling here"
- Implicit requests: "This could cause a memory leak"
- Bug reports: "This will fail when input is null"
Don't create sub-agents for:
- Questions: "Why did you choose this approach?"
- Discussions: "We should consider using a different library"
- Praise: "Nice work on this implementation"
- Vague suggestions: "This could be better"
`,
			workspace_guidelines: '',
			workspace_setup: {
				starting_files: {
					github_ref: {
						url: '@{pr_head_repo_url}',
						ref: '@{pr_head_ref}'
					}
				}
			}
		},
		enabled: false
	}
};

// Linear Issue Assistant
export const linearIssueAssistant: DefaultTrigger = {
	id: 'linear-issue-assistant',
	name: 'Resolve Linear issues',
	description: 'Analyzes and attempts to resolve Linear issues assigned to you',
	explanation:
		'Systematically resolves Linear issues by implementing code changes, running tests, and creating pull requests. Goes beyond analysis to deliver complete solutions ready for review.',
	icon: '🎯',
	provider: 'linear',
	configuration: {
		name: 'Resolve Linear issues',
		description: 'Analyzes and attempts to resolve Linear issues assigned to you',
		event_source: EventSource.EVENT_SOURCE_LINEAR,
		conditions: {
			type: TriggerConditionType.TRIGGER_CONDITION_LINEAR,
			linear: {
				entity_type: LinearEntityType.LINEAR_ENTITY_TYPE_ISSUE,
				issue: {
					assignee: '@me',
					state_types: ['backlog', 'unstarted', 'triage']
				}
			}
		},
		agent_config: {
			user_guidelines: `You are helping me resolve a Linear issue assigned to me. Follow this systematic approach to analyze, implement, and deliver a complete solution.

## Process Overview
Follow these steps to resolve the issue completely:

1. **Analysis & Exploration**: Understand the issue thoroughly and explore the relevant codebase
2. **Implementation**: Make the necessary code changes to resolve the issue
3. **Testing & Validation**: Ensure your solution works correctly and doesn't break existing functionality
4. **Pull Request Creation**: Always create and open a PR with your changes

## Detailed Step-by-Step Guide

### Step 1: Analysis & Exploration
- **Issue Summary**: Provide a clear, concise overview of what this issue is about
- **Context Analysis**: Identify what parts of the codebase/project this relates to
- **Requirements Analysis**: Break down the specific requirements and acceptance criteria
- **Codebase Exploration**: Explore the repository to understand the current implementation
- **File Identification**: Identify all files and components that need to be modified
- **Pattern Recognition**: Look for similar patterns or existing solutions in the codebase
- **Test Analysis**: Check for any related tests that need to be updated or created
- **Implementation Strategy**: Outline your approach to tackle this issue based on your exploration
- **Priority Assessment**: Evaluate if the current priority is appropriate and explain why
- **Risk Assessment**: Identify potential risks or edge cases to consider

### Step 2: Implementation
- Make minimal, focused changes that directly address the issue requirements
- Follow existing code patterns and conventions in the repository
- Ensure your changes are backward compatible unless explicitly required otherwise
- Add appropriate error handling and logging where necessary
- Add inline code comments for complex logic and update relevant documentation as needed

### Step 3: Testing & Validation
- **Dependency Setup**: If there are missing tools or third-party dependencies needed to run tests, install them by:
  - Checking for dependency files (requirements.txt, package.json, Cargo.toml, go.mod, etc.)
  - Installing from these files rather than individual packages when possible
  - Setting up virtual environments or package managers as needed by the project
- **Run Existing Tests**: Execute existing tests to ensure nothing is broken
- **Create New Tests**: Write new tests if the functionality requires it
- **Test Edge Cases**: Validate edge cases and error scenarios
- **Requirements Validation**: Confirm that the solution meets all requirements from the issue

### Step 4: Pull Request Creation (REQUIRED)
**You MUST always create and open a PR at the end. This is mandatory.**
- Create a new branch with a descriptive name based on the Linear issue
- Commit your changes with clear, descriptive commit messages
- Create a PR with:
  - Clear title referencing the Linear issue
  - Detailed description of changes made
  - Link to the Linear issue
  - Any testing instructions or notes for reviewers
- Open the PR for review

## Guidelines and Constraints
- **Always complete with a PR**: Every issue resolution must end with creating and opening a pull request
- **Minimal changes**: Make only the changes necessary to resolve the issue
- **No unrelated improvements**: Stay focused on the specific issue requirements
- **Follow conventions**: Respect existing code style and patterns
- **Test thoroughly**: Ensure your changes don't break existing functionality
- **Document changes**: Update relevant documentation and add clear commit messages

Remember: The goal is to deliver a complete, tested solution via a pull request that fully resolves the Linear issue.`,
			workspace_guidelines: '',
			workspace_setup: {
				starting_files: {
					github_ref: {
						url: repositoryToUrl('augmentcode/augment'),
						ref: 'main'
					}
				}
			}
		},
		enabled: false
	}
};

export function getDefaultGitHubTriggers(): DefaultTrigger[] {
	return [myPRAssistant, reviewHelper, ciFailureFixer, reviewedPRAssistant];
}

/**
 * Get default Linear triggers
 */
export function getDefaultLinearTriggers(): DefaultTrigger[] {
	return [linearIssueAssistant];
}

/**
 * All default triggers for easy import
 */
export const allDefaultTriggers = [
	myPRAssistant,
	reviewHelper,
	ciFailureFixer,
	reviewedPRAssistant,
	linearIssueAssistant
];

// Functions for backward compatibility
export function getAllDefaultTriggers(): DefaultTrigger[] {
	return [...getDefaultGitHubTriggers(), ...getDefaultLinearTriggers()];
}

export function getDefaultTriggersByProvider(provider: 'github' | 'linear'): DefaultTrigger[] {
	switch (provider) {
		case 'github':
			return getDefaultGitHubTriggers();
		case 'linear':
			return getDefaultLinearTriggers();
		default:
			return [];
	}
}

/**
 * Mapping of old trigger names to new trigger names to prevent duplicates
 * when trigger names are updated
 */
const OLD_TRIGGER_NAME_MAPPINGS: Record<string, string> = {
	'Review my created PRs': 'Review my own PRs',
	'Help review PRs assigned to me': 'Review PRs assigned to me',
	'Auto-fix PRs with review comments': 'Address PR comments',
	'Handle my Linear issues': 'Address open tickets'
	// Add more mappings here as trigger names are updated
};

/**
 * Check which default triggers are missing from the existing triggers
 * This function checks against both current and old trigger names to prevent
 * creating duplicates when trigger names are updated
 */
export function getMissingDefaultTriggers(existingTriggers: any[]): DefaultTrigger[] {
	const allDefaults = getAllDefaultTriggers();
	const existingNames = new Set(existingTriggers.map((t) => t.name));

	return allDefaults.filter((defaultTrigger) => {
		// Check if trigger with current name already exists
		if (existingNames.has(defaultTrigger.name)) {
			return false;
		}

		// Check if trigger with old name already exists
		// Find the old name that maps to this trigger's current name
		const oldName = Object.keys(OLD_TRIGGER_NAME_MAPPINGS).find(
			(oldName) => OLD_TRIGGER_NAME_MAPPINGS[oldName] === defaultTrigger.name
		);

		if (oldName && existingNames.has(oldName)) {
			return false;
		}

		return true;
	});
}

export function convertDefaultTriggerToConfiguration(
	defaultTrigger: DefaultTrigger
): TriggerConfiguration {
	return defaultTrigger.configuration;
}
