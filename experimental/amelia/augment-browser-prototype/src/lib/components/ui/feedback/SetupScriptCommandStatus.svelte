<script lang="ts">
	import { Icon, CheckCircle, XCircle, MinusCircle } from 'svelte-hero-icons';
	import LoadingIndicator from './LoadingIndicator.svelte';

	interface Props {
		commandResult: 'success' | 'error' | 'skipped' | 'loading' | 'none';
		class?: string;
	}

	let { commandResult, class: className = '' }: Props = $props();
</script>

{#if commandResult === 'loading'}
	<LoadingIndicator size="sm" class="text-slate-600 dark:text-slate-400 {className}" />
{:else if commandResult === 'success'}
	<Icon
		src={CheckCircle}
		class="h-4 w-4 text-green-600 dark:text-green-400 {className}"
		aria-label="Success"
		micro
	/>
{:else if commandResult === 'error'}
	<Icon
		src={XCircle}
		class="h-4 w-4 text-red-600 dark:text-red-400 {className}"
		aria-label="Error"
		micro
	/>
{:else if commandResult === 'skipped'}
	<Icon
		src={MinusCircle}
		class="h-4 w-4 text-slate-600 dark:text-slate-400 {className}"
		aria-label="Skipped"
		micro
	/>
{/if}
