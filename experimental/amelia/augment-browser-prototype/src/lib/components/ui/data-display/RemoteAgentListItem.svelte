<script lang="ts">
	import type { CleanRemoteAgent } from '$lib/api/unified-client';
	import { RemoteAgentStatus, RemoteAgentWorkspaceStatus } from '$lib/api/unified-client';
	import { getTriggerForAgent } from '$lib/stores/global-state.svelte';
	import RemoteAgentTitle from '../RemoteAgentTitle.svelte';
	import AuggieAvatar from '../visualization/AuggieAvatar.svelte';
	import RemoteAgentStatusIndicator from '../feedback/RemoteAgentStatusIndicator.svelte';
	import { scale } from 'svelte/transition';
	import { easeBounceInOut } from 'd3';
	import { elasticOut } from 'svelte/easing';

	interface Props {
		agent: CleanRemoteAgent;
		onAgentClick?: (agent: CleanRemoteAgent) => void;
		isSelected?: boolean;
		class?: string;
	}

	let { agent, onAgentClick, isSelected, class: className = '' }: Props = $props();

	// Find the trigger that created this execution
	let triggerInfo = $derived(getTriggerForAgent(agent.id));
	let trigger = $derived($triggerInfo?.data || null);

	function handleClick() {
		onAgentClick?.(agent);
	}

	function handleKeydown(e: KeyboardEvent) {
		if (e.key === 'Enter' || e.key === ' ') {
			e.preventDefault();
			handleClick();
		}
	}
</script>

<div
	class="group/agent-item relative flex w-full items-center gap-2.5 py-2 pr-2.5 pl-2 text-sm transition-all duration-200 {onAgentClick
		? 'cursor-pointer hover:bg-slate-100 dark:hover:bg-slate-800'
		: ''} {isSelected ? 'bg-slate-100 dark:bg-slate-800' : ''} {className}"
	onclick={handleClick}
	onkeydown={handleKeydown}
	role={onAgentClick ? 'button' : 'div'}
	tabindex={onAgentClick ? 0 : -1}
>
	<!-- Unread indicator -->
	{#if agent.hasUpdates && agent.status === RemoteAgentStatus.AGENT_IDLE}
		<div
			in:scale={{ easing: elasticOut }}
			out:scale
			class="absolute top-1 left-1 z-10 h-2 w-2 rounded-full border border-white bg-emerald-500 dark:border-slate-900"
		></div>
	{/if}

	<!-- Avatar -->
	<div class="flex flex-shrink-0 items-center justify-center">
		<AuggieAvatar colorSeed={agent.id} faceSeed={agent.id} size={23} />
	</div>

	<!-- Content -->
	<div class="min-w-0 flex-1">
		<div
			class="-translate-x-2 transform opacity-0 transition-all duration-200 group-hover/sidebar:translate-x-0 group-hover/sidebar:opacity-100"
		>
			<RemoteAgentTitle
				{agent}
				size="sm"
				class="wrap-break-wordtext-xs !line-clamp-1  truncate font-normal break-words text-slate-900 dark:text-white"
			/>
		</div>
	</div>

	<!-- Status indicator -->
	{#if agent.status !== RemoteAgentStatus.AGENT_IDLE || agent.hasUpdates}
		<div class="flex-shrink-0">
			<RemoteAgentStatusIndicator
				isExpanded
				variant="default"
				status={agent.status}
				workspaceStatus={agent.workspaceStatus}
				hasUpdates={agent.hasUpdates}
				size="sm"
			/>
		</div>
	{/if}
</div>
