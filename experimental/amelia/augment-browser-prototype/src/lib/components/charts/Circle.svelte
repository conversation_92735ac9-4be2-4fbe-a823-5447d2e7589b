<script lang="ts">
	import { getContext } from 'svelte';
	import type { ScaleLinear, ScaleTime, ScaleBand } from 'd3';

	interface DataPoint {
		x: any;
		y: any;
		[key: string]: any;
	}

	interface Props {
		data?: DataPoint[];
		x?: number;
		y?: number;
		r?: number;
		fill?: string;
		stroke?: string;
		strokeWidth?: number;
		opacity?: number;
		className?: string;
	}

	let {
		data,
		x,y,
		r = 4,
		fill = '#3b82f6',
		stroke = 'none',
		strokeWidth = 1,
		opacity = 1,
		className = ''
	}: Props = $props();

	// Get chart context
	// const chart = getContext('chart') as {
	// 	xScale: ScaleLinear<number, number> | ScaleTime<number, number> | ScaleBand<string>;
	// 	yScale: ScaleLinear<number, number> | ScaleBand<string>;
	// };

	// function getX(d: DataPoint) {
	// 	if (Number.isFinite(x)) {
	// 		return x;
	// 	}
	// 	if (typeof x === 'function') {
	// 		return x(d);
	// 	}
	// 	const scale = chart.xScale;
	// 	if ('bandwidth' in scale) {
	// 		// ScaleBand
	// 		const pos = (scale as ScaleBand<string>)(String(d.x));
	// 		return pos !== undefined ? pos + (scale as ScaleBand<string>).bandwidth() / 2 : 0;
	// 	} else {
	// 		// ScaleLinear or ScaleTime
	// 		return (scale as ScaleLinear<number, number> | ScaleTime<number, number>)(d.x);
	// 	}
	// }

	// function getY(d: DataPoint) {
	// 	if (Number.isFinite(y)) {
	// 		return y;
	// 	}
	// 	if (typeof y === 'function') {
	// 		return y(d);
	// 	}
	// 	const scale = chart.yScale;
	// 	if ('bandwidth' in scale) {
	// 		// ScaleBand
	// 		const pos = (scale as ScaleBand<string>)(String(d.y));
	// 		return pos !== undefined ? pos + (scale as ScaleBand<string>).bandwidth() / 2 : 0;
	// 	} else {
	// 		// ScaleLinear
	// 		return (scale as ScaleLinear<number, number>)(d.y);
	// 	}
	// }

	// function getValue<T>(value: T | ((d: DataPoint) => T), d: DataPoint): T {
	// 	return typeof value === 'function' ? (value as (d: DataPoint) => T)(d) : value;
	// }
</script>

<!-- {#each data as d} -->
	<circle
		cx={x}
		cy={y}
		r={r}
		fill={fill}
		stroke={stroke}
		stroke-width={strokeWidth}
		opacity={opacity}
		class={className}
	/>
<!-- {/each} -->
