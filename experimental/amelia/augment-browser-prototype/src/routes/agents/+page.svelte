<script lang="ts">
	import { goto } from '$app/navigation';
	import { page } from '$app/state';
	import { agents, agentsError, shouldShowAgentSkeleton } from '$lib/stores/global-state.svelte';
	import { loadAgents, initializeSession, loadEntities } from '$lib/stores/data-operations.svelte';
	import RunListHorizontal from '$lib/components/ui/data-display/RunListHorizontal.svelte';
	import RemoteAgentRow from '$lib/components/ui/data-display/RemoteAgentRow.svelte';
	import Button from '$lib/components/ui/navigation/Button.svelte';
	import RemoteAgentDetailDrawer from '$lib/components/ui/overlays/RemoteAgentDetailDrawer.svelte';
	import Input from '$lib/components/ui/forms/Input.svelte';
	import { Plus, MagnifyingGlass } from 'svelte-hero-icons';
	import { fly } from 'svelte/transition';
	import { type Snippet, onMount, onDestroy } from 'svelte';
	import type { CleanRemoteAgent } from '$lib/api/unified-client';
	import GitHubIntegrationPrompt from '$lib/components/ui/GitHubIntegrationPrompt.svelte';
	import { getProviderAuthStatus } from '$lib/stores/provider-auth';
	import {
		// updateAgentsStreaming, // DISABLED: No longer streaming agent history on /agents page
		cleanupAgentsPageStreaming
	} from '$lib/stores/agents-page-streaming.svelte';

	let { children }: { children?: Snippet } = $props();

	let agentId = $derived(page.params.taskId); // Keep same param name for URL compatibility

	// GitHub authentication status
	const githubAuthStatus = getProviderAuthStatus('github');
	let isGitHubConnected = $derived($githubAuthStatus?.isConfigured || false);

	// Filter state
	let filters = $state({
		search: '',
		status: '',
		assignee: '',
		hasAgent: '',
		repository: ''
	});

	// Mobile search states
	let agentsSearch = $state('');

	// Agent detail drawer state (for desktop only)
	let showAgentDrawer = $state(false);
	let selectedAgent = $state<CleanRemoteAgent | null>(null);

	// Load data on mount
	// Note: initializeSession() is now called globally in +layout.svelte
	onMount(async () => {
		// Load chat history for agents to show meaningful previews
		// const agentsData = $agents;
		// if (agentsData && agentsData.length > 0) {
		// 	const agentIds = agentsData.map((agent) => agent.id);
		// 	loadAgentPreviews(agentIds); // Don't await - let this load in background
		// }
		await initializeSession();
		// await loadEntities();

		// Note: loadTriggers() (called by initializeSession and loadEntities)
		// automatically loads executions for all triggers, so no manual loading needed
		console.log('Triggers and executions loaded via initializeSession/loadEntities');
	});

	// Cleanup streaming subscriptions on unmount
	onDestroy(() => {
		cleanupAgentsPageStreaming();
	});

	// Get reactive data from global state
	let agentsArray = $derived($agents);
	let shouldShowLoading = $derived($shouldShowAgentSkeleton);

	// Filter agents
	let filteredAgents = $derived.by(() => {
		if (!agentsArray) return [];

		return agentsArray.filter((agent) => {
			if (!agentsSearch) return true;
			const searchLower = agentsSearch.toLowerCase();
			return (
				agent.title?.toLowerCase().includes(searchLower) ||
				agent.id?.toLowerCase().includes(searchLower)
			);
		});
	});

	// Update streaming subscriptions when agents change
	// DISABLED: This was causing backend to remove has_updates fields
	// Agent history streaming is now only enabled for:
	// 1. Agent detail drawer (handled by RemoteAgentDetailDrawer component)
	// 2. Individual agent pages (handled by /agents/[taskId]/+page.svelte)
	// Agent list streaming (for status updates) continues to work via startAgentListStream()
	// $effect(() => {
	// 	if (filteredAgents.length > 0) {
	// 		updateAgentsStreaming(filteredAgents);
	// 	}
	// });

	// Load chat history for new agents when the agents list changes
	// $effect(() => {
	// 	const agentsData = $agents;
	// 	if (agentsData && agentsData.length > 0) {
	// 		const agentIds = agentsData.map((agent) => agent.id);
	// 		loadAgentPreviews(agentIds); // Load in background
	// 	}
	// });

	function handleAgentClick(agent: CleanRemoteAgent) {
		// Desktop: Open drawer, Mobile: Navigate to full view
		if (window.innerWidth >= 1024) {
			// lg breakpoint
			selectedAgent = agent;
			showAgentDrawer = true;
		} else {
			goto(`/agents/${agent.id}`);
		}
	}

	function handleDrawerClose() {
		showAgentDrawer = false;
		selectedAgent = null;
	}

	function handleFilterChange(key: string, value: string) {
		filters[key as keyof typeof filters] = value;
	}

	function clearAllFilters() {
		filters.search = '';
		filters.status = '';
		filters.assignee = '';
		filters.hasAgent = '';
		filters.repository = '';
	}

	function handleStartNewAgent() {
		goto('/agents/create');
	}

	function handleGitHubIntegrationComplete() {
		// Reload the page to refresh authentication status and load triggers
		window.location.reload();
	}
</script>

<svelte:head>
	<title>Agents - Augment Code</title>
</svelte:head>

{#if agentId}
	<!-- Detail view for specific agent -->
	{@render children?.()}
{:else if !isGitHubConnected}
	<!-- GitHub Integration Guard -->
	<GitHubIntegrationPrompt
		projectTitle="the Augment dashboard"
		onIntegrationComplete={handleGitHubIntegrationComplete}
	/>
{:else}
	<!-- Unified responsive layout -->
	<div class="h-screen w-full overflow-auto">
		{#if $agentsError}
			<div class="flex h-full items-center justify-center py-12">
				<div class="mx-auto max-w-md text-center">
					<div class="mb-4">
						<div
							class="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-red-100 dark:bg-red-900/20"
						>
							<svg
								class="h-8 w-8 text-red-600 dark:text-red-400"
								fill="none"
								stroke="currentColor"
								viewBox="0 0 24 24"
							>
								<path
									stroke-linecap="round"
									stroke-linejoin="round"
									stroke-width="2"
									d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
								/>
							</svg>
						</div>
						<h3 class="mb-2 text-lg font-semibold text-slate-900 dark:text-white">
							Unable to load agents
						</h3>
						<p class="mb-6 text-sm text-slate-600 dark:text-slate-400">{$agentsError}</p>
					</div>
					<div class="space-y-3">
						<Button variant="primary" size="sm" onclick={() => loadAgents(true)} class="w-full">
							Try again
						</Button>
						<Button variant="secondary" size="sm" onclick={handleStartNewAgent} class="w-full">
							Start new agent
						</Button>
					</div>
				</div>
			</div>
		{:else}
			<!-- Desktop: Horizontal layout -->
			<div class="hidden h-full lg:block">
				<RunListHorizontal
					agents={agentsArray}
					isLoading={shouldShowLoading}
					{filters}
					onAgentClick={handleAgentClick}
					onFilterChange={handleFilterChange}
					onClearFilters={clearAllFilters}
				/>
			</div>

			<!-- Mobile: Responsive layout -->
			<div class="flex h-full flex-col bg-white lg:hidden dark:bg-slate-900">
				<!-- Header with Start New Agent Button -->
				<div class="flex-shrink-0 p-4 pt-12">
					<div class="flex items-center justify-between">
						<h1 class="text-2xl font-semibold text-slate-900 dark:text-white">Remote Agents</h1>
						<Button
							variant="primary"
							size="sm"
							icon={Plus}
							onclick={handleStartNewAgent}
							class="flex-shrink-0"
						>
							Start new agent
						</Button>
					</div>
				</div>

				<!-- Agents Content -->
				<div class="flex-1 overflow-auto pt-3">
					<div class="mb-4 flex flex-col gap-1 px-2.5 sm:flex-row sm:items-center md:px-5">
						<div class="flex-1"></div>
						<!-- Search -->
						<Input
							bind:value={agentsSearch}
							placeholder="Search agents..."
							class="w-full"
							inputClass="pl-8"
							icon={MagnifyingGlass}
						/>
					</div>

					{#if shouldShowLoading}
						<div class="">
							{#each Array(5) as _}
								<div class="h-16 animate-pulse rounded-lg bg-slate-100 dark:bg-slate-800"></div>
							{/each}
						</div>
					{:else if filteredAgents.length === 0}
						<div class="py-8 text-center">
							<p class="text-slate-500 dark:text-slate-400">No agents found</p>
						</div>
					{:else}
						<div class="">
							{#each filteredAgents as agent (agent.id)}
								<div transition:fly={{ y: 20, duration: 200 }}>
									<RemoteAgentRow {agent} onAgentClick={() => handleAgentClick(agent)} />
								</div>
							{/each}
						</div>
					{/if}
				</div>
			</div>
		{/if}
	</div>
{/if}

<!-- Agent Detail Drawer (Desktop only) -->
{#if showAgentDrawer && selectedAgent}
	<RemoteAgentDetailDrawer
		open={showAgentDrawer}
		agent={selectedAgent}
		onClose={handleDrawerClose}
		onAgentDeleted={handleDrawerClose}
	/>
{/if}
