import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import { augmentSession, addSessionCookie, type AugmentSession } from '$lib/server/session';

interface TokenRequest {
	code: string;
	tenantUrl: string;
	state?: string;
	codeVerifier?: string;
	clientId?: string;
	redirectUri?: string;
}

interface TokenResponse {
	access_token: string;
	token_type: string;
	expires_in?: number;
	refresh_token?: string;
	scope?: string;
}

export const POST: RequestHandler = async ({ request }) => {
	try {
		const { code, tenantUrl, state, codeVerifier, clientId, redirectUri }: TokenRequest =
			await request.json();

		if (!code) {
			return json({ error: 'Missing authorization code' }, { status: 400 });
		}

		if (!tenantUrl) {
			return json({ error: 'Missing tenant URL' }, { status: 400 });
		}

		// Validate tenant URL
		try {
			const url = new URL(tenantUrl);
			if (!url.hostname.endsWith('.augmentcode.com')) {
				return json({ error: 'Invalid tenant URL' }, { status: 400 });
			}
		} catch {
			return json({ error: 'Invalid tenant URL format' }, { status: 400 });
		}

		// Try different token endpoints to get the shorter token format
		// The auth URL might be different from tenant URL for token exchange
		const authUrlFromSettings = request.headers.get('x-auth-url'); // We'll pass this from client

		// Default auth URLs if not provided
		const defaultAuthUrl = 'https://auth-staging.augmentcode.com';
		const authUrl = authUrlFromSettings || defaultAuthUrl;

		const tokenEndpoints = [
			// Try tenant URL endpoints first (these might give API-compatible tokens)
			`${tenantUrl}token`,
			`${tenantUrl}api/token`,
			`${tenantUrl}oauth/token`,
			// Then try auth URL endpoints (these are the standard OAuth token endpoints)
			`${authUrl}/token`,
			`${authUrl}/oauth/token`,
			`${authUrl}/api/token`,
			`${authUrl}/api/oauth/token`
		];

		let tokenResponse: TokenResponse | null = null;
		let lastError: string | null = null;

		// Try each endpoint until one works
		for (const tokenUrl of tokenEndpoints) {
			const body = new URLSearchParams({
				grant_type: 'authorization_code',
				client_id: clientId || 'augment-web-ui',
				code,
				redirect_uri: redirectUri || `${new URL(request.url).origin}/auth/callback`
			});

			// Add code_verifier for PKCE if provided
			if (codeVerifier) {
				body.append('code_verifier', codeVerifier);
			}

			try {
				// Prepare headers - OAuth token endpoints typically don't require authentication
				const headers: Record<string, string> = {
					'Content-Type': 'application/x-www-form-urlencoded',
					Accept: 'application/json',
					'User-Agent': 'Augment-Web-Client/1.0'
				};

				const response = await fetch(tokenUrl, {
					method: 'POST',
					headers,
					body: body.toString()
				});

				if (response.ok) {
					tokenResponse = await response.json();
					break; // Success, exit the loop
				} else {
					const errorText = await response.text();
					lastError = `${response.status}: ${errorText}`;
					continue; // Try next endpoint
				}
			} catch (error) {
				lastError = error instanceof Error ? error.message : 'Unknown error';
				continue; // Try next endpoint
			}
		}

		// If no endpoint worked, return error
		if (!tokenResponse) {
			console.error('All token endpoints failed. Last error:', lastError);
			return json(
				{
					error: 'Token exchange failed',
					details: `All endpoints failed. Last error: ${lastError}`
				},
				{ status: 500 }
			);
		}

		// Create session object
		const session: AugmentSession = {
			accessToken: tokenResponse.access_token,
			tenantUrl,
			scopes: tokenResponse.scope || '',
			expiresAt: tokenResponse.expires_in
				? Date.now() + tokenResponse.expires_in * 1000
				: Date.now() + 7 * 24 * 60 * 60 * 1000, // 7 days default (SOC2 compliance)
			refreshToken: tokenResponse.refresh_token
		};

		// Set secure session cookie
		const headers = new Headers();
		const sessionCookie = augmentSession.set(session);
		addSessionCookie(headers, sessionCookie);

		// Return success response without exposing tokens to client
		return json(
			{
				success: true,
				expiresAt: session.expiresAt,
				tenantUrl: session.tenantUrl,
				scopes: session.scopes
			},
			{ headers }
		);
	} catch (error) {
		console.error('Token exchange error:', error);
		return json(
			{
				error: 'Internal server error',
				details: error instanceof Error ? error.message : 'Unknown error'
			},
			{ status: 500 }
		);
	}
};
