import { json } from '@sveltejs/kit';
import type { <PERSON>quest<PERSON>and<PERSON> } from './$types';
import { getSessionData } from '$lib/utils/session-utils';
import { convertBackendEntityToUnified } from '$lib/utils/entity-conversion';

export const POST: RequestHandler = async ({ request, cookies }) => {
	try {
		// Get session data from secure cookies
		const sessionData = await getSessionData(cookies);
		if (!sessionData?.tenantUrl || !sessionData?.accessToken) {
			return json({ error: 'Missing session data - please log in again' }, { status: 401 });
		}

		const { tenantUrl, accessToken } = sessionData;

		// Make direct API call to tenant backend
		const cleanTenantUrl = tenantUrl.endsWith('/') ? tenantUrl.slice(0, -1) : tenantUrl;
		const backendEndpoint = `${cleanTenantUrl}/remote-agent-actions/triggers/matching-entities`;

		const apiBody = await request.json();

		console.log('Making direct request to triggers matching-entities:', backendEndpoint);
		console.log('Request body:', JSON.stringify(apiBody, null, 2));

		const response = await fetch(backendEndpoint, {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${accessToken}`,
				Accept: 'application/json',
				'User-Agent': 'Augment-Web-Client/1.0'
			},
			body: JSON.stringify(apiBody)
		});

		console.log('Matching entities response status:', response.status);

		if (!response.ok) {
			const errorText = await response.text();
			console.error('Backend error fetching matching entities:', {
				status: response.status,
				statusText: response.statusText,
				body: errorText
			});
			return json(
				{ error: `Backend error: ${response.statusText}`, details: errorText },
				{ status: response.status }
			);
		}

		const data = await response.json();

		// Convert and combine all entities into a single array for the client
		const allEntities = [];

		// Convert GitHub entities
		if (data.github_entities) {
			for (const entity of data.github_entities) {
				// Detect entity type based on structure
				let entityType = 'pull_request'; // default
				if (entity.workflow_run || entity.workflowRun) {
					entityType = 'workflow_run';
				} else if (entity.pull_request || entity.pullRequest) {
					entityType = 'pull_request';
				}

				const converted = convertBackendEntityToUnified('github', entityType, entity);
				if (converted) allEntities.push(converted);
			}
		}

		// Convert Linear entities
		if (data.linear_entities) {
			for (const entity of data.linear_entities) {
				const converted = convertBackendEntityToUnified('linear', 'issue', entity);
				if (converted) allEntities.push(converted);
			}
		}

		// Handle legacy entities field
		if (data.entities) {
			for (const entity of data.entities) {
				// Detect entity type based on structure
				let entityType = 'pull_request'; // default
				if (entity.workflow_run || entity.workflowRun) {
					entityType = 'workflow_run';
				} else if (entity.pull_request || entity.pullRequest) {
					entityType = 'pull_request';
				}

				const converted = convertBackendEntityToUnified('github', entityType, entity);
				if (converted) allEntities.push(converted);
			}
		}

		// Remove duplicates based on entity ID
		const uniqueEntities = allEntities.filter(
			(entity, index, self) => index === self.findIndex((e) => e.id === entity.id)
		);

		console.log('Returning unified entities:', uniqueEntities.length);

		// Return the unified entities array that the client expects
		return json(uniqueEntities);
	} catch (error) {
		console.error('Error fetching matching entities:', error);
		return json({ error: 'Internal server error' }, { status: 500 });
	}
};
