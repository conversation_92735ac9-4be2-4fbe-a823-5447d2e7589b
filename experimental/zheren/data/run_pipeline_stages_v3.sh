#!/bin/bash
# Run the Vanguard to Binks pipeline stages individually
# This version includes tenant splitting for better parallelization

set -e  # Exit on error

# Configuration
WORK_DIR="/tmp/vanguard_to_binks_pipeline"
MODE="local"  # or "ray" for distributed
REQUEST_TYPE="CHAT"  # or "CODEBASE_RETRIEVAL"
TENANTS=""  # Space-separated list of tenants, or empty for all

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --work-dir)
            WORK_DIR="$2"
            shift 2
            ;;
        --mode)
            MODE="$2"
            shift 2
            ;;
        --request-type)
            REQUEST_TYPE="$2"
            shift 2
            ;;
        --tenants)
            TENANTS="$2"
            shift 2
            ;;
        *)
            echo "Unknown option: $1"
            exit 1
            ;;
    esac
done

echo "Pipeline configuration:"
echo "  Work directory: $WORK_DIR"
echo "  Mode: $MODE"
echo "  Request type: $REQUEST_TYPE"
echo "  Tenants: ${TENANTS:-all}"

# Create work directory
mkdir -p $WORK_DIR

# Stage 0: Create initial configuration
echo "Creating initial configuration..."
if [ -z "$TENANTS" ]; then
    python experimental/zheren/data/create_initial_config.py \
        --output $WORK_DIR/initial_config.jsonl \
        --request-type $REQUEST_TYPE
else
    python experimental/zheren/data/create_initial_config.py \
        --output $WORK_DIR/initial_config.jsonl \
        --request-type $REQUEST_TYPE \
        --tenants $TENANTS
fi

# Stage 0.5: Split by tenant for parallelization
echo "Splitting configuration by tenant..."
python experimental/zheren/data/tenant_split_stage.py \
    --input $WORK_DIR/initial_config.jsonl \
    --output $WORK_DIR/stage0_split \
    --mode $MODE

# Stage 1: Request Fetch (BigQuery + GCS) - Now parallelized by tenant
echo "Running request fetch stage..."
python experimental/zheren/data/request_fetch_stage.py \
    --input $WORK_DIR/stage0_split \
    --output $WORK_DIR/stage1_fetched \
    --mode $MODE \
    --num-workers 16  # Can now use multiple workers effectively

# Check if we need classification
if [ "$REQUEST_TYPE" = "CODEBASE_RETRIEVAL" ]; then
    echo "Skipping classification stage for CODEBASE_RETRIEVAL requests..."
    # Create symlink or copy for consistency
    ln -sf $WORK_DIR/stage1_fetched $WORK_DIR/stage2_classified
else
    # Stage 2: Classification (only for CHAT requests)
    echo "Running classification stage..."
    python experimental/zheren/data/classification_stage_v2.py \
        --input $WORK_DIR/stage1_fetched \
        --output $WORK_DIR/stage2_classified \
        --mode $MODE \
        --num-workers 4
fi

# Stage 3: Gemini Classification
echo "Running Gemini classification stage..."
python experimental/zheren/data/gemini_classification_stage.py \
    --input $WORK_DIR/stage2_classified \
    --output $WORK_DIR/stage3_gemini \
    --mode $MODE \
    --num-workers 8

# Stage 4: Blob Fetch and Process
echo "Running blob fetch and process stage..."
python experimental/zheren/data/blob_fetch_and_process_stage.py \
    --input $WORK_DIR/stage3_gemini \
    --output $WORK_DIR/stage4_processed \
    --mode $MODE \
    --num-workers 16

# Stage 5: Silver File Selection
echo "Running silver file selection stage..."
python experimental/zheren/data/silver_file_selection_stage.py \
    --input $WORK_DIR/stage4_processed \
    --output $WORK_DIR/stage5_silver \
    --mode $MODE \
    --num-workers 8

# Stage 6: Repository Assembly
echo "Running repository assembly stage..."
python experimental/zheren/data/repository_assembly_stage.py \
    --input $WORK_DIR/stage5_silver \
    --output $WORK_DIR/stage6_final \
    --mode $MODE \
    --num-workers 8

echo "Pipeline complete! Output written to $WORK_DIR/stage6_final/"
echo "Final output files:"
ls -la $WORK_DIR/stage6_final/
