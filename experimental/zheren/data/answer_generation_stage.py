#!/usr/bin/env python3
"""Answer generation stage for Vanguard to Binks pipeline.

This stage generates answers for queries using the selected silver files.
Takes Repository objects as input and outputs Repository objects with populated answers.
"""

import argparse
import logging
from pathlib import Path

from research.data.ray.ray_utils import <PERSON><PERSON><PERSON><PERSON>

from experimental.zheren.data.vanguard_to_binks_answer_generation_actor import (
    AnswerGenerationActor,
)

logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


def main():
    """Main function for answer generation stage."""
    parser = argparse.ArgumentParser(
        description="Answer generation stage for Vanguard to Binks pipeline"
    )

    # Input/Output arguments
    parser.add_argument(
        "--input",
        type=str,
        required=True,
        help="Input path (JSONL file or directory of JSONL files with Repository objects)",
    )
    parser.add_argument(
        "--output",
        type=str,
        required=True,
        help="Output path (JSONL file or directory)",
    )

    # Model configuration
    parser.add_argument(
        "--model-name",
        type=str,
        default="gemini-2.5-flash-lite",
        help="Gemini model name to use for answer generation",
    )
    parser.add_argument(
        "--temperature",
        type=float,
        default=0.7,
        help="Temperature for answer generation (default: 0.7)",
    )
    parser.add_argument(
        "--max-output-tokens",
        type=int,
        default=1024,
        help="Maximum output tokens for answer generation (default: 1024)",
    )

    # Ray configuration
    parser.add_argument(
        "--mode",
        type=str,
        default="local",
        choices=["local", "ray"],
        help="Execution mode: local (for testing) or ray (distributed)",
    )
    parser.add_argument(
        "--num-workers",
        type=int,
        default=8,
        help="Number of answer generation workers (default: 8)",
    )
    parser.add_argument(
        "--num-cpu-per-worker",
        type=int,
        default=2,
        help="Number of CPUs per worker (default: 2)",
    )

    # Logging configuration
    parser.add_argument(
        "--unanswerable-log-dir",
        type=str,
        default=None,
        help="Directory to log unanswerable queries for analysis",
    )
    parser.add_argument(
        "--log-file-contents",
        action="store_true",
        default=True,
        help="Include file contents in unanswerable query logs",
    )
    parser.add_argument(
        "--max-content-chars-per-file",
        type=int,
        default=2000,
        help="Maximum characters per file to include in logs (default: 2000)",
    )

    # GCP configuration
    parser.add_argument(
        "--project-id",
        type=str,
        default="augment-research-gsc",
        help="GCP project ID for Vertex AI (default: augment-research-gsc)",
    )

    args = parser.parse_args()

    # Create output directory if needed
    output_path = Path(args.output)
    if not output_path.exists():
        output_path.mkdir(parents=True, exist_ok=True)

    logger.info(f"Running answer generation stage in {args.mode} mode")
    logger.info(f"Input: {args.input}")
    logger.info(f"Output: {args.output}")
    logger.info(f"Model: {args.model_name}")
    logger.info(f"Temperature: {args.temperature}")
    logger.info(f"Max output tokens: {args.max_output_tokens}")

    # Run the stage
    with RayRunner(
        actor_cls=AnswerGenerationActor,
        actor_args={
            "model_name": args.model_name,
            "temperature": args.temperature,
            "max_output_tokens": args.max_output_tokens,
            "project_id": args.project_id,
            "unanswerable_log_dir": args.unanswerable_log_dir,
            "log_file_contents": args.log_file_contents,
            "max_content_chars_per_file": args.max_content_chars_per_file,
        },
        num_workers=args.num_workers,
        num_cpu_per_worker=args.num_cpu_per_worker,
        num_gpu_per_worker=0,
        local=args.mode == "local",
    ) as runner:
        runner.process_jsonl(args.input, args.output)

    # Count output records
    if args.mode == "local":
        total_count = 0
        for output_file in output_path.glob("*.jsonl"):
            with open(output_file, "r") as f:
                count = sum(1 for _ in f)
                total_count += count
        logger.info(f"Answer generation stage produced {total_count} records")


if __name__ == "__main__":
    main()
