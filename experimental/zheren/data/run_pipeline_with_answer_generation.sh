#!/bin/bash
# Run the Vanguard to Binks pipeline with answer generation
# This version includes answer generation before repository assembly

set -e  # Exit on error

# Configuration
WORK_DIR="/tmp/vanguard_to_binks_pipeline_with_answers"
MODE="local"  # or "ray" for distributed
REQUEST_TYPE="CHAT"  # or "CODEBASE_RETRIEVAL"
TENANTS=""  # Space-separated list of tenants, or empty for all
ANSWER_MODEL="gemini-2.0-flash-exp"  # Gemini 2.5 Flash
ANSWER_TEMPERATURE="0.7"
ANSWER_MAX_TOKENS="1024"

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --work-dir)
            WORK_DIR="$2"
            shift 2
            ;;
        --mode)
            MODE="$2"
            shift 2
            ;;
        --request-type)
            REQUEST_TYPE="$2"
            shift 2
            ;;
        --tenants)
            TENANTS="$2"
            shift 2
            ;;
        --answer-model)
            ANSWER_MODEL="$2"
            shift 2
            ;;
        --answer-temperature)
            ANSWER_TEMPERATURE="$2"
            shift 2
            ;;
        --answer-max-tokens)
            ANSWER_MAX_TOKENS="$2"
            shift 2
            ;;
        *)
            echo "Unknown option: $1"
            exit 1
            ;;
    esac
done

echo "Pipeline configuration:"
echo "  Work directory: $WORK_DIR"
echo "  Mode: $MODE"
echo "  Request type: $REQUEST_TYPE"
echo "  Tenants: ${TENANTS:-all}"
echo "  Answer model: $ANSWER_MODEL"
echo "  Answer temperature: $ANSWER_TEMPERATURE"
echo "  Answer max tokens: $ANSWER_MAX_TOKENS"

# Create work directory
mkdir -p $WORK_DIR

# Stage 0: Create initial configuration
echo "Creating initial configuration..."
if [ -z "$TENANTS" ]; then
    python experimental/zheren/data/create_initial_config.py \
        --output $WORK_DIR/initial_config.jsonl \
        --request-type $REQUEST_TYPE
else
    python experimental/zheren/data/create_initial_config.py \
        --output $WORK_DIR/initial_config.jsonl \
        --request-type $REQUEST_TYPE \
        --tenants $TENANTS
fi

# Stage 0.5: Split by tenant for parallelization
echo "Splitting configuration by tenant..."
python experimental/zheren/data/tenant_split_stage.py \
    --input $WORK_DIR/initial_config.jsonl \
    --output $WORK_DIR/stage0_split \
    --mode $MODE

# Stage 1: Request Fetch (BigQuery + GCS) - Now parallelized by tenant
echo "Running request fetch stage..."
python experimental/zheren/data/request_fetch_stage.py \
    --input $WORK_DIR/stage0_split \
    --output $WORK_DIR/stage1_fetched \
    --mode $MODE \
    --num-workers 16  # Can now use multiple workers effectively

# Check if we need classification
if [ "$REQUEST_TYPE" = "CODEBASE_RETRIEVAL" ]; then
    echo "Skipping classification stage for CODEBASE_RETRIEVAL requests..."
    # Create symlink or copy for consistency
    ln -sf $WORK_DIR/stage1_fetched $WORK_DIR/stage2_classified
else
    # Stage 2: Classification (only for CHAT requests)
    echo "Running classification stage..."
    python experimental/zheren/data/classification_stage_v2.py \
        --input $WORK_DIR/stage1_fetched \
        --output $WORK_DIR/stage2_classified \
        --mode $MODE \
        --num-workers 4
fi

# Stage 3: Gemini Classification
echo "Running Gemini classification stage..."
python experimental/zheren/data/gemini_classification_stage.py \
    --input $WORK_DIR/stage2_classified \
    --output $WORK_DIR/stage3_gemini \
    --mode $MODE \
    --num-workers 8

# Stage 4: Blob Fetch and Process
echo "Running blob fetch and process stage..."
python experimental/zheren/data/blob_fetch_and_process_stage.py \
    --input $WORK_DIR/stage3_gemini \
    --output $WORK_DIR/stage4_processed \
    --mode $MODE \
    --num-workers 16

# Stage 5: Silver File Selection
echo "Running silver file selection stage..."
python experimental/zheren/data/silver_file_selection_stage.py \
    --input $WORK_DIR/stage4_processed \
    --output $WORK_DIR/stage5_silver \
    --mode $MODE \
    --num-workers 8

# Stage 6: Repository Assembly
echo "Running repository assembly stage..."
python experimental/zheren/data/repository_assembly_stage.py \
    --input $WORK_DIR/stage5_silver \
    --output $WORK_DIR/stage6_repositories \
    --mode $MODE \
    --num-workers 8

# Stage 7: Answer Generation (NEW - operates on Repository objects)
echo "Running answer generation stage..."
python experimental/zheren/data/answer_generation_stage.py \
    --input $WORK_DIR/stage6_repositories \
    --output $WORK_DIR/stage7_final \
    --mode $MODE \
    --num-workers 8 \
    --model-name $ANSWER_MODEL \
    --temperature $ANSWER_TEMPERATURE \
    --max-output-tokens $ANSWER_MAX_TOKENS \
    --unanswerable-log-dir $WORK_DIR/unanswerable_queries

echo "Pipeline complete! Output written to $WORK_DIR/stage7_final/"
echo "Final output files:"
ls -la $WORK_DIR/stage7_final/

# Count answerable vs unanswerable queries
echo ""
echo "Answer statistics:"
if [ -f "$WORK_DIR/stage7_final/part-00000.jsonl" ]; then
    TOTAL=$(cat $WORK_DIR/stage7_final/*.jsonl | jq -r '.documents_with_questions[].answer' | wc -l)
    UNANSWERABLE=$(cat $WORK_DIR/stage7_final/*.jsonl | jq -r '.documents_with_questions[].answer' | grep -c "^\[UNANSWERABLE\]" || true)
    ANSWERABLE=$((TOTAL - UNANSWERABLE))
    echo "  Total queries: $TOTAL"
    echo "  Answerable: $ANSWERABLE"
    echo "  Unanswerable: $UNANSWERABLE"

    # Show sample of unanswerable queries
    if [ $UNANSWERABLE -gt 0 ]; then
        echo ""
        echo "Sample unanswerable queries:"
        cat $WORK_DIR/stage7_final/*.jsonl | jq -r '.documents_with_questions[] | select(.answer | startswith("[UNANSWERABLE]")) | .question' | head -5
    fi
fi
