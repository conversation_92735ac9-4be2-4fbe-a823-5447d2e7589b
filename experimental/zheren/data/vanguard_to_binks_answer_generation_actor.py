#!/usr/bin/env python3
"""Answer generation actor for Vanguard to Binks pipeline.

This actor generates answers for queries using the selected silver files,
similar to the synthetic dataset generation but with handling for
potentially unanswerable queries.
"""

import logging
from typing import Optional, Dict, List
import json
from pathlib import Path
from datetime import datetime
import vertex<PERSON>
from vertexai.generative_models import GenerativeModel

from research.data.ray.ray_utils import AbstractRayActor

from experimental.tongfei.data.binks_schemas import (
    Repository,
    File,
    DocumentWithQuestionsV2,
)

logger = logging.getLogger(__name__)

# Special token to indicate unanswerable queries
UNANSWERABLE_TOKEN = "<[UNANSWERABLE]>"

# Prompt template for answer generation
ANSWER_GENERATION_PROMPT = """\
Below is the content from a codebase that might be relevant to answering a question.
Please read the content carefully and determine if you can answer the question based on the provided files.

### Files
{file_contents}

---

### Query: {question}

Instructions:
1. If the provided files contain sufficient information to answer the question, provide an answer that is about 100 words.
2. Cite individual files in your answer using the FULL paths when referencing specific information.
3. If the provided files do NOT contain sufficient information to answer the question, start your response with "{unanswerable_token}" followed by a brief explanation of what information is missing.

Answer:"""


class AnswerGenerationActor(AbstractRayActor[Repository, Repository]):
    """Generates answers for queries using selected silver files.

    This actor uses Gemini API to generate answers based on the silver files
    selected by the retrieval system. It handles cases where queries might
    not be answerable with the given files.

    This actor takes Repository objects as input and updates the answer field
    in DocumentWithQuestionsV2 objects.
    """

    def __init__(
        self,
        model_name: str = "gemini-2.5-flash",
        temperature: float = 0.7,
        max_output_tokens: int = 1024,
        project_id: str = "augment-research-gsc",
        location: str = "us-central1",
        unanswerable_log_dir: Optional[str] = None,
        log_file_contents: bool = True,
        max_content_chars_per_file: int = 2000,
    ):
        """Initialize the answer generation actor.

        Args:
            model_name: Gemini model name to use
            temperature: Temperature for generation
            max_output_tokens: Maximum tokens in the output
            project_id: GCP project ID
            location: GCP location
            unanswerable_log_dir: Optional directory to log unanswerable queries
            log_file_contents: Whether to include file contents in unanswerable logs
            max_content_chars_per_file: Max characters per file to include in logs
        """
        super().__init__(
            input_cls=Repository,
            output_cls=Repository,
        )

        self.model_name = model_name
        self.temperature = temperature
        self.max_output_tokens = max_output_tokens

        # Initialize Vertex AI
        vertexai.init(project=project_id, location=location)

        # Initialize Gemini model
        self.model = GenerativeModel(model_name)

        # Setup unanswerable query tracking
        self.unanswerable_log_dir = unanswerable_log_dir
        self.log_file_contents = log_file_contents
        self.max_content_chars_per_file = max_content_chars_per_file
        self.unanswerable_queries: List[Dict] = []

        if self.unanswerable_log_dir:
            Path(self.unanswerable_log_dir).mkdir(parents=True, exist_ok=True)
            logger.info(
                f"Unanswerable queries will be logged to: {self.unanswerable_log_dir}"
            )

        logger.info(
            f"Initialized AnswerGenerationActor with model={model_name}, "
            f"temperature={temperature}, max_output_tokens={max_output_tokens}"
        )

    def _get_file_contents_for_paths(
        self, file_list: list[File], paths: list[str]
    ) -> tuple[list[File], str]:
        """Get the content of files matching the given paths.

        Args:
            file_list: List of all files in the repository
            paths: List of file paths to retrieve content for

        Returns:
            Tuple of (matching_files, formatted_content)
        """
        # Create a mapping from path to file
        path_to_file = {f.max_stars_repo_path: f for f in file_list}

        # Get matching files
        matching_files = []
        for path in paths:
            if path in path_to_file:
                matching_files.append(path_to_file[path])
            else:
                logger.warning(f"Path {path} not found in file list")

        # Format file contents using the specified format
        file_contents_parts = []
        for file in matching_files:
            file_contents_parts.append(
                f"##### {file.max_stars_repo_path}\n\n{file.content}"
            )

        file_contents = "\n\n".join(file_contents_parts)

        return matching_files, file_contents

    def _track_unanswerable_query(
        self,
        repo_name: str,
        question: str,
        answer: str,
        silver_file_paths: List[str],
        matching_files: Optional[List[File]] = None,
        reason: str = "unanswerable",
    ):
        """Track unanswerable queries for later analysis.

        Args:
            repo_name: Repository name
            question: The query that couldn't be answered
            answer: The full answer including explanation
            silver_file_paths: Paths that were selected by retrieval
            matching_files: Optional list of File objects that were found
            reason: Reason for being unanswerable
        """
        unanswerable_entry = {
            "timestamp": datetime.now().isoformat(),
            "repo_name": repo_name,
            "question": question,
            "answer": answer,
            "silver_file_paths": silver_file_paths,
            "num_silver_files": len(silver_file_paths),
            "reason": reason,
            "model": self.model_name,
            "temperature": self.temperature,
        }

        # Add file contents if requested and available
        if self.log_file_contents and matching_files:
            file_contents = []
            for file in matching_files:
                content = file.content
                # Truncate content if too long
                if len(content) > self.max_content_chars_per_file:
                    content = (
                        content[: self.max_content_chars_per_file] + "... [TRUNCATED]"
                    )

                file_contents.append(
                    {
                        "path": file.max_stars_repo_path,
                        "content": content,
                        "size": file.size,
                        "extension": file.ext,
                        "truncated": len(file.content)
                        > self.max_content_chars_per_file,
                    }
                )

            unanswerable_entry["file_contents"] = file_contents
            unanswerable_entry["num_files_with_content"] = len(file_contents)

        self.unanswerable_queries.append(unanswerable_entry)

        # If log directory is specified, write immediately
        if self.unanswerable_log_dir:
            log_file = (
                Path(self.unanswerable_log_dir)
                / f"unanswerable_queries_{datetime.now().strftime('%Y%m%d_%H%M%S')}.jsonl"
            )
            with open(log_file, "a") as f:
                f.write(json.dumps(unanswerable_entry) + "\n")

    def save_unanswerable_summary(self):
        """Save a summary of all unanswerable queries encountered."""
        if not self.unanswerable_queries:
            logger.info("No unanswerable queries to summarize")
            return

        summary = {
            "total_unanswerable": len(self.unanswerable_queries),
            "timestamp": datetime.now().isoformat(),
            "model": self.model_name,
            "temperature": self.temperature,
            "reasons": {},
            "queries": self.unanswerable_queries,
        }

        # Count reasons
        for query in self.unanswerable_queries:
            reason = query.get("reason", "unknown")
            summary["reasons"][reason] = summary["reasons"].get(reason, 0) + 1

        if self.unanswerable_log_dir:
            summary_file = (
                Path(self.unanswerable_log_dir)
                / f"unanswerable_summary_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            )
            with open(summary_file, "w") as f:
                json.dump(summary, f, indent=2)
            logger.info(f"Saved unanswerable query summary to: {summary_file}")
            logger.info(f"Total unanswerable queries: {summary['total_unanswerable']}")
            for reason, count in summary["reasons"].items():
                logger.info(f"  - {reason}: {count}")

    def process(self, row: Repository) -> list[Repository]:
        """Generate answer for the queries in the repository.

        Args:
            repository: Repository with DocumentWithQuestionsV2 objects

        Returns:
            List containing repository with updated answers
        """
        repository = row
        try:
            # Process each document with questions
            for doc in repository.documents_with_questions:
                if isinstance(doc, DocumentWithQuestionsV2):
                    # Get file contents for the paths in this document
                    matching_files, file_contents = self._get_file_contents_for_paths(
                        repository.file_list, doc.paths
                    )

                    if not matching_files:
                        logger.warning(
                            f"No matching files found for document in repo {repository.max_stars_repo_name}"
                        )
                        doc.answer = f"{UNANSWERABLE_TOKEN} No relevant files were found for this query."

                        # Track this case
                        self._track_unanswerable_query(
                            repo_name=repository.max_stars_repo_name,
                            question=doc.question,
                            answer=doc.answer,
                            silver_file_paths=doc.paths,
                            matching_files=None,  # No files found
                            reason="no_matching_files",
                        )
                        continue

                    # Create the prompt
                    prompt = ANSWER_GENERATION_PROMPT.format(
                        file_contents=file_contents,
                        question=doc.question,
                        unanswerable_token=UNANSWERABLE_TOKEN,
                    )

                    # Generate answer using Vertex AI
                    try:
                        response = self.model.generate_content(
                            prompt,
                            generation_config={
                                "temperature": self.temperature,
                                "max_output_tokens": self.max_output_tokens,
                            },
                        )

                        answer = response.text.strip()
                        is_answerable = not answer.startswith(UNANSWERABLE_TOKEN)

                        logger.info(
                            f"Generated answer for question in repo {repository.max_stars_repo_name} "
                            f"(answerable: {is_answerable}, length: {len(answer)} chars)"
                        )

                        # Track unanswerable queries
                        if not is_answerable:
                            self._track_unanswerable_query(
                                repo_name=repository.max_stars_repo_name,
                                question=doc.question,
                                answer=answer,
                                silver_file_paths=doc.paths,
                                matching_files=matching_files,
                                reason="model_determined_unanswerable",
                            )

                        # Update the answer in the document
                        doc.answer = answer

                    except Exception as e:
                        logger.error(
                            f"Failed to generate answer for repo {repository.max_stars_repo_name}: {str(e)} with query {doc.question}"
                        )
                        doc.answer = f"Error generating answer: {str(e)}"
                else:
                    logger.warning(
                        f"Skipping non-V2 document in repo {repository.max_stars_repo_name}"
                    )

            return [repository]

        except Exception as e:
            logger.error(
                f"Exception while processing repository {repository.max_stars_repo_name}: {str(e)}"
            )
            # Return the repository as-is with error in answers
            for doc in repository.documents_with_questions:
                if isinstance(doc, DocumentWithQuestionsV2) and not doc.answer:
                    doc.answer = f"Exception during answer generation: {str(e)}"
            return [repository]
