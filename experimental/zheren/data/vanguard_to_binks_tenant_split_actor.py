#!/usr/bin/env python3
"""Tenant splitting actor for parallel processing in the refactored pipeline.

This module provides an actor that splits a PipelineConfig into per-tenant
configs for parallel processing.
"""

import logging
from typing import Optional

from research.data.ray.ray_utils import AbstractRayActor
from base.datasets.tenants import DATASET_TENANTS
from experimental.zheren.data.vanguard_to_binks_modular_data_structures import (
    PipelineConfig,
)

logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


class TenantSplitActor(AbstractRayActor[PipelineConfig, PipelineConfig]):
    """Splits a PipelineConfig into per-tenant configs for parallel processing.

    This actor takes a PipelineConfig with multiple tenants and creates
    individual PipelineConfig objects for each tenant, enabling parallel
    processing across tenants.
    """

    def __init__(self):
        """Initialize the tenant splitter actor."""
        super().__init__(input_cls=PipelineConfig, output_cls=PipelineConfig)
        logger.info("Initialized TenantSplitActor")

    def process(self, row: PipelineConfig) -> list[PipelineConfig]:
        """Split the pipeline config into per-tenant configs.

        Args:
            row: PipelineConfig with potentially multiple tenant names

        Returns:
            List of PipelineConfig objects, one per tenant
        """
        tenant_names = row.tenant_names
        if not tenant_names:
            # Use all available tenants
            tenant_names = list(DATASET_TENANTS.keys())

        # Calculate per-tenant limit if a total limit is specified
        if row.limit:
            per_tenant_limit = row.limit // len(tenant_names)
            remainder = row.limit % len(tenant_names)
        else:
            per_tenant_limit = None
            remainder = 0

        configs = []
        for idx, tenant_name in enumerate(tenant_names):
            # Calculate this tenant's limit
            tenant_limit = per_tenant_limit
            if tenant_limit is not None and idx < remainder:
                tenant_limit += 1

            # Create per-tenant config
            tenant_config = PipelineConfig(
                tenant_names=[tenant_name],  # Single tenant
                date_from=row.date_from,
                date_to=row.date_to,
                request_type=row.request_type,
                limit=tenant_limit,
            )
            configs.append(tenant_config)

        logger.info(f"Split pipeline config into {len(configs)} per-tenant configs")
        return configs
