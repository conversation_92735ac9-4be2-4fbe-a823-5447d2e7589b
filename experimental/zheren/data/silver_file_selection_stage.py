#!/usr/bin/env python3
"""Silver file selection stage for Vanguard to Binks pipeline.

This stage uses retrieval to select the most relevant files for each request.
"""

import argparse
import logging
from pathlib import Path

from research.data.ray.ray_utils import <PERSON><PERSON><PERSON><PERSON>

from experimental.zheren.data.vanguard_to_binks_silver_file_selection_actor import (
    SilverFileSelectionActor,
)

logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


def main():
    """Main function for silver file selection stage."""
    parser = argparse.ArgumentParser(
        description="Silver file selection stage for Vanguard to Binks pipeline"
    )

    # Input/Output arguments
    parser.add_argument(
        "--input",
        type=str,
        required=True,
        help="Input directory containing processed requests",
    )
    parser.add_argument(
        "--output",
        type=str,
        required=True,
        help="Output directory for requests with selected silver files",
    )

    # Retriever configuration
    parser.add_argument(
        "--scorer-type",
        type=str,
        default="dense_scorer_v2_fbwd",
        help="Type of retriever scorer to use (default: dense_scorer_v2_fbwd)",
    )
    parser.add_argument(
        "--scorer-ckpt",
        type=str,
        default="/mnt/efs/augment/user/tongfei/hm/chatanol/consolidate_mp_model/InitCkpt=ethanol-qwen25coder-1b5&Tokenizer=qwen25coder/out",
        help="Path to retriever checkpoint",
    )
    parser.add_argument(
        "--tokenizer",
        type=str,
        default="qwen25coder",
        help="Tokenizer for retriever (default: qwen25coder)",
    )
    parser.add_argument(
        "--chunker-name",
        type=str,
        default="smart_line_level",
        help="Chunker type for retriever (default: smart_line_level)",
    )
    parser.add_argument(
        "--chunker-max-chunk-chars",
        type=int,
        default=768,
        help="Maximum characters per chunk (default: 768)",
    )
    parser.add_argument(
        "--chunker-max-headers",
        type=int,
        default=3,
        help="Maximum headers per chunk (default: 3)",
    )
    parser.add_argument(
        "--query-formatter",
        type=str,
        default="chatanol6",
        help="Query formatter for retriever",
    )
    parser.add_argument(
        "--document-formatter",
        type=str,
        default="ethanol6-embedding-with-path-key",
        help="Document formatter for retriever",
    )
    parser.add_argument(
        "--num-retrieved-chunks",
        type=int,
        default=128,
        help="Number of chunks to use for file selection (default: 128)",
    )
    parser.add_argument(
        "--num-retrieved-extra",
        type=int,
        default=128,
        help="Extra chunks to retrieve initially (default: 128)",
    )
    parser.add_argument(
        "--max-silver-files",
        type=int,
        default=128,
        help="Maximum number of silver files to select (default: 128)",
    )
    parser.add_argument(
        "--max-tokens",
        type=int,
        default=1024,
        help="Maximum tokens for retriever query (default: 1024)",
    )

    # Ray configuration
    parser.add_argument(
        "--mode",
        type=str,
        default="local",
        choices=["local", "ray"],
        help="Execution mode: local (for testing) or ray (distributed)",
    )
    parser.add_argument(
        "--num-workers",
        type=int,
        default=2,
        help="Number of silver file selection workers (default: 2)",
    )
    parser.add_argument(
        "--num-cpu-per-worker",
        type=int,
        default=4,
        help="Number of CPUs per worker (default: 4)",
    )
    parser.add_argument(
        "--num-gpu-per-worker",
        type=int,
        default=1,
        help="Number of GPUs per worker (default: 1)",
    )

    args = parser.parse_args()

    # Create output directory if needed
    output_dir = Path(args.output)
    if not output_dir.exists():
        output_dir.mkdir(parents=True, exist_ok=True)

    logger.info(f"Running silver file selection stage in {args.mode} mode")
    logger.info(f"Input: {args.input}")
    logger.info(f"Output: {args.output}")

    # Run the stage
    with RayRunner(
        actor_cls=SilverFileSelectionActor,
        actor_args={
            "scorer_type": args.scorer_type,
            "scorer_ckpt": args.scorer_ckpt,
            "tokenizer": args.tokenizer,
            "chunker_name": args.chunker_name,
            "chunker_max_chunk_chars": args.chunker_max_chunk_chars,
            "chunker_max_headers": args.chunker_max_headers,
            "query_formatter": args.query_formatter,
            "document_formatter": args.document_formatter,
            "num_retrieved_chunks": args.num_retrieved_chunks,
            "num_retrieved_extra": args.num_retrieved_extra,
            "max_silver_files": args.max_silver_files,
            "max_tokens": args.max_tokens,
        },
        num_workers=args.num_workers,
        num_cpu_per_worker=args.num_cpu_per_worker,
        num_gpu_per_worker=args.num_gpu_per_worker,
        local=args.mode == "local",
    ) as runner:
        runner.process_jsonl(args.input, args.output)

    # Count output records
    if args.mode == "local":
        total_count = 0
        for output_file in output_dir.glob("*.jsonl"):
            with open(output_file, "r") as f:
                count = sum(1 for _ in f)
                total_count += count
        logger.info(f"Silver file selection stage produced {total_count} records")


if __name__ == "__main__":
    main()
