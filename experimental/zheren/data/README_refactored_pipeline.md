# Refactored Vanguard to Binks Pipeline

This document describes the refactored version of the pipeline that consolidates GCP operations and allows skipping classification for CODEBASE_RETRIEVAL requests.

## Key Changes

### 1. Combined Request Fetching
- **Old**: `BigQueryRequestFetcherActor` only fetched request IDs, then `SingleRequestClassificationActor` fetched the actual messages from GCS
- **New**: `RequestQueryFetchActor` combines both operations:
  - Queries BigQuery for request IDs
  - Immediately fetches the full request content from GCS
  - Returns complete request data ready for processing

### 2. Simplified Classification
- **Old**: `SingleRequestClassificationActor` had to handle GCS fetching
- **New**: `SingleRequestClassificationActor` assumes requests already have messages
  - Focuses purely on classification logic
  - Automatically passes through CODEBASE_RETRIEVAL requests

### 3. Optimized Pipeline Flow
For CHAT requests:
```
PipelineConfig → RequestQueryFetchActor → FilteredVanguardSingleRequestWithMetadata
                                        → SingleRequestClassificationActor → FilteredVanguardSingleRequestWithMetadata
                                                                            → GeminiQueryClassificationActor → ...
```

For CODEBASE_RETRIEVAL requests (can skip classification):
```
PipelineConfig → RequestQueryFetchActor → FilteredVanguardSingleRequestWithMetadata
                                        → GeminiQueryClassificationActor → ...
```

## Benefits

1. **Better separation of concerns**: Each actor has a focused responsibility
2. **Reduced GCP API calls**: All GCP operations happen in one actor
3. **More efficient**: Avoids redundant credential initialization
4. **More flexible**: Can skip unnecessary stages based on request type
5. **Easier to test**: Each actor can be tested independently

## New Files

- `vanguard_to_binks_request_fetch_actor.py`: Combined BigQuery + GCS fetching
- `vanguard_to_binks_classification_actor_v2.py`: Simplified classification
- `request_fetch_stage.py`: Stage script for request fetching
- `classification_stage_v2.py`: Stage script for classification
- `run_pipeline_stages_v2.sh`: Updated pipeline runner

## Usage

### Running the Full Pipeline
```bash
# For CHAT requests (with classification)
./run_pipeline_stages_v2.sh \
    --work-dir /tmp/pipeline_run \
    --mode local \
    --request-type CHAT \
    --tenants "dogfood aitutor-prod"

# For CODEBASE_RETRIEVAL requests (skip classification)
./run_pipeline_stages_v2.sh \
    --work-dir /tmp/pipeline_run \
    --mode local \
    --request-type CODEBASE_RETRIEVAL \
    --tenants "dogfood"
```

### Running Individual Stages

#### Stage 1: Request Fetch (BigQuery + GCS)
```bash
python experimental/zheren/data/request_fetch_stage.py \
    --input /tmp/initial_config.jsonl \
    --output /tmp/stage1_fetched \
    --mode local \
    --num-workers 4
```

#### Stage 2: Classification (Optional for CODEBASE_RETRIEVAL)
```bash
python experimental/zheren/data/classification_stage_v2.py \
    --input /tmp/stage1_fetched \
    --output /tmp/stage2_classified \
    --mode local \
    --num-workers 4
```

## Data Flow

1. **Initial Config**: Contains pipeline configuration (tenants, dates, request type)
2. **Stage 1 Output**: Complete requests with messages and blob_names
3. **Stage 2 Output**: Filtered requests (only codebase-related)
4. **Remaining stages**: Same as before

## Migration Notes

To migrate from the old pipeline:

1. Replace `bigquery_fetch_stage.py` with `request_fetch_stage.py`
2. Replace `classification_stage.py` with `classification_stage_v2.py`
3. Update your scripts to use the new stage order
4. For CODEBASE_RETRIEVAL, you can skip the classification stage entirely

## Performance Considerations

- The new `RequestQueryFetchActor` may use more memory since it fetches all request content upfront
- However, it reduces overall latency by batching GCP operations
- Consider adjusting `--num-workers` based on your memory constraints
