# Individual Stage Scripts for Vanguard to Binks Pipeline

This directory contains individual stage scripts that allow you to run each stage of the Vanguard to Binks pipeline separately, following the pattern used by your peers in `experimental/tongfei/chatanol/`.

## Overview

The pipeline has been refactored into the following individual stage scripts:

1. **`create_initial_config.py`** - Creates the initial pipeline configuration
2. **`bigquery_fetch_stage.py`** - Fetches requests from BigQuery
3. **`classification_stage.py`** - Classifies requests using PleaseHold model
4. **`gemini_classification_stage.py`** - Additional Gemini-based classification (CHAT only)
5. **`blob_fetch_and_process_stage.py`** - Fetches blob content and processes files
6. **`silver_file_selection_stage.py`** - Selects relevant files using retrieval
7. **`repository_assembly_stage.py`** - Assembles final Binks format output

## Quick Start

### Using the Shell Script

The easiest way to run the pipeline is using the provided shell script:

```bash
# Edit the configuration in run_pipeline_stages.sh
vim experimental/zheren/data/run_pipeline_stages.sh

# Run the pipeline
./experimental/zheren/data/run_pipeline_stages.sh
```

### Tenant Selection

You can select specific tenants to process by editing the `TENANTS` variable in `run_pipeline_stages.sh`:

```bash
# Use all tenants (default)
TENANTS=""

# Use specific tenants
TENANTS="dogfood,dogfood-shard"

# Use specific Vanguard tenants
TENANTS="i0-vanguard0,i0-vanguard1,i0-vanguard2"
```

Available tenants include:
- `dogfood`, `dogfood-shard`
- `aitutor-pareto`, `aitutor-turing`, `aitutor-mercor`
- `i0-vanguard0` through `i0-vanguard7`
- `i1-vanguard0` through `i1-vanguard7`

### Creating Initial Configuration

```bash
# Create config for all tenants
python experimental/zheren/data/create_initial_config.py \
    --date-from 2024-01-01 \
    --date-to 2024-01-07 \
    --output /tmp/initial_config.jsonl \
    --request-type CHAT

# Create config for specific tenants
python experimental/zheren/data/create_initial_config.py \
    --date-from 2024-01-01 \
    --date-to 2024-01-07 \
    --output /tmp/initial_config.jsonl \
    --request-type CHAT \
    --tenants "dogfood,i0-vanguard0,i0-vanguard1" \
    --limit 1000
```

## Running Individual Stages

Each stage can be run independently with its own arguments:

### Stage 1: BigQuery Fetch
```bash
python experimental/zheren/data/bigquery_fetch_stage.py \
    --input /tmp/initial_config.jsonl \
    --output /tmp/stage0_fetch \
    --mode local \
    --num-workers 10
```

### Stage 2: Classification
```bash
python experimental/zheren/data/classification_stage.py \
    --input /tmp/stage0_fetch \
    --output /tmp/stage1_classify \
    --mode local \
    --num-workers 4 \
    --num-gpu-per-worker 1
```

### Stage 3: Gemini Classification (CHAT only)
```bash
python experimental/zheren/data/gemini_classification_stage.py \
    --input /tmp/stage1_classify \
    --output /tmp/stage2_gemini_classify \
    --mode local \
    --num-workers 4
```

### Stage 4: Blob Fetch and Process
```bash
python experimental/zheren/data/blob_fetch_and_process_stage.py \
    --input /tmp/stage2_gemini_classify \
    --output /tmp/stage3_process \
    --mode local \
    --num-workers 20
```

### Stage 5: Silver File Selection
```bash
python experimental/zheren/data/silver_file_selection_stage.py \
    --input /tmp/stage3_process \
    --output /tmp/stage4_silver \
    --mode local \
    --num-workers 2 \
    --num-gpu-per-worker 1
```

### Stage 6: Repository Assembly
```bash
python experimental/zheren/data/repository_assembly_stage.py \
    --input /tmp/stage4_silver \
    --output /tmp/stage5_final \
    --mode local \
    --num-workers 8
```

## Configuration Options

### Execution Modes
- `--mode local`: Run locally for testing
- `--mode ray`: Run on Ray cluster for distributed processing

### Request Types
- `CHAT`: Regular chat requests (includes Gemini classification)
- `CODEBASE_RETRIEVAL`: Codebase retrieval requests (skips Gemini classification)

### Resource Configuration
Each stage supports:
- `--num-workers`: Number of parallel workers
- `--num-cpu-per-worker`: CPUs per worker
- `--num-gpu-per-worker`: GPUs per worker (where applicable)

## Pipeline Flow

```
Initial Config → BigQuery Fetch → Classification → [Gemini Classification*] →
Blob Fetch & Process → Silver File Selection → Repository Assembly → Final Output
```

*Gemini Classification is only used for CHAT requests, not CODEBASE_RETRIEVAL.

## Benefits of Individual Stages

1. **Debugging**: Run individual stages to isolate issues
2. **Resumability**: Resume from any stage if the pipeline fails
3. **Flexibility**: Adjust parameters for specific stages
4. **Development**: Test changes to individual components
5. **Resource Optimization**: Allocate different resources to different stages

## Example Workflows

### Development/Testing
```bash
# Test with a small subset
TENANTS="dogfood"
LIMIT="100"
MODE="local"
```

### Production Run
```bash
# Process all Vanguard tenants
TENANTS="i0-vanguard0,i0-vanguard1,i0-vanguard2,i0-vanguard3,i0-vanguard4,i0-vanguard5,i0-vanguard6,i0-vanguard7"
LIMIT=""
MODE="ray"
```

### Specific Analysis
```bash
# Focus on specific tenants for analysis
TENANTS="aitutor-pareto,aitutor-turing"
LIMIT="5000"
MODE="ray"
```
