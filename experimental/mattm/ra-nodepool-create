#!/bin/bash

set -euo pipefail

# Private node pools (use NAT for outbound connectivity):
# ra-nodepool-create gcp-agent0             n2-3  n2-standard-80  general
# ra-nodepool-create gcp-prod-agent0        n2-3  n2-standard-80  general
# ra-nodepool-create gcp-prod-agent0        n2-3  n2-standard-80  staging
# ra-nodepool-create gcp-eu-w4-prod-agent0  n2-3  n2-standard-80  general
# ra-nodepool-create gcp-eu-w4-prod-agent0  n2-3  n2-standard-80  staging
#
# Dry run (print command without executing):
# ra-nodepool-create --dry-run gcp-prod-agent0 n2-3 n2-standard-80 general

create() {
	# Parse dry-run flag if present
	local dry_run=false
	if [[ "$1" == "--dry-run" ]]; then
		dry_run=true
		shift
	fi

	declare -r cluster="$1"
	declare -r pool_name_suffix="$2"
	declare -r machine_type="$3"
	declare -r pool_group="${4:-"general"}"
	declare -r is_private="${5:-"private"}"

	# Determine pool name based on whether it's private
	if [[ "$is_private" == "private" ]]; then
		declare -r pool_name="ws-${pool_group}-${pool_name_suffix}-private"
	else
		declare -r pool_name="ws-${pool_group}-${pool_name_suffix}"
	fi

	if [[ "$cluster" == "gcp-prod-agent0" ]]; then
		declare -r project="agent-sandbox-prod"
		declare -r region="us-central1"
		declare -r node_locations="$region-a,$region-b,$region-c,$region-f"
		declare -r node_sa="${cluster}-gke-nodepool@${project}.iam.gserviceaccount.com"
	elif [[ "$cluster" == "gcp-eu-w4-prod-agent0" ]]; then
		declare -r project="agent-sandbox-prod"
		declare -r region="europe-west4"
		declare -r node_locations="$region-a,$region-b,$region-c"
		declare -r node_sa="${cluster}-node@${project}.iam.gserviceaccount.com"
	elif [[ "$cluster" == "gcp-us1" ]]; then
		declare -r project="augment-research-gsc"
		declare -r region="us-central1"
		declare -r node_locations="$region-a,$region-b,$region-c,$region-f"
		declare -r node_sa="${cluster}-gke-nodepool@${project}.iam.gserviceaccount.com"
	else
		declare -r project="augment-research-gsc"
		declare -r region="us-central1"
		declare -r node_locations="$region-a,$region-b,$region-c,$region-f"
		declare -r node_sa="${cluster}-gke-nodepool@${project}.iam.gserviceaccount.com"
	fi

	# Set up private node flags if needed
	if [[ "$is_private" == "private" ]]; then
		declare -ra private_flags=(
			--enable-private-nodes
		)
		declare -r node_labels="raws.augmentcode.com/pool-group=$pool_group,r.augmentcode.com/private-nodes=true"
	else
		declare -ra private_flags=()
		declare -r node_labels="raws.augmentcode.com/pool-group=$pool_group"
	fi

	declare -ra cmd=(
		gcloud container node-pools create
		--project="$project"
		--region="$region"
		--cluster="$cluster"

		"$pool_name"

		--node-labels="$node_labels"
		--node-taints=raws.augmentcode.com/pool-group="$pool_group":NoSchedule

		--machine-type="$machine_type"
		--disk-type=pd-balanced
		--disk-size=128
		--ephemeral-storage-local-ssd=count=0

		--node-locations="$node_locations"

		--enable-autoscaling
		--location-policy=ANY
		--total-min-nodes=1
		--total-max-nodes=32
		--num-nodes=0

		--enable-autoupgrade
		--enable-autorepair
		--enable-surge-upgrade
		--max-surge-upgrade=1
		--max-unavailable-upgrade=0

		--image-type=COS_CONTAINERD
		--enable-nested-virtualization
		--shielded-secure-boot
		--shielded-integrity-monitoring
		--service-account="$node_sa"
		"${private_flags[@]}"
	)

	printf "$(tput bold)%s$(tput sgr0)\n" "${cmd[*]}"
	if [[ "$dry_run" == "false" ]]; then
		"${cmd[@]}"
	else
		echo "[DRY RUN] Command would be executed above"
	fi
}

create "$@"
