#!/usr/bin/env python3
"""
Test script to verify the optimization logic for content-truncation filtering.
This script demonstrates the SQL queries that would be used.
"""


def test_content_truncation_query():
    """Test the content-truncation filtering query structure."""

    # Mock tenant object
    class MockTenant:
        def __init__(self):
            self.project_id = "system-services-prod"
            self.analytics_dataset_name = "us_staging_request_insight_analytics_dataset"
            self.name = "dogfood-shard"

    tenant_obj = MockTenant()

    # Original query (without optimization)
    original_query = f"""
        SELECT session_id, DATE(time) AS d, COUNT(*) AS c
        FROM `{tenant_obj.project_id}.{tenant_obj.analytics_dataset_name}.request_metadata`
        WHERE time BETWEEN @start AND @end
          AND session_id IS NOT NULL AND session_id != ''
          AND request_type = 'AGENT_CHAT'
        GROUP BY session_id, d
        ORDER BY c DESC, d DESC
        LIMIT 5000
    """

    # New optimization query - step 1: get sessions with content-truncation
    truncation_filter_query = f"""
        SELECT DISTINCT session_id
        FROM `{tenant_obj.project_id}.{tenant_obj.analytics_dataset_name}.agent_session_event`
        WHERE time BETWEEN @start AND @end
          AND session_id IS NOT NULL AND session_id != ''
          AND JSON_VALUE(sanitized_json, '$.event_name') = 'content-truncation'
    """

    # New optimization query - step 2: get session-day pairs only for filtered sessions
    optimized_query = f"""
        SELECT session_id, DATE(time) AS d, COUNT(*) AS c
        FROM `{tenant_obj.project_id}.{tenant_obj.analytics_dataset_name}.request_metadata`
        WHERE time BETWEEN @start AND @end
          AND session_id IS NOT NULL AND session_id != ''
          AND request_type = 'AGENT_CHAT'
          AND session_id IN UNNEST(@session_ids)
        GROUP BY session_id, d
        ORDER BY c DESC, d DESC
        LIMIT 5000
    """

    print("=== OPTIMIZATION COMPARISON ===")
    print("\n1. ORIGINAL APPROACH:")
    print("   - Scans ALL sessions in request_metadata table")
    print("   - Processes all session-day pairs")
    print("   - Extracts samples from all sessions (most won't have truncations)")
    print(f"   Query: {original_query}")

    print("\n2. OPTIMIZED APPROACH:")
    print("   - Step 1: Pre-filter sessions with content-truncation events")
    print("   - Step 2: Only process session-day pairs from filtered sessions")
    print("   - Significantly reduces search space")
    print(f"   Step 1 Query: {truncation_filter_query}")
    print(f"   Step 2 Query: {optimized_query}")

    print("\n=== EXPECTED BENEFITS ===")
    print("- Reduced BigQuery processing time")
    print("- Lower memory usage")
    print("- Faster sample collection")
    print("- Higher success rate (only sessions with truncations)")
    print("- Better cost efficiency")


if __name__ == "__main__":
    test_content_truncation_query()
