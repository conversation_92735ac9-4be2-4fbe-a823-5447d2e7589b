import datetime
import logging
from typing import Optional

from google.cloud import bigquery
from base.datasets.gcp_creds import get_gcp_creds
from base.datasets.tenants import DatasetTenant

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# Constants
CACHE_DIRECTORY = "/home/<USER>/ri_cache"


def get_agent_conv_last_request_ids(
    tenant: DatasetTenant,
    from_datetime: Optional[datetime.datetime] = None,
    to_datetime: Optional[datetime.datetime] = None,
    limit: Optional[int] = None,
    timeout_seconds: int = 300,
) -> list[str]:
    """Get the last request_id for each conversation in the given time range.

    Args:
        from_datetime: Start of the time range (inclusive). If None, no lower bound.
        to_datetime: End of the time range (inclusive). If None, no upper bound.
        tenant: The tenant to filter by.
        limit: Maximum number of request IDs to return. If None, no limit.
        timeout_seconds: Query timeout in seconds. Default is 300 (5 minutes).

    Returns:
        List of request_ids for the last request in each conversation.
    """
    TABLE = "agent_request_event"
    gcp_creds, _ = get_gcp_creds(None)
    bigquery_client = bigquery.Client(project=tenant.project_id, credentials=gcp_creds)

    # Build time filter conditions
    time_conditions = []
    if from_datetime:
        time_conditions.append(f"time >= TIMESTAMP('{from_datetime.isoformat()}')")
    if to_datetime:
        time_conditions.append(f"time <= TIMESTAMP('{to_datetime.isoformat()}')")
    time_filter = " AND ".join(time_conditions) if time_conditions else "1=1"

    # Add timeout and better error handling
    import logging

    logger = logging.getLogger(__name__)

    # Progressive fallback strategy: try smaller limits if queries timeout
    # For staging environments, be extra aggressive with small limits
    is_staging = "staging" in tenant.name.lower() or "dogfood" in tenant.name.lower()

    if is_staging:
        # Aggressive fallback for staging environments with large datasets
        if not limit:
            fallback_limits = [2000, 1000, 500, 100, 50, 20, 10, 5, 3, 1]
        elif limit <= 10:
            fallback_limits = [limit, 5, 3, 1]
        elif limit <= 50:
            fallback_limits = [limit, 20, 10, 5, 3, 1]
        elif limit <= 100:
            fallback_limits = [limit, 50, 20, 10, 5, 3, 1]
        elif limit <= 500:
            fallback_limits = [limit, 200, 100, 50, 20, 10, 5, 3, 1]
        elif limit <= 1000:
            fallback_limits = [limit, 500, 200, 100, 50, 20, 10, 5, 3, 1]
        else:
            fallback_limits = [limit, 1000, 500, 200, 100, 50, 20, 10, 5, 3, 1]
    else:
        fallback_limits = [limit] if limit else [100]
        if limit and limit > 100:
            fallback_limits = [limit, 100, 50, 10]
        elif limit and limit > 50:
            fallback_limits = [limit, 50, 10]
        elif limit and limit > 10:
            fallback_limits = [limit, 10]

    for attempt_limit in fallback_limits:
        # Rebuild query with current attempt limit
        attempt_limit_clause = f"LIMIT {attempt_limit}"
        attempt_query = f"""
        SELECT request_id
        FROM `{tenant.project_id}.{tenant.analytics_dataset_name}.{TABLE}`
        WHERE tenant = '{tenant.name}' AND {time_filter}
        ORDER BY time DESC
        {attempt_limit_clause}
        """

        try:
            logger.info(
                f"Executing BigQuery query for tenant {tenant.name} with limit {attempt_limit} and timeout {timeout_seconds}s"
            )
            logger.debug(f"Query: {attempt_query}")

            # Create job config with timeout
            job_config = bigquery.QueryJobConfig()
            job_config.job_timeout_ms = (
                timeout_seconds * 1000
            )  # Convert to milliseconds

            # Execute query with timeout
            query_job = bigquery_client.query(attempt_query, job_config=job_config)
            rows = query_job.result(timeout=timeout_seconds)

            request_ids = [row.request_id for row in rows]
            logger.info(
                f"Successfully retrieved {len(request_ids)} request IDs for tenant {tenant.name}"
            )
            return request_ids

        except Exception as e:
            logger.warning(
                f"BigQuery query failed for tenant {tenant.name} with limit {attempt_limit}: {type(e).__name__}: {str(e)}"
            )

            # If this was the last attempt, log full error details and raise
            if attempt_limit == fallback_limits[-1]:
                logger.error(f"All fallback attempts failed for tenant {tenant.name}")
                logger.error(f"Final query was: {attempt_query}")
                # Log more details about the exception
                if hasattr(e, "errors"):
                    logger.error(f"BigQuery errors: {e.errors}")
                if hasattr(e, "message"):
                    logger.error(f"BigQuery message: {e.message}")
                raise RuntimeError(
                    f"Failed to fetch conversation request IDs for tenant {tenant.name}: {type(e).__name__}: {str(e)}"
                ) from e
            else:
                logger.info(f"Trying smaller limit for tenant {tenant.name}...")
                continue


def get_requests_with_launch_process(
    tenant: DatasetTenant,
    from_datetime: Optional[datetime.datetime] = None,
    to_datetime: Optional[datetime.datetime] = None,
) -> list[str]:
    """Get request_ids for requests that contain LaunchProcess tool calls.

    Args:
        tenant: The tenant to filter by.
        from_datetime: Start of the time range (inclusive). If None, no lower bound.
        to_datetime: End of the time range (inclusive). If None, no upper bound.

    Returns:
        List of request_ids that have LaunchProcess tool calls.
    """
    TABLE = "agent_request_event"
    gcp_creds, _ = get_gcp_creds(None)
    bigquery_client = bigquery.Client(project=tenant.project_id, credentials=gcp_creds)

    # Build time filter conditions
    time_conditions = []
    if from_datetime:
        time_conditions.append(f"time >= TIMESTAMP('{from_datetime.isoformat()}')")
    if to_datetime:
        time_conditions.append(f"time <= TIMESTAMP('{to_datetime.isoformat()}')")
    time_filter = " AND ".join(time_conditions) if time_conditions else "1=1"

    # Query to find requests with LaunchProcess tool calls
    # Note: We'll filter for staging environment in the conversation analysis step
    query = f"""
    SELECT DISTINCT
        request_id,
        time
    FROM `{tenant.project_id}.{tenant.analytics_dataset_name}.{TABLE}`
    WHERE tenant = '{tenant.name}'
    AND {time_filter}
    AND (
        -- Check for LaunchProcess tool calls in the JSON
        JSON_EXTRACT_SCALAR(sanitized_json, '$.tool_name') = 'launch-process'
        OR JSON_EXTRACT_SCALAR(sanitized_json, '$.name') = 'launch-process'
    )
    ORDER BY time DESC
    """

    logger.info("Querying for requests with LaunchProcess tool calls...")
    rows = bigquery_client.query_and_wait(query)
    request_ids = [row.request_id for row in rows]
    logger.info(f"Found {len(request_ids)} requests with LaunchProcess tool calls")
    return request_ids
