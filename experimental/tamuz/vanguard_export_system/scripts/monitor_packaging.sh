#!/bin/bash
# Monitor the packaging progress

echo "=== Packaging Progress Monitor ==="
echo "Time: $(date)"
echo ""

# Check if process is running
PID=$(ps aux | grep "package_conversations.py" | grep -v grep | awk '{print $2}')
if [ -n "$PID" ]; then
    echo "✓ Packaging process is running (PID: $PID)"

    # Get process runtime
    RUNTIME=$(ps -p $PID -o etime= | tr -d ' ')
    echo "  Runtime: $RUNTIME"

    # Get CPU and memory usage
    CPU_MEM=$(ps -p $PID -o %cpu,%mem | tail -1)
    echo "  CPU/Memory: $CPU_MEM"
else
    echo "✗ Packaging process is not running"
fi

echo ""
echo "=== Output Directory ==="
OUTPUT_DIR="/mnt/efs/augment/user/tamuz/conversations_chunks"
if [ -d "$OUTPUT_DIR" ]; then
    echo "Files in $OUTPUT_DIR:"
    ls -lah "$OUTPUT_DIR" | grep -E "\.tar$|\.tar\.gz$"
    echo ""
    echo "Total size: $(du -sh "$OUTPUT_DIR" | cut -f1)"
else
    echo "Output directory not found"
fi

echo ""
echo "=== Disk Usage ==="
df -h / | grep -E "Filesystem|/dev/root"

echo ""
echo "=== Memory Usage ==="
free -h | grep -E "Mem:|Swap:"
