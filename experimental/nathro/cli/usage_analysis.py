"""<PERSON><PERSON><PERSON> to analyze CLI usage for one or more tenants in depth.

This script provides detailed analysis for tenants including:
1. Request/user comparison between CLI users vs non-CLI users
2. Week-over-week usage trends for all users
3. Usage patterns for users who started using CLI this week
"""

import argparse
from datetime import datetime, timed<PERSON><PERSON>
from typing import List
import pandas as pd
from google.cloud import bigquery
import pytz

from base.datasets.gcp_creds import get_gcp_creds

# Pacific timezone for all date operations
PACIFIC_TZ = pytz.timezone('US/Pacific')


def get_cli_tenants(
    client: bigquery.Client, weeks_back: int = 8
) -> List[str]:
    """Get list of tenants that used CLI in the date range."""

    # End at start of today in Pacific Time (excluding today's incomplete data)
    end_date = datetime.now(PACIFIC_TZ).replace(hour=0, minute=0, second=0, microsecond=0)
    # Start enough weeks back to capture pre-CLI usage
    start_date = end_date - timedelta(days=weeks_back * 7)

    query = """
    SELECT DISTINCT tenant
    FROM `system-services-prod.us_prod_request_insight_analytics_dataset.request_metadata`
    WHERE DATE(time, "America/Los_Angeles") >= DATE(@start_date)
    AND DATE(time, "America/Los_Angeles") < DATE(@end_date)
    AND CONTAINS_SUBSTR(user_agent, "cli/")
    AND NOT STARTS_WITH(tenant, "aitutor-")
    AND NOT CONTAINS_SUBSTR(tenant, "-discovery")
    """

    job_config = bigquery.QueryJobConfig(
        query_parameters=[
            bigquery.ScalarQueryParameter("start_date", "DATE", start_date.date()),
            bigquery.ScalarQueryParameter("end_date", "DATE", end_date.date()),
        ],
        use_query_cache=True,
        use_legacy_sql=False,
    )

    df = (
        client.query(query, job_config=job_config)
        .result()
        .to_dataframe(create_bqstorage_client=False)
    )
    return df["tenant"].tolist()


def get_user_cli_status(
    client: bigquery.Client, tenants: List[str], start_date: datetime, end_date: datetime
) -> pd.DataFrame:
    """Get all users for the tenants and identify which ones have used CLI."""

    query = """
    SELECT
        tenant,
        opaque_user_id,
        COUNT(*) as total_requests,
        SUM(CASE WHEN CONTAINS_SUBSTR(user_agent, "cli/") THEN 1 ELSE 0 END) as cli_requests,
        CASE WHEN SUM(CASE WHEN CONTAINS_SUBSTR(user_agent, "cli/") THEN 1 ELSE 0 END) > 0
             THEN 'CLI_USER' ELSE 'NON_CLI_USER' END as user_type,
        MIN(CASE WHEN CONTAINS_SUBSTR(user_agent, "cli/") THEN time ELSE NULL END) as first_cli_usage
    FROM `system-services-prod.us_prod_request_insight_analytics_dataset.request_metadata`
    WHERE DATE(time, "America/Los_Angeles") >= DATE(@start_date)
    AND DATE(time, "America/Los_Angeles") < DATE(@end_date)
    AND tenant IN UNNEST(@tenants)
    AND CONTAINS_SUBSTR(request_type, 'AGENT_CHAT')
    AND SPLIT(user_agent, '/')[OFFSET(0)] != 'AugmentHealthCheck'
    AND NOT STARTS_WITH(tenant, "aitutor-")
    GROUP BY tenant, opaque_user_id
    ORDER BY tenant, total_requests DESC
    """

    job_config = bigquery.QueryJobConfig(
        query_parameters=[
            bigquery.ScalarQueryParameter("start_date", "DATE", start_date.date()),
            bigquery.ScalarQueryParameter("end_date", "DATE", end_date.date()),
            bigquery.ArrayQueryParameter("tenants", "STRING", tenants),
        ],
        use_query_cache=True,
        use_legacy_sql=False,
    )

    df = (
        client.query(query, job_config=job_config)
        .result()
        .to_dataframe(create_bqstorage_client=False)
    )
    return df


def get_user_agent_breakdown(
    client: bigquery.Client, tenants: List[str], start_date: datetime, end_date: datetime
) -> pd.DataFrame:
    """Get breakdown of requests by user agent for CLI vs non-CLI users."""

    query = """
    WITH user_types AS (
        SELECT
            tenant,
            opaque_user_id,
            CASE WHEN SUM(CASE WHEN CONTAINS_SUBSTR(user_agent, "cli/") THEN 1 ELSE 0 END) > 0
                 THEN 'CLI_USER' ELSE 'NON_CLI_USER' END as user_type
        FROM `system-services-prod.us_prod_request_insight_analytics_dataset.request_metadata`
        WHERE DATE(time, "America/Los_Angeles") >= DATE(@start_date)
        AND DATE(time, "America/Los_Angeles") < DATE(@end_date)
        AND tenant IN UNNEST(@tenants)
        AND CONTAINS_SUBSTR(request_type, 'AGENT_CHAT')
        AND SPLIT(user_agent, '/')[OFFSET(0)] != 'AugmentHealthCheck'
        AND NOT STARTS_WITH(tenant, "aitutor-")
        GROUP BY tenant, opaque_user_id
    )
    SELECT
        ut.user_type,
        SPLIT(rm.user_agent, '/')[OFFSET(0)] as user_agent_base,
        COUNT(*) as request_count
    FROM `system-services-prod.us_prod_request_insight_analytics_dataset.request_metadata` rm
    JOIN user_types ut ON rm.tenant = ut.tenant AND rm.opaque_user_id = ut.opaque_user_id
    WHERE DATE(rm.time, "America/Los_Angeles") >= DATE(@start_date)
    AND DATE(rm.time, "America/Los_Angeles") < DATE(@end_date)
    AND rm.tenant IN UNNEST(@tenants)
    AND CONTAINS_SUBSTR(request_type, 'AGENT_CHAT')
    AND SPLIT(rm.user_agent, '/')[OFFSET(0)] != 'AugmentHealthCheck'
    AND NOT STARTS_WITH(rm.tenant, "aitutor-")
    GROUP BY ut.user_type, user_agent_base
    ORDER BY ut.user_type, request_count DESC
    """

    job_config = bigquery.QueryJobConfig(
        query_parameters=[
            bigquery.ScalarQueryParameter("start_date", "DATE", start_date.date()),
            bigquery.ScalarQueryParameter("end_date", "DATE", end_date.date()),
            bigquery.ArrayQueryParameter("tenants", "STRING", tenants),
        ],
        use_query_cache=True,
        use_legacy_sql=False,
    )

    df = (
        client.query(query, job_config=job_config)
        .result()
        .to_dataframe(create_bqstorage_client=False)
    )
    return df


def get_weekly_usage_trends(
    client: bigquery.Client, tenants: List[str], weeks_back: int = 4
) -> pd.DataFrame:
    """Get 7-day period usage trends for the tenants."""

    # End at start of today in Pacific Time (excluding today's incomplete data)
    end_date = datetime.now(PACIFIC_TZ).replace(hour=0, minute=0, second=0, microsecond=0)
    # Start 4 complete 7-day periods ago
    start_date = end_date - timedelta(days=weeks_back * 7)

    query = """
    SELECT
        tenant,
        DATE_SUB(DATE(time, "America/Los_Angeles"), INTERVAL MOD(DATE_DIFF(DATE(time, "America/Los_Angeles"), DATE(@start_date), DAY), 7) DAY) as period_start,
        COUNT(*) as total_requests,
        COUNT(DISTINCT opaque_user_id) as unique_users,
        SUM(CASE WHEN CONTAINS_SUBSTR(user_agent, "cli/") THEN 1 ELSE 0 END) as cli_requests,
        COUNT(DISTINCT CASE WHEN CONTAINS_SUBSTR(user_agent, "cli/") THEN opaque_user_id ELSE NULL END) as cli_users
    FROM `system-services-prod.us_prod_request_insight_analytics_dataset.request_metadata`
    WHERE DATE(time, "America/Los_Angeles") >= DATE(@start_date)
    AND DATE(time, "America/Los_Angeles") < DATE(@end_date)
    AND tenant IN UNNEST(@tenants)
    AND CONTAINS_SUBSTR(request_type, 'AGENT_CHAT')
    AND SPLIT(user_agent, '/')[OFFSET(0)] != 'AugmentHealthCheck'
    AND NOT STARTS_WITH(tenant, "aitutor-")
    GROUP BY tenant, period_start
    ORDER BY tenant, period_start
    """

    job_config = bigquery.QueryJobConfig(
        query_parameters=[
            bigquery.ScalarQueryParameter("start_date", "DATE", start_date.date()),
            bigquery.ScalarQueryParameter("end_date", "DATE", end_date.date()),
            bigquery.ArrayQueryParameter("tenants", "STRING", tenants),
        ],
        use_query_cache=True,
        use_legacy_sql=False,
    )

    df = (
        client.query(query, job_config=job_config)
        .result()
        .to_dataframe(create_bqstorage_client=False)
    )
    return df


def get_cli_adoption_impact_analysis(
    client: bigquery.Client, tenants: List[str], weeks_back: int = 8
) -> pd.DataFrame:
    """Analyze usage patterns before and after CLI adoption to understand if CLI drives usage or if CLI users are just power users.

    This analysis:
    1. Finds users who adopted CLI during the analysis period
    2. Compares their usage 2 weeks before vs 2 weeks after CLI adoption
    3. Includes zero-request days in the average calculation for accuracy
    """

    # End at start of today in Pacific Time (excluding today's incomplete data)
    end_date = datetime.now(PACIFIC_TZ).replace(hour=0, minute=0, second=0, microsecond=0)
    # Start enough weeks back to capture pre-CLI usage
    start_date = end_date - timedelta(days=weeks_back * 7)

    query = """
    WITH cli_adoption_dates AS (
        -- Find when each user first used CLI (search all time, not just analysis window)
        SELECT
            tenant,
            opaque_user_id,
            MIN(DATE(time, "America/Los_Angeles")) as first_cli_date
        FROM `system-services-prod.us_prod_request_insight_analytics_dataset.request_metadata`
        WHERE tenant IN UNNEST(@tenants)
        AND CONTAINS_SUBSTR(request_type, 'AGENT_CHAT')
        AND CONTAINS_SUBSTR(user_agent, "cli/")
        AND SPLIT(user_agent, '/')[OFFSET(0)] != 'AugmentHealthCheck'
        AND NOT STARTS_WITH(tenant, "aitutor-")
        GROUP BY tenant, opaque_user_id
    ),
    user_first_usage_dates AS (
        -- Find when each user first used Augment (any request type, search all time)
        SELECT
            tenant,
            opaque_user_id,
            MIN(DATE(time, "America/Los_Angeles")) as first_usage_date
        FROM `system-services-prod.us_prod_request_insight_analytics_dataset.request_metadata`
        WHERE tenant IN UNNEST(@tenants)
        AND CONTAINS_SUBSTR(request_type, 'AGENT_CHAT')
        AND SPLIT(user_agent, '/')[OFFSET(0)] != 'AugmentHealthCheck'
        AND NOT STARTS_WITH(tenant, "aitutor-")
        GROUP BY tenant, opaque_user_id
    ),
    date_range AS (
        -- Generate all dates in our analysis period
        SELECT date_val
        FROM UNNEST(GENERATE_DATE_ARRAY(DATE(@start_date), DATE_SUB(DATE(@end_date), INTERVAL 1 DAY))) AS date_val
    ),
    cli_adopters_with_analysis_window AS (
        -- Only include CLI adopters who have full 7-day windows before and after adoption
        SELECT
            tenant,
            opaque_user_id,
            first_cli_date
        FROM cli_adoption_dates
        WHERE first_cli_date >= DATE_ADD(DATE(@start_date), INTERVAL 7 DAY)  -- At least 7 days before in our data
        AND first_cli_date <= DATE_SUB(DATE(@end_date), INTERVAL 7 DAY)      -- At least 7 days after in our data
    ),
    user_date_combinations AS (
        -- Create all combinations of CLI adopters and dates within their exact 7-day windows
        SELECT
            caw.tenant,
            caw.opaque_user_id,
            caw.first_cli_date,
            dr.date_val as usage_date,
            CASE
                WHEN dr.date_val >= DATE_SUB(caw.first_cli_date, INTERVAL 7 DAY)
                     AND dr.date_val < caw.first_cli_date THEN 'PRE_CLI'
                WHEN dr.date_val >= caw.first_cli_date
                     AND dr.date_val < DATE_ADD(caw.first_cli_date, INTERVAL 7 DAY) THEN 'POST_CLI'
                ELSE NULL
            END as usage_period
        FROM cli_adopters_with_analysis_window caw
        CROSS JOIN date_range dr
        WHERE dr.date_val >= DATE_SUB(caw.first_cli_date, INTERVAL 7 DAY)
        AND dr.date_val < DATE_ADD(caw.first_cli_date, INTERVAL 7 DAY)
    ),
    actual_usage AS (
        -- Get actual daily usage (only for days with requests)
        SELECT
            tenant,
            opaque_user_id,
            DATE(time, "America/Los_Angeles") as usage_date,
            COUNT(*) as daily_requests,
            SUM(CASE WHEN CONTAINS_SUBSTR(user_agent, "cli/") THEN 1 ELSE 0 END) as daily_cli_requests
        FROM `system-services-prod.us_prod_request_insight_analytics_dataset.request_metadata`
        WHERE DATE(time, "America/Los_Angeles") >= DATE(@start_date)
        AND DATE(time, "America/Los_Angeles") < DATE(@end_date)
        AND tenant IN UNNEST(@tenants)
        AND CONTAINS_SUBSTR(request_type, 'AGENT_CHAT')
        AND SPLIT(user_agent, '/')[OFFSET(0)] != 'AugmentHealthCheck'
        AND NOT STARTS_WITH(tenant, "aitutor-")
        GROUP BY tenant, opaque_user_id, usage_date
    )
    SELECT
        udc.tenant,
        udc.opaque_user_id,
        udc.first_cli_date,
        ufu.first_usage_date,
        udc.usage_date,
        COALESCE(au.daily_requests, 0) as daily_requests,
        COALESCE(au.daily_cli_requests, 0) as daily_cli_requests,
        udc.usage_period,
        DATE_DIFF(udc.usage_date, udc.first_cli_date, DAY) as days_from_cli_adoption,
        CASE
            WHEN ufu.first_usage_date = udc.first_cli_date THEN 'NEW_USER'
            ELSE 'EXISTING_USER'
        END as user_category
    FROM user_date_combinations udc
    LEFT JOIN actual_usage au ON udc.tenant = au.tenant
                              AND udc.opaque_user_id = au.opaque_user_id
                              AND udc.usage_date = au.usage_date
    LEFT JOIN user_first_usage_dates ufu ON udc.tenant = ufu.tenant
                                         AND udc.opaque_user_id = ufu.opaque_user_id
    WHERE udc.usage_period IS NOT NULL
    ORDER BY udc.tenant, udc.opaque_user_id, udc.usage_date
    """

    job_config = bigquery.QueryJobConfig(
        query_parameters=[
            bigquery.ScalarQueryParameter("start_date", "DATE", start_date.date()),
            bigquery.ScalarQueryParameter("end_date", "DATE", end_date.date()),
            bigquery.ArrayQueryParameter("tenants", "STRING", tenants),
        ],
        use_query_cache=True,
        use_legacy_sql=False,
    )

    df = (
        client.query(query, job_config=job_config)
        .result()
        .to_dataframe(create_bqstorage_client=False)
    )
    return df





def analyze_user_comparison(user_status_df: pd.DataFrame, tenant: str = None) -> str:
    """Analyze and format the comparison between CLI and non-CLI users."""

    output = []
    output.append("=" * 80)
    if tenant:
        output.append(f"USER COMPARISON: CLI USERS vs NON-CLI USERS (LAST 7 DAYS) - {tenant.upper()}")
    else:
        output.append("USER COMPARISON: CLI USERS vs NON-CLI USERS (LAST 7 DAYS)")
    output.append("=" * 80)
    output.append("")

    # Summary statistics
    cli_users = user_status_df[user_status_df['user_type'] == 'CLI_USER']
    non_cli_users = user_status_df[user_status_df['user_type'] == 'NON_CLI_USER']

    output.append("SUMMARY (Last 7 Days):")
    output.append(f"  Total users: {len(user_status_df):,}")
    output.append(f"  CLI users: {len(cli_users):,} ({len(cli_users)/len(user_status_df)*100:.1f}%)")
    output.append(f"  Non-CLI users: {len(non_cli_users):,} ({len(non_cli_users)/len(user_status_df)*100:.1f}%)")
    output.append("")

    # Request statistics with percentiles
    if len(cli_users) > 0:
        cli_stats = cli_users['total_requests'].describe(percentiles=[0.25, 0.5, 0.75])
        cli_total_requests = cli_users['total_requests'].sum()
        cli_avg = cli_stats['mean']
        cli_p25 = cli_stats['25%']
        cli_p50 = cli_stats['50%']
        cli_p75 = cli_stats['75%']
    else:
        cli_total_requests = cli_avg = cli_p25 = cli_p50 = cli_p75 = 0

    if len(non_cli_users) > 0:
        non_cli_stats = non_cli_users['total_requests'].describe(percentiles=[0.25, 0.5, 0.75])
        non_cli_total_requests = non_cli_users['total_requests'].sum()
        non_cli_avg = non_cli_stats['mean']
        non_cli_p25 = non_cli_stats['25%']
        non_cli_p50 = non_cli_stats['50%']
        non_cli_p75 = non_cli_stats['75%']
    else:
        non_cli_total_requests = non_cli_avg = non_cli_p25 = non_cli_p50 = non_cli_p75 = 0

    # Calculate all users stats
    all_users_stats = user_status_df['total_requests'].describe(percentiles=[0.25, 0.5, 0.75])
    all_total_requests = user_status_df['total_requests'].sum()
    all_avg = all_users_stats['mean']
    all_p25 = all_users_stats['25%']
    all_p50 = all_users_stats['50%']
    all_p75 = all_users_stats['75%']

    output.append("REQUEST STATISTICS (Last 7 Days):")
    output.append(f"{'Metric':<8} {'CLI Users':<12} {'Non-CLI Users':<15} {'All Users':<12}")
    output.append("-" * 50)
    output.append(f"{'Total':<8} {cli_total_requests:<12,} {non_cli_total_requests:<15,} {all_total_requests:<12,}")
    output.append(f"{'Avg':<8} {cli_avg:<12.1f} {non_cli_avg:<15.1f} {all_avg:<12.1f}")
    output.append(f"{'P25':<8} {cli_p25:<12.1f} {non_cli_p25:<15.1f} {all_p25:<12.1f}")
    output.append(f"{'P50':<8} {cli_p50:<12.1f} {non_cli_p50:<15.1f} {all_p50:<12.1f}")
    output.append(f"{'P75':<8} {cli_p75:<12.1f} {non_cli_p75:<15.1f} {all_p75:<12.1f}")
    output.append("")

    # Comparison ratios
    output.append("COMPARISON RATIOS (CLI vs Non-CLI):")
    ratio_parts = []
    if non_cli_avg > 0:
        avg_ratio = cli_avg / non_cli_avg
        ratio_parts.append(f"Avg: {avg_ratio:.1f}x")
    if non_cli_p25 > 0:
        p25_ratio = cli_p25 / non_cli_p25
        ratio_parts.append(f"P25: {p25_ratio:.1f}x")
    if non_cli_p50 > 0:
        p50_ratio = cli_p50 / non_cli_p50
        ratio_parts.append(f"P50: {p50_ratio:.1f}x")
    if non_cli_p75 > 0:
        p75_ratio = cli_p75 / non_cli_p75
        ratio_parts.append(f"P75: {p75_ratio:.1f}x")

    if ratio_parts:
        output.append(f"  {' | '.join(ratio_parts)}")

    output.append("")

    output.append("")

    return "\n".join(output)


def analyze_user_agent_breakdown(user_agent_df: pd.DataFrame, tenant: str = None) -> str:
    """Analyze and format the user agent breakdown for CLI vs non-CLI users."""

    output = []
    output.append("=" * 80)
    if tenant:
        output.append(f"USER AGENT BREAKDOWN (LAST 7 DAYS) - {tenant.upper()}")
    else:
        output.append("USER AGENT BREAKDOWN (LAST 7 DAYS)")
    output.append("=" * 80)
    output.append("")

    if user_agent_df.empty:
        output.append("No user agent data available.")
        return "\n".join(output)

    # Calculate totals for percentages
    cli_total = user_agent_df[user_agent_df['user_type'] == 'CLI_USER']['request_count'].sum()
    non_cli_total = user_agent_df[user_agent_df['user_type'] == 'NON_CLI_USER']['request_count'].sum()

    # Get unique user agents
    all_agents = sorted(user_agent_df['user_agent_base'].unique())

    # Create table
    output.append(f"{'User Agent':<25} {'CLI Users':<15} {'Non-CLI Users':<15}")
    output.append(f"{'':25} {'Count':<7} {'%':<6} {'Count':<7} {'%':<6}")
    output.append("-" * 65)

    for agent in all_agents:
        cli_data = user_agent_df[(user_agent_df['user_type'] == 'CLI_USER') &
                                (user_agent_df['user_agent_base'] == agent)]
        non_cli_data = user_agent_df[(user_agent_df['user_type'] == 'NON_CLI_USER') &
                                    (user_agent_df['user_agent_base'] == agent)]

        cli_count = cli_data['request_count'].sum() if not cli_data.empty else 0
        non_cli_count = non_cli_data['request_count'].sum() if not non_cli_data.empty else 0

        cli_pct = (cli_count / cli_total * 100) if cli_total > 0 else 0
        non_cli_pct = (non_cli_count / non_cli_total * 100) if non_cli_total > 0 else 0

        output.append(f"{agent:<25} {cli_count:<7,} {cli_pct:<5.1f}% {non_cli_count:<7,} {non_cli_pct:<5.1f}%")

    # Add totals
    output.append("-" * 65)
    output.append(f"{'TOTAL':<25} {cli_total:<7,} {'100.0%':<6} {non_cli_total:<7,} {'100.0%':<6}")
    output.append("")

    return "\n".join(output)


def analyze_weekly_trends(weekly_df: pd.DataFrame, tenant: str = None) -> str:
    """Analyze and format weekly usage trends."""

    output = []
    output.append("=" * 80)
    if tenant:
        output.append(f"WEEK-OVER-WEEK USAGE TRENDS - {tenant.upper()}")
    else:
        output.append("WEEK-OVER-WEEK USAGE TRENDS")
    output.append("=" * 80)
    output.append("")

    if weekly_df.empty:
        output.append("No weekly data available.")
        return "\n".join(output)

    # If no specific tenant, aggregate across all tenants by period
    if not tenant:
        weekly_df = weekly_df.groupby('period_start').agg({
            'total_requests': 'sum',
            'unique_users': 'sum',
            'cli_requests': 'sum',
            'cli_users': 'sum'
        }).reset_index()

    # Sort by period
    weekly_df = weekly_df.sort_values('period_start')

    output.append(f"{'Period Start':<12} {'Total Req':<10} {'Users':<8} {'CLI Req':<8} {'CLI Users':<10} {'CLI %':<8}")
    output.append("-" * 70)

    for _, row in weekly_df.iterrows():
        period_str = row['period_start'].strftime('%Y-%m-%d')
        total_req = int(row['total_requests'])
        users = int(row['unique_users'])
        cli_req = int(row['cli_requests'])
        cli_users = int(row['cli_users'])
        cli_pct = (cli_req / total_req * 100) if total_req > 0 else 0

        output.append(f"{period_str:<12} {total_req:<10,} {users:<8,} {cli_req:<8,} {cli_users:<10,} {cli_pct:<7.1f}%")

    output.append("")

    # Calculate trends
    if len(weekly_df) >= 2:
        current_period = weekly_df.iloc[-1]  # Most recent period
        previous_period = weekly_df.iloc[-2]  # Period before current

        # Current vs previous period change
        total_req_change = ((current_period['total_requests'] - previous_period['total_requests']) /
                           previous_period['total_requests'] * 100) if previous_period['total_requests'] > 0 else 0
        cli_req_change = ((current_period['cli_requests'] - previous_period['cli_requests']) /
                         previous_period['cli_requests'] * 100) if previous_period['cli_requests'] > 0 else 0
        total_users_change = ((current_period['unique_users'] - previous_period['unique_users']) /
                             previous_period['unique_users'] * 100) if previous_period['unique_users'] > 0 else 0
        cli_users_change = ((current_period['cli_users'] - previous_period['cli_users']) /
                           previous_period['cli_users'] * 100) if previous_period['cli_users'] > 0 else 0

        output.append("TRENDS:")

        # Previous period vs period before that (if we have enough data)
        if len(weekly_df) >= 3:
            period_before_previous = weekly_df.iloc[-3]

            prev_total_req_change = ((previous_period['total_requests'] - period_before_previous['total_requests']) /
                                   period_before_previous['total_requests'] * 100) if period_before_previous['total_requests'] > 0 else 0
            prev_cli_req_change = ((previous_period['cli_requests'] - period_before_previous['cli_requests']) /
                                 period_before_previous['cli_requests'] * 100) if period_before_previous['cli_requests'] > 0 else 0
            prev_total_users_change = ((previous_period['unique_users'] - period_before_previous['unique_users']) /
                                     period_before_previous['unique_users'] * 100) if period_before_previous['unique_users'] > 0 else 0
            prev_cli_users_change = ((previous_period['cli_users'] - period_before_previous['cli_users']) /
                                   period_before_previous['cli_users'] * 100) if period_before_previous['cli_users'] > 0 else 0

            output.append(f"  Total requests: {total_req_change:+.1f}% this period, previous period {prev_total_req_change:+.1f}%")
            output.append(f"  CLI requests: {cli_req_change:+.1f}% this period, previous period {prev_cli_req_change:+.1f}%")
            output.append(f"  Total users: {total_users_change:+.1f}% this period, previous period {prev_total_users_change:+.1f}%")
            output.append(f"  CLI users: {cli_users_change:+.1f}% this period, previous period {prev_cli_users_change:+.1f}%")
        else:
            # Fallback if we don't have enough data for previous comparison
            output.append(f"  Total requests: {total_req_change:+.1f}% change from previous period")
            output.append(f"  CLI requests: {cli_req_change:+.1f}% change from previous period")
            output.append(f"  Total users: {total_users_change:+.1f}% change from previous period")
            output.append(f"  CLI users: {cli_users_change:+.1f}% change from previous period")

        output.append("")

    return "\n".join(output)


def analyze_cli_impact(cli_impact_df: pd.DataFrame, tenant: str = None) -> str:
    """Analyze whether CLI drives usage increases or if CLI users are just power users.

    Compares usage exactly 7 days before CLI adoption vs exactly 7 days after adoption.
    Requires full 7-day windows on both sides. Includes zero-request days in averages.
    """

    output = []
    output.append("=" * 80)
    if tenant:
        output.append(f"CLI IMPACT ANALYSIS: BEFORE vs AFTER ADOPTION - {tenant.upper()}")
    else:
        output.append("CLI IMPACT ANALYSIS: BEFORE vs AFTER ADOPTION")
    output.append("=" * 80)
    output.append("")
    output.append("This analysis compares daily usage patterns before vs after CLI adoption.")
    output.append("Uses exactly 7 days before and exactly 7 days after adoption.")
    output.append("Zero-request days are included in averages for accurate daily usage calculation.")
    output.append("")

    if cli_impact_df.empty:
        output.append("No CLI adoption data available for analysis.")
        return "\n".join(output)

    # Filter for users who have both pre and post CLI data (adopted CLI during analysis period)
    cli_adopters = cli_impact_df[cli_impact_df['usage_period'].isin(['PRE_CLI', 'POST_CLI'])]

    if cli_adopters.empty:
        output.append("No users found who adopted CLI during the analysis period.")
        return "\n".join(output)

    # Get users who have both pre and post data (at least 1 day in each period)
    users_with_both_periods = (
        cli_adopters.groupby(['tenant', 'opaque_user_id'])['usage_period']
        .nunique()
        .reset_index()
    )
    users_with_both_periods = users_with_both_periods[users_with_both_periods['usage_period'] == 2]

    if users_with_both_periods.empty:
        output.append("No users found with complete 7-day windows before and after CLI adoption.")
        output.append("Users need to have adopted CLI at least 7 days after analysis start and 7 days before analysis end.")
        return "\n".join(output)

    # Filter to only users with both periods
    valid_user_ids = users_with_both_periods[['tenant', 'opaque_user_id']]
    cli_adopters_filtered = cli_adopters.merge(
        valid_user_ids,
        on=['tenant', 'opaque_user_id'],
        how='inner'
    )

    # Calculate pre/post statistics for CLI adopters
    pre_cli_stats = (
        cli_adopters_filtered[cli_adopters_filtered['usage_period'] == 'PRE_CLI']
        .groupby(['tenant', 'opaque_user_id'])['daily_requests']
        .agg(['mean', 'sum', 'count'])
        .reset_index()
    )
    pre_cli_stats.columns = ['tenant', 'opaque_user_id', 'pre_avg_daily', 'pre_total', 'pre_days']

    post_cli_stats = (
        cli_adopters_filtered[cli_adopters_filtered['usage_period'] == 'POST_CLI']
        .groupby(['tenant', 'opaque_user_id'])['daily_requests']
        .agg(['mean', 'sum', 'count'])
        .reset_index()
    )
    post_cli_stats.columns = ['tenant', 'opaque_user_id', 'post_avg_daily', 'post_total', 'post_days']

    # Merge pre and post stats
    cli_comparison = pre_cli_stats.merge(post_cli_stats, on=['tenant', 'opaque_user_id'])

    # Calculate percentage changes (handle division by zero)
    cli_comparison['avg_daily_change_pct'] = cli_comparison.apply(
        lambda row: (
            ((row['post_avg_daily'] - row['pre_avg_daily']) / row['pre_avg_daily'] * 100)
            if row['pre_avg_daily'] > 0
            else (float('inf') if row['post_avg_daily'] > 0 else 0)
        ),
        axis=1
    )

    # Filter out infinite values for statistical calculations
    finite_changes = cli_comparison[cli_comparison['avg_daily_change_pct'] != float('inf')]['avg_daily_change_pct']

    # Get control group (non-CLI users) for the same time periods
    non_cli_users = cli_impact_df[cli_impact_df['usage_period'] == 'NON_CLI_USER']

    # Get user categories for users with zero pre-CLI usage
    zero_pre_users = cli_adopters_filtered[
        (cli_adopters_filtered['usage_period'] == 'POST_CLI') &
        (cli_adopters_filtered['opaque_user_id'].isin(
            cli_comparison[cli_comparison['avg_daily_change_pct'] == float('inf')]['opaque_user_id']
        ))
    ].drop_duplicates(['tenant', 'opaque_user_id'])

    truly_new_users = zero_pre_users[zero_pre_users['user_category'] == 'NEW_USER'].shape[0]
    resurrected_users = zero_pre_users[zero_pre_users['user_category'] == 'EXISTING_USER'].shape[0]

    # Calculate overall statistics
    num_cli_adopters = len(cli_comparison)
    users_with_zero_pre = (cli_comparison['avg_daily_change_pct'] == float('inf')).sum()

    # Properly categorize users (don't double-count new users)
    users_with_finite_increase = ((cli_comparison['avg_daily_change_pct'] > 0) &
                                  (cli_comparison['avg_daily_change_pct'] != float('inf'))).sum()
    users_decreased = (cli_comparison['avg_daily_change_pct'] < 0).sum()
    users_no_change = (cli_comparison['avg_daily_change_pct'] == 0).sum()

    # Total users who increased (including new and resurrected users)
    total_users_increased = users_with_finite_increase + users_with_zero_pre

    output.append(f"CLI ADOPTION IMPACT ANALYSIS:")
    output.append(f"  Users who adopted CLI during analysis period: {num_cli_adopters}")
    output.append(f"  Users with increased usage after CLI: {total_users_increased} ({total_users_increased/num_cli_adopters*100:.1f}%)")
    output.append(f"    - Existing users who increased: {users_with_finite_increase}")
    output.append(f"    - New users (first time using Augment): {truly_new_users}")
    output.append(f"    - Resurrected users (no usage in 7 days before CLI): {resurrected_users}")
    output.append(f"  Users with decreased usage after CLI: {users_decreased} ({users_decreased/num_cli_adopters*100:.1f}%)")
    if users_no_change > 0:
        output.append(f"  Users with no change in usage: {users_no_change} ({users_no_change/num_cli_adopters*100:.1f}%)")
    output.append("")

    # Show percentile breakdown (only for existing users, excludes new users)
    if len(finite_changes) > 0:
        percentiles = [10, 25, 50, 75, 90]
        output.append(f"CHANGE DISTRIBUTION (existing users only, excludes new users):")
        for p in percentiles:
            pct_val = finite_changes.quantile(p/100)
            output.append(f"  P{p}: {pct_val:+.1f}%")
        output.append("")

    # Pre vs Post comparison
    pre_avg = cli_comparison['pre_avg_daily'].mean()
    post_avg = cli_comparison['post_avg_daily'].mean()

    # Handle division by zero for overall change
    if pre_avg > 0:
        overall_change = (post_avg - pre_avg) / pre_avg * 100
    else:
        overall_change = float('inf') if post_avg > 0 else 0

    output.append(f"AGGREGATE COMPARISON (before vs after adoption):")
    output.append(f"  Average daily requests before CLI: {pre_avg:.1f} (includes zero-request days)")
    output.append(f"  Average daily requests after CLI: {post_avg:.1f} (includes zero-request days)")
    if overall_change == float('inf'):
        output.append(f"  Overall change: New usage (from 0 to {post_avg:.1f} requests/day)")
    else:
        output.append(f"  Overall change: {overall_change:+.1f}%")
    output.append("")

    # New and resurrected user analysis
    if users_with_zero_pre > 0:
        # Calculate average requests for users with zero pre-CLI usage
        zero_pre_comparison = cli_comparison[cli_comparison['avg_daily_change_pct'] == float('inf')]

        if truly_new_users > 0:
            # Get truly new users' post-CLI usage
            truly_new_data = zero_pre_users[zero_pre_users['user_category'] == 'NEW_USER']
            truly_new_user_ids = truly_new_data[['tenant', 'opaque_user_id']]
            truly_new_usage = cli_comparison.merge(truly_new_user_ids, on=['tenant', 'opaque_user_id'])
            truly_new_avg = truly_new_usage['post_total'].mean()

            output.append(f"NEW USER ANALYSIS:")
            output.append(f"  Truly new users (first time using Augment): {truly_new_users}")
            output.append(f"  Average requests per new user (7-day window): {truly_new_avg:.1f}")
            output.append("")

        if resurrected_users > 0:
            # Get resurrected users' post-CLI usage
            resurrected_data = zero_pre_users[zero_pre_users['user_category'] == 'EXISTING_USER']
            resurrected_user_ids = resurrected_data[['tenant', 'opaque_user_id']]
            resurrected_usage = cli_comparison.merge(resurrected_user_ids, on=['tenant', 'opaque_user_id'])
            resurrected_avg = resurrected_usage['post_total'].mean()

            output.append(f"RESURRECTED USER ANALYSIS:")
            output.append(f"  Resurrected users (no usage 7 days before CLI): {resurrected_users}")
            output.append(f"  Average requests per resurrected user (7-day window): {resurrected_avg:.1f}")
            output.append("")

    # Control group comparison (if available)
    if not non_cli_users.empty:
        # Calculate control group stats for same time periods as CLI adopters
        control_avg = non_cli_users['daily_requests'].mean()
        output.append(f"CONTROL GROUP (Non-CLI Users):")
        output.append(f"  Average daily requests: {control_avg:.1f}")
        output.append(f"  CLI adopters post-adoption vs Control: {post_avg/control_avg:.1f}x higher usage")
        output.append("")



    return "\n".join(output)




def main():
    parser = argparse.ArgumentParser(description="Deep dive analysis for one or more tenants' CLI usage")
    parser.add_argument(
        "tenants",
        type=str,
        nargs='*',
        help="Tenant name(s) to analyze (space-separated for multiple tenants). If not provided, will analyze all tenants with CLI usage."
    )
    parser.add_argument(
        "--tenant-view",
        action="store_true",
        help="Show separate analysis for each tenant (default: combine all tenants)",
        default=False,
    )
    args = parser.parse_args()

    # Setup BigQuery client
    gcp_creds, _ = get_gcp_creds()
    client = bigquery.Client(project="system-services-prod", credentials=gcp_creds)

    # Fixed date range for analysis: last 7 complete days (excluding today) in Pacific Time
    end_date = datetime.now(PACIFIC_TZ).replace(hour=0, minute=0, second=0, microsecond=0)  # Start of today in Pacific
    start_date = end_date - timedelta(days=7)  # 7 days before start of today in Pacific

    # If no tenants provided, fetch all tenants with CLI usage
    if not args.tenants:
        print("No tenants specified. Fetching all tenants with CLI usage...")
        args.tenants = get_cli_tenants(client)
        if not args.tenants:
            print("No tenants found with CLI usage in the specified date range.")
            return
        print(f"Found {len(args.tenants)} tenants with CLI usage: {', '.join(args.tenants)}")

    tenants_str = ", ".join(args.tenants)
    print(f"Analyzing tenant(s) '{tenants_str}' from {start_date.date()} to {end_date.date()}")
    print("Querying BigQuery...")

    # Get the data
    print("1. Getting user CLI status...")
    user_status_df = get_user_cli_status(client, args.tenants, start_date, end_date)

    print("2. Getting user agent breakdown...")
    user_agent_df = get_user_agent_breakdown(client, args.tenants, start_date, end_date)

    print("3. Getting weekly usage trends...")
    weekly_trends_df = get_weekly_usage_trends(client, args.tenants, weeks_back=4)

    print("4. Getting CLI adoption impact analysis...")
    cli_impact_df = get_cli_adoption_impact_analysis(client, args.tenants, weeks_back=8)



    if user_status_df.empty:
        print(f"No usage data found for tenant(s) '{tenants_str}' in the specified date range.")
        return

    all_results = []

    if not args.tenant_view:
        # Combined analysis (default behavior)
        tenant_label = "COMBINED" if len(args.tenants) > 1 else args.tenants[0].upper()

        # Generate analyses
        user_comparison_analysis = analyze_user_comparison(user_status_df)
        user_agent_analysis = analyze_user_agent_breakdown(user_agent_df)
        weekly_trends_analysis = analyze_weekly_trends(weekly_trends_df)
        cli_impact_analysis = analyze_cli_impact(cli_impact_df)

        # Combine all results
        results = "\n".join([
            f"TENANT USAGE ANALYSIS: {tenant_label}",
            f"Tenants: {', '.join(args.tenants)}",
            f"Analysis Period: {start_date.date()} to {end_date.date()}",
            f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            "",
            user_comparison_analysis,
            "",
            user_agent_analysis,
            "",
            weekly_trends_analysis,
            "",
            cli_impact_analysis
        ])

        all_results.append(results)
    else:
        # Separate analysis for each tenant
        for tenant in args.tenants:
            tenant_user_status = user_status_df[user_status_df['tenant'] == tenant]
            tenant_user_agent = user_agent_df[user_agent_df['user_type'].isin(['CLI_USER', 'NON_CLI_USER'])]
            tenant_weekly_trends = weekly_trends_df[weekly_trends_df['tenant'] == tenant]
            tenant_cli_impact = cli_impact_df[cli_impact_df['tenant'] == tenant]

            if tenant_user_status.empty:
                print(f"No usage data found for tenant '{tenant}' in the specified date range.")
                continue

            # Generate analyses for this tenant
            user_comparison_analysis = analyze_user_comparison(tenant_user_status, tenant)
            user_agent_analysis = analyze_user_agent_breakdown(tenant_user_agent, tenant)
            weekly_trends_analysis = analyze_weekly_trends(tenant_weekly_trends, tenant)
            cli_impact_analysis = analyze_cli_impact(tenant_cli_impact, tenant)

            # Combine results for this tenant
            tenant_results = "\n".join([
                f"TENANT USAGE ANALYSIS: {tenant.upper()}",
                f"Analysis Period: {start_date.date()} to {end_date.date()}",
                f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
                "",
                user_comparison_analysis,
                "",
                user_agent_analysis,
                "",
                weekly_trends_analysis,
                "",
                cli_impact_analysis
            ])

            all_results.append(tenant_results)

    # Print all results
    final_output = "\n\n" + "="*100 + "\n\n".join(all_results)
    print(final_output)

if __name__ == "__main__":
    main()
