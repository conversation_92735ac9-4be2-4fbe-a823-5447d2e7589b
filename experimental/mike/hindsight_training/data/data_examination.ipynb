#%%
import polars as pl
import pickle

# parquet_path = "/mnt/efs/augment/user/mike/test_vanguard/test_vanguard_permissive/mixdata/mix_empty10"

# df = pl.read_parquet(parquet_path)
# samples = df.head(20)

# print(samples.columns)
# for sample in samples.iter_rows(named=True):
#     pause_spans = pickle.loads(sample["middle_spans"])
#     pretty_print_pause_spans(
#         sample["file_path"],
#         sample["prefix"],
#         sample["suffix"],
#         sample["original_ground_truth"],
#         [pause_span.content.range for pause_span in pause_spans],
#     )
#%% md
# Hindsight Training Data Examination
#%%
# import MMIndexedDataset
from megatron.data.indexed_dataset import MMapIndexedDataset

eval_dataset_path = "/mnt/efs/augment/user/mike/hindsight_pipeline/dogfood/dogfood_groundtruth_2024-11-01_2025-01-14/indexed_dataset/8352b_1024s_1024r_2048d/dataset"
eval_dataset = MMapIndexedDataset(eval_dataset_path, skip_warmup=True)
print(len(eval_dataset))

old_eval_dataset_path = "/mnt/efs/augment/user/pranay/hindsight/dogfood_2024-11-15_2024-12-14/datasets/8352b_1024s_1024r_2048d_1976l/dataset"
old_eval_dataset = MMapIndexedDataset(old_eval_dataset_path, skip_warmup=True)
print(len(old_eval_dataset))

train_dataset_path = "/mnt/efs/augment/user/mike/hindsight_pipeline/vanguard/vanguard_permissive_2024-11-01_2025-01-14/indexed_dataset/mix_empty15_8352b_1024s_1024r_2048d/dataset"
train_dataset = MMapIndexedDataset(train_dataset_path, skip_warmup=True)
print(len(train_dataset))

old_train_dataset_path = "/mnt/efs/augment/user/pranay/hindsight/datasets/vanguard_permissive_mix_empty15_2024-11-01_2025-01-14/i0-vanguard0/datasets/8352b_1024s_1024r_2048d_1976l/dataset"
old_train_dataset = MMapIndexedDataset(old_train_dataset_path, skip_warmup=True)
print(len(old_train_dataset))
#%%
from base.tokenizers.qwen25coder_tokenizer import Qwen25CoderTokenizer
import numpy as np
from colorama import Fore, Style

tokenizer = Qwen25CoderTokenizer()


# print(f"fim token: {tokenizer.special_tokens.fim_middle}, pause token: {tokenizer.special_tokens.pause}, eos token: {tokenizer.special_tokens.eos}, padding token: {tokenizer.special_tokens.padding}")
def get_special_token_stats(dataset):
    for s in dataset[:2000]:
        if tokenizer.special_tokens.padding in s:
            s = s[: list(s).index(tokenizer.special_tokens.padding)]
        fim_indices = np.where(s == tokenizer.special_tokens.fim_middle)
        padding_indices = np.where(s == tokenizer.special_tokens.padding)
        pause_indices = np.where(s == tokenizer.special_tokens.pause)
        eos_indices = np.where(s == tokenizer.special_tokens.eos)
        if len(pause_indices[0]) > 3:
            print(
                f"{Fore.YELLOW}fim: {len(fim_indices[0])}, pause: {len(pause_indices[0])}, eos: {len(eos_indices[0])}, padding: {len(padding_indices[0])}{Style.RESET_ALL}"
            )
            print(f"pause indices: {pause_indices}")
            print(f"eos indices: {eos_indices}")
            print("long pauses:")
            s = s.tolist()
            s = s[
                : s.index(tokenizer.special_tokens.padding)
                if tokenizer.special_tokens.padding in s
                else len(s)
            ]
            fim_idx = s.index(tokenizer.special_tokens.fim_middle)
            prefix = s[: fim_idx + 1]
            suffix = s[fim_idx + 1 :]
            print(
                f"{''.join(tokenizer.detokenize(prefix).splitlines(keepends=True)[-5:])}",
                end="",
            )
            print(f"{Fore.RED}{tokenizer.detokenize(suffix)}{Style.RESET_ALL}")


# print("old train dataset")
# get_special_token_stats(old_train_dataset)
# print("new train dataset")
# get_special_token_stats(train_dataset)
# print("old eval dataset")
# get_special_token_stats(old_eval_dataset)
print("new eval dataset")
get_special_token_stats(eval_dataset)
#%% md
# Hindsight data examination
#%%
%load_ext autoreload
%autoreload 2

from base.datasets.hindsight_completion import HindsightCompletionDatum
from experimental.pranay.hindsight_training.pause_behavior import (
    load_hindsight_data,
)
from research.eval.harness.utils import read_jsonl_zst

from research.static_analysis.parsing import GlobalTsParser
from pathlib import Path


def get_model_stats(path: str):
    data = read_jsonl_zst(Path(path))
    model_stats = {}
    for datum in data:
        model = datum["completion"]["response"]["model"]
        if model not in model_stats:
            model_stats[model] = 0
        model_stats[model] += 1
    return model_stats
#%%
from multiprocessing import Pool

h0412 = "/mnt/efs/augment/data/eval/hindsight/2025-04-12-5d-v1.6/dogfood-shard/data.jsonl.zst"
h0115 = "/mnt/efs/augment/data/eval/hindsight/2025-01-15-14d-v1.4/dogfood-shard/data.jsonl.zst"
h1117 = "/mnt/efs/augment/data/eval/hindsight/2024-11-17-18d-v1.3/dogfood-shard/data.jsonl.zst"


with Pool(5) as p:
    results = p.map(get_model_stats, [h0412, h0115, h1117])
    for result in results:
        print(result)
#%%
print(results)