package cli

import (
	"context"
	"fmt"
	"os"
	"os/exec"
	"strings"
	"syscall"

	"github.com/spf13/cobra"

	"github.com/augmentcode/augment/infra/lib/logger"
	"github.com/augmentcode/augment/research/infra/lib/augment"
	"github.com/augmentcode/augment/tools/bzl/buildinfo"
)

const (
	augiRoot     = "research/infra"
	augiMain     = "augi.go"
	augiBazel    = "//research/infra:augi"
	augiBazelOut = "bazel-bin/" + augiRoot + "/augi_/augi" // NOTE(mattm): There's a "proper" way to get this path if this becomes flaky.
	augiTmp      = "/tmp/augi"
)

var bgrp = &cobra.Group{
	ID:    "root_build",
	Title: "Build and Release Commands",
}

func init() {
	mgrp := &cobra.Group{
		ID:    "root_meta",
		Title: "Metadata Commands",
	}
	rootCmd.AddGroup(bgrp, mgrp)

	addCommand(rootCmd, bgrp, buildCmd())
	addCommand(rootCmd, bgrp, installCmd(), ADMIN)
	addCommand(rootCmd, mgrp, whoamiCmd())
	addCommand(rootCmd, mgrp, repoCmd())
	addCommand(rootCmd, mgrp, versionCmd())
}

func buildTmp(ctx context.Context, path, version string, native, update bool) error {
	bazel := !native

	// Some of the "update" commands need to know the source tree of `augi`.
	sourceDir, err := augment.Dir(augiRoot)
	if err != nil {
		return err
	}

	// Common exec.CommandContext() for building
	log := logger.New(nil)
	run := func(env map[string]string, name string, args ...string) error {
		log.LogInfo("Running: %s %s", name, strings.Join(args, " "))
		cmd := exec.CommandContext(ctx, name, args...)
		cmd.Dir = sourceDir // This is only necessary for `go get`, but harmless for the others.
		cmd.Stdout = os.Stdout
		cmd.Stderr = os.Stderr
		if env != nil {
			environ := os.Environ()
			for k, v := range env {
				environ = append(environ, fmt.Sprintf("%s=%s", k, v))
			}
			cmd.Env = environ
		}
		return cmd.Run()
	}

	// When requested (the default), update the workspace. For native, this is simply a call
	// to 'go get' to update `go.mod` and `go.sum`. For bazel, additionaly call `bazel mod tidy`
	// to update `MODULE.bazel` and `gazelle` to update `BUILD` file(s). Where possible, limit
	// scope to the `//research/infra` directory.
	if update {
		if err := run(nil, "go", "get"); err != nil {
			return err
		}
	}
	if update && bazel {
		if err := run(nil, "bazel", "mod", "tidy"); err != nil {
			return err
		}
		if err := run(nil, "bazel", "run", "//:gazelle", "--", "update", sourceDir); err != nil {
			return err
		}
	}

	// A native `go build` is a bit simpler because we can pass `path` directly. When building with `bazel` we
	// need to copy from the `bazel-bin` dir to `path` (and forking `cp` is the easiest way to copy).
	if bazel {
		env := map[string]string{"STABLE_BUILD_VERSION": version}
		if outFile, err := augment.Dir(augiBazelOut); err != nil {
			return err
		} else if err := run(env, "bazel", "build", augiBazel); err != nil {
			return err
		} else {
			return run(nil, "cp", "-f", outFile, path)
		}
	} else {
		return run(nil, "go", "build", "-C", sourceDir, "-o", path, augiMain)
	}
}

func buildCmd() *cobra.Command {
	cmd := &cobra.Command{
		Use:     "build [-U] [--] [<cmdline>...]",
		Aliases: []string{"b"},
		Short:   "Build from source and run <cmdline>.",
		Args:    cobra.ArbitraryArgs,
	}
	flags := struct {
		native  bool
		update  bool
		version string
	}{}
	cmd.Flags().BoolVarP(&flags.native, "native", "N", false, "Use native 'go build' rather than bazel.")
	cmd.Flags().BoolVarP(&flags.update, "update", "U", false, "Update workspace with 'go get; bazel mod tidy; gazelle' prior to build.")
	cmd.Flags().StringVar(&flags.version, "version", "", "Set the 'version' build constant.")
	cmd.RunE = func(cmd *cobra.Command, args []string) error {
		if err := buildTmp(cmd.Context(), augiTmp, flags.version, flags.native, flags.update); err != nil {
			return err
		}
		return syscall.Exec(augiTmp, append([]string{augiTmp}, args...), os.Environ())
	}
	return cmd
}

func installCmd() *cobra.Command {
	cmd := &cobra.Command{
		Use:     "install",
		Aliases: []string{"b"},
		Short:   "Install self to system path using `sudo install`. (WARNING: Not typically needed with releases).",
		Args:    cobra.NoArgs,
	}

	flags := struct {
		dest string
		user bool
	}{}
	cmd.Flags().StringVarP(&flags.dest, "dest", "D", "/usr/local/bin/augi", "Install path.")
	cmd.Flags().BoolVarP(&flags.user, "user", "u", false, "Do not install as root.")

	cmd.RunE = func(cmd *cobra.Command, args []string) error {
		self, err := os.Executable()
		if err != nil {
			return err
		}

		install := []string{"install", "-m", "755"}
		if !flags.user {
			install = append([]string{"sudo"}, install...)
			install = append(install, "-o", "root", "-g", "root")
		}
		install = append(install, "-D", self, flags.dest)

		proc := exec.CommandContext(cmd.Context(), install[0], install[1:]...)
		proc.Stdout = os.Stdout
		proc.Stderr = os.Stderr
		return proc.Run()
	}
	return cmd
}

func whoamiCmd() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "whoami [--metadata | --hostname | --system | --augment]",
		Short: "Resolve an \"Augment\" username using one of a few sources.",
		Args:  cobra.NoArgs,
	}
	flags := struct {
		metadata bool
		hostname bool
		system   bool
		augment  bool
	}{}
	cmd.Flags().BoolVar(&flags.metadata, "metadata", false, "Use /startup/user as the source.")
	cmd.Flags().BoolVar(&flags.hostname, "hostname", false, "Use the system hostname as the source.")
	cmd.Flags().BoolVar(&flags.system, "system", false, "Use the current username as the source.")
	cmd.Flags().BoolVar(&flags.augment, "augment", false, "Use the 'name' field from ~/.augment/user.json.")
	cmd.RunE = func(cmd *cobra.Command, args []string) error {
		excl := func(bools ...bool) (sum int) {
			for _, b := range bools {
				if b {
					sum++
				}
			}
			return sum
		}(flags.metadata, flags.hostname, flags.system, flags.augment)

		if excl > 1 {
			return fmt.Errorf("only one of --metadata, --hostname, --system, or --augment is supported")
		}

		user := ""
		switch {
		case flags.metadata:
			user = augment.WhoAmIMetadata()
		case flags.hostname:
			user = augment.WhoAmIHostname()
		case flags.system:
			user = augment.WhoAmISystem()
		case flags.augment:
			user = augment.WhoAmIAugmentConfig()
		default:
			user = augment.WhoAmI()
		}
		if user == "" {
			return fmt.Errorf("No username could be resolved")
		}

		fmt.Println(user)
		return nil
	}
	return cmd
}

func repoCmd() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "repo [path...]",
		Short: "Resolve the path to the AUGMENT repo root.",
		Args:  cobra.ArbitraryArgs,
		RunE: func(cmd *cobra.Command, args []string) error {
			dir, err := augment.Dir(args...)
			if err != nil {
				return err
			}
			fmt.Println(dir)
			return nil
		},
	}
	return cmd
}

func versionCmd() *cobra.Command {
	cmd := &cobra.Command{
		Use:     "version [--simple|-s]",
		Aliases: []string{"ver", "v"},
		Short:   "Print build and version info.",
		Args:    cobra.NoArgs,
	}
	flags := struct {
		simple bool
	}{}
	cmd.Flags().BoolVarP(&flags.simple, "simple", "s", false, "Only print the version.")
	cmd.RunE = func(cmd *cobra.Command, args []string) error {
		info := buildinfo.Info
		if flags.simple {
			fmt.Println(info.BuildVersion())
		} else {
			bts := info.BuildTimestamp()
			cts := info.GitCommitTimestamp()
			ats := info.GitAuthorTimestamp()
			fmt.Printf("Version:       %s\n", info.BuildVersion())
			fmt.Printf("Timestamp:     %v (%d)\n", bts, bts.Unix())
			fmt.Printf("Builder:       %s\n", info.BuildUser())
			fmt.Printf("Hostname:      %s\n", info.BuildHost())
			fmt.Printf("Git Branch:    %s\n", info.GitBranch())
			fmt.Printf("Git Dirty:     %t\n", info.GitDirty())
			fmt.Printf("Git Commit:    %s (%s)\n", info.GitCommit(), info.GitCommitShort())
			fmt.Printf("Git Commit TS: %v (%d)\n", cts, cts.Unix())
			fmt.Printf("Git Author TS: %v (%d)\n", ats, ats.Unix())
		}
		return nil
	}
	return cmd
}
