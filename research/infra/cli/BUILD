load("@io_bazel_rules_go//go:def.bzl", "go_library")

package(default_visibility = ["//visibility:private"])

go_library(
    name = "cli",
    srcs = [
        "build.go",
        "clusters.go",
        "cw.go",
        "devpod.go",
        "gcs.go",
        "github.go",
        "k8s.go",
        "registry.go",
        "release.go",
        "root.go",
        "secrets.go",
        "ssh.go",
        "users.go",
    ],
    importpath = "github.com/augmentcode/augment/research/infra/cli",
    visibility = ["//research/infra:__pkg__"],
    deps = [
        "//infra/lib/docker",
        "//infra/lib/github",
        "//infra/lib/k8s",
        "//infra/lib/logger",
        "//infra/lib/ssh",
        "//infra/lib/utils",
        "//research/infra/cfg",
        "//research/infra/cfg/clusters",
        "//research/infra/lib/augment",
        "//research/infra/lib/augment/devpod",
        "//research/infra/lib/augment/devpod/crd/devpodv1",
        "//research/infra/lib/augment/devpod/ctrl",
        "//research/infra/lib/augment/devpod/spec",
        "//research/infra/lib/augment/release",
        "//research/infra/lib/augment/secrets",
        "//research/infra/lib/augment/tokens",
        "//research/infra/lib/coreweave",
        "//research/infra/lib/gcs",
        "//research/infra/lib/registry",
        "//tools/bzl/buildinfo:buildinfo_go",
        "@com_github_mattn_go_isatty//:go-isatty",
        "@com_github_spf13_cobra//:cobra",
        "@io_k8s_apimachinery//pkg/api/resource",
    ],
)
