local tmpl = import 'agent-workspaces.tmpl.jsonnet';

function(aH=null, stages=[2, 3, 4, 5])
  tmpl(
    aH=aH,
    cluster='gcp-eu-w4-prod-agent0',
    envs=['STAGING', 'PROD'],
    ws_dns='eu-west4.ws.augmentcode.com',
    ws_dns_parent='projects/agent-sandbox-prod/managedZones/ws-augmentcode-com',
    ws_dns_shard='c',
    glassbreakers=[
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
    ],
    stages=stages,
    enable_nat=true,
    nat_ip_count=2,
    min_ports_per_vm=1024,
  )
