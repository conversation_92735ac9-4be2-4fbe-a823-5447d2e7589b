local clusters = import '../clusters.jsonnet';

local bitnami_ss = import 'tmpl/bitnami-ss.jsonnet';
local cert_manager = import 'tmpl/cert-manager.jsonnet';
local config_connector = import 'tmpl/config-connector.jsonnet';
local external_dns = import 'tmpl/external-dns.jsonnet';
local gke_cluster = import 'tmpl/gke_cluster.jsonnet';
local haproxy = import 'tmpl/haproxy.jsonnet';
local kube_janitor = import 'tmpl/kube-janitor.jsonnet';
local otel = import 'tmpl/opentelemetry-collector.jsonnet';
local sysctl = import 'tmpl/sysctl.jsonnet';

local lib = import 'tmpl/lib.jsonnet';

// Outer (hosted on gcp-core0's ConfigConnector):
//   Stage0: The GKE Cluster Outer (Cluster, Networks, Node SA, NodePools)
//   Stage1: ConfigConnecter Outer (IAM ServiceAccount and Roles)
// Inner (internal K8s resources):
//   Stage2: ConfigConnecter Install (configconnector-operator-system)
//   Stage3: ConfigConnector and ConfigConnectorContext CRD (namespace config)
//   Stage4: The GKE Cluster Inner (StorageClass, NetDev, StorageClass)
//   Stage5: Marine Services + supporting ConfigConnector CRDs, DNS Zone, etc

function(
  cluster,
  envs,
  ws_dns,
  ws_dns_parent,
  ws_dns_shard=null,
  aH=null,
  cluster_desc_prefix='Agent Workspace',
  glassbreaker_owners=[
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
  ],
  glassbreakers=[],
  test_janitor=false,
  stages=[2, 3, 4, 5],
  enable_nat=true,
  nat_ip_count=1,
  min_ports_per_vm=null,
) {
  local H = if aH == null then clusters.cluster('gcp-core0') else aH,
  local C = clusters.cluster(cluster),

  envs_lower:: [std.asciiLower(env) for env in envs],
  envs_upper:: [std.asciiUpper(env) for env in envs],

  // Helper wrapper to apply the 'svc' NoSchedule toleration.
  local svcTolerate = function(x) lib.ApplyToleration(x, 'r.augmentcode.com/pool-type', 'svc'),

  //////////////////////////////////////////////////////////////////////////////
  //
  // Stage0: GKE Cluster Outer (Cluster, Networks, Node SA, NodePools)
  // Stage4: GKE Cluster Inner (StorageClass, NetDev, ...)
  //

  cluster:: gke_cluster + {
    host_cluster: H.name,
    host_namespace: 'cc-' + C.name,

    name:: C.name,
    project_id:: C.gcp_project,
    location:: C.gcp_region,
    primary_zone:: null,  // no primary zone, we don't need to migrate pods with pd-ssd among zones
    description:: '%s %s cluster in %s.' % [cluster_desc_prefix, C.name, C.gcp_region],
    num_nets:: 1,

    cpu_pool:: true,
    l4_pool:: false,
    t4_pool:: false,

    // NAT configuration
    enable_nat:: enable_nat,
    nat_ip_count:: nat_ip_count,
    min_ports_per_vm:: min_ports_per_vm,
  },
  stage0:: {
    outer: $.cluster.Outer + {
      cluster+: {
        spec+: {
          monitoringConfig+: {
            managedPrometheus+: {
              enabled: true,
            },
          },
        },
      },
      svc_pool+: {},
      cpu+: {
        machine_type:: 'c3-standard-44-lssd',
        initial_node_count:: 1,
        total_min_node_count:: 1,
        ephemeral_ssd_count:: 8,  // https://cloud.google.com/compute/docs/disks/local-ssd#lssd_disks_fixed
        spec+: {
          nodeConfig+: {
            // TODO(https://github.com/GoogleCloudPlatform/k8s-config-connector/issues/4285): advancedMachineFeatures: { nestedVirtualization: true }
            ephemeralStorageLocalSsdConfig+: {
              localSsdCount: 8,
            },
          },
        },
      },

    },
  },
  stage4:: {
    cluster_inner: $.cluster.Inner,
  },

  //////////////////////////////////////////////////////////////////////////////
  //
  // Stage1: ConfigConnecter Outer (IAM ServiceAccount and Roles)
  // Stage2: ConfigConnecter Install
  // Stage3: ConfigConnector Config
  //

  config_connector:: config_connector.InstallGlobal(C.k8s, contexts=[
    {
      H: H,
      C: C,
      namespace: $.cluster.host_namespace,
      gcp_target_project: C.gcp_project,
    },
  ]),
  stage1:: self.config_connector.Outer,
  stage2:: svcTolerate(self.config_connector.Install),
  stage3:: self.config_connector.Config,

  //////////////////////////////////////////////////////////////////////////////
  //
  // Stage5: Marines Resources
  //

  stage5:: C.k8s + {
    local k = self,

    ////////////////////////////////////////////////////////////////////////////
    //
    // Standard Namespaces ('aug-system' and '{cluster_name}')
    //

    // Default namespace, may go unused or be useful to tmp stuff.
    main_ns: k.Namespace + {
      name:: C.main_namespace,
    },

    // Augment-specific namespace, like kube-system.
    aug_system_ns: k.Namespace + {
      name:: 'aug-system',
    },

    ////////////////////////////////////////////////////////////////////////////
    //
    // RBAC (admin + users)
    //

    admin_rb: k.ClusterRoleBinding + {
      name:: 'aug:cluster-admins',
      role_name:: 'cluster-admin',
      groups:: ['<EMAIL>'],
    },

    root_rb: k.ClusterRoleBinding + {
      name:: 'aug:cluster-root',
      role_name:: 'cluster-admin',
      groups:: ['<EMAIL>'],
    },

    user_rbs: {
      local u = self,
      view: k.ClusterRoleBinding + {
        name:: 'aug:cluster-users',
        role_name:: 'view',
        groups:: ['<EMAIL>'],
      },
      view_extra_role: k.ClusterRole + {
        name:: 'aug:aggregate-to-view',
        metadata+: {
          labels+: {
            'rbac.authorization.k8s.io/aggregate-to-view': 'true',
          },
        },
        rules+: [
          {
            apiGroups: [''],
            resources: ['nodes'],
            verbs: k.READ_VERBS,
          },
        ],
      },
    },

    ////////////////////////////////////////////////////////////////////////////
    //
    // Bitnami Sealed Secrets
    //
    // This runs in the `kube-system` namespace to make it easy to run the `kubeseal` CLI.
    //

    bitnami_ss: {
      release: svcTolerate(bitnami_ss(C.k8s, name='bitnami-ss0', values={})),
    },

    ////////////////////////////////////////////////////////////////////////////
    //
    // GCP ConfigConnector
    //

    GCP+:: {
      Object+:: {
        project_id:: C.gcp_project,
        namespace:: $.cluster.host_namespace,
        metadata+: {
          labels: {},
        },
      },
    },

    ////////////////////////////////////////////////////////////////////////////
    //
    // Agent Workspace DNS Zone
    //

    agent_dns: {
      local a = self,
      zone: k.GCP.DNS.ManagedZone + {
        spec+: {
          dnsName: ws_dns + '.',
          description: 'Cluster-scoped zone for ' + C.name + ' Remote Agent Workspaces.',
          visibility: self.VISIBILITY.PUBLIC,
          dnssecConfig+: {
            state: 'off',
          },
        },
      },

      parent_ns_records: if ws_dns_shard != null then k.GCP.DNS.RecordSet + {
        spec+: {
          managedZoneRef+: { external: ws_dns_parent },
          name: a.zone.spec.dnsName,
          type: 'NS',
          rrdatas: [
            'ns-cloud-%s%d.googledomains.com.' % [ws_dns_shard, n]
            for n in std.range(1, 4)
          ],
        },
      },
    },

    ////////////////////////////////////////////////////////////////////////////
    //
    // External DNS
    //
    // This monitors Services (LoadBalancer) and Ingresses and creates A-records
    // in DNS. It's configured with an IAM ServiceAccount which can read all zones
    // in the project (to find the right one) and then write to zones where we want
    // to manage records.
    //

    external_dns: svcTolerate(external_dns(C.k8s, C.gcp_project, zones=[k.agent_dns.zone.spec.dnsName], namespace=k.aug_system_ns.metadata.name, owner_id=std.asciiLower(C.name)) + {
      sa+: {
        metadata+: {
          annotations+: {
            'iam.gke.io/gcp-service-account': '%s@%s.iam.gserviceaccount.com' % [k.external_dns_sa.sa.metadata.name, C.gcp_project],
          },
        },
      },
    }),

    external_dns_sa: {
      local s = self,
      sa: k.GCP.IAM.ServiceAccount + {
        std_name:: C.name + '-external-dns',
        alt_name:: C.name + '-extdns',
        name:: if std.length(self.std_name) > self.NAME_LIMIT then self.alt_name else self.std_name,
        description:: 'K8s External DNS',
      },
      sa_workload_identity: k.GCP.IAM.PolicyMember + {
        name:: s.sa.metadata.name + '.workload-identity-user',
        spec+: {
          resourceRef: s.sa.localKindRef,
          member: 'serviceAccount:%s.svc.id.goog[%s/%s]' % [C.gcp_project, k.external_dns.sa.metadata.namespace, k.external_dns.sa.metadata.name],
          role: 'roles/iam.workloadIdentityUser',
        },
      },
      sa_role_project_reader: k.GCP.IAM.PolicyMember + {
        name:: s.sa.metadata.name + '.' + C.gcp_project + '.reader',
        spec+: {
          resourceRef: {
            kind: 'Project',
            external: 'project/' + C.gcp_project,
          },
          memberFrom: self.memberFromSA(s.sa),
          role: 'roles/dns.reader',
        },
      },
      sa_role_zone_admin_agent_ws: k.GCP.IAM.PolicyMember + {
        name:: s.sa.metadata.name + '.' + C.name + '-agent-ws.admin',
        spec+: {
          resourceRef: k.agent_dns.zone.localKindRef,
          memberFrom: self.memberFromSA(s.sa),
          role: 'roles/dns.admin',
        },
      },
    },

    ////////////////////////////////////////////////////////////////////////////
    //
    // Cert Manager
    //
    // We configure a dns01 solver for the above zone(s). The dns01 solver is used
    // instead of http01 because 1) only dns01 supports wildcards and 2) it avoids a
    // dependency on the ingress controller.

    cert_manager: {
      local cm = self,
      release: svcTolerate(cert_manager(C.k8s, name='cert-manager0', namespace='cert-manager', values={
        serviceAccount+: {
          annotations+: {
            'iam.gke.io/gcp-service-account': '%s@%s.iam.gserviceaccount.com' % [cm.dns01_sa.metadata.name, C.gcp_project],
          },
        },
      })),
      // ServiceAccount for dns01 solver.
      dns01_sa: k.GCP.IAM.ServiceAccount + {
        std_name:: C.name + '-dns01-solver',
        alt_name:: C.name + '-dns01',
        name:: if std.length(self.std_name) > self.NAME_LIMIT then self.alt_name else self.std_name,
        description:: 'K8s CertManager (dns01 solver)',
      },
      // Workload Identity binding.
      dns01_workload_identity: k.GCP.IAM.PolicyMember + {
        name:: cm.dns01_sa.metadata.name + '.workload-identity-user',
        spec+: {
          resourceRef: cm.dns01_sa.localKindRef,
          member: 'serviceAccount:' + C.gcp_project + '.svc.id.goog[cert-manager/cert-manager0]',
          role: 'roles/iam.workloadIdentityUser',
        },
      },
      // Read all zones, so that the solver can find the correct zone.
      dns01_reader_role: k.GCP.IAM.PolicyMember + {
        name:: cm.dns01_sa.metadata.name + '.' + C.gcp_project + '.reader',
        spec+: {
          resourceRef: {
            kind: 'Project',
            external: 'project/' + C.gcp_project,
          },
          memberFrom: self.memberFromSA(cm.dns01_sa),
          role: 'roles/dns.reader',
        },
      },
      // Access to Agent WS DNS Zone
      dns01_role_cluster_agent_ws: k.GCP.IAM.PolicyMember + {
        name:: cm.dns01_sa.metadata.name + '.' + C.name + '-agent-ws.admin',
        spec+: {
          resourceRef: k.agent_dns.zone.localKindRef,
          memberFrom: self.memberFromSA(cm.dns01_sa),
          role: 'roles/dns.admin',
        },
      },
      dns01_issuer: k.ClusterIssuer + {
        name:: 'letsencrypt-prod',
        spec+: {
          acme: {
            privateKeySecretRef: {
              name: 'letsencrypt-account-key',
            },
            server: 'https://acme-v02.api.letsencrypt.org/directory',
            solvers: [
              {
                dns01: {
                  cloudDNS: {
                    project: C.gcp_project,
                  },
                },
              },
            ],
          },
        },
      },
      ss_issuer: k.ClusterIssuer + {
        name:: 'selfsigned',
        spec+: {
          selfSigned: {},
        },
      },
    },

    ////////////////////////////////////////////////////////////////////////////
    //
    // Image Primer
    //
    // Warm per-node image caches for the agent images. An
    // alternative to this on GKE could be to use the "Image Streaming" feature,
    // which mounts images from NFS.
    //

    image_primer: k.DaemonSet + {
      name:: 'image-primer',
      namespace:: C.sys_namespace,
      metadata+: {
        labels+: {
          'aug.service': 'image-primer',
        },
      },
      spec+: {
        template+: {
          metadata+: {
            labels+: {
              'aug.service': 'image-primer',
            },
          },
          spec+: {
            enableServiceLinks: false,
            restartPolicy: 'Always',
            tolerations: [
              {
                key: 'raws.augmentcode.com/pool-group',
                operator: 'Exists',
              },
              {
                key: 'nvidia.com/gpu',
                operator: 'Exists',
              },
            ],
            initContainers: std.flattenArrays([
              [
                k.Container + {
                  name: std.asciiLower(env) + '-virt',
                  env:: std.asciiUpper(env),
                  restartPolicy: 'Always',  // make this a sidecar container
                  image: '%s/augment-remote-agent-virt:%s' % [C.services_env[self.env].agent_image_registry, self.env],
                  imagePullPolicy: 'Always',
                  command: ['/bin/sleep', '1m'],
                },
                k.Container + {
                  name: std.asciiLower(env),
                  env:: std.asciiUpper(env),
                  restartPolicy: 'Always',  // make this a sidecar container
                  image: '%s/augment-remote-agent:%s' % [C.services_env[self.env].agent_image_registry, self.env],
                  imagePullPolicy: 'Always',
                  command: ['/bin/sleep', '1m'],
                },
              ]
              for env in envs
            ]),
            containers: [
              k.Container + {
                name: 'sleep',
                image: 'busybox:latest',
                command: ['/bin/sleep', 'infinity'],
              },
            ],
          },
        },
      },
    },

    ////////////////////////////////////////////////////////////////////////////
    //
    // Per-Node sysctl settings
    //

    sysctl: sysctl(C),

    //////////////////////////////////////////////////////////////////////////////
    //
    // Agent Docker Registries
    //

    eu_note:: if C.remote_passthrough then {
      // NOTE(mattm): CC support for REMOTE registries is limited. For EU we currently
      // ignore the value set here and use one created by hand in the ra `deploy.jsonnet`.
      // https://github.com/GoogleCloudPlatform/k8s-config-connector/issues/3720
      //
    },

    repos: {
      [env]: {
        local r = self,
        registry: k.GCP.ArtifactRegistry.Repository + {
          name:: 'agents-' + env + '-' + self.spec.location,
          spec+: {
            location: C.gcp_region,
            description: 'Remote Agent Images for ' + std.asciiUpper(env) + '.',
            format: self.FORMAT.DOCKER,
            dockerConfig: {
              immutableTags: false,
            },
            cleanupPolicyDryRun: false,
          },
        },
        image_pushers: if std.asciiUpper(env) == 'DEV' then C.k8s.GCP.IAM.PolicyMember + {
          name:: 'gcp-users' + '.' + r.registry.metadata.name + '.writer',
          namespace:: $.cluster.host_namespace,
          spec+: {
            resourceRef: {
              kind: r.registry.kind,
              external: r.registry.ref,
            },
            member: 'group:<EMAIL>',
            role: 'roles/artifactregistry.writer',
          },
        },
        testing_image_pushers: if env == 'DEV' then C.k8s.GCP.IAM.PolicyMember + {
          name:: 'testing' + '.' + r.registry.metadata.name + '.writer',
          namespace:: $.cluster.host_namespace,
          spec+: {
            resourceRef: {
              kind: r.registry.kind,
              external: r.registry.ref,
            },
            member: 'serviceAccount:<EMAIL>',
            role: 'roles/artifactregistry.writer',
          },
        },
        github_runner_image_pushers: C.k8s.GCP.IAM.PolicyMember + {
          name:: 'github-runner' + '.' + r.registry.metadata.name + '.writer',
          namespace:: $.cluster.host_namespace,
          spec+: {
            resourceRef: {
              kind: r.registry.kind,
              external: r.registry.ref,
            },
            member: 'serviceAccount:<EMAIL>',
            role: 'roles/artifactregistry.writer',
          },
        },
        image_pull: C.k8s.GCP.IAM.PolicyMember + {
          name:: $.cluster.Outer.node_sa.name + '.' + r.registry.metadata.name + '.reader',
          namespace:: $.cluster.host_namespace,
          spec+: {
            resourceRef: {
              kind: r.registry.kind,
              external: r.registry.ref,
            },
            member: 'serviceAccount:' + $.cluster.Outer.node_sa.email,
            role: 'roles/artifactregistry.reader',
          },
        },
        image_pull_users: C.k8s.GCP.IAM.PolicyMember + {
          name:: 'users-workload.' + r.registry.metadata.name + '.reader',
          namespace:: $.cluster.host_namespace,
          spec+: {
            resourceRef: {
              kind: r.registry.kind,
              external: r.registry.ref,
            },
            member: 'principalSet://iam.googleapis.com/projects/%d/locations/global/workloadIdentityPools/%s.svc.id.goog/kubernetes.cluster/https://container.googleapis.com/v1/projects/%s/locations/%s/clusters/%s' % [
              C.gcp_project_number,
              C.gcp_project,
              C.gcp_project,
              C.gcp_region,
              C.name,
            ],
            role: 'roles/artifactregistry.reader',
          },
        },
      }
      for env in $.envs_lower
    },

    ////////////////////////////////////////////////////////////////////////////
    //
    // Request Insight Publisher Service Account
    // referenced in the various RI topics
    //

    ra_ri_publisher_sa: {
      local s = self,
      sa: k.GCP.IAM.ServiceAccount + {
        name:: 'ra-ri-publisher',
        description:: 'Service account for publishing request insight events',
      },
      // Workload Identity binding for all service accounts in the cluster
      workload_identity: k.GCP.IAM.PolicyMember + {
        name:: s.sa.metadata.name + '.workload-identity-all-sas',
        spec+: {
          resourceRef: s.sa.localKindRef,
          // This grants access to all service accounts in the cluster
          member: 'principalSet://iam.googleapis.com/projects/%d/locations/global/workloadIdentityPools/%s.svc.id.goog/kubernetes.cluster/https://container.googleapis.com/v1/projects/%s/locations/%s/clusters/%s' % [
            C.gcp_project_number,
            C.gcp_project,
            C.gcp_project,
            C.gcp_region,
            C.name,
          ],
          role: 'roles/iam.workloadIdentityUser',
        },
      },
    },

    ////////////////////////////////////////////////////////////////////////////
    //
    // Agent Resources (maybe we'll put them somewhere else later).
    //
    // The remote_agents service runs in each prod/staging/dev namespace in the
    // normal services clusters. It launches workspaces in a corresponding
    // namespace in the agents cluster (1:1).
    //
    // **For now**, operationally, the remote_agent service creates its own target
    // namespace (and a service account) at startup. This may change in the future
    // and become part of a kubecfg deployment.
    //
    // The remote_agents service starts with creds for a shared `superuser` service-
    // account (one per prod/staging/dev). This account has permissions to create
    // the target namespace, a service account in `aug-system` named after the
    // target namespace (1:1), and a role binding inside the target namespace. These
    // resources are *applied* once at startup.
    //
    // After startup, the remote_agents service drops privileges to the fine-grained
    // service-account that it just created, which has membership in the role binding
    // just created, granting it permissions only in the namespace just created, to
    // a few resouce types to support users' remote agent workspaces. They are
    // secret, service, and stateful set.
    //

    remote_agents: {
      local a = self,

      sas: {
        [env]: {
          local s = self,
          sa: k.ServiceAccount + {
            name:: 'rwc-superuser-' + env,
            namespace:: k.aug_system_ns.metadata.name,
          },
          // This static token is sealed in remote_agent's deploy.jsonnet.
          tok: k.Secret + {
            i_know_what_im_doing:: true,  // =P
            name:: s.sa.metadata.name + '-tok',
            namespace:: s.sa.metadata.namespace,
            type: 'kubernetes.io/service-account-token',
            metadata+: {
              annotations+: {
                'kubernetes.io/service-account.name': s.sa.metadata.name,
              },
            },
          },
        }
        for env in $.envs_lower
      },

      superuser: {
        cr: k.ClusterRole + {
          name:: 'rwc-superuser',
          rules: [
            // The superuser creates user namespaces. DELETE is excluded, e.g., we
            // wouldn't want to delete "kube-system".
            self.Rule + {
              apiGroups: [''],
              resources: ['namespaces'],
              verbs: self.READ_VERBS + self.WRITE_VERBS_NO_DELETE,
            },
            // The superuser only needs to create rolebindings in the namespaces that
            // it creates. We don't have a way to exclude system namespaces.
            self.Rule + {
              apiGroups: ['rbac.authorization.k8s.io'],
              resources: ['rolebindings'],
              verbs: self.READ_VERBS + self.WRITE_VERBS_NO_DELETE,
            },
            // The superuser also needs to be able to update the `default` service acccount in a namespace,
            // to apply an IAM Workload Identity annotation.
            self.Rule + {
              apiGroups: [''],
              resources: ['serviceaccounts'],
              resourceNames: ['default'],
              verbs: self.READ_VERBS + self.WRITE_VERBS_NO_DELETE,
            },
            // Read events for emitting otel trace spans at startup/resume.
            self.Rule + {
              apiGroups: [''],
              resources: ['events'],
              verbs: self.READ_VERBS,
            },
          ],
        },
        r: k.Role + {
          name:: 'rwc-superuser',
          namespace:: k.aug_system_ns.metadata.name,
          rules: [
            // The superuser can create services accounts and tokens outside of the target namespace.
            // We use the "aug-system" namespace for this.
            self.Rule + {
              apiGroups: [''],
              resources: ['serviceaccounts', 'secrets'],
              verbs: self.READ_VERBS + self.WRITE_VERBS_NO_DELETE,
            },
            // The superuser will also need to create dynamic tokens for the above service account.
            self.Rule + {
              apiGroups: [''],
              resources: ['serviceaccounts/token'],
              verbs: [self.CREATE],
            },
          ],
        },
        // Each environment's superuser gets its own rb (for visibility) onto the above roles.
        role_bindings: [
          {
            cr: k.ClusterRoleBinding + {
              name:: 'rwc-superusers-' + env,
              role_name:: a.superuser.cr.name,
              sas: [a.sas[env].sa],
            },
            r: k.RoleBinding + {
              name:: 'rwc-superusers-' + env,
              namespace:: a.superuser.r.namespace,
              role_name:: a.superuser.r.name,
              sas: [a.sas[env].sa],
            },
          }
          for env in $.envs_lower
        ],
      },

      namespace_controller: {
        cr: k.ClusterRole + {
          name:: 'rwc-namespace-controller',
          rules: [
            // The fine-grained namespace controller needs to be able to manage
            // and delete workspaces for users. Currently workspaces are defined
            // as a Secret (the lead/owner), Service, and StatefulSet. (A Pod and
            // PVC also appear at runtime).
            self.Rule + {
              apiGroups: ['', 'apps'],
              resources: ['services', 'services/proxy', 'secrets', 'statefulsets', 'pods'],
              verbs: self.READ_VERBS + self.WRITE_VERBS,
            },
            // Read events for emitting otel trace spans at startup/resume.
            self.Rule + {
              apiGroups: [''],
              resources: ['events'],
              verbs: self.READ_VERBS,
            },
          ],
        },
        role_bindings: [
          {
            // Because the superuser grants permissions to fine-grained controllers, it
            // must have those permissions.
            cr: k.ClusterRoleBinding + {
              name:: 'rwc-namespace-controller-superusers-' + env,
              role_name:: a.namespace_controller.cr.name,
              sas: [a.sas[env].sa],
            },
          }
          for env in $.envs_lower
        ],
      },

      team_debuggers: {
        local team_debuggers = self,
        cr: k.ClusterRole + {
          name:: 'raws-debugger',
          rules: [
            self.Rule + {
              apiGroups: ['', 'apps'],
              resources: ['services', 'services/proxy', 'statefulsets', 'pods'],
              verbs: self.READ_VERBS,
            },
            self.Rule + {
              apiGroups: [''],
              resources: ['pods/logs'],
              verbs: self.READ_VERBS,
            },
          ],
        },
        // TODO(mattm): Per-namespace, dynamic rolebindings?
        binding: k.ClusterRoleBinding + {
          name:: 'raws-debuggers',
          role_name:: team_debuggers.cr.name,
          groups:: ['<EMAIL>'],
        },
      },
    },

    glassbreakers: [
      {
        local gb = self,
        // NOTE: This group needs to be <NAME_EMAIL> in order to be seen by k8s.
        group: k.GCP.CloudIdentity.Group + {
          email:: '<EMAIL>' % env,
          spec+: {
            displayName: 'Remote Agent Workspace Glassbreakers (%s)' % std.asciiUpper(env),
            description: 'Members with direct access to Remote Agent Workspaces such as to `kubectl exec`.',
          },
        },
        owners: [
          k.GCP.CloudIdentity.Membership + {
            group_obj:: gb.group,
            member_email:: m,
            role:: self.ROLE.OWNER,
          }
          for m in glassbreaker_owners
        ],
        members: [
          k.GCP.CloudIdentity.Membership + {
            group_obj:: gb.group,
            member_email:: m,
            role:: self.ROLE.MEMBER,
          }
          for m in glassbreakers
        ],
        cr: k.ClusterRole + {
          name:: 'ra-glassbreaker-%s' % env,
          rules: [
            self.Rule + {
              apiGroups: ['', 'apps'],
              resources: ['services', 'services/proxy', 'statefulsets', 'pods'],
              verbs: self.READ_VERBS,
            },
            self.Rule + {
              apiGroups: [''],
              resources: ['pods/logs'],
              verbs: self.READ_VERBS,
            },
            self.Rule + {
              apiGroups: [''],
              resources: ['pods/exec'],
              verbs: self.WRITE_VERBS_NO_DELETE,
            },
            self.Rule + {
              apiGroups: ['apps'],
              resources: ['statefulsets/scale'],
              verbs: self.WRITE_VERBS_NO_DELETE,
            },
          ],
        },
        // TODO(mattm): Drop this binding and add it per-namespace from `ApplyNamespace()`.
        binding: k.ClusterRoleBinding + {
          name:: 'ra-glassbreakers-%s' % env,
          role_name:: gb.cr.metadata.name,
          groups:: [gb.group.email],
        },
      }
      for env in $.envs_lower
    ],

    snapshots: {
      class: k.VolumeSnapshotClass + {
        name:: 'pd-image',
        driver: 'pd.csi.storage.gke.io',
        deletionPolicy: self.DELETION_POLICY.DELETE,
        parameters+: {
          'snapshot-type': 'images',  // https://cloud.google.com/kubernetes-engine/docs/how-to/persistent-volumes/backup-pd-volume-snapshots#restore-snapshot
        },
      },
    },

    proxy: {
      local proxy = self,
      num_shards:: 1,
      ns: k.Namespace + {
        name:: 'ws-ssh-proxy',
      },
      shards: [
        {
          local s = self,
          name:: 'ws-ssh-haproxy%02d' % [shard],
          hostname:: 'ws-proxy-%02d.%s' % [shard, ws_dns],
          fe_port:: 2222,
          be_port:: self.fe_port,
          helm: svcTolerate(haproxy(C.k8s, s.name, proxy.ns.name, probe_port=self.fe_port, values={
            containerPorts: {
              ssh: s.fe_port,
            },
            service+: {
              annotations+: {
                'external-dns.alpha.kubernetes.io/hostname': s.hostname,
              },
            },
            replicaCount: 3,
            affinity: {
              podAntiAffinity: {
                preferredDuringSchedulingIgnoredDuringExecution: [
                  {
                    weight: 100,
                    podAffinityTerm: {
                      labelSelector: {
                        matchLabels: {
                          'app.kubernetes.io/name': 'haproxy',
                          'app.kubernetes.io/instance': s.name,
                        },
                      },
                      topologyKey: 'kubernetes.io/hostname',
                    },
                  },
                ],
              },
            },
            resources+: {
              requests+: {
                cpu: '2',
                memory: '2Gi',
              },
              limits+: self.requests,
            },
            config: |||
              global
                log stdout format raw local0
                maxconn 10240
                stats socket /run/haproxy/runtime-api/admin.sock mode 660 level admin
                ssl-load-extra-files none

              defaults
                log global
                timeout client 1m
                timeout connect 1s
                timeout server 1m

              resolvers internal
                accepted_payload_size 8192
                parse-resolv-conf
                resolve_retries      3
                timeout resolve      1s
                timeout retry        1s
                hold other           30s
                hold refused         30s
                hold nx              30s
                hold timeout         30s
                hold valid           10s
                hold obsolete        30s

              frontend fe_ssh
                bind *:%(fe_port)d ssl crt /run/secrets/%(secret_name)s/tls-combined.pem
                mode tcp
                log-format "%(log_format)s"
                tcp-request inspect-delay 5s

                # Restrict to a specific set SNI name pattern (so that this can't be used for arbitrary K8s Services).
                tcp-request content reject if !{ ssl_fc_sni -m reg -- ^raws-[a-fA-F0-9-]{36}\.[a-zA-Z0-9-]{2,64}$ }

                # Restrict to SSH-looking payloads (off for now, for debugging).
                acl valid_payload req.payload(0,7) -m str "SSH-2.0"
                tcp-request content reject if !valid_payload
                tcp-request content accept if { req_ssl_hello_type 1 }

                # Build Hostname from SNI, then resolve to IP address.
                tcp-request content set-var(sess.dstName) ssl_fc_sni,concat(,,.svc.cluster.local)
                tcp-request content do-resolve(sess.dstIP,internal,ipv4) var(sess.dstName)

                default_backend be_ssh

              backend be_ssh
                mode tcp
                tcp-request content set-dst var(sess.dstIP)
                server ssh0 0.0.0.0:%(be_port)d
            ||| % {
              fe_port: s.fe_port,
              be_port: s.be_port,
              secret_name: s.cert.spec.secretName,
              log_format: '%ci:%cp [%t] %ft %b/%s %Tw/%Tc/%Tt %B %ts %ac/%fc/%bc/%sc/%rc %sq/%bq dstName:%[var(sess.dstName)] dstIP:%[var(sess.dstIP)] ',
            },
            // Do not use the checksum annotation trick to cause a rolling deployment update on config changes. Instead
            // use "reflex" to trigger haproxy's native config reload on config changes. This requires shareProcessNamespace.
            // Also, use Runtime API for cert updates. This works differently than the config reload. (A config reload would also do the trick, but the Runtime API method
            // is much more efficient).
            checksumConfigMap+: {
              // TODO(mattm): Fix (disable) when the helm chart makes it easier to avoid mounting config with a subPath.
              enabled: true,
            },
            shareProcessNamespace+: {
              enabled: true,
            },
            initContainers+: [
              k.Container + {
                name: 'config-reloader',
                restartPolicy: k.RESTART_POLICY.ALWAYS,  // make this a sidecar container
                image: 'acim/go-reflex:1.24.5',
                command: ['reflex', '-d', 'fancy'],
                workingDir: '/usr/local/etc/haproxy',
                args: [
                  '-svr',
                  '..data',
                  '--',
                  'bash',
                  '-c',
                  'pkill -SIGUSR2 haproxy',
                ],
                volumeMounts: [
                  {
                    name: 'haproxy-config',
                    mountPath: '/usr/local/etc/haproxy',
                  },
                ],
                resources: {
                  requests: {
                    cpu: '100m',
                    memory: '128Mi',
                  },
                  limits: self.requests,
                },
              },
              k.Container + {
                name: 'cert-reloader',
                restartPolicy: k.RESTART_POLICY.ALWAYS,  // make this a sidecar container
                image: 'acim/go-reflex:1.24.5',
                command: ['reflex', '-d', 'fancy'],
                workingDir: '/run/secrets/' + s.cert.spec.secretName,
                args: [
                  '-svr',
                  '..data',
                  '--',
                  'bash',
                  '-c',
                  |||
                    set -x
                    command -v socat &> /dev/null || { apt-get update && apt-get install -y socat ; }
                    {
                      echo "set ssl cert /run/secrets/%(secret_name)s/tls-combined.pem <<"
                      cat /run/secrets/%(secret_name)s/tls-combined.pem | sed '/^[[:space:]]*$/d'
                    } | socat - UNIX-CONNECT:/run/haproxy/runtime-api/admin.sock
                    echo "show ssl cert" | socat - UNIX-CONNECT:/run/haproxy/runtime-api/admin.sock
                    echo "commit ssl cert /run/secrets/%(secret_name)s/tls-combined.pem" | socat - UNIX-CONNECT:/run/haproxy/runtime-api/admin.sock
                    # NOTE(mattm): Just do a config reload because the above `set ssl cert` is failing silently to create a transaction.
                    pkill -SIGUSR2 haproxy
                  ||| % {
                    secret_name: s.cert.spec.secretName,
                  },
                ],
                volumeMounts: [
                  {
                    name: s.cert.spec.secretName,
                    mountPath: '/run/secrets/' + s.cert.spec.secretName,
                  },
                  {
                    name: 'runtime-api',
                    mountPath: '/run/haproxy/runtime-api',
                  },
                ],
                resources: {
                  requests: {
                    cpu: '100m',
                    memory: '128Mi',
                  },
                  limits: self.requests,
                },
              },
            ],
            extraVolumes+: [
              {
                name: 'runtime-api',
                emptyDir: {
                  medium: 'Memory',
                },
              },
            ],
            extraVolumeMounts+: [
              {
                name: 'runtime-api',
                mountPath: '/run/haproxy/runtime-api',
              },
            ],
            mountedSecrets+: [
              {
                volumeName: s.cert.spec.secretName,
                secretName: s.cert.spec.secretName,
                mountPath: '/run/secrets/' + s.cert.spec.secretName,
              },
            ],
          })),
          cert: k.Certificate + {
            name:: s.name,
            namespace:: proxy.ns.name,
            spec+: {
              issuerRef+: {
                kind: self.CLUSTER_ISSUER,
                name: 'letsencrypt-prod',
              },
              dnsNames: [
                s.hostname,
              ],
              secretName+: '-tls',
              usages: [
                'digital signature',
                'key encipherment',
              ],
              additionalOutputFormats: [
                { type: 'CombinedPEM' },
              ],
            },
          },
        }
        for shard in std.range(0, proxy.num_shards - 1)
      ],
    },

    janitor: if test_janitor then kube_janitor(
      C.k8s,
      args=[
        //'--dry-run',
        '--include-resources=secrets',
      ],
      rbac_rules=[
        k.BaseRole.Rule + {
          apiGroups: [''],
          resources: ['secrets'],
          verbs: self.LIST_VERBS + [self.DELETE],
        },
      ],
      config_rules={
        rules: [
          {
            id: 'test-ws-janitor',
            resources: ['secrets'],
            jmespath: "starts_with(metadata.namespace, 'test-') && metadata.labels.\"aug.remote-agent-workspace\" != null",
            ttl: '2h',
          },
        ],
      },
    ),

    otel: otel(C, C.k8s, target_gcp_project=C.services_gcp_project),

    //////////////////////////////////////////////////////////////////////////////
    //
    // Custom kube-dns deployment (only for gcp-prod-agent0)
    //

    [if cluster == 'gcp-prod-agent0' then 'custom_kube_dns']: {
      deployment: k.Deployment + {
        name:: 'aug-kube-dns',
        namespace:: 'kube-system',
        metadata+: {
          annotations+: {
            'deployment.kubernetes.io/revision': '1',
            'k8s-app': 'kube-dns',
          },
        },
        spec: std.prune(super.spec + {
          replicas: null,  // replicas are handled by the auto-scaler (below), prune so we don't get spurious diffs or compete on `apply`.
          selector: {
            matchLabels: {
              'k8s-app': 'kube-dns',
            },
          },
          strategy: {
            rollingUpdate: {
              maxSurge: '10%',
              maxUnavailable: 0,
            },
            type: 'RollingUpdate',
          },
          template: {
            metadata: {
              creationTimestamp: null,
              labels: {
                'k8s-app': 'kube-dns',
              },
            },
            spec: {
              containers: [
                {
                  name: 'kubedns',
                  image: 'registry.k8s.io/dns/k8s-dns-kube-dns:1.17.3',
                  resources: {
                    limits: {
                      memory: '400Mi',  // High memory limit to prevent OOM issues.
                    },
                    requests: {
                      cpu: '100m',
                      memory: '70Mi',
                    },
                  },
                  livenessProbe: {
                    httpGet: {
                      path: '/healthcheck/kubedns',
                      port: 10054,
                      scheme: 'HTTP',
                    },
                    initialDelaySeconds: 60,
                    timeoutSeconds: 5,
                    successThreshold: 1,
                    failureThreshold: 5,
                  },
                  readinessProbe: {
                    httpGet: {
                      path: '/readiness',
                      port: 8081,
                      scheme: 'HTTP',
                    },
                    initialDelaySeconds: 3,
                    timeoutSeconds: 5,
                  },
                  args: [
                    '--domain=cluster.local.',
                    '--dns-port=10053',
                    '--config-dir=/kube-dns-config',
                    '--v=2',
                  ],
                  env: [
                    {
                      name: 'PROMETHEUS_PORT',
                      value: '10055',
                    },
                  ],
                  ports: [
                    {
                      containerPort: 10053,
                      name: 'dns-local',
                      protocol: 'UDP',
                    },
                    {
                      containerPort: 10053,
                      name: 'dns-tcp-local',
                      protocol: 'TCP',
                    },
                    {
                      containerPort: 10055,
                      name: 'metrics',
                      protocol: 'TCP',
                    },
                  ],
                  volumeMounts: [
                    {
                      name: 'kube-dns-config',
                      mountPath: '/kube-dns-config',
                    },
                  ],
                  securityContext: {
                    allowPrivilegeEscalation: false,
                    readOnlyRootFilesystem: true,
                    runAsUser: 1001,
                    runAsGroup: 1001,
                  },
                },
                {
                  name: 'dnsmasq',
                  image: 'registry.k8s.io/dns/k8s-dns-dnsmasq-nanny:1.17.3',
                  livenessProbe: {
                    httpGet: {
                      path: '/healthcheck/dnsmasq',
                      port: 10054,
                      scheme: 'HTTP',
                    },
                    initialDelaySeconds: 60,
                    timeoutSeconds: 5,
                    successThreshold: 1,
                    failureThreshold: 5,
                  },
                  args: [
                    '-v=2',
                    '-logtostderr',
                    '-configDir=/etc/k8s/dns/dnsmasq-nanny',
                    '-restartDnsmasq=true',
                    '--',
                    '-k',
                    '--cache-size=1000',
                    '--no-negcache',
                    '--dns-forward-max=1500',
                    '--log-facility=-',
                    '--server=/cluster.local/127.0.0.1#10053',
                    '--server=/in-addr.arpa/127.0.0.1#10053',
                    '--server=/ip6.arpa/127.0.0.1#10053',
                  ],
                  ports: [
                    {
                      containerPort: 53,
                      name: 'dns',
                      protocol: 'UDP',
                    },
                    {
                      containerPort: 53,
                      name: 'dns-tcp',
                      protocol: 'TCP',
                    },
                  ],
                  resources: {
                    requests: {
                      cpu: '150m',
                      memory: '20Mi',
                    },
                  },
                  volumeMounts: [
                    {
                      name: 'kube-dns-config',
                      mountPath: '/etc/k8s/dns/dnsmasq-nanny',
                    },
                  ],
                  securityContext: {
                    capabilities: {
                      drop: ['all'],
                      add: ['NET_BIND_SERVICE', 'SETGID'],
                    },
                  },
                },
                {
                  name: 'sidecar',
                  image: 'registry.k8s.io/dns/k8s-dns-sidecar:1.17.3',
                  livenessProbe: {
                    httpGet: {
                      path: '/metrics',
                      port: 10054,
                      scheme: 'HTTP',
                    },
                    initialDelaySeconds: 60,
                    timeoutSeconds: 5,
                    successThreshold: 1,
                    failureThreshold: 5,
                  },
                  args: [
                    '--v=2',
                    '--logtostderr',
                    '--probe=kubedns,127.0.0.1:10053,kubernetes.default.svc.cluster.local,5,SRV',
                    '--probe=dnsmasq,127.0.0.1:53,kubernetes.default.svc.cluster.local,5,SRV',
                  ],
                  ports: [
                    {
                      containerPort: 10054,
                      name: 'metrics',
                      protocol: 'TCP',
                    },
                  ],
                  resources: {
                    requests: {
                      memory: '20Mi',
                      cpu: '10m',
                    },
                  },
                  securityContext: {
                    allowPrivilegeEscalation: false,
                    readOnlyRootFilesystem: true,
                    runAsUser: 1001,
                    runAsGroup: 1001,
                  },
                },
              ],
              dnsPolicy: 'Default',
              restartPolicy: 'Always',
              schedulerName: 'default-scheduler',
              securityContext: {},
              serviceAccount: 'kube-dns',
              serviceAccountName: 'kube-dns',
              terminationGracePeriodSeconds: 30,
              tolerations: [
                {
                  key: 'CriticalAddonsOnly',
                  operator: 'Exists',
                },
                {  // Allow this to run on agent nodes, since that's the only resource we really scale in this cluster
                  key: 'raws.augmentcode.com/pool-group',
                  operator: 'Exists',
                },
              ],
              volumes: [
                {
                  configMap: {
                    defaultMode: 420,
                    name: 'kube-dns',
                    optional: true,
                  },
                  name: 'kube-dns-config',
                },
              ],
            },
          },
        }),
      },

      // Custom DNS Autoscaler ServiceAccount
      autoscaler_service_account: k.ServiceAccount + {
        name:: 'kube-dns-autoscaler',
        namespace:: 'kube-system',
      },

      // Custom DNS Autoscaler RBAC
      autoscaler_cluster_role: k.ClusterRole + {
        name:: 'system:custom-dns-autoscaler',
        rules+: [
          {
            apiGroups: [''],
            resources: ['nodes'],
            verbs: ['list', 'watch'],
          },
          {
            apiGroups: ['apps'],
            resourceNames: ['aug-kube-dns'],
            resources: ['deployments/scale'],
            verbs: ['get', 'update'],
          },
          {
            apiGroups: [''],
            resources: ['configmaps'],
            verbs: ['get', 'create'],
          },
        ],
      },

      autoscaler_cluster_role_binding: k.ClusterRoleBinding + {
        name:: 'system:custom-dns-autoscaler',
        role_name:: 'system:custom-dns-autoscaler',
        subjects: [
          {
            kind: 'ServiceAccount',
            name: 'kube-dns-autoscaler',
            namespace: 'kube-system',
          },
        ],
      },


      autoscaler_deployment: k.Deployment + {
        name:: 'custom-dns-autoscaler',
        namespace:: 'kube-system',
        metadata+: {
          labels+: {
            'k8s-app': 'custom-dns-autoscaler',
          },
        },
        spec+: {
          selector: {
            matchLabels: {
              'k8s-app': 'custom-dns-autoscaler',
            },
          },
          template: {
            metadata: {
              labels: {
                'k8s-app': 'custom-dns-autoscaler',
              },
              annotations: {
                'seccomp.security.alpha.kubernetes.io/pod': 'docker/default',
              },
            },
            spec: {
              priorityClassName: 'system-cluster-critical',
              securityContext: {
                supplementalGroups: [65534],
                fsGroup: 65534,
              },
              nodeSelector: {
                'kubernetes.io/os': 'linux',
              },
              containers: [
                {
                  name: 'autoscaler',
                  image: 'registry.k8s.io/cluster-proportional-autoscaler-amd64:1.7.1',
                  resources: {
                    requests: {
                      cpu: '20m',
                      memory: '10Mi',
                    },
                  },
                  command: [
                    '/cluster-proportional-autoscaler',
                    '--namespace=kube-system',
                    '--configmap=custom-dns-autoscaler',
                    '--target=Deployment/aug-kube-dns',
                    '--default-params={"linear":{"coresPerReplica":256,"nodesPerReplica":16,"preventSinglePointFailure":true}}',
                    '--logtostderr=true',
                    '--v=2',
                  ],
                },
              ],
              tolerations: [
                {
                  key: 'CriticalAddonsOnly',
                  operator: 'Exists',
                },
              ],
              serviceAccountName: 'kube-dns-autoscaler',
            },
          },
        },
      },
    },
  },

  //////////////////////////////////////////////////////////////////////////////
  //
  // Selectively return stages
  //

  ret:: [
    $['stage%d' % [num]]
    for num in stages
  ],

}.ret
