local clusters = import '../../clusters.jsonnet';
local nccl_tcp = import 'nccl-tcp-installers.jsonnet';

{
  // The Host GCP ConfigConnector for the "Outer" resources.
  host_cluster:: 'gke_cluster.host_cluster required.',
  host_namespace:: 'gke_cluster.host_namespace required.',

  // Cluster details.
  project_id:: error 'gke_cluster.project_id required.',
  location:: error 'gke_cluster.location required.',
  name:: error 'gke_cluster.name required.',
  description:: error 'gke_cluster.description required.',
  num_nets:: 1,
  primary_zone:: null,

  core_only:: false,
  svc_pool:: true,
  cpu_pool:: !self.core_only,
  h100_reservation:: null,
  h100_shards:: 4,
  l4_pool:: !self.core_only,
  l4_reservations:: null,
  t4_pool:: !self.core_only,
  ci_pool:: false,
  hydra_pool:: false,

  // NAT configuration
  // A note on NAT IP sizing (https://cloud.google.com/nat/docs/ports-and-addresses)
  // We are using static port allocation with 1024 ports per VM
  // This provides ample port allocation for agent workloads with high connection needs
  // This means we can support ~64 VMs per external IP, or 1260 agents/IP.
  //
  // TODO(marcmac) - alerting on NAT port assignment issues for scaling.
  enable_nat:: false,
  nat_ip_count:: 2,
  min_ports_per_vm:: null,  // Required when enable_nat is true

  // Validation: min_ports_per_vm is required when NAT is enabled
  assert !$.enable_nat || $.min_ports_per_vm != null : 'min_ports_per_vm is required when enable_nat is true',

  local JUMBO_MTU = 8244,  // https://cloud.google.com/compute/docs/gpus/gpudirect

  // Outer contains the ConfigConnector assets which define the cluster on the GCP level.
  Outer: local H = clusters.cluster($.host_cluster); H.k8s + {
    local O = self,

    Object+:: {
      project_id:: $.project_id,
      namespace:: $.host_namespace,
      metadata+: {
        labels: {},  // Reset labels to avoid the defaults from the host cluster.
      },
    },

    // Every cluster has at least 1 VPC network. If a cluster has nodes with multiple NICs (e.g., GPU
    // machine-types with 5 NICs) then we need as many networks as the max number of NICs on a node.
    networks: [
      // Each VPC network is made up of the network itself plus a single subnet (and within the subnet
      // are multiple IPv4 address ranges.
      {
        local N = self,
        local is_primary = n == 0,

        // A VPC Network is a Global resource.
        net: O.GCP.Compute.Network + {
          name:: '%s-vpc%d' % [$.name, n],
          project_id:: $.project_id,
          spec+: {
            autoCreateSubnetworks: false,
            deleteDefaultRoutesOnCreate: false,
            mtu: JUMBO_MTU,
          },
        },

        // A VPC Subnet is a Regional resource.
        subnet: O.GCP.Compute.Subnetwork + {
          name:: N.net.name + '-subnet0',
          project_id:: $.project_id,
          spec+: {
            networkRef: N.net.localRef,
            region: $.location,
            privateIpGoogleAccess: true,

            // The primary IPv4 range is used for K8s Nodes in the cluster.
            ipCidrRange: '10.%d.0.0/16' % [0 + n],

            secondaryIpRange: std.prune([
              // The secondary IPv4 range is used for K8s Pods in the cluster.
              {
                rangeName: 'pod',
                ipCidrRange: '10.%d.0.0/16' % [100 + n],
              },

              // Another secondary IPv4 range (tertiary, if you will) range is used for K8s Services in the cluster.
              // This range is only needed on the primary VPC when the cluster has multiple.
              if is_primary then {
                rangeName: 'svc',
                ipCidrRange: '10.%d.0.0/16' % [if $.name == 'prod-gsc' then 50 else 200],
              },
            ]),
            // Leave these breadcrumbs behind so we don't need to guess at indexes elsewhere.
            podRange:: self.secondaryIpRange[0],
            svcRange:: self.secondaryIpRange[1],
          },
        },
      }
      for n in std.range(0, $.num_nets - 1)
    ],

    // Another breadcrumb to avoid guessing at indexes elsewhere.
    vpc0:: self.networks[0],

    // NAT resources (static IPs, router, and NAT gateway)
    nat_addresses: if $.enable_nat then [
      O.GCP.Compute.Address + {
        name:: '%s-nat-ip-%d' % [$.name, i],
        project_id:: $.project_id,
        spec+: {
          location: $.location,
          addressType: 'EXTERNAL',
        },
      }
      for i in std.range(0, $.nat_ip_count - 1)
    ] else [],

    nat_router: if $.enable_nat then O.GCP.Compute.Router + {
      name:: '%s-nat-router' % [$.name],
      project_id:: $.project_id,
      spec+: {
        region: $.location,
        networkRef: O.vpc0.net.localRef,
      },
    } else null,

    nat_gateway: if $.enable_nat then O.GCP.Compute.RouterNAT + {
      name:: '%s-nat-gateway' % [$.name],
      project_id:: $.project_id,
      spec+: {
        region: $.location,
        routerRef: O.nat_router.localRef,
        natIpAllocateOption: 'MANUAL_ONLY',
        natIps: [
          addr.localRef
          for addr in O.nat_addresses
        ],
        sourceSubnetworkIpRangesToNat: 'LIST_OF_SUBNETWORKS',
        subnetwork: [{
          subnetworkRef: {
            external: O.vpc0.subnet.ref,
          },
          sourceIpRangesToNat: ['PRIMARY_IP_RANGE'],
        }],
        logConfig: {
          enable: true,
          filter: 'ERRORS_ONLY',
        },
        enableDynamicPortAllocation: false,
        minPortsPerVm: $.min_ports_per_vm,
      },
    } else null,

    // The GKE Cluster itself. Pretty straightforward. Use the REGULAR release channel by default.
    // Enable workload identity, persistent disks, filestore, and object store FUSE.
    cluster: O.GCP.GKE.Cluster + {
      name:: $.name,
      metadata+: {
        annotations+: {
          'cnrm.cloud.google.com/remove-default-node-pool': 'true',
        },
      },
      spec+: {
        description: $.description,
        enableAutopilot: false,
        location: $.location,
        releaseChannel+: {
          // No release channel, otherwise every NodePool is forced into Auto Upgrade.
          channel: self.UNSPECIFIED,
        },
        addonsConfig+: {
          gcePersistentDiskCsiDriverConfig+: { enabled: true },
          gcpFilestoreCsiDriverConfig+: { enabled: true },
          gcsFuseCsiDriverConfig+: { enabled: true },
        },

        loggingConfig+: {
          enableComponents: [
            'SYSTEM_COMPONENTS',  // required if any logging enabled
            'APISERVER',
            'CONTROLLER_MANAGER',
            'SCHEDULER',
            'WORKLOADS',
          ],
        },

        authenticatorGroupsConfig+: {
          securityGroup: '<EMAIL>',
        },
        workloadIdentityConfig+: {
          workloadPool: $.project_id + '.svc.id.goog',
        },

        datapathProvider: self.DATAPATH_PROVIDER.ADVANCED_DATAPATH,
        networkingMode: self.NET_MODE.VPC_NATIVE,
        enableMultiNetworking: true,

        networkRef: O.vpc0.net.localRef,
        subnetworkRef: O.vpc0.subnet.localRef,
        ipAllocationPolicy+: {
          clusterSecondaryRangeName: O.vpc0.subnet.spec.podRange.rangeName,
          servicesSecondaryRangeName: O.vpc0.subnet.spec.svcRange.rangeName,
        },

        // This is required, but effecively ignored. ConfigConnecter doesn't seem to support he `nodePool`
        // property. So we set this, and the 'remove-default-node-pool' annotation. Together these avoid
        // the default node pool and let us define node pool(s) from scratch.
        initialNodeCount: 1,
      },
    },

    // The ServiceAccount that nodes act as. Technically this can differ for each node pool, but for now
    // we only need the one.
    node_sa: O.GCP.IAM.ServiceAccount + {
      local s = self,
      std_name:: $.name + '-gke-nodepool',
      alt_name:: $.name + '-node',
      name:: if std.length(self.std_name) > self.NAME_LIMIT then self.alt_name else self.std_name,
      project_id:: $.project_id,
      spec+: {
        displayName: s.name,
        description: s.name + ' NodePool Service Account.',
      },
    },
    node_sa_role: O.GCP.IAM.PolicyMember + {
      name:: O.node_sa.name + '.' + $.project_id + '.node-sa',
      spec+: {
        resourceRef: {
          kind: 'Project',
          external: 'project/' + $.project_id,
        },
        member: 'serviceAccount:' + O.node_sa.email,
        role: 'roles/container.defaultNodeServiceAccount',
      },
    },

    // Base NodePool Template. Sets defaults and flattens out interesting properties to top-level.
    _base_pool:: O.GCP.GKE.NodePool + {
      local p = self,
      name_suffix:: error 'NodePool.name_suffix required.',
      name:: $.name + '-' + self.name_suffix,
      initial_node_count:: null,
      node_locations:: if $.primary_zone != null && self.gpu_count > 0 then [$.primary_zone],
      min_node_count:: 0,
      max_node_count:: error 'NodePool.max_node_count required.',
      total_min_node_count:: null,
      total_max_node_count:: null,
      machine_type:: error 'NodePool.machine_type required.',
      gpu_count:: 0,
      gpu_type:: error 'Node.gpu_type required with Node.gpu_count > 0',

      auto_upgrade:: false,
      max_surge:: 5,
      max_unavailable:: 1,

      LOCATION_POLICY:: {
        BALANCED: 'BALANCED',
        ANY: 'ANY',
      },
      location_policy:: (
        if self.total_min_node_count != null || self.total_max_node_count != null then self.LOCATION_POLICY.ANY
        else if self.min_node_count != null || self.max_node_count != null then self.LOCATION_POLICY.BALANCED
        else null
      ),

      // Book disk size can and should be small (64GiB?) when using ephemeral_ssd_count, otherwise larger (3T) because
      // it will be used for images, logs, etc. The cost of local SSDs is comparable to pd-ssd, so the former is
      // preferred.
      disk_type:: error 'NodePool.disk_type required.',
      disk_size_gb:: error 'NodePool.disk_size_gb required.',

      // Number of local ephemeral SSDs. Some machine types include these automatically, others needs to be
      // manually specified. NOTE(mattm): I recommend we always set this even for auto machine types so we
      // can reference how much capacity we have.
      // Each SSD is 375 GiB -- where does that magic number come from?!
      ephemeral_ssd_count:: null,

      taints:: if self.gpu_count > 0 then [
        {
          effect: 'NO_SCHEDULE',
          key: 'nvidia.com/gpu',
          value: 'present',
        },
      ] else [
      ],

      // NOTE(marcmac): Required for c3 machine types.
      gvnic:: std.startsWith(p.spec.nodeConfig.machineType, 'c3'),
      nccl_fast_socket:: false,

      spec+: {
        clusterRef: O.cluster.localRef,
        initialNodeCount: p.initial_node_count,
        autoscaling+: std.prune({
          minNodeCount: p.min_node_count,
          maxNodeCount: p.max_node_count,
          totalMinNodeCount: p.total_min_node_count,
          totalMaxNodeCount: p.total_max_node_count,
          locationPolicy: p.location_policy,
        }),
        location: O.cluster.spec.location,
        nodeLocations: p.node_locations,
        maxPodsPerNode: null,  // GKE default is 110. This is mostly a networking parameter, but does effect system CPU reservation.
        networkConfig+: {
          createPodRange: false,
          podRange: O.cluster.spec.ipAllocationPolicy.clusterSecondaryRangeName,
          // No release channel, otherwise every NodePool is forced into Auto Upgrade.
        },
        management+: {
          autoRepair: true,
          autoUpgrade: p.auto_upgrade,
        },
        upgradeSettings+: {
          strategy: self.STRATEGY.SURGE,
          maxSurge: p.max_surge,
          maxUnavailable: p.max_unavailable,
        },
        nodeConfig+: {
          preemptible: false,
          imageType: 'cos_containerd',
          machineType: p.machine_type,
          diskType: p.disk_type,
          diskSizeGb: p.disk_size_gb,

          // See https://cloud.google.com/compute/docs/disks/local-ssd and `glcoud container node-pools create --help`.
          // TL;DR; use just ephemeralStorageLocalSsdConfig.localSsdCount unless we want raw NVME block devices.
          // NOTE(mattm): 3rd gen machine types get the max count automatically, earler needs to be specified, we try
          // to always set so we document how much storage to expect.
          ephemeralStorageConfig: null,  // --ephemeral-storage replaced by --ephemeral-storage-local-ssd. Old style NVME interface, do not use.
          localNvmeSsdBlockConfig: null,  // --local-nvme-ssd-block. Raw block devices, we want an ephemeral filesystem for emptyDir, logs, images, etc.
          ephemeralStorageLocalSsdConfig: if p.ephemeral_ssd_count != null then {  // --ephemeral-storage-local-ssd. NVME local storage
            localSsdCount: p.ephemeral_ssd_count,
          },

          guestAccelerator+: if p.gpu_count != null && p.gpu_count > 0 then [{
            count: p.gpu_count,
            type: p.gpu_type,
            gpuDriverInstallationConfig+: {
              gpuDriverVersion: 'LATEST',
            },
          }],

          gvnic: if p.gvnic then { enabled: true },
          fastSocket: if p.nccl_fast_socket then { enabled: true },

          taint+: p.taints,

          serviceAccountRef: O.node_sa.localRef,
          oauthScopes: [
            'https://www.googleapis.com/auth/cloud-platform',
          ],
          metadata+: {
            'disable-legacy-endpoints': 'true',
          },
        },
      },
    },

    // Node Pool Tainting Strategy:
    //   Every pool has at least one NO_SCHEDULING taint so that we can control which workloads go where.
    //   The GPU pools get an automagic taint from GKE/nVidia and pods requesting nVidia GPU affinity
    //   get an automagic tolerance from an nVidia admission controller.
    //   If we have one CPU pool with no taints, it's hard to isolate GKE services. NOTE(mattm): we could
    //   maybe have a PREFER_NO_SCHEDULE.

    // GKE Pool: Small machines used for GKE managed pods (like kube-dns).
    gke_pool: self._base_pool + {
      name_suffix:: 'gke0',
      machine_type:: 'e2-standard-16',
      disk_type:: 'pd-balanced',
      disk_size_gb:: 512,
      initial_node_count:: 1,
      min_node_count:: 1,
      max_node_count:: 3,
      auto_upgrade:: true,
      taints+:: [{ effect: 'NO_SCHEDULE', key: 'components.gke.io/gke-managed-components', value: 'true' }],
    },

    // SVC Pool: Research basic cluster services (external-dns, cert-manager, determined, ...).
    svc_pool: if $.svc_pool then self._base_pool + if $.core_only then {
      name_suffix:: 'svc0',
      machine_type:: 'c3-standard-44',
      disk_type:: 'pd-ssd',
      disk_size_gb:: 4096,
      initial_node_count:: 1,
      min_node_count:: if std.objectHas($, 'svc_pool_min_nodes') then $.svc_pool_min_nodes else 1,
      max_node_count:: 5,
      auto_upgrade:: true,
      taints+:: [{ effect: 'PREFER_NO_SCHEDULE', key: 'r.augmentcode.com/pool-type', value: 'svc' }],
    } else {
      name_suffix:: 'svc0',
      machine_type:: 'c3-standard-44-lssd',
      disk_type:: 'pd-ssd',
      disk_size_gb:: 64,
      ephemeral_ssd_count:: 8,  // https://cloud.google.com/compute/docs/disks/local-ssd#lssd_disks_fixed
      initial_node_count:: 1,
      min_node_count:: if std.objectHas($, 'svc_pool_min_nodes') then $.svc_pool_min_nodes else 1,
      max_node_count:: 5,
      auto_upgrade:: true,
      taints+:: [{ effect: 'NO_SCHEDULE', key: 'r.augmentcode.com/pool-type', value: 'svc' }],
    },

    // CPU Pool: c3 w/ local ssd.
    cpu: if $.cpu_pool then self._base_pool + {
      name_suffix:: 'cpu-c3',
      machine_type:: 'c3-standard-176-lssd',
      disk_type:: 'pd-ssd',
      disk_size_gb:: 64,
      ephemeral_ssd_count:: 32,  // https://cloud.google.com/compute/docs/disks/local-ssd#lssd_disks_fixed
      min_node_count:: null,
      max_node_count:: null,
      total_min_node_count:: 4,
      total_max_node_count:: 16,
      node_locations:: if $.primary_zone != null then [$.primary_zone],
      taints+:: [{ effect: if $.core_only then 'NO_SCHEDULE' else 'PREFER_NO_SCHEDULE', key: 'r.augmentcode.com/pool-type', value: 'cpu' }],
      spec+: {
        nodeConfig+: {
          ephemeralStorageLocalSsdConfig: {
            localSsdCount: 32,
          },
        },
      },
    },


    // NVIDIA H100 gSC *reserved* pool.
    h100_pools: if $.h100_reservation != null then [
      self._base_pool + {
        name_suffix:: 'h100-gsc' + if shard != '' then '-' + shard else '',
        machine_type:: 'a3-megagpu-8g',
        disk_type:: 'pd-ssd',
        disk_size_gb:: 64,
        ephemeral_ssd_count:: 16,  // https://cloud.google.com/compute/docs/disks/local-ssd#lssd_disk_fixed
        gpu_type:: 'nvidia-h100-mega-80gb',
        gpu_count:: 8,
        max_surge:: 0,
        max_unavailable: 20,  // We want the upgrade to be quick since we plan on cordoning the nodes for each upgrade.
        gvnic:: true,
        nccl_fast_socket:: false,
        taints+:: [],  // GPU nodes are auto-tainted (sometimes, but also defined in the base class).
        spec+: {
          autoscaling: null,
          nodeConfig+: {
            hostMaintenancePolicy+: {
              maintenanceInterval: 'PERIODIC',
            },
            reservationAffinity+: {
              consumeReservationType: 'SPECIFIC_RESERVATION',
              key: 'compute.googleapis.com/reservation-name',
              values: [$.h100_reservation],
            },
          },
          networkConfig+: {
            additionalNodeNetworkConfigs: [
              {
                networkRef: { external: net.net.name },
                subnetworkRef: { external: net.subnet.name },
              }
              for net in $.Outer.networks[1:]
            ],
          },
        },
      }
      for shard in ['a', 'b', 'c', 'd', 'e', 'f', 'g', 'h'][0:$.h100_shards]
    ],

    // nVidia L4 Pool: Basic GPU typically used 1-per-DevPod.
    l4_pool: if $.l4_pool then self._base_pool + {
      name_suffix:: 'l4',
      machine_type:: 'g2-standard-48',
      disk_type:: 'pd-ssd',
      disk_size_gb:: 64,
      ephemeral_ssd_count:: 4,  // https://cloud.google.com/compute/docs/disks/local-ssd#lssd_disk_options
      gpu_type:: 'nvidia-l4',
      gpu_count:: 4,
      max_surge:: if $.l4_reservations != null then 0 else super.max_surge,
      gvnic:: true,
      taints+:: [],  // GPU nodes are auto-tainted (sometimes, but also defined in the base class).
      spec+: if $.l4_reservations != null then {
        autoscaling: null,
        nodeConfig+: {
          reservationAffinity+: {
            consumeReservationType: 'SPECIFIC_RESERVATION',
            key: 'compute.googleapis.com/reservation-name',
            values: $.l4_reservations,
          },
        },
      } else {},
    },
    l4_pool_ondemand: if $.l4_pool then self._base_pool + {
      name_suffix:: 'l4-ondemand0',
      machine_type:: 'g2-standard-48',
      disk_type:: 'pd-ssd',
      disk_size_gb:: 64,
      ephemeral_ssd_count:: 4,  // https://cloud.google.com/compute/docs/disks/local-ssd#lssd_disk_options
      gpu_type:: 'nvidia-l4',
      gpu_count:: 4,
      gvnic:: true,
      taints+:: [],  // GPU nodes are auto-tainted (sometimes, but also defined in the base class).
      min_node_count:: null,
      max_node_count:: null,
      total_min_node_count:: 1,
      total_max_node_count:: 8,
    },

    // nVidia T4 Pool -- These are cheaper than L4s and tend to be more easily available.
    t4_pool: if $.t4_pool then self._base_pool + {
      name_suffix:: 't4',
      machine_type:: 'n1-standard-64',
      disk_type:: 'pd-ssd',
      disk_size_gb:: 64,
      ephemeral_ssd_count:: 8,  // https://cloud.google.com/compute/docs/disks/local-ssd#lssd_disk_options
      initial_node_count:: 1,
      min_node_count:: 1,
      max_node_count:: 32,
      gpu_type:: 'nvidia-tesla-t4',
      gpu_count:: 4,
      gvnic:: true,
      taints+:: [],  // GPU nodes are auto-tainted (sometimes, but also defined in the base class).
    },

    // NVIDIA T4 CI Pool
    ci_pool: if $.ci_pool then self._base_pool + {
      name_suffix:: 'ci-t4',
      node_locations:: null,  // use the cluster-wide default (multiple zones)
      machine_type:: 'n1-standard-64',
      disk_type:: 'pd-ssd',
      disk_size_gb:: 64,
      ephemeral_ssd_count:: 8,  // https://cloud.google.com/compute/docs/disks/local-ssd#lssd_disk_options
      min_node_count:: null,
      max_node_count:: null,
      total_min_node_count:: 6,
      total_max_node_count:: 64,  // a value is needed
      auto_upgrade:: true,
      gpu_type:: 'nvidia-tesla-t4',
      gpu_count:: 4,
      gvnic:: true,
      taints+:: [
        // GPU nodes are auto-tainted (sometimes, but also defined in the base class).
        // Also, add our own.
        { effect: 'NO_SCHEDULE', key: 'r.augmentcode.com/pool-type', value: 'ci' },
      ],
    },

    // Hydra Pool for small CPU containers.
    hydra_pool: if $.hydra_pool then self._base_pool + {
      name_suffix:: 'hydra',
      spec+: {
        nodeConfig+: {
          labels+: { 'r.augmentcode.com/pool-type': 'hydra' },
        },
      },
      node_locations:: null,  // use the cluster-wide default (multiple zones)
      machine_type:: 'n2-standard-48',
      disk_type:: 'pd-ssd',
      disk_size_gb:: 1000,
      ephemeral_ssd_count:: null,  // https://cloud.google.com/compute/docs/disks/local-ssd#lssd_disk_options
      min_node_count:: null,
      max_node_count:: null,
      total_min_node_count:: 6,
      total_max_node_count:: 128,  // a value is needed
      auto_upgrade:: true,
      taints+:: [
        { effect: 'NO_SCHEDULE', key: 'r.augmentcode.com/pool-type', value: 'hydra' },
      ],
    },
  },

  // Inner contains the K8s assets which are defined inside the cluster (once it's created) which
  // are closely related to the Outer config. Additional base assets are defined in {cluster}.jsonnet.
  Inner: local C = clusters.cluster($.name); C.k8s + {
    local I = self,

    // StorageClasses -- put FS on the correct network. StorageClass parameters are immutable so we
    // need to create new StorageClasses from scratch.
    _fs_sc_base:: I.StorageClass + {
      provisioner: 'filestore.csi.storage.gke.io',
      reclaimPolicy: self.RECLAIM_POLICY.DELETE,
      volumeBindingMode: self.VOLUME_BINDING_MODE.WAIT_FOR_FIRST_CONSUMER,
      allowVolumeExpansion: true,
      parameters+: {
        network: $.Outer.vpc0.net.metadata.name,
      },
    },

    fs_scs: [
      I._fs_sc_base + {
        name:: 'standard-rwx-vpc0',
        parameters+: { tier: 'standard' },
      },
      I._fs_sc_base + {
        name:: 'premium-rwx-vpc0',
        parameters+: { tier: 'premium' },
      },
      I._fs_sc_base + {
        name:: 'zonal-rwx-vpc0',
        parameters+: { tier: 'zonal' },
      },
      I._fs_sc_base + {
        name:: 'enterprise-rwx-vpc0',
        parameters+: { tier: 'enterprise' },
      },
      I._fs_sc_base + {
        name:: 'enterprise-multishare-rwx-vpc0',
        parameters+: {
          tier: 'enterprise',
          multishare: 'true',
          'instance-storageclass-label': 'enterprise-multishare-rwx',
        },
      },
    ],

    // StorageClasses -- pin PDs to a primary zone, when defined. These are meant
    // to modify the GCP-provided StorageClasses, not necessarily fully specify them.
    zonal_scs: if $.primary_zone != null then [
      I.StorageClass + {
        name:: 'standard',
        default:: false,
        parameters+: {
          type: 'pd-standard',
        },
        provisioner: 'kubernetes.io/gce-pd',
        reclaimPolicy: self.RECLAIM_POLICY.DELETE,
        volumeBindingMode: self.VOLUME_BINDING_MODE.IMMEDIATE,
        allowedTopologies+: [{
          matchLabelExpressions: [{
            key: 'topology.gke.io/zone',
            values: [$.primary_zone],
          }],
        }],
      },
      I.StorageClass + {
        name:: 'premium-rwo',
        default:: false,
        parameters+: {
          type: 'pd-ssd',
        },
        provisioner: 'pd.csi.storage.gke.io',
        reclaimPolicy: self.RECLAIM_POLICY.DELETE,
        volumeBindingMode: self.VOLUME_BINDING_MODE.WAIT_FOR_FIRST_CONSUMER,
        allowedTopologies+: [{
          matchLabelExpressions: [{
            key: 'topology.gke.io/zone',
            values: [$.primary_zone],
          }],
        }],
      },
      I.StorageClass + {
        name:: 'standard-rwo',
        default:: true,
        parameters+: {
          type: 'pd-balanced',
        },
        provisioner: 'pd.csi.storage.gke.io',
        reclaimPolicy: self.RECLAIM_POLICY.DELETE,
        volumeBindingMode: self.VOLUME_BINDING_MODE.WAIT_FOR_FIRST_CONSUMER,
        allowedTopologies+: [{
          matchLabelExpressions: [{
            key: 'topology.gke.io/zone',
            values: [$.primary_zone],
          }],
        }],
      },
    ],

    // VolumeSnapshotClasses
    volume_snapshot_classes: [
      I.VolumeSnapshotClass + {
        name:: 'pd',
        metadata+: {
          annotations+: {
            'snapshot.storage.kubernetes.io/is-default-class': 'true',  // this is per-driver
          },
        },
        driver: 'pd.csi.storage.gke.io',
        deletionPolicy: self.DELETION_POLICY.DELETE,
      },
      I.VolumeSnapshotClass + {
        name:: 'fs',
        metadata+: {
          annotations+: {
            'snapshot.storage.kubernetes.io/is-default-class': 'true',  // this is per-driver
          },
        },
        driver: 'filestore.csi.storage.gke.io',
        deletionPolicy: self.DELETION_POLICY.DELETE,
      },
    ],

    // Networks
    nccl_tcp: if $.num_nets > 1 then nccl_tcp(),

    nets: if $.num_nets > 1 then [
      {
        local n = self,
        local net = $.Outer.networks[num].net,
        local subnet = $.Outer.networks[num].subnet,

        params: I.GKENetworkParamSet + {
          name:: subnet.metadata.name,  // includes net prefix
          vpc:: net.metadata.name,
          vpcSubnet:: subnet.metadata.name,
          deviceMode:: self.DEVICE_MODE.NET_DEVICE,
        },

        network: I.Network + {
          name:: subnet.metadata.name,  // includes net prefix
          type:: self.TYPE.DEVICE,
          params:: n.params,
        },
      }
      for num in std.range(1, $.num_nets - 1)
    ],
  },
}
