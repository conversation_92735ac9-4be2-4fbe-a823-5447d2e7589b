use std::fmt::Debug;

use blob_names::B<PERSON>bName;
use content_manager_client::BlobScope;
use request_context::TenantInfo;

#[derive(<PERSON><PERSON>, PartialEq, Eq, Debug)]
pub struct TenantBlobContentKeys {
    pub request_id: request_context::RequestId,
    pub tenant_info: TenantInfo,
    pub blob_scope: BlobScope,
    pub blob_names: Vec<BlobName>,
}

#[cfg(test)]
pub mod tests {
    use super::*;
    use std::hash::{Hash, Hasher};
    use std::{cmp::Ordering, collections::hash_map::DefaultHasher};

    // use crate::test::get_test_data_path;
    #[test]
    fn test_blob_scope_eq() {
        let key1 = BlobScope {
            transformation_key: "key1".to_string(),
            sub_key: "subkey1".to_string(),
        };
        let key2 = BlobScope {
            transformation_key: "key1".to_string(),
            sub_key: "subkey1".to_string(),
        };
        assert_eq!(key1, key2);

        let key3 = BlobScope {
            transformation_key: "key1".to_string(),
            sub_key: "subkey2".to_string(),
        };
        assert_ne!(key1, key3);
    }

    #[test]
    fn test_blob_scope_ord() {
        let key1 = BlobScope {
            transformation_key: "key1".to_string(),
            sub_key: "subkey1".to_string(),
        };
        let key2 = BlobScope {
            transformation_key: "key1".to_string(),
            sub_key: "subkey2".to_string(),
        };
        assert_eq!(key1.cmp(&key2), Ordering::Less);

        let key3 = BlobScope {
            transformation_key: "key1".to_string(),
            sub_key: "subkey1".to_string(),
        };
        assert_eq!(key1.cmp(&key3), Ordering::Equal);
    }

    fn hash<T: Hash>(t: &T) -> u64 {
        let mut s = DefaultHasher::new();
        t.hash(&mut s);
        s.finish()
    }

    #[test]
    fn test_blob_scope_hash() {
        let key1 = BlobScope {
            transformation_key: "key1".to_string(),
            sub_key: "subkey1".to_string(),
        };
        let key2 = BlobScope {
            transformation_key: "key1".to_string(),
            sub_key: "subkey2".to_string(),
        };
        assert_ne!(hash(&key1), hash(&key2));

        let key3 = BlobScope {
            transformation_key: "key1".to_string(),
            sub_key: "subkey1".to_string(),
        };
        assert_eq!(hash(&key1), hash(&key3));
    }
}

#[cfg(test)]
pub mod test_utils {
    use blob_names::BlobName;
    use content_manager_client::BlobScope;
    use content_manager_rs_proto::content_manager::{self, BatchGetContentResponse};
    use request_context::{RequestContext, TenantId};
    use std::time::Instant;
    use tokio::sync::mpsc::{self, Receiver};

    use crate::chunk_cache::ChunkKey;

    /// Shared test constant used across CPU server tests
    pub const MOCK_HASH: &str = "mock_hash";
    pub const TEST_TRANSFORMATION_KEY: &str = "key";

    /// Load test content bytes for a given file name under the cpu_server test_data dir
    pub fn load_test_content(file_name: &str) -> Vec<u8> {
        std::fs::read(crate::test::get_test_data_path(
            format!("services/embeddings_search_host/cpu_server/src/test_data/{file_name}")
                .as_str(),
        ))
        .unwrap()
    }

    /// Convenience for the common case used in many tests
    pub fn load_default_test_content() -> Vec<u8> {
        load_test_content("tk1_sk1_hello.npy")
    }

    /// Build a successful BatchGetContentResponse for a (blob, scope)
    pub fn create_success_response(
        blob_name: &BlobName,
        blob_scope: &BlobScope,
        content: Vec<u8>,
    ) -> BatchGetContentResponse {
        BatchGetContentResponse {
            response: Some(
                content_manager::batch_get_content_response::Response::FinalContent(
                    content_manager::BatchGetContentFinalContent {
                        blob_name: blob_name.to_string(),
                        transformation_key: blob_scope.transformation_key.clone(),
                        sub_key: blob_scope.sub_key.clone(),
                        content,
                        final_hash: MOCK_HASH.to_string(),
                        metadata: vec![],
                    },
                ),
            ),
        }
    }

    /// Create a BlobScope with consistent transformation key
    pub fn create_blob_scope(sub_key: &str) -> BlobScope {
        BlobScope {
            transformation_key: TEST_TRANSFORMATION_KEY.to_string(),
            sub_key: sub_key.to_string(),
        }
    }

    /// Create a not found BatchGetContentResponse
    pub fn create_not_found_response(
        blob_name: &BlobName,
        blob_scope: &BlobScope,
    ) -> BatchGetContentResponse {
        BatchGetContentResponse {
            response: Some(
                content_manager::batch_get_content_response::Response::NotFoundContent(
                    content_manager::BatchGetContentNotFound {
                        blob_name: blob_name.to_string(),
                        transformation_key: blob_scope.transformation_key.clone(),
                        sub_key: blob_scope.sub_key.clone(),
                    },
                ),
            ),
        }
    }

    /// Create a ChunkKey with consistent transformation key
    pub fn create_chunk_key(blob_name: BlobName, chunk_index: usize) -> ChunkKey {
        ChunkKey {
            blob_name,
            transformation_key: TEST_TRANSFORMATION_KEY.to_string(),
            chunk_index,
        }
    }

    pub fn get_content_handler(
        embedding_filename: &'static str,
    ) -> impl Fn(
        &RequestContext,
        &Option<TenantId>,
        &BlobScope,
        &[BlobName],
        Option<Instant>,
    ) -> Receiver<tonic::Result<BatchGetContentResponse>> {
        |_, _, blob_scope, blob_names, _| {
            let (tx, rx) = mpsc::channel(blob_names.len());
            for blob_name in blob_names {
                let response = create_success_response(
                    blob_name,
                    blob_scope,
                    load_test_content(embedding_filename),
                );
                let _ = tx.try_send(Ok(response));
            }
            rx
        }
    }

    pub fn default_get_content_handler() -> impl Fn(
        &RequestContext,
        &Option<TenantId>,
        &BlobScope,
        &[BlobName],
        Option<Instant>,
    )
        -> Receiver<tonic::Result<BatchGetContentResponse>> {
        get_content_handler("tk1_sk1_hello.npy")
    }

    pub fn default_get_content_multiple_scopes_handler() -> impl Fn(
        &RequestContext,
        &Option<TenantId>,
        Vec<(BlobName, BlobScope)>,
        Option<Instant>,
    ) -> Receiver<
        tonic::Result<BatchGetContentResponse>,
    > {
        |_, _, blobs, _| {
            let (tx, rx) = mpsc::channel(blobs.len());
            for (blob_name, blob_scope) in blobs {
                let response = create_success_response(&blob_name, &blob_scope, Vec::new());
                tx.try_send(Ok(response)).unwrap();
            }
            rx
        }
    }
}
