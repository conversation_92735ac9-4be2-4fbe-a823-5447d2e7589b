use std::sync::Arc;

use feature_flags::FeatureFlagsServiceHandle;
use grpc_auth::tenant_info_from_grpc_req;
use grpc_service::{log_response_fn, send_and_ignore};
use request_context::{RequestContext, TenantId, TokenScope};
use tokio::sync::mpsc;
use tokio_stream::wrappers::ReceiverStream;
use tonic::{Request, Response, Status};
use tracing::Instrument;

use crate::dot_math::SimdData;
use crate::metrics::{REPLICA_BLOB_SENT, REPLICA_BLOB_SKIPPED, REPLICA_BYTES_SENT};
use crate::{
    embedding_cache::EmbeddingCache,
    proto::embeddings_search::{
        peer_embeddings_search_server::{PeerEmbeddingsSearch, PeerEmbeddingsSearchServer},
        GetPeerCacheRequest, GetPeerCacheResponse,
    },
};

const MAX_REPLICATION_AGE: feature_flags::IntFlag =
    feature_flags::IntFlag::new("embeddings_search_max_replication_age_s", 0);

struct PeerEmbeddingsSearchDataImpl<T: SimdData> {
    pub embedding_cache: Arc<dyn EmbeddingCache<T> + Send + Sync + 'static>,
}

impl<T: SimdData> PeerEmbeddingsSearchDataImpl<T> {}

pub struct PeerEmbeddingsSearchImpl<T: SimdData> {
    namespace: String,
    feature_flags: FeatureFlagsServiceHandle,
    data: Arc<PeerEmbeddingsSearchDataImpl<T>>,
}

impl<T: SimdData> PeerEmbeddingsSearchImpl<T> {
    pub fn new(
        namespace: String,
        embedding_cache: Arc<dyn EmbeddingCache<T> + Send + Sync + 'static>,
        feature_flags: FeatureFlagsServiceHandle,
    ) -> Self {
        PeerEmbeddingsSearchImpl {
            namespace,
            feature_flags,
            data: Arc::new(PeerEmbeddingsSearchDataImpl { embedding_cache }),
        }
    }

    pub fn new_server(self) -> PeerEmbeddingsSearchServer<Self> {
        PeerEmbeddingsSearchServer::new(self)
    }
}

#[tonic::async_trait]
impl<T: SimdData> PeerEmbeddingsSearch for PeerEmbeddingsSearchImpl<T> {
    type GetPeerCacheStream = ReceiverStream<Result<GetPeerCacheResponse, Status>>;

    async fn get_peer_cache(
        &self,
        request: Request<GetPeerCacheRequest>,
    ) -> Result<Response<Self::GetPeerCacheStream>, Status> {
        let tenant_info = tenant_info_from_grpc_req(&request)
            .ok_or_else(|| Status::internal("tenant_info must be set"))?;
        let request_context = RequestContext::try_from(request.metadata())?;
        let span =
            tracing::info_span!("get_peer_cache", request_id = %request_context.request_id());
        log_response_fn(|| async {
            if tenant_info.tenant_id.clone().unwrap_or_default() != TenantId::default() {
                tracing::error!("tenant_id must not be set: got {:?}", tenant_info);
                return Err(Status::unauthenticated("invalid tenant_id"));
            }
            if tenant_info.shard_namespace != self.namespace {
                tracing::error!("shard_namespace must match: got {:?}", tenant_info);
                return Err(Status::unauthenticated("invalid shard_namespace"));
            }
            tenant_info.validate_scope(TokenScope::ContentR)?;

            let (tx, rx) = mpsc::channel::<Result<GetPeerCacheResponse, Status>>(32);
            let data = self.data.clone();
            let inner_span = tracing::info_span!("replicate_cache_stream", request_id = %request_context.request_id());
            let max_replication_age_s = MAX_REPLICATION_AGE.get_from(&self.feature_flags);

            tokio::spawn(async move {
                tracing::info!("get_peer_cache: start");
                let cutoff_timestamp = if max_replication_age_s == 0 {
                    -1 // epoch times should always be >= 0
                } else {
                    chrono::Utc::now().timestamp_micros() - max_replication_age_s * 1000000
                };
                tracing::info!("Ignoring entries with timestamp older than: {}us epoch", cutoff_timestamp);

                let mut i = data.embedding_cache.iter();
                while let Some(cache_entry) = i.recv().await {
                    match cache_entry {
                        Ok(entry) => {
                            tracing::debug!(
                                "Cache entry time: {}, cutoff time: {}, prune: {}",
                                entry.timestamp_us,
                                cutoff_timestamp,
                                entry.timestamp_us < cutoff_timestamp
                            );
                            if entry.timestamp_us < cutoff_timestamp {
                                tracing::debug!("Ignoring cache entry due to age: tenant_id={} blob_name={:?} transformation_key={}, sub_key={}",
                                    entry.tenant_id,
                                    entry.blob_name,
                                    entry.transformation_key,
                                    entry.sub_key,
                                );
                                REPLICA_BLOB_SKIPPED.inc();
                                continue;
                            }
                            tracing::debug!(
                                "send replicate cache: tenant_id={} blob_name={:?} transformation_key={:?} sub_key={:?}",
                                entry.tenant_id,
                                entry.blob_name,
                                entry.transformation_key,
                                entry.sub_key
                            );

                            REPLICA_BLOB_SENT.inc();
                            REPLICA_BYTES_SENT.inc_by(entry.tensor.contents.len() as u64);

                            if !send_and_ignore(&tx, Ok(GetPeerCacheResponse {
                                tenant_id: entry.tenant_id.to_string(),
                                blob_name: entry.blob_name.to_string(),
                                transformation_key: entry.transformation_key,
                                sub_key: entry.sub_key,
                                tensor: Some(entry.tensor),
                                timestamp_us: entry.timestamp_us,
                            }), "replicate_cache_stream").await {
                                return;
                            }
                        }
                        Err(e) => {
                            if !send_and_ignore(&tx, Err(e), "replicate_cache_stream").await {
                                return;
                            }
                        }
                    }
                }
                tracing::info!("get_peer_cache: end");
            }.instrument(inner_span));

            Ok(Response::new(ReceiverStream::new(rx)))
        }, "get_peer_cache")
        .instrument(span)
        .await
    }
}
