use std::sync::Arc;
use std::time::Duration;
use std::time::Instant;

use async_trait::async_trait;
use bytes::Bytes;
use content_manager_client::BlobScope;
use content_manager_client::ContentManagerClient;
use grpc_service::send_and_ignore;
use moka::future::Cache;
use tokio::sync::mpsc;
use tokio::sync::mpsc::Receiver;
use tonic::Result;
use tracing::Instrument;

use crate::{
    config::Config,
    metrics::{
        CacheType, CACHE_BYTES_COUNT, CACHE_DOWNLOAD_LATENCY_COLLECTOR, CACHE_ENTRY_COUNT,
        CACHE_EVICTION_COUNT_COLLECTOR, CACHE_LOOKUP_COUNT_COLLECTOR,
    },
    proto::{
        content_manager::batch_get_content_response::Response::{FinalContent, NotFoundContent},
        embeddings_search::BlobMetadata,
    },
};
use blob_names::BlobName;
use request_context::{RequestContext, TenantId, TenantInfo, EMPTY_TENANT_ID};

fn chunk_sub_key(chunk_index: usize) -> String {
    format!("chunk-{}.pb", chunk_index)
}

#[derive(Clone, Debug, Eq, PartialEq, Hash)]
pub struct ChunkKey {
    pub blob_name: BlobName,
    pub transformation_key: String,
    pub chunk_index: usize,
}

#[derive(Debug)]
pub struct ChunkEntry {
    pub content: Vec<u8>,
    pub metadata: Vec<BlobMetadata>,
}

pub type ChunkResult = Arc<ChunkEntry>;

#[async_trait]
pub trait ChunkCache {
    /// Returns the requested chunks in the same order as they were requested.
    /// The entry is None if the request chunk is missing.
    async fn get_chunks(
        &self,
        request_context: &RequestContext,
        tenant_info: &TenantInfo,
        chunk_keys: Vec<ChunkKey>,
    ) -> Receiver<Result<Option<ChunkResult>>>;
}

pub struct ChunkCacheImpl {
    data: Arc<ChunkCacheDataImpl>,
}
impl ChunkCacheImpl {
    pub fn new(
        config: Config,
        content_manager: Arc<dyn ContentManagerClient + Send + Sync + 'static>,
    ) -> Self {
        Self {
            data: Arc::new(ChunkCacheDataImpl::new(config, content_manager)),
        }
    }
}

#[async_trait]
impl ChunkCache for ChunkCacheImpl {
    async fn get_chunks(
        &self,
        request_context: &RequestContext,
        tenant_info: &TenantInfo,
        chunk_keys: Vec<ChunkKey>,
    ) -> Receiver<Result<Option<ChunkResult>>> {
        // 32 is a somewhat arbitrary size, usually callers ask for the top 32 results
        let (tx, rx) = mpsc::channel::<Result<Option<ChunkResult>>>(32);
        let data = self.data.clone();
        let request_context = request_context.clone();
        let tenant_info = tenant_info.clone();
        tracing::info!(
            "Getting {} chunks with {} cached entries taking {} bytes",
            chunk_keys.len(),
            self.data.cache.entry_count(),
            self.data.cache.weighted_size()
        );

        let mut cached = vec![None; chunk_keys.len()];

        let tenant_id = tenant_info.tenant_id.clone().unwrap_or(EMPTY_TENANT_ID);
        for (index, chunk_key) in chunk_keys.iter().enumerate() {
            let cached_chunk = data.read_from_cache(&tenant_id, chunk_key.clone()).await;
            if let Some(cached_chunk) = cached_chunk {
                CACHE_LOOKUP_COUNT_COLLECTOR
                    .with_label_values(&[
                        CacheType::Chunk.as_str(),
                        "hit",
                        tenant_info.metrics_tenant_name(),
                    ])
                    .inc();
                cached[index] = Some(cached_chunk);
            } else {
                CACHE_LOOKUP_COUNT_COLLECTOR
                    .with_label_values(&[
                        CacheType::Chunk.as_str(),
                        "miss",
                        tenant_info.metrics_tenant_name(),
                    ])
                    .inc();
            }
        }

        CACHE_ENTRY_COUNT
            .with_label_values(&[CacheType::Chunk.as_str()])
            .set(data.cache.entry_count() as f64);
        CACHE_BYTES_COUNT
            .with_label_values(&[CacheType::Chunk.as_str()])
            .set(data.cache.weighted_size() as f64);

        let skip_download = cached.iter().all(|x| x.is_some());
        let mut downloaded_chunks_rx = if skip_download {
            // Create an empty receiver stream to simplify later code
            let (_empty_tx, empty_rx) = mpsc::channel::<Result<Option<ChunkResult>>>(1);
            empty_rx
        } else {
            let missing_keys = chunk_keys
                .iter()
                .enumerate()
                .filter(|(index, _key)| cached[*index].is_none())
                .map(|(_, key)| key)
                .cloned()
                .collect();
            data.read_from_content_manager(&request_context, &tenant_info, missing_keys)
                .await
        };

        let background_span = tracing::info_span!("get_chunks background");
        tokio::spawn(
            async move {
                for (index, x) in cached.iter().enumerate() {
                    match x {
                        // This chunk is cached
                        Some(cached_chunk) => {
                            if !send_and_ignore(
                                &tx,
                                Ok(Some(cached_chunk.clone())),
                                "get_chunks cached chunk",
                            )
                            .await
                            {
                                return;
                            }
                        }
                        // This chunk is not cached, listen for the next chunk
                        // from the download stream (which is ordered).
                        None => match downloaded_chunks_rx.recv().await {
                            Some(Ok(maybe_chunk)) => {
                                if let Some(chunk) = maybe_chunk.clone() {
                                    data.cache
                                        .insert(
                                            (tenant_id.clone(), chunk_keys[index].clone()),
                                            chunk.clone(),
                                        )
                                        .await;
                                } else {
                                    tracing::error!(
                                        "get_chunks received None for chunk {:?}",
                                        chunk_keys[index]
                                    );
                                }
                                if !send_and_ignore(
                                    &tx,
                                    Ok(maybe_chunk),
                                    "get_chunks downloaded chunk",
                                )
                                .await
                                {
                                    return;
                                }
                            }
                            Some(Err(e)) => {
                                send_and_ignore(&tx, Err(e), "get_chunks error").await;
                                return;
                            }
                            None => {
                                // End of stream
                                tracing::error!(
                                    "missing chunk {:?} and any other chunks after index {}",
                                    chunk_keys[index],
                                    index
                                );
                                return;
                            }
                        },
                    }
                }
            }
            .instrument(background_span),
        );

        rx
    }
}

struct ChunkCacheDataImpl {
    pub content_manager: Arc<dyn ContentManagerClient + Send + Sync + 'static>,
    pub cache: Cache<(TenantId, ChunkKey), ChunkResult>,
}

impl ChunkCacheDataImpl {
    pub fn new(
        config: Config,
        content_manager: Arc<dyn ContentManagerClient + Send + Sync + 'static>,
    ) -> Self {
        let cache = Cache::builder()
            .max_capacity(config.chunk_cache_size_bytes)
            .eviction_policy(config.chunk_cache_eviction.into())
            // Add an estimate of the key size to the size of the content
            .weigher(|_, v: &ChunkResult| (v.as_ref().content.len() as u32 + 128))
            .time_to_idle(Duration::from_secs(config.cache_tti_seconds))
            .eviction_listener(|_, _, c| {
                let removal_cause = format!("{:?}", c);
                CACHE_EVICTION_COUNT_COLLECTOR
                    .with_label_values(&[CacheType::Chunk.as_str(), &removal_cause])
                    .inc();
            })
            .build();
        ChunkCacheDataImpl {
            content_manager,
            cache,
        }
    }

    pub async fn read_from_cache(
        &self,
        tenant_id: &TenantId,
        chunk_key: ChunkKey,
    ) -> Option<ChunkResult> {
        let cache_key = (tenant_id.clone(), chunk_key);
        self.cache.get(&cache_key).await
    }

    // The ordering of the output vector is the same as the input vector - the
    // two will the same length and the indices will match up.
    // This function does not update the cache, that is left to the caller.
    pub async fn read_from_content_manager(
        &self,
        request_context: &RequestContext,
        tenant_info: &TenantInfo,
        chunk_keys: Vec<ChunkKey>,
    ) -> Receiver<Result<Option<ChunkResult>>> {
        let blobs = chunk_keys
            .iter()
            .map(|key| {
                (
                    key.blob_name.clone(),
                    BlobScope {
                        transformation_key: key.transformation_key.clone(),
                        sub_key: chunk_sub_key(key.chunk_index),
                    },
                )
            })
            .collect();

        let mut response = self
            .content_manager
            .get_content_multiple_scopes(request_context, &tenant_info.tenant_id, blobs, None)
            .await;

        // 32 is a somewhat arbitrary size, usually callers ask for the top 32 results
        let (tx, rx) = mpsc::channel::<Result<Option<ChunkResult>>>(32);

        let download_span = tracing::info_span!("read_from_content_manager download");
        let download_start = Instant::now();
        let metrics_tenant_name = tenant_info.metrics_tenant_name().to_string();
        tokio::spawn(
            async move {
                let mut current_index = 0;
                while let Some(next_message) = response.recv().await {
                    let next_message = match next_message {
                        Ok(message) => message,
                        Err(e) => {
                            send_and_ignore(&tx, Err(e), "get_chunks error").await;
                            return;
                        }
                    };
                    match next_message.response {
                        Some(NotFoundContent(missing_content)) => {
                            tracing::error!("chunk not found: {:?}", missing_content);
                            if !send_and_ignore(&tx, Ok(None), "get_chunks none error").await {
                                return;
                            }
                        }
                        Some(FinalContent(final_content)) => {
                            assert_eq!(
                                String::from(&chunk_keys[current_index].blob_name),
                                final_content.blob_name
                            );
                            assert_eq!(
                                chunk_keys[current_index].transformation_key,
                                final_content.transformation_key
                            );
                            let bytes = Bytes::from(final_content.content);
                            let entry = Arc::new(ChunkEntry {
                                content: bytes.to_vec(),
                                metadata: final_content
                                    .metadata
                                    .iter()
                                    .map(|metadata| BlobMetadata {
                                        key: metadata.key.clone(),
                                        value: metadata.value.clone(),
                                    })
                                    .collect(),
                            });
                            if !send_and_ignore(&tx, Ok(Some(entry)), "get_chunks chunk").await {
                                return;
                            }
                        }
                        None => {}
                    }
                    current_index += 1;
                }

                if current_index != chunk_keys.len() {
                    tracing::error!(
                        "Failed to get all chunks from content manager: expected={}, actual={}",
                        chunk_keys.len(),
                        current_index
                    );
                }
                CACHE_DOWNLOAD_LATENCY_COLLECTOR
                    .with_label_values(&[CacheType::Chunk.as_str(), &metrics_tenant_name])
                    .observe(download_start.elapsed().as_secs_f64());
            }
            .instrument(download_span),
        );

        rx
    }
}

#[cfg(test)]
mod tests {
    use crate::config;
    use crate::util::test_utils::{
        create_blob_scope, create_chunk_key, create_not_found_response, create_success_response,
        default_get_content_multiple_scopes_handler,
    };
    use content_manager_client::MockContentManagerClient;
    use mockall::predicate;
    use tokio::sync::mpsc;

    use super::*;

    async fn get_chunks_list(
        cache: &ChunkCacheImpl,
        request_context: &RequestContext,
        tenant_info: &TenantInfo,
        chunk_keys: Vec<ChunkKey>,
    ) -> Result<Vec<ChunkResult>> {
        let mut result = vec![];
        let mut rx = cache
            .get_chunks(request_context, tenant_info, chunk_keys)
            .await;
        while let Some(chunk) = rx.recv().await {
            if let Some(chunk) = chunk? {
                result.push(chunk.clone());
            }
        }
        Ok(result)
    }

    #[tokio::test]
    async fn test_get_chunks() {
        let blob1 = BlobName::from_bytes(&[0; 32]).unwrap();
        let blob2 = BlobName::from_bytes(&[1; 32]).unwrap();

        let mut client: MockContentManagerClient = MockContentManagerClient::new();

        // First call: expect request for blob1 chunk-0 and blob2 chunk-3
        client
            .expect_get_content_multiple_scopes()
            .times(1)
            .with(
                predicate::always(), // request_context
                predicate::always(), // tenant_id
                predicate::eq(vec![
                    (blob1.clone(), create_blob_scope("chunk-0.pb")),
                    (blob2.clone(), create_blob_scope("chunk-3.pb")),
                ]),
                predicate::always(), // deadline
            )
            .returning(default_get_content_multiple_scopes_handler());

        // Second call: expect request for blob1 chunk-1 and chunk-2 (the missing ones)
        client
            .expect_get_content_multiple_scopes()
            .times(1)
            .with(
                predicate::always(), // request_context
                predicate::always(), // tenant_id
                predicate::eq(vec![
                    (blob1.clone(), create_blob_scope("chunk-1.pb")),
                    (blob1.clone(), create_blob_scope("chunk-2.pb")),
                ]),
                predicate::always(), // deadline
            )
            .returning(default_get_content_multiple_scopes_handler());

        let client = Arc::new(client);
        let cache = ChunkCacheImpl::new(Config::default(), client.clone());
        let tenant_info = TenantInfo::new_for_test();

        // First request: New chunks - new content manager request
        let request_context_1 = RequestContext::new_for_test();
        let chunk_keys_1 = vec![
            create_chunk_key(blob1.clone(), 0),
            create_chunk_key(blob2.clone(), 3),
        ];
        let result_1 = get_chunks_list(
            &cache,
            &request_context_1,
            &tenant_info,
            chunk_keys_1.clone(),
        )
        .await
        .expect("Failed to get chunks");
        assert_eq!(result_1.len(), 2);

        // Second request: Same chunks as previous - no new content manager request
        let request_context_2 = RequestContext::new_for_test();
        let chunk_keys_2 = chunk_keys_1.clone();
        let result_2 = get_chunks_list(
            &cache,
            &request_context_2,
            &tenant_info,
            chunk_keys_2.clone(),
        )
        .await
        .expect("Failed to get chunks");
        assert_eq!(result_2.len(), 2);

        // Third request: New chunks - should trigger second content manager call
        let request_context_3 = RequestContext::new_for_test();
        let chunk_keys_3 = vec![
            create_chunk_key(blob1.clone(), 0), // This one is cached
            create_chunk_key(blob1.clone(), 1), // This one is new
            create_chunk_key(blob1.clone(), 2), // This one is new
        ];
        let result_3 = get_chunks_list(
            &cache,
            &request_context_3,
            &tenant_info,
            chunk_keys_3.clone(),
        )
        .await
        .expect("Failed to get chunks");
        assert_eq!(result_3.len(), 3);

        // Fourth request: Back to first chunks - should be served from cache (no new call)
        let request_context_4 = RequestContext::new_for_test();
        let chunk_keys_4 = chunk_keys_1.clone();
        let result_4 = get_chunks_list(
            &cache,
            &request_context_4,
            &tenant_info,
            chunk_keys_4.clone(),
        )
        .await
        .expect("Failed to get chunks");
        assert_eq!(result_4.len(), 2);
    }

    #[tokio::test]
    async fn test_get_chunks_with_missing() {
        let real_blob = BlobName::from_bytes(&[0; 32]).unwrap();
        let fake_blob = BlobName::from_bytes(&[1; 32]).unwrap();

        let mut client = MockContentManagerClient::new();

        // Expect a call for all 3 chunks, but only real_blob chunks will return content
        client
            .expect_get_content_multiple_scopes()
            .times(1)
            .with(
                predicate::always(), // request_context
                predicate::always(), // tenant_id
                predicate::eq(vec![
                    (real_blob.clone(), create_blob_scope("chunk-0.pb")),
                    (fake_blob.clone(), create_blob_scope("chunk-2.pb")),
                    (real_blob.clone(), create_blob_scope("chunk-1.pb")),
                ]),
                predicate::always(), // deadline
            )
            .returning({
                let real_blob = real_blob.clone();
                move |_, _, blobs, _| {
                    let (tx, rx) = mpsc::channel(blobs.len());
                    for (blob_name, blob_scope) in blobs {
                        let response = if blob_name == real_blob {
                            // Return content for real blob
                            create_success_response(&blob_name, &blob_scope, Vec::new())
                        } else {
                            // Return not found for fake blob
                            create_not_found_response(&blob_name, &blob_scope)
                        };
                        let _ = tx.try_send(Ok(response));
                    }
                    rx
                }
            });

        let client = Arc::new(client);
        let cache = ChunkCacheImpl::new(Config::default(), client.clone());

        let tenant_info = TenantInfo::new_for_test();

        // 1 existing chunk, 1 missing, and another existing
        let request_context_1 = RequestContext::new_for_test();
        let chunk_keys_1 = vec![
            create_chunk_key(real_blob.clone(), 0),
            create_chunk_key(fake_blob.clone(), 2),
            create_chunk_key(real_blob.clone(), 1),
        ];
        let mut rx = cache
            .get_chunks(&request_context_1, &tenant_info, chunk_keys_1)
            .await;

        // We get results for all chunks, 1 found, 1 missing, and another found
        let resp_1 = rx.recv().await;
        assert!(resp_1.is_some());
        assert!(resp_1.as_ref().unwrap().is_ok());
        assert!(resp_1.as_ref().unwrap().as_ref().unwrap().is_some());

        let resp_2 = rx.recv().await;
        assert!(resp_2.is_some());
        assert!(resp_2.as_ref().unwrap().is_ok());
        assert!(resp_2.as_ref().unwrap().as_ref().unwrap().is_none());

        let resp_3 = rx.recv().await;
        assert!(resp_3.is_some());
        assert!(resp_3.as_ref().unwrap().is_ok());
        assert!(resp_3.as_ref().unwrap().as_ref().unwrap().is_some());

        assert!(rx.recv().await.is_none());
    }

    #[tokio::test]
    async fn test_cache_tti() {
        let keep_blob = BlobName::from_bytes(&[0; 32]).unwrap();
        let stop_blob = BlobName::from_bytes(&[1; 32]).unwrap();

        let mut client = MockContentManagerClient::new();

        // Expect one call for both blobs initially
        client
            .expect_get_content_multiple_scopes()
            .times(1)
            .with(
                predicate::always(), // request_context
                predicate::always(), // tenant_id
                predicate::eq(vec![
                    (keep_blob.clone(), create_blob_scope("chunk-0.pb")),
                    (stop_blob.clone(), create_blob_scope("chunk-0.pb")),
                ]),
                predicate::always(), // deadline
            )
            .returning(default_get_content_multiple_scopes_handler());

        let client = Arc::new(client);
        let config = config::Config {
            cache_tti_seconds: 3,
            ..Default::default()
        };
        let cache = ChunkCacheImpl::new(config.clone(), client.clone());

        let request_context = &RequestContext::new_for_test();
        let tenant_info = &TenantInfo::new_for_test();
        let tenant_id = tenant_info.tenant_id.clone().unwrap_or(EMPTY_TENANT_ID);
        let chunk_key_keep_checking = create_chunk_key(keep_blob.clone(), 0);
        let chunk_key_stop_checking = create_chunk_key(stop_blob.clone(), 0);

        // initial read to populate cache
        let mut rx = cache
            .get_chunks(
                request_context,
                tenant_info,
                vec![
                    chunk_key_keep_checking.clone(),
                    chunk_key_stop_checking.clone(),
                ],
            )
            .await;
        let resp_1 = rx.recv().await;
        assert!(resp_1.is_some(), "Failed to get chunk for initial read");
        assert!(
            resp_1.as_ref().unwrap().is_ok(),
            "Failed to get chunk for initial read"
        );
        assert!(
            resp_1.as_ref().unwrap().as_ref().unwrap().is_some(),
            "Failed to get chunk for initial read"
        );
        let resp_2 = rx.recv().await;
        assert!(resp_2.is_some(), "Failed to get chunk for initial read");
        assert!(
            resp_2.as_ref().unwrap().is_ok(),
            "Failed to get chunk for initial read"
        );
        assert!(
            resp_2.as_ref().unwrap().as_ref().unwrap().is_some(),
            "Failed to get chunk for initial read"
        );
        assert!(
            rx.recv().await.is_none(),
            "Got extra chunk for initial read"
        );

        // our cache exp is 3s, so expect that this is still around
        tokio::time::sleep(Duration::from_secs(2)).await;
        cache
            .data
            .read_from_cache(&tenant_id, chunk_key_keep_checking.clone())
            .await
            .expect("Failed to get chunk");

        // wait another two seconds -- recently accessed chunk should be around; other should not
        tokio::time::sleep(Duration::from_secs(2)).await;
        cache
            .data
            .read_from_cache(&tenant_id, chunk_key_keep_checking.clone())
            .await
            .expect("Failed to get chunk");

        let result = cache
            .data
            .read_from_cache(&tenant_id, chunk_key_stop_checking.clone())
            .await;
        assert!(result.is_none(), "chunk_stop_checking found");
    }
}
