load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "go",
    srcs = ["client.go"],
    importpath = "github.com/augmentcode/augment/services/chat_host/client/go",
    visibility = ["//visibility:public"],
    deps = [
        "//base/blob_names:blob_names_go_proto",
        "//base/feature_flags:feature_flags_go",
        "//services/api_proxy:model_finder_go_proto",
        "//services/chat_host:chat_host_go_proto",
        "//services/deploy/model_instance:model_instance_go_proto",
        "//services/lib/request_context:request_context_go",
        "@com_github_rs_zerolog//log",
        "@org_golang_google_grpc//:grpc",
        "@org_golang_google_grpc//credentials",
    ],
)
