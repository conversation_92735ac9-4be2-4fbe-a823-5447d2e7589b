package main

import (
	"context"
	"testing"

	featureflags "github.com/augmentcode/augment/base/feature_flags"
	"github.com/augmentcode/augment/base/logging/audit"
	auditocsf "github.com/augmentcode/augment/base/logging/audit_ocsf"
	auth_entities "github.com/augmentcode/augment/services/auth/central/server/auth_entities_proto"
	stripe_event "github.com/augmentcode/augment/services/auth/stripe_webhook/stripe_event"
	"github.com/augmentcode/augment/services/billing/lib/orb"
	orb_config "github.com/augmentcode/augment/services/billing/lib/orb/config"
	stripelib "github.com/augmentcode/augment/services/billing/lib/stripe"
	ripublisher "github.com/augmentcode/augment/services/request_insight/publisher"
	tw_client "github.com/augmentcode/augment/services/tenant_watcher/client"
	tw_pb "github.com/augmentcode/augment/services/tenant_watcher/proto"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
	"github.com/stripe/stripe-go/v80"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/types/known/timestamppb"
)

func TestStripeEventProcessorIntegration(t *testing.T) {
	// Set up BigTable fixture
	bigtableFixture := NewBigtableFixture(t)
	defer bigtableFixture.Cleanup()

	// Create a context
	ctx := context.Background()

	// Create a DAO factory with the BigTable fixture
	daoFactory := NewDAOFactory(bigtableFixture.Table)

	// Create a mock Stripe client
	mockStripeClient := stripelib.NewMockStripeClient()

	// Create a mock Orb client
	mockOrbClient := orb.NewMockOrbClient()

	// Create a mock feature flag handler
	featureFlagHandle := featureflags.NewLocalFeatureFlagHandler()

	// Create auth servicer for API testing
	mockTenantWatcherClient := &tw_client.MockTenantWatcherClient{
		Tenants: []*tw_pb.Tenant{
			{
				Id:             "test-tenant-id",
				Name:           "test-tenant",
				ShardNamespace: "test.com",
				Cloud:          "test-cloud",
				Tier:           tw_pb.TenantTier_PROFESSIONAL,
			},
			{
				Id:             "test-community-tenant-id",
				Name:           "test-community-tenant",
				ShardNamespace: "test.com",
				Cloud:          "test-cloud",
				Tier:           tw_pb.TenantTier_COMMUNITY,
			},
		},
	}
	tenantMap := NewTenantMap(daoFactory, mockTenantWatcherClient, "test.com", featureFlagHandle, NewMockAsyncOpsPublisher(), audit.NewDefaultAuditLogger())
	ocsfAuditLogger, _ := auditocsf.NewMockOCSFAuditLogger()

	authServicer := NewAuthGrpcServer(
		featureFlagHandle,
		daoFactory,
		tenantMap,
		audit.NewDefaultAuditLogger(),
		ripublisher.NewRequestInsightPublisherMock(),
		NewMockAsyncOpsPublisher(),
		nil, // asyncOpsWorker
		&StripeConfig{Enabled: false},
		&orb_config.OrbConfig{Enabled: false},
		mockStripeClient,
		nil, // billingEventProcessor
		ocsfAuditLogger,
	)
	authServicer.orbClient = mockOrbClient

	// Create the Stripe event processor
	processor := &StripeEventProcessor{
		config:            &Config{},
		daoFactory:        daoFactory,
		stripeClient:      mockStripeClient,
		featureFlagHandle: featureFlagHandle,
		orbClient:         mockOrbClient,
		asyncOpsPublisher: NewMockAsyncOpsPublisher(),
		tenantMap:         tenantMap,
	}

	t.Run("Setup Intent Succeeded Event with Orb", func(t *testing.T) {
		// Create a test user with Orb billing method
		userID := uuid.New().String()
		stripeCustomerID := "cus_" + uuid.New().String()
		orbSubscriptionID := "sub_" + uuid.New().String()
		orbCustomerID := "cus_orb_" + uuid.New().String()

		user := &auth_entities.User{
			Id:                userID,
			Email:             "<EMAIL>",
			StripeCustomerId:  stripeCustomerID,
			OrbCustomerId:     orbCustomerID,
			OrbSubscriptionId: orbSubscriptionID,
		}

		userDAO := daoFactory.GetUserDAO()
		_, err := userDAO.Create(ctx, user)
		require.NoError(t, err, "Failed to create test user")

		// Add the user to the mock Stripe client
		mockStripeClient.AddCustomer(user.Email, stripeCustomerID, userID)

		// Create a subscription for the user with HasPaymentMethod = false
		subscriptionDAO := daoFactory.GetSubscriptionDAO()
		subscription := &auth_entities.Subscription{
			SubscriptionId:    orbSubscriptionID,
			StripeCustomerId:  stripeCustomerID,
			PriceId:           "price_test",
			Status:            auth_entities.Subscription_ACTIVE,
			Seats:             5,
			CancelAtPeriodEnd: false,
			HasPaymentMethod:  false, // Initially false
			Owner:             &auth_entities.Subscription_UserId{UserId: userID},
			CreatedAt:         timestamppb.Now(),
			UpdatedAt:         timestamppb.Now(),
		}
		_, err = subscriptionDAO.Create(ctx, subscription)
		require.NoError(t, err, "Failed to create subscription")

		// Create a setup intent succeeded event
		setupIntentID := "seti_" + uuid.New().String()
		paymentMethodID := "pm_" + uuid.New().String()
		event := &stripe_event.StripeEvent{
			EventType:        "setup_intent.succeeded",
			StripeCustomerId: proto.String(stripeCustomerID),
			Event: &stripe_event.StripeEvent_SetupIntentEvent{
				SetupIntentEvent: &stripe_event.SetupIntentEvent{
					SetupIntentId:   setupIntentID,
					PaymentMethodId: paymentMethodID,
					Status:          "succeeded",
					CustomerId:      stripeCustomerID,
					Livemode:        false,
					Usage:           "off_session",
				},
			},
		}

		// Process the event directly with the Orb-enabled processor
		err = processor.processStripeEvent(ctx, event)
		require.NoError(t, err, "Failed to process setup intent succeeded event")

		// Verify the user still exists
		updatedUser, err := userDAO.Get(ctx, userID)
		require.NoError(t, err, "Failed to get user")
		assert.Equal(t, stripeCustomerID, updatedUser.StripeCustomerId, "User should have the correct Stripe customer ID")

		// Verify the subscription was updated with HasPaymentMethod = true
		updatedSubscription, err := subscriptionDAO.Get(ctx, orbSubscriptionID)
		require.NoError(t, err, "Failed to get updated subscription")
		assert.True(t, updatedSubscription.HasPaymentMethod, "Subscription should have HasPaymentMethod set to true")
	})

	t.Run("Setup Intent with Unknown Customer", func(t *testing.T) {
		// Create a setup intent event with a customer ID that doesn't exist
		stripeCustomerID := "cus_nonexistent_" + uuid.New().String()
		setupIntentID := "seti_" + uuid.New().String()
		paymentMethodID := "pm_" + uuid.New().String()
		event := &stripe_event.StripeEvent{
			EventType:        "setup_intent.succeeded",
			StripeCustomerId: proto.String(stripeCustomerID),
			Event: &stripe_event.StripeEvent_SetupIntentEvent{
				SetupIntentEvent: &stripe_event.SetupIntentEvent{
					SetupIntentId:   setupIntentID,
					PaymentMethodId: paymentMethodID,
					Status:          "succeeded",
					CustomerId:      stripeCustomerID,
					Livemode:        false,
					Usage:           "off_session",
				},
			},
		}

		// Process the event directly
		err := processor.processStripeEvent(ctx, event)
		require.Error(t, err, "Should error when processing setup intent event with unknown customer")
	})

	t.Run("Setup Intent with Missing Customer ID", func(t *testing.T) {
		// Create a setup intent event with no customer ID
		setupIntentID := "seti_" + uuid.New().String()
		paymentMethodID := "pm_" + uuid.New().String()
		event := &stripe_event.StripeEvent{
			EventType: "setup_intent.succeeded",
			// No StripeCustomerId field
			Event: &stripe_event.StripeEvent_SetupIntentEvent{
				SetupIntentEvent: &stripe_event.SetupIntentEvent{
					SetupIntentId:   setupIntentID,
					PaymentMethodId: paymentMethodID,
					Status:          "succeeded",
					// No CustomerId field
					Livemode: false,
					Usage:    "off_session",
				},
			},
		}

		// Process the event directly
		err := processor.processStripeEvent(ctx, event)
		require.Error(t, err, "Should error when processing setup intent event with missing customer ID")
	})

	t.Run("Payment Method Detached Event with Orb", func(t *testing.T) {
		// Create a mock Orb client
		mockOrbClient := orb.NewMockOrbClient()

		// Create a processor with the Orb client
		processorWithOrb := &StripeEventProcessor{
			config:            &Config{},
			daoFactory:        daoFactory,
			stripeClient:      mockStripeClient,
			featureFlagHandle: featureFlagHandle,
			orbClient:         mockOrbClient,
		}

		// Create a test user with Orb billing method
		userID := uuid.New().String()
		stripeCustomerID := "cus_" + uuid.New().String()
		subscriptionID := "sub_" + uuid.New().String()

		user := &auth_entities.User{
			Id:                userID,
			Email:             "<EMAIL>",
			StripeCustomerId:  stripeCustomerID,
			OrbSubscriptionId: subscriptionID, // Add OrbSubscriptionId
		}

		userDAO := daoFactory.GetUserDAO()
		_, err := userDAO.Create(ctx, user)
		require.NoError(t, err, "Failed to create test user")

		// Create a subscription for the user with HasPaymentMethod = true
		subscriptionDAO := daoFactory.GetSubscriptionDAO()
		subscription := &auth_entities.Subscription{
			SubscriptionId:    subscriptionID,
			StripeCustomerId:  stripeCustomerID,
			PriceId:           "price_test",
			Status:            auth_entities.Subscription_ACTIVE,
			Seats:             5,
			CancelAtPeriodEnd: false,
			HasPaymentMethod:  true, // Initially true
			Owner:             &auth_entities.Subscription_UserId{UserId: userID},
			CreatedAt:         timestamppb.Now(),
			UpdatedAt:         timestamppb.Now(),
		}
		_, err = subscriptionDAO.Create(ctx, subscription)
		require.NoError(t, err, "Failed to create subscription")

		// Configure mock to return no payment method after payment method is detached
		mockStripeClient.AddCustomer(user.Email, stripeCustomerID, userID)
		mockStripeClient.SetCustomerHasPaymentMethod(stripeCustomerID, false)

		// Create a payment method detached event
		paymentMethodID := "pm_" + uuid.New().String()
		event := &stripe_event.StripeEvent{
			EventType:        "payment_method.detached",
			StripeCustomerId: proto.String(stripeCustomerID),
			Event: &stripe_event.StripeEvent_PaymentMethodEvent{
				PaymentMethodEvent: &stripe_event.PaymentMethodEvent{
					PaymentMethodId: paymentMethodID,
				},
			},
		}

		// Process the event directly with the Orb-enabled processor
		err = processorWithOrb.processStripeEvent(ctx, event)
		require.NoError(t, err, "Failed to process payment method detached event")

		// Verify the user still exists
		updatedUser, err := userDAO.Get(ctx, userID)
		require.NoError(t, err, "Failed to get user")
		assert.Equal(t, stripeCustomerID, updatedUser.StripeCustomerId, "User should have the correct Stripe customer ID")

		// Verify the subscription was updated with HasPaymentMethod = false
		updatedSubscription, err := subscriptionDAO.Get(ctx, subscriptionID)
		require.NoError(t, err, "Failed to get updated subscription")
		assert.False(t, updatedSubscription.HasPaymentMethod, "Subscription should have HasPaymentMethod set to false")
	})

	t.Run("Payment Method Detached with Unknown Customer", func(t *testing.T) {
		// Create a payment method detached event with a customer ID that doesn't exist
		stripeCustomerID := "cus_nonexistent_" + uuid.New().String()
		paymentMethodID := "pm_" + uuid.New().String()
		event := &stripe_event.StripeEvent{
			EventType:        "payment_method.detached",
			StripeCustomerId: proto.String(stripeCustomerID),
			Event: &stripe_event.StripeEvent_PaymentMethodEvent{
				PaymentMethodEvent: &stripe_event.PaymentMethodEvent{
					PaymentMethodId: paymentMethodID,
				},
			},
		}

		// Process the event directly
		err := processor.processStripeEvent(ctx, event)
		require.Error(t, err, "Should error when processing payment method detached event with unknown customer")
	})

	t.Run("Customer Updated, successfully sync address", func(t *testing.T) {
		// Create a test user
		userID := uuid.New().String()
		stripeCustomerID := "cus_" + uuid.New().String()
		orbCustomerID := "orb_" + uuid.New().String()

		user := &auth_entities.User{
			Id:               userID,
			Email:            "<EMAIL>",
			StripeCustomerId: stripeCustomerID,
			OrbCustomerId:    orbCustomerID,
		}

		userDAO := daoFactory.GetUserDAO()
		_, err := userDAO.Create(ctx, user)
		require.NoError(t, err, "Failed to create test user")

		// Create a customer updated event
		event := &stripe_event.StripeEvent{
			EventType:        "customer.updated",
			StripeCustomerId: proto.String(stripeCustomerID),
			Event: &stripe_event.StripeEvent_CustomerEvent{
				CustomerEvent: &stripe_event.CustomerEvent{},
			},
		}

		mockStripeClient.AddCustomer(user.Email, stripeCustomerID, userID)
		mockStripeClient.SetCustomerHasPaymentMethod(stripeCustomerID, true)
		mockStripeClient.SetCustomerAddress(stripeCustomerID, &stripe.Address{
			Line1:      "123 Main St",
			Line2:      "Apt 4B",
			City:       "San Francisco",
			State:      "CA",
			PostalCode: "94105",
			Country:    "US",
		})

		// Configure mock orb client
		mockOrbClient.On("UpdateCustomerInformation", mock.Anything, orbCustomerID, mock.Anything).Return(nil)

		// Process the event directly
		err = processor.processStripeEvent(ctx, event)
		require.NoError(t, err, "Failed to process customer updated event")
		mockOrbClient.AssertNumberOfCalls(t, "UpdateCustomerInformation", 1)
		mockOrbClient.AssertCalled(t, "UpdateCustomerInformation", mock.Anything, orbCustomerID,
			mock.MatchedBy(func(updateInfo orb.OrbCustomerUpdateInfo) bool {
				return updateInfo.Address != nil && updateInfo.Address.Line1 == "123 Main St" && updateInfo.Address.Line2 == "Apt 4B" && updateInfo.Address.City == "San Francisco" && updateInfo.Address.State == "CA" && updateInfo.Address.PostalCode == "94105" && updateInfo.Address.Country == "US"
			}))
	})

	t.Run("Customer Updated, successfully sync name", func(t *testing.T) {
		mockOrbClient = orb.NewMockOrbClient()
		processor.orbClient = mockOrbClient

		// Create a test user
		userID := uuid.New().String()
		stripeCustomerID := "cus_" + uuid.New().String()
		orbCustomerID := "orb_" + uuid.New().String()

		user := &auth_entities.User{
			Id:               userID,
			Email:            "<EMAIL>",
			StripeCustomerId: stripeCustomerID,
			OrbCustomerId:    orbCustomerID,
		}

		userDAO := daoFactory.GetUserDAO()
		_, err := userDAO.Create(ctx, user)
		require.NoError(t, err, "Failed to create test user")

		// Create a customer updated event
		event := &stripe_event.StripeEvent{
			EventType:        "customer.updated",
			StripeCustomerId: proto.String(stripeCustomerID),
			Event: &stripe_event.StripeEvent_CustomerEvent{
				CustomerEvent: &stripe_event.CustomerEvent{},
			},
		}

		mockStripeClient.AddCustomer(user.Email, stripeCustomerID, userID)
		mockStripeClient.SetCustomerHasPaymentMethod(stripeCustomerID, true)
		mockStripeClient.SetCustomerName(stripeCustomerID, "John Doe")

		// Configure mock orb client
		mockOrbClient.On("UpdateCustomerInformation", mock.Anything, orbCustomerID, mock.Anything).Return(nil)

		// Process the event directly
		err = processor.processStripeEvent(ctx, event)
		require.NoError(t, err, "Failed to process customer updated event")
		mockOrbClient.AssertNumberOfCalls(t, "UpdateCustomerInformation", 1)
		mockOrbClient.AssertCalled(t, "UpdateCustomerInformation", mock.Anything, orbCustomerID,
			mock.MatchedBy(func(updateInfo orb.OrbCustomerUpdateInfo) bool {
				return updateInfo.Name != nil && *updateInfo.Name == "John Doe"
			}))
	})

	// If we get a customer updated event and the customer has no address, don't add it to Orb
	t.Run("Customer Updated, no address", func(t *testing.T) {
		mockOrbClient = orb.NewMockOrbClient()
		processor.orbClient = mockOrbClient

		// Create a test user
		userID := uuid.New().String()
		stripeCustomerID := "cus_" + uuid.New().String()
		orbCustomerID := "orb_" + uuid.New().String()

		user := &auth_entities.User{
			Id:               userID,
			Email:            "<EMAIL>",
			StripeCustomerId: stripeCustomerID,
			OrbCustomerId:    orbCustomerID,
		}

		userDAO := daoFactory.GetUserDAO()
		_, err := userDAO.Create(ctx, user)
		require.NoError(t, err, "Failed to create test user")

		// Create a customer updated event
		event := &stripe_event.StripeEvent{
			EventType:        "customer.updated",
			StripeCustomerId: proto.String(stripeCustomerID),
			Event: &stripe_event.StripeEvent_CustomerEvent{
				CustomerEvent: &stripe_event.CustomerEvent{},
			},
		}

		mockStripeClient.AddCustomer(user.Email, stripeCustomerID, userID)
		mockStripeClient.SetCustomerHasPaymentMethod(stripeCustomerID, true)
		// Do not set the customer's address so that they don't have one

		// Process the event directly
		err = processor.processStripeEvent(ctx, event)
		require.NoError(t, err)
		mockOrbClient.AssertNumberOfCalls(t, "UpdateCustomerInformation", 0)
	})

	t.Run("Customer Updated, no customer found", func(t *testing.T) {
		// Create a customer updated event
		event := &stripe_event.StripeEvent{
			EventType:        "customer.updated",
			StripeCustomerId: proto.String("cus_" + uuid.New().String()),
			Event: &stripe_event.StripeEvent_CustomerEvent{
				CustomerEvent: &stripe_event.CustomerEvent{},
			},
		}

		// Process the event directly
		err := processor.processStripeEvent(ctx, event)
		require.Error(t, err)
	})

	t.Run("Tax ID created, only one tax id", func(t *testing.T) {
		mockOrbClient = orb.NewMockOrbClient()
		processor.orbClient = mockOrbClient

		// Create a test user
		userID := uuid.New().String()
		stripeCustomerID := "cus_" + uuid.New().String()
		orbCustomerID := "orb_" + uuid.New().String()

		user := &auth_entities.User{
			Id:               userID,
			Email:            "<EMAIL>",
			StripeCustomerId: stripeCustomerID,
			OrbCustomerId:    orbCustomerID,
		}

		userDAO := daoFactory.GetUserDAO()
		_, err := userDAO.Create(ctx, user)
		require.NoError(t, err, "Failed to create test user")

		// Create a tax id created event
		event := &stripe_event.StripeEvent{
			EventType:        "customer.tax_id.created",
			StripeCustomerId: proto.String(stripeCustomerID),
			Event: &stripe_event.StripeEvent_CustomerEvent{
				CustomerEvent: &stripe_event.CustomerEvent{},
			},
		}

		mockStripeClient.AddCustomer(user.Email, stripeCustomerID, userID)
		mockStripeClient.SetCustomerTaxIDs(stripeCustomerID, []*stripe.TaxID{{
			ID:      "tax_" + uuid.New().String(),
			Country: "US",
			Type:    "EIN",
			Value:   "*********",
		}})

		// Configure mock orb client
		mockOrbClient.On("UpdateCustomerVAT", ctx, orbCustomerID, mock.Anything, mock.Anything).Return(nil)

		// Process the event directly
		err = processor.processStripeEvent(ctx, event)
		require.NoError(t, err)
		mockOrbClient.AssertNumberOfCalls(t, "UpdateCustomerVAT", 1)
		mockOrbClient.AssertCalled(t, "UpdateCustomerVAT", ctx, orbCustomerID,
			mock.MatchedBy(func(vat *orb.VAT) bool {
				return vat.Country == "US" && vat.Type == "EIN" && vat.Value == "*********"
			}), mock.Anything)
	})

	t.Run("Tax ID created, multiple tax IDs", func(t *testing.T) {
		mockOrbClient = orb.NewMockOrbClient()
		processor.orbClient = mockOrbClient

		// Create a test user
		userID := uuid.New().String()
		stripeCustomerID := "cus_" + uuid.New().String()
		orbCustomerID := "orb_" + uuid.New().String()

		user := &auth_entities.User{
			Id:               userID,
			Email:            "<EMAIL>",
			StripeCustomerId: stripeCustomerID,
			OrbCustomerId:    orbCustomerID,
		}

		userDAO := daoFactory.GetUserDAO()
		_, err := userDAO.Create(ctx, user)
		require.NoError(t, err, "Failed to create test user")

		// Create a tax id created event
		event := &stripe_event.StripeEvent{
			EventType:        "customer.tax_id.created",
			StripeCustomerId: proto.String(stripeCustomerID),
			Event: &stripe_event.StripeEvent_CustomerEvent{
				CustomerEvent: &stripe_event.CustomerEvent{},
			},
		}

		mockStripeClient.AddCustomer(user.Email, stripeCustomerID, userID)
		mockStripeClient.SetCustomerTaxIDs(stripeCustomerID, []*stripe.TaxID{{
			ID:      "tax_" + uuid.New().String(),
			Country: "US",
			Type:    "EIN",
			Value:   "*********",
			Created: 100,
		}, {
			ID:      "tax_" + uuid.New().String(),
			Country: "US",
			Type:    "EIN",
			Value:   "*********",
			Created: 200,
		}})

		// Configure mock orb client
		mockOrbClient.On("UpdateCustomerVAT", ctx, orbCustomerID, mock.Anything, mock.Anything).Return(nil)

		// Process the event directly
		err = processor.processStripeEvent(ctx, event)
		require.NoError(t, err)
		mockOrbClient.AssertNumberOfCalls(t, "UpdateCustomerVAT", 1)
		mockOrbClient.AssertCalled(t, "UpdateCustomerVAT", ctx, orbCustomerID,
			mock.MatchedBy(func(vat *orb.VAT) bool {
				return vat.Country == "US" && vat.Type == "EIN" && vat.Value == "*********"
			}), mock.Anything)

		// Assert that we removed the old tax ID, now we only have the newer tax ID
		customer, err := mockStripeClient.GetCustomer(stripeCustomerID)
		require.NoError(t, err)
		require.Len(t, customer.TaxIDs.Data, 1)
		require.Equal(t, "*********", customer.TaxIDs.Data[0].Value)
	})

	t.Run("Tax ID deleted, none left", func(t *testing.T) {
		mockOrbClient = orb.NewMockOrbClient()
		processor.orbClient = mockOrbClient

		// Create a test user
		userID := uuid.New().String()
		stripeCustomerID := "cus_" + uuid.New().String()
		orbCustomerID := "orb_" + uuid.New().String()

		user := &auth_entities.User{
			Id:               userID,
			Email:            "<EMAIL>",
			StripeCustomerId: stripeCustomerID,
			OrbCustomerId:    orbCustomerID,
		}

		userDAO := daoFactory.GetUserDAO()
		_, err := userDAO.Create(ctx, user)
		require.NoError(t, err, "Failed to create test user")

		// Create a tax id deleted event
		event := &stripe_event.StripeEvent{
			EventType:        "customer.tax_id.deleted",
			StripeCustomerId: proto.String(stripeCustomerID),
			Event: &stripe_event.StripeEvent_CustomerEvent{
				CustomerEvent: &stripe_event.CustomerEvent{},
			},
		}

		mockStripeClient.AddCustomer(user.Email, stripeCustomerID, userID)
		mockStripeClient.SetCustomerTaxIDs(stripeCustomerID, []*stripe.TaxID{})

		// Configure mock orb client
		mockOrbClient.On("UpdateCustomerVAT", mock.Anything, orbCustomerID, mock.Anything, mock.Anything).Return(nil)

		// Process the event directly
		err = processor.processStripeEvent(ctx, event)
		require.NoError(t, err)

		// Assert we set VAT to nil
		mockOrbClient.AssertNumberOfCalls(t, "UpdateCustomerVAT", 1)
		mockOrbClient.AssertCalled(t, "UpdateCustomerVAT", ctx, orbCustomerID,
			mock.MatchedBy(func(vat *orb.VAT) bool {
				return vat == nil
			}), mock.Anything)
	})

	t.Run("Missing preconditions or IDs", func(t *testing.T) {
		mockOrbClient = orb.NewMockOrbClient()
		processor.orbClient = mockOrbClient

		// Event stripe customer ID is nil
		event := &stripe_event.StripeEvent{
			EventType:        "customer.tax_id.created",
			StripeCustomerId: nil,
			Event: &stripe_event.StripeEvent_CustomerEvent{
				CustomerEvent: &stripe_event.CustomerEvent{},
			},
		}

		// Process the event directly
		err := processor.processStripeEvent(ctx, event)
		require.Error(t, err)

		// Failed to get customer
		event.StripeCustomerId = proto.String("cus_" + uuid.New().String())
		err = processor.processStripeEvent(ctx, event)
		require.Error(t, err)

		// No user found in our database
		stripeCustomerID := "cus_" + uuid.New().String()
		event.StripeCustomerId = proto.String(stripeCustomerID)
		mockStripeClient.AddCustomer("<EMAIL>", stripeCustomerID, "")
		err = processor.processStripeEvent(ctx, event)
		require.Error(t, err)

		// No Orb Customer ID
		userID := uuid.New().String()
		stripeCustomerID = "cus_" + uuid.New().String()
		event.StripeCustomerId = proto.String(stripeCustomerID)
		mockStripeClient.AddCustomer("<EMAIL>", stripeCustomerID, userID)
		user := &auth_entities.User{
			Id:               userID,
			Email:            "<EMAIL>",
			StripeCustomerId: stripeCustomerID,
		}
		userDAO := daoFactory.GetUserDAO()
		_, err = userDAO.Create(ctx, user)
		require.NoError(t, err, "Failed to create test user")
		err = processor.processStripeEvent(ctx, event)
		require.Error(t, err)
	})

	t.Run("Checkout Session Failed Event", func(t *testing.T) {
		// Create a test user
		userID := uuid.New().String()
		pendingChangeId := uuid.New().String()
		// Create a checkout session completed event with unpaid status
		checkoutSessionID := "cs_" + uuid.New().String()
		user := &auth_entities.User{
			Id:      userID,
			Email:   "<EMAIL>",
			Tenants: []string{"test-tenant-id"},
			PendingChange: &auth_entities.PendingChange{
				Id: pendingChangeId,
				Change: &auth_entities.PendingChange_PlanChange{
					PlanChange: &auth_entities.PlanChange{
						TargetPlanId:      "pro",
						Status:            auth_entities.PlanChange_PENDING,
						CheckoutSessionId: &checkoutSessionID,
					},
				},
				CreatedAt: timestamppb.Now(),
			},
		}
		userDAO := daoFactory.GetUserDAO()
		_, err := userDAO.Create(ctx, user)
		require.NoError(t, err, "Failed to create test user")
		// Create tenant subscription mapping
		tenantSubscriptionMappingDAO := daoFactory.GetTenantSubscriptionMappingDAO()
		tenantMapping := &auth_entities.TenantSubscriptionMapping{
			TenantId: "test-tenant-id",
		}
		_, err = tenantSubscriptionMappingDAO.Create(ctx, tenantMapping)
		require.NoError(t, err, "Failed to create tenant subscription mapping")
		// Create user tenant mapping
		userTenantMappingDAO := daoFactory.GetUserTenantMappingDAO("test-tenant")
		userTenantMapping := &auth_entities.UserTenantMapping{
			UserId: userID,
			Tenant: "test-tenant",
		}
		_, err = userTenantMappingDAO.Create(ctx, userTenantMapping)
		require.NoError(t, err, "Failed to create user tenant mapping")
		// Create a checkout session completed event with unpaid status
		event := &stripe_event.StripeEvent{
			EventType: "checkout.session.completed",
			Event: &stripe_event.StripeEvent_CheckoutEvent{
				CheckoutEvent: &stripe_event.CheckoutEvent{
					CheckoutSessionId: checkoutSessionID,
					PaymentStatus:     proto.String("unpaid"),
					AmountPaidCents:   proto.Int64(2999),
					Metadata: map[string]string{
						"event_type":      "plan_change",
						"augment_user_id": userID,
					},
				},
			},
		}
		// Process the event directly
		err = processor.processStripeEvent(ctx, event)
		require.NoError(t, err, "Failed to process checkout session failed event")
		// Verify the pending change was marked as failed
		updatedUser, err := userDAO.Get(ctx, userID)
		require.NoError(t, err, "Failed to get updated user")
		require.NotNil(t, updatedUser.PendingChange, "User should have a pending change")
		planChange := updatedUser.PendingChange.GetPlanChange()
		require.NotNil(t, planChange, "Plan change should not be nil")
		assert.Equal(t, auth_entities.PlanChange_PAYMENT_FAILED, planChange.Status, "Plan change should be marked as payment failed")
	})
	t.Run("Checkout Session ID Mismatch", func(t *testing.T) {
		// Create a test user with a pending change that has a different checkout session ID
		userID := uuid.New().String()
		pendingChangeId := uuid.New().String()
		storedCheckoutSessionID := "cs_stored_" + uuid.New().String()
		eventCheckoutSessionID := "cs_event_" + uuid.New().String()

		user := &auth_entities.User{
			Id:      userID,
			Email:   "<EMAIL>",
			Tenants: []string{"test-tenant-id"},
			PendingChange: &auth_entities.PendingChange{
				Id: pendingChangeId,
				Change: &auth_entities.PendingChange_PlanChange{
					PlanChange: &auth_entities.PlanChange{
						TargetPlanId:      "pro",
						Status:            auth_entities.PlanChange_PENDING,
						CheckoutSessionId: &storedCheckoutSessionID, // Different from event
					},
				},
				CreatedAt: timestamppb.Now(),
			},
		}
		userDAO := daoFactory.GetUserDAO()
		_, err := userDAO.Create(ctx, user)
		require.NoError(t, err, "Failed to create test user")

		// Create tenant subscription mapping
		tenantSubscriptionMappingDAO := daoFactory.GetTenantSubscriptionMappingDAO()
		tenantMapping := &auth_entities.TenantSubscriptionMapping{
			TenantId: "test-tenant-id",
		}
		_, err = tenantSubscriptionMappingDAO.Create(ctx, tenantMapping)
		require.NoError(t, err, "Failed to create tenant subscription mapping")

		// Create user tenant mapping
		userTenantMappingDAO := daoFactory.GetUserTenantMappingDAO("test-tenant")
		userTenantMapping := &auth_entities.UserTenantMapping{
			UserId: userID,
			Tenant: "test-tenant",
		}
		_, err = userTenantMappingDAO.Create(ctx, userTenantMapping)
		require.NoError(t, err, "Failed to create user tenant mapping")

		// Create a checkout session event with a different checkout session ID
		event := &stripe_event.StripeEvent{
			EventType: "checkout.session.completed",
			Event: &stripe_event.StripeEvent_CheckoutEvent{
				CheckoutEvent: &stripe_event.CheckoutEvent{
					CheckoutSessionId: eventCheckoutSessionID, // Different from stored
					PaymentStatus:     proto.String("paid"),
					AmountPaidCents:   proto.Int64(2999),
					Metadata: map[string]string{
						"event_type":      "plan_change",
						"augment_user_id": userID,
					},
				},
			},
		}

		// Process the event directly - should fail with checkout session ID mismatch
		err = processor.processStripeEvent(ctx, event)
		require.Error(t, err, "Should error when checkout session IDs don't match")
		assert.Contains(t, err.Error(), "checkout session ID mismatch", "Error should mention checkout session ID mismatch")
	})

	t.Run("Checkout Session with Missing User ID", func(t *testing.T) {
		// Create a checkout session event with no user ID in metadata
		checkoutSessionID := "cs_" + uuid.New().String()
		event := &stripe_event.StripeEvent{
			EventType: "checkout.session.completed",
			Event: &stripe_event.StripeEvent_CheckoutEvent{
				CheckoutEvent: &stripe_event.CheckoutEvent{
					CheckoutSessionId: checkoutSessionID,
					PaymentStatus:     proto.String("paid"),
					Metadata: map[string]string{
						"event_type": "plan_change",
						// No augment_user_id
					},
				},
			},
		}
		// Process the event directly
		err := processor.processStripeEvent(ctx, event)
		require.Error(t, err, "Should error when processing checkout session event with missing user ID")
	})
}

func TestFindUserForStripeCustomerId(t *testing.T) {
	// Setup necessary test components
	bigtableFixture := NewBigtableFixture(t)
	defer bigtableFixture.Cleanup()
	ctx := context.Background()
	daoFactory := NewDAOFactory(bigtableFixture.Table)
	mockStripeClient := stripelib.NewMockStripeClient()

	// Create a processor
	processor := &StripeEventProcessor{
		config:            &Config{},
		daoFactory:        daoFactory,
		stripeClient:      mockStripeClient,
		featureFlagHandle: featureflags.NewLocalFeatureFlagHandler(),
	}
	t.Run("Found user by augment user ID", func(t *testing.T) {
		// Create a user with metadata
		userID := uuid.New().String()
		stripeCustomerID := "cus_" + uuid.New().String()
		user := &auth_entities.User{
			Id:               userID,
			Email:            "<EMAIL>",
			StripeCustomerId: stripeCustomerID,
		}
		userDAO := daoFactory.GetUserDAO()
		_, err := userDAO.Create(ctx, user)
		require.NoError(t, err, "Failed to create test user")

		// Add the user to the mock Stripe client
		mockStripeClient.AddCustomer(user.Email, stripeCustomerID, userID)

		// Find the user
		foundUser, err := processor.findUserForStripeCustomerId(ctx, stripeCustomerID)
		require.NoError(t, err)
		assert.Equal(t, userID, foundUser.Id, "Found user should match the created user")
	})

	t.Run("Found user by stripe customer ID", func(t *testing.T) {
		// Create a user without metadata
		userID := uuid.New().String()
		stripeCustomerID := "cus_" + uuid.New().String()
		user := &auth_entities.User{
			Id:               userID,
			Email:            "<EMAIL>",
			StripeCustomerId: stripeCustomerID,
		}
		userDAO := daoFactory.GetUserDAO()
		_, err := userDAO.Create(ctx, user)
		require.NoError(t, err, "Failed to create test user")

		// Add the user to the mock Stripe client
		mockStripeClient.AddCustomer(user.Email, stripeCustomerID, "") // do not set augment ID for metadata

		// Find the user
		foundUser, err := processor.findUserForStripeCustomerId(ctx, stripeCustomerID)
		require.NoError(t, err)
		assert.Equal(t, userID, foundUser.Id, "Found user should match the created user")
	})

	t.Run("User not found", func(t *testing.T) {
		// Find the user
		foundUser, err := processor.findUserForStripeCustomerId(ctx, "nonexistent_customer_id")
		require.Error(t, err)
		assert.Nil(t, foundUser, "Should not find a user")
	})
}
