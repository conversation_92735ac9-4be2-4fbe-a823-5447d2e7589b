package main

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/stripe/stripe-go/v80"
	"google.golang.org/protobuf/types/known/timestamppb"

	featureflag "github.com/augmentcode/augment/base/feature_flags"
	"github.com/augmentcode/augment/base/logging/audit"
	auditocsf "github.com/augmentcode/augment/base/logging/audit_ocsf"
	auth_entities "github.com/augmentcode/augment/services/auth/central/server/auth_entities_proto"
	front_end_token_service "github.com/augmentcode/augment/services/auth/central/server/front_end_token_service_proto"
	stripelib "github.com/augmentcode/augment/services/billing/lib/stripe"
	ripublisher "github.com/augmentcode/augment/services/request_insight/publisher"
	tw_client "github.com/augmentcode/augment/services/tenant_watcher/client"
	tw_pb "github.com/augmentcode/augment/services/tenant_watcher/proto"
)

const (
	testTenantID        = "individual-2"
	testTenantName      = "test-tenant1"
	testTenantNamespace = "individual"
)

// ErrorMockStripeClient is a mock that returns errors for testing error handling
type ErrorMockStripeClient struct {
	err error
}

// ListSubscriptions returns the configured error
func (m *ErrorMockStripeClient) ListSubscriptions(customerID string, includeCanceled ...bool) ([]*stripe.Subscription, error) {
	return nil, m.err
}

// CancelSubscription is a stub implementation
func (m *ErrorMockStripeClient) CancelSubscription(subscriptionID string) error {
	return m.err
}

// CreateSubscription is a stub implementation
func (m *ErrorMockStripeClient) CreateSubscription(customerID, userID, priceID string, trialDays *int64) error {
	return m.err
}

// FindCustomerByEmail is a stub implementation
func (m *ErrorMockStripeClient) FindCustomerByEmail(email string) (*stripe.Customer, error) {
	return nil, m.err
}

// CreateCustomer is a stub implementation
func (m *ErrorMockStripeClient) CreateCustomer(params *stripe.CustomerParams) (*stripe.Customer, error) {
	return nil, m.err
}

// UpdateSubscriptionSeats is a stub implementation
func (m *ErrorMockStripeClient) UpdateSubscriptionSeats(subscriptionID string, seats int) error {
	return m.err
}

// CreateSetupIntent is a stub implementation
func (m *ErrorMockStripeClient) CreateSetupIntent(customerID string) (*stripe.SetupIntent, error) {
	return nil, m.err
}

// GetSubscription is a stub implementation
func (m *ErrorMockStripeClient) GetSubscription(subscriptionID string) (*stripe.Subscription, error) {
	return nil, m.err
}

type FrontEndTokenServiceSUT struct {
	daoFactory           *DAOFactory
	tenantMap            *TenantMap
	frontEndTokenService *FrontEndTokenServiceGrpcServer
}

func createFrontEndTokenServiceSUT(t *testing.T, bigtableFixture *BigtableFixture) *FrontEndTokenServiceSUT {
	ret := &FrontEndTokenServiceSUT{}

	ret.daoFactory = NewDAOFactory(bigtableFixture.Table)

	// Create a mock tenant map
	mockClient := &tw_client.MockTenantWatcherClient{
		Tenants: []*tw_pb.Tenant{
			{
				Id:             testTenantID,
				Name:           testTenantName,
				ShardNamespace: testTenantNamespace,
				Cloud:          "test-cloud",
				AuthConfiguration: &tw_pb.AuthConfiguration{
					Domain: "test-tenant1.com",
				},
			},
		},
	}

	ret.tenantMap = NewTenantMap(
		ret.daoFactory,
		mockClient,
		"us-central.api.augmentcode.com",
		featureflag.NewLocalFeatureFlagHandler(),
		NewMockAsyncOpsPublisher(),
		audit.NewDefaultAuditLogger(),
	)

	// Create a mock Stripe client
	mockStripeClient := stripelib.NewMockStripeClient()
	mockOcsfAuditLogger, _ := auditocsf.NewMockOCSFAuditLogger()

	// Create the front end token service
	ret.frontEndTokenService = NewFrontEndTokenServiceGrpcServer(
		featureflag.NewLocalFeatureFlagHandler(),
		ret.daoFactory,
		ret.tenantMap,
		nil, // tokenExchangeClient not needed for this test
		&audit.AuditLogger{},
		ripublisher.NewRequestInsightPublisherMock(),
		&Config{CodeTTLSeconds: 600},
		nil,              // No shutdown channel needed for this test
		nil,              // No need for a real signup limiter in this test
		nil,              // No need for a real team management server in this test
		mockStripeClient, // Use the mock Stripe client
		nil,              // No need for a real runUserSecurityChecks in this test
		mockOcsfAuditLogger,
	)

	return ret
}

func caseGetTokenInfo(t *testing.T, bigtableFixture *BigtableFixture) {
	sut := createFrontEndTokenServiceSUT(t, bigtableFixture)

	ctx := createAuthorizedRequestContext()
	tokenHashDAO := sut.daoFactory.GetTokenHashDAO()

	// Test case 1: Token doesn't exist
	request := &front_end_token_service.GetTokenInfoRequest{
		Token: "nonexistent-token",
	}
	response, err := sut.frontEndTokenService.GetTokenInfo(ctx, request)
	require.NoError(t, err)
	assert.Empty(t, response.AugmentUserId)
	assert.Empty(t, response.TenantId)

	// Test case 2: Valid token
	testToken := "test-token"
	testTokenHash := hashToken(testToken)
	testUserId := "test-user-id"

	// Create a token in the database
	tokenRecord := &auth_entities.TokenHash{
		Hash:                  testTokenHash,
		TenantId:              testTenantID,
		AugmentUserId:         testUserId,
		EmailAddress:          "<EMAIL>",
		CreationTime:          timestamppb.Now(),
		ExpirationTimeSeconds: 0, // Never expires
	}

	_, err = tokenHashDAO.Create(ctx, tokenRecord)
	require.NoError(t, err)

	// Test retrieving the token info
	request = &front_end_token_service.GetTokenInfoRequest{
		Token: testToken,
	}
	response, err = sut.frontEndTokenService.GetTokenInfo(ctx, request)
	require.NoError(t, err)
	assert.Equal(t, testUserId, response.AugmentUserId)
	assert.Equal(t, testTenantID, response.TenantId)

	// Test case 3: Expired token
	expiredToken := "expired-token"
	expiredTokenHash := hashToken(expiredToken)

	// Create an expired token in the database
	expiredTokenRecord := &auth_entities.TokenHash{
		Hash:                  expiredTokenHash,
		TenantId:              testTenantID,
		AugmentUserId:         testUserId,
		EmailAddress:          "<EMAIL>",
		CreationTime:          timestamppb.New(time.Now().Add(-time.Hour)), // Created 1 hour ago
		ExpirationTimeSeconds: 1800,                                        // Expires after 30 minutes
	}

	_, err = tokenHashDAO.Create(ctx, expiredTokenRecord)
	require.NoError(t, err)

	// Test retrieving the expired token info
	request = &front_end_token_service.GetTokenInfoRequest{
		Token: expiredToken,
	}
	response, err = sut.frontEndTokenService.GetTokenInfo(ctx, request)
	require.NoError(t, err)
	assert.Empty(t, response.AugmentUserId)
	assert.Empty(t, response.TenantId)
}

func caseIsUserAdmin(t *testing.T, bigtableFixture *BigtableFixture) {
	sut := createFrontEndTokenServiceSUT(t, bigtableFixture)

	userDAO := sut.daoFactory.GetUserDAO()
	ctx := createAuthorizedRequestContext()

	testUserID := "test-admin-user"
	testUser := &auth_entities.User{
		Id:    testUserID,
		Email: "<EMAIL>",
		Tenants: []string{
			testTenantID,
		},
	}

	_, err := userDAO.Create(ctx, testUser)
	require.NoError(t, err)

	// Create a tenant mapping with admin role
	tenantMappingDAO := sut.daoFactory.GetUserTenantMappingDAO(testTenantName)
	adminMapping := &auth_entities.UserTenantMapping{
		UserId: testUserID,
		CustomerUiRoles: []auth_entities.CustomerUiRole{
			auth_entities.CustomerUiRole_ADMIN,
		},
	}

	_, err = tenantMappingDAO.Create(ctx, adminMapping)
	require.NoError(t, err)

	// Test case 1: User is an admin
	adminRequest := &front_end_token_service.IsUserAdminRequest{
		UserId: testUserID,
	}
	adminResponse, err := sut.frontEndTokenService.IsUserAdmin(ctx, adminRequest)
	require.NoError(t, err)
	assert.True(t, adminResponse.IsAdmin)

	nonAdminUserID := "test-non-admin-user"
	nonAdminUser := &auth_entities.User{
		Id:    nonAdminUserID,
		Email: "<EMAIL>",
		Tenants: []string{
			testTenantID,
		},
	}

	_, err = userDAO.Create(ctx, nonAdminUser)
	require.NoError(t, err)

	nonAdminMapping := &auth_entities.UserTenantMapping{
		UserId: nonAdminUserID,
		CustomerUiRoles: []auth_entities.CustomerUiRole{
			auth_entities.CustomerUiRole_UNKNOWN_CUSTOMER_UI_ROLE,
		},
	}

	_, err = tenantMappingDAO.Create(ctx, nonAdminMapping)
	require.NoError(t, err)

	// Test case 2: User is not an admin
	nonAdminRequest := &front_end_token_service.IsUserAdminRequest{
		UserId: nonAdminUserID,
	}
	nonAdminResponse, err := sut.frontEndTokenService.IsUserAdmin(ctx, nonAdminRequest)
	require.NoError(t, err)
	assert.False(t, nonAdminResponse.IsAdmin)

	// Test case 3: User doesn't exist
	nonExistentRequest := &front_end_token_service.IsUserAdminRequest{
		UserId: "non-existent-user",
	}
	nonExistentResponse, err := sut.frontEndTokenService.IsUserAdmin(ctx, nonExistentRequest)
	require.NoError(t, err)
	assert.False(t, nonExistentResponse.IsAdmin)
}

func caseGetUser(t *testing.T, bigtableFixture *BigtableFixture) {
	sut := createFrontEndTokenServiceSUT(t, bigtableFixture)

	idpUserMappingDAO := sut.daoFactory.GetIDPUserMappingDAO()
	ctx := createAuthorizedRequestContext()

	t.Run("idp by user ID points to invalid user", func(t *testing.T) {
		idpUserID := "idp_123456"
		invalidAugmentUserID := "augment_123456"

		// Create an IDP user mapping that points to an invalid user
		mapping := &auth_entities.IdpUserMapping{
			IdpUserId:     idpUserID,
			AugmentUserId: invalidAugmentUserID,
		}

		_, err := idpUserMappingDAO.Create(bigtableFixture.Ctx, mapping)
		require.NoError(t, err)

		// Create a request to get the user
		request := &front_end_token_service.GetUserRequest{
			IdpUserId: idpUserID,
		}

		// Get the user
		response, err := sut.frontEndTokenService.GetUser(ctx, request)
		require.NoError(t, err)
		assert.Nil(t, response.User)
	})
}

// Note there are higher level test cases in app_test.py for the following cases:
// - successful token exchange
// - invalid code
// - invalid code verifier
// - missing required parameters
// - code reuse
// - unsupported grant type
// we judge that additional tests here for those cases are not worth the maintenance burden.
func caseTokenFromCode(t *testing.T, bigtableFixture *BigtableFixture) {
	sut := createFrontEndTokenServiceSUT(t, bigtableFixture)

	ctx := createAuthorizedRequestContext()
	codeDAO := sut.daoFactory.GetCodeDAO()
	tokenHashDAO := sut.daoFactory.GetTokenHashDAO()

	testCode := "test-code-123"
	testClientID := "test-client"
	testRedirectURI := "https://example.com/callback"
	testUserID := "test-user-id"
	testEmail := "<EMAIL>"
	testIdpUserID := "idp-user-123"

	t.Run("unsupported grant type", func(t *testing.T) {
		request := &front_end_token_service.TokenFromCodeRequest{
			Code:         testCode,
			ClientId:     testClientID,
			RedirectUri:  testRedirectURI,
			GrantType:    "invalid_grant_type",
			CodeVerifier: "",
		}

		response, err := sut.frontEndTokenService.TokenFromCode(ctx, request)
		require.NoError(t, err)
		assert.Equal(t, "unsupported_grant_type", response.Error)
		assert.Empty(t, response.AccessToken)
	})

	t.Run("client ID mismatch", func(t *testing.T) {
		// Create a valid code
		codeRecord := &auth_entities.Code{
			Code:                testCode,
			Email:               testEmail,
			IdpUserId:           testIdpUserID,
			AugmentUserId:       testUserID,
			ClientId:            testClientID,
			TenantId:            testTenantID,
			RedirectUri:         testRedirectURI,
			CodeChallenge:       "",
			IsUsed:              false,
			CreationTimeSeconds: time.Now().Unix(),
		}

		_, err := codeDAO.Create(ctx, codeRecord)
		require.NoError(t, err)

		request := &front_end_token_service.TokenFromCodeRequest{
			Code:         testCode,
			ClientId:     "wrong-client-id",
			RedirectUri:  testRedirectURI,
			GrantType:    "authorization_code",
			CodeVerifier: "",
		}

		response, err := sut.frontEndTokenService.TokenFromCode(ctx, request)
		require.NoError(t, err)
		assert.Equal(t, "invalid_grant", response.Error)
		assert.Empty(t, response.AccessToken)
	})

	t.Run("redirect URI mismatch", func(t *testing.T) {
		// Clear the table and create a fresh code
		bigtableFixture.ClearTable(t)

		codeRecord := &auth_entities.Code{
			Code:                testCode + "-redirect",
			Email:               testEmail,
			IdpUserId:           testIdpUserID,
			AugmentUserId:       testUserID,
			ClientId:            testClientID,
			TenantId:            testTenantID,
			RedirectUri:         testRedirectURI,
			CodeChallenge:       "",
			IsUsed:              false,
			CreationTimeSeconds: time.Now().Unix(),
		}

		_, err := codeDAO.Create(ctx, codeRecord)
		require.NoError(t, err)

		request := &front_end_token_service.TokenFromCodeRequest{
			Code:         testCode + "-redirect",
			ClientId:     testClientID,
			RedirectUri:  "https://wrong.com/callback",
			GrantType:    "authorization_code",
			CodeVerifier: "",
		}

		response, err := sut.frontEndTokenService.TokenFromCode(ctx, request)
		require.NoError(t, err)
		assert.Equal(t, "invalid_grant", response.Error)
		assert.Empty(t, response.AccessToken)
	})

	t.Run("expired code", func(t *testing.T) {
		// Clear the table and create a fresh code
		bigtableFixture.ClearTable(t)

		// Create an expired code (created 1 hour ago with 10 minute TTL)
		expiredCreationTime := time.Now().Unix() - 3600 // 1 hour ago

		codeRecord := &auth_entities.Code{
			Code:                testCode + "-expired",
			Email:               testEmail,
			IdpUserId:           testIdpUserID,
			AugmentUserId:       testUserID,
			ClientId:            testClientID,
			TenantId:            testTenantID,
			RedirectUri:         testRedirectURI,
			CodeChallenge:       "",
			IsUsed:              false,
			CreationTimeSeconds: expiredCreationTime,
		}

		_, err := codeDAO.Create(ctx, codeRecord)
		require.NoError(t, err)

		request := &front_end_token_service.TokenFromCodeRequest{
			Code:         testCode + "-expired",
			ClientId:     testClientID,
			RedirectUri:  testRedirectURI,
			GrantType:    "authorization_code",
			CodeVerifier: "",
		}

		response, err := sut.frontEndTokenService.TokenFromCode(ctx, request)
		require.NoError(t, err)
		assert.Equal(t, "invalid_grant", response.Error)
		assert.Empty(t, response.AccessToken)
	})

	t.Run("successful token exchange with expiration", func(t *testing.T) {
		// Clear the table and create a fresh code
		bigtableFixture.ClearTable(t)

		codeRecord := &auth_entities.Code{
			Code:                testCode + "-expiring",
			Email:               testEmail,
			IdpUserId:           testIdpUserID,
			AugmentUserId:       testUserID,
			ClientId:            testClientID,
			TenantId:            testTenantID,
			RedirectUri:         testRedirectURI,
			CodeChallenge:       "",
			IsUsed:              false,
			CreationTimeSeconds: time.Now().Unix(),
		}

		_, err := codeDAO.Create(ctx, codeRecord)
		require.NoError(t, err)

		// Request a token with 3600 seconds (1 hour) expiration
		expirationSeconds := int64(3600)
		request := &front_end_token_service.TokenFromCodeRequest{
			Code:                  testCode + "-expiring",
			ClientId:              testClientID,
			RedirectUri:           testRedirectURI,
			GrantType:             "authorization_code",
			CodeVerifier:          "",
			ExpirationTimeSeconds: expirationSeconds,
		}

		response, err := sut.frontEndTokenService.TokenFromCode(ctx, request)
		require.NoError(t, err)
		assert.Empty(t, response.Error)
		assert.NotEmpty(t, response.AccessToken)
		assert.Equal(t, expirationSeconds, response.ExpiresIn)

		// Verify the token was stored in the database with the correct expiration
		tokenHash := hashToken(response.AccessToken)
		storedToken, err := tokenHashDAO.Get(ctx, tokenHash)
		require.NoError(t, err)
		assert.NotNil(t, storedToken)
		assert.Equal(t, expirationSeconds, storedToken.ExpirationTimeSeconds)
		assert.Equal(t, testTenantID, storedToken.TenantId)
		assert.Equal(t, testUserID, storedToken.AugmentUserId)
	})
}

func TestFrontEndTokenService(t *testing.T) {
	// Create a single fixture for all tests
	bigtableFixture := NewBigtableFixture(t)
	defer bigtableFixture.Cleanup()

	testCases := []struct {
		name     string
		testFunc func(t *testing.T, bigtableFixture *BigtableFixture)
	}{
		{
			name:     "get token info",
			testFunc: caseGetTokenInfo,
		},
		{
			name:     "is user admin",
			testFunc: caseIsUserAdmin,
		},
		{
			name:     "get user",
			testFunc: caseGetUser,
		},
		{
			name:     "token from code",
			testFunc: caseTokenFromCode,
		},
	}

	for _, tc := range testCases {
		bigtableFixture.ClearTable(t)

		t.Run(tc.name, func(t *testing.T) {
			tc.testFunc(t, bigtableFixture)
		})
	}
}
