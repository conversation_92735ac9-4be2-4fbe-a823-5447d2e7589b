package main

import (
	"context"
	"crypto/rand"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"os"
	"regexp"
	"slices"
	"sort"
	"strings"
	"sync"
	"time"

	featureflags "github.com/augmentcode/augment/base/feature_flags"
	"github.com/augmentcode/augment/base/logging/audit"
	auditocsf "github.com/augmentcode/augment/base/logging/audit_ocsf"
	orb_event "github.com/augmentcode/augment/services/auth/billing_webhook/orb_event/proto"
	auth_entities "github.com/augmentcode/augment/services/auth/central/server/auth_entities_proto"
	front_end_token_service_pb "github.com/augmentcode/augment/services/auth/central/server/front_end_token_service_proto"
	authpb "github.com/augmentcode/augment/services/auth/central/server/proto"
	"github.com/augmentcode/augment/services/billing/lib/orb"
	orb_config "github.com/augmentcode/augment/services/billing/lib/orb/config"
	stripelib "github.com/augmentcode/augment/services/billing/lib/stripe"
	"github.com/augmentcode/augment/services/lib/grpc/auth"
	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
	riproto "github.com/augmentcode/augment/services/request_insight/proto"
	ripublisher "github.com/augmentcode/augment/services/request_insight/publisher"
	tenantwatcherclient "github.com/augmentcode/augment/services/tenant_watcher/client"
	tw_pb "github.com/augmentcode/augment/services/tenant_watcher/proto"
	tenantutil "github.com/augmentcode/augment/services/tenant_watcher/util"
	tokenscopesproto "github.com/augmentcode/augment/services/token_exchange/token_scopes_proto"
	"github.com/google/uuid"
	"github.com/prometheus/client_golang/prometheus"

	"github.com/rs/zerolog"
	"github.com/rs/zerolog/log"
	"github.com/stripe/stripe-go/v80"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/timestamppb"
)

var (
	// inActiveSubscriptionStatusTypes is a list of stripe subscription statuses that are considered inactive.
	inactiveSubscriptionStatusTypes = []auth_entities.Subscription_Status{
		auth_entities.Subscription_UNKNOWN,
		auth_entities.Subscription_INCOMPLETE,
		auth_entities.Subscription_INCOMPLETE_EXPIRED,
		auth_entities.Subscription_PAST_DUE,
		auth_entities.Subscription_CANCELED,
		auth_entities.Subscription_UNPAID,
		auth_entities.Subscription_PAUSED,
	}

	// SubscriptionTypeCounter tracks the types of subscriptions returned to users
	SubscriptionTypeCounter = prometheus.NewCounterVec(
		prometheus.CounterOpts{
			Name: "au_auth_subscription_type_total",
			Help: "Count of subscription types returned to users",
		},
		[]string{"tenant_tier", "subscription_type", "tenant_name"},
	)

	// Track GetUserBillingInfo calls
	GetUserBillingInfoCounter = prometheus.NewCounterVec(
		prometheus.CounterOpts{
			Name: "au_auth_get_user_billing_info_total",
			Help: "Count of GetUserBillingInfo calls",
		},
		[]string{"status"}, // OK or error response
	)
)

func init() {
	prometheus.MustRegister(SubscriptionTypeCounter)
	prometheus.MustRegister(GetUserBillingInfoCounter)
}

type AuthGrpcServer struct {
	daoFactory              *DAOFactory
	tenantMap               *TenantMap
	auditLogger             *audit.AuditLogger
	requestInsightPublisher ripublisher.RequestInsightPublisher
	asyncOpsPublisher       AsyncOpsPublisher
	asyncOpsWorker          *AsyncOpsWorker
	featureFlagHandle       featureflags.FeatureFlagHandle
	stripeConfig            *StripeConfig
	orbConfig               *orb_config.OrbConfig
	orbClient               orb.OrbClient
	stripeClient            stripelib.StripeClient
	billingEventProcessor   BillingEventProcessor
	ocsfAuditLogger         *auditocsf.OCSFAuditLogger
}

func NewAuthGrpcServer(
	featureFlagHandle featureflags.FeatureFlagHandle,
	daoFactory *DAOFactory,
	tenantMap *TenantMap,
	auditLogger *audit.AuditLogger,
	requestInsightPublisher ripublisher.RequestInsightPublisher,
	asyncOpsPublisher AsyncOpsPublisher,
	asyncOpsWorker *AsyncOpsWorker,
	stripeConfig *StripeConfig,
	orbConfig *orb_config.OrbConfig,
	stripeClient stripelib.StripeClient,
	billingEventProcessor BillingEventProcessor,
	ocsfAuditLogger *auditocsf.OCSFAuditLogger,
) *AuthGrpcServer {
	if stripeConfig != nil && stripeConfig.Enabled {
		key, err := os.ReadFile(stripeConfig.SecretKeyPath)
		if err != nil {
			log.Error().Err(err).Msg("Failed to read Stripe secret key")
		} else {
			cleanKey := strings.TrimSpace(string(key))
			stripe.Key = cleanKey
			log.Info().Msg("Stripe integration enabled for AuthGrpcServer")
		}
	}
	// Initialize Orb client if enabled
	var orbClient orb.OrbClient
	if orbConfig != nil && orbConfig.Enabled {
		// Read the Orb API key from the specified file path
		key, err := os.ReadFile(orbConfig.ApiKeyPath)
		if err != nil {
			log.Error().Err(err).Msg("Failed to read Orb secret key")
		} else {
			cleanKey := strings.TrimSpace(string(key))
			orbClient = orb.NewOrbClient(cleanKey, featureFlagHandle)
			log.Info().Msg("Orb client initialized successfully")
		}
	}

	server := &AuthGrpcServer{
		daoFactory:              daoFactory,
		tenantMap:               tenantMap,
		auditLogger:             auditLogger,
		requestInsightPublisher: requestInsightPublisher,
		asyncOpsPublisher:       asyncOpsPublisher,
		asyncOpsWorker:          asyncOpsWorker,
		featureFlagHandle:       featureFlagHandle,
		stripeConfig:            stripeConfig,
		orbConfig:               orbConfig,
		orbClient:               orbClient,
		stripeClient:            stripeClient,
		billingEventProcessor:   billingEventProcessor,
		ocsfAuditLogger:         ocsfAuditLogger,
	}

	return server
}

// This method should be used when authenticating service requests (with service tokens) to
// access/modify information about a specific tenant.
//
// Returns an error that can be returned directly to the caller if provided gRPC context does not
// contain auth claims that give access to the provided tenant id and optional scope. Some things to
// note:
//  1. The auth server interceptor is responsible for checking that the token is cryptographically
//     valid, so here we only need to concern ourselves with whether the token's content is correct.
//  2. The service token comes from token-exchnage, which checks peer certificates before granting
//     scopes. As long as a caller has a scope in its claims, we can be sure they are allowed to
//     have that scope.
func authCheck(
	ctx context.Context, tenantID string, requiredScope tokenscopesproto.Scope,
) error {
	authClaims, ok := auth.GetAugmentClaims(ctx)
	if !ok {
		return status.Error(codes.Unknown, "Invalid context")
	}

	if tenantID == "" {
		if !authClaims.AllowsAllTenants() {
			log.Ctx(ctx).Error().Msgf("Auth claims give permission for tenant, but request requires access to all tenants")
			return status.Error(codes.PermissionDenied, "Access denied")
		}
	} else if !authClaims.IsTenantAllowed(tenantID) {
		log.Ctx(ctx).Error().Msgf("Auth claims doesn't allow access to tenant %s", tenantID)
		return status.Error(codes.PermissionDenied, "Access denied")
	}

	if !authClaims.HasScope(requiredScope) {
		log.Ctx(ctx).Error().Msgf("Auth claims do not have required scope %s", requiredScope)
		return status.Error(codes.PermissionDenied, "Access denied")
	}

	return nil
}

// This method should be used when authenticating user requests (with user tokens)
// to access/modify their own information.
//
// Returns an error that can be returned directly to the caller if provided gRPC context does not
// contain auth claims that give access to the user's own information. Some things to note:
//  1. The auth server interceptor is responsible for checking that the token is cryptographically
//     valid, so here we only need to concern ourselves with whether the token's content is correct.
//  2. This function verifies that the user ID in the auth claims matches the requested user ID,
//     allowing users to access their own information regardless of tenant.
//  3. The required scope is still checked to ensure the caller has appropriate permissions for
//     the requested operation, even when accessing their own information.
func selfAuthCheck(ctx context.Context, userID string, requiredScope tokenscopesproto.Scope) error {
	authClaims, ok := auth.GetAugmentClaims(ctx)
	if !ok {
		return status.Error(codes.Unknown, "Invalid context")
	}

	if !authClaims.HasScope(requiredScope) {
		log.Ctx(ctx).Error().Msgf("Auth claims do not have scope %s", requiredScope)
		return status.Error(codes.PermissionDenied, "Access denied")
	}

	if authClaims.UserID != userID {
		log.Ctx(ctx).Error().Msgf("Auth claims user ID %s does not match requested user ID %s", authClaims.UserID, userID)
		return status.Error(codes.PermissionDenied, "Access denied: user ID in token does not match requested user")
	}

	return nil
}

// Rebuild all table indexes
func (s *AuthGrpcServer) RebuildIndexes(ctx context.Context, req *authpb.RebuildIndexesRequest) (*authpb.RebuildIndexesResponse, error) {
	err := authCheck(ctx, "", tokenscopesproto.Scope_AUTH_ADMIN)
	if err != nil {
		return nil, err
	}

	authClaims, _ := auth.GetAugmentClaims(ctx)
	requestContext, _ := requestcontext.FromGrpcContext(ctx)
	s.auditLogger.WriteAuditLog(
		authClaims,
		"RebuildIndexes request received",
		audit.NewProtoRequest(req),
		requestContext,
	)

	var wg sync.WaitGroup
	wg.Add(1)
	var rebuildErr error

	go func() {
		defer wg.Done()

		// Use background context to avoid cancellation during rebuild
		backgroundCtx := context.Background()
		if authClaims != nil {
			backgroundCtx = authClaims.NewContext(backgroundCtx)
		}

		userDAO := s.daoFactory.GetUserDAO()
		rebuildErr = userDAO.RebuildIndexes(backgroundCtx)
	}()

	wg.Wait()

	if rebuildErr != nil {
		log.Error().Err(rebuildErr).Msg("Failed to rebuild user indexes")
		return nil, status.Error(codes.Internal, "Failed to rebuild indexes")
	}

	log.Info().Msg("Successfully rebuilt user indexes")
	return &authpb.RebuildIndexesResponse{}, nil
}

// generateToken generates a random token of the specified number of bytes
func generateToken(nbytes int) string {
	b := make([]byte, nbytes)
	if _, err := rand.Read(b); err != nil {
		panic(err)
	}
	return hex.EncodeToString(b)
}

// hashToken creates a SHA256 hash of the token
func hashToken(token string) string {
	hash := sha256.Sum256([]byte(token))
	return hex.EncodeToString(hash[:])
}

// CreateTokenForUser creates a long-lived API token for a user (for trigger executions)
func (s *AuthGrpcServer) CreateTokenForUser(ctx context.Context, req *authpb.CreateTokenForUserRequest) (*authpb.CreateTokenForUserResponse, error) {
	log.Ctx(ctx).Info().
		Str("augment_user_id", req.AugmentUserId).
		Str("tenant_id", req.TenantId).
		Int64("expiration_time_seconds", req.ExpirationTimeSeconds).
		Msg("Creating long-lived API token for user")

	// Check AUTH_MINT scope permission
	authClaims, ok := auth.GetAugmentClaims(ctx)
	if !ok {
		return nil, status.Error(codes.Unknown, "Invalid context")
	}
	if !authClaims.HasScope(tokenscopesproto.Scope_AUTH_MINT) {
		log.Ctx(ctx).Error().Msg("Auth claims do not have required AUTH_MINT scope")
		return nil, status.Error(codes.PermissionDenied, "Insufficient permissions to create tokens")
	}

	// Validate input
	if req.AugmentUserId == "" {
		return nil, status.Error(codes.InvalidArgument, "augment_user_id is required")
	}
	if req.TenantId == "" {
		return nil, status.Error(codes.InvalidArgument, "tenant_id is required")
	}

	// Enforce expiration: must be > 0 and <= 90 days
	const maxExpirationSeconds int64 = 90 * 24 * 60 * 60 // 90 days
	if req.ExpirationTimeSeconds <= 0 {
		return nil, status.Error(codes.InvalidArgument, "expiration_time_seconds must be > 0 and <= 90 days")
	}
	if req.ExpirationTimeSeconds > maxExpirationSeconds {
		return nil, status.Error(codes.InvalidArgument, "expiration_time_seconds must be > 0 and <= 90 days")
	}

	// Generate a new API token
	newToken := generateToken(32) // 16 bytes = 32 hex chars
	tokenHashDAO := s.daoFactory.GetTokenHashDAO()

	// Create token record with specified expiration
	newTokenRecord := &auth_entities.TokenHash{
		Hash:                  hashToken(newToken),
		TenantId:              req.TenantId,
		IdpUserId:             "", // Not applicable for scheduled trigger tokens
		AugmentUserId:         req.AugmentUserId,
		EmailAddress:          "", // Will be filled in if needed
		CreationTime:          timestamppb.Now(),
		ExpirationTimeSeconds: req.ExpirationTimeSeconds,
	}

	if _, err := tokenHashDAO.Create(ctx, newTokenRecord); err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to create token for user")
		return nil, status.Error(codes.Internal, "failed to create token")
	}

	// Calculate expires_in for response (always positive due to validation)
	expiresIn := req.ExpirationTimeSeconds

	// Audit log the token creation
	s.auditLogger.WriteAuditLog(
		authClaims,
		fmt.Sprintf("Created API token for user %s, expires_in: %d seconds",
			req.AugmentUserId, expiresIn),
	)

	log.Ctx(ctx).Info().
		Str("augment_user_id", req.AugmentUserId).
		Str("tenant_id", req.TenantId).
		Int64("expires_in", expiresIn).
		Msg("Successfully created long-lived API token for user")

	return &authpb.CreateTokenForUserResponse{
		AccessToken: newToken,
		ExpiresIn:   expiresIn,
	}, nil
}

// Returns the tenant object for the provided tenant ID, or an error that can be returned directly
// to the caller if something goes wrong.
func (s *AuthGrpcServer) getTenant(ctx context.Context, tenantID string) (*tw_pb.Tenant, error) {
	tenant, err := s.tenantMap.GetTenantByIdDeletedOk(tenantID)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to fetch tenant information")
		return nil, status.Error(codes.Internal, "Internal error")
	}
	if tenant == nil {
		log.Ctx(ctx).Error().Msgf("Tenant ID %s not found", tenantID)
		return nil, status.Error(codes.PermissionDenied, "Access denied")
	}
	return tenant, nil
}

func (s *AuthGrpcServer) AddUserToTenant(ctx context.Context, req *authpb.AddUserToTenantRequest) (*authpb.AddUserToTenantResponse, error) {
	log.Ctx(ctx).Info().Msgf("AddUserToTenant: %s", req.Email)
	err := authCheck(ctx, req.TenantId, tokenscopesproto.Scope_AUTH_RW)
	if err != nil {
		// Log OCSF event for authorization failure
		if authInfo, ok := auth.GetAugmentClaims(ctx); ok {
			var userEmail string
			if email, ok := authInfo.GetIapEmail(); ok {
				userEmail = email
			} else if authInfo.UserEmail != "" {
				userEmail = authInfo.UserEmail
			} else {
				userEmail = "unknown"
			}

			if err := s.ocsfAuditLogger.LogAPIActivityWithTenant(
				auditocsf.NewAPIActivityEventBuilder(auditocsf.ActivityAPICreate).
					WithAPI(auditocsf.API{
						Operation: "AddUserToTenant",
						Service: auditocsf.APIService{
							Name: "AuthService",
						},
					}).
					WithActor(auditocsf.Actor{
						User: &auditocsf.User{
							UID:    userEmail,
							TypeID: 1, // User
						},
					}).
					WithStatus(auditocsf.StatusFailure).
					WithMessage(fmt.Sprintf("Authorization failed - insufficient permissions for AddUserToTenant")).
					Build(),
				req.TenantId,
			); err != nil {
				log.Ctx(ctx).Warn().Err(err).Msg("Failed to log OCSF audit event for authorization failure")
			}
		}
		return nil, err
	}

	// Check the tenant exists.
	// TODO(jacqueline): With a valid service token I'm not sure this is necessary, but there are
	// tests that require it so I'm leaving it for now.
	tenant, err := s.getTenant(ctx, req.TenantId)
	if err != nil {
		return nil, err
	}
	if tenant.DeletedAt != "" {
		return nil, status.Error(codes.PermissionDenied, "Tenant deleted")
	}

	if req.Email == "" {
		return nil, status.Error(codes.InvalidArgument, "Invalid request")
	}

	user, err := s.tenantMap.GetUserByEmailAddress(ctx, req.Email)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to get user")
		return nil, status.Error(codes.Internal, "Failed to fetch user")
	}

	authInfo, _ := auth.GetAugmentClaims(ctx)
	requestContext, _ := requestcontext.FromGrpcContext(ctx)
	extras := []audit.AuditLogExtra{
		audit.NewTenantName(tenant.Name),
		audit.NewTenantID(req.TenantId),
		requestContext,
		audit.NewProtoRequest(req),
	}
	if user != nil {
		extras = append(extras, audit.NewUser(user.Id))
	}
	s.auditLogger.WriteAuditLog(
		authInfo,
		fmt.Sprintf("Add user '%s' to tenant %s", req.Email, tenant.Name),
		extras...,
	)

	// Ensure the user is in the tenant
	// Only allow adding the user if they are not currently in any other tenant
	user, err = s.tenantMap.EnsureUserInTenant(ctx, user, req.Email, req.TenantId, "", front_end_token_service_pb.TenantEnsureMode_TENANT_ENSURE_MODE_ADD_IF_EMPTY)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to ensure user in tenant")
		return nil, status.Error(codes.Internal, "Failed to ensure user in tenant")
	}

	// Publish Request Insight AddUserToTenant event.
	tenantEvent := ripublisher.NewTenantEvent()
	tenantEvent.Event = &riproto.TenantEvent_AddUserToTenant{
		AddUserToTenant: &riproto.AddUserToTenant{
			User: user,
		},
	}
	riErr := s.requestInsightPublisher.PublishTenantEvent(ctx, &riproto.TenantInfo{
		TenantId:   req.TenantId,
		TenantName: authInfo.TenantName,
	}, tenantEvent)
	if riErr != nil {
		log.Ctx(ctx).Warn().Err(riErr).Msg("Failed to publish AddUserToTenant event")
	}
	s.ocsfAuditLogger.LogAPIActivityWithTenant(
		auditocsf.NewAPIActivityEventBuilder(auditocsf.ActivityAPIUpdate).
			WithAPI(auditocsf.API{
				Operation: "AddUserToTenant",
				Service: auditocsf.APIService{
					Name: "AuthService",
				},
			}).
			WithActor(auditocsf.Actor{
				User: &auditocsf.User{
					UID: authInfo.OpaqueUserID,
				},
			}).
			WithStatus(auditocsf.StatusSuccess).
			WithMessage(fmt.Sprintf("Add user '%s' to tenant %s", req.Email, tenant.Name)).
			Build(),
		req.TenantId,
	)

	return &authpb.AddUserToTenantResponse{
		User: user,
	}, nil
}

func (s *AuthGrpcServer) UpdateUserOnTenant(ctx context.Context, req *authpb.UpdateUserOnTenantRequest) (*authpb.UpdateUserOnTenantResponse, error) {
	err := authCheck(ctx, req.TenantId, tokenscopesproto.Scope_AUTH_RW)
	if err != nil {
		return nil, err
	}

	tenant, err := s.getTenant(ctx, req.TenantId)
	if err != nil {
		return nil, err
	}
	if tenant.DeletedAt != "" {
		return nil, status.Error(codes.PermissionDenied, "Tenant Deleted")
	}

	authInfo, _ := auth.GetAugmentClaims(ctx)
	requestContext, _ := requestcontext.FromGrpcContext(ctx)
	s.auditLogger.WriteAuditLog(
		authInfo,
		fmt.Sprintf("Update user '%s' in tenant %s", req.UserId, tenant.Name),
		audit.NewTenantName(tenant.Name),
		audit.NewTenantID(req.TenantId),
		audit.NewUser(req.UserId),
		requestContext,
		audit.NewProtoRequest(req),
	)

	tenantMappingDAO := s.daoFactory.GetUserTenantMappingDAO(tenant.Name)
	mapping, err := tenantMappingDAO.GetByUser(ctx, req.UserId)
	if err != nil {
		return nil, status.Error(codes.Internal, "Failed to check existing mapping")
	}

	if mapping == nil {
		return nil, status.Error(codes.NotFound, "Resource not found")
	}
	mapping.CustomerUiRoles = req.CustomerUiRoles
	_, err = tenantMappingDAO.Update(ctx, mapping)
	if err != nil {
		return nil, status.Error(codes.Internal, "Failed to update mapping")
	}

	s.ocsfAuditLogger.LogAPIActivityWithTenant(
		auditocsf.NewAPIActivityEventBuilder(auditocsf.ActivityAPIUpdate).
			WithAPI(auditocsf.API{
				Operation: "UpdateUserOnTenant",
				Service: auditocsf.APIService{
					Name: "AuthService",
				},
			}).
			WithActor(auditocsf.Actor{
				User: &auditocsf.User{
					UID: authInfo.GetAuditLogPrincipalUserId(),
				},
			}).
			WithMessage(fmt.Sprintf("Updated user '%s' in tenant %s", req.UserId, tenant.Name)).
			Build(),
		authInfo.GetAuditLogTenantName(),
	)

	return &authpb.UpdateUserOnTenantResponse{
		CustomerUiRoles: mapping.CustomerUiRoles,
	}, nil
}

func (s *AuthGrpcServer) GetUserOnTenant(ctx context.Context, req *authpb.GetUserOnTenantRequest) (*authpb.GetUserOnTenantResponse, error) {
	err := authCheck(ctx, req.TenantId, tokenscopesproto.Scope_AUTH_R)
	if err != nil {
		return nil, err
	}

	tenant, err := s.getTenant(ctx, req.TenantId)
	if err != nil {
		return nil, err
	}

	tenantMappingDAO := s.daoFactory.GetUserTenantMappingDAO(tenant.Name)
	mapping, err := tenantMappingDAO.GetByUser(ctx, req.UserId)
	if err != nil {
		return nil, status.Error(codes.Internal, "Failed to check existing mapping")
	}

	if mapping == nil {
		return nil, status.Error(codes.NotFound, "Resource not found")
	}

	return &authpb.GetUserOnTenantResponse{
		CustomerUiRoles: mapping.CustomerUiRoles,
	}, nil
}

func (s *AuthGrpcServer) RemoveUserFromTenant(ctx context.Context, req *authpb.RemoveUserFromTenantRequest) (*authpb.RemoveUserFromTenantResponse, error) {
	err := authCheck(ctx, req.TenantId, tokenscopesproto.Scope_AUTH_RW)
	if err != nil {
		// Log OCSF event for authorization failure
		if authInfo, ok := auth.GetAugmentClaims(ctx); ok {
			var userEmail string
			if email, ok := authInfo.GetIapEmail(); ok {
				userEmail = email
			} else if authInfo.UserEmail != "" {
				userEmail = authInfo.UserEmail
			} else {
				userEmail = "unknown"
			}

			if err := s.ocsfAuditLogger.LogAPIActivityWithTenant(
				auditocsf.NewAPIActivityEventBuilder(auditocsf.ActivityAPIDelete).
					WithAPI(auditocsf.API{
						Operation: "RemoveUserFromTenant",
						Service: auditocsf.APIService{
							Name: "AuthService",
						},
					}).
					WithActor(auditocsf.Actor{
						User: &auditocsf.User{
							UID:    authInfo.OpaqueUserID,
							TypeID: 1,
						},
					}).
					WithMessage("Authorization failed - insufficient permissions for RemoveUserFromTenant").
					WithStatus(auditocsf.StatusFailure).
					WithMessage(fmt.Sprintf("Authorization failed - insufficient permissions for RemoveUserFromTenant: user %s (%s, email: %s) attempted to access tenant %s",
						authInfo.OpaqueUserID, authInfo.OpaqueUserIDType, userEmail, req.TenantId)).
					Build(),
				req.TenantId,
			); err != nil {
				log.Ctx(ctx).Warn().Err(err).Msg("Failed to log OCSF audit event for authorization failure")
			}
		}
		return nil, err
	}

	// Get tenant and auth info for OCSF logging
	tenant, err := s.getTenant(ctx, req.TenantId)
	if err != nil {
		return nil, err
	}
	authInfo, _ := auth.GetAugmentClaims(ctx)

	err = RemoveUserFromTenantInternal(
		ctx,
		req.UserId,
		req.TenantId,
		nil,
		s.daoFactory,
		s.tenantMap,
		s.auditLogger,
		s.requestInsightPublisher,
	)
	if err != nil {
		return nil, err
	}

	// Log OCSF event for user removal from tenant
	if err := s.ocsfAuditLogger.LogAPIActivityWithTenant(
		auditocsf.NewAPIActivityEventBuilder(auditocsf.ActivityAPIDelete).
			WithAPI(auditocsf.API{
				Operation: "RemoveUserFromTenant",
				Service: auditocsf.APIService{
					Name: "AuthService",
				},
			}).
			WithActor(auditocsf.Actor{
				User: &auditocsf.User{
					UID: authInfo.OpaqueUserID,
				},
			}).
			WithMessage(fmt.Sprintf("Removed user '%s' from tenant %s", req.UserId, tenant.Name)).
			Build(),
		req.TenantId,
	); err != nil {
		log.Ctx(ctx).Warn().Err(err).Msg("Failed to log OCSF audit event for user removal from tenant")
	}

	return &authpb.RemoveUserFromTenantResponse{}, nil
}

// helper function to remove user from tenant - this should be used AFTER any auth checks
func (s *AuthGrpcServer) removeUserFromTenant(ctx context.Context, req *authpb.RemoveUserFromTenantRequest, tenantIdToNameMappings map[string]string) (*authpb.RemoveUserFromTenantResponse, error) {
	err := RemoveUserFromTenantInternal(
		ctx,
		req.UserId,
		req.TenantId,
		tenantIdToNameMappings,
		s.daoFactory,
		s.tenantMap,
		s.auditLogger,
		s.requestInsightPublisher,
	)
	if err != nil {
		return nil, err
	}
	return &authpb.RemoveUserFromTenantResponse{}, nil
}

func (s *AuthGrpcServer) UpdateUserEmail(ctx context.Context, req *authpb.UpdateUserEmailRequest) (*authpb.UpdateUserEmailResponse, error) {
	err := authCheck(ctx, "", tokenscopesproto.Scope_AUTH_RW)
	if err != nil {
		return nil, err
	}

	// Log the operation
	authClaims, _ := auth.GetAugmentClaims(ctx)
	requestContext, _ := requestcontext.FromGrpcContext(ctx)
	s.auditLogger.WriteAuditLog(
		authClaims,
		fmt.Sprintf("Update user '%s' email from %s to %s", req.UserId, req.CurrentEmail, req.NewEmail),
		audit.NewUser(req.UserId),
		requestContext,
		audit.NewProtoRequest(req),
	)

	userDAO := s.daoFactory.GetUserDAO()

	// Update the user email
	updateErr := error(nil)
	user, err := userDAO.TryUpdate(ctx, req.UserId, func(u *auth_entities.User) bool {
		if u.Email != req.CurrentEmail {
			updateErr = status.Error(codes.InvalidArgument, "Current email doesn't match")
			return false
		}

		u.Email = req.NewEmail
		return true
	}, DefaultRetry)
	if user == nil {
		return nil, status.Error(codes.NotFound, "User not found")
	}
	if err != nil {
		return nil, status.Error(codes.Internal, "Failed to update user email")
	}
	if updateErr != nil {
		return nil, updateErr
	}

	// Delete all token hashes for this user
	totalTokensDeleted := 0
	for _, tenantID := range user.Tenants {
		tokensDeleted, err := deleteUserTokensForTenant(ctx, req.UserId, tenantID, s.daoFactory)
		if err != nil {
			log.Ctx(ctx).Error().Err(err).Str("tenant_id", tenantID).Msg("Failed to delete tokens for tenant")
		} else {
			totalTokensDeleted += tokensDeleted
		}
	}

	log.Ctx(ctx).Info().Int("count", totalTokensDeleted).Str("user_id", req.UserId).Msg("Deleted tokens after email update")

	// Revoke any user cookies used for authentication
	userDAO.UpdateNonce(ctx, user.Id)

	return &authpb.UpdateUserEmailResponse{}, nil
}

func (s *AuthGrpcServer) ListTenantUsers(ctx context.Context, req *authpb.ListTenantUsersRequest) (*authpb.ListTenantUsersResponse, error) {
	err := authCheck(ctx, req.TenantId, tokenscopesproto.Scope_AUTH_R)
	if err != nil {
		return nil, err
	}

	tenant, err := s.getTenant(ctx, req.TenantId)
	if err != nil {
		return nil, err
	}

	tenantMappingDAO := s.daoFactory.GetUserTenantMappingDAO(tenant.Name)
	userDAO := s.daoFactory.GetUserDAO()

	userIDs := make([]string, 0, 1024)
	userIdToRoles := make(map[string]*authpb.UserTenantCustomerUiRoles, len(userIDs))
	err = tenantMappingDAO.FindAll(ctx, func(mapping *auth_entities.UserTenantMapping) bool {
		if mapping.Tenant == tenant.Name {
			userIdToRoles[mapping.UserId] = &authpb.UserTenantCustomerUiRoles{CustomerUiRoles: mapping.CustomerUiRoles}
			userIDs = append(userIDs, mapping.UserId)
		}
		return true
	})
	if err != nil {
		return nil, status.Error(codes.Internal, "Failed to fetch mappings")
	}

	slices.Sort(userIDs)

	users := make([]*auth_entities.User, 0, len(userIDs))
	idx := 0

	err = userDAO.BatchGet(ctx, userIDs, 1000, func(user *auth_entities.User) bool {
		idxMatch := idx
		for userIDs[idxMatch] < user.Id {
			if idxMatch != idx {
				log.Ctx(ctx).Error().Msgf("User not found: %s", userIDs[idxMatch])
			}
			idxMatch++
		}
		if userIDs[idxMatch] != user.Id {
			log.Ctx(ctx).Error().Msgf("Unexpected user ID returned: %s != %s", userIDs[idxMatch], user.Id)
			return false
		}
		idx = idxMatch

		// Filter user suspensions for current user circumstances if requested
		if req.FilterSuspensions && len(user.Tenants) > 0 {
			tenant, err := s.getTenant(ctx, user.Tenants[0])
			if err == nil && tenant != nil {
				user.Suspensions = s.filterSuspensions(ctx, user, tenant)
			}
		}

		users = append(users, user)
		return true
	})
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msgf("Failed to fetch users")
	}

	return &authpb.ListTenantUsersResponse{
		Users:                   users,
		UserIdToCustomerUiRoles: userIdToRoles,
	}, nil
}

// Get the relevant subscription ID for a user, taking into account teams
func (s *AuthGrpcServer) getTeamOrUserSubscription(ctx context.Context, user *auth_entities.User, tenant *tw_pb.Tenant) (*auth_entities.Subscription, error) {
	subscriptionId := user.OrbSubscriptionId
	if tenantutil.IsEnterpriseTenant(tenant) {
		return nil, nil
	}
	if tenantutil.IsSelfServeTeamTenant(tenant) {
		// Determine subscription from the tenant team mapping
		tenantSubscriptionMappingDAO := s.daoFactory.GetTenantSubscriptionMappingDAO()
		mapping, err := tenantSubscriptionMappingDAO.Get(ctx, tenant.Id)
		if err != nil {
			log.Ctx(ctx).Error().Err(err).Msgf("Failed to fetch tenant subscription mapping")
			subscriptionId = ""
		}
		if err == nil && mapping != nil {
			subscriptionId = mapping.OrbSubscriptionId
		}
	}
	if subscriptionId == "" {
		log.Ctx(ctx).Error().Str("user_id", user.Id).Msg("User does not have an Orb subscription")
		return nil, status.Error(codes.InvalidArgument, "Orb user does not have a subscription")
	}

	subscriptionDAO := s.daoFactory.GetSubscriptionDAO()
	subscription, err := subscriptionDAO.Get(ctx, subscriptionId)
	if err != nil {
		return nil, err
	}
	if subscription == nil {
		log.Ctx(ctx).Error().Str("subscription_id", subscriptionId).Str("user_id", user.Id).Msg("Subscription not found")
		return nil, status.Error(codes.NotFound, fmt.Sprintf("Subscription not found: subscription_id=%s, user_id=%s", subscriptionId, user.Id))
	}
	return subscription, nil
}

// filterSuspensions filters user suspensions based on tenant type, suspension type, and payment status
func (s *AuthGrpcServer) filterSuspensions(ctx context.Context, user *auth_entities.User, tenant *tw_pb.Tenant) []*auth_entities.UserSuspension {
	// If user is exempt from suspensions, don't include any suspensions
	if user.SuspensionExempt {
		return []*auth_entities.UserSuspension{}
	}

	// Check if user has payment method using existing subscription data
	trial_plan := false
	community_plan := false

	// For Orb users, check the subscription's HasPaymentMethod field
	if subscription, err := s.getTeamOrUserSubscription(ctx, user, tenant); err == nil && subscription != nil {
		if subscription.ExternalPlanId == s.orbConfig.GetTrialPlan().ID {
			trial_plan = true
		} else if subscription.ExternalPlanId == s.orbConfig.GetCommunityPlan().ID {
			community_plan = true
		}
	}

	// Suspensions don't apply to enterprise users
	isEnterprise := tenantutil.IsEnterpriseTenant(tenant)

	var filteredSuspensions []*auth_entities.UserSuspension

	for _, suspension := range user.Suspensions {
		shouldInclude := false

		switch suspension.SuspensionType {
		case auth_entities.UserSuspensionType_USER_SUSPENSION_TYPE_FREE_TRIAL_ABUSE:
			// Include only for trial plan users
			// TODO: Enforce for community when able to identify paying community users.
			shouldInclude = !isEnterprise && trial_plan
		case auth_entities.UserSuspensionType_USER_SUSPENSION_TYPE_DISPOSABLE_EMAIL:
			// Include only for trial plan users
			// TODO: Enforce for community when able to identify paying community users.
			shouldInclude = !isEnterprise && trial_plan
		case auth_entities.UserSuspensionType_USER_SUSPENSION_TYPE_COMMUNITY_ABUSE:
			// Include only for community plan users
			// TODO: Enforce only when able to tell paying from non-paying community users.
			shouldInclude = false && !isEnterprise && community_plan
		case auth_entities.UserSuspensionType_USER_SUSPENSION_TYPE_API_ABUSE:
			// Include for all users
			shouldInclude = true
		case auth_entities.UserSuspensionType_USER_SUSPENSION_TYPE_PAYMENT_FRAUD:
			// Include for all non-enterprise users
			shouldInclude = !isEnterprise
		default:
			// Include unknown suspension types for safety
			shouldInclude = true
		}

		if shouldInclude {
			filteredSuspensions = append(filteredSuspensions, suspension)
		}
	}

	return filteredSuspensions
}

func (s *AuthGrpcServer) GetUser(ctx context.Context, req *authpb.GetUserRequest) (*authpb.GetUserResponse, error) {
	// Get auth claims first
	authInfo, ok := auth.GetAugmentClaims(ctx)
	if !ok {
		return nil, status.Error(codes.Unknown, "Invalid context")
	}

	// Auth checks
	if req.TenantId != nil || authInfo.AllowsAllTenants() {
		// Tenant-based authorization
		if err := authCheck(ctx, req.GetTenantId(), tokenscopesproto.Scope_AUTH_R); err != nil {
			return nil, err
		}
	} else {
		// Self-access authorization
		if err := selfAuthCheck(ctx, req.UserId, tokenscopesproto.Scope_AUTH_R); err != nil {
			return nil, err
		}
	}

	// Get user information after auth checks
	userDAO := s.daoFactory.GetUserDAO()
	user, err := userDAO.Get(ctx, req.UserId)
	if err != nil {
		return nil, status.Error(codes.Internal, "Failed to fetch user")
	}
	if user == nil {
		return nil, status.Error(codes.NotFound, "Resource not found")
	}

	// Verify user belongs to the tenant if tenant-based access
	if req.TenantId != nil && !slices.Contains(user.Tenants, *req.TenantId) {
		return nil, status.Error(codes.NotFound, "Resource not found")
	}

	// Filter user suspensions for current user circumstances
	if len(user.Tenants) > 0 {
		tenant, err := s.getTenant(ctx, user.Tenants[0])
		if err == nil && tenant != nil {
			user.Suspensions = s.filterSuspensions(ctx, user, tenant)
		}
	}

	return &authpb.GetUserResponse{User: user}, nil
}

// Get the information of a user related to billing.
func (s *AuthGrpcServer) GetUserBillingInfo(ctx context.Context, req *authpb.GetUserBillingInfoRequest) (*authpb.GetUserBillingInfoResponse, error) {
	// Auth check. Only allow tenant based auth since this is only used by the billing service now.
	if err := authCheck(ctx, req.TenantId, tokenscopesproto.Scope_AUTH_R); err != nil {
		return nil, err
	}

	// Use the helper function to get the user billing info
	billingInfo, err := GetUserBillingInfo(ctx, req.UserId, req.TenantId, s.daoFactory, s.tenantMap)
	if err != nil {
		return nil, err
	}

	// Convert to proto response
	return &authpb.GetUserBillingInfoResponse{
		UserId:            billingInfo.UserID,
		Email:             billingInfo.Email,
		IsSelfServeTeam:   billingInfo.IsSelfServeTeam,
		OrbCustomerId:     billingInfo.OrbCustomerID,
		OrbSubscriptionId: billingInfo.OrbSubscriptionID,
		StripeCustomerId:  billingInfo.StripeCustomerID,
	}, nil
}

func matchUser(user *auth_entities.User, searchString string) bool {
	return strings.Contains(strings.ToLower(user.Email), searchString) ||
		strings.Contains(strings.ToLower(user.Id), searchString) ||
		strings.Contains(strings.ToLower(user.OrbCustomerId), searchString) ||
		strings.Contains(strings.ToLower(user.OrbSubscriptionId), searchString) ||
		strings.Contains(strings.ToLower(user.StripeCustomerId), searchString) ||
		slices.ContainsFunc(user.Tenants, func(tenant string) bool {
			return strings.Contains(strings.ToLower(tenant), searchString)
		}) ||
		slices.ContainsFunc(user.IdpUserIds, func(idpUserId string) bool {
			return strings.Contains(strings.ToLower(idpUserId), searchString)
		})
}

func (s *AuthGrpcServer) GetUsers(ctx context.Context, req *authpb.GetUsersRequest) (*authpb.GetUsersResponse, error) {
	if err := authCheck(ctx, "", tokenscopesproto.Scope_AUTH_R); err != nil {
		return nil, err
	}
	if req.IncludeGdprCcpaDeleted {
		if err := authCheck(ctx, "", tokenscopesproto.Scope_PII_ADMIN); err != nil {
			return nil, err
		}
	}

	// Get the user DAO
	userDAO := s.daoFactory.GetUserDAO()

	// Validation
	if len(req.PageToken) > 64 {
		return nil, status.Error(codes.InvalidArgument, "Page token too long")
	}

	pageSize := max(req.PageSize, 1000)
	if pageSize <= 0 {
		pageSize = 1000
	}

	if len(req.SearchString) > 128 {
		return nil, status.Error(codes.InvalidArgument, "Search string too long")
	}

	searchString := strings.ToLower(req.SearchString)

	// Get the users
	users := make([]*auth_entities.User, 0, pageSize)
	nextPageToken := ""

	err := userDAO.FindAllWithStartKey(ctx, req.PageToken, func(user *auth_entities.User) bool {
		if !req.IncludeGdprCcpaDeleted && user.GetDeletionState() == auth_entities.User_GDPR_CCPA_DELETED {
			return true
		}

		// only return users with an email that matches the search string if provided
		// if no search string is specified, return all users
		if searchString != "" && !matchUser(user, searchString) {
			return true
		}

		// Filter user suspensions for current user circumstances if requested
		if req.FilterSuspensions && len(user.Tenants) > 0 {
			tenant, err := s.getTenant(ctx, user.Tenants[0])
			if err == nil && tenant != nil {
				user.Suspensions = s.filterSuspensions(ctx, user, tenant)
			}
		}

		users = append(users, user)
		if len(users) >= int(pageSize) {
			nextPageToken = user.Id + string(0xFF)
			return false
		}

		return true
	})
	if err != nil {
		return nil, err
	}

	// Return the users
	return &authpb.GetUsersResponse{
		Users:         users,
		NextPageToken: nextPageToken,
	}, nil
}

// Update the information of a user related to billing.
func (s *AuthGrpcServer) UpdateUserBillingInfo(ctx context.Context, req *authpb.UpdateUserBillingInfoRequest) (*authpb.UpdateUserBillingInfoResponse, error) {
	// Auth check. Only allow tenant based auth since this is only used by the billing service now.
	if err := authCheck(ctx, req.TenantId, tokenscopesproto.Scope_AUTH_RW); err != nil {
		return nil, err
	}

	// Verify the user exists
	userDAO := s.daoFactory.GetUserDAO()
	user, err := userDAO.Get(ctx, req.UserId)
	if err != nil {
		return nil, err
	}
	if user == nil {
		return nil, status.Error(codes.NotFound, "Resource not found")
	}

	// audit log
	authInfo, _ := auth.GetAugmentClaims(ctx)
	requestContext, _ := requestcontext.FromGrpcContext(ctx)
	s.auditLogger.WriteAuditLog(
		authInfo,
		fmt.Sprintf("Update user '%s' billing info in tenant %s", req.UserId, req.TenantId),
		audit.NewUser(req.UserId),
		audit.NewTenantID(req.TenantId),
		requestContext,
		audit.NewProtoRequest(req),
	)

	// Update the user
	updateUser := func(u *auth_entities.User) bool {
		update := false
		if req.OrbCustomerId != nil && u.OrbCustomerId != *req.OrbCustomerId {
			u.OrbCustomerId = *req.OrbCustomerId
			update = true
		}
		if req.OrbSubscriptionId != nil && u.OrbSubscriptionId != *req.OrbSubscriptionId {
			u.OrbSubscriptionId = *req.OrbSubscriptionId
			update = true
		}
		if req.StripeCustomerId != nil && u.StripeCustomerId != *req.StripeCustomerId {
			u.StripeCustomerId = *req.StripeCustomerId
			update = true
		}
		return update
	}
	_, err = userDAO.TryUpdate(ctx, user.Id, updateUser, DefaultRetry)
	if err != nil {
		return nil, err
	}

	return &authpb.UpdateUserBillingInfoResponse{}, nil
}

func (s *AuthGrpcServer) GetTokenInfo(ctx context.Context, req *authpb.GetTokenInfoRequest) (*authpb.GetTokenInfoResponse, error) {
	tokenHashDAO := s.daoFactory.GetTokenHashDAO()
	tokenInfo, err := tokenHashDAO.Get(ctx, tokenHash(req.Token))
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to get token info")
		return nil, status.Error(codes.Internal, "Failed to fetch token info")
	}
	if tokenInfo == nil {
		log.Ctx(ctx).Warn().Msgf("Token not found for hash %s", tokenHash(req.Token))
		return nil, status.Error(codes.NotFound, "Resource not found")
	}

	tenant, err := s.tenantMap.GetTenantByIdDeletedOk(tokenInfo.TenantId)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to get tenant")
		return nil, status.Error(codes.Internal, "Internal error")
	}
	if tenant == nil {
		log.Ctx(ctx).Error().Msgf("Tenant ID %s not found", tokenInfo.TenantId)
		return nil, status.Error(codes.Internal, "Internal")
	}
	if tenant.DeletedAt != "" {
		log.Ctx(ctx).Error().Msgf("Tenant %s is deleted", tenant.Name)
		return nil, status.Error(codes.NotFound, "Tenant deleted")
	}

	if tokenInfo.ExpirationTimeSeconds != 0 && time.Now().Sub(tokenInfo.CreationTime.AsTime()) >= time.Duration(tokenInfo.ExpirationTimeSeconds)*time.Second {
		log.Ctx(ctx).Info().Msgf("Token expired for hash %s", tokenHash(req.Token))

		// Log OCSF event for expired token authentication failure
		if err := s.ocsfAuditLogger.LogAPIActivityWithTenant(
			auditocsf.NewAPIActivityEventBuilder(auditocsf.ActivityAPIRead).
				WithAPI(auditocsf.API{
					Operation: "GetTokenInfo",
					Service: auditocsf.APIService{
						Name: "AuthService",
					},
				}).
				WithActor(auditocsf.Actor{
					User: &auditocsf.User{
						UID:    tokenInfo.AugmentUserId,
						TypeID: 1,
					},
				}).
				WithStatus(auditocsf.StatusFailure).
				WithMessage("Retrieved token is expired").
				Build(),
			tokenInfo.TenantId,
		); err != nil {
			log.Ctx(ctx).Warn().Err(err).Msg("Failed to log OCSF audit event for expired token")
		}

		return nil, status.Error(codes.NotFound, "Token expired")
	}

	// Get user
	userID := tokenInfo.AugmentUserId
	userDAO := s.daoFactory.GetUserDAO()
	user, err := userDAO.Get(ctx, userID)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("user_id", userID).Msg("GetTokenInfo failed: error fetching user")
		return nil, status.Error(codes.Internal, "Failed to fetch user")
	}
	if user == nil {
		log.Ctx(ctx).Error().Str("user_id", userID).Msg("GetTokenInfo failed: user not found")
		return nil, status.Error(codes.NotFound, "User not found")
	}

	// Filter suspensions based on tenant type and suspension type
	filteredSuspensions := s.filterSuspensions(ctx, user, tenant)

	response := &authpb.GetTokenInfoResponse{
		UserId:        user.Email,
		AugmentUserId: userID,
		UserEmail:     user.Email,
		TenantId:      tokenInfo.TenantId,
		TenantName:    tenant.Name,
		Suspensions:   filteredSuspensions,
	}

	// Get subscription type
	subscriptionType, subscription, err := s.getSubscriptionType(ctx, tenant, user)
	if err != nil {
		return nil, err
	}

	// Set the subscription field based on type
	switch s := subscriptionType.(type) {
	case EnterpriseSubscription:
		response.Subscription = s.GetTokenInfoResponse_Enterprise
	case ActiveSubscription:
		response.Subscription = s.GetTokenInfoResponse_ActiveSubscription
	case TrialSubscription:
		response.Subscription = s.GetTokenInfoResponse_Trial
	case InactiveSubscription:
		response.Subscription = s.GetTokenInfoResponse_InactiveSubscription
	}

	// Check feature flag to determine if featureGatingInfo should be populated
	enableFeatureGatingInfo, err := s.featureFlagHandle.GetBool("auth_central_enable_feature_gating_info", false)
	if err != nil {
		log.Warn().Err(err).Msg("Failed to get auth_central_enable_feature_gating_info feature flag, defaulting to false")
		enableFeatureGatingInfo = false
	}

	if enableFeatureGatingInfo {
		response.FeatureGatingInfo = buildFeatureGatingInfo(s.orbConfig, subscriptionType, subscription, user)
	}

	return response, nil
}

// Get the subscription type for a user
func (s *AuthGrpcServer) GetUserSubscriptionInfo(
	ctx context.Context,
	req *authpb.GetUserSubscriptionInfoRequest,
) (*authpb.GetUserSubscriptionInfoResponse, error) {
	// Get auth claims first
	_, ok := auth.GetAugmentClaims(ctx)
	if !ok {
		return nil, status.Error(codes.Unknown, "Invalid context")
	}

	// Tenant-based authorization
	if err := authCheck(ctx, req.GetTenantId(), tokenscopesproto.Scope_AUTH_R); err != nil {
		return nil, err
	}

	// Get user
	userDAO := s.daoFactory.GetUserDAO()
	user, err := userDAO.Get(ctx, req.UserId)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("user_id", req.UserId).Msg("GetSubscriptionType failed: error fetching user")
		return nil, status.Error(codes.Internal, "Failed to fetch user")
	}
	if user == nil {
		log.Ctx(ctx).Error().Str("user_id", req.UserId).Msg("GetSubscriptionType failed: user not found")
		return nil, status.Error(codes.NotFound, "User not found")
	}

	// Get tenant
	tenant, err := s.getTenant(ctx, req.TenantId)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("tenant_id", req.TenantId).Msg("GetSubscriptionType failed: error fetching tenant")
		return nil, status.Error(codes.Internal, "Failed to fetch tenant")
	}
	if tenant == nil {
		log.Ctx(ctx).Error().Str("tenant_id", req.TenantId).Msg("GetSubscriptionType failed: tenant not found")
		return nil, status.Error(codes.NotFound, "Tenant not found")
	}

	// Get subscription type
	subscriptionType, subscription, err := s.getSubscriptionType(ctx, tenant, user)
	if err != nil {
		return nil, err
	}

	filteredSuspensions := s.filterSuspensions(ctx, user, tenant)

	response := &authpb.GetUserSubscriptionInfoResponse{
		Suspensions: filteredSuspensions,
	}

	// Set the subscription field based on type
	switch s := subscriptionType.(type) {
	case EnterpriseSubscription:
		response.Subscription = &authpb.GetUserSubscriptionInfoResponse_Enterprise{
			Enterprise: s.GetTokenInfoResponse_Enterprise.Enterprise,
		}
	case ActiveSubscription:
		response.Subscription = &authpb.GetUserSubscriptionInfoResponse_ActiveSubscription{
			ActiveSubscription: s.GetTokenInfoResponse_ActiveSubscription.ActiveSubscription,
		}
	case TrialSubscription:
		response.Subscription = &authpb.GetUserSubscriptionInfoResponse_Trial{
			Trial: s.GetTokenInfoResponse_Trial.Trial,
		}
	case InactiveSubscription:
		response.Subscription = &authpb.GetUserSubscriptionInfoResponse_InactiveSubscription{
			InactiveSubscription: s.GetTokenInfoResponse_InactiveSubscription.InactiveSubscription,
		}
	}

	// Check feature flag to determine if featureGatingInfo should be populated
	enableFeatureGatingInfo, err := s.featureFlagHandle.GetBool("auth_central_enable_feature_gating_info", false)
	if err != nil {
		log.Warn().Err(err).Msg("Failed to get auth_central_enable_feature_gating_info feature flag, defaulting to false")
		enableFeatureGatingInfo = false
	}

	if enableFeatureGatingInfo {
		response.FeatureGatingInfo = buildFeatureGatingInfo(s.orbConfig, subscriptionType, subscription, user)
	}

	return response, nil
}

// Custom interface for token info response subscription type
type SubscriptionType interface {
	isSubscriptionType()
}

type EnterpriseSubscription struct {
	*authpb.GetTokenInfoResponse_Enterprise
}

type ActiveSubscription struct {
	*authpb.GetTokenInfoResponse_ActiveSubscription
}

type TrialSubscription struct {
	*authpb.GetTokenInfoResponse_Trial
}

type InactiveSubscription struct {
	*authpb.GetTokenInfoResponse_InactiveSubscription
}

// Implement the interface
func (EnterpriseSubscription) isSubscriptionType() {}
func (ActiveSubscription) isSubscriptionType()     {}
func (TrialSubscription) isSubscriptionType()      {}
func (InactiveSubscription) isSubscriptionType()   {}

// getSubscriptionType determines the subscription type based on tenant tier and user subscription
// Works with both Stripe and Orb billing methods
func (s *AuthGrpcServer) getSubscriptionType(
	ctx context.Context,
	tenant *tw_pb.Tenant,
	user *auth_entities.User,
) (SubscriptionType, *auth_entities.Subscription, error) {
	var subscription SubscriptionType

	// if the user is in multiple tenants, let's return an active subscription since they may be currently switching tenants
	if len(user.Tenants) > 1 {
		log.Ctx(ctx).Info().Str("user_id", user.Id).Int("tenant_count", len(user.Tenants)).Msg("User is in multiple tenants, returning active subscription")
		subscription = ActiveSubscription{
			&authpb.GetTokenInfoResponse_ActiveSubscription{
				ActiveSubscription: &authpb.ActiveSubscription{},
			},
		}
		SubscriptionTypeCounter.WithLabelValues(tenant.Tier.String(), "ACTIVE_MULTI_TENANT", tenantwatcherclient.MetricsTenantName(tenant)).Inc()
		return subscription, nil, nil
	}

	// Set subscription based on tenant tier and user subscription
	if tenant.Tier == tw_pb.TenantTier_TENANT_TIER_UNKNOWN {
		log.Ctx(ctx).Error().Msgf("Tenant %s has unknown tier", tenant.Name)
		return nil, nil, status.Error(codes.Internal, "Internal error")
	} else if tenant.Tier == tw_pb.TenantTier_ENTERPRISE {
		// Enterprise tier
		subscription = EnterpriseSubscription{
			&authpb.GetTokenInfoResponse_Enterprise{
				Enterprise: &authpb.EnterpriseSubscription{},
			},
		}
		SubscriptionTypeCounter.WithLabelValues(tenant.Tier.String(), "ENTERPRISE", tenantwatcherclient.MetricsTenantName(tenant)).Inc()
	} else {
		// Check to see if subscriptionCreationInfo is pending
		if user.SubscriptionCreationInfo != nil && user.SubscriptionCreationInfo.Status == auth_entities.User_SubscriptionCreationInfo_PENDING {
			subscription = ActiveSubscription{
				&authpb.GetTokenInfoResponse_ActiveSubscription{
					ActiveSubscription: &authpb.ActiveSubscription{},
				},
			}
			SubscriptionTypeCounter.WithLabelValues(tenant.Tier.String(), "ACTIVE_SUBSCRIPTION_CREATION_PENDING", tenantwatcherclient.MetricsTenantName(tenant)).Inc()
			return subscription, nil, nil
		}

		// Get user billing info - this will handle getting the appropriate subscription id for the team OR user
		userBillingInfo, err := GetUserBillingInfo(ctx, user.Id, tenant.Id, s.daoFactory, s.tenantMap)
		if err != nil {
			log.Ctx(ctx).Error().Err(err).Msg("Failed to get user billing info")
			return nil, nil, status.Error(codes.Internal, "Failed to get user billing info")
		}

		return s.getOrbSubscriptionType(ctx, tenant, &userBillingInfo.OrbSubscriptionID, user.Id)
	}
	return subscription, nil, nil
}

func (s *AuthGrpcServer) getOrbSubscriptionType(
	ctx context.Context,
	tenant *tw_pb.Tenant,
	orbSubscriptionId *string,
	augmentUserId string,
) (SubscriptionType, *auth_entities.Subscription, error) {
	var subscriptionType SubscriptionType

	if orbSubscriptionId == nil || *orbSubscriptionId == "" {
		log.Ctx(ctx).Warn().Msgf("No Orb subscription ID found for user %s", augmentUserId)
		// return an inactive subscription
		subscriptionType = InactiveSubscription{
			&authpb.GetTokenInfoResponse_InactiveSubscription{
				InactiveSubscription: &authpb.InactiveSubscription{},
			},
		}
		SubscriptionTypeCounter.WithLabelValues(tenant.Tier.String(), "INACTIVE_NO_ORB_SUBSCRIPTION", tenantwatcherclient.MetricsTenantName(tenant)).Inc()
		return subscriptionType, nil, nil
	}

	// Get Orb subscription
	subscription, err := s.getUserSubscription(ctx, orbSubscriptionId)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("orb_subscription_id", *orbSubscriptionId).Msg("Failed to get user subscription")
		return nil, nil, status.Error(codes.Internal, "Failed to fetch user subscription")
	}

	// Check if subscription is nil
	// If subscription ID is present, but subscription entry does not yet exist, this means
	// we are in the interval of time between the creation of the subscription and the webhook
	// updating the subscription table, or the webhook processor isn't turned on.
	// In either case, this should only happen for a newly created user; treating as active.
	if subscription == nil {
		log.Ctx(ctx).Warn().Str("orb_subscription_id", *orbSubscriptionId).Str("user_id", augmentUserId).Msg("No subscription found for Orb user, treating as active")
		// Create a timestamp for 14 days in the future
		futureTime := time.Now().Add(14 * 24 * time.Hour)
		subscriptionType = ActiveSubscription{
			&authpb.GetTokenInfoResponse_ActiveSubscription{
				ActiveSubscription: &authpb.ActiveSubscription{
					EndDate:              timestamppb.New(futureTime), // it's a new user so most likely 14 days
					UsageBalanceDepleted: false,
				},
			},
		}
		return subscriptionType, nil, nil
	}

	// Check subscription status
	if subscription.OrbStatus == auth_entities.Subscription_ORB_STATUS_ACTIVE {

		// Active subscription
		subscriptionType = ActiveSubscription{
			&authpb.GetTokenInfoResponse_ActiveSubscription{
				ActiveSubscription: &authpb.ActiveSubscription{
					EndDate:              subscription.EndDate,
					UsageBalanceDepleted: subscription.UsageBalanceDepleted,
				},
			},
		}
		SubscriptionTypeCounter.WithLabelValues(tenant.Tier.String(), "ORB_ACTIVE", tenantwatcherclient.MetricsTenantName(tenant)).Inc()

	} else if subscription.OrbStatus == auth_entities.Subscription_ORB_STATUS_ENDED {
		subscriptionType = InactiveSubscription{
			&authpb.GetTokenInfoResponse_InactiveSubscription{
				InactiveSubscription: &authpb.InactiveSubscription{},
			},
		}
		SubscriptionTypeCounter.WithLabelValues(tenant.Tier.String(), "ORB_INACTIVE", tenantwatcherclient.MetricsTenantName(tenant)).Inc()
	} else {
		// We do not expect orb subscription status to be anything other than ACTIVE or ENDED.
		log.Ctx(ctx).Error().Msgf("Unknown Orb subscription status: %v", subscription.OrbStatus)
		SubscriptionTypeCounter.WithLabelValues(tenant.Tier.String(), "ORB_UNKNOWN", tenantwatcherclient.MetricsTenantName(tenant)).Inc()
		return nil, nil, status.Error(codes.Internal, "Internal error")
	}
	return subscriptionType, subscription, nil
}

// getUserSubscription returns the user subscription
func (s *AuthGrpcServer) getUserSubscription(ctx context.Context, subscriptionID *string) (*auth_entities.Subscription, error) {
	// Get subscription

	if subscriptionID == nil || *subscriptionID == "" {
		return nil, nil
	}
	subscriptionDAO := s.daoFactory.GetSubscriptionDAO()
	subscription, err := subscriptionDAO.Get(ctx, *subscriptionID)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("subscription_id", *subscriptionID).Msg("GetUserSubscription failed: error fetching subscription")
		return nil, status.Error(codes.Internal, "Failed to fetch subscription")
	}

	return subscription, nil
}

// tokenHash generates a SHA256 hash of the token
func tokenHash(token string) string {
	hash := sha256.Sum256([]byte(token))
	return hex.EncodeToString(hash[:])
}

func (s *AuthGrpcServer) RevokeUserCookies(ctx context.Context, request *authpb.RevokeUserCookiesRequest) (*authpb.RevokeUserCookiesResponse, error) {
	err := authCheck(ctx, request.TenantId, tokenscopesproto.Scope_AUTH_RW)
	if err != nil {
		return nil, err
	}

	tenant, err := s.getTenant(ctx, request.TenantId)
	if err != nil {
		return nil, status.Error(codes.Internal, "Failed to fetch tenant")
	}

	authInfo, _ := auth.GetAugmentClaims(ctx)
	requestContext, _ := requestcontext.FromGrpcContext(ctx)
	s.auditLogger.WriteAuditLog(
		authInfo,
		fmt.Sprintf("Revoke user cookie from tenant %s", authInfo.TenantName),
		audit.NewTenantName(tenant.Name),
		audit.NewUser(request.UserId),
		audit.NewProtoRequest(request),
		requestContext,
	)

	userDAO := s.daoFactory.GetUserDAO()
	user, err := userDAO.Get(ctx, request.UserId)
	if err != nil {
		return nil, status.Error(codes.Internal, "Failed to fetch user")
	}

	if user == nil {
		return nil, status.Error(codes.NotFound, "Resource not found")
	}

	tenantMappingDAO := s.daoFactory.GetUserTenantMappingDAO(tenant.Name)
	mapping, err := tenantMappingDAO.GetByUser(ctx, request.UserId)
	if err != nil {
		return nil, status.Error(codes.Internal, "Failed to check existing mapping")
	}

	if mapping == nil {
		return nil, status.Error(codes.NotFound, "Resource not found")
	}

	// Force authentication on next request.
	userDAO.UpdateNonce(ctx, user.Id)

	return &authpb.RevokeUserCookiesResponse{}, nil
}

func (s *AuthGrpcServer) RevokeUser(ctx context.Context, request *authpb.RevokeUserRequest) (*authpb.RevokeUserResponse, error) {
	err := authCheck(ctx, request.TenantId, tokenscopesproto.Scope_AUTH_RW)
	if err != nil {
		return nil, err
	}

	tenant, err := s.getTenant(ctx, request.TenantId)
	if err != nil {
		return nil, status.Error(codes.Internal, "Failed to fetch tenant")
	}

	user, err := s.tenantMap.GetUserByEmailAddress(ctx, request.Email)
	if err != nil {
		return nil, err
	}
	if user == nil {
		return nil, status.Error(codes.NotFound, "Resource not found")
	}

	authInfo, _ := auth.GetAugmentClaims(ctx)
	requestContext, _ := requestcontext.FromGrpcContext(ctx)
	s.auditLogger.WriteAuditLog(
		authInfo,
		fmt.Sprintf("Revoke user '%s' from tenant %s", request.Email, tenant.Name),
		audit.NewTenantName(tenant.Name),
		audit.NewUser(user.Id),
		audit.NewProtoRequest(request),
		requestContext,
	)

	// Remove any tokens associated with this tenant and user.
	tokensDeleted, err := deleteUserTokensForTenant(ctx, user.Id, tenant.Id, s.daoFactory)
	if err != nil {
		return nil, err
	}

	// Revoke any user cookies used for authentication, since we are not removing the user
	userDAO := s.daoFactory.GetUserDAO()
	userDAO.UpdateNonce(ctx, user.Id)

	// Log OCSF event for user revocation (account access removal)
	if err := s.ocsfAuditLogger.LogAPIActivityWithTenant(
		auditocsf.NewAPIActivityEventBuilder(auditocsf.ActivityAPIDelete).
			WithAPI(auditocsf.API{
				Operation: "RevokeUser",
				Service: auditocsf.APIService{
					Name: "AuthService",
				},
			}).
			WithActor(auditocsf.Actor{
				User: &auditocsf.User{
					UID: authInfo.OpaqueUserID,
				},
			}).
			WithMessage(fmt.Sprintf("Revoked user '%s' from tenant %s", request.Email, tenant.Name)).
			Build(),
		request.TenantId,
	); err != nil {
		log.Ctx(ctx).Warn().Err(err).Msg("Failed to log OCSF audit event for user revocation")
	}

	return &authpb.RevokeUserResponse{TokensDeleted: int32(tokensDeleted)}, nil
}

func (s *AuthGrpcServer) RevokeUserById(ctx context.Context, request *authpb.RevokeUserByIdRequest) (*authpb.RevokeUserByIdResponse, error) {
	// For self-token revocation, users don't need any specific scope - just verify they are revoking their own tokens
	authClaims, ok := auth.GetAugmentClaims(ctx)
	if !ok {
		return nil, status.Error(codes.Unknown, "Invalid context")
	}

	if authClaims.OpaqueUserID != request.UserId {
		log.Ctx(ctx).Error().Msgf("Auth claims user ID %s (type: %s) does not match requested user ID %s", authClaims.OpaqueUserID, authClaims.OpaqueUserIDType, request.UserId)

		// Log OCSF event for authorization failure due to user ID mismatch
		var userEmail string
		if email, ok := authClaims.GetIapEmail(); ok {
			userEmail = email
		} else if authClaims.UserEmail != "" {
			userEmail = authClaims.UserEmail
		} else {
			userEmail = "unknown"
		}

		if err := s.ocsfAuditLogger.LogAPIActivityWithTenant(
			auditocsf.NewAPIActivityEventBuilder(auditocsf.ActivityAPIDelete).
				WithAPI(auditocsf.API{
					Operation: "RevokeUserById",
					Service: auditocsf.APIService{
						Name: "AuthService",
					},
				}).
				WithActor(auditocsf.Actor{
					User: &auditocsf.User{
						UID:    authClaims.OpaqueUserID,
						TypeID: 1,
					},
				}).
				WithMessage(fmt.Sprintf("Authorization failed - user ID mismatch: token user %s (%s, email: %s) attempted to access user %s",
					authClaims.OpaqueUserID, authClaims.OpaqueUserIDType, userEmail, request.UserId)).
				WithStatus(auditocsf.StatusFailure).
				Build(),
			request.TenantId,
		); err != nil {
			log.Ctx(ctx).Warn().Err(err).Msg("Failed to log OCSF audit event for user ID mismatch")
		}

		return nil, status.Error(codes.PermissionDenied, "Access denied: user ID in token does not match requested user")
	}

	// Remove any tokens associated with this tenant and user.
	tokensDeleted, err := deleteUserTokensForTenant(ctx, request.UserId, request.TenantId, s.daoFactory)
	if err != nil {
		return nil, err
	}

	// Revoke any user cookies used for authentication, since we are not removing the user
	userDAO := s.daoFactory.GetUserDAO()
	userDAO.UpdateNonce(ctx, request.UserId)

	return &authpb.RevokeUserByIdResponse{TokensDeleted: int32(tokensDeleted)}, nil
}

func (s *AuthGrpcServer) CreateUserSuspension(ctx context.Context, req *authpb.CreateUserSuspensionRequest) (*authpb.CreateUserSuspensionResponse, error) {
	// Add suspension to the user
	log.Ctx(ctx).Info().Str("user_id", req.UserId).Msg("CreateUserSuspension request received")

	err := authCheck(ctx, req.TenantId, tokenscopesproto.Scope_AUTH_RW)
	if err != nil {
		// Log OCSF event for authorization failure
		if authInfo, ok := auth.GetAugmentClaims(ctx); ok {
			var userEmail string
			if email, ok := authInfo.GetIapEmail(); ok {
				userEmail = email
			} else if authInfo.UserEmail != "" {
				userEmail = authInfo.UserEmail
			} else {
				userEmail = "unknown"
			}

			if err := s.ocsfAuditLogger.LogAPIActivityWithTenant(
				auditocsf.NewAPIActivityEventBuilder(auditocsf.ActivityAPICreate).
					WithAPI(auditocsf.API{
						Operation: "CreateUserSuspension",
						Service: auditocsf.APIService{
							Name: "AuthService",
						},
					}).
					WithActor(auditocsf.Actor{
						User: &auditocsf.User{
							UID:       authInfo.OpaqueUserID,
							TypeID:    1,
							EmailAddr: userEmail,
						},
					}).
					WithStatus(auditocsf.StatusFailure).
					WithMessage(fmt.Sprintf("Authorization failed - insufficient permissions for CreateUserSuspension: user %s (%s, email: %s) attempted to access tenant %s",
						authInfo.OpaqueUserID, authInfo.OpaqueUserIDType, userEmail, req.TenantId)).
					Build(),
				req.TenantId,
			); err != nil {
				log.Ctx(ctx).Warn().Err(err).Msg("Failed to log OCSF audit event for authorization failure")
			}
		}
		return nil, err
	}

	// If IAP User, ensure reason is provided
	err = requireReasonForIAPUser(ctx, req.Evidence)
	if err != nil {
		log.Error().Err(err).Msg("Reason is required for IAP users")
		return nil, err
	}

	authInfo, _ := auth.GetAugmentClaims(ctx)
	requestContext, _ := requestcontext.FromGrpcContext(ctx)
	suspensionId := uuid.New().String()
	s.auditLogger.WriteAuditLog(
		authInfo,
		fmt.Sprintf("Add suspension '%s' type %s to user '%s' with evidence '%s'",
			suspensionId, req.SuspensionType.String(), req.UserId, req.Evidence),
		audit.NewUser(req.UserId),
		audit.NewProtoRequest(req),
		requestContext,
	)

	userDAO := s.daoFactory.GetUserDAO()

	user, err := userDAO.Get(ctx, req.UserId)
	if err != nil {
		return nil, err
	}
	if user == nil {
		return nil, status.Error(codes.NotFound, "Resource not found")
	}

	// Check user context for validation
	isEnterpriseTenant := false
	isSelfServeTeam := false
	userTenant := &tw_pb.Tenant{}
	for _, tenantID := range user.Tenants {
		tenant, err := s.getTenant(ctx, tenantID)
		if err != nil {
			return nil, err
		}

		userTenant = tenant
		if tenantutil.IsEnterpriseTenant(tenant) {
			isEnterpriseTenant = true
			break
		}
		if tenantutil.IsSelfServeTeamTenant(tenant) {
			isSelfServeTeam = true
			break
		}
	}

	// Only self-serve users can be suspended
	if isEnterpriseTenant {
		return nil, status.Error(codes.InvalidArgument, "Enterprise users cannot be suspended")
	}

	if user.SuspensionExempt {
		return nil, status.Error(codes.InvalidArgument, "User is exempt from suspension")
	}

	// Validate suspension type against user and account
	logoutUser := false
	switch req.SuspensionType {
	case auth_entities.UserSuspensionType_USER_SUSPENSION_TYPE_API_ABUSE,
		auth_entities.UserSuspensionType_USER_SUSPENSION_TYPE_PAYMENT_FRAUD:
		// API abuse suspensions - for any self-serve user (including team members), excluding enterprise users
		logoutUser = true
	case auth_entities.UserSuspensionType_USER_SUSPENSION_TYPE_COMMUNITY_ABUSE:
		// Community abuse suspensions - only for individual community users (not team members or enterprise)
		if isSelfServeTeam {
			return nil, status.Error(codes.InvalidArgument, "Team users cannot be suspended for community abuse")
		}
		if !tenantutil.IsCommunityTenant(userTenant) {
			return nil, status.Error(codes.InvalidArgument, "Can only suspend community users for community abuse")
		}
		logoutUser = true

	case auth_entities.UserSuspensionType_USER_SUSPENSION_TYPE_FREE_TRIAL_ABUSE,
		auth_entities.UserSuspensionType_USER_SUSPENSION_TYPE_DISPOSABLE_EMAIL:

		subscription, err := s.getTeamOrUserSubscription(ctx, user, userTenant)
		if err != nil {
			return nil, err
		}

		// Subscription must be on the trial plan
		if subscription.ExternalPlanId != s.orbConfig.GetTrialPlan().ID {
			return nil, status.Error(codes.InvalidArgument, "Subscription is not on the trial plan")
		}

		// Check if the user has a payment method configured
		if subscription.HasPaymentMethod {
			return nil, status.Error(codes.InvalidArgument, "User has a payment method configured")
		}

	default:
		return nil, status.Error(codes.InvalidArgument, "Invalid suspension type")
	}

	// Add suspension to the user
	suspension := &auth_entities.UserSuspension{
		SuspensionId:   suspensionId,
		CreatedTime:    timestamppb.Now(),
		SuspensionType: req.SuspensionType,
		Evidence:       req.Evidence,
	}
	updateUser := func(u *auth_entities.User) bool {
		u.Suspensions = append(u.Suspensions, suspension)
		return true
	}
	_, err = userDAO.TryUpdate(ctx, user.Id, updateUser, DefaultRetry)
	if err != nil {
		return nil, err
	}

	// Log OCSF event for account suspension/locking
	if err := s.ocsfAuditLogger.LogAPIActivityWithTenant(
		auditocsf.NewAPIActivityEventBuilder(auditocsf.ActivityAPIUpdate).
			WithAPI(auditocsf.API{
				Operation: "CreateUserSuspension",
				Service: auditocsf.APIService{
					Name: "AuthService",
				},
			}).
			WithActor(auditocsf.Actor{
				User: &auditocsf.User{
					UID: authInfo.OpaqueUserID,
				},
			}).
			WithMessage(fmt.Sprintf("Account suspended for user '%s' - type: %s, reason: %s",
				req.UserId, req.SuspensionType.String(), req.Evidence)).
			Build(),
		req.TenantId,
	); err != nil {
		log.Ctx(ctx).Warn().Err(err).Msg("Failed to log OCSF audit event for user suspension")
	}

	tokensDeleted := 0
	if logoutUser {
		// Remove any tokens associated with user.
		tokensDeleted, err = deleteUserTokensForTenant(ctx, user.Id, req.TenantId, s.daoFactory)
		if err != nil {
			return nil, err
		}
	}

	return &authpb.CreateUserSuspensionResponse{
		SuspensionId:  suspensionId,
		TokensDeleted: int32(tokensDeleted),
	}, nil
}

func (s *AuthGrpcServer) DeleteUserSuspensions(ctx context.Context, req *authpb.DeleteUserSuspensionsRequest) (*authpb.DeleteUserSuspensionsResponse, error) {
	// Lift an existing user suspension
	log.Ctx(ctx).Info().Str("user_id", req.UserId).Msg("DeleteUserSuspensions request received")

	err := authCheck(ctx, req.TenantId, tokenscopesproto.Scope_AUTH_RW)
	if err != nil {
		return nil, err
	}

	authInfo, _ := auth.GetAugmentClaims(ctx)
	suspensionId := uuid.New().String()
	requestContext, _ := requestcontext.FromGrpcContext(ctx)
	s.auditLogger.WriteAuditLog(
		authInfo,
		fmt.Sprintf("Delete suspensions '%s' for user '%s'",
			suspensionId, req.UserId),
		audit.NewUser(req.UserId),
		audit.NewProtoRequest(req),
		requestContext,
	)

	userDAO := s.daoFactory.GetUserDAO()

	user, err := userDAO.Get(ctx, req.UserId)
	if err != nil {
		return nil, err
	}
	if user == nil {
		return nil, status.Error(codes.NotFound, "Resource not found")
	}

	// Create a map for quick lookup of suspension IDs to remove
	toRemove := make(map[string]bool)
	for _, id := range req.SuspensionIds {
		toRemove[id] = true
	}

	// Lift suspension by suspension ID
	suspensionsDeleted := 0
	updateUser := func(u *auth_entities.User) bool {
		// Filter suspensions, keeping only those not in the removal list
		newSuspensions := make([]*auth_entities.UserSuspension, 0, len(u.Suspensions))
		for _, s := range u.Suspensions {
			if !toRemove[s.SuspensionId] {
				newSuspensions = append(newSuspensions, s)
			}
		}
		suspensionsDeleted = len(u.Suspensions) - len(newSuspensions)
		u.Suspensions = newSuspensions
		return true
	}
	_, err = userDAO.TryUpdate(ctx, user.Id, updateUser, DefaultRetry)
	if err != nil {
		return nil, err
	}

	return &authpb.DeleteUserSuspensionsResponse{
		SuspensionsDeleted: int32(suspensionsDeleted),
	}, nil
}

func (s *AuthGrpcServer) UpdateSuspensionExemption(ctx context.Context, req *authpb.UpdateSuspensionExemptionRequest) (*authpb.UpdateSuspensionExemptionResponse, error) {
	// Update suspension exemption for a user
	log.Ctx(ctx).Info().Str("user_id", req.UserId).Msg("UpdateSuspensionExemption request received")

	err := authCheck(ctx, req.TenantId, tokenscopesproto.Scope_AUTH_RW)
	if err != nil {
		return nil, err
	}

	// If IAP User, ensure reason is provided
	err = requireReasonForIAPUser(ctx, req.Reason)
	if err != nil {
		log.Error().Err(err).Msg("Reason is required for IAP users")
		return nil, err
	}

	authInfo, _ := auth.GetAugmentClaims(ctx)
	requestContext, _ := requestcontext.FromGrpcContext(ctx)
	auditLogText := fmt.Sprintf("Update suspension exemption for user '%s' to %v", req.UserId, req.Exempt)
	if req.Reason != "" {
		auditLogText += fmt.Sprintf(". Reason: %s", req.Reason)
	}
	s.auditLogger.WriteAuditLog(
		authInfo,
		auditLogText,
		audit.NewUser(req.UserId),
		audit.NewProtoRequest(req),
		requestContext,
	)

	userDAO := s.daoFactory.GetUserDAO()

	user, err := userDAO.Get(ctx, req.UserId)
	if err != nil {
		return nil, err
	}
	if user == nil {
		return nil, status.Error(codes.NotFound, "Resource not found")
	}

	// Update suspension exemption
	updateUser := func(u *auth_entities.User) bool {
		u.SuspensionExempt = req.Exempt
		return true
	}
	_, err = userDAO.TryUpdate(ctx, user.Id, updateUser, DefaultRetry)
	if err != nil {
		return nil, err
	}

	return &authpb.UpdateSuspensionExemptionResponse{
		UserId:   req.UserId,
		TenantId: req.TenantId,
		Exempt:   req.Exempt,
	}, nil
}

// Remove self-serve accounts for users that are on team / enterprise plans already
func (s *AuthGrpcServer) RemoveSelfServeAccountsForTeam(ctx context.Context, req *authpb.RemoveSelfServeAccountsForTeamRequest) (*authpb.RemoveSelfServeAccountsForTeamResponse, error) {
	requestID := uuid.New().String()
	logger := log.With().Str("requestID", requestID).Logger()
	logger.Info().Msg("RemoveSelfServeAccountsForTeam request received")

	err := authCheck(ctx, "", tokenscopesproto.Scope_AUTH_RW)
	if err != nil {
		return nil, err
	}

	authClaims, ok := auth.GetAugmentClaims(ctx)
	if !ok {
		return nil, status.Error(codes.Unknown, "Invalid context")
	}
	requestContext, _ := requestcontext.FromGrpcContext(ctx)
	s.auditLogger.WriteAuditLog(
		authClaims,
		fmt.Sprintf("RemoveExtraSelfServeTenantsFromUsers request received"),
		audit.NewProtoRequest(req),
		requestContext,
	)

	resp := &authpb.RemoveSelfServeAccountsForTeamResponse{
		RemovedUsers: make([]*authpb.RemoveSelfServeAccountsForTeamResponse_UserRemovals, 0),
	}

	// Iterate through all users
	userDAO := s.daoFactory.GetUserDAO()
	failedUsers := make([]string, 0)

	var wg sync.WaitGroup
	var error error
	wg.Add(1)
	go func() {
		defer wg.Done()
		backgroundCtx := context.Background()
		if authClaims, ok := auth.GetAugmentClaims(ctx); ok {
			backgroundCtx = authClaims.NewContext(backgroundCtx)
		}

		// Process all users
		err := userDAO.FindAll(backgroundCtx, func(user *auth_entities.User) bool {
			// only process users in the specified tenant if provided
			// if no tenant is specified, process all users
			if req.TenantId != nil && *req.TenantId != "" && !slices.Contains(user.Tenants, *req.TenantId) {
				return true
			}

			// only return users with multiple tenants
			if len(user.Tenants) == 1 {
				return true
			}

			// check if any tenant is an enterprise or team tenant
			primaryTenants := make([]*authpb.RemoveSelfServeAccountsForTeamResponse_TenantInfo, 0)
			teamOrEnterpriseTenantIDs := make([]string, 0)
			for _, tenantID := range user.Tenants {

				// skip processing this user if any of their tenants are in the ignore list
				if slices.Contains(req.TenantIdsToIgnore, tenantID) {
					return true
				}
				tenant, err := s.getTenant(ctx, tenantID)
				if err != nil {
					logger.Error().Err(err).Msgf("Error getting tenant %s", tenantID)
					failedUsers = append(failedUsers, user.Id)
					return true
				}
				if tenant.DeletedAt != "" {
					continue
				}
				if tenantutil.IsEnterpriseTenant(tenant) || tenantutil.IsSelfServeTeamTenant(tenant) {
					teamOrEnterpriseTenantIDs = append(teamOrEnterpriseTenantIDs, tenantID)
					primaryTenants = append(primaryTenants, &authpb.RemoveSelfServeAccountsForTeamResponse_TenantInfo{
						TenantId:   tenantID,
						TenantName: tenant.Name,
					})
				}
			}

			if len(teamOrEnterpriseTenantIDs) == 0 {
				return true
			}

			logger.Info().Msgf("User %s is on team/enterprise tenant %v and has other tenants", user.Id, teamOrEnterpriseTenantIDs)

			// remove any tenant that is not an enterprise or team tenant
			tenantsToRemove := make([]*authpb.RemoveSelfServeAccountsForTeamResponse_TenantInfo, 0)
			for _, tenantID := range user.Tenants {
				if !slices.Contains(teamOrEnterpriseTenantIDs, tenantID) {
					tenant, err := s.getTenant(ctx, tenantID)
					if err != nil {
						logger.Error().Err(err).Msgf("Error getting tenant %s", tenantID)
						continue
					}
					logger.Info().Msgf("Would remove user %s from tenant %s (%s)", user.Id, tenantID, tenant.Name)
					tenantsToRemove = append(tenantsToRemove, &authpb.RemoveSelfServeAccountsForTeamResponse_TenantInfo{
						TenantId:   tenantID,
						TenantName: tenant.Name,
					})

					if !req.DryRun {
						// Remove the user from the tenant
						removeTenantReq := &authpb.RemoveUserFromTenantRequest{
							UserId:   user.Id,
							TenantId: tenantID,
						}
						_, err := s.removeUserFromTenant(backgroundCtx, removeTenantReq, nil)
						if err != nil {
							logger.Error().Err(err).Msgf("Error removing user %s from tenant %s", user.Id, tenantID)
							if !slices.Contains(failedUsers, user.Id) {
								failedUsers = append(failedUsers, user.Id)
							}
						} else {
							logger.Info().Msgf("Removed user %s from tenant %s", user.Id, tenantID)
						}
					}
				}
			}

			if len(tenantsToRemove) > 0 {
				resp.RemovedUsers = append(resp.RemovedUsers, &authpb.RemoveSelfServeAccountsForTeamResponse_UserRemovals{
					UserId:         user.Id,
					PrimaryTenants: primaryTenants,
					RemovedTenants: tenantsToRemove,
				})
			}
			return true
		})
		if err != nil {
			logger.Error().Err(err).Msg("Error iterating through users")
			error = err
		}

		logger.Info().Int("users processed", len(resp.RemovedUsers)).Int("failed_users", len(failedUsers)).Msg("RemoveSelfServeAccountsForTeam request completed")
	}()

	wg.Wait()

	if error != nil {
		logger.Error().Err(error).Msg("Error iterating through users")
		return nil, error
	}

	resp.FailedUsers = failedUsers

	return resp, nil
}

func (s *AuthGrpcServer) RemoveExtraSelfServeTenantsFromUsers(ctx context.Context, req *authpb.RemoveExtraSelfServeTenantsFromUsersRequest) (*authpb.RemoveExtraSelfServeTenantsFromUsersResponse, error) {
	requestID := uuid.New().String()
	logger := log.With().Str("requestID", requestID).Logger()

	logger.Info().Msg("RemoveExtraSelfServeTenantsFromUsers request received")

	err := authCheck(ctx, "", tokenscopesproto.Scope_AUTH_RW)
	if err != nil {
		return nil, err
	}

	authClaims, ok := auth.GetAugmentClaims(ctx)
	if !ok {
		return nil, status.Error(codes.Unknown, "Invalid context")
	}
	requestContext, _ := requestcontext.FromGrpcContext(ctx)
	s.auditLogger.WriteAuditLog(
		authClaims,
		fmt.Sprintf("RemoveExtraSelfServeTenantsFromUsers request received"),
		requestContext,
		audit.NewProtoRequest(req),
	)

	resp := &authpb.RemoveExtraSelfServeTenantsFromUsersResponse{
		RemovedUsers: make([]*authpb.RemoveExtraSelfServeTenantsFromUsersResponse_UserRemovals, 0),
	}

	checkNamespace := len(req.NamespaceIds) > 0 && !(len(req.NamespaceIds) == 1 && req.NamespaceIds[0] == "")

	// Iterate through all users
	userDAO := s.daoFactory.GetUserDAO()
	failedUsers := make([]string, 0)

	var wg sync.WaitGroup
	var error error
	wg.Add(1)
	go func() {
		defer wg.Done()
		backgroundCtx := context.Background()
		if authClaims, ok := auth.GetAugmentClaims(ctx); ok {
			backgroundCtx = authClaims.NewContext(backgroundCtx)
		}
		err := userDAO.FindAll(backgroundCtx, func(user *auth_entities.User) bool {
			userLog := logger.With().Str("userID", user.Id).Logger()

			// only process users with multiple tenants
			if len(user.Tenants) <= 1 {
				return true
			}

			primaryTenant := &authpb.RemoveExtraSelfServeTenantsFromUsersResponse_TenantInfo{}
			var primaryTenantTier tw_pb.TenantTier
			tenantsToRemove := make([]*authpb.RemoveExtraSelfServeTenantsFromUsersResponse_TenantInfo, 0)

			// Phase 1: Collect all tenant information
			userTenants := make([]*tw_pb.Tenant, 0, len(user.Tenants))
			for _, tenantID := range user.Tenants {
				tenant, err := s.getTenant(ctx, tenantID)
				if err != nil {
					userLog.Error().Err(err).Msgf("Error getting tenant %s", tenantID)
					failedUsers = append(failedUsers, user.Id)
					return true
				}
				if tenant.DeletedAt != "" {
					continue
				}
				userTenants = append(userTenants, tenant)
			}

			userLog.Info().Msgf("Got %d active tenants for user", len(userTenants))

			// Phase 2: Check if user is in specified namespace
			if checkNamespace {
				inRequestedNamespace := slices.ContainsFunc(userTenants, func(tenant *tw_pb.Tenant) bool {
					return slices.Contains(req.NamespaceIds, tenant.ShardNamespace)
				})
				if !inRequestedNamespace {
					return true
				}
			}

			// Phase 3: Check for enterprise or team tenants
			for _, tenant := range userTenants {
				if tenantutil.IsSelfServeTeamTenant(tenant) || tenantutil.IsEnterpriseTenant(tenant) {
					userLog.Info().Msgf("User is on enterprise or team tenant, skipping")
					return true
				}
			}

			userLog.Info().Msgf("User is in specified namespace and has no enterprise or team tenants, processing...")

			// Phase 4: Determine primary tenant and tenants to remove
			for i, tenant := range userTenants {
				if i == 0 {
					primaryTenant.TenantId = tenant.Id
					primaryTenant.TenantName = tenant.Name
					primaryTenantTier = tenant.Tier
				} else {
					if tenant.Tier != primaryTenantTier {
						userLog.Warn().Msgf("User %s is on tenants with different tiers (%s vs %s). Skipping.",
							user.Id, primaryTenantTier, tenant.Tier)
						return true
					}
					tenantsToRemove = append(tenantsToRemove, &authpb.RemoveExtraSelfServeTenantsFromUsersResponse_TenantInfo{
						TenantId:   tenant.Id,
						TenantName: tenant.Name,
					})
				}
			}

			if primaryTenant.TenantId == "" {
				userLog.Info().Msgf("User %s is not on any tenants. Skipping.", user.Id)
				return true
			}

			if len(tenantsToRemove) == 0 {
				return true
			}

			// Phase 5: Remove the user from the unnecessary tenants

			userLog.Info().Msgf("User %s is on primary tenant %s and has other tenants %v", user.Id, primaryTenant.TenantName, tenantsToRemove)

			if req.MakeChanges {
				// Remove the user from the unnecessary tenants
				for _, tenantToRemove := range tenantsToRemove {
					userLog.Info().Msgf("Removing user %s from tenant %s (%s)", user.Id, tenantToRemove.TenantId, tenantToRemove.TenantName)
					removeTenantReq := &authpb.RemoveUserFromTenantRequest{
						UserId:   user.Id,
						TenantId: tenantToRemove.TenantId,
					}
					_, err := s.removeUserFromTenant(backgroundCtx, removeTenantReq, nil)
					if err != nil {
						if !slices.Contains(failedUsers, user.Id) {
							failedUsers = append(failedUsers, user.Id)
						}
						userLog.Error().Err(err).Msgf("Error removing user %s from tenant %s", user.Id, tenantToRemove.TenantId)
					} else {
						userLog.Info().Msgf("Removed user %s from tenant %s", user.Id, tenantToRemove.TenantId)
					}
				}
			} else {
				userLog.Info().Msgf("[dry-run] Would remove user %s from tenants %v", user.Id, tenantsToRemove)
			}

			resp.RemovedUsers = append(resp.RemovedUsers, &authpb.RemoveExtraSelfServeTenantsFromUsersResponse_UserRemovals{
				UserId:         user.Id,
				PrimaryTenant:  primaryTenant,
				RemovedTenants: tenantsToRemove,
			})
			return true
		})
		if err != nil {
			logger.Error().Err(err).Msg("Error iterating through users")
			error = err
		}

		logger.Info().Int("users processed", len(resp.RemovedUsers)).Int("failed_users", len(failedUsers)).Msg("RemoveExtraSelfServeTenantsFromUsers request completed")
	}()
	wg.Wait()

	if error != nil {
		logger.Error().Err(error).Msg("Error iterating through users")
		return nil, error
	}

	resp.FailedUsers = failedUsers

	return resp, nil
}

func (s *AuthGrpcServer) RemoveDeletedTenantsFromUsers(ctx context.Context, req *authpb.RemoveDeletedTenantsFromUsersRequest) (*authpb.RemoveDeletedTenantsFromUsersResponse, error) {
	requestID := uuid.New().String()
	logger := log.With().Str("requestID", requestID).Logger()

	logger.Info().Msg("RemoveDeletedTenantsFromUsers request received")

	err := authCheck(ctx, "", tokenscopesproto.Scope_AUTH_RW)
	if err != nil {
		return nil, err
	}

	authClaims, ok := auth.GetAugmentClaims(ctx)
	if !ok {
		return nil, status.Error(codes.Unknown, "Invalid context")
	}
	requestContext, _ := requestcontext.FromGrpcContext(ctx)
	s.auditLogger.WriteAuditLog(
		authClaims,
		fmt.Sprintf("RemoveDeletedTenantsFromUsers request received"),
		requestContext,
		audit.NewProtoRequest(req),
	)

	resp := &authpb.RemoveDeletedTenantsFromUsersResponse{
		RemovedUsers: make([]*authpb.RemoveDeletedTenantsFromUsersResponse_UserRemovals, 0),
	}

	// Iterate through all users
	userDAO := s.daoFactory.GetUserDAO()
	failedUsers := make([]string, 0)

	var wg sync.WaitGroup
	var error error
	wg.Add(1)
	go func() {
		defer wg.Done()
		backgroundCtx := context.Background()
		if authClaims, ok := auth.GetAugmentClaims(ctx); ok {
			backgroundCtx = authClaims.NewContext(backgroundCtx)
		}

		err = userDAO.FindAll(backgroundCtx, func(user *auth_entities.User) bool {
			userLog := logger.With().Str("userID", user.Id).Logger()
			tenantsToRemove := make([]*authpb.RemoveDeletedTenantsFromUsersResponse_TenantInfo, 0)

			for _, tenantID := range user.Tenants {
				tenant, err := s.tenantMap.GetTenantByIdDeletedOk(tenantID)
				if err != nil {
					userLog.Error().Err(err).Msgf("Error getting tenant %s", tenantID)
					failedUsers = append(failedUsers, user.Id)
					return true
				}

				// Skip active tenants
				if tenant != nil && tenant.DeletedAt == "" {
					continue
				}

				// Add deleted tenant to removal list
				tenantInfo := &authpb.RemoveDeletedTenantsFromUsersResponse_TenantInfo{
					TenantId: tenantID,
				}
				if tenant != nil {
					tenantInfo.TenantName = tenant.Name
				}
				tenantsToRemove = append(tenantsToRemove, tenantInfo)
			}

			if len(tenantsToRemove) == 0 {
				return true
			}

			userLog.Info().Msgf("User %s is on deleted tenants %v", user.Id, tenantsToRemove)

			if req.MakeChanges {
				// Remove the user from the tenant
				for _, tenantToRemove := range tenantsToRemove {
					userLog.Info().Msgf("Removing user %s from tenant %s (%s)", user.Id, tenantToRemove.TenantId, tenantToRemove.TenantName)
					removeTenantReq := &authpb.RemoveUserFromTenantRequest{
						UserId:   user.Id,
						TenantId: tenantToRemove.TenantId,
					}
					_, err := s.removeUserFromTenant(backgroundCtx, removeTenantReq, req.TenantMappingsForDeletedTenants)
					if err != nil {
						if !slices.Contains(failedUsers, user.Id) {
							failedUsers = append(failedUsers, user.Id)
						}
						userLog.Error().Err(err).Msgf("Error removing user %s from tenant %s", user.Id, tenantToRemove.TenantId)
					} else {
						userLog.Info().Msgf("Removed user %s from tenant %s", user.Id, tenantToRemove.TenantId)
					}
				}
			} else {
				userLog.Info().Msgf("[dry-run] Would remove user %s from tenants %v", user.Id, tenantsToRemove)
			}

			resp.RemovedUsers = append(resp.RemovedUsers, &authpb.RemoveDeletedTenantsFromUsersResponse_UserRemovals{
				UserId:         user.Id,
				RemovedTenants: tenantsToRemove,
			})
			return true
		})
		if err != nil {
			log.Ctx(ctx).Error().Err(err).Msg("Error iterating through users")
			error = err
		}

		logger.Info().Int("users processed", len(resp.RemovedUsers)).Int("failed_users", len(failedUsers)).Msg("RemoveDeletedTenantsFromUsers request completed")
	}()
	wg.Wait()

	if error != nil {
		log.Ctx(ctx).Error().Err(error).Msg("Error iterating through users")
		return nil, error
	}
	resp.FailedUsers = failedUsers

	return resp, nil
}

func (s *AuthGrpcServer) DeleteTenantSubscriptionMapping(ctx context.Context, req *authpb.DeleteTenantSubscriptionMappingRequest) (*authpb.DeleteTenantSubscriptionMappingResponse, error) {
	// Validate tenant_id is provided
	if req.TenantId == "" {
		return nil, status.Error(codes.Internal, "tenant_id is required")
	}

	requestContext, err := requestcontext.FromGrpcContext(ctx)
	if err != nil {
		return nil, err
	}
	ctx = requestContext.AnnotateLogContext(ctx)

	authInfo, ok := auth.GetAugmentClaims(ctx)
	if !ok {
		return nil, status.Error(codes.Unauthenticated, "Unauthenticated")
	}
	ctx = authInfo.AnnotateLogContext(ctx)

	log.Ctx(ctx).Info().Str("tenant_id", req.TenantId).Msg("DeleteTenantSubscriptionMapping request received")

	// Auth check - require AUTH_RW scope
	err = authCheck(ctx, req.TenantId, tokenscopesproto.Scope_AUTH_RW)
	if err != nil {
		return nil, err
	}

	// Get auth claims for audit logging
	authClaims, ok := auth.GetAugmentClaims(ctx)
	if !ok {
		return nil, status.Error(codes.Unknown, "Invalid context")
	}
	s.auditLogger.WriteAuditLog(
		authClaims,
		fmt.Sprintf("DeleteTenantSubscriptionMapping request received for tenant_id: %s", req.TenantId),
		requestContext,
		audit.NewTenantID(req.TenantId),
		audit.NewProtoRequest(req),
	)

	// Delete the tenant subscription mapping
	tenantSubscriptionMappingDAO := s.daoFactory.GetTenantSubscriptionMappingDAO()
	err = tenantSubscriptionMappingDAO.Delete(ctx, req.TenantId)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("tenant_id", req.TenantId).Msg("Failed to delete tenant subscription mapping")
		return nil, status.Error(codes.Internal, "Failed to delete tenant subscription mapping")
	}

	log.Ctx(ctx).Info().Str("tenant_id", req.TenantId).Msg("DeleteTenantSubscriptionMapping request completed successfully")

	return &authpb.DeleteTenantSubscriptionMappingResponse{}, nil
}

func (s *AuthGrpcServer) UpdateSubscriptionOwnerToTeam(ctx context.Context, req *authpb.UpdateSubscriptionOwnerToTeamRequest) (*authpb.UpdateSubscriptionOwnerToTeamResponse, error) {
	requestContext, err := requestcontext.FromGrpcContext(ctx)
	if err != nil {
		return nil, err
	}
	ctx = requestContext.AnnotateLogContext(ctx)

	authInfo, ok := auth.GetAugmentClaims(ctx)
	if !ok {
		return nil, status.Error(codes.Unauthenticated, "Unauthenticated")
	}
	ctx = authInfo.AnnotateLogContext(ctx)

	// Auth check - require AUTH_RW scope for all tenants
	err = authCheck(ctx, "", tokenscopesproto.Scope_AUTH_RW)
	if err != nil {
		return nil, err
	}

	log.Ctx(ctx).Info().Strs("subscription_ids", req.SubscriptionIds).Msg("UpdateSubscriptionOwnerToTeam request received")

	// Process each subscription ID
	results := make([]*authpb.UpdateSubscriptionOwnerToTeamResponse_SubscriptionUpdateResult, 0, len(req.SubscriptionIds))

	for _, subscriptionId := range req.SubscriptionIds {
		result := s.updateSingleSubscriptionOwnerToTeam(ctx, subscriptionId)
		results = append(results, result)
	}

	log.Ctx(ctx).Info().Int("total_subscriptions", len(req.SubscriptionIds)).Msg("UpdateSubscriptionOwnerToTeam request completed")

	return &authpb.UpdateSubscriptionOwnerToTeamResponse{
		Results: results,
	}, nil
}

// Helper function to update a single subscription
func (s *AuthGrpcServer) updateSingleSubscriptionOwnerToTeam(ctx context.Context, subscriptionId string) *authpb.UpdateSubscriptionOwnerToTeamResponse_SubscriptionUpdateResult {
	result := &authpb.UpdateSubscriptionOwnerToTeamResponse_SubscriptionUpdateResult{
		SubscriptionId: subscriptionId,
		Success:        false,
	}

	// Get the subscription first to check its current owner
	subscriptionDAO := s.daoFactory.GetSubscriptionDAO()
	subscription, err := subscriptionDAO.Get(ctx, subscriptionId)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("subscription_id", subscriptionId).Msg("Failed to get subscription")
		result.ErrorMessage = "Failed to get subscription"
		return result
	}
	if subscription == nil {
		log.Ctx(ctx).Error().Str("subscription_id", subscriptionId).Msg("Subscription not found")
		result.ErrorMessage = "Subscription not found"
		return result
	}

	// Verify the owner field of the subscription is a user not a tenant
	userOwner, ok := subscription.Owner.(*auth_entities.Subscription_UserId)
	if !ok {
		log.Ctx(ctx).Error().Str("subscription_id", subscriptionId).Msg("Subscription owner is not a user")
		result.ErrorMessage = "Subscription owner is not a user"
		return result
	}

	userID := userOwner.UserId
	log.Ctx(ctx).Info().Str("user_id", userID).Str("subscription_id", subscriptionId).Msg("Found user-owned subscription")

	// Get the user to retrieve their tenant information
	userDAO := s.daoFactory.GetUserDAO()
	user, err := userDAO.Get(ctx, userID)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("user_id", userID).Msg("Failed to get user")
		result.ErrorMessage = "Failed to get user"
		return result
	}
	if user == nil {
		log.Ctx(ctx).Error().Str("user_id", userID).Msg("User not found")
		result.ErrorMessage = "User not found"
		return result
	}

	// Verify user has only 1 tenant
	if len(user.Tenants) != 1 {
		log.Ctx(ctx).Error().Str("user_id", userID).Int("tenant_count", len(user.Tenants)).Msg("User does not belong to exactly one tenant")
		result.ErrorMessage = "User must belong to exactly one tenant"
		return result
	}

	tenantID := user.Tenants[0]
	log.Ctx(ctx).Info().Str("tenant_id", tenantID).Str("user_id", userID).Msg("Found user's tenant")

	// Get the tenant to verify it's a self-serve team
	tenant, err := s.getTenant(ctx, tenantID)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("tenant_id", tenantID).Msg("Failed to get tenant")
		result.ErrorMessage = "Failed to get tenant"
		return result
	}
	if tenant == nil {
		log.Ctx(ctx).Error().Str("tenant_id", tenantID).Msg("Tenant not found")
		result.ErrorMessage = "Tenant not found"
		return result
	}

	// Verify tenantutil.isSelfServeTeam is true for the tenant
	if !tenantutil.IsSelfServeTeamTenant(tenant) {
		log.Ctx(ctx).Error().Str("tenant_id", tenantID).Msg("Tenant is not a self-serve team")
		result.ErrorMessage = "Tenant must be a self-serve team"
		return result
	}

	log.Ctx(ctx).Info().Str("tenant_id", tenantID).Msg("Verified tenant is a self-serve team")

	// Get auth claims for audit logging
	authClaims, ok := auth.GetAugmentClaims(ctx)
	requestContext, _ := requestcontext.FromGrpcContext(ctx)
	s.auditLogger.WriteAuditLog(
		authClaims,
		fmt.Sprintf("UpdateSubscriptionOwnerToTeam request received for subscription_id: %s, changing owner from user %s to tenant %s", subscriptionId, userID, tenantID),
		audit.NewTenantName(tenant.Name),
		audit.NewTenantID(tenantID),
		audit.NewUser(userID),
		requestContext,
	)

	// Update the subscription owner to be the tenant
	_, err = subscriptionDAO.TryUpdate(ctx, subscriptionId, func(sub *auth_entities.Subscription) bool {
		sub.Owner = &auth_entities.Subscription_TenantId{TenantId: tenantID}
		return true
	}, DefaultRetry)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("subscription_id", subscriptionId).Msg("Failed to update subscription owner")
		result.ErrorMessage = "Failed to update subscription owner"
		return result
	}

	log.Ctx(ctx).Info().Str("subscription_id", subscriptionId).Str("tenant_id", tenantID).Msg("UpdateSubscriptionOwnerToTeam completed successfully for subscription")

	result.Success = true
	return result
}

func (s *AuthGrpcServer) SuspensionCleanup(ctx context.Context, req *authpb.SuspensionCleanupRequest) (*authpb.SuspensionCleanupResponse, error) {
	log.Ctx(ctx).Info().Msg("SuspensionCleanup request received: " + req.String())

	// Check permissions
	authClaims, ok := auth.GetAugmentClaims(ctx)
	if !ok {
		return nil, status.Error(codes.Unknown, "Invalid context")
	}
	if !authClaims.HasScope(tokenscopesproto.Scope_AUTH_RW) {
		return nil, status.Error(codes.PermissionDenied, "Insufficient permissions")
	}

	requestContext, _ := requestcontext.FromGrpcContext(ctx)
	s.auditLogger.WriteAuditLog(
		authClaims,
		"SuspensionCleanup",
		audit.NewProtoRequest(req),
		requestContext,
	)

	var evidenceRegex *regexp.Regexp
	if req.EvidenceRegex != "" {
		var err error
		evidenceRegex, err = regexp.Compile(req.EvidenceRegex)
		if err != nil {
			return nil, status.Error(codes.InvalidArgument, fmt.Sprintf("Invalid evidence regex: %v", err))
		}
	}

	// Create a wait group to wait for background processing
	var wg sync.WaitGroup
	wg.Add(1)
	suspensionsRemoved := 0
	suspensionsDeduped := 0
	updatesFailed := 0

	// perform processing in the background.
	go func() {
		defer wg.Done()
		ctx := context.Background()
		userDAO := s.daoFactory.GetUserDAO()

		// Process all users
		err := userDAO.FindAll(ctx, func(user *auth_entities.User) bool {
			if len(user.Suspensions) == 0 {
				return true // no suspensions
			}

			removeSuspension := false
			dedupSuspension := false
			_, err := userDAO.TryUpdate(ctx, user.Id, func(u *auth_entities.User) bool {
				removeSuspension = false
				dedupSuspension = false
				suspensionsModified := false
				newSuspensions := make([]*auth_entities.UserSuspension, 0, len(u.Suspensions))
				seenSuspensionTypes := make(map[auth_entities.UserSuspensionType]bool)
				for _, s := range u.Suspensions {
					evidenceMatches := evidenceRegex == nil || evidenceRegex.MatchString(s.Evidence)

					if slices.Contains(req.RemoveSuspensionTypes, s.SuspensionType) && evidenceMatches {
						suspensionsModified = true
						removeSuspension = true
						continue // remove
					}
					if slices.Contains(req.DedupSuspensionTypes, s.SuspensionType) && evidenceMatches {
						if seenSuspensionTypes[s.SuspensionType] {
							suspensionsModified = true
							dedupSuspension = true
							continue // already seen this type, dedup
						}
						seenSuspensionTypes[s.SuspensionType] = true
					}
					newSuspensions = append(newSuspensions, s)
				}

				u.Suspensions = newSuspensions
				if suspensionsModified {
					log.Ctx(ctx).Info().Str("user_id", u.Id).Msg("SuspensionCleanup: Modified suspensions")
				}
				return suspensionsModified
			}, DefaultRetry)
			if err != nil {
				updatesFailed++
				log.Ctx(ctx).Warn().Err(err).Str("user_id", user.Id).Msg("SuspensionCleanup: Failed to update suspensions")
			}
			if removeSuspension {
				suspensionsRemoved++
			}
			if dedupSuspension {
				suspensionsDeduped++
			}
			return true
		})
		if err != nil {
			log.Ctx(ctx).Error().Err(err).Msg("SuspensionCleanup: Failed to fetch users")
		}
		log.Ctx(ctx).Info().Int("suspensions_removed", suspensionsRemoved).Int("suspensions_deduped", suspensionsDeduped).Msg("SuspensionCleanup: Completed")
	}()

	// Wait for background processing to complete
	wg.Wait()

	return &authpb.SuspensionCleanupResponse{
		SuspensionsRemoved: int32(suspensionsRemoved),
		SuspensionsDeduped: int32(suspensionsDeduped),
		UpdatesFailed:      int32(updatesFailed),
	}, nil
}

func (s *AuthGrpcServer) MergeDuplicateAccounts(ctx context.Context, req *authpb.MergeDuplicateAccountsRequest) (*authpb.MergeDuplicateAccountsResponse, error) {
	requestID := uuid.New().String()
	logger := log.With().Str("requestID", requestID).Logger()

	logger.Info().Bool("dry_run", req.DryRun).Int("user_count", len(req.UserIds)).Msg("MergeDuplicateAccounts request received")

	err := authCheck(ctx, "", tokenscopesproto.Scope_AUTH_ADMIN)
	if err != nil {
		return nil, err
	}

	authClaims, ok := auth.GetAugmentClaims(ctx)
	if !ok {
		return nil, status.Error(codes.Unknown, "Invalid context")
	}
	requestContext, _ := requestcontext.FromGrpcContext(ctx)

	for _, userID := range req.UserIds {
		s.auditLogger.WriteAuditLog(
			authClaims,
			fmt.Sprintf("MergeDuplicateAccounts request received for %d users", len(req.UserIds)),
			audit.NewUser(userID),
			audit.NewProtoRequest(req),
			requestContext,
		)
	}

	// Validate input
	if len(req.UserIds) < 2 {
		return nil, status.Error(codes.InvalidArgument, "At least 2 user IDs are required for merging")
	}

	userDAO := s.daoFactory.GetUserDAO()
	var failedUsers []string
	var merge *authpb.UserMerge
	var mergeError error

	// Create a wait group to wait for background processing
	var wg sync.WaitGroup
	wg.Add(1)

	go func() {
		defer wg.Done()
		backgroundCtx := context.Background()
		if authClaims, ok := auth.GetAugmentClaims(ctx); ok {
			backgroundCtx = authClaims.NewContext(backgroundCtx)
		}

		// Fetch all users to be merged
		users := make([]*auth_entities.User, 0, len(req.UserIds))
		for _, userID := range req.UserIds {
			user, err := userDAO.Get(backgroundCtx, userID)
			if err != nil {
				logger.Error().Err(err).Str("user_id", userID).Msg("Failed to fetch user")
				failedUsers = append(failedUsers, userID)
				continue
			}
			if user == nil {
				logger.Error().Str("user_id", userID).Msg("User not found")
				failedUsers = append(failedUsers, userID)
				continue
			}
			if len(user.Tenants) == 0 {
				logger.Error().Str("user_id", userID).Msg("User is not in any tenant, skipping")
				continue
			}
			users = append(users, user)
		}

		// If we don't have at least 2 valid users, return early
		if len(users) < 2 {
			return
		}

		// Create a string of all user IDs for logging
		allUserIds := strings.Join(req.UserIds, ",")
		logger = logger.With().Str("user_ids_to_merge", allUserIds).Logger()

		// Create merge info for processing
		var userToTenantMap map[string]string
		merge, userToTenantMap, err = s.createMergeInfo(backgroundCtx, users, logger)
		if err != nil {
			logger.Error().Err(err).Msg("Failed to create merge info")
			// Add all user IDs to failed users if merge info creation fails
			for _, user := range users {
				if !slices.Contains(failedUsers, user.Id) {
					failedUsers = append(failedUsers, user.Id)
				}
			}
			return
		}

		// Log the merge info
		mergeJson, err := json.Marshal(merge)
		if err != nil {
			logger.Error().Err(err).Msg("Failed to marshal merge info")
		} else {
			logger.Info().RawJSON("merge", mergeJson).Msg("Merge info")
		}

		if !req.DryRun {
			// Actually perform the merge
			if err := s.executeMerge(backgroundCtx, merge, userToTenantMap, logger); err != nil {
				mergeError = err
				logger.Error().Err(err).Msg("Failed to execute merge")
				// Add all user IDs to failed users if merge execution fails
				for _, user := range users {
					if !slices.Contains(failedUsers, user.Id) {
						failedUsers = append(failedUsers, user.Id)
					}
				}
				return
			}
			logger.Info().Int("count", len(users)).Msg("Merged duplicate users")
		} else {
			logger.Info().Int("count", len(users)).Str("primary_user", merge.PrimaryUserId).Msg("[dry-run] Would merge duplicate users")
		}
	}()

	// Wait for background processing to complete
	wg.Wait()
	if mergeError != nil {
		return nil, mergeError
	}

	return &authpb.MergeDuplicateAccountsResponse{
		UserMerge:   merge,
		FailedUsers: failedUsers,
	}, nil
}

type userInfo struct {
	user                     *auth_entities.User
	hasActiveOrbSubscription bool
	planPriority             int
	tenant                   *tw_pb.Tenant
}

func (s *AuthGrpcServer) createMergeInfo(ctx context.Context, users []*auth_entities.User, logger zerolog.Logger) (*authpb.UserMerge, map[string]string, error) {
	// Sort the users by ID
	sort.Slice(users, func(i, j int) bool {
		return users[i].Id < users[j].Id
	})

	// The target user we want to use is the one with the lowest id
	targetUser := users[0]
	targetUserEmail, err := normalizeEmail(targetUser.Email)
	if err != nil {
		return nil, nil, err
	}

	// Fetch subscription info for all users
	userInfos := make([]*userInfo, 0, len(users))
	hasSubscriptions := false
	for _, user := range users {
		userInfo := &userInfo{
			user:                     user,
			hasActiveOrbSubscription: false,
			planPriority:             0,
		}
		if len(user.Tenants) == 0 {
			logger.Warn().Str("user_id", user.Id).Msg("User has no tenant")
		} else {
			tenant, err := s.tenantMap.GetTenantByIdDeletedOk(user.Tenants[0])
			if err != nil {
				logger.Error().Err(err).Str("user_id", user.Id).Msg("Failed to fetch tenant")
				return nil, nil, err
			}
			if tenant != nil {
				userInfo.tenant = tenant
			}
		}
		// Check if the user has an orb subscription
		billingInfo, err := GetUserBillingInfo(ctx, user.Id, userInfo.tenant.Id, s.daoFactory, s.tenantMap)
		if err != nil {
			logger.Error().Err(err).Str("user_id", user.Id).Msg("Failed to get billing info")
			return nil, nil, err
		}
		if billingInfo.OrbSubscriptionID == "" {
			logger.Info().Str("user_id", user.Id).Msg("User has no subscription")
			userInfos = append(userInfos, userInfo)
			continue
		}
		subscription, err := s.orbClient.GetUserSubscription(ctx, billingInfo.OrbSubscriptionID, nil)
		if err != nil {
			logger.Error().Err(err).Str("user_id", user.Id).Msg("Failed to fetch subscription from Orb")
			return nil, nil, err
		}
		if subscription == nil {
			logger.Error().Str("user_id", user.Id).Msg("Failed to fetch subscription")
			return nil, nil, status.Error(codes.Internal, "Failed to fetch subscription")
		}
		userInfo.hasActiveOrbSubscription = subscription != nil && parseOrbStatus(subscription.OrbStatus) == auth_entities.Subscription_ORB_STATUS_ACTIVE
		userInfo.planPriority = s.getPlanPriority(subscription.ExternalPlanID)
		hasSubscriptions = true
		userInfos = append(userInfos, userInfo)

		logger.Info().Str("user_id", user.Id).Bool("has_active_subscription", userInfo.hasActiveOrbSubscription).Int("plan_priority", userInfo.planPriority).Msgf("Fetched subscription info with subscription id %s customer id %s", user.OrbSubscriptionId, user.OrbCustomerId)
	}

	var userWithBestSubscription *auth_entities.User
	if !hasSubscriptions {
		// No subscriptions - return the first user
		userWithBestSubscription = targetUser
	} else {
		userWithBestSubscription, err = s.selectUserWithBestSubscription(ctx, userInfos, logger)
		if err != nil {
			return nil, nil, err
		}
	}

	if targetUser.Id != userWithBestSubscription.Id {
		logger.Info().Str("primary_user_id", targetUser.Id).Str("subscription_source_user_id", userWithBestSubscription.Id).Msg("Subscription info will be moved to primary user")
	}

	merge := &authpb.UserMerge{
		PrimaryUserId:              targetUser.Id,
		Email:                      targetUserEmail,
		MergedUserIds:              make([]string, 0),
		PrimaryUserTenant:          make(map[string]string),
		MergedUserTenants:          make(map[string]string),
		SubscriptionSourceUserId:   userWithBestSubscription.Id,
		PreservedOrbCustomerId:     userWithBestSubscription.OrbCustomerId,
		PreservedOrbSubscriptionId: userWithBestSubscription.OrbSubscriptionId,
		PreservedStripeCustomerId:  userWithBestSubscription.StripeCustomerId,
		MergedUserSubscriptionIds:  make([]string, 0),
		CanceledSubscriptionIds:    make([]string, 0),
		IdpUserIds:                 make([]string, 0),
	}

	// Store user ID to tenant ID mapping for efficient lookup
	userToTenantMap := make(map[string]string)

	// Store IDP user IDs to avoid duplicates
	idpUserIdsSet := make(map[string]bool)

	// Store emails to avoid duplicates
	emailsSet := make(map[string]bool)

	// Collect info from users to be merged
	for _, userInfo := range userInfos {
		logger.Info().Str("user_id", userInfo.user.Id).Msg("Collecting info from user to be merged")

		if userInfo.user.Id != targetUser.Id {
			merge.MergedUserIds = append(merge.MergedUserIds, userInfo.user.Id)
			logger.Info().Str("user_id", userInfo.user.Id).Msg("Added user to merged users")
		}

		// Collect IDP user IDs - avoid duplicates
		for _, idpUserId := range userInfo.user.IdpUserIds {
			if _, ok := idpUserIdsSet[idpUserId]; !ok {
				idpUserIdsSet[idpUserId] = true
				merge.IdpUserIds = append(merge.IdpUserIds, idpUserId)
			}
		}
		// Collect emails - avoid duplicates
		if _, ok := emailsSet[userInfo.user.Email]; !ok {
			emailsSet[userInfo.user.Email] = true
			merge.MergedEmails = append(merge.MergedEmails, userInfo.user.Email)
		}

		// Collect tenant info
		if len(userInfo.user.Tenants) > 1 {
			logger.Error().Str("user_id", userInfo.user.Id).Int("tenant_count", len(userInfo.user.Tenants)).Msg("User is in multiple tenants")
			return nil, nil, status.Error(codes.Internal, "User is in multiple tenants")
		}
		// Store the mapping
		userToTenantMap[userInfo.user.Id] = userInfo.user.Tenants[0]

		tenant, err := s.getTenant(ctx, userInfo.user.Tenants[0])
		if err != nil {
			return nil, nil, err
		}

		if userInfo.user.Id == userWithBestSubscription.Id {
			merge.PrimaryUserTenant[userInfo.user.Tenants[0]] = tenant.Name
		} else {
			merge.MergedUserTenants[userInfo.user.Tenants[0]] = tenant.Name

			if userInfo.user.OrbSubscriptionId != userWithBestSubscription.OrbSubscriptionId {
				// Collect subscription info
				if userInfo.user.OrbSubscriptionId != "" {
					merge.MergedUserSubscriptionIds = append(merge.MergedUserSubscriptionIds, userInfo.user.OrbSubscriptionId)
				}

				if userInfo.hasActiveOrbSubscription {
					merge.CanceledSubscriptionIds = append(merge.CanceledSubscriptionIds, userInfo.user.OrbSubscriptionId)
					logger.Info().Str("user_id", userInfo.user.Id).Str("subscription_id", userInfo.user.OrbSubscriptionId).Msg("Added subscription to be canceled")
				}
			}
		}
	}

	return merge, userToTenantMap, nil
}

func (s *AuthGrpcServer) selectUserWithBestSubscription(ctx context.Context, userInfos []*userInfo, logger zerolog.Logger) (*auth_entities.User, error) {
	logger.Info().Int("user_count", len(userInfos)).Msg("Selecting user with best subscription")

	// Sort by priority: non-nil tenant > tenant tier > active status > plan type > payment method > user id
	sort.Slice(userInfos, func(i, j int) bool {
		a, b := userInfos[i], userInfos[j]

		// First priority: non-nil tenant beats nil tenant
		if (a.tenant == nil) != (b.tenant == nil) {
			return a.tenant != nil
		}

		// Second priority: tenant tier (enterprise > self-serve > other)
		aIsEnterprise := a.tenant != nil && tenantutil.IsEnterpriseTenant(a.tenant)
		bIsEnterprise := b.tenant != nil && tenantutil.IsEnterpriseTenant(b.tenant)
		if aIsEnterprise != bIsEnterprise {
			return aIsEnterprise
		}

		aIsSelfServeTeam := a.tenant != nil && tenantutil.IsSelfServeTeamTenant(a.tenant)
		bIsSelfServeTeam := b.tenant != nil && tenantutil.IsSelfServeTeamTenant(b.tenant)
		if aIsSelfServeTeam != bIsSelfServeTeam {
			return aIsSelfServeTeam
		}

		// Third priority: active status (active beats inactive)
		if a.hasActiveOrbSubscription != b.hasActiveOrbSubscription {
			return a.hasActiveOrbSubscription
		}

		// Fourth priority: plan type (developer > community > trial)
		if a.planPriority != b.planPriority {
			return a.planPriority > b.planPriority
		}

		// Tiebreaker: prefer user with lower id
		return a.user.Id < b.user.Id
	})

	return userInfos[0].user, nil
}

func (s *AuthGrpcServer) getPlanPriority(externalPlanID string) int {
	// Priority: developer > community > trial
	switch externalPlanID {
	case "orb_developer_plan": // developer plan
		return 4
	case s.orbConfig.GetCommunityPlan().ID:
		return 3
	case s.orbConfig.GetTrialPlan().ID:
		return 2
	default:
		return 1
	}
}

func (s *AuthGrpcServer) executeMerge(ctx context.Context, merge *authpb.UserMerge, userToTenantMap map[string]string, logger zerolog.Logger) error {
	logger.Info().Str("primary_user_id", merge.PrimaryUserId).Msg("Executing user merge")

	authClaims, _ := auth.GetAugmentClaims(ctx)
	requestContext, _ := requestcontext.FromGrpcContext(ctx)

	for _, userId := range merge.MergedUserIds {
		s.auditLogger.WriteAuditLog(
			authClaims,
			fmt.Sprintf("Merging user %s into user %s", userId, merge.PrimaryUserId),
			audit.NewUser(userId),
			audit.NewProtoRequest(merge),
			requestContext,
		)
	}

	s.auditLogger.WriteAuditLog(
		authClaims,
		fmt.Sprintf("Merging users %v into user %s", merge.MergedUserIds, merge.PrimaryUserId),
		audit.NewUser(merge.PrimaryUserId),
		audit.NewProtoRequest(merge),
		requestContext,
	)

	userDAO := s.daoFactory.GetUserDAO()

	// Set new primary user info
	_, err := userDAO.TryUpdate(ctx, merge.PrimaryUserId, func(u *auth_entities.User) bool {
		if merge.Email != "" {
			u.Email = merge.Email
		}
		u.StripeCustomerId = merge.PreservedStripeCustomerId
		u.OrbCustomerId = merge.PreservedOrbCustomerId
		u.OrbSubscriptionId = merge.PreservedOrbSubscriptionId
		u.IdpUserIds = merge.IdpUserIds
		return true
	}, DefaultRetry)
	if err != nil {
		logger.Error().Err(err).Str("user_id", merge.PrimaryUserId).Msg("Failed to update primary user info")
		return err
	}

	logger.Info().Str("user_id", merge.PrimaryUserId).Msgf("Updated primary user info to preserve subscription id %s customer id %s", merge.PreservedOrbSubscriptionId, merge.PreservedOrbCustomerId)

	if len(merge.PrimaryUserTenant) != 1 {
		return fmt.Errorf("Failed to find primary tenant for user %s", merge.PrimaryUserId)
	}

	// Get the primary tenant ID
	var primaryTenantId string
	for tenantId := range merge.PrimaryUserTenant {
		primaryTenantId = tenantId
		break
	}

	// Move user to new primary tenant
	if userToTenantMap[merge.PrimaryUserId] != primaryTenantId {
		logger.Info().Str("user_id", merge.PrimaryUserId).Str("old_tenant", userToTenantMap[merge.PrimaryUserId]).Str("new_tenant", primaryTenantId).Msg("Moving user to new primary tenant")
		if err := s.tenantMap.MoveUserToTenant(ctx, merge.PrimaryUserId, primaryTenantId); err != nil {
			logger.Error().Err(err).Str("user_id", merge.PrimaryUserId).Msg("Failed to move user to new primary tenant")
			return err
		}
	}

	// Map all existing IDP mappings to the primary user
	mappingsUpdated := 0
	idpUserMappingDAO := s.daoFactory.GetIDPUserMappingDAO()
	err = idpUserMappingDAO.FindAll(ctx, func(mapping *auth_entities.IdpUserMapping) bool {
		if slices.Contains(merge.MergedUserIds, mapping.AugmentUserId) {
			_, err := idpUserMappingDAO.TryUpdate(ctx, mapping.IdpUserId, func(m *auth_entities.IdpUserMapping) bool {
				m.AugmentUserId = merge.PrimaryUserId
				return true
			}, DefaultRetry)
			if err != nil {
				logger.Error().Err(err).Str("user_id", mapping.AugmentUserId).Msg("Failed to update IDP mapping")
				return false
			}
			mappingsUpdated++
		}
		return true
	})
	if err != nil {
		logger.Error().Err(err).Msg("Failed to iterate through IDP mappings")
		return err
	}
	logger.Info().Int("mappings_updated", mappingsUpdated).Msg("Updated IDP mappings")

	// Update the subscription owner
	subscriptionDAO := s.daoFactory.GetSubscriptionDAO()
	_, err = subscriptionDAO.TryUpdate(ctx, merge.PreservedOrbSubscriptionId, func(s *auth_entities.Subscription) bool {
		s.Owner = &auth_entities.Subscription_UserId{UserId: merge.PrimaryUserId}
		return true
	}, DefaultRetry)
	if err != nil {
		logger.Error().Err(err).Str("subscription_id", merge.PreservedOrbSubscriptionId).Msg("Failed to update subscription owner")
		return err
	}

	// Set the orb metadata to the primary user
	if merge.PreservedOrbCustomerId != "" {
		logger.Info().Str("customer_id", merge.PreservedOrbCustomerId).Str("user_id", merge.PrimaryUserId).Msg("Updating Orb customer metadata")
		if err := s.orbClient.UpdateCustomerMetadata(ctx, merge.PreservedOrbCustomerId, map[string]string{
			"augment_user_id": merge.PrimaryUserId,
		}); err != nil {
			logger.Error().Err(err).Str("customer_id", merge.PreservedOrbCustomerId).Str("user_id", merge.PrimaryUserId).Msg("Failed to update Orb customer metadata")
			return err
		}

		// Update customer email to match the primary user's email
		if len(merge.MergedEmails) > 0 {
			// Use the primary user's email (first in the list)
			logger.Info().Str("customer_id", merge.PreservedOrbCustomerId).Str("email", merge.Email).Msg("Updating Orb customer email")

			updateInfo := orb.OrbCustomerUpdateInfo{
				Email: &merge.Email,
				Name:  &merge.Email,
			}

			if err := s.orbClient.UpdateCustomerInformation(ctx, merge.PreservedOrbCustomerId, updateInfo); err != nil {
				logger.Error().Err(err).Str("customer_id", merge.PreservedOrbCustomerId).Str("email", merge.Email).Msg("Failed to update Orb customer email")
				return err
			}
		}
	}

	// Delete the other users
	for _, userId := range merge.MergedUserIds {
		tenantId, ok := userToTenantMap[userId]
		if !ok {
			return fmt.Errorf("Failed to find tenant for user %s", userId)
		}
		logger.Info().Str("user_id", userId).Str("tenant_id", tenantId).Msg("Deleting merged user")
		_, err := s.removeUserFromTenant(ctx, &authpb.RemoveUserFromTenantRequest{
			UserId:   userId,
			TenantId: tenantId,
		}, nil)
		if err != nil {
			logger.Error().Err(err).Str("user_id", userId).Str("tenant_id", tenantId).Msg("Failed to delete merged user")
			return err
		}
	}

	// Cancel any remaining subscriptions
	for _, orbSubscriptionId := range merge.CanceledSubscriptionIds {
		logger.Info().Str("subscription_id", orbSubscriptionId).Msg("Cancelling subscription")
		err := s.orbClient.CancelOrbSubscriptionUnscheduleFirst(ctx, orbSubscriptionId, orb.PlanChangeImmediate, nil)
		if err != nil {
			logger.Error().Err(err).Str("subscription_id", orbSubscriptionId).Msg("Failed to cancel subscription")
			return err
		}
	}

	return nil
}

// find connected user groups by IDP user IDs
// ex. If user A has IDP user IDs [1, 2, 3] and user B has IDP user IDs [2, 3, 4], then users A and B are in the same group
func (s *AuthGrpcServer) findConnectedUserGroups(usersByIdpUserId map[string][]*auth_entities.User) [][]*auth_entities.User {
	userToGroup := make(map[string]int) // user ID -> group ID
	groups := make([][]*auth_entities.User, 0)

	for _, users := range usersByIdpUserId {
		if len(users) <= 1 {
			continue
		}

		// Find all existing groups that any of these users belong to
		existingGroups := make(map[int]bool) // group ID -> bool
		var usersNotInGroups []*auth_entities.User

		for _, user := range users {
			if groupId, exists := userToGroup[user.Id]; exists {
				existingGroups[groupId] = true
			} else {
				usersNotInGroups = append(usersNotInGroups, user)
			}
		}

		if len(existingGroups) == 0 {
			// Create new group with all users
			groupId := len(groups)
			groups = append(groups, make([]*auth_entities.User, 0))
			for _, user := range users {
				groups[groupId] = append(groups[groupId], user)
				userToGroup[user.Id] = groupId
			}
		} else if len(existingGroups) == 1 {
			// Add new users to existing group
			var targetGroup int
			for groupId := range existingGroups {
				targetGroup = groupId
				break
			}
			for _, user := range usersNotInGroups {
				groups[targetGroup] = append(groups[targetGroup], user)
				userToGroup[user.Id] = targetGroup
			}
		} else {
			// Merge multiple existing groups + new users
			var targetGroup int
			var groupsToMerge []int

			for groupId := range existingGroups {
				if targetGroup == 0 {
					targetGroup = groupId
				} else {
					groupsToMerge = append(groupsToMerge, groupId)
				}
			}

			// Merge other groups into target group
			for _, groupId := range groupsToMerge {
				for _, user := range groups[groupId] {
					groups[targetGroup] = append(groups[targetGroup], user)
					userToGroup[user.Id] = targetGroup
				}
				groups[groupId] = nil // Mark as merged
			}

			// Add new users to target group
			for _, user := range usersNotInGroups {
				groups[targetGroup] = append(groups[targetGroup], user)
				userToGroup[user.Id] = targetGroup
			}
		}
	}

	// Filter out nil groups and remove duplicates
	var result [][]*auth_entities.User
	for _, group := range groups {
		if group != nil && len(group) > 1 {
			// Remove duplicates within group
			seen := make(map[string]bool)
			var uniqueUsers []*auth_entities.User
			for _, user := range group {
				if !seen[user.Id] {
					seen[user.Id] = true
					uniqueUsers = append(uniqueUsers, user)
				}
			}
			if len(uniqueUsers) > 1 {
				result = append(result, uniqueUsers)
			}
		}
	}

	return result
}

func (s *AuthGrpcServer) DeduplicateUserTenantList(ctx context.Context, req *authpb.DeduplicateUserTenantListRequest) (*authpb.DeduplicateUserTenantListResponse, error) {
	err := authCheck(ctx, "", tokenscopesproto.Scope_AUTH_RW)
	if err != nil {
		return nil, err
	}

	userDAO := s.daoFactory.GetUserDAO()
	user, err := userDAO.Get(ctx, req.UserId)
	if err != nil {
		return nil, err
	}

	currentTenants := user.Tenants
	newTenants := make([]string, 0, len(currentTenants))
	for _, tenant := range currentTenants {
		if !slices.Contains(newTenants, tenant) {
			newTenants = append(newTenants, tenant)
		}
	}

	if len(newTenants) > 1 {
		return nil, status.Error(codes.FailedPrecondition, "User has multiple tenants after deduplication")
	}

	if len(newTenants) != len(currentTenants) {
		if req.MakeChanges {
			_, err = userDAO.TryUpdate(ctx, req.UserId, func(u *auth_entities.User) bool {
				u.Tenants = newTenants
				return true
			}, DefaultRetry)
			if err != nil {
				return nil, err
			}
		} else {
			log.Ctx(ctx).Info().Str("user_id", req.UserId).Msg("Would have made changes to user's tenants list")
		}
	}

	var newTenant string
	if len(newTenants) > 0 {
		newTenant = newTenants[0]
	}

	return &authpb.DeduplicateUserTenantListResponse{
		PreviousTenants: currentTenants,
		NewTenant:       newTenant,
	}, nil
}

func (s *AuthGrpcServer) MigratePopulateIDPUserMappings(ctx context.Context, req *authpb.MigratePopulateIDPUserMappingsRequest) (*authpb.MigratePopulateIDPUserMappingsResponse, error) {
	requestID := uuid.New().String()
	logger := log.With().Str("requestID", requestID).Logger()

	logger.Info().Msg("MigratePopulateIDPUserMappings request received")

	err := authCheck(ctx, "", tokenscopesproto.Scope_AUTH_RW)
	if err != nil {
		return nil, err
	}

	authInfo, _ := auth.GetAugmentClaims(ctx)
	requestContext, _ := requestcontext.FromGrpcContext(ctx)
	s.auditLogger.WriteAuditLog(
		authInfo,
		"Migrate populate IDP user mappings",
		requestContext,
	)

	userDAO := s.daoFactory.GetUserDAO()
	idpUserMappingDAO := s.daoFactory.GetIDPUserMappingDAO()

	idpUserIDToUserId := make(map[string]string)
	err = idpUserMappingDAO.FindAll(ctx, func(mapping *auth_entities.IdpUserMapping) bool {
		idpUserIDToUserId[mapping.IdpUserId] = mapping.AugmentUserId
		return true
	})
	if err != nil {
		logger.Error().Err(err).Msg("Failed to fetch IDP user mappings")
		return nil, err
	}

	var wg sync.WaitGroup
	wg.Add(1)
	var error error

	go func() {
		defer wg.Done()

		ctx := context.Background()

		usersMigrated := 0
		usersFailed := 0
		migrateUser := func(user *auth_entities.User) bool {
			for _, idpUserID := range user.IdpUserIds {
				if _, ok := idpUserIDToUserId[idpUserID]; ok {
					continue // already exists
				}

				mapping, err := idpUserMappingDAO.Get(ctx, idpUserID)
				if err != nil {
					usersFailed++
					logger.Error().Err(err).Str("user_id", user.Id).Str("idp_user_id", idpUserID).Msg("MigratePopulateIDPUserMappings: Failed to get IDP user mapping")
					continue
				}
				if mapping != nil {
					continue // already exists
				}
				// Create the mapping
				mapping = &auth_entities.IdpUserMapping{
					IdpUserId:     idpUserID,
					AugmentUserId: user.Id,
				}
				_, err = idpUserMappingDAO.Create(ctx, mapping)
				if err != nil {
					usersFailed++
					logger.Warn().Err(err).Str("user_id", user.Id).Str("idp_user_id", idpUserID).Msg("MigratePopulateIDPUserMappings: Failed to create IDP user mapping")
					continue
				}
				usersMigrated++
			}
			return true
		}

		// Process all users
		err = userDAO.FindAll(ctx, migrateUser)
		if err != nil {
			logger.Error().Err(err).Msg("MigratePopulateIDPUserMappings: Failed to fetch users")
			error = err
			return
		}

		logger.Info().Int("users_migrated", usersMigrated).Int("users_failed", usersFailed).Msg("MigratePopulateIDPUserMappings: Migration complete")
	}()

	wg.Wait()

	if error != nil {
		return nil, error
	}

	return &authpb.MigratePopulateIDPUserMappingsResponse{}, nil
}

func (s *AuthGrpcServer) BackfillCancellations(ctx context.Context, req *authpb.BackfillCancellationsRequest) (*authpb.BackfillCancellationsResponse, error) {
	log.Ctx(ctx).Info().Msgf("BackfillCancellations request received, make_changes=%s, max_invoices_to_process=%d, minimum_invoice_amount=%s", req.MakeChanges, req.MaxInvoicesToProcess, req.MinimumInvoiceAmount)

	if req.MaxInvoicesToProcess <= 0 {
		return nil, status.Error(codes.InvalidArgument, "max_invoices_to_process must be greater than 0")
	}

	// All invoices above $0 is the default
	minimumInvoiceAmount := "0"
	if req.MinimumInvoiceAmount != "" {
		minimumInvoiceAmount = req.MinimumInvoiceAmount
	}

	err := authCheck(ctx, "", tokenscopesproto.Scope_AUTH_RW)
	if err != nil {
		return nil, err
	}

	authClaims, ok := auth.GetAugmentClaims(ctx)
	if !ok {
		return nil, status.Error(codes.Unknown, "Invalid context")
	}
	s.auditLogger.WriteAuditLog(
		authClaims,
		fmt.Sprintf("BackfillCancellations request received"),
	)

	// Create a wait group to wait for background processing
	var wg sync.WaitGroup
	var error error
	wg.Add(1)

	successfulInvoiceIds := make([]string, 0) // successfully cancelled
	failedInvoiceIds := make([]string, 0)
	notAttemptedInvoiceIds := make([]string, 0)
	voidedInvoiceIds := make([]string, 0)

	go func() {
		defer wg.Done()
		backgroundCtx := context.Background()
		if authClaims, ok := auth.GetAugmentClaims(ctx); ok {
			backgroundCtx = authClaims.NewContext(backgroundCtx)
		}
		// Get all failed invoices, no more than max invoices, with amount greater than minimum invoice amount
		failedInvoices, err := s.orbClient.ListFailedInvoices(backgroundCtx, int(req.MaxInvoicesToProcess), minimumInvoiceAmount)
		if err != nil {
			log.Ctx(backgroundCtx).Error().Err(err).Msg("Failed to list failed invoices")
			error = err
			return
		}

		// For each failed invoice, check if we should cancel the subscription
		for _, invoice := range failedInvoices {
			log.Ctx(backgroundCtx).Info().Str("invoice_id", invoice.ID).Msg("Processing invoice")

			time.Sleep(time.Millisecond * 20) // Avoid rate limits

			// Check if the status is not issued, we don't need to cancel
			if invoice.Status != "issued" {
				log.Ctx(backgroundCtx).Info().
					Str("invoice_id", invoice.ID).
					Str("status", invoice.Status).
					Msg("Not canceling subscription yet, not issued")
				notAttemptedInvoiceIds = append(notAttemptedInvoiceIds, invoice.ID)
				continue
			}

			if invoice.Source == "subscription" {
				// Get the subscription and confirm it is active
				subscription, err := s.orbClient.GetUserSubscription(backgroundCtx, invoice.SubscriptionID, nil)
				if err != nil {
					log.Ctx(backgroundCtx).Error().Err(err).Str("invoice_id", invoice.ID).Msg("Failed to get subscription")
					failedInvoiceIds = append(failedInvoiceIds, invoice.ID)
					continue
				}
				if subscription.OrbStatus != "active" {
					// If the subscription is not active, we should just void the invoices
					// This is to get rid of the small one-off invoices that hang around for the pro-rated amount after a subscription cancellation
					if req.MakeChanges {
						log.Ctx(backgroundCtx).Info().Str("invoice_id", invoice.ID).Msg("Voiding invoice for failed payment invoice and inactive subscription")
						err := voidOrbInvoice(backgroundCtx, invoice.ID, s.orbClient)
						if err != nil {
							log.Ctx(backgroundCtx).Error().Err(err).Str("invoice_id", invoice.ID).Msg("Failed to void invoice")
							failedInvoiceIds = append(failedInvoiceIds, invoice.ID)
							continue
						}
					} else {
						log.Ctx(backgroundCtx).Info().Str("invoice_id", invoice.ID).Msg("Would void invoice for failed payment invoice and inactive subscription")
					}
					voidedInvoiceIds = append(voidedInvoiceIds, invoice.ID)
				} else {
					// Check if we should cancel the subscription
					cancelResult := shouldCancelOrbSubscription(backgroundCtx, invoice, s.orbClient, s.stripeClient)
					if cancelResult.Err != nil {
						log.Ctx(backgroundCtx).Error().Err(cancelResult.Err).Str("invoice_id", invoice.ID).Msg("Failed to check if we should cancel subscription")
						failedInvoiceIds = append(failedInvoiceIds, invoice.ID)
						continue
					}
					if !cancelResult.ShouldCancel {
						log.Ctx(backgroundCtx).Info().Str("invoice_id", invoice.ID).Str("subscription_id", invoice.SubscriptionID).Msg("Not canceling subscription")
						notAttemptedInvoiceIds = append(notAttemptedInvoiceIds, invoice.ID)
						continue
					}

					// Cancel the subscription
					if req.MakeChanges {
						log.Ctx(backgroundCtx).Info().Str("invoice_id", invoice.ID).Str("reason", cancelResult.Reason).Str("subscription_id", invoice.SubscriptionID).Msg("Canceling subscription")
						// Void the invoice in Orb
						err := voidOrbInvoice(backgroundCtx, invoice.ID, s.orbClient)
						if err != nil {
							log.Ctx(backgroundCtx).Error().Err(err).Str("invoice_id", invoice.ID).Msg("Failed to void invoice")
							failedInvoiceIds = append(failedInvoiceIds, invoice.ID)
							continue
						}
						err = cancelOrbSubscriptionForFailedPayment(backgroundCtx, invoice, cancelResult.Reason, s.orbClient, s.auditLogger, s.daoFactory)
						if err != nil {
							log.Ctx(backgroundCtx).Error().Err(err).Str("invoice_id", invoice.ID).Msg("Failed to cancel subscription")
							failedInvoiceIds = append(failedInvoiceIds, invoice.ID)
							continue
						}
						if cancelResult.ClearPaymentMethods {
							err := removePaymentMethodForCustomer(backgroundCtx, cancelResult.StripeCustomerID, s.stripeClient)
							if err != nil {
								log.Ctx(backgroundCtx).Error().Err(err).Str("invoice_id", invoice.ID).Msg("Failed to remove payment method for customer")
								failedInvoiceIds = append(failedInvoiceIds, invoice.ID)
								continue
							}
						}
						successfulInvoiceIds = append(successfulInvoiceIds, invoice.ID)
					} else {
						log.Ctx(backgroundCtx).Info().Str("invoice_id", invoice.ID).Str("reason", cancelResult.Reason).Str("subscription_id", invoice.SubscriptionID).Msg("Would cancel subscription")
						successfulInvoiceIds = append(successfulInvoiceIds, invoice.ID)
					}
				}
			} else if invoice.Source == "one_off" {
				// Check if we should void the invoice
				voidResult := shouldVoidOrbOneOffInvoice(backgroundCtx, invoice, s.orbClient, s.stripeClient)
				if voidResult.Err != nil {
					log.Ctx(backgroundCtx).Error().Err(voidResult.Err).Str("invoice_id", invoice.ID).Msg("Failed to check if we should void one off invoice")
					failedInvoiceIds = append(failedInvoiceIds, invoice.ID)
					continue
				}

				if voidResult.ShouldVoid {
					if req.MakeChanges {
						log.Ctx(backgroundCtx).Info().Str("invoice_id", invoice.ID).Msg("Voiding invoice for one off invoice")
						err := voidOrbOneOffInvoice(backgroundCtx, invoice, voidResult.Reason, s.orbClient)
						if err != nil {
							log.Ctx(backgroundCtx).Error().Err(err).Str("invoice_id", invoice.ID).Msg("Failed to void invoice")
							failedInvoiceIds = append(failedInvoiceIds, invoice.ID)
							continue
						}
					} else {
						log.Ctx(backgroundCtx).Info().Str("invoice_id", invoice.ID).Msg("Would void invoice for one off invoice")
					}
					voidedInvoiceIds = append(voidedInvoiceIds, invoice.ID)
				} else {
					log.Ctx(backgroundCtx).Info().Str("invoice_id", invoice.ID).Msg("Not voiding invoice yet for one off invoice")
					notAttemptedInvoiceIds = append(notAttemptedInvoiceIds, invoice.ID)
				}
			}

		}
	}()
	wg.Wait()

	if error != nil {
		log.Ctx(ctx).Error().Err(error).Msg("Error in processing invoices")
		return nil, error
	}

	log.Ctx(ctx).Info().
		Int("num_successful_invoices", len(successfulInvoiceIds)).
		Int("num_failed_invoices", len(failedInvoiceIds)).
		Int("num_not_attempted_invoices", len(notAttemptedInvoiceIds)).
		Int("num_voided_invoices", len(voidedInvoiceIds)).
		Msg("BackfillCancellations request completed")

	log.Ctx(ctx).Info().
		Interface("successful_invoice_ids", successfulInvoiceIds).
		Interface("failed_invoice_ids", failedInvoiceIds).
		Interface("not_attempted_invoice_ids", notAttemptedInvoiceIds).
		Interface("voided_invoice_ids", voidedInvoiceIds).
		Msg("BackfillCancellations request results")

	return &authpb.BackfillCancellationsResponse{
		SuccessfulInvoiceIds:   successfulInvoiceIds,
		FailedInvoiceIds:       failedInvoiceIds,
		NotAttemptedInvoiceIds: notAttemptedInvoiceIds,
		VoidedInvoiceIds:       voidedInvoiceIds,
	}, nil
}

func (s *AuthGrpcServer) ForgetUser(ctx context.Context, req *authpb.ForgetUserRequest) (*authpb.ForgetUserResponse, error) {
	err := authCheck(ctx, "", tokenscopesproto.Scope_PII_ADMIN)
	if err != nil {
		return nil, err
	}

	authClaims, _ := auth.GetAugmentClaims(ctx)
	requestCtx, _ := requestcontext.FromGrpcContext(ctx)
	s.auditLogger.WriteAuditLog(
		authClaims,
		fmt.Sprintf("ForgetUser request received for user %s", req.UserId),
		audit.NewUser(req.UserId),
		audit.NewProtoRequest(req),
		requestCtx,
	)

	// Make sure the user doesn't belong to any tenants.
	// TODO(jacqueline): Consider doing the full account cleanup here. For now I'm leaving this as a
	// separate step.
	userDAO := s.daoFactory.GetUserDAO()
	user, err := userDAO.Get(ctx, req.UserId)
	if err != nil {
		return nil, fmt.Errorf("failed to get user: %w", err)
	}
	if user == nil {
		return nil, status.Error(codes.NotFound, "Resource not found")
	}
	if len(user.Tenants) > 0 {
		return nil, status.Error(codes.InvalidArgument, "User belongs to tenants. Please delete the user's account before calling ForgetUser.")
	}

	// Mark the user as deleted.
	_, err = userDAO.TryUpdate(ctx, req.UserId, func(u *auth_entities.User) bool {
		u.DeletionState = auth_entities.User_GDPR_CCPA_DELETED
		return true
	}, DefaultRetry)
	if err != nil {
		return nil, fmt.Errorf("failed to mark user as deleted: %w", err)
	}

	normalizedEmail, err := normalizeEmail(user.Email)
	if err != nil {
		return nil, fmt.Errorf("failed to normalize email: %w", err)
	}

	// Delete tenant invitations where the user is the invitee (by email)
	err = s.deleteTenantInvitationsByEmail(ctx, normalizedEmail, user.Id)
	if err != nil {
		return nil, fmt.Errorf("failed to delete tenant invitations by email: %w", err)
	}

	// Delete codes that reference this user
	err = s.deleteCodes(ctx, user.Id, normalizedEmail)
	if err != nil {
		return nil, fmt.Errorf("failed to delete codes: %w", err)
	}

	// Delete terms approvals for this user's email
	err = s.deleteTermsApprovals(ctx, normalizedEmail)
	if err != nil {
		return nil, fmt.Errorf("failed to delete terms approvals: %w", err)
	}

	// Log OCSF event for user account deletion (GDPR/CCPA deletion)
	// Use admin's tenant context for audit trail since deleted user was already removed from all tenants
	var adminEmail string
	if email, ok := authClaims.GetIapEmail(); ok {
		adminEmail = email
	} else if authClaims.UserEmail != "" {
		adminEmail = authClaims.UserEmail
	} else {
		adminEmail = "unknown"
	}

	// Use admin's tenant ID for the audit log, or fall back to non-tenant logging if admin has global access
	if authClaims.TenantID != "" && authClaims.TenantID != "*" {
		if err := s.ocsfAuditLogger.LogAPIActivityWithTenant(
			auditocsf.NewAPIActivityEventBuilder(auditocsf.ActivityAPIDelete).
				WithAPI(auditocsf.API{
					Operation: "ForgetUser",
					Service: auditocsf.APIService{
						Name: "AuthService",
					},
				}).
				WithActor(auditocsf.Actor{
					User: &auditocsf.User{
						UID: authClaims.OpaqueUserID,
					},
				}).
				WithMessage(fmt.Sprintf("Permanently deleted user account '%s' (GDPR/CCPA) - performed by admin %s (%s, email: %s)",
					req.UserId, authClaims.OpaqueUserID, authClaims.OpaqueUserIDType, adminEmail)).
				Build(),
			authClaims.TenantID,
		); err != nil {
			log.Ctx(ctx).Warn().Err(err).Msg("Failed to log OCSF audit event for user deletion")
		}
	} else {
		// Fall back to non-tenant logging for global admins
		if err := s.ocsfAuditLogger.LogAPIActivity(
			auditocsf.NewAPIActivityEventBuilder(auditocsf.ActivityAPIDelete).
				WithAPI(auditocsf.API{
					Operation: "ForgetUser",
					Service: auditocsf.APIService{
						Name: "AuthService",
					},
				}).
				WithActor(auditocsf.Actor{
					User: &auditocsf.User{
						UID: authClaims.OpaqueUserID,
					},
				}).
				WithMessage(fmt.Sprintf("Permanently deleted user account '%s' (GDPR/CCPA) - performed by global admin %s (%s, email: %s)",
					req.UserId, authClaims.OpaqueUserID, authClaims.OpaqueUserIDType, adminEmail)).
				Build(),
		); err != nil {
			log.Ctx(ctx).Warn().Err(err).Msg("Failed to log OCSF audit event for user deletion")
		}
	}

	return &authpb.ForgetUserResponse{}, nil
}

// deleteTenantInvitationsByEmail deletes all tenant invitations where the user is the invitee.
// TODO: eventually, we should also redact the user's email from any rows where they are the inviter
// but we currently don't have a team name, so we need some way to identify the team to the invitee
func (s *AuthGrpcServer) deleteTenantInvitationsByEmail(ctx context.Context, email string, userID string) error {
	// Normalize the email for comparison
	normalizedEmail, err := normalizeEmail(email)
	if err != nil {
		return fmt.Errorf("failed to normalize email %s: %w", email, err)
	}

	// Get a global tenant invitation DAO to scan all tenants
	tenantInvitationDAO := s.daoFactory.GetTenantInvitationDAO("")

	// Find all invitations for this email across all tenants
	var invitationsToDelete []*auth_entities.TenantInvitation
	err = tenantInvitationDAO.ListInvitationsForAllTenants(ctx, func(invitation *auth_entities.TenantInvitation) bool {
		normalizedInvitationEmail, err := normalizeEmail(invitation.InviteeEmail)
		if err != nil {
			normalizedInvitationEmail = invitation.InviteeEmail
		}

		if normalizedInvitationEmail == normalizedEmail {
			invitationsToDelete = append(invitationsToDelete, invitation)
		}
		return true
	})
	if err != nil {
		return fmt.Errorf("failed to list invitations: %w", err)
	}

	// Delete each invitation
	for _, invitation := range invitationsToDelete {
		tenantDAO := s.daoFactory.GetTenantInvitationDAO(invitation.TenantId)
		err = tenantDAO.Delete(ctx, invitation.Id)
		if err != nil {
			log.Ctx(ctx).Error().Err(err).Msgf("Failed to delete invitation %s for tenant %s", invitation.Id, invitation.TenantId)
			return fmt.Errorf("failed to delete invitation %s: %w", invitation.Id, err)
		}
		log.Ctx(ctx).Info().Msgf("Deleted invitation %s for user %s in tenant %s", invitation.Id, userID, invitation.TenantId)
	}

	log.Ctx(ctx).Info().Msgf("Deleted %d invitations for user %s", len(invitationsToDelete), userID)
	return nil
}

// deleteCodes deletes all codes that reference the user by ID or email.
func (s *AuthGrpcServer) deleteCodes(ctx context.Context, userID, email string) error {
	codeDAO := s.daoFactory.GetCodeDAO()

	// Find all codes that reference this user
	var codesToDelete []string
	err := codeDAO.FindAll(ctx, func(code *auth_entities.Code) bool {
		if code.AugmentUserId == userID || code.Email == email {
			codesToDelete = append(codesToDelete, code.Code)
		}
		return true
	})
	if err != nil {
		return fmt.Errorf("failed to list codes: %w", err)
	}

	// Delete each code
	for _, codeValue := range codesToDelete {
		err = codeDAO.Delete(ctx, codeValue)
		if err != nil {
			log.Ctx(ctx).Error().Err(err).Msgf("Failed to delete code %s", codeValue)
			return fmt.Errorf("failed to delete code %s: %w", codeValue, err)
		}
		log.Ctx(ctx).Info().Msgf("Deleted code for user %s", userID)
	}

	log.Ctx(ctx).Info().Msgf("Deleted %d codes for user %s", len(codesToDelete), userID)
	return nil
}

// deleteTermsApprovals deletes all terms approvals for the user's email.
func (s *AuthGrpcServer) deleteTermsApprovals(ctx context.Context, email string) error {
	termsDAO := s.daoFactory.GetTermsDAO()

	// Find all terms approvals for this email
	var approvalsToDelete []*auth_entities.TermsApproval
	err := termsDAO.FindAll(ctx, func(terms *auth_entities.TermsApproval) bool {
		normalizedTermsEmail, err := normalizeEmail(terms.Email)
		if err != nil {
			normalizedTermsEmail = terms.Email
		}
		if normalizedTermsEmail == email {
			approvalsToDelete = append(approvalsToDelete, terms)
		}
		return true
	})
	if err != nil {
		return fmt.Errorf("failed to list terms approvals: %w", err)
	}

	// Delete each terms approval
	for _, terms := range approvalsToDelete {
		err = termsDAO.Delete(ctx, terms.Email, terms.Revision)
		if err != nil {
			log.Ctx(ctx).Error().Err(err).Msgf("Failed to delete terms approval for email %s revision %s", terms.Email, terms.Revision)
			return fmt.Errorf("failed to delete terms approval for email %s revision %s: %w", terms.Email, terms.Revision, err)
		}
		log.Ctx(ctx).Info().Msgf("Deleted terms approval for email %s revision %s", email, terms.Revision)
	}

	log.Ctx(ctx).Info().Msgf("Deleted %d terms approvals for email %s", len(approvalsToDelete), email)
	return nil
}

// Clears tier change from a user or team, or clears seat change for a subscription.
// Should be called for support reasons, after we have ensured that the user is in a good state otherwise (subscription + tenants are all set).
// Can only be called by IAP User.
func (s *AuthGrpcServer) RemovePendingStatus(ctx context.Context, req *authpb.RemovePendingStatusRequest) (*authpb.RemovePendingStatusResponse, error) {
	err := authCheck(ctx, "", tokenscopesproto.Scope_AUTH_RW)
	if err != nil {
		return nil, err
	}

	// Ensure IAP User
	authClaims, ok := auth.GetAugmentClaims(ctx)
	if !ok {
		return nil, status.Error(codes.Unknown, "Invalid context")
	}
	_, ok = authClaims.GetIapEmail()
	if !ok {
		return nil, status.Error(codes.PermissionDenied, "This endpoint is only accessible to IAP users")
	}

	requestContext, _ := requestcontext.FromGrpcContext(ctx)
	s.auditLogger.WriteAuditLog(
		authClaims,
		fmt.Sprintf("RemovePendingStatus request received for %s", req.String()),
		requestContext,
		audit.NewProtoRequest(req),
	)

	switch req.Request.(type) {
	case *authpb.RemovePendingStatusRequest_UserId:
		return s.removeTierChangeForUser(ctx, req.GetUserId())
	case *authpb.RemovePendingStatusRequest_TenantId:
		return s.removeTierChangeForTenant(ctx, req.GetTenantId())
	case *authpb.RemovePendingStatusRequest_SubscriptionId:
		return s.removeSeatChangeForSubscription(ctx, req.GetSubscriptionId())
	default:
		return nil, status.Error(codes.InvalidArgument, "Invalid request type")
	}
}

func (s *AuthGrpcServer) removeTierChangeForUser(ctx context.Context, userId string) (*authpb.RemovePendingStatusResponse, error) {
	userDAO := s.daoFactory.GetUserDAO()
	user, err := userDAO.TryUpdate(ctx, userId, func(u *auth_entities.User) bool {
		u.TierChange = nil
		return true
	}, DefaultRetry)
	if err != nil {
		return nil, fmt.Errorf("failed to remove pending tier change for user %s: %w", userId, err)
	}
	// If not found, return error
	if user.GetId() == "" {
		return nil, status.Error(codes.NotFound, "User not found")
	}
	return &authpb.RemovePendingStatusResponse{}, nil
}

func (s *AuthGrpcServer) removeTierChangeForTenant(ctx context.Context, tenantId string) (*authpb.RemovePendingStatusResponse, error) {
	tenantSubscriptionMappingDAO := s.daoFactory.GetTenantSubscriptionMappingDAO()
	tenant, err := tenantSubscriptionMappingDAO.TryUpdate(ctx, tenantId, func(t *auth_entities.TenantSubscriptionMapping) bool {
		t.PlanChange = nil
		return true
	}, DefaultRetry)
	if err != nil {
		return nil, fmt.Errorf("failed to remove pending tier change for tenant %s: %w", tenantId, err)
	}
	// If not found, return error
	if tenant.GetTenantId() == "" {
		return nil, status.Error(codes.NotFound, "Tenant not found")
	}
	return &authpb.RemovePendingStatusResponse{}, nil
}

func (s *AuthGrpcServer) removeSeatChangeForSubscription(ctx context.Context, subscriptionId string) (*authpb.RemovePendingStatusResponse, error) {
	subscriptionDAO := s.daoFactory.GetSubscriptionDAO()
	subscription, err := subscriptionDAO.TryUpdate(ctx, subscriptionId, func(s *auth_entities.Subscription) bool {
		s.SubscriptionChangeId = nil
		return true
	}, DefaultRetry)
	if err != nil {
		return nil, fmt.Errorf("failed to remove pending seat change for subscription %s: %w", subscriptionId, err)
	}
	// If not found, return error
	if subscription.GetSubscriptionId() == "" {
		return nil, status.Error(codes.NotFound, "Subscription not found")
	}
	return &authpb.RemovePendingStatusResponse{}, nil
}

// TriggerOrbSubscriptionEvent manually triggers subscription processing for a given subscription ID
// This reuses the same logic that normally happens when Orb sends webhook events
// Can only be called by IAP User.
func (s *AuthGrpcServer) TriggerOrbSubscriptionEvent(ctx context.Context, req *authpb.TriggerOrbSubscriptionEventRequest) (*authpb.TriggerOrbSubscriptionEventResponse, error) {
	err := authCheck(ctx, "", tokenscopesproto.Scope_AUTH_ADMIN)
	if err != nil {
		return nil, err
	}

	// Ensure IAP User
	authClaims, ok := auth.GetAugmentClaims(ctx)
	if !ok {
		return nil, status.Error(codes.Unknown, "Invalid context")
	}
	iapEmail, ok := authClaims.GetIapEmail()
	if !ok {
		return nil, status.Error(codes.PermissionDenied, "This endpoint is only accessible to IAP users")
	}

	if req.SubscriptionId == "" {
		return nil, status.Error(codes.InvalidArgument, "subscription_id is required")
	}
	if req.OrbCustomerId == "" {
		return nil, status.Error(codes.InvalidArgument, "orb_customer_id is required")
	}

	requestContext, _ := requestcontext.FromGrpcContext(ctx)
	s.auditLogger.WriteAuditLog(
		authClaims,
		fmt.Sprintf("TriggerOrbSubscriptionEvent request received for subscription_id: %s", req.SubscriptionId),
		audit.NewProtoRequest(req),
		requestContext,
	)

	log.Ctx(ctx).Info().
		Str("subscription_id", req.SubscriptionId).
		Str("orb_customer_id", req.OrbCustomerId).
		Str("iap_email", iapEmail).
		Msg("Triggering subscription event manually via RPC")

	// Create a subscription event to trigger the billing event processor
	mockEvent := &orb_event.OrbEvent{
		EventType: "subscription.manual_update_triggered",
		Metadata: &orb_event.EventMetadata{
			RequestId: fmt.Sprintf("manual-rpc-%d", time.Now().Unix()),
			EventId:   fmt.Sprintf("manual-evt-%d", time.Now().Unix()),
		},
		OrbCustomerId: &req.OrbCustomerId,
		Event: &orb_event.OrbEvent_SubscriptionEvent{
			SubscriptionEvent: &orb_event.SubscriptionEvent{
				SubscriptionId: req.SubscriptionId,
			},
		},
	}

	err = s.billingEventProcessor.ProcessOrbEvent(ctx, mockEvent)
	if err != nil {
		log.Ctx(ctx).Error().
			Err(err).
			Str("subscription_id", req.SubscriptionId).
			Str("orb_customer_id", req.OrbCustomerId).
			Msg("Failed to process subscription event")
		return nil, status.Error(codes.Internal, fmt.Sprintf("Failed to process subscription event: %v", err))
	}

	// Fetch the updated subscription object from the database
	subscriptionDAO := s.daoFactory.GetSubscriptionDAO()
	subscription, err := subscriptionDAO.Get(ctx, req.SubscriptionId)
	if err != nil {
		log.Ctx(ctx).Error().
			Err(err).
			Str("subscription_id", req.SubscriptionId).
			Str("orb_customer_id", req.OrbCustomerId).
			Msg("Failed to fetch subscription after processing")
		return nil, status.Error(codes.Internal, fmt.Sprintf("Failed to fetch subscription after processing: %v", err))
	}

	if subscription == nil {
		log.Ctx(ctx).Error().
			Str("subscription_id", req.SubscriptionId).
			Str("orb_customer_id", req.OrbCustomerId).
			Msg("Subscription not found after processing")
		return nil, status.Error(codes.NotFound, fmt.Sprintf("Subscription not found after processing: %s", req.SubscriptionId))
	}

	log.Ctx(ctx).Info().
		Str("subscription_id", req.SubscriptionId).
		Str("orb_customer_id", req.OrbCustomerId).
		Msg("Successfully processed subscription event")

	return &authpb.TriggerOrbSubscriptionEventResponse{
		Subscription: subscription,
	}, nil
}

func (s *AuthGrpcServer) AddUserTags(ctx context.Context, req *authpb.AddUserTagsRequest) (*authpb.AddUserTagsResponse, error) {
	// Auth check - require AUTH_RW scope
	err := authCheck(ctx, "", tokenscopesproto.Scope_AUTH_RW)
	if err != nil {
		return nil, err
	}

	authInfo, _ := auth.GetAugmentClaims(ctx)
	s.auditLogger.WriteAuditLog(
		authInfo,
		fmt.Sprintf("Add tags to user '%s': %v", req.UserId, req.Tags),
		audit.NewUser(req.UserId),
		audit.NewProtoRequest(req),
	)

	userDAO := s.daoFactory.GetUserDAO()
	user, err := userDAO.TryUpdate(ctx, req.UserId, func(u *auth_entities.User) bool {
		// Add only new tags
		for _, tag := range req.Tags {
			if !slices.Contains(u.Tags, tag) {
				u.Tags = append(u.Tags, tag)
			}
		}
		return true
	}, DefaultRetry)
	if err != nil {
		return nil, fmt.Errorf("failed to add tags to user %s: %w", req.UserId, err)
	}
	// If not found, return error
	if user.GetId() == "" {
		return nil, status.Error(codes.NotFound, "User not found")
	}
	return &authpb.AddUserTagsResponse{}, nil
}

func (s *AuthGrpcServer) RemoveUserTags(ctx context.Context, req *authpb.RemoveUserTagsRequest) (*authpb.RemoveUserTagsResponse, error) {
	// Auth check - require AUTH_RW scope
	err := authCheck(ctx, "", tokenscopesproto.Scope_AUTH_RW)
	if err != nil {
		return nil, err
	}

	authInfo, _ := auth.GetAugmentClaims(ctx)
	requestContext, _ := requestcontext.FromGrpcContext(ctx)
	s.auditLogger.WriteAuditLog(
		authInfo,
		fmt.Sprintf("Remove tags from user '%s': %v", req.UserId, req.Tags),
		audit.NewUser(req.UserId),
		audit.NewProtoRequest(req),
		requestContext,
	)

	userDAO := s.daoFactory.GetUserDAO()
	user, err := userDAO.TryUpdate(ctx, req.UserId, func(u *auth_entities.User) bool {
		// Remove only specified tags
		newTags := make([]string, 0, len(u.Tags))
		for _, tag := range u.Tags {
			if !slices.Contains(req.Tags, tag) {
				newTags = append(newTags, tag)
			}
		}
		u.Tags = newTags
		return true
	}, DefaultRetry)
	if err != nil {
		return nil, fmt.Errorf("failed to remove tags from user %s: %w", req.UserId, err)
	}
	// If not found, return error
	if user.GetId() == "" {
		return nil, status.Error(codes.NotFound, "User not found")
	}
	return &authpb.RemoveUserTagsResponse{}, nil
}

func (s *AuthGrpcServer) AllowNewTrialForUsers(ctx context.Context, req *authpb.AllowNewTrialForUsersRequest) (*authpb.AllowNewTrialForUsersResponse, error) {
	requestID := uuid.New().String()
	logger := log.With().Str("requestID", requestID).Logger()

	logger.Info().Int("user_count", len(req.UserIds)).Msg("AllowNewTrialForUsers request received")

	// Auth check - require AUTH_ADMIN scope for wildcard tenant access
	err := authCheck(ctx, "", tokenscopesproto.Scope_AUTH_ADMIN)
	if err != nil {
		return nil, err
	}

	resp := &authpb.AllowNewTrialForUsersResponse{
		Results: make([]*authpb.TrialAllowResult, 0, len(req.UserIds)),
	}

	successfulAllows := 0
	failedAllows := 0

	// Process each user
	var wg sync.WaitGroup
	wg.Add(1)
	go func() {
		defer wg.Done()
		backgroundCtx := context.Background()
		if authClaims, ok := auth.GetAugmentClaims(ctx); ok {
			backgroundCtx = authClaims.NewContext(backgroundCtx)
		}

		for _, userID := range req.UserIds {
			userLogger := logger.With().Str("user_id", userID).Logger()
			result := &authpb.TrialAllowResult{
				UserId: userID,
				Status: authpb.TrialAllowResult_STATUS_UNKNOWN,
			}

			// Allow new trial for the user
			err := s.allowNewTrialForUser(backgroundCtx, userID, userLogger)
			if err != nil {
				userLogger.Error().Err(err).Msg("Failed to allow new trial for user")
				result.Status = authpb.TrialAllowResult_STATUS_ERROR
				result.ErrorMessage = fmt.Sprintf("%v", err)
				failedAllows++
			} else {
				result.Status = authpb.TrialAllowResult_STATUS_SUCCESS
				successfulAllows++
			}

			resp.Results = append(resp.Results, result)

			// Sleep 0.1 second between users
			time.Sleep(100 * time.Millisecond)
		}
	}()
	wg.Wait()

	logger.Info().
		Int("successful_allows", successfulAllows).
		Int("failed_allows", failedAllows).
		Msg("AllowNewTrialForUsers request completed")

	return resp, nil
}

// allowNewTrialForUser allows a new trial for a single user
func (s *AuthGrpcServer) allowNewTrialForUser(
	ctx context.Context,
	userID string,
	logger zerolog.Logger,
) error {
	userDAO := s.daoFactory.GetUserDAO()
	user, err := userDAO.Get(ctx, userID)
	if err != nil {
		logger.Error().Err(err).Msg("Failed to get user")
		return fmt.Errorf("failed to get user: %w", err)
	}
	if user == nil {
		logger.Error().Msg("User not found")
		return fmt.Errorf("user not found")
	}

	var tenant *tw_pb.Tenant
	if len(user.Tenants) > 1 {
		logger.Error().Int("tenant_count", len(user.Tenants)).Msg("User is in more than 1 tenants")
		return fmt.Errorf("user is in %d tenants", len(user.Tenants))
	} else if len(user.Tenants) == 1 {
		// User is in exactly one tenant
		// Verify user is not on enterprise or self-serve team
		tenantID := user.Tenants[0]
		logger.Info().Str("tenant_id", tenantID).Msg("Found user's tenant")

		tenant, err = s.tenantMap.GetTenantByIdDeletedOk(tenantID)
		if err != nil {
			logger.Error().Err(err).Str("tenant_id", tenantID).Msg("Failed to get tenant")
			return fmt.Errorf("failed to get tenant: %w", err)
		}
		if tenant == nil {
			logger.Error().Str("tenant_id", tenantID).Msg("Tenant not found")
			return fmt.Errorf("tenant not found")
		}

		if tenantutil.IsEnterpriseTenant(tenant) {
			logger.Error().Str("tenant_id", tenantID).Msg("User is on enterprise tenant")
			return fmt.Errorf("user is on enterprise tenant")
		}
		if tenantutil.IsSelfServeTeamTenant(tenant) {
			logger.Error().Str("tenant_id", tenantID).Msg("User is on self-serve team tenant")
			return fmt.Errorf("user is on self-serve team tenant")
		}
	} else {
		// user is not in any tenant now, that's OK
		logger.Info().Str("user_id", userID).Msg("User has no tenant")
	}

	// Verify the user does not have an active non-trial orb subscription
	if user.OrbSubscriptionId != "" {
		subscriptionInfo, err := s.orbClient.GetUserSubscription(ctx, user.OrbSubscriptionId, nil)
		if err != nil {
			logger.Error().Err(err).Str("orb_subscription_id", user.OrbSubscriptionId).Msg("Failed to get orb subscription")
			return fmt.Errorf("failed to get orb subscription: %w", err)
		}

		// Check if subscription is active
		if subscriptionInfo != nil && subscriptionInfo.OrbStatus == "active" {
			// if it is a Trial subscription, cancel it immediately, otherwise fail
			if subscriptionInfo.ExternalPlanID == s.orbConfig.GetTrialPlan().ID {
				if subscriptionInfo.CreatedAt.After(time.Now().Add(-48 * time.Hour)) {
					logger.Error().
						Str("user_id", userID).
						Str("orb_subscription_id", user.OrbSubscriptionId).
						Msg("User has active trial subscription created less than 48 hours ago")
					return fmt.Errorf("user has active trial subscription created less than 48 hours ago")
				}

				err = s.orbClient.CancelOrbSubscriptionUnscheduleFirst(ctx, user.OrbSubscriptionId, orb.PlanChangeImmediate, nil)
				if err != nil {
					logger.Error().Err(err).Str("orb_subscription_id", user.OrbSubscriptionId).Msg("Failed to cancel orb subscription")
					return fmt.Errorf("failed to cancel orb subscription: %w", err)
				}
			} else {
				logger.Error().
					Str("orb_subscription_id", user.OrbSubscriptionId).
					Str("plan_id", subscriptionInfo.ExternalPlanID).
					Str("status", subscriptionInfo.OrbStatus).
					Msg("User has active non-trial subscription")
				return fmt.Errorf("user has active non-trial subscription")
			}
		}
	}

	// Clear the user's subscription creation info and info and clear the user's orb subscription id
	_, err = userDAO.TryUpdate(ctx, userID, func(u *auth_entities.User) bool {
		// Clear subscription creation info
		u.SubscriptionCreationId = ""
		u.SubscriptionCreationInfo = nil

		// Clear orb subscription id
		u.OrbSubscriptionId = ""

		return true
	}, DefaultRetry)
	if err != nil {
		logger.Error().Err(err).Msg("Failed to update user subscription fields")
		return fmt.Errorf("failed to update user subscription fields: %w", err)
	}

	logger.Info().Msg("Cleared user subscription fields")

	// Remove user from their tenant
	if tenant != nil {
		_, err = s.removeUserFromTenant(ctx, &authpb.RemoveUserFromTenantRequest{
			UserId:   userID,
			TenantId: tenant.Id,
		}, map[string]string{tenant.Id: tenant.Name})
		if err != nil {
			return fmt.Errorf("failed to remove user from tenant: %w", err)
		}
	}

	// Audit log for individual user trial allow
	authClaims, ok := auth.GetAugmentClaims(ctx)
	if ok {
		requestContext, _ := requestcontext.FromGrpcContext(ctx)
		if tenant != nil {
			auditMessage := fmt.Sprintf("Allowed new trial for user %s (orb_customer_id: %s, tenant_id: %s)",
				userID, user.OrbCustomerId, tenant.Id)
			s.auditLogger.WriteAuditLog(
				authClaims,
				auditMessage,
				audit.NewTenantName(tenant.Name),
				audit.NewTenantID(tenant.Id),
				audit.NewUser(userID),
				requestContext,
			)
		} else {
			auditMessage := fmt.Sprintf("Allowed new trial for user %s (orb_customer_id: %s, no tenant)",
				userID, user.OrbCustomerId)
			s.auditLogger.WriteAuditLog(
				authClaims,
				auditMessage,
				audit.NewUser(userID),
				requestContext,
			)
		}

	}

	return nil
}

func (s *AuthGrpcServer) RemoveMessagesFromAsyncOpsDeadLetterQueue(ctx context.Context, req *authpb.RemoveMessagesFromAsyncOpsDeadLetterQueueRequest) (*authpb.RemoveMessagesFromAsyncOpsDeadLetterQueueResponse, error) {
	err := authCheck(ctx, "", tokenscopesproto.Scope_AUTH_ADMIN)
	if err != nil {
		return nil, err
	}

	authClaims, canAudit := auth.GetAugmentClaims(ctx)
	requestContext, _ := requestcontext.FromGrpcContext(ctx)
	if canAudit {
		s.auditLogger.WriteAuditLog(
			authClaims,
			fmt.Sprintf("RemoveMessagesFromAsyncOpsDeadLetterQueue request received"),
			requestContext,
			audit.NewProtoRequest(req),
		)
	}
	if s.asyncOpsWorker == nil {
		return nil, status.Error(codes.Internal, "Async ops worker not configured")
	}
	// We use a 360 second timeout, because the async ops deadletter queue defines the maximum backoff time as 300 seconds.
	timeout := 360 * time.Second
	if req.TimeoutSeconds != 0 {
		// However, we allow the caller to override this timeout.
		timeout = time.Duration(req.TimeoutSeconds) * time.Second
	}
	err = s.asyncOpsWorker.subscribeClient.RemoveDeadLetters(authClaims.NewContext(context.Background()), req.MessageIds, timeout)
	resp := authpb.RemoveMessagesFromAsyncOpsDeadLetterQueueResponse{}
	if canAudit {
		s.auditLogger.WriteAuditLog(
			authClaims,
			fmt.Sprintf("RemoveMessagesFromAsyncOpsDeadLetterQueue request processed"),
			requestContext,
			audit.NewProtoResponse(&resp),
		)
	}
	return &resp, err
}

func (s *AuthGrpcServer) BackfillNetsuiteInfo(ctx context.Context, req *authpb.BackfillNetsuiteInfoRequest) (*authpb.BackfillNetsuiteInfoResponse, error) {
	err := authCheck(ctx, "", tokenscopesproto.Scope_AUTH_ADMIN)
	if err != nil {
		return nil, err
	}
	if req.MaxUsers <= 0 {
		return nil, status.Error(codes.FailedPrecondition, "Max users cannot be <= 0")
	}
	if !s.orbConfig.AccountingConfig.Enabled {
		return nil, status.Error(codes.FailedPrecondition, "Netsuite accounting is not enabled")
	}

	authClaims, _ := auth.GetAugmentClaims(ctx)
	requestContext, _ := requestcontext.FromGrpcContext(ctx)
	s.auditLogger.WriteAuditLog(
		authClaims,
		fmt.Sprintf("BackfillNetsuiteInfo request received"),
		audit.NewProtoRequest(req),
		requestContext,
	)

	wg := sync.WaitGroup{}
	var error error
	wg.Add(1)

	var failedCustomers []string
	var successfulCustomers []string
	var unchangedCustomers []string

	go func() {
		defer wg.Done()
		backgroundCtx := authClaims.NewContext(context.Background())

		cursor := ""
		counter := 0
		maxUsers := int(req.MaxUsers)

		for {
			if counter >= maxUsers {
				break
			}

			// Fetch customers in batches of max 100 (or remaining if less than 100),
			// 100 is the max size from Orb
			batchSize := int64(100)
			remaining := maxUsers - counter
			if remaining < 100 {
				batchSize = int64(remaining)
			}

			log.Ctx(ctx).Info().Msgf("Fetching batch of customers, cursor=%s, batchSize=%d", cursor, batchSize)
			customersPage, err := s.orbClient.ListCustomers(backgroundCtx, cursor, batchSize, nil)
			if err != nil {
				log.Ctx(backgroundCtx).Error().Err(err).Msg("Failed to list customers")
				error = err
				return
			}

			log.Ctx(ctx).Info().Msgf("Retrieved %d customers in this batch", len(customersPage.Customers))

			for _, customer := range customersPage.Customers {
				counter++
				log.Ctx(ctx).Info().Msgf("Processing customer %d, Orb ID %s", counter, customer.OrbCustomerID)
				if customer.AccountingInfo != nil && customer.AccountingInfo.ProviderType == s.orbConfig.AccountingConfig.ProviderType && customer.AccountingInfo.ProviderID == s.orbConfig.AccountingConfig.ProviderID {
					log.Ctx(ctx).Info().Msgf("Customer %s already has accounting info, skipping", customer.OrbCustomerID)
					unchangedCustomers = append(unchangedCustomers, customer.OrbCustomerID)
					continue
				}
				if req.DryRun {
					log.Ctx(ctx).Info().Msgf("Dry run, would update customer %s", customer.OrbCustomerID)
					successfulCustomers = append(successfulCustomers, customer.OrbCustomerID)
					continue
				} else {
					log.Ctx(ctx).Info().Msgf("Updating customer %s", customer.OrbCustomerID)
				}
				err := s.orbClient.UpdateCustomerInformation(backgroundCtx, customer.OrbCustomerID, orb.OrbCustomerUpdateInfo{
					AccountingInfo: &orb.AccountingProviderInfo{
						ProviderType: s.orbConfig.AccountingConfig.ProviderType,
						ProviderID:   s.orbConfig.AccountingConfig.ProviderID,
					},
				})
				if err != nil {
					log.Ctx(ctx).Error().Err(err).Str("customer_id", customer.OrbCustomerID).Msg("Failed to update customer information")
					failedCustomers = append(failedCustomers, customer.OrbCustomerID)
				} else {
					log.Ctx(ctx).Info().Str("customer_id", customer.OrbCustomerID).Msg("Successfully updated customer information")
					successfulCustomers = append(successfulCustomers, customer.OrbCustomerID)
				}

				// 50ms sleep between writes: 1000/50 = 20 req/sec max, below 50/sec write rate limit
				time.Sleep(50 * time.Millisecond)
			}

			// If no more customers, end
			if !customersPage.HasMore || customersPage.NextCursor == nil {
				log.Ctx(ctx).Info().Msg("No more customers to process")
				break
			}

			// 50ms sleep between reading pages: 1000/50 = 20 req/sec max, below 100/sec read rate limit
			time.Sleep(50 * time.Millisecond)

			cursor = *customersPage.NextCursor
			log.Ctx(ctx).Info().Msgf("Moving to next page of users")
		}
	}()

	wg.Wait()

	if error != nil {
		return nil, error
	}

	log.Ctx(ctx).Info().Int("failed_customers", len(failedCustomers)).Int("successful_customers", len(successfulCustomers)).Int("unchanged_customers", len(unchangedCustomers)).Msg("BackfillNetsuiteInfo request completed")
	log.Ctx(ctx).Info().Interface("failed_customer_ids", failedCustomers).Interface("successful_customer_ids", successfulCustomers).Interface("unchanged_customer_ids", unchangedCustomers).Msg("BackfillNetsuiteInfo request results")

	return &authpb.BackfillNetsuiteInfoResponse{
		FailedCustomerIds:     failedCustomers,
		SuccessfulCustomerIds: successfulCustomers,
		UnchangedCustomerIds:  unchangedCustomers,
	}, nil
}

func (s *AuthGrpcServer) BackfillAddresses(ctx context.Context, req *authpb.BackfillAddressesRequest) (*authpb.BackfillAddressesResponse, error) {
	err := authCheck(ctx, "", tokenscopesproto.Scope_AUTH_ADMIN)
	if err != nil {
		return nil, err
	}
	if req.MaxUsers <= 0 {
		return nil, status.Error(codes.FailedPrecondition, "Max users cannot be <= 0")
	}
	// If you pass in IDs, they must be less than MaxUsers
	if len(req.OrbCustomerIds) > 0 && req.MaxUsers < int32(len(req.OrbCustomerIds)) {
		return nil, status.Error(codes.FailedPrecondition, "Max users cannot be less than the number of customers provided")
	}
	// We don't use created before when there's specific IDs, fail to prevent confusion around what is actually happening
	if len(req.OrbCustomerIds) > 0 && req.CustomerCreatedBeforeTime != nil {
		return nil, status.Error(codes.FailedPrecondition, "Cannot request backfill for specific customers and a customer created before time")
	}

	authClaims, _ := auth.GetAugmentClaims(ctx)
	requestContext, _ := requestcontext.FromGrpcContext(ctx)
	s.auditLogger.WriteAuditLog(
		authClaims,
		fmt.Sprintf("BackfillAddresses request received"),
		requestContext,
		audit.NewProtoRequest(req),
	)

	wg := sync.WaitGroup{}
	var error error
	wg.Add(1)

	results := &BackfillAddressResults{}

	go func() {
		defer wg.Done()
		backgroundCtx := authClaims.NewContext(context.Background())

		if len(req.OrbCustomerIds) > 0 {
			log.Ctx(ctx).Info().Int("num_customers", len(req.OrbCustomerIds)).Msg("BackfillAddresses request received for specific customers")
			for i, orbCustomerID := range req.OrbCustomerIds {
				log.Ctx(ctx).Info().Str("customer_id", orbCustomerID).Msgf("Processing customer %d", i+1)
				customer, err := s.orbClient.GetCustomerInfo(backgroundCtx, orbCustomerID)
				if err != nil {
					log.Ctx(backgroundCtx).Error().Err(err).Str("customer_id", orbCustomerID).Msg("Failed to get customer info")
					results.FailedCustomers = append(results.FailedCustomers, orbCustomerID)
					continue
				}
				if customer == nil {
					log.Ctx(backgroundCtx).Error().Str("customer_id", orbCustomerID).Msg("Failed to get customer info")
					results.FailedCustomers = append(results.FailedCustomers, orbCustomerID)
					continue
				}
				err = s.syncCustomerAddress(backgroundCtx, *customer, req.DryRun, results)
				if err != nil {
					log.Ctx(backgroundCtx).Error().Err(err).Str("customer_id", orbCustomerID).Msg("Failed to sync customer address")
					// Error already recorded in results by syncCustomerAddress
				}
				// Sleep between processing each customer to avoid rate limits
				time.Sleep(50 * time.Millisecond)
			}
		} else {
			log.Ctx(ctx).Info().Msg("BackfillAddresses request received for all customers")
			cursor := ""
			counter := 0
			maxUsers := int(req.MaxUsers)

			// Convert protobuf timestamp to Go time.Time if provided
			var timeBefore *time.Time
			if req.CustomerCreatedBeforeTime != nil {
				t := req.CustomerCreatedBeforeTime.AsTime()
				timeBefore = &t
			}

			for {
				if counter >= maxUsers {
					break
				}

				// Fetch customers in batches of max 100 (or remaining if less than 100),
				// 100 is the max size from Orb
				batchSize := int64(100)
				remaining := maxUsers - counter
				if remaining < 100 {
					batchSize = int64(remaining)
				}

				log.Ctx(ctx).Info().Msgf("Fetching batch of customers, cursor=%s, batchSize=%d", cursor, batchSize)
				customersPage, err := s.orbClient.ListCustomers(backgroundCtx, cursor, batchSize, timeBefore)
				if err != nil {
					log.Ctx(backgroundCtx).Error().Err(err).Msg("Failed to list customers")
					error = err
					return
				}

				log.Ctx(ctx).Info().Msgf("Retrieved %d customers in this batch", len(customersPage.Customers))

				for _, customer := range customersPage.Customers {
					counter++
					log.Ctx(ctx).Info().Msgf("Processing customer %d, Orb ID %s", counter, customer.OrbCustomerID)

					err := s.syncCustomerAddress(backgroundCtx, customer, req.DryRun, results)
					if err != nil {
						log.Ctx(backgroundCtx).Error().Err(err).Str("customer_id", customer.OrbCustomerID).Msg("Failed to sync customer address")
						// Error already recorded in results by syncCustomerAddress
					}
					// Sleep between processing each customer to avoid rate limits
					time.Sleep(50 * time.Millisecond)
				}
				if !customersPage.HasMore || customersPage.NextCursor == nil {
					log.Ctx(ctx).Info().Msg("No more customers to process")
					break
				}
				// Sleep between reading pages to avoid rate limits
				time.Sleep(50 * time.Millisecond)

				cursor = *customersPage.NextCursor
				log.Ctx(ctx).Info().Msgf("Moving to next page of users")
			}
		}
	}()

	wg.Wait()

	if error != nil {
		return nil, error
	}

	log.Ctx(ctx).Info().Int("failed_customers", len(results.FailedCustomers)).Int("successful_customers", len(results.SuccessfulCustomers)).Int("no_stripe_address_customers", len(results.NoStripeAddressCustomers)).Int("unchanged_customers", len(results.UnchangedCustomers)).Msg("BackfillAddresses request completed")
	log.Ctx(ctx).Info().Interface("failed_customer_ids", results.FailedCustomers).Interface("successful_customer_ids", results.SuccessfulCustomers).Interface("no_stripe_address_customer_ids", results.NoStripeAddressCustomers).Interface("unchanged_customer_ids", results.UnchangedCustomers).Msg("BackfillAddresses request results")
	return &authpb.BackfillAddressesResponse{
		SuccessfulCustomerIds:      results.SuccessfulCustomers,
		FailedCustomerIds:          results.FailedCustomers,
		NoStripeAddressCustomerIds: results.NoStripeAddressCustomers,
		UnchangedCustomerIds:       results.UnchangedCustomers,
	}, nil
}

type CustomerSyncResult string

const (
	CustomerSyncSuccessful          CustomerSyncResult = "successful"
	CustomerSyncFailed              CustomerSyncResult = "failed"
	CustomerSyncNoStripeAddress     CustomerSyncResult = "no_stripe_address"
	CustomerSyncOrbAddressUnchanged CustomerSyncResult = "orb_address_unchanged"
)

// BackfillAddressResults holds the categorized results of address backfill operations
type BackfillAddressResults struct {
	SuccessfulCustomers      []string
	FailedCustomers          []string
	NoStripeAddressCustomers []string
	UnchangedCustomers       []string
}

func (s *AuthGrpcServer) syncCustomerAddress(ctx context.Context, customer orb.OrbCustomerInfo, dryRun bool, results *BackfillAddressResults) error {
	var syncResult CustomerSyncResult

	if !billingAddressEmpty(customer.BillingAddress) {
		log.Ctx(ctx).Info().Str("customer_id", customer.OrbCustomerID).Msg("Customer already has an address in Orb, skipping")
		syncResult = CustomerSyncOrbAddressUnchanged
	} else if customer.StripeCustomerID == "" {
		log.Ctx(ctx).Error().Str("customer_id", customer.OrbCustomerID).Msg("Customer does not have a Stripe customer ID")
		syncResult = CustomerSyncFailed
	} else {
		stripeCustomer, err := s.stripeClient.GetCustomer(customer.StripeCustomerID)
		if err != nil {
			log.Ctx(ctx).Error().Err(err).Str("customer_id", customer.OrbCustomerID).Msg("Failed to get Stripe customer")
			syncResult = CustomerSyncFailed
			// Add to results and return error
			results.FailedCustomers = append(results.FailedCustomers, customer.OrbCustomerID)
			return err
		}

		var addressToSync *stripe.Address
		if stripeCustomer.Address != nil {
			// First, check if the customer has an address in Stripe. If they do it should have been synced to Orb, but this should be the first one we check regardless
			addressToSync = stripeCustomer.Address
		} else {
			// Otherwise, check the customer's payment methods to find an address
			paymentMethods, err := s.stripeClient.GetPaymentMethodsForCustomer(customer.StripeCustomerID)
			if err != nil {
				log.Ctx(ctx).Error().Err(err).Str("customer_id", customer.OrbCustomerID).Msg("Failed to get payment methods for customer")
				syncResult = CustomerSyncFailed
				results.FailedCustomers = append(results.FailedCustomers, customer.OrbCustomerID)
				return err
			}
			// Get the most recent payment method
			var mostRecentPaymentMethod *stripe.PaymentMethod
			for _, paymentMethod := range paymentMethods {
				if mostRecentPaymentMethod == nil || paymentMethod.Created >= mostRecentPaymentMethod.Created {
					mostRecentPaymentMethod = paymentMethod
				}
			}
			if mostRecentPaymentMethod != nil && mostRecentPaymentMethod.BillingDetails != nil && mostRecentPaymentMethod.BillingDetails.Address != nil {
				addressToSync = mostRecentPaymentMethod.BillingDetails.Address
			}
		}

		if addressToSync == nil {
			log.Ctx(ctx).Info().Str("customer_id", customer.OrbCustomerID).Msg("Customer does not have an address in Stripe")
			// TODO: consider if we want to do anything here based on the user's current plan. For now, leave it as is. In the future we can consider
			// removing payment methods, etc to force user to add billing address when they reenter payment depending on subscription status (inactive vs free vs paid)
			syncResult = CustomerSyncNoStripeAddress
		} else {
			if !dryRun {
				log.Ctx(ctx).Info().Str("customer_id", customer.OrbCustomerID).Msg("Updating customer's address in Orb")
				err := s.orbClient.UpdateCustomerInformation(ctx, customer.OrbCustomerID,
					orb.OrbCustomerUpdateInfo{
						Address: &orb.Address{
							Line1:      addressToSync.Line1,
							Line2:      addressToSync.Line2,
							City:       addressToSync.City,
							State:      addressToSync.State,
							PostalCode: addressToSync.PostalCode,
							Country:    addressToSync.Country,
						},
					})
				if err != nil {
					log.Ctx(ctx).Error().Err(err).Str("customer_id", customer.OrbCustomerID).Msg("Failed to update customer billing address")
					syncResult = CustomerSyncFailed
					results.FailedCustomers = append(results.FailedCustomers, customer.OrbCustomerID)
					return err
				}
			} else {
				log.Ctx(ctx).Info().Str("customer_id", customer.OrbCustomerID).Msg("Dry run, would update customer's address in Orb")
			}
			syncResult = CustomerSyncSuccessful
		}
	}

	// Add result to appropriate slice
	switch syncResult {
	case CustomerSyncSuccessful:
		results.SuccessfulCustomers = append(results.SuccessfulCustomers, customer.OrbCustomerID)
	case CustomerSyncFailed:
		results.FailedCustomers = append(results.FailedCustomers, customer.OrbCustomerID)
	case CustomerSyncNoStripeAddress:
		results.NoStripeAddressCustomers = append(results.NoStripeAddressCustomers, customer.OrbCustomerID)
	case CustomerSyncOrbAddressUnchanged:
		results.UnchangedCustomers = append(results.UnchangedCustomers, customer.OrbCustomerID)
	}
	return nil
}

// Chhecks if the billing address is empty
func billingAddressEmpty(address *orb.Address) bool {
	return address == nil || (address.Line1 == "" && address.Line2 == "" && address.City == "" && address.State == "" && address.PostalCode == "" && address.Country == "")
}
