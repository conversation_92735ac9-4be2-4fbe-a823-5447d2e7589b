package main

import (
	"fmt"
	"math"
	"time"

	auth_entities "github.com/augmentcode/augment/services/auth/central/server/auth_entities_proto"
	orb_config "github.com/augmentcode/augment/services/billing/lib/orb/config"
	"google.golang.org/protobuf/types/known/timestamppb"
)

// Constants for feature gating messages and configuration
const (
	// Threshold for showing subscription ending warning
	SUBSCRIPTION_ENDING_WARNING_THRESHOLD_DAYS = 14

	// Warning message templates for different feature gating scenarios
	INACTIVE_SUBSCRIPTION_WARNING_TEMPLATE = "Your subscription is no longer active"

	USAGE_BALANCE_DEPLETED_WARNING_TEMPLATE = "You are out of %s"

	SUBSCRIPTION_ENDING_WARNING = "Your subscription expires in %d day%s"

	// Markdown message templates for different feature gating scenarios
	// TODO: replace https://app.augmentcode.com/account with the correct links for different env
	SUBSCRIPTION_ENDING_DISCLAIMER_MKDOWN_MESSAGE = "\n\n---\n\n🚨 **Your access expires in %d day%s. [Purchase a subscription](https://app.augmentcode.com/account?utm_source=vscode&utm_medium=in_agent_conversation&utm_campaign=end_of_trial_reminder&utm_content=purchase-a-subscription)** 🚨"

	// Separate templates for with and without user email to address internationalization concerns
	INACTIVE_SUBSCRIPTION_DISCLAIMER_MKDOWN_MESSAGE_WITH_EMAIL = "**⚠️ Your subscription for %s is inactive. Please update your plan [here](https://app.augmentcode.com/account?utm_source=vscode&utm_medium=in_agent_conversation&utm_campaign=inactive_subscription_reminder&utm_content=here) to continue using Augment. ⚠️**"

	INACTIVE_SUBSCRIPTION_DISCLAIMER_MKDOWN_MESSAGE_WITHOUT_EMAIL = "**⚠️ Your subscription is inactive. Please update your plan [here](https://app.augmentcode.com/account?utm_source=vscode&utm_medium=in_agent_conversation&utm_campaign=inactive_subscription_reminder&utm_content=here) to continue using Augment. ⚠️**"

	OUT_OF_USAGE_CREDITS_DISCLAIMER_MKDOWN_MESSAGE_WITH_EMAIL = "*You are out of %s for %s. Please update your account [here](https://app.augmentcode.com/account) to continue using Augment.*"

	OUT_OF_USAGE_CREDITS_DISCLAIMER_MKDOWN_MESSAGE_WITHOUT_EMAIL = "*You are out of %s. Please update your account [here](https://app.augmentcode.com/account) to continue using Augment.*"
)

// Helper functions to generate warning messages using fmt.Sprintf

// convertDurationToDays converts a time duration to days using the same logic as date-fns formatDistance
// This ensures consistency with frontend display by using a 42-hour threshold (2520 minutes)
// - If duration < 42 hours: returns 1 day
// - If duration >= 42 hours: returns Math.round(minutes / 1440) days
func convertDurationToDays(duration time.Duration) int {
	minutes := duration.Minutes()

	if minutes < 2520 { // Less than 42 hours
		return 1
	} else {
		// Round minutes to days (same as date-fns: Math.round(minutes / minutesInDay))
		return int(math.Round(minutes / 1440)) // 1440 = minutesInDay
	}
}

func getUsageBalanceDepletedWarning(usageUnitDisplayName, userEmail string) string {
	return fmt.Sprintf(USAGE_BALANCE_DEPLETED_WARNING_TEMPLATE, usageUnitDisplayName)
}

func getSubscriptionEndingWarning(daysRemaining int) string {
	plural := ""
	if daysRemaining != 1 {
		plural = "s"
	}
	return fmt.Sprintf(SUBSCRIPTION_ENDING_WARNING, daysRemaining, plural)
}

func getInactiveSubscriptionWarning(userEmail string) string {
	return INACTIVE_SUBSCRIPTION_WARNING_TEMPLATE
}

func getUsageBalanceDepletedMarkdownMessage(usageUnitDisplayName, userEmail string) string {
	if userEmail != "" {
		return fmt.Sprintf(OUT_OF_USAGE_CREDITS_DISCLAIMER_MKDOWN_MESSAGE_WITH_EMAIL, usageUnitDisplayName, userEmail)
	}
	return fmt.Sprintf(OUT_OF_USAGE_CREDITS_DISCLAIMER_MKDOWN_MESSAGE_WITHOUT_EMAIL, usageUnitDisplayName)
}

func getSubscriptionEndingSuffixMessage(daysRemaining int) string {
	plural := ""
	if daysRemaining != 1 {
		plural = "s"
	}
	return fmt.Sprintf(SUBSCRIPTION_ENDING_DISCLAIMER_MKDOWN_MESSAGE, daysRemaining, plural)
}

func getInactiveSubscriptionMarkdownMessage(userEmail string) string {
	if userEmail != "" {
		return fmt.Sprintf(INACTIVE_SUBSCRIPTION_DISCLAIMER_MKDOWN_MESSAGE_WITH_EMAIL, userEmail)
	}
	return fmt.Sprintf(INACTIVE_SUBSCRIPTION_DISCLAIMER_MKDOWN_MESSAGE_WITHOUT_EMAIL)
}

// buildFeatureGatingInfo creates feature gating information based on subscription type and user state
func buildFeatureGatingInfo(
	orbConfig *orb_config.OrbConfig,
	subscriptionType SubscriptionType,
	// Note: we "fake" an ActiveSubscription when user's subscription is pending creation, the subscription passed in
	// can be nil even if subscriptionType is ActiveSubscription, should always check subscription != nil before using it
	subscription *auth_entities.Subscription,
	user *auth_entities.User,
) *auth_entities.FeatureGatingInfo {
	var featureControls []*auth_entities.FeatureControl

	userEmail := ""
	if user != nil && user.Email != "" {
		userEmail = user.Email
	}

	switch s := subscriptionType.(type) {
	case EnterpriseSubscription:
		// Enterprise users have all features enabled
		featureControls = append(featureControls, &auth_entities.FeatureControl{
			FeatureTypes: []auth_entities.FeatureControl_FeatureType{auth_entities.FeatureControl_ALL_FEATURES},
			State:        auth_entities.FeatureControl_ENABLED,
		})

	case ActiveSubscription:
		if s.ActiveSubscription.UsageBalanceDepleted {
			// Usage balance depleted - disable specific features (Chat, Agent, etc.)
			usageUnitDisplayName := orbConfig.DefaultUsageUnitDisplayName
			if subscription != nil {
				plan := orbConfig.GetPlan(subscription.ExternalPlanId)
				if plan != nil {
					usageUnitDisplayName = plan.DisplayInfo.UsageUnitDisplayName
				}
			}

			warningMsg := getUsageBalanceDepletedWarning(usageUnitDisplayName, userEmail)
			mkdownMsg := getUsageBalanceDepletedMarkdownMessage(usageUnitDisplayName, userEmail)

			// Disable specific features that require usage credits
			disabledFeatureControl := &auth_entities.FeatureControl{
				FeatureTypes: []auth_entities.FeatureControl_FeatureType{
					auth_entities.FeatureControl_CHAT,
					auth_entities.FeatureControl_AGENT,
					auth_entities.FeatureControl_REMOTE_AGENT,
					auth_entities.FeatureControl_CLI_AGENT,
				},
				State:          auth_entities.FeatureControl_DISABLED,
				DisabledReason: auth_entities.FeatureControl_USAGE_BALANCE_DEPLETED,
				DisplayInfo: &auth_entities.FeatureControlDisplayInfo{
					WarningMessage: warningMsg,
					MkdownMessage:  mkdownMsg,
				},
			}
			featureControls = append(featureControls, disabledFeatureControl)

			// Enable all other features as fallback
			enabledFeatureControl := &auth_entities.FeatureControl{
				FeatureTypes: []auth_entities.FeatureControl_FeatureType{auth_entities.FeatureControl_ALL_FEATURES},
				State:        auth_entities.FeatureControl_ENABLED,
			}
			featureControls = append(featureControls, enabledFeatureControl)
		} else {
			// Active subscription with credits - features enabled
			featureControl := &auth_entities.FeatureControl{
				FeatureTypes: []auth_entities.FeatureControl_FeatureType{auth_entities.FeatureControl_ALL_FEATURES},
				State:        auth_entities.FeatureControl_ENABLED,
			}

			// Show warning if subscription is ending soon
			warningMsg := ""
			suffixMsg := ""
			if s.ActiveSubscription.EndDate != nil {
				featureControl.EndDate = s.ActiveSubscription.EndDate

				// Calculate days until end using date-fns compatible logic
				duration := s.ActiveSubscription.EndDate.AsTime().Sub(time.Now())
				daysUntilEndInt := convertDurationToDays(duration)

				if daysUntilEndInt <= SUBSCRIPTION_ENDING_WARNING_THRESHOLD_DAYS && daysUntilEndInt > 0 {
					warningMsg = getSubscriptionEndingWarning(daysUntilEndInt)
					suffixMsg = getSubscriptionEndingSuffixMessage(daysUntilEndInt)
				}
			}

			if warningMsg != "" || suffixMsg != "" {
				featureControl.DisplayInfo = &auth_entities.FeatureControlDisplayInfo{
					WarningMessage: warningMsg,
					ReminderSuffix: suffixMsg,
				}
			}

			featureControls = append(featureControls, featureControl)
		}

	case TrialSubscription:
		// Trial subscription - features enabled but show ending warning
		warningMsg := ""
		suffixMsg := ""

		var endDate *timestamppb.Timestamp

		if s.Trial.TrialEnd != nil {
			// Calculate days until end using date-fns compatible logic
			duration := s.Trial.TrialEnd.AsTime().Sub(time.Now())
			daysUntilEndInt := convertDurationToDays(duration)

			if daysUntilEndInt <= SUBSCRIPTION_ENDING_WARNING_THRESHOLD_DAYS && daysUntilEndInt > 0 {
				warningMsg = getSubscriptionEndingWarning(daysUntilEndInt)
				suffixMsg = getSubscriptionEndingSuffixMessage(daysUntilEndInt)
				endDate = s.Trial.TrialEnd
			}
		}

		featureControl := &auth_entities.FeatureControl{
			FeatureTypes: []auth_entities.FeatureControl_FeatureType{auth_entities.FeatureControl_ALL_FEATURES},
			State:        auth_entities.FeatureControl_ENABLED,
			EndDate:      endDate,
		}

		if warningMsg != "" || suffixMsg != "" {
			featureControl.DisplayInfo = &auth_entities.FeatureControlDisplayInfo{
				WarningMessage: warningMsg,
				ReminderSuffix: suffixMsg,
			}
		}
		featureControls = append(featureControls, featureControl)

	case InactiveSubscription:
		// Inactive subscription - all features disabled
		warningMsg := getInactiveSubscriptionWarning(userEmail)
		mkdownMsg := getInactiveSubscriptionMarkdownMessage(userEmail)

		// Disable all chat features with a warning and markdown message
		disableChatFeatureControl := &auth_entities.FeatureControl{
			FeatureTypes: []auth_entities.FeatureControl_FeatureType{
				auth_entities.FeatureControl_CHAT,
				auth_entities.FeatureControl_AGENT,
				auth_entities.FeatureControl_REMOTE_AGENT,
				auth_entities.FeatureControl_CLI_AGENT,
			},
			State:          auth_entities.FeatureControl_DISABLED,
			DisabledReason: auth_entities.FeatureControl_INACTIVE_SUBSCRIPTION,
			DisplayInfo: &auth_entities.FeatureControlDisplayInfo{
				WarningMessage: warningMsg,
				MkdownMessage:  mkdownMsg,
			},
		}

		// Disable all other features with no message specified (these will 402)
		// TODO: in the future, we may want to set a different field like DisplayMessages to show that no message should be displayed and instead we should directly 402
		disableAllFeatureControl := &auth_entities.FeatureControl{
			FeatureTypes:   []auth_entities.FeatureControl_FeatureType{auth_entities.FeatureControl_ALL_FEATURES},
			State:          auth_entities.FeatureControl_DISABLED,
			DisabledReason: auth_entities.FeatureControl_INACTIVE_SUBSCRIPTION,
		}

		featureControls = append(featureControls, disableChatFeatureControl, disableAllFeatureControl)
	}

	return &auth_entities.FeatureGatingInfo{
		FeatureControls: featureControls,
	}
}
