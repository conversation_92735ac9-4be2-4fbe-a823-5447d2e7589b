package main

import (
	"context"
	"fmt"
	"math"
	"math/rand"
	"slices"
	"strconv"
	"strings"
	"sync"
	"time"

	featureflags "github.com/augmentcode/augment/base/feature_flags"
	"github.com/augmentcode/augment/base/logging/audit"
	"github.com/augmentcode/augment/services/billing/lib/orb"
	orb_config "github.com/augmentcode/augment/services/billing/lib/orb/config"
	stripelib "github.com/augmentcode/augment/services/billing/lib/stripe"
	"github.com/augmentcode/augment/services/lib/grpc/auth"
	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
	ripb "github.com/augmentcode/augment/services/request_insight/proto"
	ripublisher "github.com/augmentcode/augment/services/request_insight/publisher"
	tw_pb "github.com/augmentcode/augment/services/tenant_watcher/proto"
	tenantutil "github.com/augmentcode/augment/services/tenant_watcher/util"
	tokenscopesproto "github.com/augmentcode/augment/services/token_exchange/token_scopes_proto"
	"github.com/google/uuid"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/rs/zerolog"
	"github.com/rs/zerolog/log"
	"golang.org/x/sync/errgroup"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/timestamppb"

	auth_entities "github.com/augmentcode/augment/services/auth/central/server/auth_entities_proto"
	auth_internal "github.com/augmentcode/augment/services/auth/central/server/auth_internal_proto"
	authpb "github.com/augmentcode/augment/services/auth/central/server/proto"
	orblib "github.com/orbcorp/orb-go"
)

// RandomSelector interface for dependency injection of random number generation
type RandomSelector interface {
	Intn(n int) int
}

// DefaultRandomSelector implements RandomSelector using math/rand
type DefaultRandomSelector struct{}

func (d *DefaultRandomSelector) Intn(n int) int {
	return rand.Intn(n)
}

// If false, disable all team mangement endpoints. This is intended to be a quick off switch for
// emergencies; feature rollout should be handled by frontend flags.
var teamManagementEnabled = featureflags.NewBoolFlag("auth_central_team_management_enabled", true)

// If false, InviteUsersToTenant requests will be rejected if any email contains a +.
var inviteUsersToTenantPlusAllowed = featureflags.NewBoolFlag("auth_central_invite_users_to_tenant_plus_allowed", false)

// If false, team management operations will not be blocked during subscription/plan changes.
var subscriptionChangeBlockingEnabled = featureflags.NewBoolFlag("team_management_subscription_change_blocking_enabled", true)

// If false, the Windsurf promotion will not be enabled.
var windsurfPromotionEnabled = featureflags.NewBoolFlag("auth_central_windsurf_promotion_enabled", false)

// If false, the Cursor promotion will not be enabled.
var cursorPromotionEnabled = featureflags.NewBoolFlag("auth_central_cursor_promotion_enabled", false)

// Promotion types
type PromotionType string

const (
	PromotionTypeSignup PromotionType = "signup-promo"
)

// Promotion configuration
type PromotionConfig struct {
	Type         PromotionType
	DisplayName  string
	Description  string
	FeatureFlag  *featureflags.BoolFlag
	Credits      float64
	ExpiryMonths int
}

// Constants for promotions
// reCAPTCHA threshold for promotions. If the score is below this threshold, the promotion will not be granted.
var promotionRecaptchaThreshold = featureflags.NewFloatFlag("auth_central_promotion_recaptcha_threshold", 0.0)

// If false, the pending changes checkout flow will not be enabled.
var pendingChangesEnabled = featureflags.NewBoolFlag("team_management_pending_changes_enabled", true)

// Constants for Windsurf promotion
const (
	windsurfPromotionName          = "windsurf_2025"
	cursorPromotionName            = "cursor_2025"
	promotionCredits               = 600.0           // Number of credits to grant
	promotionExpiryMonths          = 1               // Credits expire in 1 month
	promotionNewUserWindow         = 5 * time.Minute // Users must be created in the last 5 minutes to be eligible
	maxPromotionEnrollmentAttempts = 5               // Maximum number of attempts to enroll for a promotion
	promotionRecaptchaAction       = "promotion_upload"
)

// Promotion map - maps promotion names to their configuration
var promotionMap = map[string]PromotionConfig{
	windsurfPromotionName: {
		Type:         PromotionTypeSignup,
		DisplayName:  "windsurf",
		Description:  "Windsurf 2025 promotion",
		FeatureFlag:  windsurfPromotionEnabled,
		Credits:      promotionCredits,
		ExpiryMonths: promotionExpiryMonths,
	},
	cursorPromotionName: {
		Type:         PromotionTypeSignup,
		DisplayName:  "cursor",
		Description:  "Cursor 2025 promotion",
		FeatureFlag:  cursorPromotionEnabled,
		Credits:      promotionCredits,
		ExpiryMonths: promotionExpiryMonths,
	},
}

var promotionProcessingTotal = prometheus.NewCounterVec(
	prometheus.CounterOpts{
		Name: "au_auth_central_promotion_processing_total",
		Help: "Total number of promotion processing attempts by type and outcome",
	},
	[]string{"promotion_type", "status", "reason"},
)

func init() {
	prometheus.MustRegister(promotionProcessingTotal)
}

type TeamManagementServer struct {
	daoFactory              *DAOFactory
	tenantMap               *TenantMap
	auditLogger             *audit.AuditLogger
	requestInsightPublisher ripublisher.RequestInsightPublisher
	featureFlagHandle       featureflags.FeatureFlagHandle
	asyncOpsPublisher       AsyncOpsPublisher
	stripeClient            stripelib.StripeClient
	orbConfig               *orb_config.OrbConfig
	orbClient               orb.OrbClient
	randomSelector          RandomSelector
	recaptchaValidator      RecaptchaValidator
}

func NewTeamManagementServer(
	featureFlagHandle featureflags.FeatureFlagHandle,
	daoFactory *DAOFactory,
	tenantMap *TenantMap,
	auditLogger *audit.AuditLogger,
	requestInsightPublisher ripublisher.RequestInsightPublisher,
	asyncOpsPublisher AsyncOpsPublisher,
	stripeClient stripelib.StripeClient,
	orbConfig *orb_config.OrbConfig,
	orbClient orb.OrbClient,
	randomSelector RandomSelector,
	recaptchaValidator RecaptchaValidator,
) *TeamManagementServer {
	// Use default random selector if none provided
	if randomSelector == nil {
		randomSelector = &DefaultRandomSelector{}
	}

	return &TeamManagementServer{
		daoFactory:              daoFactory,
		tenantMap:               tenantMap,
		auditLogger:             auditLogger,
		requestInsightPublisher: requestInsightPublisher,
		featureFlagHandle:       featureFlagHandle,
		asyncOpsPublisher:       asyncOpsPublisher,
		stripeClient:            stripeClient,
		orbConfig:               orbConfig,
		orbClient:               orbClient,
		randomSelector:          randomSelector,
		recaptchaValidator:      recaptchaValidator,
	}
}

// Returns an error that can be returned directly to the caller if provided gRPC context does not
// contain auth claims that give access to the provided fields. Leave tenantID nil for requests that
// don't require tenant validation and userID nil for requests that don't require user validation.
func (s *TeamManagementServer) teamManagementAuthCheck(
	ctx context.Context,
	tenantID *string,
	userID *string,
	requiredScope tokenscopesproto.Scope,
	adminOnly bool,
	apiName string,
) error {
	authClaims, ok := auth.GetAugmentClaims(ctx)
	if !ok {
		log.Ctx(ctx).Error().Msgf("Failed to get auth claims from context")
		return status.Error(codes.PermissionDenied, "Invalid context")
	}

	if !authClaims.HasScope(requiredScope) {
		log.Ctx(ctx).Error().Msgf("Auth claims do not give have scope %s", requiredScope)
		return status.Error(codes.PermissionDenied, "Access denied")
	}

	iapEmail, isIapUser := authClaims.GetIapEmail()

	if tenantID != nil {
		// Validate that the tenant from the request matches the tenant from the auth claims.
		if *tenantID == "" {
			if !authClaims.AllowsAllTenants() {
				log.Ctx(ctx).Error().Msgf("Auth claims give permission for tenant %s, but request requires access to all tenants", tenantID)
				return status.Error(codes.PermissionDenied, "Access denied")
			}
		} else if !authClaims.IsTenantAllowed(*tenantID) {
			log.Ctx(ctx).Error().Msgf("Auth claims give permission for tenant %s, but request has tenant %s", tenantID, *tenantID)
			return status.Error(codes.PermissionDenied, "Access denied")
		}
	}

	if userID != nil {
		if isIapUser {
			log.Ctx(ctx).Info().Msgf("Skipping user validation for IAP user %s", iapEmail)
		} else if authClaims.ServiceName != "" {
			log.Ctx(ctx).Info().Msgf("Skipping user validation for service token %s", authClaims.ServiceName)
		} else {
			// Validate that the user from the request matches the user from the auth claims.
			claimsAugmentUserID := augmentUserID(authClaims)
			if claimsAugmentUserID == "" {
				log.Ctx(ctx).Error().Msgf("Auth claims do not have an AUGMENT user ID")
				return status.Error(codes.PermissionDenied, "Access denied")
			}

			if *userID == "" {
				log.Ctx(ctx).Error().Msgf("Invalid (empty) user ID")
				return status.Error(codes.InvalidArgument, "Access denied")
			} else if *userID != claimsAugmentUserID {
				log.Ctx(ctx).Error().Msgf(
					"Auth claims user ID %s does not match requested user ID %s",
					claimsAugmentUserID, *userID)
				return status.Error(codes.PermissionDenied, "Access denied")
			}
		}
	}

	if adminOnly {
		if isIapUser {
			log.Ctx(ctx).Info().Msgf("Skipping admin validation for IAP user %s", iapEmail)
		} else if authClaims.ServiceName != "" {
			log.Ctx(ctx).Info().Msgf("Skipping admin validation for service token %s", authClaims.ServiceName)
		} else {
			augmentUserID := augmentUserID(authClaims)
			if augmentUserID == "" {
				log.Ctx(ctx).Error().Msgf("Auth claims do not have an AUGMENT user ID")
				return status.Error(codes.PermissionDenied, "Access denied")
			}
			if authClaims.TenantName == "" {
				log.Ctx(ctx).Error().Msgf("Auth claims do not have a tenant name")
				return status.Error(codes.PermissionDenied, "Access denied")
			}
			tenantMappingDAO := s.daoFactory.GetUserTenantMappingDAO(authClaims.TenantName)
			mapping, err := tenantMappingDAO.GetByUser(ctx, augmentUserID)
			if err != nil {
				log.Ctx(ctx).Error().Err(err).Msgf("Failed to get user mapping")
				return status.Error(codes.Internal, "Failed to get user mapping")
			} else if mapping == nil {
				log.Ctx(ctx).Error().Msgf("User %s is not in tenant %s", augmentUserID, authClaims.TenantID)
				return status.Error(codes.PermissionDenied, "Access denied")
			}

			isAdmin := false
			roles := mapping.CustomerUiRoles
			for _, role := range roles {
				if role == auth_entities.CustomerUiRole_ADMIN {
					isAdmin = true
					break
				}
			}
			if !isAdmin {
				log.Ctx(ctx).Error().Msgf("User %s is not an admin", augmentUserID)
				return status.Error(codes.PermissionDenied, "Access denied")
			}
		}
	}

	return nil
}

// teamManagementWildcardAuthCheck performs auth checks for endpoints that require wildcard tenant access.
// This is a specialized version of teamManagementAuthCheck for operations that need to access all tenants.
func (s *TeamManagementServer) teamManagementWildcardAuthCheck(
	ctx context.Context,
	requiredScope tokenscopesproto.Scope,
	apiName string,
) error {
	authClaims, ok := auth.GetAugmentClaims(ctx)
	if !ok {
		log.Ctx(ctx).Error().Msgf("Failed to get auth claims from context")
		return status.Error(codes.PermissionDenied, "Invalid context")
	}

	// Check that the token has the required scope
	if !authClaims.HasScope(requiredScope) {
		log.Ctx(ctx).Error().Msgf("Auth claims do not have required scope %s for %s", requiredScope, apiName)
		return status.Error(codes.PermissionDenied, "Access denied")
	}

	// Check that the token allows access to all tenants
	if !authClaims.AllowsAllTenants() {
		log.Ctx(ctx).Error().Msgf("Auth claims give permission for tenant %s, but %s requires access to all tenants", authClaims.TenantID, apiName)
		return status.Error(codes.PermissionDenied, "Access denied")
	}

	return nil
}

// Get the AUGMENT user ID from auth claims, if any. Returns the empty string if the given claims
// don't have an AUGMENT user ID.
func augmentUserID(authClaims *auth.AugmentClaims) string {
	claimsOpaqueUserID := authClaims.GetOpaqueUserID()
	if claimsOpaqueUserID == nil {
		return ""
	} else if claimsOpaqueUserID.UserIdType != auth_entities.UserId_AUGMENT {
		return ""
	} else {
		return claimsOpaqueUserID.UserId
	}
}

func (s *TeamManagementServer) teamManagementEnabled() bool {
	enabled, err := teamManagementEnabled.Get(s.featureFlagHandle)
	if err != nil {
		log.Warn().Err(err).Msgf("Failed to get teamManagementEnabled feature flag, defaulting to true")
		enabled = true
	}
	if !enabled {
		log.Warn().Msgf("Team management is disabled")
	}
	return enabled
}

func (s *TeamManagementServer) WriteTeamsAuditLog(ctx context.Context, message string, extras ...audit.AuditLogExtra) {
	authInfo, ok := auth.GetAugmentClaims(ctx)
	if !ok {
		log.Ctx(ctx).Error().Msg("Failed to get auth claims")
		return
	}
	s.auditLogger.WriteAuditLog(
		authInfo,
		message,
		extras...,
	)
}

// TeamTenantCreationEligibility represents the result of checking if a user can create a team tenant
type TeamTenantCreationEligibility struct {
	Allowed bool
	// ExistingTeamTenantID is set only when the user already belongs to a self-serve team tenant
	ExistingTeamTenantID string
}

// checkTeamTenantCreationEligibility determines if a user can create a team tenant
// Returns the eligibility result or an error if the user is not eligible
func isAllowedToCreateTeamTenant(ctx context.Context, user *auth_entities.User, daoFactory *DAOFactory, tenantMap *TenantMap, featureFlagHandle featureflags.FeatureFlagHandle, targetTenantID string) (*TeamTenantCreationEligibility, error) {
	// Check for any plan/tier changes in progress before proceeding
	if changeInProgress, changeDescription, err := CheckTierOrPlanChanges(ctx, daoFactory, tenantMap, featureFlagHandle, user.Id); err != nil {
		log.Ctx(ctx).Error().Str("user_id", user.Id).Err(err).Msgf("Error checking for plan or tier change in progress for user %s", user.Id)
		return nil, fmt.Errorf("isAllowedToCreateTeamTenant: failed to check for plan or tier change in progress: %w", err)
	} else if changeInProgress {
		log.Ctx(ctx).Error().Str("user_id", user.Id).Str("change_description", changeDescription).Msg("Blocking isAllowedToCreateTeamTenant due to plan or tier change in progress")
		return nil, status.Error(codes.FailedPrecondition, "User has a plan or tier change in progress.")
	}

	// Check if user belongs to at least one tenant
	if len(user.Tenants) == 0 {
		log.Ctx(ctx).Error().Msgf("User %s does not belong to any tenant", user.Id)
		return nil, status.Error(codes.FailedPrecondition, "User does not belong to any tenant")
	}

	// Check if user belongs to any enterprise, community, or self-serve team tenants
	for _, tenantId := range user.Tenants {
		if tenantId == targetTenantID {
			// The target tenant is the one we're creating and adding the user to, so skip it
			// This can happen due to retrying the request after failures
			log.Ctx(ctx).Info().Msgf("Skip checking target tenant %s in user's tenants list", targetTenantID)
			continue
		}
		tenant, err := tenantMap.GetTenantByID(ctx, tenantId)
		if err != nil || tenant == nil {
			log.Ctx(ctx).Error().Err(err).Msgf("Failed to get tenant by id %s", tenantId)
			return nil, status.Error(codes.Internal, "Failed to get tenant by id")
		}
		if tenantutil.IsEnterpriseTenant(tenant) || tenantutil.IsCommunityTenant(tenant) {
			log.Ctx(ctx).Error().Msgf("User %s belongs to a %s tenant: %s", user.Id, tenant.Tier, tenant.Name)
			return nil, status.Error(codes.FailedPrecondition, "User belongs to an enterprise tenant")
		}
		if tenantutil.IsSelfServeTeamTenant(tenant) {
			log.Ctx(ctx).Info().Msgf("User %s already belongs to self-serve team tenant: %s", user.Id, tenant.Name)
			return &TeamTenantCreationEligibility{
				Allowed:              false,
				ExistingTeamTenantID: tenantId,
			}, nil
		}
	}

	return &TeamTenantCreationEligibility{
		Allowed:              true,
		ExistingTeamTenantID: "",
	}, nil
}

func (s *TeamManagementServer) CreateTenantForTeam(
	ctx context.Context, req *authpb.CreateTenantForTeamRequest,
) (*authpb.CreateTenantForTeamResponse, error) {
	if !s.teamManagementEnabled() {
		log.Ctx(ctx).Warn().Msgf("Team management is disabled for CreateTenantForTeam")
		return nil, status.Error(codes.Internal, "Disabled")
	}

	err := s.teamManagementAuthCheck(ctx, nil, &req.AdminUserId, tokenscopesproto.Scope_AUTH_RW, false, "CreateTenantForTeam")
	if err != nil {
		return nil, err
	}

	// Get the user to check if they're eligible for team creation
	userDAO := s.daoFactory.GetUserDAO()
	user, err := userDAO.Get(ctx, req.AdminUserId)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msgf("Failed to get user %s", req.AdminUserId)
		return nil, status.Error(codes.Internal, "Failed to get user information")
	}
	if user == nil {
		log.Ctx(ctx).Error().Msgf("User %s not found", req.AdminUserId)
		return nil, status.Error(codes.NotFound, "User not found")
	}

	// Check if user is allowed to create team tenant or already has one
	eligibilityResult, err := isAllowedToCreateTeamTenant(ctx, user, s.daoFactory, s.tenantMap, s.featureFlagHandle, "")
	if err != nil {
		return nil, err
	}

	// If user already has a self-serve team, find the existing tenant creation record
	if !eligibilityResult.Allowed && eligibilityResult.ExistingTeamTenantID != "" {
		tenantCreationDAO := s.daoFactory.GetTenantCreationDAO()
		var existingTenantCreationID string

		err = tenantCreationDAO.FindAll(ctx, func(tenantCreation *auth_entities.TenantCreation) bool {
			if tenantCreation.TenantId == eligibilityResult.ExistingTeamTenantID {
				existingTenantCreationID = tenantCreation.Id
				return false
			}
			return true
		})
		if err != nil {
			log.Ctx(ctx).Error().Err(err).Msgf("Failed to search tenant creation records for user %s", req.AdminUserId)
			return nil, status.Error(codes.Internal, "Failed to search tenant creation records")
		}

		if existingTenantCreationID != "" {
			log.Ctx(ctx).Info().Msgf("Found existing tenant creation %s for user %s in self-serve team %s",
				existingTenantCreationID, req.AdminUserId, eligibilityResult.ExistingTeamTenantID)
			return &authpb.CreateTenantForTeamResponse{
				TenantCreationId: existingTenantCreationID,
			}, nil
		} else {
			log.Ctx(ctx).Error().Msgf("Failed to find existing tenant creation for user %s in self-serve team %s",
				req.AdminUserId, eligibilityResult.ExistingTeamTenantID)
			return nil, status.Error(codes.Internal, "Failed to find existing tenant creation")
		}
	}

	// Make sure this user has an orb subscription. Their subscription will be moved to be associated
	// with the team tenant.
	if user.OrbSubscriptionId == "" {
		log.Ctx(ctx).Error().Msgf("CreateTenantForTeam: User %s does not have an Orb subscription", user.Id)
		return nil, status.Error(codes.FailedPrecondition, "User does not have an Orb subscription")
	}
	subscriptionDAO := s.daoFactory.GetSubscriptionDAO()
	subscription, err := subscriptionDAO.Get(ctx, user.OrbSubscriptionId)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msgf(
			"CreateTenantForTeam: Failed to get Orb subscription %s", user.OrbSubscriptionId)
		return nil, status.Error(codes.Internal, "Failed to get subscription")
	} else if subscription == nil {
		log.Ctx(ctx).Error().Msgf("CreateTenantForTeam: Orb subscription %s not found", user.OrbSubscriptionId)
		return nil, status.Error(codes.FailedPrecondition, "Subscription not found")
	}

	// Generate a unique tenant creation ID
	tenantCreationID := uuid.New().String()
	log.Ctx(ctx).Info().Msgf("Creating tenant creation request %s for user %s", tenantCreationID, req.AdminUserId)

	// Audit Log
	requestContext, _ := requestcontext.FromGrpcContext(ctx)
	s.WriteTeamsAuditLog(ctx, fmt.Sprintf("creating team tenant for admin user %s", req.AdminUserId),
		audit.NewUser(req.AdminUserId),
		audit.NewProtoRequest(req),
		requestContext,
		audit.NewTenantID(tenantCreationID),
		audit.NewTenantName(tenantCreationID), // valid for self-serve
	)

	// Create and publish the tenant creation message for async processing
	tenantCreationMsg := &auth_internal.CreateTenantForTeamMessage{
		TenantCreationId: tenantCreationID,
		TenantCreationRequest: &authpb.CreateTenantForTeamRequest{
			AdminUserId: req.AdminUserId,
		},
		PublishTime: timestamppb.Now(),
	}

	err = s.asyncOpsPublisher.PublishTenantCreation(ctx, tenantCreationMsg)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msgf("Failed to publish tenant creation message for id %s", tenantCreationID)
		return nil, status.Error(codes.Internal, "Failed to publish tenant creation message")
	}

	// Create and save a new tenant creation record to the internal database
	tenantCreationDAO := s.daoFactory.GetTenantCreationDAO()
	tenantCreation := &auth_entities.TenantCreation{
		Id:        tenantCreationID,
		CreatedAt: timestamppb.Now(),
		Status:    auth_entities.TenantCreation_PENDING,
	}

	_, err = tenantCreationDAO.Create(ctx, tenantCreation)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msgf("Failed to create tenant creation record %s", tenantCreationID)
		return nil, status.Error(codes.Internal, "Failed to create tenant creation record")
	}

	log.Ctx(ctx).Info().Msgf("Created tenant creation request %s for user %s", tenantCreationID, req.AdminUserId)

	return &authpb.CreateTenantForTeamResponse{
		TenantCreationId: tenantCreationID,
	}, nil
}

func (s *TeamManagementServer) GetCreateTenantForTeamStatus(
	ctx context.Context, req *authpb.GetCreateTenantForTeamStatusRequest,
) (*authpb.GetCreateTenantForTeamStatusResponse, error) {
	if !s.teamManagementEnabled() {
		log.Ctx(ctx).Warn().Msgf("Team management is disabled for GetCreateTenantForTeamStatus")
		return nil, status.Error(codes.Internal, "Disabled")
	}

	err := s.teamManagementAuthCheck(ctx, nil, nil, tokenscopesproto.Scope_AUTH_R, false, "GetCreateTenantForTeamStatus")
	if err != nil {
		return nil, err
	}

	log.Ctx(ctx).Info().Msgf("Getting status for tenant creation request %s", req.TenantCreationId)

	tenantCreationDAO := s.daoFactory.GetTenantCreationDAO()
	tenantCreation, err := tenantCreationDAO.Get(ctx, req.TenantCreationId)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msgf("Failed to get tenant creation record %s", req.TenantCreationId)
		return nil, status.Error(codes.Internal, "Failed to get tenant creation record")
	}
	if tenantCreation == nil {
		log.Ctx(ctx).Error().Msgf("Tenant creation record %s not found", req.TenantCreationId)
		return nil, status.Error(codes.NotFound, "Tenant creation record not found")
	}

	log.Ctx(ctx).Info().Msgf("Got status for tenant creation request %s: %v", req.TenantCreationId, tenantCreation)

	return &authpb.GetCreateTenantForTeamStatusResponse{
		TenantCreation: tenantCreation,
	}, nil
}

func (s *TeamManagementServer) InviteUsersToTenant(
	ctx context.Context, req *authpb.InviteUsersToTenantRequest,
) (*authpb.InviteUsersToTenantResponse, error) {
	if !s.teamManagementEnabled() {
		return nil, status.Error(codes.Internal, "Disabled")
	}

	err := s.teamManagementAuthCheck(ctx, &req.TenantId, nil, tokenscopesproto.Scope_AUTH_RW, false, "InviteUsersToTenant")
	if err != nil {
		return nil, err
	}

	// Audit Log
	requestContext, _ := requestcontext.FromGrpcContext(ctx)
	s.WriteTeamsAuditLog(ctx, fmt.Sprintf("inviting users %v to tenant %s", req.InviteeEmails, req.TenantId),
		audit.NewProtoRequest(req),
		requestContext,
		audit.NewTenantID(req.TenantId),
		audit.NewTenantName(req.TenantId), // valid for self-serve
	)

	results := make(
		[]*authpb.InviteUsersToTenantResponse_InvitationCreationStatus, 0, len(req.InviteeEmails),
	)

	// Restrict this endpoint to self-serve team tenants.
	tenant, err := s.tenantMap.GetTenantByID(ctx, req.TenantId)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msgf("Failed to get tenant %s", req.TenantId)
		return nil, status.Error(codes.Internal, "Failed to get tenant")
	} else if tenant == nil {
		log.Ctx(ctx).Error().Msgf("Tenant %s not found", req.TenantId)
		return nil, status.Error(codes.NotFound, "Tenant not found")
	} else if !tenantutil.IsSelfServeTeamTenant(tenant) {
		log.Ctx(ctx).Error().Msgf("Tenant %s is not a self-serve team tenant. Cannot invite users", req.TenantId)
		return nil, status.Error(codes.FailedPrecondition, "Invitations not permitted to this tenant")
	}

	// Auth claims have already been checked above, so we know this parsing will succeed.
	authInfo, _ := auth.GetAugmentClaims(ctx)
	inviterUserID := authInfo.GetOpaqueUserID().GetUserId()
	inviterEmail := authInfo.UserEmail
	if inviterEmail == "" {
		log.Ctx(ctx).Error().Msg("Failed to get inviter email from auth claims")
		return nil, status.Error(codes.FailedPrecondition, "Failed to get inviter email")
	}

	// TEMPORARY HACK: Disallow inviting emails with +.
	// TODO(jacqueline): This should really be checked at signup but currently we aren't doing the
	// similar email checks in the invitation flow.
	plusAllowed, _ := inviteUsersToTenantPlusAllowed.Get(s.featureFlagHandle)
	if !plusAllowed {
		for _, email := range req.InviteeEmails {
			if strings.Contains(email, "+") {
				log.Ctx(ctx).Error().Msgf("Disallowing invite for email %s to tenant %s due to +", email)
				return nil, status.Error(codes.InvalidArgument, "Email contains +")
			}
		}
	}

	// Deduplicate and normalize emails from the request.
	deduplicatedEmails := make(map[string]struct{})
	for _, email := range req.InviteeEmails {
		normalizedEmail, err := normalizeEmail(email)
		if err != nil {
			log.Ctx(ctx).Error().Err(err).Msgf("Failed to normalize email %s", email)
			results = append(
				results,
				&authpb.InviteUsersToTenantResponse_InvitationCreationStatus{
					Email:  email,
					Status: authpb.InviteUsersToTenantResponse_InvitationCreationStatus_ERROR,
				},
			)
			continue
		}
		deduplicatedEmails[normalizedEmail] = struct{}{}
	}

	// Get the list of all emails with pending invitations for this tenant, to avoid creating
	// duplicate invitations. Note that there's a race here where it's still possible to end up with
	// duplicates. Jacqueline is choosing to ignore this for now because the duplicates should be
	// harmless and this will be easy to solve once we've migrated to a relational database.
	pendingInvitations, err := GetInvitationsForTenant(
		ctx, s.daoFactory, req.TenantId, auth_entities.TenantInvitation_PENDING,
	)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to fetch invitations")
		return nil, status.Error(codes.Internal, "Failed to fetch invitations")
	}
	pendingInvitationsByEmail := make(map[string]*auth_entities.TenantInvitation)
	for _, invitation := range pendingInvitations {
		pendingInvitationsByEmail[invitation.InviteeEmail] = invitation
	}

	// Check if the tenant has sufficient seats available for non-admin callers.
	// For admin callers we will add seats in parallel and have decided that the (low) risk of
	// over-inviting is acceptable.
	isAdmin := false
	augmentUserID := augmentUserID(authInfo)
	if augmentUserID != "" {
		tenantMappingDAO := s.daoFactory.GetUserTenantMappingDAO(tenant.Name)
		mapping, err := tenantMappingDAO.GetByUser(ctx, augmentUserID)
		if err != nil {
			log.Ctx(ctx).Error().Err(err).Msgf("Failed to get user mapping")
			return nil, status.Error(codes.Internal, "Failed to get user mapping")
		} else if mapping != nil {
			for _, role := range mapping.CustomerUiRoles {
				if role == auth_entities.CustomerUiRole_ADMIN {
					isAdmin = true
					break
				}
			}
		}
	}

	// Skip seat validation for admin users since we don't want to add friction for adding users.
	// Only enforce seat limits for non-admin users to prevent them from exceeding the team's paid capacity.
	if !isAdmin {
		// Count new invitations (excluding ones that already exist)
		newInvitationCount := 0
		for email := range deduplicatedEmails {
			if _, exists := pendingInvitationsByEmail[email]; !exists {
				newInvitationCount++
			}
		}

		// Get the subscription for the tenant
		tenantSubscriptionMappingDAO := s.daoFactory.GetTenantSubscriptionMappingDAO()
		subscriptionMapping, err := tenantSubscriptionMappingDAO.Get(ctx, req.TenantId)
		if err != nil {
			log.Ctx(ctx).Error().Err(err).Msg("Failed to fetch subscription mapping")
			return nil, status.Error(codes.Internal, "Failed to fetch subscription mapping")
		}

		if subscriptionMapping == nil {
			log.Ctx(ctx).Error().Str("tenant_id", req.TenantId).Msg("Subscription not found for tenant")
			return nil, status.Error(codes.NotFound, "Subscription not found for tenant")
		}

		// Get the subscription to check its seat limit
		subscriptionDAO := s.daoFactory.GetSubscriptionDAO()
		subscription, err := subscriptionDAO.Get(ctx, subscriptionMapping.OrbSubscriptionId)
		if err != nil {
			log.Ctx(ctx).Error().Err(err).Msg("Failed to fetch subscription")
			return nil, status.Error(codes.Internal, "Failed to fetch subscription")
		}

		if subscription == nil {
			log.Ctx(ctx).Error().Str("subscription_id", subscriptionMapping.OrbSubscriptionId).Msg("Subscription not found")
			return nil, status.Error(codes.NotFound, "Subscription not found")
		}

		// Validate that there are enough seats available
		err = s.ValidateSubscriptionSeats(ctx, req.TenantId, subscription.Seats, newInvitationCount)
		if err != nil {
			return nil, err
		}
	}

	tenantInvitationDAO := s.daoFactory.GetTenantInvitationDAO(req.TenantId)
	for inviteeEmail := range deduplicatedEmails {
		// If the email is already invited just respond with "success".
		if existingInvitation, ok := pendingInvitationsByEmail[inviteeEmail]; ok {
			results = append(
				results,
				&authpb.InviteUsersToTenantResponse_InvitationCreationStatus{
					Email:        inviteeEmail,
					Status:       authpb.InviteUsersToTenantResponse_InvitationCreationStatus_SUCCESS,
					InvitationId: existingInvitation.Id,
				},
			)
			continue
		}

		// Check if the inviteeEmail is associated with an enterprise tenant.
		tenant, err := s.tenantMap.GetTenantForEmailDomain(ctx, inviteeEmail)
		if err != nil {
			log.Ctx(ctx).Error().Err(err).Msgf("Failed to check if email is associated with enterprise tenant")
			results = append(
				results,
				&authpb.InviteUsersToTenantResponse_InvitationCreationStatus{
					Email:  inviteeEmail,
					Status: authpb.InviteUsersToTenantResponse_InvitationCreationStatus_ERROR,
				},
			)
			continue
		}

		// If the email is associated with an enterprise tenant, we can't invite them
		if tenant != nil && tenantutil.IsEnterpriseTenant(tenant) {
			log.Ctx(ctx).Error().Msgf("Email %s is associated with enterprise tenant %s", inviteeEmail, tenant.Name)
			results = append(
				results,
				&authpb.InviteUsersToTenantResponse_InvitationCreationStatus{
					Email:  inviteeEmail,
					Status: authpb.InviteUsersToTenantResponse_InvitationCreationStatus_ERROR,
				},
			)
			continue
		}

		// Create the invitation.
		invitation := &auth_entities.TenantInvitation{
			Id:            uuid.New().String(),
			CreatedAt:     timestamppb.Now(),
			InviteeEmail:  inviteeEmail,
			TenantId:      req.TenantId,
			InviterUserId: inviterUserID,
			InviterEmail:  inviterEmail,
			Status:        auth_entities.TenantInvitation_PENDING,
		}
		_, err = tenantInvitationDAO.Create(ctx, invitation)
		var status authpb.InviteUsersToTenantResponse_InvitationCreationStatus_Status
		var invitationID string
		if err != nil {
			log.Ctx(ctx).Error().Err(err).Msgf("Failed to invite %s to %s", inviteeEmail, req.TenantId)
			status = authpb.InviteUsersToTenantResponse_InvitationCreationStatus_ERROR
		} else {
			log.Ctx(ctx).Info().Msgf("Invited %s to %s", inviteeEmail, req.TenantId)
			status = authpb.InviteUsersToTenantResponse_InvitationCreationStatus_SUCCESS
			invitationID = invitation.Id
		}
		results = append(
			results,
			&authpb.InviteUsersToTenantResponse_InvitationCreationStatus{
				Email:        inviteeEmail,
				Status:       status,
				InvitationId: invitationID,
			},
		)

		// Pulish this invitation to request insight.
		event := ripublisher.NewTenantEvent()
		event.Event = &ripb.TenantEvent_InviteUserToTenant{
			InviteUserToTenant: &ripb.InviteUserToTenant{
				Invitation: invitation,
			},
		}
		riErr := s.requestInsightPublisher.PublishTenantEvent(ctx, &ripb.TenantInfo{
			TenantId:   req.TenantId,
			TenantName: authInfo.TenantName,
		}, event)
		if riErr != nil {
			log.Ctx(ctx).Warn().Err(riErr).Msg("Failed to publish InviteUserToTenant event")
		}
	}

	emailsInvited := []string{}
	for _, result := range results {
		if result.Status == authpb.InviteUsersToTenantResponse_InvitationCreationStatus_SUCCESS {
			emailsInvited = append(emailsInvited, result.Email)

			// Publish an async message for sending theinvitation email
			s.asyncOpsPublisher.PublishSendInvitationEmail(ctx, &auth_internal.SendInvitationEmailMessage{
				TenantId:     req.TenantId,
				InvitationId: result.InvitationId,
				InviteeEmail: result.Email,
				PublishTime:  timestamppb.Now(),
			})
		}
	}
	s.WriteTeamsAuditLog(ctx, fmt.Sprintf("successfully invited users %v to tenant %s", emailsInvited, req.TenantId),
		audit.NewProtoRequest(req),
		requestContext,
		audit.NewTenantID(req.TenantId),
		audit.NewTenantName(req.TenantId), // valid for self-serve
	)

	return &authpb.InviteUsersToTenantResponse{
		InvitationStatuses: results,
	}, nil
}

func (s *TeamManagementServer) GetTenantInvitations(
	ctx context.Context, req *authpb.GetTenantInvitationsRequest,
) (*authpb.GetTenantInvitationsResponse, error) {
	if !s.teamManagementEnabled() {
		return nil, status.Error(codes.Internal, "Disabled")
	}

	err := s.teamManagementAuthCheck(ctx, &req.TenantId, nil, tokenscopesproto.Scope_AUTH_R, false, "GetTenantInvitations")
	if err != nil {
		return nil, err
	}

	invitations, err := GetInvitationsForTenant(
		ctx, s.daoFactory, req.TenantId, auth_entities.TenantInvitation_PENDING,
	)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to fetch invitations")
		return nil, status.Error(codes.Internal, "Failed to fetch invitations")
	}

	return &authpb.GetTenantInvitationsResponse{
		Invitations: invitations,
	}, nil
}

func (s *TeamManagementServer) GetUserInvitations(
	ctx context.Context, req *authpb.GetUserInvitationsRequest,
) (*authpb.GetUserInvitationsResponse, error) {
	// At the point in the login flow when this endpoint is called we don't have a user id. Instead of
	// validating a user id we will filter for the correct email in application logic.
	err := s.teamManagementAuthCheck(ctx, nil, nil, tokenscopesproto.Scope_AUTH_R, false, "GetUserInvitations")
	if err != nil {
		return nil, err
	}
	return s.getUserInvitations(ctx, req)
}

// Application logic for the GetUserInvitations endpoint, pulled out so that it can be used by
// both TeamManagementService and FrontEndTokenService. This does NOT perform any authorization
// checks; callers must do that themselves.
func (s *TeamManagementServer) getUserInvitations(
	ctx context.Context, req *authpb.GetUserInvitationsRequest,
) (*authpb.GetUserInvitationsResponse, error) {
	if !s.teamManagementEnabled() {
		return nil, status.Error(codes.Internal, "Disabled")
	}

	normalizedEmail, err := normalizeEmail(req.Email)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msgf("Failed to normalize email %s", req.Email)
		return nil, status.Error(codes.InvalidArgument, "Failed to normalize email")
	}

	// Listing all the invitations for an email requires a table scan.
	tenantInvitationDAO := s.daoFactory.GetTenantInvitationDAO("")

	// Look for PENDING invitations with a matching normalized email.
	invitations := make([]*auth_entities.TenantInvitation, 0)

	err = tenantInvitationDAO.ListInvitationsForAllTenants(ctx, func(invitation *auth_entities.TenantInvitation) bool {
		normalizedInvitationEmail, err := normalizeEmail(invitation.InviteeEmail)
		if err != nil {
			log.Ctx(ctx).Error().Err(err).Msgf(
				"Failed to normalize email %s for invitation %s", invitation.InviteeEmail, invitation.Id)
			return true
		}

		if invitation.Status == auth_entities.TenantInvitation_PENDING &&
			normalizedInvitationEmail == normalizedEmail {
			invitations = append(invitations, invitation)
		}
		return true
	})
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to fetch invitations")
		return nil, status.Error(codes.Internal, "Failed to fetch invitations")
	}

	log.Ctx(ctx).Info().Msgf("Found %d matching invitations for email %s", len(invitations), req.Email)
	return &authpb.GetUserInvitationsResponse{
		Invitations: invitations,
	}, nil
}

func (s *TeamManagementServer) ResolveInvitations(
	ctx context.Context, req *authpb.ResolveInvitationsRequest,
) (*authpb.ResolveInvitationsResponse, error) {
	err := s.teamManagementAuthCheck(ctx, nil, nil, tokenscopesproto.Scope_AUTH_RW, false, "ResolveInvitations")
	if err != nil {
		return nil, err
	}
	return s.resolveInvitations(ctx, req)
}

// Application logic for the ResolveInvitations endpoint, pulled out so that it can be used by
// both TeamManagementService and FrontEndTokenService. This does NOT perform any authorization
// checks; callers must do that themselves.
func (s *TeamManagementServer) resolveInvitations(
	ctx context.Context, req *authpb.ResolveInvitationsRequest,
) (*authpb.ResolveInvitationsResponse, error) {
	if !s.teamManagementEnabled() {
		return nil, status.Error(codes.Internal, "Disabled")
	}

	var acceptId string
	if req.AcceptInvitationId != nil {
		acceptId = *req.AcceptInvitationId
	}
	requestContext, _ := requestcontext.FromGrpcContext(ctx)
	s.WriteTeamsAuditLog(ctx, fmt.Sprintf("accepting invitation %s and declining invitations %v", acceptId, req.DeclineInvitationIds),
		audit.NewProtoRequest(req),
		requestContext,
	)

	// TODO(jacqueline): Think through races (e.g., invitation resolutions for the same email and
	// enterprise tenants associated with this email created after the invitation creation). I think
	// it's largely safe to ignore races here, but it requires more thought.

	// Publish the operation before writing to the database to avoid orphaned invitation resolution
	// operations.
	invitationResolutionID := uuid.New().String()
	s.asyncOpsPublisher.PublishInvitationResolution(ctx, &auth_internal.ResolveInvitationsMessage{
		InvitationResolutionId:    invitationResolutionID,
		ResolveInvitationsRequest: req,
		PublishTime:               timestamppb.Now(),
	})

	invitationResolutionDAO := s.daoFactory.GetInvitationResolutionDAO()
	invitationResolution := &auth_entities.InvitationResolution{
		Id:        invitationResolutionID,
		CreatedAt: timestamppb.Now(),
		Status:    auth_entities.InvitationResolution_PENDING,
	}
	_, err := invitationResolutionDAO.Create(ctx, invitationResolution)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to create invitation resolution record")
		return nil, status.Error(codes.Internal, "Failed to create invitation resolution record")
	}

	return &authpb.ResolveInvitationsResponse{
		InvitationResolutionId: invitationResolutionID,
	}, nil
}

func (s *TeamManagementServer) GetResolveInvitationsStatus(
	ctx context.Context, req *authpb.GetResolveInvitationsStatusRequest,
) (*authpb.GetResolveInvitationsStatusResponse, error) {
	err := s.teamManagementAuthCheck(ctx, nil, nil, tokenscopesproto.Scope_AUTH_R, false, "GetResolveInvitationsStatus")
	if err != nil {
		return nil, err
	}
	return s.getResolveInvitationsStatus(ctx, req)
}

// Application logic for the GetResolveInvitationsStatus endpoint, pulled out so that it can be used
// by both TeamManagementService and FrontEndTokenService. This does NOT perform any authorization
// checks; callers must do that themselves.
func (s *TeamManagementServer) getResolveInvitationsStatus(
	ctx context.Context, req *authpb.GetResolveInvitationsStatusRequest,
) (*authpb.GetResolveInvitationsStatusResponse, error) {
	if !s.teamManagementEnabled() {
		return nil, status.Error(codes.Internal, "Disabled")
	}

	invitationResolutionDAO := s.daoFactory.GetInvitationResolutionDAO()
	invitationResolution, err := invitationResolutionDAO.Get(ctx, req.InvitationResolutionId)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to fetch invitation resolution")
		return nil, status.Error(codes.Internal, "Failed to fetch invitation resolution")
	}

	if invitationResolution == nil {
		log.Ctx(ctx).Error().Msg("Invitation resolution not found")
		return nil, status.Error(codes.NotFound, "Invitation resolution not found")
	}

	return &authpb.GetResolveInvitationsStatusResponse{
		InvitationResolution: invitationResolution,
	}, nil
}

func (s *TeamManagementServer) GetSubscription(
	ctx context.Context, req *authpb.GetSubscriptionRequest,
) (*authpb.GetSubscriptionResponse, error) {
	// Validate the request
	var subscriptionId string
	var userId, tenantId *string

	switch id := req.LookupId.(type) {
	case *authpb.GetSubscriptionRequest_SubscriptionId:
		// We'll need to get the user/tenant ID from the subscription to check permissions
		subscriptionId = id.SubscriptionId
		subscriptionDAO := s.daoFactory.GetSubscriptionDAO()
		subscription, err := subscriptionDAO.Get(ctx, subscriptionId)
		if err != nil {
			log.Ctx(ctx).Error().Err(err).Msg("Failed to fetch subscription")
			return nil, status.Error(codes.Internal, "Subscription not found or you don't have permission to access it")
		}

		if subscription == nil {
			log.Ctx(ctx).Error().Msg("Subscription not found")
			return nil, status.Error(codes.Internal, "Subscription not found or you don't have permission to access it")
		}

		// Get the tenant ID from the subscription
		switch owner := subscription.Owner.(type) {
		case *auth_entities.Subscription_TenantId:
			// This use case is currently not supported in FE
			tenantId = &owner.TenantId
		case *auth_entities.Subscription_UserId:
			// For individual subscriptions, we need to check if the caller is the owner
			userId = &owner.UserId
		default:
			log.Ctx(ctx).Error().Str("subscription_id", id.SubscriptionId).Msg("Subscription exists but has no valid owner type")
			return nil, status.Error(codes.Internal, "Subscription not found or you don't have permission to access it")
		}

	case *authpb.GetSubscriptionRequest_TenantId:
		tenantId = &id.TenantId
	default:
		return nil, status.Error(codes.InvalidArgument, "Either subscription_id or tenant_id must be provided")
	}

	// Check permissions given the userID or tenantID
	err := s.teamManagementAuthCheck(ctx, tenantId, userId, tokenscopesproto.Scope_AUTH_R, false, "GetSubscription")
	if err != nil {
		return nil, err
	}

	// Get the subscription for the tenant
	if tenantId != nil {
		// Get the subscription ID from the tenant
		tenantSubscriptionMappingDAO := s.daoFactory.GetTenantSubscriptionMappingDAO()
		subscriptionMapping, err := tenantSubscriptionMappingDAO.Get(ctx, *tenantId)
		if err != nil {
			log.Ctx(ctx).Error().Err(err).Msg("Failed to fetch subscription mapping")
			return nil, status.Error(codes.Internal, "Failed to fetch subscription mapping")
		}

		if subscriptionMapping == nil {
			log.Ctx(ctx).Error().Str("tenant_id", *tenantId).Msg("Subscription not found for tenant")
			return nil, status.Error(codes.NotFound, "Subscription not found for tenant")
		}

		subscriptionId = subscriptionMapping.OrbSubscriptionId
	}

	subscriptionDAO := s.daoFactory.GetSubscriptionDAO()
	subscription, err := subscriptionDAO.Get(ctx, subscriptionId)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to fetch subscription")
		return nil, status.Error(codes.Internal, "Failed to fetch subscription")
	}

	if subscription == nil {
		log.Ctx(ctx).Error().Str("subscription_id", subscriptionId).Msg("Subscription not found")
		return nil, status.Error(codes.NotFound, "Subscription not found")
	}

	return &authpb.GetSubscriptionResponse{
		Subscription: subscription,
	}, nil
}

// paginatedListHelper handles common pagination logic for list endpoints
// This is a generic helper function that can be used with different types
func paginatedListHelper[T any](
	ctx context.Context,
	pageSize uint32,
	pageToken string,
	defaultPageSize uint32,
	fetchFunc func(ctx context.Context, token string, pageSize uint32) ([]T, string, error),
	errorMsg string,
) ([]T, string, error) {
	// Set default page size if not specified
	if pageSize == 0 {
		pageSize = defaultPageSize
	}

	// Fetch data using the provided function
	items, nextToken, err := fetchFunc(ctx, pageToken, pageSize)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg(errorMsg)
		return nil, "", status.Error(codes.Internal, errorMsg)
	}

	return items, nextToken, nil
}

func (s *TeamManagementServer) ListSubscriptions(
	ctx context.Context, req *authpb.ListSubscriptionsRequest,
) (*authpb.ListSubscriptionsResponse, error) {
	// Check permissions - this endpoint requires wildcard tenant access
	err := s.teamManagementWildcardAuthCheck(ctx, tokenscopesproto.Scope_AUTH_R, "ListSubscriptions")
	if err != nil {
		return nil, err
	}

	// Get the subscriptions with pagination
	subscriptionDAO := s.daoFactory.GetSubscriptionDAO()

	subscriptions, nextPageToken, err := paginatedListHelper(
		ctx,
		req.PageSize,
		req.PageToken,
		100, // Default page size
		subscriptionDAO.FindAllPaginated,
		"Failed to fetch subscriptions",
	)

	return &authpb.ListSubscriptionsResponse{
		Subscriptions: subscriptions,
		NextPageToken: nextPageToken,
	}, err
}

func (s *TeamManagementServer) ListTenantSubscriptionMappings(
	ctx context.Context, req *authpb.ListTenantSubscriptionMappingsRequest,
) (*authpb.ListTenantSubscriptionMappingsResponse, error) {
	// Check permissions - this endpoint requires wildcard tenant access
	err := s.teamManagementWildcardAuthCheck(ctx, tokenscopesproto.Scope_AUTH_R, "ListTenantSubscriptionMappings")
	if err != nil {
		return nil, err
	}

	// Get the tenant subscription mappings with pagination
	tenantSubscriptionMappingDAO := s.daoFactory.GetTenantSubscriptionMappingDAO()

	tenantSubscriptionMappings, nextPageToken, err := paginatedListHelper(
		ctx,
		req.PageSize,
		req.PageToken,
		100, // Default page size
		tenantSubscriptionMappingDAO.FindAllPaginated,
		"Failed to fetch tenant subscription mappings",
	)

	return &authpb.ListTenantSubscriptionMappingsResponse{
		TenantSubscriptionMappings: tenantSubscriptionMappings,
		NextPageToken:              nextPageToken,
	}, err
}

func (s *TeamManagementServer) ListUserTenantMappings(
	ctx context.Context, req *authpb.ListUserTenantMappingsRequest,
) (*authpb.ListUserTenantMappingsResponse, error) {
	// Check permissions - this endpoint requires wildcard tenant access
	err := s.teamManagementWildcardAuthCheck(ctx, tokenscopesproto.Scope_AUTH_R, "ListUserTenantMappings")
	if err != nil {
		return nil, err
	}

	// Get the user tenant mappings with pagination
	userTenantMappingDAO := s.daoFactory.GetUserTenantMappingDAO("")

	userTenantMappings, nextPageToken, err := paginatedListHelper(
		ctx,
		req.PageSize,
		req.PageToken,
		100, // Default page size
		userTenantMappingDAO.FindAllPaginated,
		"Failed to fetch user tenant mappings",
	)

	return &authpb.ListUserTenantMappingsResponse{
		UserTenantMappings: userTenantMappings,
		NextPageToken:      nextPageToken,
	}, err
}

func (s *TeamManagementServer) DeleteInvitation(
	ctx context.Context, req *authpb.DeleteInvitationRequest,
) (*authpb.DeleteInvitationResponse, error) {
	if !s.teamManagementEnabled() {
		return nil, status.Error(codes.Internal, "Disabled")
	}

	err := s.teamManagementAuthCheck(
		ctx, &req.TenantId, nil, tokenscopesproto.Scope_AUTH_RW, false, "DeleteInvitation")
	if err != nil {
		return nil, err
	}

	// Audit Log
	s.WriteTeamsAuditLog(ctx, fmt.Sprintf("deleting invitation %s for tenant %s", req.InvitationId, req.TenantId))

	invitationDAO := s.daoFactory.GetTenantInvitationDAO(req.TenantId)
	err = invitationDAO.Delete(ctx, req.InvitationId)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to delete invitation")
		return nil, status.Error(codes.Internal, "Failed to delete invitation")
	}

	// Record invitation deletion in request insight.
	event := ripublisher.NewTenantEvent()
	event.Event = &ripb.TenantEvent_DeleteInvitation{
		DeleteInvitation: &ripb.DeleteInvitation{
			InvitationId: req.InvitationId,
		},
	}
	riErr := s.requestInsightPublisher.PublishTenantEvent(ctx, &ripb.TenantInfo{
		TenantId:   req.TenantId,
		TenantName: req.TenantId, // Self-serve tenants' names are the same as their ID
	}, event)
	if riErr != nil {
		log.Ctx(ctx).Warn().Err(riErr).Msg("Failed to publish DeleteInvitation event")
	}

	log.Ctx(ctx).Info().Msgf("successfully deleted invitation %s for tenant %s", req.InvitationId, req.TenantId)
	s.WriteTeamsAuditLog(ctx,
		fmt.Sprintf("successfully deleted invitation %s for tenant %s", req.InvitationId, req.TenantId),
		audit.NewProtoRequest(req),
		audit.NewTenantID(req.TenantId),
		audit.NewTenantName(req.TenantId), // valid for self-serve
	)

	return &authpb.DeleteInvitationResponse{}, nil
}

func (s *TeamManagementServer) UpdateSubscription(
	ctx context.Context, req *authpb.UpdateSubscriptionRequest,
) (*authpb.UpdateSubscriptionResponse, error) {
	if !s.teamManagementEnabled() {
		return nil, status.Error(codes.Internal, "Disabled")
	}

	// Validate the request
	if req.SubscriptionId == "" {
		return nil, status.Error(codes.InvalidArgument, "Subscription ID is required")
	}

	if req.Seats <= 0 {
		return nil, status.Error(codes.InvalidArgument, "Seats must be greater than 0")
	}

	// Get the subscription to check permissions
	subscriptionDAO := s.daoFactory.GetSubscriptionDAO()
	subscription, err := subscriptionDAO.Get(ctx, req.SubscriptionId)
	if err != nil {
		log.Ctx(ctx).Error().Str("subscription_id", req.SubscriptionId).Msg("Failed to fetch subscription")
		return nil, status.Error(codes.Internal, "Failed to fetch subscription")
	}

	if subscription == nil {
		return nil, status.Error(codes.NotFound, "Subscription not found")
	}

	// Seats must be less than the configured max seats for the plan
	planInfo := s.orbConfig.GetPlan(subscription.ExternalPlanId)
	if planInfo == nil {
		log.Ctx(ctx).Error().Str("plan_id", subscription.ExternalPlanId).Msg("Unknown plan ID")
		return nil, status.Error(codes.Internal, "Unknown plan ID")
	}
	if int(req.Seats) > planInfo.Features.MaxSeats {
		return nil, status.Error(codes.InvalidArgument, fmt.Sprintf("Seats must be less than %d", planInfo.Features.MaxSeats))
	}

	// Check permissions based on the subscription owner
	var tenantId *string

	switch owner := subscription.Owner.(type) {
	case *auth_entities.Subscription_TenantId:
		tenantId = &owner.TenantId
	case *auth_entities.Subscription_UserId:
		log.Ctx(ctx).Error().Str("subscription_id", req.SubscriptionId).Msg("Individual subscriptions cannot be updated with this endpoint")
		return nil, status.Error(codes.Internal, "Individual subscriptions cannot be updated with this endpoint")
	default:
		log.Ctx(ctx).Error().Msg("Subscription exists but has no valid owner type")
		return nil, status.Error(codes.Internal, "Subscription has no valid owner")
	}

	// Check permissions given the tenantID
	err = s.teamManagementAuthCheck(ctx, tenantId, nil, tokenscopesproto.Scope_AUTH_RW, true, "UpdateSubscription")
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to check permissions")
		return nil, err
	}

	// Require reason for IAP user
	err = requireReasonForIAPUser(ctx, req.Reason)
	if err != nil {
		log.Error().Err(err).Msg("Reason is required for IAP users")
		return nil, err
	}

	tenant, err := s.tenantMap.GetTenantByID(ctx, *tenantId)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("tenant_id", *tenantId).Msg("Failed to get tenant")
		return nil, status.Error(codes.Internal, "Failed to get tenant")
	}
	if !tenantutil.IsSelfServeTeamTenant(tenant) {
		log.Ctx(ctx).Error().Str("tenant_id", *tenantId).Msg("Tenant is not a self-serve team")
		return nil, status.Error(codes.InvalidArgument, "Tenant is not a self-serve team")
	}

	// Check for seat changes
	seatChangeInProgress, seatChangeDescription, err := CheckSubscriptionSeatChange(ctx, s.daoFactory, s.featureFlagHandle, subscription)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msgf("Failed to check seat changes for subscription %s", req.SubscriptionId)
		return nil, err
	}
	if seatChangeInProgress {
		log.Ctx(ctx).Error().Str("subscription_id", req.SubscriptionId).Str("change_description", seatChangeDescription).Msg("Blocking UpdateSubscription due to seat change in progress")
		return nil, status.Error(codes.FailedPrecondition, "Seat change in progress.")
	}

	// Check for any plan/tier changes in progress before proceeding
	changeInProgress, changeDescription, err := CheckTeamPlanChanges(ctx, s.daoFactory, s.featureFlagHandle, tenant)
	if err != nil {
		log.Ctx(ctx).Error().Str("tenant_id", tenant.Id).Err(err).Msgf("Error checking for plan or tier change in progress for tenant %s", tenant.Id)
		return nil, fmt.Errorf("UpdateSubscription: failed to check for plan or tier change in progress: %w", err)
	}
	if changeInProgress {
		log.Ctx(ctx).Error().Str("tenant_id", tenant.Id).Str("change_description", changeDescription).Msg("Blocking UpdateSubscription due to plan or tier change in progress")
		return nil, status.Error(codes.FailedPrecondition, "Plan or tier change in progress.")
	}

	// If they are not on the trial, check that they have a payment method
	if subscription.ExternalPlanId != s.orbConfig.GetTrialPlan().ID {
		// Get Stripe Customer ID from the tenant Subscription Mapping
		// We can use tenant subscription mapping because we know this is a team
		tenantSubscriptionMappingDAO := s.daoFactory.GetTenantSubscriptionMappingDAO()
		tenantSubscriptionMapping, err := tenantSubscriptionMappingDAO.Get(ctx, *tenantId)
		if err != nil {
			log.Ctx(ctx).Error().Err(err).Str("tenant_id", *tenantId).Msg("Failed to fetch tenant subscription mapping")
			return nil, status.Error(codes.Internal, "Failed to fetch tenant subscription mapping")
		}
		if tenantSubscriptionMapping == nil {
			log.Ctx(ctx).Error().Str("tenant_id", *tenantId).Msg("Tenant subscription mapping not found")
			return nil, status.Error(codes.NotFound, "Tenant subscription mapping not found")
		}

		hasPaymentMethod, err := s.stripeClient.HasPaymentMethod(tenantSubscriptionMapping.StripeCustomerId)
		if err != nil {
			log.Ctx(ctx).Error().Err(err).Str("stripe_customer_id", tenantSubscriptionMapping.StripeCustomerId).Msg("Failed to check payment method status")
			return nil, status.Error(codes.Internal, "Failed to check payment method status")
		}
		if !hasPaymentMethod {
			log.Ctx(ctx).Error().Str("stripe_customer_id", tenantSubscriptionMapping.StripeCustomerId).Msg("User does not have a payment method")
			return nil, status.Error(codes.FailedPrecondition, "User does not have a payment method")
		}
	}

	// Validate that the subscription is not ended
	if subscription.OrbStatus == auth_entities.Subscription_ORB_STATUS_ENDED {
		log.Ctx(ctx).Error().Str("subscription_id", req.SubscriptionId).Msg("Subscription is ended")
		return nil, status.Error(codes.FailedPrecondition, "Subscription is ended")
	}

	// Audit Log
	auditLogText := fmt.Sprintf("updating subscription %s for tenant %s to %d seats", req.SubscriptionId, *tenantId, req.Seats)
	if req.Reason != "" {
		auditLogText += fmt.Sprintf(" for reason: %s", req.Reason)
	}
	s.WriteTeamsAuditLog(ctx, auditLogText,
		audit.NewTenantID(*tenantId),
		audit.NewTenantName(*tenantId), // valid for self-serve
		audit.NewProtoRequest(req),
	)

	// Validate that the requested seat count is sufficient
	err = s.ValidateSubscriptionSeats(ctx, *tenantId, req.Seats, 0)
	if err != nil {
		return nil, err
	}

	// Publish seat update to pubsub queue and update subscription change info in database
	updateSubscriptionID := uuid.New().String()
	updateMsg := &auth_internal.UpdateSubscriptionMessage{
		Id:             updateSubscriptionID,
		SubscriptionId: req.SubscriptionId,
		NumSeats:       req.Seats,
		PublishTime:    timestamppb.Now(),
	}
	err = s.asyncOpsPublisher.PublishUpdateSubscription(ctx, updateMsg)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to publish update subscription message")
		return nil, status.Error(codes.Internal, "Failed to publish update subscription message")
	}

	// Update subscription DAO with update ID
	_, err = subscriptionDAO.TryUpdate(ctx, req.SubscriptionId, func(sub *auth_entities.Subscription) bool {
		sub.SubscriptionChangeId = &updateSubscriptionID
		return true
	}, DefaultRetry)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to update subscription with change info")
		return nil, status.Error(codes.Internal, "Failed to update subscription with change info")
	}

	return &authpb.UpdateSubscriptionResponse{}, nil
}

// PutUserOnPlan associates a user with a plan
func (s *TeamManagementServer) PutUserOnPlan(
	ctx context.Context, req *authpb.PutUserOnPlanRequest,
) (*authpb.PutUserOnPlanResponse, error) {
	if req.UserId == "" {
		log.Ctx(ctx).Error().Msg("User ID is required")
		return nil, status.Error(codes.InvalidArgument, "User ID is required")
	}
	if req.PlanId == "" {
		log.Ctx(ctx).Error().Msg("Plan ID is required")
		return nil, status.Error(codes.InvalidArgument, "Plan ID is required")
	}

	// Get the user to check if they exist
	userDAO := s.daoFactory.GetUserDAO()
	user, err := userDAO.Get(ctx, req.UserId)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msgf("Failed to get user %s", req.UserId)
		return nil, status.Error(codes.Internal, "Failed to get user information")
	}
	if user == nil {
		log.Ctx(ctx).Error().Msgf("User %s not found", req.UserId)
		return nil, status.Error(codes.NotFound, "User not found")
	}

	if user.StripeCustomerId == "" {
		log.Ctx(ctx).Error().Str("user_id", req.UserId).Msg("User does not have a stripe customer ID")
		return nil, status.Error(codes.FailedPrecondition, "User does not have a stripe customer ID")
	}

	if user.OrbCustomerId == "" {
		log.Ctx(ctx).Error().Str("user_id", req.UserId).Msg("User does not have an orb customer ID")
		return nil, status.Error(codes.FailedPrecondition, "User does not have an orb customer ID")
	}

	if len(user.Tenants) != 1 {
		log.Ctx(ctx).Error().Str("user_id", req.UserId).Msg("User does not belong to exactly one tenant")
		return nil, status.Error(codes.FailedPrecondition, "User does not belong to exactly one tenant")
	}

	// Get the current tenant
	currentTenantID := user.Tenants[0]
	currentTenant, err := s.tenantMap.GetTenantByID(ctx, currentTenantID)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("tenant_id", currentTenantID).Msg("Failed to get tenant")
		return nil, status.Error(codes.Internal, "Failed to get tenant information")
	}
	if currentTenant == nil {
		log.Ctx(ctx).Error().Str("tenant_id", currentTenantID).Msg("Tenant not found")
		return nil, status.Error(codes.NotFound, "Tenant not found")
	}

	// Check for any plan/tier changes in progress before proceeding
	if changeInProgress, changeDescription, err := CheckTierOrPlanChanges(ctx, s.daoFactory, s.tenantMap, s.featureFlagHandle, req.UserId); err != nil {
		log.Ctx(ctx).Error().Str("user_id", req.UserId).Err(err).Msgf("Error checking for plan or tier change in progress for user %s", req.UserId)
		return nil, fmt.Errorf("PutUserOnPlan: failed to check for plan or tier change in progress: %w", err)
	} else if changeInProgress {
		log.Ctx(ctx).Error().Str("user_id", req.UserId).Str("change_description", changeDescription).Msg("Blocking PutUserOnPlan due to plan or tier change in progress")
		return nil, status.Error(codes.FailedPrecondition, "User has a plan or tier change in progress.")
	}
	// Check for seat changes
	if changeInProgress, changeDescription, err := CheckSeatChanges(ctx, s.daoFactory, s.tenantMap, s.featureFlagHandle, req.UserId); err != nil {
		log.Ctx(ctx).Error().Err(err).Msgf("Failed to check seat changes for user %s", req.UserId)
		return nil, err
	} else if changeInProgress {
		log.Ctx(ctx).Error().Str("user_id", req.UserId).Str("change_description", changeDescription).Msg("Blocking PutUserOnPlan due to seat change in progress")
		return nil, status.Error(codes.FailedPrecondition, "User has a seat change in progress.")
	}

	// Check if this is a self-serve team tenant
	isSelfServeTeam := tenantutil.IsSelfServeTeamTenant(currentTenant)

	// Handle teams and individual users differently
	if isSelfServeTeam {
		return s.putTeamOnPlan(ctx, req, user, currentTenant)
	} else {
		return s.putIndividualUserOnPlan(ctx, req, user, currentTenant)
	}
}

// putTeamOnPlan handles putting a team on a plan
func (s *TeamManagementServer) putTeamOnPlan(
	ctx context.Context,
	req *authpb.PutUserOnPlanRequest,
	user *auth_entities.User,
	currentTenant *tw_pb.Tenant,
) (*authpb.PutUserOnPlanResponse, error) {
	// For team tenants, the user must be an admin
	err := s.teamManagementAuthCheck(ctx, &currentTenant.Id, &req.UserId, tokenscopesproto.Scope_AUTH_RW, true, "PutTeamOnPlan")
	if err != nil {
		return nil, err
	}

	// If IAP User, require reason
	if err := requireReasonForIAPUser(ctx, req.Reason); err != nil {
		return nil, err
	}

	auditLogText := fmt.Sprintf("putting team with tenant ID %s on plan %s", currentTenant.Id, req.PlanId)
	if req.Reason != "" {
		auditLogText += fmt.Sprintf(" for reason: %s", req.Reason)
	}
	s.WriteTeamsAuditLog(ctx, auditLogText,
		audit.NewTenantID(currentTenant.Id),
		audit.NewTenantName(currentTenant.Name),
		audit.NewUser(req.UserId),
	)

	// Use the plan ID directly
	targetOrbPlanID := req.PlanId

	// Validate that the plan exists in our configuration
	plan := s.orbConfig.FindPlan(targetOrbPlanID)
	if plan == nil {
		log.Ctx(ctx).Error().Str("plan_id", targetOrbPlanID).Msg("Plan not found in configuration")
		return nil, status.Error(codes.Internal, "Plan not found in configuration")
	}

	// Check if teams can be on this plan (community plans are not allowed for teams)
	switch plan.Features.PlanType {
	case orb_config.PlanTypeCommunity:
		log.Ctx(ctx).Error().Str("tenant_id", currentTenant.Id).Str("plan_id", targetOrbPlanID).Msg("Teams cannot be put on the community plan")
		return nil, status.Error(codes.InvalidArgument, "Teams cannot be put on the community plan")
	case orb_config.PlanTypePaid:
		// Teams can be on paid plans - continue
	default:
		log.Ctx(ctx).Error().Str("tenant_id", currentTenant.Id).Str("plan_id", targetOrbPlanID).Str("plan_type", string(plan.Features.PlanType)).Msg("Invalid plan type for teams")
		return nil, status.Error(codes.Internal, "Unknown plan type")
	}

	// Get the tenant subscription mapping to retrieve Orb subscription ID
	tenantSubscriptionMappingDAO := s.daoFactory.GetTenantSubscriptionMappingDAO()
	subscriptionMapping, err := tenantSubscriptionMappingDAO.Get(ctx, currentTenant.Id)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("tenant_id", currentTenant.Id).Msg("Failed to get tenant subscription mapping")
		return nil, status.Error(codes.Internal, "Failed to get tenant subscription mapping")
	}
	if subscriptionMapping == nil {
		log.Ctx(ctx).Error().Str("tenant_id", currentTenant.Id).Msg("Tenant subscription mapping not found")
		return nil, status.Error(codes.NotFound, "Tenant subscription mapping not found")
	}

	// Ensure that the team has a payment method
	// They will need one for any plan they go to, as they can't go to community
	hasPaymentMethod, err := s.stripeClient.HasPaymentMethod(subscriptionMapping.StripeCustomerId)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("stripe_customer_id", subscriptionMapping.StripeCustomerId).Msg("Failed to check payment method status")
		return nil, status.Error(codes.Internal, "Failed to check payment method status")
	}
	if !hasPaymentMethod {
		log.Ctx(ctx).Error().Str("stripe_customer_id", subscriptionMapping.StripeCustomerId).Str("tenant_id", currentTenant.Id).Msg("Team does not have a payment method")
		return nil, status.Error(codes.FailedPrecondition, "Team does not have a payment method")
	}

	// Get current Orb subscription information to check the current plan
	orbSubscriptionID := subscriptionMapping.OrbSubscriptionId
	orbSubInfo, err := s.orbClient.GetUserSubscription(ctx, orbSubscriptionID, &orb.ItemIds{
		SeatsID:            s.orbConfig.SeatsItemID,
		IncludedMessagesID: s.orbConfig.IncludedMessagesItemID,
	})
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("orb_subscription_id", orbSubscriptionID).Msg("Failed to get Orb subscription info")
		return nil, status.Error(codes.Internal, "Failed to get Orb subscription info")
	}
	if orbSubInfo == nil {
		log.Ctx(ctx).Error().Str("orb_subscription_id", orbSubscriptionID).Msg("Orb subscription info not found")
		return nil, status.Error(codes.NotFound, "Orb subscription info not found")
	}

	// If the team is already on the requested Orb plan and the plan is active, no need to change
	if orbSubInfo.ExternalPlanID == targetOrbPlanID && orbSubInfo.OrbStatus == "active" {
		log.Ctx(ctx).Info().
			Str("tenant_id", currentTenant.Id).
			Str("orb_plan_id", targetOrbPlanID).
			Msg("Team is already on the requested Orb plan and the plan is active, no change needed")
		return &authpb.PutUserOnPlanResponse{}, nil
	}

	// Generate a unique plan change ID
	planChangeID := uuid.New().String()

	// Construct the TeamPlanChangeMessage for async processing
	planChangeMsg := &auth_internal.TeamPlanChangeMessage{
		TeamTenantId:      currentTenant.Id,
		TargetOrbPlanId:   targetOrbPlanID,
		PlanChangeId:      planChangeID,
		PublishTime:       timestamppb.Now(),
		InitiatedByUserId: user.Id,
	}

	// Publish the plan change message
	if err := s.asyncOpsPublisher.PublishTeamPlanChange(ctx, planChangeMsg); err != nil {
		log.Ctx(ctx).Error().
			Err(err).
			Str("tenant_id", currentTenant.Id).
			Str("plan_change_id", planChangeID).
			Str("target_orb_plan_id", targetOrbPlanID).
			Msg("Failed to publish team Orb plan change message")
		return nil, status.Errorf(codes.Internal,
			"Failed to publish team Orb plan change: tenant_id=%s, plan_change_id=%s: %v",
			currentTenant.Id, planChangeID, err)
	}

	// Update the tenant subscription mapping with the plan change info
	newMapping, err := tenantSubscriptionMappingDAO.TryUpdate(ctx, currentTenant.Id, func(mapping *auth_entities.TenantSubscriptionMapping) bool {
		if mapping.PlanChange != nil {
			log.Ctx(ctx).Warn().
				Str("tenant_id", currentTenant.Id).
				Str("plan_change_id", planChangeID).
				Msg("Plan change already in progress, skipping update")
			return false
		}

		mapping.PlanChange = &auth_entities.TenantSubscriptionMapping_PlanChangeInfo{
			Id:              planChangeID,
			TargetOrbPlanId: targetOrbPlanID,
			CreatedAt:       timestamppb.Now(),
			UpdatedAt:       timestamppb.Now(),
		}
		return true
	}, DefaultRetry)
	if err != nil {
		log.Ctx(ctx).Error().
			Err(err).
			Str("tenant_id", currentTenant.Id).
			Str("plan_change_id", planChangeID).
			Str("target_orb_plan_id", targetOrbPlanID).
			Msg("Failed to update tenant subscription mapping with plan change info")
		return nil, status.Errorf(codes.Internal,
			"Failed to update tenant subscription mapping with plan change info: tenant_id=%s, plan_change_id=%s: %v",
			currentTenant.Id, planChangeID, err)
	}

	if newMapping.PlanChange == nil {
		log.Ctx(ctx).Error().
			Str("tenant_id", currentTenant.Id).
			Str("plan_change_id", planChangeID).
			Str("target_orb_plan_id", targetOrbPlanID).
			Msg("Failed to update tenant subscription mapping with plan change info")
		return nil, status.Errorf(codes.Internal,
			"Failed to update tenant subscription mapping with plan change info: tenant_id=%s, plan_change_id=%s",
			currentTenant.Id, planChangeID)
	}

	log.Ctx(ctx).Info().
		Str("tenant_id", currentTenant.Id).
		Str("plan_change_id", planChangeID).
		Str("current_orb_plan_id", orbSubInfo.ExternalPlanID).
		Str("target_orb_plan_id", targetOrbPlanID).
		Msg("Team Orb plan change successfully initiated")

	s.WriteTeamsAuditLog(ctx, fmt.Sprintf("successfully initiated Orb plan change for team with tenant ID %s from plan %s to plan %s",
		currentTenant.Id, orbSubInfo.ExternalPlanID, targetOrbPlanID))

	return &authpb.PutUserOnPlanResponse{}, nil
}

// checkCommunityConversionBlocks checks if conversions to community plans should be blocked
// based on the current plan type and feature flags
func (s *TeamManagementServer) checkCommunityConversionBlocks(
	ctx context.Context,
	user_id string,
	currentPlan *orb_config.PlanConfig,
	targetPlan *orb_config.PlanConfig,
) error {
	// If we can't determine the current plan, allow the conversion
	if currentPlan == nil {
		log.Ctx(ctx).Warn().Str("user_id", user_id).Msg("Could not determine current plan for community conversion check, allowing conversion")
		return nil
	}

	// Check if conversion should be blocked based on current plan type
	switch currentPlan.Features.PlanType {
	case orb_config.PlanTypePaidTrial:
		blockTrialConversions, err := BlockTrialToCommunityConversions.Get(s.featureFlagHandle)
		if err != nil {
			log.Ctx(ctx).Error().Err(err).Msg("Error reading block trial to community conversions feature flag")
			return nil // Allow conversion if we can't read the flag
		}
		if blockTrialConversions {
			log.Ctx(ctx).Info().Str("user_id", user_id).Str("current_plan_id", currentPlan.ID).Msg("Blocking trial to community conversion due to feature flag")
			return status.Error(codes.FailedPrecondition, "Trial to community plan conversions are currently disabled")
		}
	case orb_config.PlanTypePaid:
		blockPaidConversions, err := BlockPaidToCommunityConversions.Get(s.featureFlagHandle)
		if err != nil {
			log.Ctx(ctx).Error().Err(err).Msg("Error reading block paid to community conversions feature flag")
			return nil // Allow conversion if we can't read the flag
		}
		if blockPaidConversions {
			log.Ctx(ctx).Info().Str("user_id", user_id).Str("current_plan_id", currentPlan.ID).Msg("Blocking paid to community conversion due to feature flag")
			return status.Error(codes.FailedPrecondition, "Paid to community plan conversions are currently disabled")
		}
	case orb_config.PlanTypeCommunity:
		// Community to community is allowed (no-op)
		log.Ctx(ctx).Debug().Str("user_id", user_id).Msg("User is already on community plan")
	default:
		log.Ctx(ctx).Warn().Str("user_id", user_id).Str("current_plan_type", string(currentPlan.Features.PlanType)).Msg("Unknown current plan type for community conversion check")
	}

	return nil
}

// putIndividualUserOnPlan handles putting an individual user on a plan
func (s *TeamManagementServer) putIndividualUserOnPlan(
	ctx context.Context,
	req *authpb.PutUserOnPlanRequest,
	user *auth_entities.User,
	currentTenant *tw_pb.Tenant,
) (*authpb.PutUserOnPlanResponse, error) {
	// For individual users, they can only change their own subscription
	err := s.teamManagementAuthCheck(ctx, nil, &req.UserId, tokenscopesproto.Scope_AUTH_RW, false, "PutUserOnPlan")
	if err != nil {
		return nil, err
	}

	// If IAP User, require reason
	if err := requireReasonForIAPUser(ctx, req.Reason); err != nil {
		return nil, err
	}

	// If not going to the community plan, ensure that the user has a payment method
	if req.PlanId != s.orbConfig.GetCommunityPlan().ID {
		hasPaymentMethod, err := s.stripeClient.HasPaymentMethod(user.StripeCustomerId)
		if err != nil {
			log.Ctx(ctx).Error().Err(err).Str("user_id", req.UserId).Str("stripe_customer_id", user.StripeCustomerId).Msg("Failed to check payment method status")
			return nil, status.Error(codes.Internal, "Failed to check payment method status")
		}
		if !hasPaymentMethod {
			log.Ctx(ctx).Error().Str("user_id", req.UserId).Str("stripe_customer_id", user.StripeCustomerId).Msg("User does not have a payment method")
			return nil, status.Error(codes.FailedPrecondition, "User does not have a payment method")
		}
	}

	// Get current subscription information to check the current plan
	var currentPlan *orb_config.PlanConfig
	if user.OrbSubscriptionId != "" {
		currentSubscription, err := s.orbClient.GetUserSubscription(ctx, user.OrbSubscriptionId, nil)
		if err != nil {
			log.Ctx(ctx).Error().Err(err).Str("orb_subscription_id", user.OrbSubscriptionId).Msg("Failed to get current Orb subscription info")
			return nil, status.Error(codes.Internal, "Failed to get current Orb subscription info")
		}
		if currentSubscription != nil {
			currentPlan = s.orbConfig.GetPlan(currentSubscription.ExternalPlanID)
		}
	}

	if currentTenant.Tier != tw_pb.TenantTier_COMMUNITY && currentTenant.Tier != tw_pb.TenantTier_PROFESSIONAL {
		log.Ctx(ctx).Error().Str("tenant_name", currentTenant.Name).Str("tenant_tier", currentTenant.Tier.String()).Msg("Invalid tenant tier")
		return nil, status.Error(codes.InvalidArgument, "Current tenant must be either professional or community tier")
	}

	// Validate that the plan exists in our configuration
	plan := s.orbConfig.FindPlan(req.PlanId)
	if plan == nil {
		log.Ctx(ctx).Error().Str("plan_id", req.PlanId).Msg("Plan not found in configuration")
		return nil, status.Error(codes.InvalidArgument, "Plan not found in configuration")
	}

	// Check if conversion should be blocked based on current plan type
	if plan.Features.PlanType == orb_config.PlanTypeCommunity {
		if err := s.checkCommunityConversionBlocks(ctx, req.UserId, currentPlan, plan); err != nil {
			return nil, err
		}
	}

	// Set the tier based on the plan type
	var newTier auth_entities.UserTier
	var newTierAsTenantTier tw_pb.TenantTier
	switch plan.Features.PlanType {
	case orb_config.PlanTypeCommunity:
		newTier = auth_entities.UserTier_COMMUNITY
		newTierAsTenantTier = tw_pb.TenantTier_COMMUNITY
	case orb_config.PlanTypePaid:
		newTier = auth_entities.UserTier_PROFESSIONAL
		newTierAsTenantTier = tw_pb.TenantTier_PROFESSIONAL
	default:
		log.Ctx(ctx).Error().Str("plan_id", req.PlanId).Str("plan_type", string(plan.Features.PlanType)).Msg("Invalid plan type")
		return nil, status.Error(codes.InvalidArgument, "Invalid plan type")
	}

	newTenant := currentTenant
	if currentTenant.Tier != newTierAsTenantTier {
		var newTenantName string
		var tenantNamesStr string
		switch newTier {
		case auth_entities.UserTier_COMMUNITY:
			tenantNamesStr, err = s.featureFlagHandle.GetString("auth_central_signup_tenant", "")
		case auth_entities.UserTier_PROFESSIONAL:
			tenantNamesStr, err = s.featureFlagHandle.GetString("auth_central_individual_tenant", "")
		}
		if err != nil {
			log.Ctx(ctx).Error().Err(err).Str("user_id", req.UserId).Str("plan_id", req.PlanId).Msg("PutUserOnPlan failed: error getting tenant name from feature flag")
			return nil, status.Error(codes.Internal, fmt.Sprintf("Failed to get new tenant name for plan %s", req.PlanId))
		}

		if tenantNamesStr == "" {
			log.Ctx(ctx).Error().Str("user_id", req.UserId).Str("plan_id", req.PlanId).Msg("PutUserOnPlan failed: tenant name not configured")
			return nil, status.Error(codes.Internal, fmt.Sprintf("Default tenant not configured for plan %s", req.PlanId))
		}

		// Split comma-separated list of tenant names
		tenantNames := strings.Split(tenantNamesStr, ",")

		// Choose a tenant randomly
		if len(tenantNames) > 0 {
			newTenantName = tenantNames[s.randomSelector.Intn(len(tenantNames))]
			log.Ctx(ctx).Info().
				Str("selected_tenant", newTenantName).
				Strs("available_tenants", tenantNames).
				Msg("Selected tenant for user plan change")
		}

		// Get new tenant
		newTenant, err = s.tenantMap.GetTenant(ctx, newTenantName)
		if err != nil {
			log.Ctx(ctx).Error().Err(err).Str("user_id", req.UserId).Str("tenant_name", newTenantName).Msg("PutUserOnPlan failed: error fetching new tenant")
			return nil, status.Error(codes.Internal, "Failed to get new tenant")
		}
		if newTenant == nil {
			log.Ctx(ctx).Error().Str("user_id", req.UserId).Str("tenant_name", newTenantName).Msg("PutUserOnPlan failed: new tenant not found")
			return nil, status.Error(codes.Internal, "New tenant not found")
		}
	}

	// Generate a unique tier change ID
	tierChangeID := uuid.New().String()

	// Construct the UserTierChangeMessage
	tierChangeMsg := &auth_internal.UserTierChangeMessage{
		User:          user,
		CurrentTenant: currentTenant,
		NewTier:       newTier,
		NewTenant:     newTenant,
		TierChangeId:  tierChangeID,
		PublishTime:   timestamppb.Now(),
		NewPlanId:     req.PlanId,
	}

	auditLogText := fmt.Sprintf("putting individual user %s on plan %s", req.UserId, req.PlanId)
	if req.Reason != "" {
		auditLogText += fmt.Sprintf(" for reason: %s", req.Reason)
	}
	s.WriteTeamsAuditLog(ctx, auditLogText,
		audit.NewUser(req.UserId),
		audit.NewTenantName(currentTenant.Name),
		audit.NewTenantID(currentTenant.Id),
		audit.NewProtoRequest(req),
	)

	// Publish the tier change message
	if err := s.asyncOpsPublisher.PublishUserTierChange(ctx, tierChangeMsg); err != nil {
		log.Ctx(ctx).Error().
			Err(err).
			Str("user_id", user.Id).
			Str("tier_change_id", tierChangeID).
			Str("plan_id", req.PlanId).
			Msg("Failed to publish tier change message")
		return nil, status.Errorf(codes.Internal,
			"Failed to publish user tier change: user_id=%s, tier_change_id=%s: %v",
			user.Id, tierChangeID, err)
	}

	// Update the user with the tier change info
	updateUser := func(u *auth_entities.User) bool {
		// If the user already has a tier change in progress, don't update
		if u.TierChange != nil {
			log.Ctx(ctx).Warn().
				Str("user_id", user.Id).
				Str("existing_tier_change_id", u.TierChange.Id).
				Str("new_tier_change_id", tierChangeID).
				Msg("User already has a tier change in progress during update")
			// Return false to indicate no changes were made
			return false
		}

		// Only set the tier change if it wasn't already set
		u.TierChange = &auth_entities.User_TierChangeInfo{
			Id:         tierChangeID,
			TargetTier: newTier,
			CreatedAt:  timestamppb.Now(),
			UpdatedAt:  timestamppb.Now(),
		}
		return true
	}

	newUser, err := s.daoFactory.GetUserDAO().TryUpdate(ctx, req.UserId, updateUser, DefaultRetry)
	if err != nil {
		log.Ctx(ctx).Error().
			Err(err).
			Str("user_id", user.Id).
			Str("tier_change_id", tierChangeID).
			Msg("Failed to update user with tier change info")
		return nil, status.Errorf(codes.Internal,
			"Failed to update user with tier change info: user_id=%s, tier_change_id=%s: %v",
			user.Id, tierChangeID, err)
	}

	if newUser.TierChange == nil {
		log.Ctx(ctx).Error().
			Str("user_id", user.Id).
			Str("tier_change_id", tierChangeID).
			Msg("Failed to update user with tier change info")
		return nil, status.Errorf(codes.Internal,
			"Failed to update user with tier change info: user_id=%s, tier_change_id=%s",
			user.Id, tierChangeID)
	}

	log.Ctx(ctx).Info().
		Str("user_id", user.Id).
		Str("tier_change_id", tierChangeID).
		Str("plan_id", req.PlanId).
		Msg("Individual user plan change successfully initiated")

	s.WriteTeamsAuditLog(ctx, fmt.Sprintf("successfully initiated plan change for individual user %s to plan %s", user.Id, req.PlanId))

	return &authpb.PutUserOnPlanResponse{}, nil
}

// ValidateSubscriptionSeats checks if a subscription has enough seats for the current team members,
// pending invitations, and optionally additional new invitations.
func (s *TeamManagementServer) ValidateSubscriptionSeats(
	ctx context.Context,
	tenantID string,
	seatLimit int32,
	newInvitationCount int,
) error {
	// 1. Count active team members in the tenant
	activeTeamMemberCount, err := GetActiveTeamMemberCount(ctx, s.daoFactory, s.tenantMap, tenantID)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("tenant_id", tenantID).Msg("Failed to fetch team members")
		return status.Error(codes.Internal, "Failed to fetch team members")
	}

	// 2. Count pending invitations for the tenant
	pendingInvitations, err := GetInvitationsForTenant(
		ctx, s.daoFactory, tenantID, auth_entities.TenantInvitation_PENDING,
	)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("tenant_id", tenantID).Msg("Failed to fetch pending invitations")
		return status.Error(codes.Internal, "Failed to fetch pending invitations")
	}
	pendingInvitationCount := len(pendingInvitations)

	// 3. Calculate total required seats
	totalRequired := activeTeamMemberCount + pendingInvitationCount + newInvitationCount

	// 4. Validate that available seats are sufficient
	if totalRequired > int(seatLimit) {
		log.Ctx(ctx).Error().Int("seat_limit", int(seatLimit)).Int("active_members", activeTeamMemberCount).Int("pending_invitations", pendingInvitationCount).Int("new_invitations", newInvitationCount).Msg("Not enough seats available")
		return status.Error(
			codes.InvalidArgument,
			fmt.Sprintf("Not enough seats available. You need %d seats but only have %d. Please upgrade your subscription to add more team members.", totalRequired, seatLimit),
		)
	}

	return nil
}

// Purchase additional Orb usage units
// We use "Credits" as the input here, but "Credits" is actually the number of user messages.
func (s *TeamManagementServer) PurchaseCredits(
	ctx context.Context, req *authpb.PurchaseCreditsRequest,
) (*authpb.PurchaseCreditsResponse, error) {
	// Ensure Orb is enabled
	if s.orbClient == nil || !s.orbConfig.Enabled {
		log.Ctx(ctx).Error().Str("augment_user_id", req.UserId).Str("tenant_id", req.TenantId).Float32("num_purchasing", req.Credits).Msg("Orb is not enabled")
		return nil, status.Error(codes.Internal, "Orb is not enabled")
	}

	// Check the requests inputs
	if req.UserId == "" {
		log.Ctx(ctx).Error().Str("tenant_id", req.TenantId).Float32("num_purchasing", req.Credits).Msg("Invalid (empty) user ID")
		return nil, status.Error(codes.InvalidArgument, "Invalid (empty) user ID")
	}
	if req.TenantId == "" {
		log.Ctx(ctx).Error().Str("augment_user_id", req.UserId).Float32("num_purchasing", req.Credits).Msg("Invalid (empty) tenant ID")
		return nil, status.Error(codes.InvalidArgument, "Invalid (empty) tenant ID")
	}

	// Get Orb Customer ID from User
	billingInfo, err := GetUserBillingInfo(ctx, req.UserId, req.TenantId, s.daoFactory, s.tenantMap)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("augment_user_id", req.UserId).Str("tenant_id", req.TenantId).Float32("num_purchasing", req.Credits).Msg("Failed to get user billing info")
		return nil, status.Error(codes.Internal, "Failed to get user billing info")
	}

	// Perform auth check
	adminNecessary := billingInfo.IsSelfServeTeam // If the user is on a team, they must be an admin to purchase credits
	err = s.teamManagementAuthCheck(ctx, nil, &req.UserId, tokenscopesproto.Scope_AUTH_RW, adminNecessary, "PurchaseCredits")
	if err != nil {
		return nil, err
	}
	// Auth claims have already been checked above, so we know this parsing will succeed.
	authInfo, _ := auth.GetAugmentClaims(ctx)

	// Ensure the user has a payment method saved
	hasPaymentMethod, err := s.stripeClient.HasPaymentMethod(billingInfo.StripeCustomerID)
	log.Ctx(ctx).Info().Bool("has_payment_method", hasPaymentMethod).Str("augment_user_id", req.UserId).Str("stripe_customer_id", billingInfo.StripeCustomerID).Msg("Checked payment method status")
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("augment_user_id", req.UserId).Str("stripe_customer_id", billingInfo.StripeCustomerID).Float32("num_purchasing", req.Credits).Msg("Failed to check payment method status")
		return nil, status.Error(codes.Internal, "Failed to check payment method status")
	}
	if !hasPaymentMethod {
		log.Ctx(ctx).Error().Str("augment_user_id", req.UserId).Str("stripe_customer_id", billingInfo.StripeCustomerID).Float32("num_purchasing", req.Credits).Msg("User does not have a payment method")
		return nil, status.Error(codes.FailedPrecondition, "User does not have a payment method")
	}

	// Audit Log
	// TODO(sophie): if we add "reason" or flexible cost basis to purchase credits, add that in audit log
	s.WriteTeamsAuditLog(ctx, fmt.Sprintf("purchasing %f requests for Orb customer %s", req.Credits, billingInfo.OrbCustomerID),
		audit.NewUser(req.UserId),
		audit.NewTenantID(req.TenantId),
		audit.NewTenantName(req.TenantId), // valid for self-serve
		audit.NewProtoRequest(req),
	)

	// Get the user's Orb subscription
	orbSubscription, err := s.orbClient.GetUserSubscription(ctx, billingInfo.OrbSubscriptionID, nil)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("augment_user_id", req.UserId).Str("orb_customer_id", billingInfo.OrbCustomerID).Str("orb_subscription_id", billingInfo.OrbSubscriptionID).Msg("Failed to get user subscription")
		return nil, status.Error(codes.Internal, "Failed to get user subscription")
	}

	// return failed precondiiton if we have a canceled subscription
	if orbSubscription.OrbStatus == "ended" {
		log.Ctx(ctx).Error().Str("augment_user_id", req.UserId).Str("orb_customer_id", billingInfo.OrbCustomerID).Str("orb_subscription_id", billingInfo.OrbSubscriptionID).Msg("Cannot purchase credits on a canceled subscription")
		return nil, status.Error(codes.FailedPrecondition, "Cannot purchase credits on a canceled subscription")
	}

	plan := s.orbConfig.FindPlan(orbSubscription.ExternalPlanID)
	if plan == nil {
		log.Ctx(ctx).Error().Str("orb_subscription_id", orbSubscription.OrbSubscriptionID).Str("plan_id", orbSubscription.ExternalPlanID).Msg("Failed to find plan")
		return nil, status.Error(codes.Internal, "Failed to find plan")
	}

	// Ensure the user is not on the trial plan
	if plan.Features.PlanType == orb_config.PlanTypePaidTrial {
		log.Ctx(ctx).Error().Str("augment_user_id", req.UserId).Str("orb_customer_id", billingInfo.OrbCustomerID).Str("plan_id", orbSubscription.ExternalPlanID).Msg("Cannot purchase credits on a trial plan")
		return nil, status.Error(codes.InvalidArgument, "Cannot purchase credits on a trial plan")
	}

	// Get the cost per message and total cost of the purchase
	costPerMessage := s.orbConfig.CostPerMessage
	totalCost := costPerMessage * float64(req.Credits)

	// Confirm that the spend is within bounds given the message cost and min/max purchase amounts
	if totalCost > s.orbConfig.MaxAddonPurchase {
		log.Ctx(ctx).Error().Str("augment_user_id", req.UserId).Str("orb_customer_id", billingInfo.OrbCustomerID).Str("orb_subscription_id", billingInfo.OrbSubscriptionID).Float32("credits", req.Credits).Msgf("Cannot purchase more than $%f worth of messages at a time", s.orbConfig.MaxAddonPurchase)
		return nil, status.Error(codes.InvalidArgument, fmt.Sprintf("Cannot purchase more than $%.2f worth of messages at a time", s.orbConfig.MaxAddonPurchase))
	}
	if totalCost < s.orbConfig.MinAddonPurchase {
		log.Ctx(ctx).Error().Str("augment_user_id", req.UserId).Str("orb_customer_id", billingInfo.OrbCustomerID).Str("orb_subscription_id", billingInfo.OrbSubscriptionID).Float32("credits", req.Credits).Msgf("Cannot purchase less than $%f worth of messages at a time", s.orbConfig.MinAddonPurchase)
		return nil, status.Error(codes.InvalidArgument, fmt.Sprintf("Cannot purchase less than $%.2f worth of messages at a time", s.orbConfig.MinAddonPurchase))
	}

	// Create an Orb event for the credit purchase
	description := fmt.Sprintf("Purchased messages from Manage Subscription Page on %s by %s", time.Now().Format(time.DateOnly), billingInfo.Email)
	// The credit block should start at the start date of the current billing period per https://docs.withorb.com/self-serve/product-access#unblocking-product-access
	// We need to round the current billing period start date to midnight for the API to accept it
	roundedStartDate := time.Date(orbSubscription.CurrentBillingPeriodStartDate.Year(), orbSubscription.CurrentBillingPeriodStartDate.Month(), orbSubscription.CurrentBillingPeriodStartDate.Day(), 0, 0, 0, 0, orbSubscription.CurrentBillingPeriodStartDate.Location())
	err = s.orbClient.PurchaseCredits(ctx, orb.OrbCreditPurchase{
		CustomerOrbID: billingInfo.OrbCustomerID,
		NumberCredits: float64(req.Credits),
		Description:   description,
		CostBasis:     costPerMessage,              // cost basis = cost per message
		ExpiryDate:    time.Now().AddDate(1, 0, 0), // these expire in one year
		Currency:      s.orbConfig.PricingUnit,     // ex: "usermessages", "credits" -- the Orb currency
		StartDate:     &roundedStartDate,
	}, req.IdempotencyKey)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("augment_user_id", req.UserId).Str("orb_customer_id", billingInfo.OrbCustomerID).Msg("Failed to purchase credits")
		return nil, status.Error(codes.Internal, "Failed to purchase credits")
	}
	log.Ctx(ctx).Info().Str("augment_user_id", req.UserId).Str("tenant_id", req.TenantId).Str("orb_customer_id", billingInfo.OrbCustomerID).Float32("num_purchasing", req.Credits).Float64("total_cost", totalCost).Msg("Successfully purchased credits")

	s.WriteTeamsAuditLog(ctx, fmt.Sprintf("successfully purchased %f requests for Orb customer %s, at a cost basis of $%.2f per request for a total cost of $%.2f", req.Credits, billingInfo.OrbCustomerID, costPerMessage, totalCost))

	// Record this puchase in Request Insight.
	requestContext, riErr := requestcontext.FromGrpcContext(ctx)
	if riErr != nil {
		log.Ctx(ctx).Warn().Err(riErr).Msg("Failed to get request context")
	} else {
		event := ripublisher.NewSessionEvent()
		event.Event = &ripb.SessionEvent_PurchaseCredits{
			PurchaseCredits: &ripb.PurchaseCredits{
				OrbSubscriptionId: billingInfo.OrbSubscriptionID,
				Credits:           req.Credits,
			},
		}
		riErr := s.requestInsightPublisher.PublishSessionEvent(
			ctx,
			requestContext.RequestSessionId.String(),
			authInfo.GetOpaqueUserID(),
			&ripb.TenantInfo{
				TenantId:   authInfo.TenantID,
				TenantName: authInfo.TenantName,
			}, event)
		if riErr != nil {
			log.Ctx(ctx).Warn().Err(riErr).Msg("Failed to publish PurchaseCredits event")
		}
	}

	return &authpb.PurchaseCreditsResponse{}, nil
}

func (s *TeamManagementServer) CancelSubscription(
	ctx context.Context, req *authpb.CancelSubscriptionRequest,
) (*authpb.CancelSubscriptionResponse, error) {
	// Ensure Orb is enabled
	if s.orbClient == nil || !s.orbConfig.Enabled {
		log.Ctx(ctx).Error().Str("augment_user_id", req.UserId).Str("tenant_id", req.TenantId).Msg("Orb is not enabled")
		return nil, status.Error(codes.Internal, "Orb is not enabled")
	}

	// Check the requests inputs
	if req.UserId == "" {
		log.Ctx(ctx).Error().Str("tenant_id", req.TenantId).Msg("Invalid (empty) user ID")
		return nil, status.Error(codes.InvalidArgument, "Invalid (empty) user ID")
	}
	if req.TenantId == "" {
		log.Ctx(ctx).Error().Str("augment_user_id", req.UserId).Msg("Invalid (empty) tenant ID")
		return nil, status.Error(codes.InvalidArgument, "Invalid (empty) tenant ID")
	}

	// Get Orb Customer and Subscription ID from User's Billing Info
	billingInfo, err := GetUserBillingInfo(ctx, req.UserId, req.TenantId, s.daoFactory, s.tenantMap)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("augment_user_id", req.UserId).Str("tenant_id", req.TenantId).Msg("Failed to get user billing info")
		return nil, status.Error(codes.Internal, "Failed to get user billing info")
	}
	if billingInfo.OrbSubscriptionID == "" {
		tenant, err := s.tenantMap.GetTenant(ctx, req.TenantId)
		if err != nil {
			log.Ctx(ctx).Error().Err(err).Str("tenant_id", req.TenantId).Msg("Failed to get tenant")
			return nil, status.Error(codes.Internal, "Failed to get tenant")
		}
		// Enterprise users don't necessarily have Orb subscriptions
		if tenantutil.IsEnterpriseTenant(tenant) {
			log.Ctx(ctx).Info().Str("augment_user_id", req.UserId).Str("tenant_id", req.TenantId).Msg("User does not have an Orb subscription ID, but is in an enterprise tenant. Skipping.")
			return &authpb.CancelSubscriptionResponse{}, nil
		}
		log.Ctx(ctx).Error().Str("augment_user_id", req.UserId).Msg("User does not have an Orb subscription ID")
		return nil, status.Error(codes.Internal, "User does not have an active subscription")
	}

	// Perform auth check
	adminNecessary := billingInfo.IsSelfServeTeam // If the user is on a team, they must be an admin to cancel the subscription
	err = s.teamManagementAuthCheck(ctx, nil, &req.UserId, tokenscopesproto.Scope_AUTH_RW, adminNecessary, "CancelSubscription")
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("augment_user_id", req.UserId).Str("tenant_id", req.TenantId).Bool("admin_necessary", adminNecessary).Msg("Failed to check permissions")
		return nil, err
	}

	// Check IAP user reason requirement after auth check
	err = requireReasonForIAPUser(ctx, req.Reason)
	if err != nil {
		log.Error().Err(err).Msg("Reason is required for IAP users")
		return nil, err
	}

	// We know this will succeed because the auth check passed.
	authInfo, _ := auth.GetAugmentClaims(ctx)

	// Check for any plan/tier changes in progress before proceeding
	if changeInProgress, changeDescription, err := CheckTierOrPlanChanges(ctx, s.daoFactory, s.tenantMap, s.featureFlagHandle, req.UserId); err != nil {
		log.Ctx(ctx).Error().Str("user_id", req.UserId).Err(err).Msgf("Error checking for plan or tier change in progress for user %s", req.UserId)
		return nil, fmt.Errorf("CancelSubscription: failed to check for plan or tier change in progress: %w", err)
	} else if changeInProgress {
		log.Ctx(ctx).Error().Str("user_id", req.UserId).Str("change_description", changeDescription).Msg("Blocking CancelSubscription due to plan or tier change in progress")
		return nil, status.Error(codes.FailedPrecondition, "User has a plan or tier change in progress.")
	}
	// Check for seat changes
	if changeInProgress, changeDescription, err := CheckSeatChanges(ctx, s.daoFactory, s.tenantMap, s.featureFlagHandle, req.UserId); err != nil {
		log.Ctx(ctx).Error().Err(err).Msgf("Failed to check seat changes for user %s", req.UserId)
		return nil, err
	} else if changeInProgress {
		log.Ctx(ctx).Error().Str("user_id", req.UserId).Str("change_description", changeDescription).Msg("Blocking CancelSubscription due to seat change in progress")
		return nil, status.Error(codes.FailedPrecondition, "User has a seat change in progress.")
	}

	// Audit Log
	auditLog := fmt.Sprintf("cancelling subscription %s for user %s", billingInfo.OrbSubscriptionID, req.UserId)
	if req.Reason != "" {
		auditLog += fmt.Sprintf(" with reason %s", req.Reason)
	}
	s.WriteTeamsAuditLog(ctx, auditLog,
		audit.NewUser(req.UserId),
		audit.NewTenantID(req.TenantId),
		audit.NewTenantName(req.TenantId), // valid for self-serve
		audit.NewProtoRequest(req),
	)

	// Determine cancellation parameters
	cancelTime := orb.PlanChangeEndOfTerm
	if req.CancelImmediately {
		cancelTime = orb.PlanChangeImmediate
	}

	// Cancel the Orb subscription - unschedule any existing cancellation first
	err = s.orbClient.CancelOrbSubscriptionUnscheduleFirst(ctx, billingInfo.OrbSubscriptionID, cancelTime, nil)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).
			Str("augment_user_id", req.UserId).
			Str("orb_customer_id", billingInfo.OrbCustomerID).
			Str("orb_subscription_id", billingInfo.OrbSubscriptionID).
			Msg("Failed to cancel Orb subscription")
		return nil, status.Error(codes.Internal, "Failed to cancel Orb subscription")
	}
	log.Ctx(ctx).Info().
		Str("augment_user_id", req.UserId).
		Str("tenant_id", req.TenantId).
		Str("orb_customer_id", billingInfo.OrbCustomerID).
		Str("orb_subscription_id", billingInfo.OrbSubscriptionID).
		Msg("Successfully canceled Orb subscription")

	s.WriteTeamsAuditLog(ctx, fmt.Sprintf("successfully cancelled subscription %s for user %s", billingInfo.OrbSubscriptionID, req.UserId))

	// Record this cancellation in Request Insight.
	requestContext, riErr := requestcontext.FromGrpcContext(ctx)
	if riErr != nil {
		log.Ctx(ctx).Warn().Err(riErr).Msg("Failed to get request context")
	} else {
		event := ripublisher.NewSessionEvent()
		cancelSubscription := &ripb.CancelSubscription{
			OrbSubscriptionId: billingInfo.OrbSubscriptionID,
		}

		cancelSubscription.Feedback = req.Feedback

		event.Event = &ripb.SessionEvent_CancelSubscription{
			CancelSubscription: cancelSubscription,
		}
		riErr := s.requestInsightPublisher.PublishSessionEvent(
			ctx,
			requestContext.RequestSessionId.String(),
			authInfo.GetOpaqueUserID(),
			&ripb.TenantInfo{
				TenantId:   authInfo.TenantID,
				TenantName: authInfo.TenantName,
			}, event)
		if riErr != nil {
			log.Ctx(ctx).Warn().Err(riErr).Msg("Failed to publish CancelSubscription event")
		}
	}

	return &authpb.CancelSubscriptionResponse{}, nil
}

func (s *TeamManagementServer) DeleteAccount(
	ctx context.Context, req *authpb.DeleteAccountRequest,
) (*authpb.DeleteAccountResponse, error) {
	if req.UserId == "" {
		log.Ctx(ctx).Error().Str("tenant_id", req.TenantId).Msg("Invalid (empty) user ID")
		return nil, status.Error(codes.InvalidArgument, "Invalid (empty) user ID")
	}
	if req.TenantId == "" {
		log.Ctx(ctx).Error().Str("user_id", req.UserId).Msg("Invalid (empty) tenant ID")
		return nil, status.Error(codes.InvalidArgument, "Invalid (empty) tenant ID")
	}

	err := s.teamManagementAuthCheck(ctx, &req.TenantId, &req.UserId, tokenscopesproto.Scope_AUTH_RW, false, "DeleteAccount")
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("user_id", req.UserId).Str("tenant_id", req.TenantId).Msg("Failed to check permissions")
		return nil, err
	}

	tenant, err := s.tenantMap.GetTenantByID(ctx, req.TenantId)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("tenant_id", req.TenantId).Msg("Failed to get tenant")
		return nil, status.Error(codes.Internal, "Failed to get tenant")
	}
	if tenant == nil {
		log.Ctx(ctx).Error().Str("tenant_id", req.TenantId).Msg("Tenant not found")
		return nil, status.Error(codes.NotFound, "Tenant not found")
	}

	billingInfo, err := GetUserBillingInfo(ctx, req.UserId, req.TenantId, s.daoFactory, s.tenantMap)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("augment_user_id", req.UserId).Str("tenant_id", req.TenantId).Msg("Failed to get user billing info")
		return nil, status.Error(codes.Internal, "Failed to get user billing info")
	}

	s.WriteTeamsAuditLog(ctx,
		fmt.Sprintf("delete account request for user %s in tenant %s", req.UserId, req.TenantId),
		audit.NewUser(req.UserId),
		audit.NewTenantID(req.TenantId),
		audit.NewTenantName(req.TenantId), // valid for self-serve
		audit.NewProtoRequest(req),
	)

	if tenantutil.IsEnterpriseTenant(tenant) {
		return &authpb.DeleteAccountResponse{
			DeletionType: authpb.AccountDeletionType_ACCOUNT_DELETION_TYPE_ENTERPRISE,
		}, nil
	}

	// Determine deletion type based on billing info
	var deletionType authpb.AccountDeletionType
	if billingInfo.IsSelfServeTeam {
		if billingInfo.IsAdmin {
			deletionType = authpb.AccountDeletionType_ACCOUNT_DELETION_TYPE_TEAM_ADMIN
		} else {
			deletionType = authpb.AccountDeletionType_ACCOUNT_DELETION_TYPE_TEAM_MEMBER
		}
	} else {
		deletionType = authpb.AccountDeletionType_ACCOUNT_DELETION_TYPE_INDIVIDUAL
	}

	// Handle team-specific pre-deletion checks and operations
	if deletionType == authpb.AccountDeletionType_ACCOUNT_DELETION_TYPE_TEAM_ADMIN {
		if err := s.validateTeamAdminDeletion(ctx, req, tenant); err != nil {
			return nil, err
		}

		// Clean up team pending invitations
		if err := s.deleteAllPendingInvitations(ctx, req.TenantId); err != nil {
			log.Ctx(ctx).Error().Err(err).
				Str("user_id", req.UserId).
				Str("tenant_id", req.TenantId).
				Msg("Failed to delete pending invitations for team")
			return nil, status.Error(codes.Internal, "Failed to clean up pending invitations")
		}
	}

	if deletionType == authpb.AccountDeletionType_ACCOUNT_DELETION_TYPE_TEAM_ADMIN ||
		deletionType == authpb.AccountDeletionType_ACCOUNT_DELETION_TYPE_INDIVIDUAL {
		// Cancel the Orb subscription
		cancelSubReq := &authpb.CancelSubscriptionRequest{
			UserId:            req.UserId,
			TenantId:          req.TenantId,
			CancelImmediately: true,
		}
		_, err := s.CancelSubscription(ctx, cancelSubReq)

		// Remove payment methods from Stripe
		err = s.removePaymentMethodsForUser(ctx, req.UserId, req.TenantId, billingInfo)
		if err != nil {
			log.Ctx(ctx).Error().Err(err).
				Str("user_id", req.UserId).
				Str("tenant_id", req.TenantId).
				Str("deletion_type", deletionType.String()).
				Msg("Failed to remove payment methods")
			return nil, err
		}

	}

	err = RemoveUserFromTenantInternal(
		ctx,
		req.UserId,
		req.TenantId,
		nil,
		s.daoFactory,
		s.tenantMap,
		s.auditLogger,
		s.requestInsightPublisher,
	)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).
			Str("user_id", req.UserId).
			Str("tenant_id", req.TenantId).
			Msg("Failed to remove user from tenant")
		return nil, err
	}

	log.Ctx(ctx).Info().
		Str("user_id", req.UserId).
		Str("tenant_id", req.TenantId).
		Str("deletion_type", deletionType.String()).
		Msg("Successfully deleted account")

	return &authpb.DeleteAccountResponse{
		DeletionType: deletionType,
	}, nil
}

// validateTeamAdminDeletion validates that a team admin can be deleted
// This includes checking that they are the sole member of the team
func (s *TeamManagementServer) validateTeamAdminDeletion(
	ctx context.Context,
	req *authpb.DeleteAccountRequest,
	tenant *tw_pb.Tenant,
) error {
	// Check how many users are in the tenant and verify it's only the requesting user
	var userCount int
	var isRequestingUserInTenant bool
	userTenantMappingDAO := s.daoFactory.GetUserTenantMappingDAO(tenant.Name)
	err := userTenantMappingDAO.FindAll(ctx, func(mapping *auth_entities.UserTenantMapping) bool {
		userCount++
		if mapping.UserId == req.UserId {
			isRequestingUserInTenant = true
		}
		return true
	})
	if err != nil {
		log.Ctx(ctx).Error().Err(err).
			Str("tenant_id", req.TenantId).
			Msg("Failed to count tenant users")
		return status.Error(codes.Internal, "Failed to check team size")
	}

	if userCount > 1 {
		log.Ctx(ctx).Error().
			Str("user_id", req.UserId).
			Str("tenant_id", req.TenantId).
			Int("user_count", userCount).
			Msg("Cannot delete account - team admin with active team members")
		return status.Error(codes.FailedPrecondition, "Cannot delete account while team has other members. Please remove all team members first.")
	}

	if userCount == 1 && !isRequestingUserInTenant {
		log.Ctx(ctx).Error().
			Str("user_id", req.UserId).
			Str("tenant_id", req.TenantId).
			Msg("Cannot delete account - requesting user is not the sole member of the tenant")
		return status.Error(codes.FailedPrecondition, "Cannot delete account - user mismatch")
	}

	return nil
}

// removePaymentMethodsForUser removes all payment methods from Stripe for a user
func (s *TeamManagementServer) removePaymentMethodsForUser(ctx context.Context, userID, tenantID string, billingInfo *UserBillingInfo) error {
	// If there's no Stripe customer ID, nothing to remove
	if billingInfo.StripeCustomerID == "" {
		log.Ctx(ctx).Info().
			Str("user_id", userID).
			Str("tenant_id", tenantID).
			Msg("No Stripe customer ID found, skipping payment method removal")
		return nil
	}

	// Remove payment methods from Stripe
	err := s.stripeClient.RemovePaymentMethodsFromCustomer(billingInfo.StripeCustomerID)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).
			Str("user_id", userID).
			Str("tenant_id", tenantID).
			Str("stripe_customer_id", billingInfo.StripeCustomerID).
			Msg("Failed to remove payment methods from Stripe")
		return err
	}

	log.Ctx(ctx).Info().
		Str("user_id", userID).
		Str("tenant_id", tenantID).
		Str("stripe_customer_id", billingInfo.StripeCustomerID).
		Msg("Successfully removed payment methods from Stripe")

	authClaims, _ := auth.GetAugmentClaims(ctx)
	requestContext, _ := requestcontext.FromGrpcContext(ctx)
	s.auditLogger.WriteAuditLog(
		authClaims,
		fmt.Sprintf("Removed payment methods from Stripe customer %s", billingInfo.StripeCustomerID),
		requestContext,
		audit.NewUser(userID),
	)

	return nil
}

// deleteAllPendingInvitations deletes all pending invitations for a given tenant
func (s *TeamManagementServer) deleteAllPendingInvitations(ctx context.Context, tenantId string) error {
	invitationDAO := s.daoFactory.GetTenantInvitationDAO(tenantId)

	var invitationIds []string
	err := invitationDAO.FindAll(ctx, func(invitation *auth_entities.TenantInvitation) bool {
		if invitation.Status == auth_entities.TenantInvitation_PENDING {
			invitationIds = append(invitationIds, invitation.Id)
		}
		return true
	})
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("tenant_id", tenantId).Msg("Failed to list pending invitations")
		return err
	}

	deletedCount := 0
	for _, invitationId := range invitationIds {
		deleteReq := &authpb.DeleteInvitationRequest{
			InvitationId: invitationId,
			TenantId:     tenantId,
		}

		_, err := s.DeleteInvitation(ctx, deleteReq)
		if err != nil {
			log.Ctx(ctx).Error().Err(err).
				Str("tenant_id", tenantId).
				Str("invitation_id", invitationId).
				Msg("Failed to delete invitation during account deletion")
			// Continue trying to delete other invitations rather than failing completely
		} else {
			deletedCount++
		}
	}

	if len(invitationIds) > 0 {
		s.WriteTeamsAuditLog(ctx, fmt.Sprintf("deleted %d pending invitations for tenant %s during account deletion", deletedCount, tenantId))
	}

	return nil
}

// Checks inputs and gets the user's billing info
func (s *TeamManagementServer) validateUserAndGetBillingInfo(
	ctx context.Context, userID string, scope tokenscopesproto.Scope, adminNecessary bool, apiName string,
) (*UserBillingInfo, error) {
	// Ensure Orb is enabled
	if s.orbClient == nil || !s.orbConfig.Enabled {
		log.Ctx(ctx).Error().Str("augment_user_id", userID).Msg("Orb is not enabled")
		return nil, status.Error(codes.Internal, "Orb is not enabled")
	}

	// Check the requests inputs
	if userID == "" {
		log.Ctx(ctx).Error().Msg("Invalid (empty) user ID")
		return nil, status.Error(codes.InvalidArgument, "Invalid (empty) user ID")
	}

	// Get the tenant ID from the user DAO
	userDAO := s.daoFactory.GetUserDAO()
	user, err := userDAO.Get(ctx, userID)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("augment_user_id", userID).Msg("Failed to get user")
		return nil, status.Error(codes.Internal, "Failed to get user")
	}
	if user == nil {
		log.Ctx(ctx).Error().Str("augment_user_id", userID).Msg("User not found")
		return nil, status.Error(codes.NotFound, "User not found")
	}
	userTenants := user.Tenants
	if len(userTenants) > 1 {
		log.Ctx(ctx).Error().Str("augment_user_id", userID).Msg("User is in multiple tenants")
		return nil, status.Error(codes.Internal, "User is in multiple tenants")
	}
	if len(userTenants) == 0 {
		log.Ctx(ctx).Error().Str("augment_user_id", userID).Msg("User is in 0 tenants")
		return nil, status.Error(codes.Internal, "User is in 0 tenants")
	}

	// Get Orb Customer ID from User
	billingInfo, err := GetUserBillingInfo(ctx, userID, userTenants[0], s.daoFactory, s.tenantMap)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("augment_user_id", userID).Msg("Failed to get user billing info")
		return nil, status.Error(codes.Internal, "Failed to get user billing info")
	}

	// Auth check
	// If the action must be performed by an admin, and the user is on a team, then they must be an admin of that team
	err = s.teamManagementAuthCheck(ctx, nil, &userID, scope, (adminNecessary && billingInfo.IsSelfServeTeam), apiName)
	if err != nil {
		return nil, err
	}
	return billingInfo, nil
}

func (s *TeamManagementServer) GetUserOrbPlanInfo(
	ctx context.Context, req *authpb.GetUserOrbPlanInfoRequest,
) (*authpb.GetUserOrbPlanInfoResponse, error) {
	billingInfo, err := s.validateUserAndGetBillingInfo(ctx, req.UserId, tokenscopesproto.Scope_AUTH_R, false, "GetUserOrbPlanInfo")
	if err != nil {
		return nil, err
	}

	// If the user has a pending subscription creation, they don't have a plan to get information on
	// Return nil for plan info and that they are in a pending subscription status
	if billingInfo.SubscriptionCreationInfo != nil && billingInfo.SubscriptionCreationInfo.Status == auth_entities.User_SubscriptionCreationInfo_PENDING {
		log.Info().Ctx(ctx).Msgf("User %s has a pending subscription creation, returning no plan info", req.UserId)
		return &authpb.GetUserOrbPlanInfoResponse{
			PlanInfo: &authpb.GetUserOrbPlanInfoResponse_PendingPlan{
				PendingPlan: &authpb.PendingOrbPlan{},
			},
		}, nil
	}

	// Get Orb Info
	if s.orbClient == nil {
		log.Ctx(ctx).Error().Str("augment_user_id", req.UserId).Str("orb_customer_id", billingInfo.OrbCustomerID).Str("orb_subscription_id", billingInfo.OrbSubscriptionID).Msg("Orb client is nil, cannot get plan information")
		return nil, status.Error(codes.Internal, "Orb client is nil, cannot get plan information")
	}
	orbPlanInfo, err := s.orbClient.GetPlanInformation(ctx, orb.ItemIds{IncludedMessagesID: s.orbConfig.IncludedMessagesItemID, SeatsID: s.orbConfig.SeatsItemID}, &billingInfo.OrbSubscriptionID, nil)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("augment_user_id", req.UserId).Str("orb_customer_id", billingInfo.OrbCustomerID).Str("orb_subscription_id", billingInfo.OrbSubscriptionID).Msg("Failed to get Orb plan info")
		return nil, status.Error(codes.Internal, "Failed to get Orb plan info")
	}

	resp, err := FormatPlan(orbPlanInfo, s.orbConfig)
	if err != nil {
		return nil, err
	}

	return &authpb.GetUserOrbPlanInfoResponse{
		PlanInfo: &authpb.GetUserOrbPlanInfoResponse_OrbPlanInfo{
			OrbPlanInfo: resp,
		},
	}, nil
}

// Format into auth format
func FormatPlan(orbPlanInfo *orb.OrbPlanInfo, orbConfig *orb_config.OrbConfig) (*authpb.OrbPlanInfo, error) {
	planFeatures := orbConfig.GetPlan(orbPlanInfo.ExternalPlanID)
	if planFeatures == nil {
		log.Error().Str("plan_id", orbPlanInfo.ExternalPlanID).Msg("Unknown plan ID")
		return nil, fmt.Errorf("unknown plan ID: %s", orbPlanInfo.ExternalPlanID)
	}

	// Convert plan type enum to protobuf enum
	var planTypeEnum authpb.OrbPlanInfo_PlanType
	switch planFeatures.Features.PlanType {
	case orb_config.PlanTypeCommunity:
		planTypeEnum = authpb.OrbPlanInfo_PLAN_TYPE_COMMUNITY
	case orb_config.PlanTypePaidTrial:
		planTypeEnum = authpb.OrbPlanInfo_PLAN_TYPE_TRIAL
	case orb_config.PlanTypePaid:
		planTypeEnum = authpb.OrbPlanInfo_PLAN_TYPE_PAID
	default:
		planTypeEnum = authpb.OrbPlanInfo_PLAN_TYPE_UNKNOWN
	}

	planInfo := &authpb.OrbPlanInfo{
		ExternalPlanId:          orbPlanInfo.ExternalPlanID,
		FormattedPlanName:       orbPlanInfo.Name,
		AdditionalUsageUnitCost: fmt.Sprintf("%.2f", orbConfig.CostPerMessage),
		AddUsageAvailable:       planFeatures.Features.AddCreditsAvailable,
		UsageUnitsPerSeat:       float32(orbPlanInfo.MessagesPerSeat),
		TrainingAllowed:         planFeatures.Features.TrainingAllowed,
		TeamsAllowed:            planFeatures.Features.TeamsAllowed,
		MaxNumSeats:             int32(planFeatures.Features.MaxSeats),
		UsageUnitName:           planFeatures.DisplayInfo.UsageUnitDisplayName, // Deprecated: kept for now for backward compatibility
		PricePerSeat:            orbPlanInfo.PricePerSeat,
		PlanType:                planTypeEnum,
		// UI-specific display information
		DisplayInfo: &authpb.OrbPlanDisplayInfo{
			Color:                planFeatures.DisplayInfo.Color,
			Icon:                 planFeatures.DisplayInfo.Icon,
			SortOrder:            int32(planFeatures.DisplayInfo.SortOrder),
			UsageUnitDisplayName: planFeatures.DisplayInfo.UsageUnitDisplayName,
			PlanFacts:            planFeatures.DisplayInfo.PlanFacts,
		},
	}
	return planInfo, nil
}

func (s *TeamManagementServer) GetAllOrbPlans(
	ctx context.Context, req *authpb.GetAllOrbPlansRequest,
) (*authpb.GetAllOrbPlansResponse, error) {
	// Check the requests inputs
	authClaims, ok := auth.GetAugmentClaims(ctx)
	if !ok {
		return nil, status.Error(codes.PermissionDenied, "Invalid context")
	}
	if !authClaims.HasScope(tokenscopesproto.Scope_AUTH_R) {
		log.Ctx(ctx).Error().Msgf("Auth claims do not give have scope %s", tokenscopesproto.Scope_AUTH_R)
		return nil, status.Error(codes.PermissionDenied, "Access denied")
	}

	// Get all the plans we need to get generic info for
	allResp := &authpb.GetAllOrbPlansResponse{}
	var filteredPlans []string
	for _, plan := range s.orbConfig.Plans {
		// Only include plans that are in "active" state
		if plan.State == orb_config.PlanStateActive {
			// plan.ID is the external_plan_id in Orb
			filteredPlans = append(filteredPlans, plan.ID)
		}
	}

	for _, planID := range filteredPlans {
		orbPlanInfo, err := s.orbClient.GetPlanInformation(ctx, orb.ItemIds{IncludedMessagesID: s.orbConfig.IncludedMessagesItemID, SeatsID: s.orbConfig.SeatsItemID}, nil, &planID)
		if err != nil {
			log.Ctx(ctx).Error().Err(err).Str("plan_id", planID).Msg("Failed to get Orb plan info")
			return nil, status.Error(codes.Internal, "Failed to get Orb plan info")
		}

		orbPlan, err := FormatPlan(orbPlanInfo, s.orbConfig)
		if err != nil {
			return nil, err
		}
		allResp.OrbPlans = append(allResp.OrbPlans, orbPlan)
	}

	return allResp, nil
}

func (s *TeamManagementServer) UnschedulePendingSubscriptionCancellation(
	ctx context.Context, req *authpb.UnschedulePendingSubscriptionCancellationRequest,
) (*authpb.UnschedulePendingSubscriptionCancellationResponse, error) {
	billingInfo, err := s.validateUserAndGetBillingInfo(ctx, req.UserId, tokenscopesproto.Scope_AUTH_RW, true, "UnschedulePendingSubscriptionCancellation")
	if err != nil {
		return nil, err
	}
	// We know this will succeed because the auth check passed inside validateUserAndGetBillingInfo.
	authInfo, _ := auth.GetAugmentClaims(ctx)

	// Check for any plan/tier/seat changes in progress before proceeding
	if changeInProgress, changeDescription, err := CheckTierOrPlanChanges(ctx, s.daoFactory, s.tenantMap, s.featureFlagHandle, req.UserId); err != nil {
		log.Ctx(ctx).Error().Str("user_id", req.UserId).Err(err).Msgf("Error checking for plan or tier change in progress for user %s", req.UserId)
		return nil, fmt.Errorf("UnschedulePendingSubscriptionCancellation: failed to check for plan or tier change in progress: %w", err)
	} else if changeInProgress {
		log.Ctx(ctx).Error().Str("user_id", req.UserId).Str("change_description", changeDescription).Msg("Blocking UnschedulePendingSubscriptionCancellation due to plan or tier change in progress")
		return nil, status.Error(codes.FailedPrecondition, "User has a plan or tier change in progress.")
	}
	// Check for seat changes
	if changeInProgress, changeDescription, err := CheckSeatChanges(ctx, s.daoFactory, s.tenantMap, s.featureFlagHandle, req.UserId); err != nil {
		log.Ctx(ctx).Error().Err(err).Msgf("Failed to check seat changes for user %s", req.UserId)
		return nil, err
	} else if changeInProgress {
		log.Ctx(ctx).Error().Str("user_id", req.UserId).Str("change_description", changeDescription).Msg("Blocking UnschedulePendingSubscriptionCancellation due to seat change in progress")
		return nil, status.Error(codes.FailedPrecondition, "User has a seat change in progress.")
	}

	// Get the user's Orb subscription
	orbSubscription, err := s.orbClient.GetUserSubscription(ctx, billingInfo.OrbSubscriptionID, nil)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("augment_user_id", req.UserId).Str("orb_customer_id", billingInfo.OrbCustomerID).Str("orb_subscription_id", billingInfo.OrbSubscriptionID).Msg("Failed to get user subscription")
		return nil, status.Error(codes.Internal, "Failed to get user subscription")
	}

	// Audit log
	s.WriteTeamsAuditLog(ctx,
		fmt.Sprintf("unscheduling pending subscription cancellation for user %s, subscription ID %s", req.UserId, billingInfo.OrbSubscriptionID),
		audit.NewUser(req.UserId),
	)

	plan := s.orbConfig.FindPlan(orbSubscription.ExternalPlanID)
	if plan == nil {
		log.Ctx(ctx).Error().Str("orb_subscription_id", billingInfo.OrbSubscriptionID).Str("plan_id", orbSubscription.ExternalPlanID).Msg("Failed to find plan")
		return nil, status.Error(codes.Internal, "Failed to find plan")
	}

	// Make sure we're not on the trial plan
	// You shouldn't be able to unschedule cancellation for the trial plan (makes no sense), but guard against this as this would give them free forever
	if plan.Features.PlanType == orb_config.PlanTypePaidTrial {
		log.Ctx(ctx).Error().Str("augment_user_id", req.UserId).Str("orb_subscription_id", billingInfo.OrbSubscriptionID).Msg("Cannot unschedule cancellation on a trial plan")
		return nil, status.Error(codes.FailedPrecondition, "Cannot unschedule cancellation on a trial plan")
	}

	// Check to make sure subscription is scheduled to be cancelled
	if orbSubscription.EndDate.IsZero() {
		log.Ctx(ctx).Error().Str("augment_user_id", req.UserId).Str("orb_subscription_id", billingInfo.OrbSubscriptionID).Msg("Subscription is not scheduled to be cancelled")
		return nil, status.Error(codes.FailedPrecondition, "Subscription is not scheduled to be cancelled")
	}

	// Check to make sure subscription is not already cancelled
	if orbSubscription.EndDate.Before(time.Now()) {
		log.Ctx(ctx).Error().Str("augment_user_id", req.UserId).Str("orb_subscription_id", billingInfo.OrbSubscriptionID).Msg("Subscription is already cancelled")
		return nil, status.Error(codes.FailedPrecondition, "Subscription is already cancelled")
	}

	// Unschedule the cancellation
	err = s.orbClient.UnschedulePendingSubscriptionCancellation(ctx, billingInfo.OrbSubscriptionID, nil)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("augment_user_id", req.UserId).Str("orb_subscription_id", billingInfo.OrbSubscriptionID).Msg("Failed to unschedule subscription cancellation")
		return nil, status.Error(codes.Internal, "Failed to unschedule subscription cancellation")
	}

	// Validate the future plan
	err = s.validateFuturePlanQuantities(ctx, billingInfo)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("augment_user_id", req.UserId).Str("orb_subscription_id", billingInfo.OrbSubscriptionID).Msg("Failed to validate future plan")
		return nil, status.Error(codes.Internal, "Failed to validate future plan")
	}

	// Audit log on success
	s.WriteTeamsAuditLog(ctx, fmt.Sprintf("successfully unscheduled pending subscription cancellation for user %s, subscription ID %s", req.UserId, billingInfo.OrbSubscriptionID))

	// Record this unschedule cancellation in request insight.
	requestContext, riErr := requestcontext.FromGrpcContext(ctx)
	if riErr != nil {
		log.Ctx(ctx).Warn().Err(riErr).Msg("Failed to get request context")
	} else {
		event := ripublisher.NewSessionEvent()
		event.Event = &ripb.SessionEvent_UnschedulePendingSubscriptionCancellation{
			UnschedulePendingSubscriptionCancellation: &ripb.UnschedulePendingSubscriptionCancellation{
				OrbSubscriptionId: billingInfo.OrbSubscriptionID,
			},
		}
		riErr := s.requestInsightPublisher.PublishSessionEvent(
			ctx,
			requestContext.RequestSessionId.String(),
			authInfo.GetOpaqueUserID(),
			&ripb.TenantInfo{
				TenantId:   authInfo.TenantID,
				TenantName: authInfo.TenantName,
			}, event)
		if riErr != nil {
			log.Ctx(ctx).Warn().Err(riErr).Msg("Failed to publish UnschedulePendingSubscriptionCancellation event")
		}
	}

	return &authpb.UnschedulePendingSubscriptionCancellationResponse{}, nil
}

func (s *TeamManagementServer) UnschedulePlanChanges(
	ctx context.Context, req *authpb.UnschedulePlanChangesRequest,
) (*authpb.UnschedulePlanChangesResponse, error) {
	billingInfo, err := s.validateUserAndGetBillingInfo(ctx, req.UserId, tokenscopesproto.Scope_AUTH_RW, true, "UnschedulePlanChanges")
	if err != nil {
		return nil, err
	}

	// Get auth info for audit logging
	authInfo, _ := auth.GetAugmentClaims(ctx)

	// Check for any plan/tier changes in progress before proceeding
	if changeInProgress, changeDescription, err := CheckTierOrPlanChanges(ctx, s.daoFactory, s.tenantMap, s.featureFlagHandle, req.UserId); err != nil {
		log.Ctx(ctx).Error().Str("user_id", req.UserId).Err(err).Msgf("Error checking for plan or tier change in progress for user %s", req.UserId)
		return nil, fmt.Errorf("UnschedulePlanChanges: failed to check for plan or tier change in progress: %w", err)
	} else if changeInProgress {
		log.Ctx(ctx).Error().Str("user_id", req.UserId).Str("change_description", changeDescription).Msg("Blocking UnschedulePlanChanges due to plan or tier change in progress")
		return nil, status.Error(codes.FailedPrecondition, "User has a plan or tier change in progress.")
	}
	// Check for seat changes
	if changeInProgress, changeDescription, err := CheckSeatChanges(ctx, s.daoFactory, s.tenantMap, s.featureFlagHandle, req.UserId); err != nil {
		log.Ctx(ctx).Error().Err(err).Msgf("Failed to check seat changes for user %s", req.UserId)
		return nil, err
	} else if changeInProgress {
		log.Ctx(ctx).Error().Str("user_id", req.UserId).Str("change_description", changeDescription).Msg("Blocking UnschedulePlanChanges due to seat change in progress")
		return nil, status.Error(codes.FailedPrecondition, "User has a seat change in progress.")
	}

	// Check if the subscription has scheduled plan changes
	scheduledTargetPlanID, err := s.orbClient.GetScheduledPlanChanges(ctx, billingInfo.OrbSubscriptionID, billingInfo.OrbCustomerID)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("augment_user_id", req.UserId).Str("orb_subscription_id", billingInfo.OrbSubscriptionID).Msg("Failed to get scheduled plan changes")
		return nil, status.Error(codes.Internal, "Failed to get scheduled plan changes")
	}

	if scheduledTargetPlanID == nil {
		log.Ctx(ctx).Warn().Str("augment_user_id", req.UserId).Str("orb_subscription_id", billingInfo.OrbSubscriptionID).Msg("No scheduled plan changes found")
		return nil, status.Error(codes.InvalidArgument, "No scheduled plan changes found")
	}

	// Unschedule the plan changes
	err = s.orbClient.UnschedulePlanChanges(ctx, billingInfo.OrbSubscriptionID, nil)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("augment_user_id", req.UserId).Str("orb_subscription_id", billingInfo.OrbSubscriptionID).Msg("Failed to unschedule pending plan changes")
		return nil, status.Error(codes.Internal, "Failed to unschedule pending plan changes")
	}

	// Validate the future plan
	err = s.validateFuturePlanQuantities(ctx, billingInfo)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("augment_user_id", req.UserId).Str("orb_subscription_id", billingInfo.OrbSubscriptionID).Msg("Failed to validate future plan")
		return nil, status.Error(codes.Internal, "Failed to validate future plan")
	}

	// Audit log on success
	s.WriteTeamsAuditLog(ctx,
		fmt.Sprintf("successfully unscheduled pending plan changes for user %s, subscription ID %s", req.UserId, billingInfo.OrbSubscriptionID),
		audit.NewUser(req.UserId),
	)

	// Record this unschedule plan changes in request insight.
	requestContext, riErr := requestcontext.FromGrpcContext(ctx)
	if riErr != nil {
		log.Ctx(ctx).Warn().Err(riErr).Msg("Failed to get request context")
	} else {
		event := ripublisher.NewSessionEvent()
		event.Event = &ripb.SessionEvent_UnschedulePlanChanges{
			UnschedulePlanChanges: &ripb.UnschedulePlanChanges{
				OrbSubscriptionId: billingInfo.OrbSubscriptionID,
			},
		}
		riErr := s.requestInsightPublisher.PublishSessionEvent(
			ctx,
			requestContext.RequestSessionId.String(),
			authInfo.GetOpaqueUserID(),
			&ripb.TenantInfo{
				TenantId:   authInfo.TenantID,
				TenantName: authInfo.TenantName,
			}, event)
		if riErr != nil {
			log.Ctx(ctx).Warn().Err(riErr).Msg("Failed to publish UnschedulePlanChanges event")
		}
	}

	return &authpb.UnschedulePlanChangesResponse{}, nil
}

// After unscheduling a plan change or cancellation, make sure that next month's plan has the correct number of credits.
// If we had to calculate pro-rated credits and had scheduled a future credit change to the full amount,
// that future change was overwritten if the user scheduling a plan change or cancellation.
// Now that we are unscheduling that, we should make sure next month, the user gets the correct number of credits
func (s *TeamManagementServer) validateFuturePlanQuantities(ctx context.Context, billingInfo *UserBillingInfo) error {
	// Get Orb Subscription Info
	orbSubscription, err := s.orbClient.GetUserSubscription(ctx, billingInfo.OrbSubscriptionID, &orb.ItemIds{
		SeatsID:            s.orbConfig.SeatsItemID,
		IncludedMessagesID: s.orbConfig.IncludedMessagesItemID,
	})
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("augment_user_id", billingInfo.UserID).Str("orb_customer_id", billingInfo.OrbCustomerID).Str("orb_subscription_id", billingInfo.OrbSubscriptionID).Msg("Failed to get user subscription")
		return err
	}
	// Get the plan info about what the plan should have
	orbPlanInfo, err := s.orbClient.GetPlanInformation(ctx, orb.ItemIds{IncludedMessagesID: s.orbConfig.IncludedMessagesItemID, SeatsID: s.orbConfig.SeatsItemID}, &billingInfo.OrbSubscriptionID, nil)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("augment_user_id", billingInfo.UserID).Str("orb_customer_id", billingInfo.OrbCustomerID).Str("orb_subscription_id", billingInfo.OrbSubscriptionID).Msg("Failed to get plan info")
		return err
	}

	if orbSubscription.CurrentFixedQuantities == nil {
		log.Ctx(ctx).Error().Str("augment_user_id", billingInfo.UserID).Str("orb_customer_id", billingInfo.OrbCustomerID).Str("orb_subscription_id", billingInfo.OrbSubscriptionID).Msg("Failed to get current fixed quantities")
		return err
	}

	nextMonthSeats := orbSubscription.CurrentFixedQuantities.Seats
	nextMonthCredits := orbSubscription.CurrentFixedQuantities.IncludedMessages
	if orbSubscription.FutureFixedQuantities != nil {
		if orbSubscription.FutureFixedQuantities.Seats > 0 {
			nextMonthSeats = orbSubscription.FutureFixedQuantities.Seats
		}
		if orbSubscription.FutureFixedQuantities.IncludedMessages > 0 {
			nextMonthCredits = orbSubscription.FutureFixedQuantities.IncludedMessages
		}
	}

	if nextMonthSeats*int(orbPlanInfo.MessagesPerSeat) != nextMonthCredits {
		log.Ctx(ctx).Info().Msgf("Setting future number of credits to be correct")
		err = s.orbClient.UpdateFixedQuantity(ctx, orb.OrbQuantityUpdate{
			OrbSubscriptionID: orbSubscription.OrbSubscriptionID,
			PriceOverride: orb.OrbPriceOverrides{
				PriceID:  orbPlanInfo.IncludedMessagesPriceID,
				Quantity: float64(nextMonthSeats) * orbPlanInfo.MessagesPerSeat,
			},
			UpdateTimeType: orb.PlanChangeEndOfTerm,
		}, nil)
		if err != nil {
			log.Ctx(ctx).Error().Err(err).Str("augment_user_id", billingInfo.UserID).Str("orb_customer_id", billingInfo.OrbCustomerID).Str("orb_subscription_id", billingInfo.OrbSubscriptionID).Msg("Failed to update future number of credits")
			return err
		}
	}

	return nil
}

func (s *TeamManagementServer) GetUserOrbCreditsInfo(
	ctx context.Context, req *authpb.GetUserOrbCreditsInfoRequest,
) (*authpb.GetUserOrbCreditsInfoResponse, error) {
	billingInfo, err := s.validateUserAndGetBillingInfo(ctx, req.UserId, tokenscopesproto.Scope_AUTH_R, false, "GetUserOrbCreditsInfo")
	if err != nil {
		return nil, err
	}

	// Get Orb Subscription Info. We need the current billing period start date in order to get how many credits have been used this billing period
	orbSubscription, err := s.orbClient.GetUserSubscription(ctx, billingInfo.OrbSubscriptionID, nil)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("augment_user_id", req.UserId).Str("orb_customer_id", billingInfo.OrbCustomerID).Str("orb_subscription_id", billingInfo.OrbSubscriptionID).Msg("Failed to get user subscription")
		return nil, status.Error(codes.Internal, "Failed to get user subscription")
	}

	// Get Orb Credit Info
	creditInfo, err := s.orbClient.GetCustomerCreditInfo(ctx, billingInfo.OrbCustomerID, orbSubscription.CurrentBillingPeriodStartDate, s.orbConfig.PricingUnit)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("augment_user_id", req.UserId).Str("orb_customer_id", billingInfo.OrbCustomerID).Str("orb_subscription_id", billingInfo.OrbSubscriptionID).Msg("Failed to get Orb credit info")
		return nil, status.Error(codes.Internal, "Failed to get Orb credit info")
	}

	return &authpb.GetUserOrbCreditsInfoResponse{
		UsageUnitsAvailable:            int32(creditInfo.ActiveCredits),
		UsageUnitsUsedThisBillingCycle: int32(creditInfo.CreditsUsedThisBillingCycle),
		UsageUnitsPending:              int32(creditInfo.PendingCredits),
	}, nil
}

func (s *TeamManagementServer) GetUserOrbPaymentInfo(
	ctx context.Context, req *authpb.GetUserOrbPaymentInfoRequest,
) (*authpb.GetUserOrbPaymentInfoResponse, error) {
	billingInfo, err := s.validateUserAndGetBillingInfo(ctx, req.UserId, tokenscopesproto.Scope_AUTH_R, false, "GetUserOrbPaymentInfo")
	if err != nil {
		return nil, err
	}

	var (
		hasPaymentMethod  bool
		failedPaymentInfo *orb.FailedPaymentInfo
		stripeErr         error
	)

	// Create an error group with the request context
	g, gctx := errgroup.WithContext(ctx)

	// Run Stripe API call to determine if we have a payment method
	g.Go(func() error {
		var err error
		hasPaymentMethod, err = s.stripeClient.HasPaymentMethod(billingInfo.StripeCustomerID)
		if err != nil {
			stripeErr = err
			log.Ctx(ctx).Error().Err(err).Str("augment_user_id", req.UserId).Str("stripe_customer_id", billingInfo.StripeCustomerID).Msg("Failed to get payment method status from Stripe")
			return err
		}
		return nil
	})

	// Run Orb API call to determine if we have any failed payments
	g.Go(func() error {
		var err error
		failedPaymentInfo, err = s.orbClient.GetFailedPaymentInfo(gctx, billingInfo.OrbCustomerID)
		if err != nil {
			log.Ctx(ctx).Warn().Err(err).Str("augment_user_id", req.UserId).Str("orb_customer_id", billingInfo.OrbCustomerID).Msg("Failed to get failed payment info")
			// Don't propagate this error since we don't want it to fail the entire request
			return nil
		}
		return nil
	})

	// Wait for all goroutines to complete
	_ = g.Wait()

	// Handle Stripe error
	if stripeErr != nil {
		return nil, status.Error(codes.Internal, "Failed to get payment method status from Stripe")
	}

	resp := &authpb.GetUserOrbPaymentInfoResponse{
		HasPaymentMethod: hasPaymentMethod,
	}
	if failedPaymentInfo != nil {
		resp.FailedPayment = &authpb.FailedPayment{
			Amount:     failedPaymentInfo.Amount,
			Date:       failedPaymentInfo.Date.Format(time.RFC3339),
			InvoiceUrl: failedPaymentInfo.InvoiceUrl,
		}
	}
	return resp, nil
}

func (s *TeamManagementServer) GetUserOrbSubscriptionInfo(
	ctx context.Context, req *authpb.GetUserOrbSubscriptionInfoRequest,
) (*authpb.GetUserOrbSubscriptionInfoResponse, error) {
	billingInfo, err := s.validateUserAndGetBillingInfo(ctx, req.UserId, tokenscopesproto.Scope_AUTH_R, false, "GetUserOrbSubscriptionInfo")
	if err != nil {
		return nil, err
	}

	// If the user has a pending subscription, return pending
	if billingInfo.SubscriptionCreationInfo != nil && billingInfo.SubscriptionCreationInfo.Status == auth_entities.User_SubscriptionCreationInfo_PENDING {
		return &authpb.GetUserOrbSubscriptionInfoResponse{
			OrbSubscriptionInfo: &authpb.GetUserOrbSubscriptionInfoResponse_PendingSubscription{
				PendingSubscription: &authpb.PendingOrbSubscription{},
			},
		}, nil
	}

	// No subscription ID, but subscription is not pending. Return nonexistent subscription.
	if billingInfo.OrbSubscriptionID == "" {
		return &authpb.GetUserOrbSubscriptionInfoResponse{
			OrbSubscriptionInfo: &authpb.GetUserOrbSubscriptionInfoResponse_NonexistentSubscription{
				NonexistentSubscription: &authpb.NoSubscription{},
			},
		}, nil
	}

	var (
		orbSubscription       *orb.OrbSubscriptionInfo
		orbPlanInfo           *orb.OrbPlanInfo
		scheduledTargetPlanID *string
		subscriptionErr       error
		planInfoErr           error
		scheduledPlanErr      error
	)
	g, gctx := errgroup.WithContext(ctx)

	// Get Orb Subscription Info
	g.Go(func() error {
		var err error
		orbSubscription, err = s.orbClient.GetUserSubscription(gctx, billingInfo.OrbSubscriptionID, &orb.ItemIds{
			SeatsID:            s.orbConfig.SeatsItemID,
			IncludedMessagesID: s.orbConfig.IncludedMessagesItemID,
		})
		if err != nil {
			subscriptionErr = err
			return err
		}
		return nil
	})

	// Get Orb Plan Info
	g.Go(func() error {
		var err error
		orbPlanInfo, err = s.orbClient.GetPlanInformation(gctx, orb.ItemIds{
			IncludedMessagesID: s.orbConfig.IncludedMessagesItemID,
			SeatsID:            s.orbConfig.SeatsItemID,
		}, &billingInfo.OrbSubscriptionID, nil)
		if err != nil {
			planInfoErr = err
			return err
		}
		return nil
	})

	// Get Scheduled Plan Changes
	g.Go(func() error {
		var err error
		scheduledTargetPlanID, err = s.orbClient.GetScheduledPlanChanges(gctx, billingInfo.OrbSubscriptionID, billingInfo.OrbCustomerID)
		if err != nil {
			scheduledPlanErr = err
			return err
		}
		return nil
	})

	// Wait for all goroutines to complete
	if err := g.Wait(); err != nil {
		// Return the specific error message based on which call failed
		if subscriptionErr != nil {
			log.Ctx(ctx).Error().Err(err).Str("augment_user_id", req.UserId).Str("orb_customer_id", billingInfo.OrbCustomerID).Str("orb_subscription_id", billingInfo.OrbSubscriptionID).Msg("Failed to get user subscription")
			return nil, status.Error(codes.Unknown, "Failed to get user subscription")
		}
		if planInfoErr != nil {
			log.Ctx(ctx).Error().Err(err).Str("augment_user_id", req.UserId).Str("orb_customer_id", billingInfo.OrbCustomerID).Str("orb_subscription_id", billingInfo.OrbSubscriptionID).Msg("Failed to get Orb plan info")
			return nil, status.Error(codes.Unknown, "Failed to get plan info")
		}
		if scheduledPlanErr != nil {
			log.Ctx(ctx).Error().Err(err).Str("augment_user_id", req.UserId).Str("orb_customer_id", billingInfo.OrbCustomerID).Str("orb_subscription_id", billingInfo.OrbSubscriptionID).Msg("Failed to get scheduled plan changes")
			return nil, status.Error(codes.Unknown, "Failed to get scheduled plan changes")
		}
		return nil, status.Error(codes.Unknown, "Failed to get information")
	}

	// If no Orb subscription, return null
	if orbSubscription == nil {
		return &authpb.GetUserOrbSubscriptionInfoResponse{
			OrbSubscriptionInfo: &authpb.GetUserOrbSubscriptionInfoResponse_NonexistentSubscription{
				NonexistentSubscription: &authpb.NoSubscription{},
			},
		}, nil
	}

	// Exclude billing portal url from response for non-admin users on a self-serve team
	portalUrl := orbSubscription.PortalUrl
	if billingInfo.IsSelfServeTeam && !billingInfo.IsAdmin {
		portalUrl = ""
	}

	// Format the response
	subscription := &authpb.OrbSubscriptionInfo{
		OrbCustomerId:           billingInfo.OrbCustomerID,
		OrbSubscriptionId:       billingInfo.OrbSubscriptionID,
		PortalUrl:               portalUrl,
		ExternalPlanId:          orbSubscription.ExternalPlanID,
		NextBillingCycleAmount:  orbSubscription.NextBillingCycleAmount,
		BillingPeriodEndDateIso: orbSubscription.CurrentBillingPeriodEndDate.Format(time.RFC3339),
		SubscriptionStatus:      authpb.OrbSubscriptionInfo_SubscriptionStatus(authpb.OrbSubscriptionInfo_SubscriptionStatus_value[strings.ToUpper(orbSubscription.OrbStatus)]),
	}
	endDate := orbSubscription.EndDate.Format(time.RFC3339)
	if orbSubscription.ExternalPlanID == s.orbConfig.GetTrialPlan().ID {
		subscription.TrialPeriodEndDateIso = &endDate
	}
	if !orbSubscription.EndDate.IsZero() {
		subscription.SubscriptionEndDateIso = &endDate
		subscription.SubscriptionEndAtBillingCycleEnd = orbSubscription.EndDate.Equal(orbSubscription.CurrentBillingPeriodEndDate)
	}

	// Set the number of seats and monthly cost
	pricePerSeat, err := strconv.ParseFloat(orbPlanInfo.PricePerSeat, 64)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("augment_user_id", req.UserId).Str("orb_customer_id", billingInfo.OrbCustomerID).Str("orb_subscription_id", billingInfo.OrbSubscriptionID).Msg("Failed to parse price per seat")
		return nil, status.Error(codes.Internal, "Failed to parse price per seat")
	}
	if orbSubscription.FutureFixedQuantities != nil && orbSubscription.FutureFixedQuantities.Seats > 0 {
		// If we have a future number of seats set that is nonzero and different from current seats, use it
		subscription.Seats = int32(orbSubscription.FutureFixedQuantities.Seats)
		subscription.MonthlyTotalCost = fmt.Sprintf("%.2f", float64(orbSubscription.FutureFixedQuantities.Seats)*pricePerSeat)
	} else {
		// Otherwise, the current number of seats will continue
		subscription.Seats = int32(orbSubscription.CurrentFixedQuantities.Seats)
		subscription.MonthlyTotalCost = fmt.Sprintf("%.2f", float64(orbSubscription.CurrentFixedQuantities.Seats)*pricePerSeat)
	}

	// Set the renewing number of usage units
	if orbSubscription.FutureFixedQuantities != nil && orbSubscription.FutureFixedQuantities.IncludedMessages > 0 {
		// If we have a future number of included messages set that is nonzero, set it
		subscription.UsageUnitsRenewingEachBillingCycle = int32(orbSubscription.FutureFixedQuantities.IncludedMessages)
	} else if !subscription.SubscriptionEndAtBillingCycleEnd {
		// If the subscription is not ending at billing cycle end, then the current number of included messages will continue
		subscription.UsageUnitsRenewingEachBillingCycle = int32(orbSubscription.CurrentFixedQuantities.IncludedMessages)
	} else {
		subscription.UsageUnitsRenewingEachBillingCycle = 0
	}

	// Set scheduled plan change information.
	// Right now we only schedule plan changes either immediately or at the end of the billing period
	// So we are not returning effective dates for now
	if scheduledTargetPlanID != nil {
		subscription.ScheduledTargetPlanId = scheduledTargetPlanID
		// We need to set the future invoice amount manually, as there is no future invoice when you are scheduled to go down in plans
		targetPlan := s.orbConfig.FindPlan(*scheduledTargetPlanID)
		if targetPlan != nil {
			planInfo, err := s.orbClient.GetPlanInformation(ctx, orb.ItemIds{IncludedMessagesID: s.orbConfig.IncludedMessagesItemID, SeatsID: s.orbConfig.SeatsItemID}, nil, scheduledTargetPlanID)
			if err != nil {
				log.Ctx(ctx).Error().Err(err).Str("augment_user_id", req.UserId).Str("orb_customer_id", billingInfo.OrbCustomerID).Str("orb_subscription_id", billingInfo.OrbSubscriptionID).Msg("Failed to get target plan info")
			} else {
				targetPricePerSeat, err := strconv.ParseFloat(planInfo.PricePerSeat, 64)
				if err != nil {
					log.Ctx(ctx).Error().Err(err).Str("augment_user_id", req.UserId).Str("orb_customer_id", billingInfo.OrbCustomerID).Str("orb_subscription_id", billingInfo.OrbSubscriptionID).Msg("Failed to parse target plan price per seat")
				} else {
					subscription.NextBillingCycleAmount = fmt.Sprintf("%.2f", targetPricePerSeat*float64(subscription.Seats))
				}
			}
		}
	}

	// Set the current number of usage units -- the number of seats * the number of messages per seat
	subscription.UsageUnitsIncludedThisBillingCycle = subscription.Seats * int32(orbPlanInfo.MessagesPerSeat)

	// Fetch internal subscription data to get payment failure information
	subscriptionDAO := s.daoFactory.GetSubscriptionDAO()
	internalSubscription, err := subscriptionDAO.Get(ctx, billingInfo.OrbSubscriptionID)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("subscription_id", billingInfo.OrbSubscriptionID).Msg("Failed to get internal subscription data")
		subscription.CancelledDueToPaymentFailure = false
	} else if internalSubscription != nil {
		subscription.CancelledDueToPaymentFailure = internalSubscription.CancelledDueToPaymentFailure
	}

	resp := &authpb.GetUserOrbSubscriptionInfoResponse{
		OrbSubscriptionInfo: &authpb.GetUserOrbSubscriptionInfoResponse_Subscription{
			Subscription: subscription,
		},
	}

	return resp, nil
}

func (s *TeamManagementServer) GetTenantPlanStatus(
	ctx context.Context, req *authpb.GetTenantPlanStatusRequest,
) (*authpb.GetTenantPlanStatusResponse, error) {
	if !s.teamManagementEnabled() {
		log.Ctx(ctx).Warn().Msgf("Team management is disabled for GetTenantPlanStatus")
		return nil, status.Error(codes.Internal, "Disabled")
	}

	// Validate request
	if req.TenantId == "" {
		log.Ctx(ctx).Error().Msg("TenantId is required for GetTenantPlanStatus")
		return nil, status.Error(codes.InvalidArgument, "TenantId is required")
	}

	// Auth check: Ensure the caller has read access to the specified tenant.
	err := s.teamManagementAuthCheck(ctx, &req.TenantId, nil, tokenscopesproto.Scope_AUTH_R, false, "GetTenantPlanStatus")
	if err != nil {
		return nil, err
	}

	log.Ctx(ctx).Info().Str("tenant_id", req.TenantId).Msg("Fetching tenant plan status")

	tenantSubscriptionMappingDAO := s.daoFactory.GetTenantSubscriptionMappingDAO()
	mapping, err := tenantSubscriptionMappingDAO.Get(ctx, req.TenantId)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("tenant_id", req.TenantId).Msg("Failed to fetch tenant subscription mapping")
		return nil, status.Error(codes.Internal, "Failed to fetch tenant subscription mapping")
	}

	if mapping == nil {
		log.Ctx(ctx).Warn().Str("tenant_id", req.TenantId).Msg("Tenant subscription mapping not found")
		return &authpb.GetTenantPlanStatusResponse{
			IsPending: false,
		}, nil
	}

	resp := &authpb.GetTenantPlanStatusResponse{
		IsPending: false, // Default to not pending
	}

	if mapping.PlanChange != nil {
		resp.IsPending = true
		if mapping.PlanChange.TargetOrbPlanId != "" {
			resp.PendingTargetPlanId = &mapping.PlanChange.TargetOrbPlanId
		}
		if mapping.PlanChange.CreatedAt != nil {
			resp.PlanChangeCreatedAt = mapping.PlanChange.CreatedAt
		}
	}

	return resp, nil
}

// EvaluatePromotionEligibility evaluates if a user is eligible for a specific promotion
func (s *TeamManagementServer) EvaluatePromotionEligibility(
	ctx context.Context, req *authpb.EvaluatePromotionEligibilityRequest,
) (*authpb.EvaluatePromotionEligibilityResponse, error) {
	if req.UserId == "" {
		return nil, status.Error(codes.InvalidArgument, "User ID is required")
	}

	err := s.teamManagementAuthCheck(ctx, nil, &req.UserId, tokenscopesproto.Scope_AUTH_RW, false, "EvaluatePromotionEligibility")
	if err != nil {
		return nil, err
	}

	// For now, supports only windsurf or cursor promotions
	if req.PromotionName != windsurfPromotionName && req.PromotionName != cursorPromotionName {
		log.Ctx(ctx).Info().
			Str("user_id", req.UserId).
			Str("promotion_name", req.PromotionName).
			Msg("Unsupported promotion type")
		return nil, status.Error(codes.InvalidArgument, "Unsupported promotion type")
	}

	userDAO := s.daoFactory.GetUserDAO()
	user, err := userDAO.Get(ctx, req.UserId)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("user_id", req.UserId).Msg("Failed to get user")
		return nil, status.Error(codes.Internal, "Failed to get user")
	}
	if user == nil {
		log.Ctx(ctx).Info().Str("user_id", req.UserId).Msg("User not found")
		return nil, status.Error(codes.NotFound, "User not found")
	}

	eligibilityResult, err := s.isEligibleForSignupPromotion(ctx, user, req.PromotionName)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("user_id", req.UserId).Str("promotion_name", req.PromotionName).Msg("Failed to check signup promotion eligibility")
		return nil, status.Error(codes.Internal, "Failed to check promotion eligibility")
	}

	s.WriteTeamsAuditLog(ctx,
		fmt.Sprintf("Evaluated signup promotion eligibility for user %s. Promotion status: %s, reason: %s", req.UserId, eligibilityResult.Status.String(), eligibilityResult.Reason),
		audit.NewUser(req.UserId),
	)

	log.Ctx(ctx).Info().
		Str("user_id", req.UserId).
		Str("promotion_name", req.PromotionName).
		Str("promotion_status", eligibilityResult.Status.String()).
		Str("reason", eligibilityResult.Reason).
		Msg("Evaluated signup promotion eligibility")

	if eligibilityResult.Status == auth_entities.PromotionStatus_PROMOTION_STATUS_ELIGIBLE {
		// Update user with promotion status
		_, err = userDAO.TryUpdate(ctx, req.UserId, func(u *auth_entities.User) bool {
			for _, promotion := range u.Promotions {
				if promotion.PromotionName == req.PromotionName {
					promotion.Status = eligibilityResult.Status
					return true
				}
			}

			// Add new promotion if not found
			newPromotion := &auth_entities.UserPromotion{
				PromotionName: req.PromotionName,
				Status:        eligibilityResult.Status,
			}

			u.Promotions = append(u.Promotions, newPromotion)
			return true
		}, DefaultRetry)
		if err != nil {
			log.Ctx(ctx).Error().Err(err).Str("user_id", req.UserId).Msg("Failed to update user with promotion status")
			return nil, status.Error(codes.Internal, "Failed to update user")
		}
	}
	return &authpb.EvaluatePromotionEligibilityResponse{
		PromotionStatus: eligibilityResult.Status,
	}, nil
}

func getPromotionConfig(promotionName string) (PromotionConfig, bool) {
	config, exists := promotionMap[promotionName]
	return config, exists
}

func getPromotionsOfType(promotionType PromotionType) []string {
	var promotions []string
	for name, config := range promotionMap {
		if config.Type == promotionType {
			promotions = append(promotions, name)
		}
	}
	return promotions
}

func hasPromotionsOfType(user *auth_entities.User, promotionType PromotionType) []*auth_entities.UserPromotion {
	promotions := []*auth_entities.UserPromotion{}
	promotionsOfType := getPromotionsOfType(promotionType)
	for _, userPromotion := range user.Promotions {
		for _, promotionName := range promotionsOfType {
			if userPromotion.PromotionName == promotionName {
				promotions = append(promotions, userPromotion)
			}
		}
	}
	return promotions
}

type PromotionEligibilityResult struct {
	Status auth_entities.PromotionStatus
	Reason string // Specific reason, empty if eligible
}

// isEligibleForSignupPromotion checks if a user is eligible for a signup promotion (windsurf or cursor). This is limited to individual users created in the last 5 minutes.
func (s *TeamManagementServer) isEligibleForSignupPromotion(ctx context.Context, user *auth_entities.User, promotionName string) (PromotionEligibilityResult, error) {
	// Get promotion configuration
	config, exists := getPromotionConfig(promotionName)
	if !exists {
		log.Ctx(ctx).Error().Str("user_id", user.Id).Str("promotion_name", promotionName).Msg("Unsupported promotion type")
		return PromotionEligibilityResult{
			Status: auth_entities.PromotionStatus_PROMOTION_STATUS_UNKNOWN,
			Reason: "unsupported_promotion_type",
		}, fmt.Errorf("unsupported promotion type: %s", promotionName)
	}

	// Check if this is a signup promotion
	if config.Type != PromotionTypeSignup {
		log.Ctx(ctx).Error().Str("user_id", user.Id).Str("promotion_name", promotionName).Str("type", string(config.Type)).Msg("Not a signup promotion")
		return PromotionEligibilityResult{
			Status: auth_entities.PromotionStatus_PROMOTION_STATUS_UNKNOWN,
			Reason: "not_signup_promotion",
		}, fmt.Errorf("promotion %s is not a signup promotion", promotionName)
	}

	// Check feature flag
	enabled, err := config.FeatureFlag.Get(s.featureFlagHandle)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("promotion", config.DisplayName).Msg("Failed to get promotion enabled feature flag")
		return PromotionEligibilityResult{
			Status: auth_entities.PromotionStatus_PROMOTION_STATUS_UNKNOWN,
			Reason: "feature_flag_error",
		}, nil
	}
	if !enabled {
		log.Ctx(ctx).Info().Str("user_id", user.Id).Str("promotion", config.DisplayName).Msg("Promotion is disabled, not eligible")
		return PromotionEligibilityResult{
			Status: auth_entities.PromotionStatus_PROMOTION_STATUS_INELIGIBLE,
			Reason: "feature_flag_disabled",
		}, nil
	}

	// Check if user already has any promotion of the same type (signup-promo)
	existingPromotions := hasPromotionsOfType(user, config.Type)
	var currentPromotion *auth_entities.UserPromotion

	if len(existingPromotions) > 0 {
		// If user is already enrolled in a different promotion of the same type, they're ineligible
		for _, existingPromotion := range existingPromotions {
			if existingPromotion.PromotionName != promotionName && existingPromotion.Status == auth_entities.PromotionStatus_PROMOTION_STATUS_ENROLLED {
				log.Ctx(ctx).Info().
					Str("user_id", user.Id).
					Str("existing_promotion", existingPromotion.PromotionName).
					Str("requested_promotion", promotionName).
					Str("promotion_type", string(config.Type)).
					Msg("User is already enrolled in a different promotion of the same type")
				return PromotionEligibilityResult{
					Status: auth_entities.PromotionStatus_PROMOTION_STATUS_INELIGIBLE,
					Reason: "already_enrolled_in_other_signup_promo",
				}, nil
			}
			if existingPromotion.PromotionName == promotionName {
				currentPromotion = existingPromotion
			}
		}
	}

	// Check current promotion status if it exists
	if currentPromotion != nil {
		if currentPromotion.Status == auth_entities.PromotionStatus_PROMOTION_STATUS_ENROLLED {
			return PromotionEligibilityResult{
				Status: currentPromotion.Status,
				Reason: "already_enrolled",
			}, nil
		}
		if currentPromotion.EnrollmentAttempts >= maxPromotionEnrollmentAttempts {
			return PromotionEligibilityResult{
				Status: auth_entities.PromotionStatus_PROMOTION_STATUS_INELIGIBLE,
				Reason: "max_attempts_reached",
			}, nil
		}
		if currentPromotion.Status == auth_entities.PromotionStatus_PROMOTION_STATUS_ELIGIBLE {
			return PromotionEligibilityResult{
				Status: currentPromotion.Status,
			}, nil
		}
	}

	// User should not be in a self-serve team
	currentTenant, err := s.tenantMap.GetTenantByID(ctx, user.Tenants[0])
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("user_id", user.Id).Msg("Failed to get tenant")
		return PromotionEligibilityResult{
			Status: auth_entities.PromotionStatus_PROMOTION_STATUS_UNKNOWN,
			Reason: "tenant_lookup_error",
		}, err
	}
	if currentTenant == nil {
		log.Ctx(ctx).Error().Str("user_id", user.Id).Msg("Tenant not found")
		return PromotionEligibilityResult{
			Status: auth_entities.PromotionStatus_PROMOTION_STATUS_UNKNOWN,
			Reason: "tenant_not_found",
		}, fmt.Errorf("tenant not found")
	}
	isSelfServeTeam := tenantutil.IsSelfServeTeamTenant(currentTenant)
	if isSelfServeTeam {
		log.Ctx(ctx).Info().Str("user_id", user.Id).Str("promotion", config.DisplayName).Msg("User is in a self-serve team, not eligible for signup promotion")
		return PromotionEligibilityResult{
			Status: auth_entities.PromotionStatus_PROMOTION_STATUS_INELIGIBLE,
			Reason: "user_in_self_serve_team",
		}, nil
	}

	// User must have been created in the last 5 minutes
	if user.CreatedAt == nil {
		log.Ctx(ctx).Info().Str("user_id", user.Id).Str("promotion", config.DisplayName).Msg("User has no creation time, not eligible for signup promotion")
		return PromotionEligibilityResult{
			Status: auth_entities.PromotionStatus_PROMOTION_STATUS_INELIGIBLE,
			Reason: "no_creation_time",
		}, nil
	}

	newUserWindowAgo := time.Now().Add(-promotionNewUserWindow)
	userCreatedAt := user.CreatedAt.AsTime()
	if userCreatedAt.Before(newUserWindowAgo) {
		log.Ctx(ctx).Info().
			Str("user_id", user.Id).
			Str("promotion", config.DisplayName).
			Time("created_at", userCreatedAt).
			Time("cutoff", newUserWindowAgo).
			Msgf("User was not created in the last %d minutes, not eligible for %s promotion", int(promotionNewUserWindow.Minutes()), config.DisplayName)
		return PromotionEligibilityResult{
			Status: auth_entities.PromotionStatus_PROMOTION_STATUS_INELIGIBLE,
			Reason: "user_not_new_enough",
		}, nil
	}

	log.Ctx(ctx).Info().Str("user_id", user.Id).Str("promotion", config.DisplayName).Msg("User is eligible for signup promotion")
	return PromotionEligibilityResult{
		Status: auth_entities.PromotionStatus_PROMOTION_STATUS_ELIGIBLE,
		Reason: "",
	}, nil
}

// ProcessPromotion processes a promotion for a user, awarding any applicable credits and updating the user record
func (s *TeamManagementServer) ProcessPromotion(
	ctx context.Context, req *authpb.ProcessPromotionRequest,
) (*authpb.ProcessPromotionResponse, error) {
	promotionName := req.PromotionName

	if s.orbClient == nil || !s.orbConfig.Enabled {
		log.Ctx(ctx).Error().Str("augment_user_id", req.UserId).Msg("Orb is not enabled")
		promotionProcessingTotal.WithLabelValues(promotionName, "error", "orb_disabled").Inc()
		return nil, status.Error(codes.Internal, "Orb is not enabled")
	}

	if req.UserId == "" {
		log.Ctx(ctx).Error().Msg("User ID is required")
		promotionProcessingTotal.WithLabelValues(promotionName, "error", "missing_user_id").Inc()
		return nil, status.Error(codes.InvalidArgument, "User ID is required")
	}

	err := s.teamManagementAuthCheck(ctx, nil, &req.UserId, tokenscopesproto.Scope_AUTH_RW, false, "ProcessPromotion")
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("user_id", req.UserId).Msg("Failed to check permissions")
		promotionProcessingTotal.WithLabelValues(promotionName, "error", "auth_failed").Inc()
		return nil, err
	}

	if req.RecaptchaToken == "" {
		log.Ctx(ctx).Warn().Str("user_id", req.UserId).Msg("No reCAPTCHA token provided for promotion processing")
		promotionProcessingTotal.WithLabelValues(promotionName, "error", "no_recaptcha_token").Inc()
		return nil, status.Error(codes.PermissionDenied, "reCAPTCHA token is required")
	}
	threshold, err := promotionRecaptchaThreshold.Get(s.featureFlagHandle)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to get promotion reCAPTCHA threshold feature flag")
		promotionProcessingTotal.WithLabelValues(promotionName, "error", "feature_flag_error").Inc()
		return nil, status.Error(codes.Internal, "Failed to get promotion reCAPTCHA threshold")
	}
	err = s.validateRecaptchaToken(ctx, req.RecaptchaToken, promotionRecaptchaAction, threshold)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("user_id", req.UserId).Msg("reCAPTCHA validation failed")
		promotionProcessingTotal.WithLabelValues(promotionName, "error", "recaptcha_failed").Inc()
		if err := s.increasePromotionEnrollmentAttempts(ctx, req.UserId, promotionName); err != nil {
			log.Ctx(ctx).Error().Err(err).Str("user_id", req.UserId).Msg("Failed to increase promotion enrollment attempts")
		}
		return nil, status.Error(codes.PermissionDenied, "Security verification failed")
	}
	log.Ctx(ctx).Info().Str("user_id", req.UserId).Msg("reCAPTCHA validation successful")

	// Check if promotion is supported
	config, exists := getPromotionConfig(req.PromotionName)
	if !exists {
		log.Ctx(ctx).Info().
			Str("user_id", req.UserId).
			Str("promotion_name", req.PromotionName).
			Msg("Unsupported promotion type")
		promotionProcessingTotal.WithLabelValues(promotionName, "error", "unsupported_promotion").Inc()
		return nil, status.Error(codes.InvalidArgument, "Unsupported promotion type")
	}

	if !req.FileValidationPassed {
		log.Ctx(ctx).Error().Str("user_id", req.UserId).Msg("File validation failed")
		if err := s.increasePromotionEnrollmentAttempts(ctx, req.UserId, promotionName); err != nil {
			log.Ctx(ctx).Error().Err(err).Str("user_id", req.UserId).Msg("Failed to increase promotion enrollment attempts")
		}
		promotionProcessingTotal.WithLabelValues(promotionName, "error", "file_validation_failed").Inc()
		return nil, status.Error(codes.InvalidArgument, "File validation failed")
	}

	userDAO := s.daoFactory.GetUserDAO()
	user, err := userDAO.Get(ctx, req.UserId)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("user_id", req.UserId).Msg("Failed to get user")
		promotionProcessingTotal.WithLabelValues(promotionName, "error", "get_user_failed").Inc()
		return nil, status.Error(codes.Internal, "Failed to get user")
	}
	if user == nil {
		log.Ctx(ctx).Info().Str("user_id", req.UserId).Msg("User not found")
		promotionProcessingTotal.WithLabelValues(promotionName, "error", "user_not_found").Inc()
		return nil, status.Error(codes.NotFound, "User not found")
	}

	eligibilityResult, err := s.isEligibleForSignupPromotion(ctx, user, promotionName)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("user_id", req.UserId).Msg("Failed to check windsurf promotion eligibility")
		promotionProcessingTotal.WithLabelValues(promotionName, "error", "evaluate_failed").Inc()
		return nil, status.Error(codes.Internal, "Failed to check promotion eligibility")
	}

	switch eligibilityResult.Status {
	case auth_entities.PromotionStatus_PROMOTION_STATUS_UNKNOWN:
		log.Ctx(ctx).Info().
			Str("user_id", req.UserId).
			Str("promotion_status", eligibilityResult.Status.String()).
			Str("reason", eligibilityResult.Reason).
			Msg("User promotion status is unknown, not eligible for promotion")

		promotionProcessingTotal.WithLabelValues(promotionName, "error", eligibilityResult.Reason).Inc()
		return &authpb.ProcessPromotionResponse{Success: false}, nil

	case auth_entities.PromotionStatus_PROMOTION_STATUS_INELIGIBLE:
		log.Ctx(ctx).Info().
			Str("user_id", req.UserId).
			Str("promotion_status", eligibilityResult.Status.String()).
			Str("reason", eligibilityResult.Reason).
			Msg("User is not eligible for promotion")
		if err := s.increasePromotionEnrollmentAttempts(ctx, req.UserId, promotionName); err != nil {
			log.Ctx(ctx).Error().Err(err).Str("user_id", req.UserId).Msg("Failed to increase promotion enrollment attempts")
		}

		promotionProcessingTotal.WithLabelValues(promotionName, "error", eligibilityResult.Reason).Inc()
		return &authpb.ProcessPromotionResponse{Success: false}, nil

	case auth_entities.PromotionStatus_PROMOTION_STATUS_ENROLLED:
		log.Ctx(ctx).Info().
			Str("user_id", req.UserId).
			Str("promotion_status", eligibilityResult.Status.String()).
			Str("reason", eligibilityResult.Reason).
			Msg("User is already enrolled for promotion")

		promotionProcessingTotal.WithLabelValues(promotionName, "success", eligibilityResult.Reason).Inc()
		return &authpb.ProcessPromotionResponse{Success: true}, nil

	case auth_entities.PromotionStatus_PROMOTION_STATUS_ELIGIBLE:
		log.Ctx(ctx).Info().
			Str("user_id", req.UserId).
			Str("promotion_status", eligibilityResult.Status.String()).
			Msg("User is eligible for promotion, proceeding with enrollment")
		// Continue with promotion processing below - this is the only case that continues
	default:
		log.Ctx(ctx).Error().
			Str("user_id", req.UserId).
			Str("promotion_status", eligibilityResult.Status.String()).
			Msg("Unexpected promotion status")
		promotionProcessingTotal.WithLabelValues(promotionName, "error", "unexpected_status").Inc()
		return nil, status.Error(codes.Internal, "Unexpected promotion status")
	}

	// Process the Windsurf 2025 promotion
	if user.OrbCustomerId == "" {
		log.Ctx(ctx).Error().Str("user_id", req.UserId).Msg("User has no Orb customer ID")
		promotionProcessingTotal.WithLabelValues(promotionName, "error", "no_orb_customer_id").Inc()
		return nil, status.Error(codes.NotFound, "User has no Orb customer ID")
	}

	// Generate idempotency key based on promotion type - one per user/promotion type combination
	// This ensures users can only enroll in one promotion of each type
	idempotencyKey := fmt.Sprintf("%s-%s", req.UserId, string(config.Type))

	log.Ctx(ctx).Info().Str("user_id", req.UserId).Str("idempotency_key", idempotencyKey).Str("promotion_type", string(config.Type)).Msg("Generated idempotency key for promotion processing")

	log.Ctx(ctx).Info().Str("user_id", req.UserId).Float64("credits", config.Credits).Str("promotion", promotionName).Msg("Adding free credits for signup promotion")
	s.WriteTeamsAuditLog(ctx,
		fmt.Sprintf("Processing promotion %s for user %s. Giving user %.0f free credits", promotionName, req.UserId, config.Credits),
		audit.NewUser(req.UserId),
	)

	err = s.orbClient.GrantFreeCredits(ctx, orb.OrbCreditGrant{
		CustomerOrbID: user.OrbCustomerId,
		NumberCredits: config.Credits,
		Description:   config.Description,
		ExpiryDate:    time.Now().AddDate(0, config.ExpiryMonths, 0),
		Currency:      s.orbConfig.PricingUnit,
	}, &idempotencyKey)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("user_id", req.UserId).Str("promotion", promotionName).Msg("Failed to add credits for signup promotion")
		promotionProcessingTotal.WithLabelValues(promotionName, "error", "credit_grant_failed").Inc()
		return nil, status.Error(codes.Internal, "Failed to add credits for signup promotion")
	}

	if err := s.increasePromotionEnrollmentAttempts(ctx, req.UserId, promotionName); err != nil {
		log.Ctx(ctx).Error().Err(err).Str("user_id", req.UserId).Msg("Failed to increase promotion enrollment attempts")
	}

	// Mark the promotion as enrolled
	_, err = userDAO.TryUpdate(ctx, req.UserId, func(u *auth_entities.User) bool {
		for _, promotion := range u.Promotions {
			if promotion.PromotionName == promotionName {
				promotion.Status = auth_entities.PromotionStatus_PROMOTION_STATUS_ENROLLED
				promotion.EnrolledAt = timestamppb.Now()
				return true
			}
		}
		log.Ctx(ctx).Error().Str("user_id", req.UserId).Str("promotion_name", promotionName).Msg("Failed to find promotion to update status")
		return false // Should not happen since EvaluatePromotionEligibility already added the promotion
	}, DefaultRetry)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("user_id", req.UserId).Msg("Failed to update user promotion to enrolled status")
		promotionProcessingTotal.WithLabelValues(promotionName, "error", "user_update_failed").Inc()
		return nil, status.Error(codes.Internal, "Failed to update user promotion status")
	}

	log.Ctx(ctx).Info().
		Str("user_id", req.UserId).
		Str("promotion_name", promotionName).
		Msg("Successfully processed promotion for user")

	promotionProcessingTotal.WithLabelValues(promotionName, "success", "enrolled").Inc()
	return &authpb.ProcessPromotionResponse{Success: true}, nil
}

func (s *TeamManagementServer) increasePromotionEnrollmentAttempts(ctx context.Context, userID, promotionName string) error {
	userDAO := s.daoFactory.GetUserDAO()
	_, err := userDAO.TryUpdate(ctx, userID, func(u *auth_entities.User) bool {
		for _, promotion := range u.Promotions {
			if promotion.PromotionName == promotionName {
				promotion.EnrollmentAttempts++
				return true
			}
		}
		// add promotion if not found
		newPromotion := &auth_entities.UserPromotion{
			PromotionName:      promotionName,
			EnrollmentAttempts: 1,
		}
		u.Promotions = append(u.Promotions, newPromotion)
		return true
	}, DefaultRetry)
	return err
}

func (s *TeamManagementServer) GrantFreeCreditsToUsers(ctx context.Context, req *authpb.GrantFreeCreditsToUsersRequest) (*authpb.GrantFreeCreditsToUsersResponse, error) {
	requestID := uuid.New().String()
	logger := log.With().Str("requestID", requestID).Logger()

	logger.Info().Int("user_count", len(req.UserCredits)).Msg("GrantFreeCreditsToUsers request received")

	// Validate idempotency key length
	if len(req.IdempotencyKey) > 16 {
		return nil, status.Error(codes.InvalidArgument, "Idempotency key must be 16 characters or less")
	}

	// Auth check - require AUTH_RW scope for wildcard tenant access
	err := s.teamManagementWildcardAuthCheck(ctx, tokenscopesproto.Scope_AUTH_RW, "GrantFreeCreditsToUsers")
	if err != nil {
		return nil, err
	}

	authClaims, ok := auth.GetAugmentClaims(ctx)
	if !ok {
		return nil, status.Error(codes.Unknown, "Invalid context")
	}

	// Check if this is an IAP user or service token
	_, isIapUser := authClaims.GetIapEmail()
	if !isIapUser && authClaims.ServiceName == "" {
		logger.Error().Msg("GrantFreeCreditsToUsers endpoint is only accessible to IAP users or service tokens")
		return nil, status.Error(codes.PermissionDenied, "This endpoint is only accessible to IAP users or service tokens")
	}
	err = requireReasonForIAPUser(ctx, req.Reason)
	if err != nil {
		logger.Error().Err(err).Msg("Reason is required for IAP users")
		return nil, err
	}
	requestContext, _ := requestcontext.FromGrpcContext(ctx)

	resp := &authpb.GrantFreeCreditsToUsersResponse{
		Results: make([]*authpb.CreditGrantResult, 0, len(req.UserCredits)),
	}

	successfulGrants := 0
	failedGrants := 0

	// We may need to grant credits to a large number of users, so starting a goroutine with a background
	// context to keep it running even if the RPC times out. If the RPC times out, we need to query the
	// audit log to determine which grants succeeded.
	var wg sync.WaitGroup
	wg.Add(1)
	go func() {
		defer wg.Done()
		backgroundCtx := authClaims.NewContext(context.Background())

		// Process each user credit grant
		for _, userCredit := range req.UserCredits {

			s.auditLogger.WriteAuditLog(
				authClaims,
				fmt.Sprintf("GrantFreeCreditsToUsers request received for user %s", userCredit.UserId),
				audit.NewUser(userCredit.UserId),
				requestContext,
			)

			userLogger := logger.With().
				Str("user_id", userCredit.UserId).
				Int64("num_user_messages", userCredit.NumUserMessages).
				Logger()

			userLogger.Info().Msg("Starting credit grant for user")

			result := &authpb.CreditGrantResult{
				UserId: userCredit.UserId,
				Status: authpb.CreditGrantResult_STATUS_UNKNOWN,
			}

			// Grant credits to the user
			description := fmt.Sprintf("Free credits granted to user %s", userCredit.UserId)
			if req.Reason != "" {
				description = req.Reason
			}
			err := s.grantCreditsToUser(backgroundCtx, userCredit, req.IdempotencyKey, description, userLogger)
			if err != nil {
				userLogger.Error().Err(err).Msg("Failed to grant credits to user")
				result.Status = authpb.CreditGrantResult_STATUS_ERROR
				result.ErrorMessage = fmt.Sprintf("%v", err)
				failedGrants++
			} else {
				result.Status = authpb.CreditGrantResult_STATUS_SUCCESS
				successfulGrants++
			}

			resp.Results = append(resp.Results, result)

			// Sleep 0.1 second between users
			time.Sleep(100 * time.Millisecond)
		}
	}()
	wg.Wait()

	logger.Info().
		Int("successful_grants", successfulGrants).
		Int("failed_grants", failedGrants).
		Msg("GrantFreeCreditsToUsers request completed")

	return resp, nil
}

// grantCreditsToUser grants free credits to a single user
func (s *TeamManagementServer) grantCreditsToUser(
	ctx context.Context,
	userCredit *authpb.UserCreditGrant,
	baseIdempotencyKey string,
	reason string,
	logger zerolog.Logger,
) error {
	userDAO := s.daoFactory.GetUserDAO()

	// Validate user credit input
	if userCredit.UserId == "" {
		return fmt.Errorf("user_id cannot be empty")
	}
	if userCredit.NumUserMessages <= 0 {
		return fmt.Errorf("num_user_messages must be positive")
	}

	// Get user from BigTable to get tenant ID
	user, err := userDAO.Get(ctx, userCredit.UserId)
	if err != nil {
		logger.Error().Err(err).Msg("Failed to get user")
		return fmt.Errorf("failed to get user: %w", err)
	}
	if user == nil {
		logger.Error().Msg("User not found")
		return fmt.Errorf("user not found")
	}

	// User must have exactly one tenant
	if len(user.Tenants) != 1 {
		logger.Error().Int("tenant_count", len(user.Tenants)).Msg("User must have exactly one tenant")
		return fmt.Errorf("user must have exactly one tenant, has %d", len(user.Tenants))
	}

	// Use the single tenant ID to get billing info
	tenantID := user.Tenants[0]

	// Get user billing info using the existing utility function
	billingInfo, err := GetUserBillingInfo(ctx, userCredit.UserId, tenantID, s.daoFactory, s.tenantMap)
	if err != nil {
		logger.Error().Err(err).Str("tenant_id", tenantID).Msg("Failed to get user billing info")
		return fmt.Errorf("failed to get user billing info: %w", err)
	}

	if billingInfo.OrbCustomerID == "" {
		logger.Error().Str("tenant_id", tenantID).Bool("is_self_serve_team", billingInfo.IsSelfServeTeam).Msg("No Orb customer ID found")
		return fmt.Errorf("no Orb customer ID found")
	}

	t, err := s.tenantMap.GetTenantByIdDeletedOk(tenantID)
	if err != nil {
		logger.Error().Err(err).Str("tenant_id", tenantID).Msg("Failed to get tenant name")
		return fmt.Errorf("failed to get tenant name: %w", err)
	}

	// Grant free credits using Orb client
	if s.orbClient == nil {
		logger.Error().Msg("Orb client not available")
		return fmt.Errorf("orb client not available")
	}

	creditGrant := orb.OrbCreditGrant{
		CustomerOrbID: billingInfo.OrbCustomerID,
		NumberCredits: float64(userCredit.NumUserMessages),
		Description:   reason,
		ExpiryDate:    time.Now().AddDate(1, 0, 0), // Expire in 1 year
		Currency:      s.orbConfig.PricingUnit,
		StartDate:     nil, // Start immediately
	}

	idempotencyKey := fmt.Sprintf("grant-cr-%s-%s", userCredit.UserId, baseIdempotencyKey)
	err = s.orbClient.GrantFreeCredits(ctx, creditGrant, &idempotencyKey)
	if err != nil {
		logger.Error().Err(err).Str("orb_customer_id", billingInfo.OrbCustomerID).Bool("is_self_serve_team", billingInfo.IsSelfServeTeam).Msg("Failed to grant free credits")
		return fmt.Errorf("failed to grant free credits: %w", err)
	}

	logger.Info().Str("orb_customer_id", billingInfo.OrbCustomerID).Bool("is_self_serve_team", billingInfo.IsSelfServeTeam).Int64("num_credits", userCredit.NumUserMessages).Msg("Successfully granted free credits")

	// Audit log for individual user credit grant
	authClaims, _ := auth.GetAugmentClaims(ctx)
	requestContext, _ := requestcontext.FromGrpcContext(ctx)
	s.auditLogger.WriteAuditLog(
		authClaims,
		fmt.Sprintf("Granted %d credits to user %s (orb_customer_id: %s, tenant_id: %s, is_self_serve_team: %v, reason: %s)",
			userCredit.NumUserMessages, userCredit.UserId, billingInfo.OrbCustomerID, tenantID, billingInfo.IsSelfServeTeam, reason),
		audit.NewUser(userCredit.UserId),
		audit.NewTenantName(t.Name),
		requestContext,
	)

	return nil
}

// validateRecaptchaToken validates a reCAPTCHA token using the configured validator
func (s *TeamManagementServer) validateRecaptchaToken(ctx context.Context, token string, expectedAction string, threshold float64) error {
	if s.recaptchaValidator == nil {
		log.Ctx(ctx).Warn().Msg("reCAPTCHA validator not configured, skipping validation")
		return nil
	}

	valid := s.recaptchaValidator.Allow(ctx, token, expectedAction, "", "", threshold, "")
	if !valid {
		return fmt.Errorf("reCAPTCHA validation failed")
	}

	return nil
}

func (s *TeamManagementServer) UpdateAdminUserForTeam(ctx context.Context, req *authpb.UpdateAdminUserForTeamRequest) (*authpb.UpdateAdminUserForTeamResponse, error) {
	requestID := uuid.New().String()
	logger := log.With().Str("requestID", requestID).Str("tenant_id", req.TenantId).Str("admin_user_id", req.AdminUserId).Logger()

	logger.Info().Msg("UpdateAdminUserForTeam request received")

	// Auth check - require AUTH_RW scope for wildcard tenant access
	err := s.teamManagementWildcardAuthCheck(ctx, tokenscopesproto.Scope_AUTH_RW, "UpdateAdminUserForTeam")
	if err != nil {
		return nil, err
	}

	authClaims, ok := auth.GetAugmentClaims(ctx)
	if !ok {
		return nil, status.Error(codes.Unknown, "Invalid context")
	}

	// Check if this is an IAP user - only IAP users can access this endpoint
	iapEmail, isIapUser := authClaims.GetIapEmail()
	if !isIapUser {
		logger.Error().Msg("UpdateAdminUserForTeam endpoint is only accessible to IAP users")
		return nil, status.Error(codes.PermissionDenied, "This endpoint is only accessible to IAP users")
	}

	logger.Info().Str("iap_email", iapEmail).Msg("IAP user accessing UpdateAdminUserForTeam")

	// Find the self serve team tenant using the tenant id, verify it is self serve team
	tenant, err := s.tenantMap.GetTenantByID(ctx, req.TenantId)
	if err != nil || tenant == nil {
		logger.Error().Err(err).Msg("Failed to get tenant or tenant not found")
		return nil, status.Error(codes.NotFound, "Tenant not found")
	}

	if !tenantutil.IsSelfServeTeamTenant(tenant) {
		logger.Error().Msg("Tenant is not a self-serve team")
		return nil, status.Error(codes.InvalidArgument, "Tenant is not a self-serve team")
	}

	// Find the TenantSubscriptionMapping of the team tenant
	tenantSubscriptionMappingDAO := s.daoFactory.GetTenantSubscriptionMappingDAO()
	tenantMapping, err := tenantSubscriptionMappingDAO.Get(ctx, req.TenantId)
	if err != nil || tenantMapping == nil {
		logger.Error().Err(err).Msg("Failed to get tenant subscription mapping or mapping not found")
		return nil, status.Error(codes.NotFound, "Tenant subscription mapping not found")
	}

	// Find the admin user by admin user id and verify the admin user belongs to the tenant
	userDAO := s.daoFactory.GetUserDAO()
	adminUser, err := userDAO.Get(ctx, req.AdminUserId)
	if err != nil || adminUser == nil {
		logger.Error().Err(err).Msg("Failed to get admin user or user not found")
		return nil, status.Error(codes.NotFound, "Admin user not found")
	}

	if !slices.Contains(adminUser.Tenants, req.TenantId) {
		logger.Error().Msg("Admin user does not belong to the tenant")
		return nil, status.Error(codes.InvalidArgument, "Admin user does not belong to the tenant")
	}

	// The admin user must have Stripe and Orb customer IDs
	if adminUser.StripeCustomerId == "" || adminUser.OrbCustomerId == "" {
		logger.Error().Msg("Admin user does not have Stripe or Orb customer IDs")
		return nil, status.Error(codes.InvalidArgument, "Admin user does not have Stripe or Orb customer IDs")
	}

	// Give the admin user CustomerUiRole_ADMIN (if not already have the role)
	userTenantMappingDAO := s.daoFactory.GetUserTenantMappingDAO(tenant.Name)
	userTenantMapping, err := userTenantMappingDAO.GetByUser(ctx, req.AdminUserId)
	if err != nil || userTenantMapping == nil {
		logger.Error().Err(err).Msg("Failed to get user tenant mapping or mapping not found")
		return nil, status.Error(codes.NotFound, "User tenant mapping not found")
	}

	hasAdminRole := slices.Contains(userTenantMapping.CustomerUiRoles, auth_entities.CustomerUiRole_ADMIN)
	if !hasAdminRole {
		userTenantMapping.CustomerUiRoles = append(userTenantMapping.CustomerUiRoles, auth_entities.CustomerUiRole_ADMIN)
		_, err = userTenantMappingDAO.Update(ctx, userTenantMapping)
		if err != nil {
			logger.Error().Err(err).Msg("Failed to update user tenant mapping with admin role")
			return nil, status.Error(codes.Internal, "Failed to update user tenant mapping with admin role")
		}
		logger.Info().Msg("Added ADMIN role to user")
	}

	s.auditLogger.WriteAuditLog(
		authClaims,
		fmt.Sprintf("IAP user %s: UpdateAdminUserForTeam request for tenant %s, admin user %s", iapEmail, req.TenantId, req.AdminUserId),
		audit.NewTenantName(tenant.Name),
		audit.NewUser(req.AdminUserId),
	)

	// Copy the admin user's billing info to the team's TenantSubscriptionMapping entity
	_, err = tenantSubscriptionMappingDAO.TryUpdate(ctx, req.TenantId, func(mapping *auth_entities.TenantSubscriptionMapping) bool {
		mapping.StripeCustomerId = adminUser.StripeCustomerId
		mapping.OrbCustomerId = adminUser.OrbCustomerId
		mapping.OrbSubscriptionId = adminUser.OrbSubscriptionId

		return true
	}, DefaultRetry)
	if err != nil {
		logger.Error().Err(err).Msg("Failed to update tenant subscription mapping")
		return nil, status.Error(codes.Internal, "Failed to update tenant subscription mapping")
	}

	logger.Info().
		Str("tenant_id", req.TenantId).
		Str("admin_user_id", req.AdminUserId).
		Str("stripe_customer_id", adminUser.StripeCustomerId).
		Str("orb_customer_id", adminUser.OrbCustomerId).
		Str("orb_subscription_id", adminUser.OrbSubscriptionId).
		Msg("Updated tenant subscription mapping with admin user's billing info")

	// Update subscription owner to team tenant if subscription is found by orbSubscriptionId
	if adminUser.OrbSubscriptionId != "" {
		subscriptionDAO := s.daoFactory.GetSubscriptionDAO()
		subscription, err := subscriptionDAO.Get(ctx, adminUser.OrbSubscriptionId)
		if err != nil {
			logger.Error().Err(err).Str("orb_subscription_id", adminUser.OrbSubscriptionId).Msg("Failed to get subscription")
			return nil, status.Error(codes.Internal, "Failed to get subscription")
		}

		if subscription != nil {
			_, err = subscriptionDAO.TryUpdate(ctx, adminUser.OrbSubscriptionId, func(sub *auth_entities.Subscription) bool {
				sub.Owner = &auth_entities.Subscription_TenantId{TenantId: req.TenantId}
				return true
			}, DefaultRetry)
			if err != nil {
				logger.Error().Err(err).Str("orb_subscription_id", adminUser.OrbSubscriptionId).Msg("Failed to update subscription owner")
				return nil, status.Error(codes.Internal, "Failed to update subscription owner")
			}
			logger.Info().Str("orb_subscription_id", adminUser.OrbSubscriptionId).Str("tenant_id", req.TenantId).Msg("Updated subscription owner to team tenant")
		} else {
			logger.Info().Str("orb_subscription_id", adminUser.OrbSubscriptionId).Msg("Subscription not found, skipping owner update")
		}
	}

	logger.Info().Msg("UpdateAdminUserForTeam request completed successfully")

	return &authpb.UpdateAdminUserForTeamResponse{}, nil
}

// Generic RPC for staging changes to a user/team for account/billing management
func (s *TeamManagementServer) StageChange(
	ctx context.Context, req *authpb.StageChangeRequest,
) (*authpb.StageChangeResponse, error) {
	enabled, err := pendingChangesEnabled.Get(s.featureFlagHandle)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to get pending changes enabled feature flag")
		return nil, status.Error(codes.Internal, "Failed to get pending changes enabled feature flag")
	}
	if !enabled {
		log.Ctx(ctx).Warn().Msg("Pending changes are not enabled")
		return nil, status.Error(codes.Unimplemented, "Pending changes are not enabled")
	}

	if req.UserId == "" {
		log.Ctx(ctx).Error().Msg("User ID is required")
		return nil, status.Error(codes.InvalidArgument, "User ID is required")
	}
	if req.Change == nil {
		log.Ctx(ctx).Error().Msg("Change is required")
		return nil, status.Error(codes.InvalidArgument, "Change is required")
	}

	billingInfo, err := s.validateUserAndGetBillingInfo(ctx, req.UserId, tokenscopesproto.Scope_AUTH_RW, true, "StageChange")
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to validate user and get billing info")
		return nil, err
	}

	userDAO := s.daoFactory.GetUserDAO()
	user, err := userDAO.Get(ctx, req.UserId)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msgf("Failed to get user %s", req.UserId)
		return nil, status.Error(codes.Internal, "Failed to get user information")
	}
	if user == nil {
		log.Ctx(ctx).Error().Msgf("User %s not found", req.UserId)
		return nil, status.Error(codes.NotFound, "User not found")
	}
	currentTenantID := user.Tenants[0]

	// Get pending change from billing info
	pendingChange := billingInfo.PendingChange

	switch req.GetChange().(type) {
	case *authpb.StageChangeRequest_PlanChange_:
		if req.GetPlanChange() == nil {
			log.Ctx(ctx).Error().Msg("Plan change data is missing")
			return nil, status.Error(codes.InvalidArgument, "Plan change data is missing")
		}

		return s.stagePlanChange(ctx, user, currentTenantID, billingInfo, pendingChange, req.GetPlanChange().PlanId)
	default:
		log.Ctx(ctx).Error().Msg("Unsupported change type")
		return nil, status.Error(codes.InvalidArgument, "Unsupported change type")
	}
}

// TL;DR
//
// the order operations take place with orb here is
//
// 1. get the pending change
// 2. cancel the pending change (if it exists)
// 3. stage a new pending change
//
// orb only allows 1 pending change at a time per subscription, so we are protected from multiple rpcs executing concurrently after this call
func (s *TeamManagementServer) stagePlanChange(
	ctx context.Context, user *auth_entities.User, tenantID string, billingInfo *UserBillingInfo, pendingChange *auth_entities.PendingChange, targetPlanId string,
) (*authpb.StageChangeResponse, error) {
	if targetPlanId == "" {
		log.Ctx(ctx).Error().Msg("Plan ID is required")
		return nil, status.Error(codes.InvalidArgument, "Plan ID is required")
	}

	if planChange := pendingChange.GetPlanChange(); planChange != nil && planChange.GetStatus() == auth_entities.PlanChange_PENDING {
		log.Ctx(ctx).Error().Msg("Plan change is already staged")
		return nil, status.Error(codes.FailedPrecondition, "Plan change is already staged")
	}

	targetPlan := s.orbConfig.FindPlan(targetPlanId)
	if targetPlan == nil {
		log.Ctx(ctx).Error().Str("user_id", user.Id).Str("subscription_id", billingInfo.OrbSubscriptionID).Msgf(
			"Failed to find plan %s", targetPlanId)
		return nil, status.Error(codes.Internal, fmt.Sprintf("Failed to find plan %s", targetPlanId))
	}
	if targetPlan.Features.PlanType == orb_config.PlanTypePaidTrial {
		log.Ctx(ctx).Error().Str("user_id", user.Id).Str("subscription_id", billingInfo.OrbSubscriptionID).Msgf(
			"Cannot change to a trial plan")
		return nil, status.Error(codes.FailedPrecondition, fmt.Sprintf("Cannot change to a trial plan"))
	}
	if billingInfo.IsSelfServeTeam && !targetPlan.Features.TeamsAllowed {
		log.Ctx(ctx).Error().Str("user_id", user.Id).Str("subscription_id", billingInfo.OrbSubscriptionID).Msgf(
			"Teams cannot be put on the target plan %s", targetPlanId)
		return nil, status.Error(codes.InvalidArgument, fmt.Sprintf("Teams cannot be put on the target plan %s", targetPlanId))
	}

	var currentSubscription *orb.OrbSubscriptionInfo
	if billingInfo.OrbSubscriptionID != "" {
		var err error
		currentSubscription, err = s.orbClient.GetUserSubscription(ctx, billingInfo.OrbSubscriptionID, &orb.ItemIds{
			SeatsID:            s.orbConfig.SeatsItemID,
			IncludedMessagesID: s.orbConfig.IncludedMessagesItemID,
		})
		if err != nil {
			log.Ctx(ctx).Error().Err(err).Str("subscription_id", billingInfo.OrbSubscriptionID).Msg("Failed to get user's current subscription")
			return nil, status.Error(codes.Internal, "Failed to get user's current subscription")
		}
	}

	targetPlanInfo, err := s.orbClient.GetPlanInformation(ctx, orb.ItemIds{SeatsID: s.orbConfig.SeatsItemID, IncludedMessagesID: s.orbConfig.IncludedMessagesItemID}, nil, &targetPlan.ID)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("plan_id", targetPlan.ID).Msg("Failed to get target plan info")
		return nil, err
	}

	if currentSubscription == nil || currentSubscription.OrbStatus != "active" {
		resp, err := s.stageSubscriptionCreation(ctx, user, tenantID, billingInfo, targetPlan, targetPlanInfo)
		if err != nil {
			log.Ctx(ctx).Error().Err(err).Str("user_id", user.Id).Msg("Failed to stage subscription creation")
			return nil, err
		}

		creditsThisMonth := float64(resp.NumSeats) * targetPlanInfo.MessagesPerSeat
		// For new subscriptions, we don't need proration breakdown (no existing plan)
		emptyBreakdown := &ProRatedCreditsBreakdown{}
		// For new subscriptions, we always use BillingCycleAlignmentPlanChangeDate
		return s.buildPlanChangeResponse(ctx, &resp.ChangeID, currentSubscription, targetPlanInfo, billingInfo, creditsThisMonth, targetPlan, true, resp.NumSeats, true, emptyBreakdown)
	}

	if currentSubscription.ExternalPlanID == targetPlan.ID {
		log.Ctx(ctx).Info().Str("user_id", user.Id).Str("subscription_id", billingInfo.OrbSubscriptionID).Msgf(
			"User already has the correct plan, no change needed")
		return &authpb.StageChangeResponse{}, nil
	}

	if currentSubscription.PendingSubscriptionChangeID != "" {
		pendingSubscriptionChange, err := s.orbClient.GetPendingSubscriptionChange(ctx, currentSubscription.PendingSubscriptionChangeID)
		if err != nil {
			log.Ctx(ctx).Error().Err(err).Str("subscription_id", billingInfo.OrbSubscriptionID).Msg("Failed to get pending subscription change")
			return nil, status.Error(codes.Internal, "Failed to get pending subscription change")
		}

		if pendingSubscriptionChange.PlanID == targetPlan.ID {
			log.Ctx(ctx).Info().Str("user_id", user.Id).Str("subscription_id", billingInfo.OrbSubscriptionID).Msgf(
				"User already has a pending change to the correct plan, no change needed")

			currentPlanInfo, err := s.orbClient.GetPlanInformation(ctx, orb.ItemIds{SeatsID: s.orbConfig.SeatsItemID, IncludedMessagesID: s.orbConfig.IncludedMessagesItemID}, nil, &currentSubscription.ExternalPlanID)
			if err != nil {
				log.Ctx(ctx).Error().Err(err).Str("plan_id", currentSubscription.ExternalPlanID).Msg("Failed to get current plan info")
				return nil, err
			}

			// Calculate billing cycle start from anchor day instead of using billing period state date
			billingStart := CalculateBillingCycleStartFromAnchor(currentSubscription.BillingCycleDay, time.Now())
			breakdown, err := CalculateProRatedCreditsBreakdown(
				currentPlanInfo.MessagesPerSeat,
				targetPlanInfo.MessagesPerSeat,
				billingStart,
				currentSubscription.CurrentBillingPeriodEndDate,
				time.Now(),
			)
			if err != nil {
				log.Ctx(ctx).Error().Err(err).Str("subscription_id", billingInfo.OrbSubscriptionID).Msg("Failed to calculate pro-rated credits")
				return nil, err
			}
			usesBillingCycleAlignment := s.usesBillingCycleAlignmentPlanChangeDate(currentPlanInfo.ExternalPlanID, targetPlanInfo.ExternalPlanID)
			return s.buildPlanChangeResponse(ctx, &currentSubscription.PendingSubscriptionChangeID, currentSubscription, targetPlanInfo, billingInfo, math.Ceil(breakdown.TotalNewCredits+currentPlanInfo.MessagesPerSeat), targetPlan, true, currentSubscription.CurrentFixedQuantities.Seats, usesBillingCycleAlignment, &breakdown)
		}

		// wait 30 seconds before cancelling the last staged changed using the fact that
		// orb's staged changes only live for 24 hours https://docs.withorb.com/essentials/pending-changes#expiration
		pivotTime := pendingSubscriptionChange.ExpirationTime.Add(-24 * time.Hour).Add(30 * time.Second)

		if pendingSubscriptionChange.Status == orblib.SubscriptionChangeGetResponseStatusPending {
			if time.Now().After(pivotTime) {
				s.WriteTeamsAuditLog(ctx, fmt.Sprintf("cancelling pending subscription change for user %s, subscription ID %s", user.Id, billingInfo.OrbSubscriptionID))

				if err := s.orbClient.CancelPendingSubscriptionChange(ctx, currentSubscription.PendingSubscriptionChangeID, nil); err != nil {
					log.Ctx(ctx).Error().Err(err).Str("subscription_id", billingInfo.OrbSubscriptionID).Msg("Failed to cancel pending subscription change")
					return nil, status.Error(codes.Internal, "Failed to cancel pending subscription change")
				}

				s.WriteTeamsAuditLog(ctx, fmt.Sprintf("successfully cancelled pending subscription change for user %s, subscription ID %s", user.Id, billingInfo.OrbSubscriptionID))
			} else {
				log.Ctx(ctx).Info().Str("user_id", user.Id).Str("subscription_id", billingInfo.OrbSubscriptionID).Msgf(
					"User has a pending subscription change, but it is not yet time to cancel it")
				return nil, status.Error(codes.FailedPrecondition, "User has a pending subscription change, but it is not yet time to cancel it")
			}
		}
	}

	currentPlanInfo, err := s.orbClient.GetPlanInformation(ctx, orb.ItemIds{SeatsID: s.orbConfig.SeatsItemID, IncludedMessagesID: s.orbConfig.IncludedMessagesItemID}, &billingInfo.OrbSubscriptionID, nil)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("subscription_id", billingInfo.OrbSubscriptionID).Msg("Failed to get current plan info")
		return nil, err
	}

	isUpgradeByPrice, err := IsPlanUpgrade(ctx, currentPlanInfo, targetPlanInfo)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("subscription_id", billingInfo.OrbSubscriptionID).Msg("Failed to determine if plan change is an upgrade or downgrade")
		return nil, err
	}

	isUpgrade := isUpgradeByPrice || currentPlanInfo.ExternalPlanID == s.orbConfig.GetTrialPlan().ID

	if !isUpgrade {
		// downgrades happen at the end of the billing period with the exception of community, thus don't require any action to stage a pending change
		// community doesn't require a staged plan change since it doesn't require payment collection
		// but we still need to return the plan change summary

		// For downgrades, credits remain the same this month
		creditsThisMonth := currentPlanInfo.MessagesPerSeat * float64(currentSubscription.CurrentFixedQuantities.Seats)
		// For downgrades, we don't need proration breakdown (no additional credits)
		emptyBreakdown := &ProRatedCreditsBreakdown{}
		usesBillingCycleAlignment := s.usesBillingCycleAlignmentPlanChangeDate(currentPlanInfo.ExternalPlanID, targetPlanInfo.ExternalPlanID)
		return s.buildPlanChangeResponse(ctx, nil, currentSubscription, targetPlanInfo, billingInfo, creditsThisMonth, targetPlan, false, currentSubscription.CurrentFixedQuantities.Seats, usesBillingCycleAlignment, emptyBreakdown)
	}

	var billingCycleAlignment orb.BillingCycleAlignment
	var includedMessagesQuantityPerSeat float64
	var prorationBreakdown *ProRatedCreditsBreakdown
	if s.usesBillingCycleAlignmentPlanChangeDate(currentPlanInfo.ExternalPlanID, targetPlanInfo.ExternalPlanID) {
		billingCycleAlignment = orb.BillingCycleAlignmentPlanChangeDate
		includedMessagesQuantityPerSeat = targetPlanInfo.MessagesPerSeat
		// For plan change date alignment, we don't need proration breakdown
		prorationBreakdown = &ProRatedCreditsBreakdown{}
	} else {
		var err error
		// Calculate billing cycle start from anchor day instead of using billing period state date
		billingStart := CalculateBillingCycleStartFromAnchor(currentSubscription.BillingCycleDay, time.Now())
		breakdown, err := CalculateProRatedCreditsBreakdown(
			currentPlanInfo.MessagesPerSeat,
			targetPlanInfo.MessagesPerSeat,
			billingStart,
			currentSubscription.CurrentBillingPeriodEndDate,
			time.Now(),
		)
		if err != nil {
			log.Ctx(ctx).Error().Err(err).Str("subscription_id", billingInfo.OrbSubscriptionID).Msg("Failed to calculate pro-rated credits")
			return nil, err
		}
		billingCycleAlignment = orb.BillingCycleAlignmentUnchanged
		includedMessagesQuantityPerSeat = math.Ceil(breakdown.TotalNewCredits + currentPlanInfo.MessagesPerSeat)
		prorationBreakdown = &breakdown
	}

	planChange := orb.OrbPlanChange{
		CustomerOrbID:  billingInfo.OrbCustomerID,
		SubscriptionID: billingInfo.OrbSubscriptionID,
		NewPlanID:      targetPlan.ID,
		PriceOverrides: []orb.OrbPriceOverrides{
			{
				PriceID:  targetPlanInfo.SeatsPriceID,
				Quantity: float64(currentSubscription.CurrentFixedQuantities.Seats),
			},
			{
				PriceID:  targetPlanInfo.IncludedMessagesPriceID,
				Quantity: float64(currentSubscription.CurrentFixedQuantities.Seats) * includedMessagesQuantityPerSeat,
			},
		},
		PlanChangeType:        orb.PlanChangeImmediate,
		BillingCycleAlignment: billingCycleAlignment,
	}

	s.WriteTeamsAuditLog(ctx, fmt.Sprintf("requesting staged plan change for user %s, subscription ID %s", user.Id, billingInfo.OrbSubscriptionID))

	resp, err := s.orbClient.SetCustomerPlanType(ctx, planChange, nil, true)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("subscription_id", billingInfo.OrbSubscriptionID).Msg("Failed to stage plan change")
		return nil, err
	}

	s.WriteTeamsAuditLog(ctx, fmt.Sprintf("successfully staged plan change for user %s, subscription ID %s", user.Id, billingInfo.OrbSubscriptionID))

	usesBillingCycleAlignment := billingCycleAlignment == orb.BillingCycleAlignmentPlanChangeDate
	return s.buildPlanChangeResponse(ctx, &resp.PendingSubscriptionChangeID, currentSubscription, targetPlanInfo, billingInfo, includedMessagesQuantityPerSeat*float64(currentSubscription.CurrentFixedQuantities.Seats), targetPlan, true, currentSubscription.CurrentFixedQuantities.Seats, usesBillingCycleAlignment, prorationBreakdown)
}

type StageSubscriptionCreationResponse struct {
	NumSeats int
	ChangeID string
}

func (s *TeamManagementServer) stageSubscriptionCreation(ctx context.Context, user *auth_entities.User, tenantID string, billingInfo *UserBillingInfo, targetPlan *orb_config.PlanConfig, targetPlanInfo *orb.OrbPlanInfo) (StageSubscriptionCreationResponse, error) {
	s.WriteTeamsAuditLog(ctx, fmt.Sprintf("requesting staged subscription creation for user %s", user.Id))
	var numSeats int
	var err error

	if billingInfo.IsSelfServeTeam {
		numSeats, err = GetActiveTeamMemberCountWithPendingInvitations(ctx, s.daoFactory, s.tenantMap, tenantID)
		if err != nil {
			return StageSubscriptionCreationResponse{}, err
		}
	} else {
		numSeats = 1
	}

	planId := targetPlan.ID

	orbSubscription := orb.OrbSubscription{
		CustomerOrbID:  billingInfo.OrbCustomerID,
		ExternalPlanID: planId,
		PriceOverrides: []orb.OrbPriceOverrides{
			{
				PriceID:  targetPlanInfo.SeatsPriceID,
				Quantity: float64(numSeats),
			},
			{
				PriceID:  targetPlanInfo.IncludedMessagesPriceID,
				Quantity: float64(numSeats) * targetPlanInfo.MessagesPerSeat,
			},
		},
	}

	pendingChangeID, err := s.orbClient.CreateSubscription(ctx, orbSubscription, nil, true)
	if err != nil {
		return StageSubscriptionCreationResponse{}, status.Error(codes.Internal, fmt.Sprintf("Failed to create new Orb subscription: %v", err))
	}

	s.WriteTeamsAuditLog(ctx, fmt.Sprintf("successfully staged subscription creation for user %s", user.Id))

	return StageSubscriptionCreationResponse{NumSeats: numSeats, ChangeID: pendingChangeID}, nil
}

// Helper function to determine if a plan change uses BillingCycleAlignmentPlanChangeDate
func (s *TeamManagementServer) usesBillingCycleAlignmentPlanChangeDate(currentPlanID, targetPlanID string) bool {
	return currentPlanID == s.orbConfig.GetTrialPlan().ID ||
		currentPlanID == s.orbConfig.GetCommunityPlan().ID ||
		targetPlanID == s.orbConfig.GetCommunityPlan().ID
}

func (s *TeamManagementServer) buildPlanChangeResponse(
	ctx context.Context,
	pendingChangeID *string,
	currentSubscription *orb.OrbSubscriptionInfo,
	targetPlanInfo *orb.OrbPlanInfo,
	billingInfo *UserBillingInfo,
	creditsThisMonth float64,
	targetPlan *orb_config.PlanConfig,
	isUpgrade bool,
	numSeats int,
	usesBillingCycleAlignmentPlanChangeDate bool,
	prorationBreakdown *ProRatedCreditsBreakdown,
) (*authpb.StageChangeResponse, error) {
	pricePerSeat, err := strconv.ParseFloat(targetPlanInfo.PricePerSeat, 64)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("subscription_id", billingInfo.OrbSubscriptionID).Msg("Failed to parse price per seat")
		return nil, err
	}

	// Build proration information for immediate charges (upgrades)
	// Only include proration if we're not using BillingCycleAlignmentPlanChangeDate
	var proration *authpb.StageChangeResponse_PlanChangeResponse_Proration
	if pendingChangeID != nil && !usesBillingCycleAlignmentPlanChangeDate {
		// For upgrades with proration, get the actual amount due from the pending change
		pendingSubscriptionChange, err := s.orbClient.GetPendingSubscriptionChange(ctx, *pendingChangeID)
		if err != nil {
			log.Ctx(ctx).Error().Err(err).Str("subscription_id", billingInfo.OrbSubscriptionID).Msg("Failed to get pending subscription change")
			return nil, err
		}

		// Use the proration breakdown passed from the caller
		// This ensures consistent calculations across all code paths

		// Scale by number of seats to get total credits
		proratedCredits := prorationBreakdown.ProratedCredits * float64(numSeats)
		creditsLeftInMonth := prorationBreakdown.CreditsLeftInMonthForCurrentPlan * float64(numSeats)
		totalNewCredits := prorationBreakdown.TotalNewCredits * float64(numSeats)

		proration = &authpb.StageChangeResponse_PlanChangeResponse_Proration{
			ProratedAmountDue:                pendingSubscriptionChange.OriginalAmount,
			CreditedAmount:                   pendingSubscriptionChange.CreditAmount,
			TotalAmountDue:                   pendingSubscriptionChange.AmountDue,
			ProratedCredits:                  fmt.Sprintf("%.0f", math.Round(proratedCredits)),
			CreditsLeftInMonthForCurrentPlan: fmt.Sprintf("%.0f", math.Round(creditsLeftInMonth)),
			TotalNewCredits:                  fmt.Sprintf("%.0f", math.Round(totalNewCredits)),
		}
	}

	// Build next billing period information
	var nextBillingPeriodStartDateIso *string
	if currentSubscription != nil && !usesBillingCycleAlignmentPlanChangeDate {
		startDateIso := currentSubscription.CurrentBillingPeriodEndDate.Format(time.RFC3339)
		nextBillingPeriodStartDateIso = &startDateIso
	} else {
		// immediate is nil
		nextBillingPeriodStartDateIso = nil
	}

	nextBillingPeriod := &authpb.StageChangeResponse_PlanChangeResponse_BillingPeriod{
		StartDateIso: nextBillingPeriodStartDateIso,
		AmountDue:    fmt.Sprintf("%.2f", float64(numSeats)*pricePerSeat),
		Credits:      fmt.Sprintf("%.2f", float64(numSeats)*targetPlanInfo.MessagesPerSeat),
	}

	var confirmationMessage string

	var aiTrainingText string
	if targetPlan.Features.TrainingAllowed {
		aiTrainingText = fmt.Sprintf("By selecting the %s, I agree to permit Augment to train AI models on my code and usage data. ", targetPlan.OrbPlan.Name)
	}

	var timingText string
	if currentSubscription != nil {
		if usesBillingCycleAlignmentPlanChangeDate {
			timingText = "I understand this change will take effect immediately, and I will lose any unused messages from my monthly subscription, but any additional purchased messages will remain."
		} else if !isUpgrade {
			timingText = "I understand this downgrade will take effect at the end of my billing period. My existing messages and invoice amount will remain unchanged for the current billing period."
		} else {
			timingText = "I understand this change will take effect immediately, and I will be charged for the prorated difference in my monthly subscription."
		}
	}

	confirmationMessage = aiTrainingText + timingText

	return &authpb.StageChangeResponse{
		Response: &authpb.StageChangeResponse_PlanChangeResponse_{
			PlanChangeResponse: &authpb.StageChangeResponse_PlanChangeResponse{
				PendingChangeId:     pendingChangeID,
				Proration:           proration,
				NextBillingPeriod:   nextBillingPeriod,
				ConfirmationMessage: confirmationMessage,
			},
		},
	}, nil
}

// UnstageChange cancels a pending change by its ID
func (s *TeamManagementServer) UnstageChange(
	ctx context.Context, req *authpb.UnstageChangeRequest,
) (*authpb.UnstageChangeResponse, error) {
	enabled, err := pendingChangesEnabled.Get(s.featureFlagHandle)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to get pending changes enabled feature flag")
		return nil, status.Error(codes.Internal, "Failed to get pending changes enabled feature flag")
	}
	if !enabled {
		log.Ctx(ctx).Warn().Msg("Pending changes are not enabled")
		return nil, status.Error(codes.Unimplemented, "Pending changes are not enabled")
	}

	if req.UserId == "" {
		log.Ctx(ctx).Error().Msg("User ID is required")
		return nil, status.Error(codes.InvalidArgument, "User ID is required")
	}

	if req.PendingChangeId == "" {
		log.Ctx(ctx).Error().Msg("Pending change ID is required")
		return nil, status.Error(codes.InvalidArgument, "Pending change ID is required")
	}

	// Validate user and get billing info
	billingInfo, err := s.validateUserAndGetBillingInfo(ctx, req.UserId, tokenscopesproto.Scope_AUTH_RW, true, "UnstageChange")
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to validate user and get billing info")
		return nil, err
	}

	// Get the user entity to check for pending changes
	userDAO := s.daoFactory.GetUserDAO()
	user, err := userDAO.Get(ctx, req.UserId)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msgf("Failed to get user %s", req.UserId)
		return nil, status.Error(codes.Internal, "Failed to get user information")
	}
	if user == nil {
		log.Ctx(ctx).Error().Msgf("User not found: %s", req.UserId)
		return nil, status.Error(codes.NotFound, "User not found")
	}

	// Get pending change from billing info
	pendingChange := billingInfo.PendingChange

	// Check if the user/team has this pending change ID on their entity
	if pendingChange != nil && pendingChange.GetPlanChange() != nil &&
		pendingChange.GetPlanChange().OrbPendingChangeId != nil &&
		*pendingChange.GetPlanChange().OrbPendingChangeId == req.PendingChangeId {
		entityType := "user"
		if billingInfo.IsSelfServeTeam {
			entityType = "team"
		}
		log.Ctx(ctx).Error().
			Str("pending_change_id", req.PendingChangeId).
			Str("user_id", req.UserId).
			Str("entity_type", entityType).
			Msg("Cannot cancel pending change that is already associated with entity")
		return nil, status.Error(codes.InvalidArgument, fmt.Sprintf("Cannot cancel pending change that is already associated with %s", entityType))
	}

	// Get the pending subscription change details to validate ownership
	orbPendingChange, err := s.orbClient.GetPendingSubscriptionChange(ctx, req.PendingChangeId)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("pending_change_id", req.PendingChangeId).Msg("Failed to get pending subscription change")
		return nil, status.Error(codes.Internal, "Failed to get pending subscription change")
	}

	// Validate that the pending change belongs to the user's customer
	if orbPendingChange.CustomerID != billingInfo.OrbCustomerID {
		log.Ctx(ctx).Error().
			Str("pending_change_id", req.PendingChangeId).
			Str("expected_customer_id", billingInfo.OrbCustomerID).
			Str("actual_customer_id", orbPendingChange.CustomerID).
			Msg("Pending change does not belong to the requesting user's customer")
		return nil, status.Error(codes.PermissionDenied, "Pending change does not belong to the requesting user")
	}

	// Call Orb to cancel the pending subscription change
	err = s.orbClient.CancelPendingSubscriptionChange(ctx, req.PendingChangeId, nil)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("pending_change_id", req.PendingChangeId).Msg("Failed to cancel pending subscription change")
		return nil, status.Error(codes.Internal, "Failed to cancel pending subscription change")
	}

	// Audit log on success
	s.WriteTeamsAuditLog(ctx, fmt.Sprintf("successfully cancelled pending subscription change %s", req.PendingChangeId))

	return &authpb.UnstageChangeResponse{
		// Empty response indicates success
	}, nil
}

func (s *TeamManagementServer) HandleStagedChange(
	ctx context.Context, req *authpb.HandleStagedChangeRequest,
) (*authpb.HandleStagedChangeResponse, error) {
	pendingChangesEnabled, err := pendingChangesEnabled.Get(s.featureFlagHandle)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to get pending changes enabled feature flag")
		return nil, status.Error(codes.Internal, "Failed to get pending changes enabled feature flag")
	}
	if !pendingChangesEnabled {
		log.Ctx(ctx).Warn().Msg("Pending changes are not enabled")
		return nil, status.Error(codes.Unimplemented, "Pending changes are not enabled")
	}

	if req.UserId == "" {
		log.Ctx(ctx).Error().Msg("User ID is required")
		return nil, status.Error(codes.InvalidArgument, "User ID is required")
	}
	// TODO(cam): should we check the change field or the success/cancel urls?

	// Validate user and get billing info
	billingInfo, err := s.validateUserAndGetBillingInfo(ctx, req.UserId, tokenscopesproto.Scope_AUTH_RW, true, "HandleStagedChange")
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to validate user and get billing info")
		return nil, err
	}

	// Get the user entity
	userDAO := s.daoFactory.GetUserDAO()
	user, err := userDAO.Get(ctx, req.UserId)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msgf("Failed to get user %s", req.UserId)
		return nil, status.Error(codes.Internal, "Failed to get user information")
	}
	if user == nil {
		log.Ctx(ctx).Error().Msgf("User %s not found", req.UserId)
		return nil, status.Error(codes.NotFound, "User not found")
	}

	// Check if there is already a plan change in progress
	pendingChange := billingInfo.PendingChange
	if pendingChange != nil && pendingChange.GetPlanChange() != nil && pendingChange.GetPlanChange().GetStatus() == auth_entities.PlanChange_PENDING {
		log.Ctx(ctx).Error().Str("user_id", user.Id).Msg("Plan change is already in progress")
		return nil, status.Error(codes.FailedPrecondition, "Plan change is already in progress")
	}

	// Get the target plan ID from the request or from the pending change in Orb
	var targetPlanId string
	switch change := req.GetChange().(type) {
	case *authpb.HandleStagedChangeRequest_TargetPlanId:
		targetPlanId = change.TargetPlanId
	case *authpb.HandleStagedChangeRequest_PendingChangeId:
		// Get the pending change from Orb to extract the target plan ID
		orbPendingChange, err := s.orbClient.GetPendingSubscriptionChange(ctx, change.PendingChangeId)
		if err != nil {
			log.Ctx(ctx).Error().Err(err).Str("pending_change_id", change.PendingChangeId).Msg("Failed to get pending subscription change")
			return nil, status.Error(codes.Internal, "Failed to get pending subscription change")
		}
		targetPlanId = orbPendingChange.PlanID
	default:
		log.Ctx(ctx).Error().Msg("Either target_plan_id or pending_change_id must be provided")
		return nil, status.Error(codes.InvalidArgument, "Either target_plan_id or pending_change_id must be provided")
	}

	// Get target plan information
	targetPlan := s.orbConfig.FindPlan(targetPlanId)
	if targetPlan == nil {
		log.Ctx(ctx).Error().Str("user_id", user.Id).Str("target_plan_id", targetPlanId).Msg("Failed to find target plan")
		return nil, status.Error(codes.Internal, fmt.Sprintf("Failed to find target plan %s", targetPlanId))
	}

	// Check if teams are allowed on the target plan
	if billingInfo.IsSelfServeTeam && !targetPlan.Features.TeamsAllowed {
		log.Ctx(ctx).Error().Str("user_id", user.Id).Str("target_plan_id", targetPlan.ID).Msg("Teams are not allowed on this plan")
		return nil, status.Error(codes.InvalidArgument, "Teams are not allowed on this plan")
	}

	// Get current subscription info to determine if it's an upgrade or downgrade
	var currentSubscription *orb.OrbSubscriptionInfo
	if billingInfo.OrbSubscriptionID != "" {
		var err error
		currentSubscription, err = s.orbClient.GetUserSubscription(ctx, billingInfo.OrbSubscriptionID, &orb.ItemIds{SeatsID: s.orbConfig.SeatsItemID, IncludedMessagesID: s.orbConfig.IncludedMessagesItemID})
		if err != nil {
			log.Ctx(ctx).Error().Err(err).Str("subscription_id", billingInfo.OrbSubscriptionID).Msg("Failed to get current subscription")
			return nil, status.Error(codes.Internal, "Failed to get current subscription")
		}
	}

	// Handle the case where the target plan is community (potentially a creation)
	if targetPlan.Features.PlanType == orb_config.PlanTypeCommunity {
		return s.handleCommunityPlanChange(ctx, user, billingInfo, targetPlanId)
	}

	// Handle the case where there's no current subscription (creation case)
	if currentSubscription == nil || currentSubscription.OrbStatus != "active" {
		// Creating a paid plan subscription - treat as upgrade (requires checkout)
		var pendingChangeId *string
		if change, ok := req.GetChange().(*authpb.HandleStagedChangeRequest_PendingChangeId); ok {
			pendingChangeId = &change.PendingChangeId
		}
		return s.handleUpgradeCheckout(ctx, user, billingInfo, pendingChangeId, req.SuccessUrl, req.CancelUrl)
	}

	currentPlanInfo, err := s.orbClient.GetPlanInformation(ctx, orb.ItemIds{SeatsID: s.orbConfig.SeatsItemID, IncludedMessagesID: s.orbConfig.IncludedMessagesItemID}, nil, &currentSubscription.ExternalPlanID)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("plan_id", currentSubscription.ExternalPlanID).Msg("Failed to get current plan info")
		return nil, err
	}

	targetPlanInfo, err := s.orbClient.GetPlanInformation(ctx, orb.ItemIds{SeatsID: s.orbConfig.SeatsItemID, IncludedMessagesID: s.orbConfig.IncludedMessagesItemID}, nil, &targetPlan.ID)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("plan_id", targetPlan.ID).Msg("Failed to get target plan info")
		return nil, err
	}

	// Determine if this is an upgrade or downgrade
	isUpgradeByPrice, err := IsPlanUpgrade(ctx, currentPlanInfo, targetPlanInfo)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("subscription_id", billingInfo.OrbSubscriptionID).Msg("Failed to determine if plan change is an upgrade or downgrade")
		return nil, err
	}

	isUpgrade := isUpgradeByPrice || currentPlanInfo.ExternalPlanID == s.orbConfig.GetTrialPlan().ID

	if !isUpgrade {
		return s.handleDowngrade(ctx, user, billingInfo, targetPlan, currentSubscription, targetPlanInfo)
	} else {
		var pendingChangeId *string
		if change, ok := req.GetChange().(*authpb.HandleStagedChangeRequest_PendingChangeId); ok {
			pendingChangeId = &change.PendingChangeId
		}
		return s.handleUpgradeCheckout(ctx, user, billingInfo, pendingChangeId, req.SuccessUrl, req.CancelUrl)
	}
}

// handleCommunityPlanChange enqueues a plan change to community plan
func (s *TeamManagementServer) handleCommunityPlanChange(
	ctx context.Context, user *auth_entities.User, billingInfo *UserBillingInfo, targetPlanId string,
) (*authpb.HandleStagedChangeResponse, error) {
	// Generate a unique plan change ID
	planChangeID := uuid.New().String()

	// Publish plan change message for async processing
	planChangeMsg := &auth_internal.PlanChangeMessage{
		UserId:       user.Id,
		PlanChangeId: planChangeID,
		PublishTime:  timestamppb.Now(),
		TargetPlanId: targetPlanId,
		// No orb_pending_change_id for community plans
	}

	err := s.asyncOpsPublisher.PublishPlanChange(ctx, planChangeMsg)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("user_id", user.Id).Str("plan_change_id", planChangeID).Msg("Failed to publish plan change message")
		return nil, status.Error(codes.Internal, "Failed to enqueue plan change")
	}

	// Create the pending change
	err = CreatePendingPlanChange(ctx, s.daoFactory, user, billingInfo, targetPlanId, planChangeID, nil)
	if err != nil {
		return nil, err
	}

	// Assign the message ID to the pending change if it's not already set
	err = AssignMessageIDToPendingChange(ctx, s.daoFactory, billingInfo, planChangeID)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("user_id", user.Id).Str("message_id", planChangeID).Msg("Failed to assign message ID to pending change")
		// Don't fail the entire operation if we can't assign the message ID
	}

	s.WriteTeamsAuditLog(ctx, fmt.Sprintf("successfully enqueued community plan change for user %s", user.Id))

	return &authpb.HandleStagedChangeResponse{
		// No checkout session URL for community plan
		PlanChangeType: authpb.HandleStagedChangeResponse_IMMEDIATE,
	}, nil
}

// handleDowngrade schedules a downgrade in Orb
func (s *TeamManagementServer) handleDowngrade(
	ctx context.Context, user *auth_entities.User, billingInfo *UserBillingInfo, targetPlan *orb_config.PlanConfig, currentSubscription *orb.OrbSubscriptionInfo, targetPlanInfo *orb.OrbPlanInfo,
) (*authpb.HandleStagedChangeResponse, error) {
	// Determine the number of seats to use - prefer future seats if available, otherwise use current seats
	var seatsToUse int
	if currentSubscription.FutureFixedQuantities != nil && currentSubscription.FutureFixedQuantities.Seats > 0 {
		seatsToUse = currentSubscription.FutureFixedQuantities.Seats
	} else {
		seatsToUse = currentSubscription.CurrentFixedQuantities.Seats
	}

	// For downgrades, we schedule the plan change in Orb to take effect at the end of the billing period
	orbPlanChange := orb.OrbPlanChange{
		CustomerOrbID:         billingInfo.OrbCustomerID,
		SubscriptionID:        billingInfo.OrbSubscriptionID,
		NewPlanID:             targetPlan.ID,
		PlanChangeType:        orb.PlanChangeEndOfTerm,
		BillingCycleAlignment: orb.BillingCycleAlignmentUnchanged,
		PriceOverrides: []orb.OrbPriceOverrides{
			{
				PriceID:  targetPlanInfo.SeatsPriceID,
				Quantity: float64(seatsToUse),
			},
			{
				PriceID:  targetPlanInfo.IncludedMessagesPriceID,
				Quantity: float64(seatsToUse) * targetPlanInfo.MessagesPerSeat,
			},
		},
	}

	// Unschedule any pending cancellation before scheduling the downgrade
	err := s.orbClient.UnschedulePendingSubscriptionCancellation(ctx, billingInfo.OrbSubscriptionID, nil)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("user_id", user.Id).Str("subscription_id", billingInfo.OrbSubscriptionID).Msg("Failed to unschedule pending subscription cancellation")
		return nil, status.Error(codes.Internal, "Failed to unschedule pending subscription cancellation")
	}

	// Schedule the plan change in Orb
	_, err = s.orbClient.SetCustomerPlanType(ctx, orbPlanChange, nil, false)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("user_id", user.Id).Str("target_plan_id", targetPlan.ID).Msg("Failed to schedule downgrade in Orb")
		return nil, status.Error(codes.Internal, fmt.Sprintf("Failed to schedule downgrade: %v", err))
	}

	s.WriteTeamsAuditLog(ctx, fmt.Sprintf("successfully scheduled downgrade for user %s to plan %s", user.Id, targetPlan.ID))

	return &authpb.HandleStagedChangeResponse{
		// No checkout session URL for downgrades
		PlanChangeType: authpb.HandleStagedChangeResponse_END_OF_BILLING_PERIOD,
	}, nil
}

// handleUpgradeCheckout creates a checkout session for upgrades
func (s *TeamManagementServer) handleUpgradeCheckout(
	ctx context.Context, user *auth_entities.User, billingInfo *UserBillingInfo, pendingChangeId *string, successURL string, cancelURL string,
) (*authpb.HandleStagedChangeResponse, error) {
	// Get the pending subscription change from Orb to get the payment amount
	if pendingChangeId == nil || *pendingChangeId == "" {
		log.Ctx(ctx).Error().Str("user_id", user.Id).Msg("No pending change ID provided for upgrade")
		return nil, status.Error(codes.FailedPrecondition, "No pending change ID provided")
	}

	pendingChange, err := s.orbClient.GetPendingSubscriptionChange(ctx, *pendingChangeId)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("pending_change_id", *pendingChangeId).Msg("Failed to get pending subscription change")
		return nil, status.Error(codes.Internal, "Failed to get pending subscription change")
	}

	// Get the plan name for the checkout session
	planConfig := s.orbConfig.GetPlan(pendingChange.PlanID)
	planName := ""
	if planConfig != nil {
		planName = planConfig.OrbPlan.Name
	}

	// Calculate checkout session expiration time: 1 hour before pending change expiration
	// This ensures we don't try to process a change that isn't pending anymore
	checkoutExpiresAt := pendingChange.ExpirationTime.Add(-1 * time.Hour)

	// Validate that the checkout session will live for at least 35 minutes
	// Stripe requires checkout sessions to have a minimum lifetime of 30 minutes, we add 5 minutes buffer
	minExpirationTime := time.Now().Add(35 * time.Minute)
	if checkoutExpiresAt.Before(minExpirationTime) {
		return nil, status.Errorf(codes.FailedPrecondition, "Cannot create checkout session: pending change expires too soon (checkout session would expire in less than 35 minutes)")
	}

	checkoutSession, err := s.stripeClient.CreateCheckoutSessionForPlanChange(
		billingInfo.StripeCustomerID,
		pendingChange.AmountDue,
		successURL,
		cancelURL,
		planName,
		user.Id,
		"plan_change",
		&checkoutExpiresAt,
	)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("user_id", user.Id).Msg("Failed to create checkout session")
		return nil, status.Error(codes.Internal, "Failed to create checkout session")
	}

	// Generate a unique plan change ID for the overall pending change
	planChangeID := uuid.New().String()

	// Create the pending change with checkout session data
	options := &PlanChangeOptions{
		CheckoutSessionUrl: &checkoutSession.URL,
		CheckoutSessionId:  &checkoutSession.ID,
		TotalCost:          &pendingChange.AmountDue,
		OrbPendingChangeId: pendingChangeId,
	}
	err = CreatePendingPlanChange(ctx, s.daoFactory, user, billingInfo, pendingChange.PlanID, planChangeID, options)
	if err != nil {
		return nil, err
	}

	s.WriteTeamsAuditLog(ctx, fmt.Sprintf("successfully created checkout session for user %s upgrade", user.Id))

	return &authpb.HandleStagedChangeResponse{
		CheckoutSessionUrl: &checkoutSession.URL,
		PlanChangeType:     authpb.HandleStagedChangeResponse_IMMEDIATE,
	}, nil
}

// TryPublishStagedChange checks if a checkout session payment succeeded and enqueues the staged change if it did
func (s *TeamManagementServer) TryPublishStagedChange(
	ctx context.Context, req *authpb.TryPublishStagedChangeRequest,
) (*authpb.TryPublishStagedChangeResponse, error) {
	// Validate request
	if req.UserId == "" {
		return nil, status.Error(codes.InvalidArgument, "user_id is required")
	}

	// Validate user and get billing info (includes auth check)
	billingInfo, err := s.validateUserAndGetBillingInfo(ctx, req.UserId, tokenscopesproto.Scope_AUTH_RW, true, "TryPublishStagedChange")
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to validate user and get billing info")
		return nil, err
	}

	// Check if there's a pending change
	if billingInfo.PendingChange == nil {
		return nil, status.Error(codes.NotFound, "No pending change found")
	}

	// Get the plan change from the pending change
	planChange := billingInfo.PendingChange.GetPlanChange()
	if planChange == nil {
		return nil, status.Error(codes.InvalidArgument, "Pending change is not a plan change")
	}

	// Get the checkout session ID from the pending change
	if planChange.CheckoutSessionId == nil || *planChange.CheckoutSessionId == "" {
		return nil, status.Error(codes.InvalidArgument, "No checkout session ID found in pending change")
	}
	checkoutSessionId := *planChange.CheckoutSessionId

	// Retrieve the checkout session from Stripe
	checkoutSession, err := s.stripeClient.GetCheckoutSession(checkoutSessionId)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("checkout_session_id", checkoutSessionId).Msg("Failed to retrieve checkout session")
		return nil, status.Error(codes.Internal, "Failed to retrieve checkout session")
	}

	// Verify the session belongs to this user
	if checkoutSession.Customer == nil || checkoutSession.Customer.ID != billingInfo.StripeCustomerID {
		log.Ctx(ctx).Error().
			Str("session_customer_id", func() string {
				if checkoutSession.Customer != nil {
					return checkoutSession.Customer.ID
				}
				return "nil"
			}()).
			Str("user_customer_id", billingInfo.StripeCustomerID).
			Str("user_id", req.UserId).
			Msg("Checkout session customer ID doesn't match user's customer ID")
		return nil, status.Error(codes.PermissionDenied, "Checkout session does not belong to this user")
	}

	// Check if the session is complete
	if checkoutSession.Status != "complete" {
		log.Ctx(ctx).Info().
			Str("checkout_session_id", checkoutSessionId).
			Str("session_status", string(checkoutSession.Status)).
			Str("user_id", req.UserId).
			Msg("Checkout session is not complete, not publishing staged change")
		return nil, status.Error(codes.FailedPrecondition, "Checkout session is not complete")
	}

	// Check the payment status to determine if we should publish the change
	paymentStatus := string(checkoutSession.PaymentStatus)
	switch paymentStatus {
	case "paid", "no_payment_required":
		// Payment succeeded or no payment required - proceed with publishing
		log.Ctx(ctx).Info().
			Str("checkout_session_id", checkoutSessionId).
			Str("payment_status", paymentStatus).
			Str("user_id", req.UserId).
			Msg("Payment successful, proceeding to publish staged change")
	case "unpaid":
		// Payment not completed - treat as no-op, don't publish the change
		log.Ctx(ctx).Info().
			Str("checkout_session_id", checkoutSessionId).
			Str("payment_status", paymentStatus).
			Str("user_id", req.UserId).
			Msg("Payment not completed, not publishing staged change")
		return &authpb.TryPublishStagedChangeResponse{}, nil
	default:
		// Unknown payment status - error out for safety
		log.Ctx(ctx).Error().
			Str("checkout_session_id", checkoutSessionId).
			Str("payment_status", paymentStatus).
			Str("user_id", req.UserId).
			Msg("Unknown payment status, cannot publish staged change")
		return nil, status.Error(codes.Internal, fmt.Sprintf("Unknown payment status: %s", paymentStatus))
	}

	// Generate a unique plan change ID for the processor
	planChangeID := uuid.New().String()

	// Extract the amount from the checkout session
	var collectedAmountCents *int64
	if checkoutSession.AmountTotal > 0 {
		collectedAmountCents = &checkoutSession.AmountTotal
	}

	// Publish plan change message for async processing
	planChangeMsg := &auth_internal.PlanChangeMessage{
		UserId:               req.UserId,
		PlanChangeId:         planChangeID,
		PublishTime:          timestamppb.Now(),
		TargetPlanId:         planChange.TargetPlanId,
		OrbPendingChangeId:   planChange.OrbPendingChangeId,
		CollectedAmountCents: collectedAmountCents,
	}

	err = s.asyncOpsPublisher.PublishPlanChange(ctx, planChangeMsg)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("user_id", req.UserId).Str("plan_change_id", planChangeID).Msg("Failed to publish plan change message")
		return nil, status.Error(codes.Internal, "Failed to enqueue plan change")
	}

	// Assign the message ID to the pending change if it's not already set
	err = AssignMessageIDToPendingChange(ctx, s.daoFactory, billingInfo, planChangeID)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("user_id", req.UserId).Str("message_id", planChangeID).Msg("Failed to assign message ID to pending change")
		// Don't fail the entire operation if we can't assign the message ID
	}

	s.WriteTeamsAuditLog(ctx, fmt.Sprintf("successfully published plan change for user %s after checkout session completion", req.UserId))

	log.Ctx(ctx).Info().
		Str("user_id", req.UserId).
		Str("checkout_session_id", checkoutSessionId).
		Str("plan_change_id", planChangeID).
		Str("target_plan_id", planChange.TargetPlanId).
		Msg("Successfully published plan change after checkout session completion")

	return &authpb.TryPublishStagedChangeResponse{}, nil
}

// GetPendingChange retrieves the pending change for a user or team
func (s *TeamManagementServer) GetPendingChange(
	ctx context.Context, req *authpb.GetPendingChangeRequest,
) (*authpb.GetPendingChangeResponse, error) {
	enabled, err := pendingChangesEnabled.Get(s.featureFlagHandle)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to get pending changes enabled feature flag")
		return nil, status.Error(codes.Internal, "Failed to get pending changes enabled feature flag")
	}
	if !enabled {
		log.Ctx(ctx).Warn().Msg("Pending changes are not enabled")
		return nil, status.Error(codes.Unimplemented, "Pending changes are not enabled")
	}

	billingInfo, err := s.validateUserAndGetBillingInfo(ctx, req.UserId, tokenscopesproto.Scope_AUTH_R, false, "GetPendingChange")
	if err != nil {
		return nil, err
	}

	return &authpb.GetPendingChangeResponse{
		PendingChange: billingInfo.PendingChange,
	}, nil
}
