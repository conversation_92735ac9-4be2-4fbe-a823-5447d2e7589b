package main

// we use readable constants in our tests to make debugging easier

const (
	// Test infrastructure constants
	instanceID      = "test-instance"
	tableName       = "test-table"
	projectID       = "test-project"
	tenantName      = "individual"
	tenantID        = "individual-2"
	tenantNamespace = "individual"

	// User IDs for testing
	authorizedUserID = "test-user"
	adminUserID      = "admin-user"
	nonAdminUserID   = "non-admin-user"
	invitedUserEmail = "<EMAIL>"

	// Tenant IDs for different types of tenants
	professionalTenantID            = "professional-tenant"
	professionalTenantID2           = "professional-tenant-id2"
	communityTenantID               = "community-tenant"
	testCommunityTenantID           = "test-community-tenant-id"
	enterpriseTenantID              = "enterprise-tenant"
	selfServeTeamTenantID           = "self-serve-team-tenant" // Used by team_management_test.go
	selfServeTeamTenantID2          = "self-serve-team-tenant-2"
	testSelfServeTeamTenantID       = "self-serve-team"
	selfServeTenantID               = "self-serve"      // Used by subscription_creation_test.go
	invitationSelfServeTeamTenantID = "self-serve-team" // Used by invitation_resolution_test.go
	vanguardTenantID                = "vanguard0"
	discoveryTenantID               = "discovery0"
	deletedTenantID                 = "deleted-tenant-id"

	// Enterprise domain
	enterpriseDomain = "enterprise-domain.com"

	// Subscription and billing constants
	noChangeSubscriptionId = "no-change-id"
	subscriptionId         = "test-subscription-id"
	subscriptionChangeId   = "test-change-id"
	messagesPerSeat        = 100
	maxTrialSeats          = 5

	// Orb Plan IDs
	orbTrialPlanID        = "orb_trial_plan"
	orbProfessionalPlanID = "orb_developer_plan"
	orbCommunityPlanID    = "orb_community_plan"
	orbMaxPlanID          = "orb_max_plan"
	trialPlanId           = "orb_trial_plan" // Alternative naming for compatibility

	// Orb Price IDs
	orbTrialPlanSeatPriceID          = "orb-trial-plan-seat-price-id"
	orbTrialPlanCreditPriceID        = "orb-trial-plan-credit-price-id"
	orbProfessionalPlanSeatPriceID   = "orb-developer-plan-seat-price-id"
	orbProfessionalPlanCreditPriceID = "orb-developer-plan-credit-price-id"
	orbCommunityPlanSeatPriceID      = "orb-community-plan-seat-price-id"
	orbCommunityPlanCreditPriceID    = "orb-community-plan-credit-price-id"
	orbMaxPlanSeatPriceID            = "orb-max-plan-seat-price-id"
	orbMaxPlanCreditPriceID          = "orb-max-plan-credit-price-id"
	seatPriceId                      = "seat-price-id"
	creditPriceId                    = "credit-price-id"

	// Orb Plan Credits
	orbProfessionalPlanMonthlyCredits = 600
	orbCommunityPlanMonthlyCredits    = 50
	orbTrialPlanCredits               = 600

	// Orb Currency
	orbCurrency = "usermessages"
)
