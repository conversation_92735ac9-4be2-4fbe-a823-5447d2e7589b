"""Global authorization service."""

import argparse
import base64
import copy
import functools
import hmac
import json
import logging
import os
import pathlib
import re
import secrets
import sys
import threading
from dataclasses import dataclass
from datetime import datetime, timezone
from enum import Enum
from hashlib import sha256
from pathlib import Path
from typing import Any, Callable, Protocol, Sequence, cast
from urllib.parse import parse_qsl, urlencode, urlparse

import dataclasses_json
import flask
import grpc
import prometheus_client
import pydantic
import requests
import segment.analytics as analytics
from authlib.integrations.flask_client import OAuth, OAuthError
from dataclasses_json import dataclass_json
from google.cloud import recaptchaenterprise_v1
from gunicorn.app.base import BaseApplication
from prometheus_flask_exporter.multiprocess import GunicornPrometheusMetrics
from pydantic import SecretStr

import base.feature_flags
import base.tracing
import services.lib.grpc.tls_config.tls_config as tls_config
import services.request_insight.publisher.request_insight_publisher as request_insight_publisher
import services.tenant_watcher.tenant_watcher_pb2 as tenant_watcher_pb2
from base.logging.audit import audit
from base.logging.struct_logging import setup_struct_logging
import base.logging.audit_ocsf.python as audit_ocsf
from services.auth.central.server import (
    auth_entities_pb2,
    auth_pb2,
    front_end_token_service_pb2,
    front_end_token_service_pb2_grpc,
    invitation_service,
)
from services.auth.central.server.auth_entities_pb2 import (
    User,
    UserSuspensionType,
)
from services.auth.central.server.config import (
    Auth0ActionEndpointConfig,
    Config,
)
from services.auth.central.server.tenant_map import TenantDetails, TenantMap
from services.tenant_watcher.client.client import TenantsClient
from services.tenant_watcher.client.tenant_cache import KILL_PID_ON_EXIT_ENV_VAR_NAME


_response_counter = prometheus_client.Counter(
    "au_auth_central_response",
    "Counter of auth central responses",
    ["method", "status"],
)

_signup_callbacks_counter = prometheus_client.Counter(
    "au_auth_central_signup_callback",
    "Counter of auth central signup callbacks",
    ["signup_type", "status"],
)

_login_redirect_uri_counter = prometheus_client.Counter(
    "au_auth_central_login_redirect_uri",
    "Counter of auth central login redirect uris",
    ["uri"],
)

_invitation_counter = prometheus_client.Counter(
    "au_auth_central_invitation",
    "Counter of auth central invitation operations",
    ["operation", "status"],
)

_x_forwarded_for_fail_counter = prometheus_client.Counter(
    "au_auth_central_x_forwarded_for_fails",
    "Counter of auth central x-forwarded-for failures",
)

_captcha_histogram = prometheus_client.Histogram(
    "au_auth_central_captcha_score",
    "Histogram of captcha scores",
    ["operation", "status"],
    buckets=[0.0, 0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1.0],
)

_errors_counter = prometheus_client.Counter(
    "au_auth_central_errors",
    "Counter of auth central errors",
    ["error_type", "detail"],
)

_ops_counter = prometheus_client.Counter(
    "au_auth_central_ops",
    "Counter of auth central operations",
    ["operation", "status"],
)

_verdicts_to_auth0 = prometheus_client.Counter(
    "au_auth_central_verdicts_to_auth0",
    "Counter of auth central verdicts sent to Auth0",
    ["verdict"],
)

_SIGNUP_TENANT = base.feature_flags.StringFlag("auth_central_signup_tenant", "")
_INDIVIDUAL_TENANT = base.feature_flags.StringFlag("auth_central_individual_tenant", "")

_SIGNUP_DONE_REDIRECT = base.feature_flags.StringFlag(
    "auth_central_signup_done_redirect", "/echo"
)
_INDIVIDUAL_SIGNUP_DONE_REDIRECT = base.feature_flags.StringFlag(
    "auth_central_individual_redirect", "/echo"
)
_LOGIN_INVITATIONS_ENABLED = base.feature_flags.BoolFlag(
    "auth_central_login_invitations_enabled", True
)

_REACT_FRONTEND_ENABLED = base.feature_flags.BoolFlag(
    "auth_central_react_frontend", False
)

_RECAPTCHA_THRESHOLD = base.feature_flags.FloatFlag(
    "auth_central_recaptcha_threshold", 0.0
)

_ALLOW_SIMILAR_SIGNUPS = base.feature_flags.BoolFlag(
    "auth_central_allow_similar_signups", False
)

_SIMILAR_SIGNUPS_WHITELIST_DOMAINS = base.feature_flags.StringFlag(
    "auth_central_similar_signups_whitelist_domains", ""
)

_VERISOUL_ENABLED = base.feature_flags.BoolFlag("auth_central_verisoul_enabled", False)

_VEROSINT_FINGERPRINTING = base.feature_flags.BoolFlag(
    "auth_central_verosint_fingerprinting", False
)

_VERISOUL_FAIL_ON_INVALID = base.feature_flags.IntFlag(
    "auth_central_verisoul_fail_on_invalid", 3
)

_VERISOUL_MULTIPLE_USER_THRESHOLD = base.feature_flags.IntFlag(
    "auth_central_verisoul_multiple_user_threshold", 0
)

_HCAPTCHA_COLLECT = base.feature_flags.BoolFlag("auth_central_hcaptcha_collect", False)

_BEARER_TOKEN_AUTH_ENABLED = base.feature_flags.BoolFlag(
    "auth_central_bearer_token_auth_enabled", False
)
_IMPLICIT_TOS_ACCEPTANCE = base.feature_flags.BoolFlag(
    "auth_central_implicit_tos_acceptance", False
)

_BLOCKED_COUNTRIES = base.feature_flags.StringFlag("auth_central_blocked_countries", "")

_DISABLE_SECOND_SIGNUP_FLOW = base.feature_flags.BoolFlag(
    "auth_central_disable_second_signup_flow", False
)


def parse_blocked_countries(countries: str) -> list[str]:
    return [country.strip() for country in countries.split(",") if country.strip()]


class InvalidEmailError(Exception):
    """Exception raised when an invalid email address is encountered."""

    pass


class SignupStatus(Enum):
    """Possible return values of do_signup."""

    SIGNUP_LIMIT_REACHED = "signup-limit-reached"
    ALREADY_SIGNED_UP = "already-signed-up"
    IN_ANOTHER_TENANT = "in-another-tenant"
    YOUR_ORG_HAS_AUGMENT = "your-org-has-augment"
    INTERNAL_ERROR = "internal-error"
    INVALID_EMAIL = "invalid-email"


def _token_hash(token: str) -> str:
    return sha256(token.encode("utf-8")).hexdigest()


def _base64_url_encode(data: bytes) -> str:
    """Base64-URL-encode the given data."""
    return base64.urlsafe_b64encode(data).decode("utf-8").replace("=", "")


def _generate_code_challenge(code_verifier: str) -> str:
    return _base64_url_encode(sha256(code_verifier.encode("utf-8")).digest())


def _decode_hmac_bearer_token(token: str, secret: str) -> dict | None:
    """
    Decode an HMAC-SHA256 signed bearer token.

    Args:
        token: The bearer token to decode
        secret: The HMAC secret key

    Returns:
        The decoded payload as a dictionary, or None if invalid
    """
    try:
        # Split the token into payload and signature
        if "." not in token:
            logging.warning("Invalid token format: no signature separator")
            return None

        payload_b64, signature_b64 = token.rsplit(".", 1)

        # Decode the payload
        try:
            # Add padding if needed for base64 decoding
            payload_b64_padded = payload_b64 + "=" * (4 - len(payload_b64) % 4)
            payload_bytes = base64.urlsafe_b64decode(payload_b64_padded)
            payload = json.loads(payload_bytes.decode("utf-8"))
        except (ValueError, json.JSONDecodeError) as e:
            logging.warning("Failed to decode token payload: %s", e)
            return None

        # Verify the HMAC signature
        expected_signature = hmac.new(
            secret.encode("utf-8"), payload_b64.encode("utf-8"), sha256
        ).digest()
        expected_signature_b64 = (
            base64.urlsafe_b64encode(expected_signature).decode("utf-8").rstrip("=")
        )

        # Decode the provided signature
        try:
            signature_b64_padded = signature_b64 + "=" * (4 - len(signature_b64) % 4)
            provided_signature = base64.urlsafe_b64decode(signature_b64_padded)
            provided_signature_b64 = (
                base64.urlsafe_b64encode(provided_signature).decode("utf-8").rstrip("=")
            )
        except ValueError as e:
            logging.warning("Failed to decode token signature: %s", e)
            return None

        # Compare signatures using constant-time comparison
        if not hmac.compare_digest(expected_signature_b64, provided_signature_b64):
            logging.warning("HMAC signature verification failed")
            return None

        return payload

    except Exception as e:
        logging.error("Error decoding HMAC bearer token: %s", e)
        return None


def decode_bearer_token(config: Any, auth_header: str | None) -> dict | None:
    """
    Decode test user information from HMAC-signed bearer token in Authorization header.

    Args:
        config: Application configuration containing e2e secret path
        auth_header: Authorization bearer token

    Returns:
        Decoded payload dictionary if valid token found, None otherwise
    """
    # Read the e2e secret
    e2e_secret = None
    if config.e2e_customerui_secret_path:
        try:
            e2e_secret = (
                pathlib.Path(config.e2e_customerui_secret_path)
                .read_text(encoding="utf-8")
                .strip()
            )
        except Exception as e:
            logging.error("Failed to read e2e customer UI secret: %s", e)
            return None
    else:
        logging.info("E2E Customer UI secret path not configured")
        return None

    # Check for Authorization header with HMAC-signed bearer token
    if auth_header and auth_header.startswith("Bearer ") and e2e_secret:
        bearer_token = auth_header[7:]  # Remove 'Bearer ' prefix
        logging.info("Found Authorization header with bearer token")

        # Decode the HMAC-signed token
        decoded_payload = _decode_hmac_bearer_token(bearer_token, e2e_secret)
        if decoded_payload:
            logging.info("E2E Token decoded successfully: %s", decoded_payload)

            # Validate that the user email is in the @augm.io domain
            user_email = decoded_payload.get("email")
            if user_email and not user_email.endswith("@augm.io"):
                logging.error(
                    "Test user email domain validation failed: %s is not in @augm.io domain",
                    user_email,
                )
                return None

            return decoded_payload
        else:
            logging.warning("Failed to decode E2E bearer token")
    elif auth_header and auth_header.startswith("Bearer "):
        logging.info("Authorization header found but e2e secret not available")
    else:
        logging.debug("No Authorization header with bearer token found")

    return None


def get_candidate_signup_tenant_list(config: Config, is_individual: bool):
    if is_individual:
        tenant_names_str = _INDIVIDUAL_TENANT.get(
            base.feature_flags.get_global_context()
        )
        backup_tenant = config.individual_tenant
    else:
        tenant_names_str = _SIGNUP_TENANT.get(base.feature_flags.get_global_context())
        backup_tenant = config.signup_tenant

    # Handle empty string case
    if not tenant_names_str:
        if backup_tenant is None:
            return []
        return [backup_tenant]

    # Split and filter out empty strings
    tenant_names = [name for name in tenant_names_str.split(",") if name]

    if not tenant_names:
        if backup_tenant is None:
            return []
        return [backup_tenant]

    return tenant_names


def get_signup_tenant(tenant_names: list[str], idp_user_id: str):
    # HACK
    #
    # This is a hack to deterministically select the tenant the user will be placed in
    # to ensure we don't accidently put the user in multiple tenants if this code executes
    # multiple times concurrently.
    #
    # Some might consider this to be "clever" but this is what Google's Engineering book
    # says about "clever" solutions:
    #
    # > We’ve taken to saying, “It’s programming if 'clever' is a compliment,
    # > but it’s software engineering if 'clever' is an accusation.”
    #
    # Create a hash of the idp_user_id so we have a nice distribution of tenants
    # and use it to select a tenant
    hash_value = int(sha256(idp_user_id.encode("utf-8")).hexdigest(), 16)
    tenant_index = hash_value % len(tenant_names)
    tenant_name = tenant_names[tenant_index]
    logging.info(
        "Deterministic signup tenant: %s. Chosen from list: %s based on idp_user_id.",
        tenant_name,
        tenant_names,
    )

    return tenant_name


def check_if_uri_differs_only_by_scheme(
    redirect_uris: Sequence[str], uri: str
) -> str | None:
    """Check if the uri differs only by scheme from any of the redirect uris.

    Returns the scheme name if it does, None otherwise.
    """
    split_uri = uri.split("://", 1)
    if len(split_uri) != 2:
        return None

    for redirect_uri in redirect_uris:
        if redirect_uri.endswith("://" + split_uri[1]):
            return split_uri[0]
    return None


def _find_arg_safe(args: list[tuple[str, str]], arg_name: str):
    """
    Return the argument only if it exists once in the list of arguments.

    Returns None if the argument is not found.

    Raises RuntimeError if the argument is found multiple times.
    """
    found = [arg[1] for arg in args if arg[0] == arg_name]
    if len(found) == 1:
        return found[0]
    elif len(found) > 1:
        raise RuntimeError("Multiple values for {arg_name}")
    else:
        return None


def setup_segment(config: Config):
    """Initialize Segment analytics with configuration."""
    if config.segment and config.segment.enabled:
        analytics.write_key = config.segment.write_key
        analytics.host = config.segment.host


def identify_user(user_id: str, traits: dict, anonymous_id: str | None = None):
    """Identify user in Segment with their traits."""
    if not analytics.write_key:
        return

    try:
        # Remove None values
        traits = {k: v for k, v in traits.items() if v is not None}

        if anonymous_id:
            try:
                # https://segment.com/docs/connections/sources/catalog/libraries/server/python/#alias
                analytics.alias(anonymous_id, user_id)
            except Exception as e:
                logging.exception("Failed to alias user in Segment: %s", e)
        else:
            logging.info(
                "No anonymous ID provided for user %s - skipping alias", user_id
            )

        # Then identify the user with their traits
        analytics.identify(user_id, anonymous_id=anonymous_id, traits=traits)
    except Exception as e:
        logging.exception("Failed to identify user in Segment: %s", e)


def track_event(user_id: str, event_name: str, anonymous_id: str | None = None):
    """Track authentication events in Segment."""
    if not analytics.write_key:
        return

    try:
        analytics.track(user_id, event_name, anonymous_id=anonymous_id)
    except Exception as e:
        logging.exception("Failed to track auth event in Segment: %s", e)


def parse_x_forwarded_for(header: str) -> str:
    """Parse X-Forwarded-For header and return the penultimate IP address.

    The penultimate IP address is the IP address of the client that made the
    request. The last IP address is the IP address of the load balancer.
    Verified this in my dev deploy.

    Other IP addresses are not to be trusted.

    If we add or change proxies in the future, we may need to update this logic.
    """
    ips = header.split(",")
    if len(ips) < 2:
        logging.error(
            "X-Forwarded-For header has fewer than 2 IP addresses: %s", header
        )
        _x_forwarded_for_fail_counter.inc()
        return ""
    return ips[-2].strip()


class CheckerResult(Enum):
    ALLOWED = 1
    BLOCKED = 2
    REGION_BLOCKED = 3


class CaptchaChecker(Protocol):
    def allow(
        self,
        token: str,
        recaptcha_action: str,
        user_ip_address: str,
        user_agent: str,
        threshold: float,
        user_email: str | None = None,
    ) -> CheckerResult: ...

    def site_key(self) -> str | None: ...


class RecaptchaChecker(CaptchaChecker):
    def __init__(
        self,
        project_id: str,
        site_key: str,
        ri_publisher: request_insight_publisher.RequestInsightPublisher | None = None,
    ):
        self._project_id = project_id
        self._site_key = site_key
        self._ri_publisher = ri_publisher

    def create_assessment(
        self,
        token: str,
        recaptcha_action: str,
        user_ip_address: str,
        user_agent: str,
        user_email: str | None = None,
    ) -> float | None:
        """Create an assessment to analyze the risk of a UI action.
        Args:
            project_id: GCloud Project ID
            token: The token obtained from the client on passing the recaptchaSiteKey.
            recaptcha_action: Action name corresponding to the token.
            user_ip_address: IP address of the user sending a request.
            user_agent: User agent is included in the HTTP request in the request header.
            user_email: Email of the user who made the request (if known)
        """

        client = recaptchaenterprise_v1.RecaptchaEnterpriseServiceClient()

        # Set the properties of the event to be tracked.
        event = recaptchaenterprise_v1.Event()
        event.site_key = self._site_key
        event.token = token or "bogus-token"
        event.user_ip_address = user_ip_address
        event.user_agent = user_agent
        if user_email:
            event.user_info = recaptchaenterprise_v1.UserInfo()
            event.user_info.account_id = user_email

        assessment = recaptchaenterprise_v1.Assessment()
        assessment.event = event

        project_name = f"projects/{self._project_id}"

        # Build the assessment request.
        request = recaptchaenterprise_v1.CreateAssessmentRequest()
        request.assessment = assessment
        request.parent = project_name

        response = client.create_assessment(request)

        # Check if the token is valid.
        if not response.token_properties.valid:
            if token:
                logging.info(
                    "create_assessment failed because the token was "
                    + "invalid for for the following reasons: %s",
                    response.token_properties.invalid_reason,
                )
            else:
                logging.info("create_assessment failed because the token was empty")
            return None

        # Check if the expected action was executed.
        if response.token_properties.action != recaptcha_action:
            logging.info(
                "create_assessment action mismatch response=%s action=%s",
                response.token_properties.action,
                recaptcha_action,
            )
            return None

        assessment_name = client.parse_assessment_path(response.name).get("assessment")

        logging.info(
            "create_assessment email: %s name: %s score: %s reasons: %s",
            user_email,
            assessment_name,
            response.risk_analysis.score,
            list(response.risk_analysis.reasons),
        )

        event = request_insight_publisher.new_generic_event()
        recaptcha_event = event.recaptcha
        if user_email is not None:
            recaptcha_event.email = user_email
        recaptcha_event.assessment_name = assessment_name or ""
        recaptcha_event.assessment_reasons.extend(
            [str(r) for r in response.risk_analysis.reasons]
        )
        recaptcha_event.score = response.risk_analysis.score
        recaptcha_event.action = recaptcha_action
        recaptcha_event.user_agent = user_agent
        recaptcha_event.source_ip = user_ip_address

        if self._ri_publisher is not None:
            self._ri_publisher.publish_generic_events(events=[event])

        return response.risk_analysis.score

    def allow(
        self,
        token: str,
        recaptcha_action: str,
        user_ip_address: str,
        user_agent: str,
        threshold: float,
        user_email: str | None = None,
    ) -> CheckerResult:
        if threshold < 0.0:
            return CheckerResult.ALLOWED

        try:
            score = self.create_assessment(
                token, recaptcha_action, user_ip_address, user_agent, user_email
            )
        except Exception as e:
            logging.exception("create_assessment failed: %s", e)
            # Fail open if we can't contact recaptcha
            _captcha_histogram.labels(recaptcha_action, "failed").observe(1.0)
            return CheckerResult.ALLOWED

        # Fail closed unless threshold == 0.0 which means we just want to log.
        if score is None:
            _captcha_histogram.labels(recaptcha_action, "failed").observe(0.0)
            return CheckerResult.ALLOWED if threshold == 0.0 else CheckerResult.BLOCKED

        _captcha_histogram.labels(recaptcha_action, "success").observe(score)
        return CheckerResult.ALLOWED if score >= threshold else CheckerResult.BLOCKED

    def site_key(self) -> str | None:
        return self._site_key


class NullRecaptchaChecker(CaptchaChecker):
    def allow(
        self,
        token: str,
        recaptcha_action: str,
        user_ip_address: str,
        user_agent: str,
        threshold: float,
        user_email: str | None = None,
    ) -> CheckerResult:
        return CheckerResult.ALLOWED

    def site_key(self) -> str | None:
        return None


class HttpResponse:
    def __init__(self, status_code: int, body: str):
        self.status_code = status_code
        self.body = body


class HttpClient:
    def post(
        self, url: str, headers: dict[str, str], json: Any, timeout: float
    ) -> HttpResponse:
        response = requests.post(url, headers=headers, json=json, timeout=timeout)
        return HttpResponse(response.status_code, response.text)


class VerisoulChecker:
    def __init__(
        self,
        api_key: pydantic.SecretStr,
        env: str,
        http_client: HttpClient | None = None,
        ri_publisher: request_insight_publisher.RequestInsightPublisher | None = None,
    ):
        self._api_key = api_key
        self._env = env
        self._ri_publisher = ri_publisher
        self._http_client = http_client or HttpClient()

    def allow(
        self,
        session_id: str,
        user_id: str | None,
        idp_user_id: str,
        user_email: str,
        timeout: float = 3.0,
    ) -> CheckerResult:
        if not _VERISOUL_ENABLED.get(base.feature_flags.get_global_context()):
            return CheckerResult.ALLOWED

        account: dict[str, Any] = {
            "id": idp_user_id,
            "email": user_email,
        }

        if (
            not session_id
            and _VERISOUL_FAIL_ON_INVALID.get(base.feature_flags.get_global_context())
            & 1
        ):
            return CheckerResult.BLOCKED

        logging.info("Verisoul check user id %s", idp_user_id)

        try:
            raw_response = self._http_client.post(
                url=f"https://api.{self._env}.verisoul.ai/session/authenticate?accounts_linked=true",
                headers={"x-api-key": self._api_key.get_secret_value()},
                json={"session_id": session_id, "account": account},
                timeout=timeout,
            )

            if raw_response.status_code != 200:
                logging.warning(
                    "Verisoul check %s api key %s session id %s failed with status code %d: %s",
                    idp_user_id,
                    self._api_key.get_secret_value()[0:3],
                    session_id,
                    raw_response.status_code,
                    raw_response.body,
                )

                # Parse the error response to understand why it failed
                if raw_response.status_code == 400:
                    try:
                        error_response = json.loads(raw_response.body)
                        error_message = error_response.get("message", "")

                        if error_message == "Session ID has expired.":
                            _errors_counter.labels("verisoul_session", "expired").inc()
                        else:
                            _errors_counter.labels("verisoul_session", "invalid").inc()

                        if (
                            _VERISOUL_FAIL_ON_INVALID.get(
                                base.feature_flags.get_global_context()
                            )
                            & 2
                        ):
                            logging.warning(
                                "Verisoul check %s session id %s failed with status code %d: %s",
                                idp_user_id,
                                session_id,
                                raw_response.status_code,
                                raw_response.body,
                            )
                            return CheckerResult.BLOCKED
                    except json.JSONDecodeError:
                        _errors_counter.labels("verisoul_session", "parse_error").inc()
                _errors_counter.labels(
                    "verisoul_http_request", str(raw_response.status_code)
                ).inc()

                # Fail open for now - 400 can mean an invalid session ID which could
                # be a sign of user tampering
                return CheckerResult.ALLOWED
        except Exception as e:
            logging.warning(
                "Verisoul check %s session id %s failed with exception: %s",
                idp_user_id,
                session_id,
                e,
            )

            # Categorize different types of exceptions for better monitoring
            if "Read timed out" in str(e):
                error_type = "read_timeout_exception"
            elif "Connection refused" in str(e):
                error_type = "connection_refused_exception"
            elif "timeout" in str(e).lower():
                error_type = "timeout_exception"
            else:
                error_type = "unknown_exception"
            _errors_counter.labels("verisoul", error_type).inc()
            # Fail open if we failed to contact Verisoul
            return CheckerResult.ALLOWED

        try:
            report = json.loads(raw_response.body)
            if not isinstance(report, dict):
                raise ValueError("Verisoul response is not a dictionary")
        except Exception as e:
            logging.warning(
                "Verisoul check %s session id %s failed to parse response: %s %s",
                user_email,
                session_id,
                e,
                raw_response.body,
            )
            _errors_counter.labels("verisoul", "parse_response").inc()
            # Fail open in this case
            return CheckerResult.ALLOWED

        try:
            event = request_insight_publisher.new_generic_event()

            if user_id is not None:
                event.verisoul.opaque_user_id.user_id = user_id
                event.verisoul.opaque_user_id.user_id_type = (
                    auth_entities_pb2.UserId.UserIdType.AUGMENT
                )
            event.verisoul.report = raw_response.body

            if self._ri_publisher is not None:
                self._ri_publisher.publish_generic_events(events=[event])
                _ops_counter.labels("verisoul", "ri_publish").inc()

        except Exception as e:
            logging.warning(
                "Verisoul check %s session id %s failed to publish to request insight: %s",
                idp_user_id,
                session_id,
                e,
            )
            _errors_counter.labels("verisoul", "ri_publish").inc()

        threshold = _VERISOUL_MULTIPLE_USER_THRESHOLD.get(
            base.feature_flags.get_global_context()
        )
        if threshold > 0:
            accounts_linked = report.get("accounts_linked", -1)
            if accounts_linked == -1:
                _errors_counter.labels("verisoul", "missing_accounts_linked").inc()
            elif not isinstance(accounts_linked, (int, float)):
                _errors_counter.labels("verisoul", "invalid_accounts_linked").inc()
            elif accounts_linked >= threshold:
                logging.info(
                    "User %s has %d accounts linked, above threshold of %d",
                    idp_user_id,
                    accounts_linked,
                    threshold,
                )
                _errors_counter.labels("verisoul", "accounts_linked").inc()
                return CheckerResult.BLOCKED

        blocked_countries = parse_blocked_countries(
            _BLOCKED_COUNTRIES.get(base.feature_flags.get_global_context())
        )

        country = report.get("account", {}).get("country", "")
        if country in blocked_countries:
            logging.info(
                "User %s is in blocked country %s, blocking", idp_user_id, country
            )
            _errors_counter.labels("verisoul", "blocked_country").inc()
            return CheckerResult.REGION_BLOCKED

        true_country = report.get("session", {}).get("true_country_code", "")
        if true_country in blocked_countries:
            logging.info(
                "User %s is in blocked country %s, blocking", idp_user_id, true_country
            )
            _errors_counter.labels("verisoul", "blocked_true_country").inc()
            return CheckerResult.REGION_BLOCKED

        _ops_counter.labels("verisoul", "success").inc()
        logging.info("Verisoul check success %s", idp_user_id)
        return CheckerResult.ALLOWED


def oauth_error_to_user_message(
    library_error: str,
    library_description: str | None,
    error: str | None,
    description: str | None,
) -> tuple[str, str]:
    message = "We encountered an unexpected error."
    details = f"Details: error: {library_error} description: {library_description} time:{datetime.now(timezone.utc)}"

    if error is not None:
        details = f"Details: error: {error} description: {description} time:{datetime.now(timezone.utc)}"

        if error == "access_denied":
            message = "Sign in failed: the system that you used to sign in refused the sign in."

            if description is not None:
                if description.startswith("[DENY]"):
                    message = "Sign up failed: your login did not pass checks."
                    details = f"time:{datetime.now(timezone.utc)}"
                elif "not assigned" in description:
                    message = "Sign in failed: you have not been assigned access to Augment in your organization's sign in system."
                    details = f"time:{datetime.now(timezone.utc)}"

    return message, details


@dataclass_json
@dataclass(frozen=True)
class Auth0ActionRequest(dataclasses_json.DataClassJsonMixin):
    event_type: str
    event: dict[str, Any]


class Auth0ActionEndpoint:
    def __init__(
        self,
        config: Auth0ActionEndpointConfig,
        ri_publisher: request_insight_publisher.RequestInsightPublisher | None,
        front_end_token_service: front_end_token_service_pb2_grpc.FrontEndTokenServiceStub,
    ):
        self._api_key = pydantic.SecretStr(
            pathlib.Path(config.api_key_path).read_text(encoding="utf-8").strip()
        )
        self._ri_publisher = ri_publisher
        self._front_end_token_service = front_end_token_service

    def verify_request(self, request: flask.Request) -> bool:
        return (
            request.headers.get("Authorization", "")
            == f"Bearer {self._api_key.get_secret_value()}"
        )

    def handle_request(self) -> tuple[flask.Response, int]:
        request = flask.request

        if not self.verify_request(request):
            return flask.jsonify({"error": "Unauthorized"}), 401

        try:
            auth0_action_request = Auth0ActionRequest.from_dict(request.get_json())
        except Exception as e:
            logging.error(f"Failed to parse Auth0 action request: {e}")
            return flask.jsonify({"error": "Bad request"}), 400

        if self._ri_publisher is not None:
            event = request_insight_publisher.new_generic_event()
            event.auth0.event_type = auth0_action_request.event_type
            event.auth0.event_json = json.dumps(auth0_action_request.event)
            self._ri_publisher.publish_generic_events(events=[event])

        email_address = auth0_action_request.event.get("user", {}).get("email", "")
        user_agent = auth0_action_request.event.get("request", {}).get("user_agent", "")
        ip_address = auth0_action_request.event.get("request", {}).get("ip", "")

        if not is_valid_email(email_address):
            _verdicts_to_auth0.labels("block_email").inc()
            return flask.jsonify({"verdict": "block"}), 200

        if not user_agent or not ip_address:
            _verdicts_to_auth0.labels("allow_no_user_agent_or_ip").inc()
            return flask.jsonify({"verdict": "allow"}), 200

        try:
            response = self._front_end_token_service.RunUserSecurityChecks(
                front_end_token_service_pb2.RunUserSecurityChecksRequest(
                    email_address=email_address,
                    user_agent=user_agent,
                    ip_address=ip_address,
                    sign_up=True,
                )
            )
            if not response.passed:
                _verdicts_to_auth0.labels("block").inc()
                return flask.jsonify({"verdict": "block"}), 200
        except grpc.RpcError as e:
            logging.error("Failed to run user security checks: %s", e)

        _verdicts_to_auth0.labels("allow").inc()
        return flask.jsonify({"verdict": "allow"}), 200


def create_app(
    config: Config,
    secret_key: SecretStr,
    prometheus: bool = True,
    disable_secure_cookie_for_testing: bool = False,
    captcha_checker_inject: CaptchaChecker | None = None,
):
    app = flask.Flask(__name__)

    # Create gRPC channel to the backend
    backend_channel = grpc.insecure_channel(f"localhost:{config.backend_port}")
    front_end_token_service = front_end_token_service_pb2_grpc.FrontEndTokenServiceStub(
        backend_channel
    )

    ri_publisher = (
        request_insight_publisher.RequestInsightPublisher.create_from_file(
            pathlib.Path(config.request_insight_publisher_config_path)
        )
        if config.request_insight_publisher_config_path
        else None
    )

    # Load reCAPTCHA keys from secret manager if configured
    if captcha_checker_inject is not None:
        captcha_checker = captcha_checker_inject
    else:
        captcha_checker = NullRecaptchaChecker()

        if config.recaptcha_config is not None:
            try:
                recaptcha_json = pathlib.Path(
                    config.recaptcha_config.keys_path
                ).read_text(encoding="utf-8")
                recaptcha_config = json.loads(recaptcha_json)
                site_key = recaptcha_config.get("site_key", "")

                logging.info(
                    "Initializing reCAPTCHA checker with site key starting with %s",
                    site_key[0:10],
                )
                captcha_checker = RecaptchaChecker(
                    config.recaptcha_config.project_id, site_key, ri_publisher
                )
            except Exception as e:
                logging.error(f"Failed to load reCAPTCHA keys: {e}")

    verisoul = config.verisoul
    verisoul_checker: VerisoulChecker | None = None
    if verisoul is not None:
        logging.info("Initializing Verisoul with project id %s", verisoul.project_id)
        verisoul_api_key = pydantic.SecretStr(
            pathlib.Path(verisoul.api_key_path).read_text(encoding="utf-8").strip()
        )

        verisoul_checker = VerisoulChecker(
            verisoul_api_key, verisoul.env, ri_publisher=ri_publisher
        )

    invitation_service.initialize(front_end_token_service)

    app.config.update(
        # Used for secure session cookies
        SECRET_KEY=secret_key.get_secret_value(),
        SESSION_COOKIE_SECURE=True,
        SESSION_COOKIE_HTTPONLY=True,
        SESSION_COOKIE_SAMESITE="Lax",
        augment_override_authorize_access_token=None,
        # PERMANENT_SESSION_LIFETIME=2678400  # 31 days in seconds
    )

    if disable_secure_cookie_for_testing:
        logging.warning("DISABLING SECURE COOKIE FOR INTERNAL TESTING")
        app.config.update(
            SESSION_COOKIE_SECURE=False,
        )

    # Initialize Auth0
    auth0_oauth = OAuth(app)

    # Initialize Segment
    setup_segment(config)

    # Register the login app Auth0 client
    if config.login_auth0 is not None:
        client_id, client_secret = map(
            lambda x: x.strip(),
            pathlib.Path(config.login_auth0.credentials_path)
            .read_text(encoding="utf-8")
            .splitlines()[0:2],
        )
        auth0_oauth.register(
            "auth0_login",
            client_id=client_id,
            client_secret=client_secret,
            client_kwargs={"scope": "openid email profile"},
            authorize_params={"prompt": "login"},
            server_metadata_url=config.login_auth0.server_metadata_url,
        )

    # Register the signup app Auth0 client
    if config.signup_auth0 is not None:
        client_id, client_secret = map(
            lambda x: x.strip(),
            pathlib.Path(config.signup_auth0.credentials_path)
            .read_text(encoding="utf-8")
            .splitlines()[0:2],
        )
        auth0_oauth.register(
            "auth0_signup",
            client_id=client_id,
            client_secret=client_secret,
            client_kwargs={"scope": "openid email profile"},
            authorize_params={"prompt": "login"},
            server_metadata_url=config.signup_auth0.server_metadata_url or None,
        )

    audit_logger = audit.AuditLogger()
    ocsf_audit_logger = audit_ocsf.new_default_ocsf_audit_logger()

    if prometheus:
        _ = GunicornPrometheusMetrics(app, port=9090, group_by="endpoint")

    client_credentials = tls_config.get_client_tls_creds(config.grpc.client_mtls)
    tenant_watcher_client = TenantsClient(
        config.tenant_watcher.tenant_watcher_endpoint, client_credentials
    )

    staging_user_email_match = (
        re.compile(config.staging_user_email_regex)
        if config.staging_user_email_regex
        else None
    )

    tenant_map = TenantMap(
        tenant_watcher_client=tenant_watcher_client,
        api_proxy_hostname_domain=config.tenant_watcher.api_proxy_hostname_domain,
    )

    tenant_map_loaded = threading.Event()  # using Event as thread-safe boolean

    dev_callback_url = config.dev_callback_url

    def clear_auth_session_state():
        # Clear our auth state but keep the authlib session state around so
        # we don't get strange CSRF errors
        flask.session.pop("user_id", None)
        flask.session.pop("idp_user_id", None)
        flask.session.pop("user_email", None)
        flask.session.pop("email", None)
        flask.session.pop("nonce", None)
        flask.session.pop("individual", None)
        flask.session.pop("in_usa", None)

    # Helper functions for API endpoints
    def error_response(
        message: str, status_code: int, **extra_info: Any
    ) -> flask.Response:
        """Create standardized error response.

        Args:
            message: Error message
            status_code: HTTP status code
            **extra_info: Additional fields for support_info

        Returns:
            Flask JSON response
        """
        support_info = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            **extra_info,
        }

        response = flask.jsonify(
            {
                "message": message,
                "support_info": support_info,
            }
        )
        response.status_code = status_code
        return response

    def extract_oauth_params(
        source: dict, param_source_name: str = "request"
    ) -> tuple[dict | None, flask.Response | None]:
        """Extract and validate OAuth parameters from request.

        Returns:
            Tuple of (params_dict, error_response)
            - params_dict: Dict with validated OAuth params if successful
            - error_response: Flask JSON response if validation fails
        """
        client_id = source.get("client_id")
        redirect_uri = source.get("redirect_uri")
        state = source.get("state")
        code_challenge = source.get("code_challenge")

        if not all([client_id, redirect_uri, state, code_challenge]):
            logging.warning(
                "Missing required OAuth parameters in %s: %s", param_source_name, source
            )
            return None, error_response("Missing required OAuth parameters", 400)

        return {
            "client_id": cast(str, client_id),
            "redirect_uri": cast(str, redirect_uri),
            "state": cast(str, state),
            "code_challenge": cast(str, code_challenge),
        }, None

    def get_authenticated_user(
        require_exists: bool = True,
    ) -> tuple[User | None, str | None, str | None, flask.Response | None]:
        """Get authenticated user from session and verify in backend.

        Args:
            require_exists: If True, return error if user doesn't exist in DB

        Returns:
            Tuple of (user, user_email, idp_user_id, error_response)
        """
        user_email = flask.session.get("user_email")
        idp_user_id = flask.session.get("idp_user_id")

        if not user_email or not idp_user_id:
            logging.warning("User not authenticated: %s", flask.session)
            if require_exists:
                return None, None, None, error_response("Authentication required", 401)
            else:
                # Not authenticated - return None values, no error
                return None, None, None, None

        try:
            user = front_end_token_service.GetUser(
                front_end_token_service_pb2.GetUserRequest(
                    idp_user_id=idp_user_id,
                    email_address=user_email,
                )
            ).user
        except grpc.RpcError as e:
            logging.warning(
                "Failed to get user from service: %s (code: %s, details: %s)",
                str(e),
                getattr(e, "code", lambda: "unknown")()
                if callable(getattr(e, "code", None))
                else "unknown",
                getattr(e, "details", lambda: "unknown")()
                if callable(getattr(e, "details", None))
                else "unknown",
            )

            if require_exists:
                return (
                    None,
                    None,
                    None,
                    error_response(
                        "An unexpected error occurred. Please try again.", 500
                    ),
                )
            else:
                user = None

        if require_exists and (not user or not user.id):
            return None, None, None, error_response("User not found", 404)

        return user, user_email, idp_user_id, None

    def resolve_user_tenant(
        user_email: str, user: User | None
    ) -> tuple[
        tenant_watcher_pb2.Tenant | None, TenantDetails | None, flask.Response | None
    ]:
        """Resolve tenant for user.

        Returns:
            Tuple of (tenant_proto, tenant_details, error_response)
        """
        tenant_proto = None
        if user and len(user.tenants) > 0:
            tenant_proto = tenant_map.get_tenant_by_id(user.tenants[0])

        if not tenant_proto:
            logging.warning(
                "No tenant found for user %s (email domain: %s)",
                user_email,
                user_email.split("@")[1] if "@" in user_email else "unknown",
            )
            return (
                None,
                None,
                error_response("No tenant found for user", 404, email=user_email),
            )

        tenant = tenant_map.tenant_to_tenant_details(tenant_proto)
        logging.info("Tenant found: %s", tenant)

        return tenant_proto, tenant, None

    def resolve_tenant_for_user(
        user_email: str,
        user: User | None,
        tenant_map: TenantMap,
    ) -> tuple[
        tenant_watcher_pb2.Tenant | None,
        TenantDetails | None,
        front_end_token_service_pb2.TenantEnsureMode.ValueType,
    ]:
        """Resolve the appropriate tenant for a user.

        It handles:
        - Multiple tenant scenarios with self-serve team prioritization
        - Enterprise vs non-enterprise tenant priority rules
        - Deleted tenant detection
        - Email domain tenant vs user's current tenant logic
        - EnsureUserInTenant mode determination

        Args:
            user_email: The user's email address
            user: The User object if the user exists, None otherwise
            tenant_map: The TenantMap instance for tenant lookups

        Returns:
            A tuple of (tenant_proto, tenant_details, ensure_mode) where:
            - tenant_proto: The tenant protobuf object or None
            - tenant_details: The TenantDetails object or None
            - ensure_mode: The mode to use when ensuring user is in tenant
        """
        tenant_from_email = tenant_map.get_tenant_for_email_domain(user_email)
        ensure_user_in_tenant_mode = front_end_token_service_pb2.TenantEnsureMode.TENANT_ENSURE_MODE_REQUIRE_EXISTING

        if user is not None and len(user.tenants) > 0:
            # Get tenant information for every tenant the user belongs to.
            tenants_from_user = []
            for tenant_id in user.tenants:
                tenant = tenant_map.get_tenant_by_id(tenant_id)
                if tenant is not None:
                    tenants_from_user.append(tenant)
                else:
                    logging.warning(
                        "User is in tenant %s but tenant not found",
                        tenant_id,
                    )

            if len(tenants_from_user) == 0:
                # User is in no valid tenants (all deleted)
                logging.warning(
                    "User is in no valid tenants: %s",
                    user.tenants,
                )
                _response_counter.labels("tenant_resolution", "deleted_tenant").inc()
                # Return None for all values - caller should handle this case
                return None, None, ensure_user_in_tenant_mode

            # Choose a self-serve team tenant if there is one. The need for this should be
            # temporary, while self-serve teams are all manually created and added to. Once
            # they're created automatically it should be enforced that a user can't belong to
            # more than one tenant.
            tenant_from_user = None
            for tenant in tenants_from_user:
                if (
                    tenant.config
                    and tenant.config.configs.get("is_self_serve_team") == "true"
                ):
                    logging.info(
                        "Choosing self-serve team tenant %s for user %s (total tenants %d)",
                        tenant.name,
                        user_email,
                        len(tenants_from_user),
                    )
                    tenant_from_user = tenant
                    break
            # If there wasn't a self-serve team tenant, just choose an arbitrary tenant.
            if tenant_from_user is None:
                tenant_from_user = tenants_from_user[0]
        else:
            tenant_from_user = None

        if (
            tenant_from_user is not None
            and tenant_from_user.tier == tenant_watcher_pb2.TenantTier.ENTERPRISE
        ):
            # This allows us to move some enterprise users to a test
            # namespace to test new features.
            tenant = tenant_from_user or tenant_from_email
        else:
            # Email address tenant wins over user's current tenant.
            # Used to simplify migrations between tenants for sales.
            # When we have bulk move, we can probably remove this.
            if tenant_from_email:
                # Email domain tenant exists
                tenant = tenant_from_email
                # Add user to tenant if they previously didn't have one
                if tenant_from_user is None:
                    ensure_user_in_tenant_mode = front_end_token_service_pb2.TenantEnsureMode.TENANT_ENSURE_MODE_ADD_IF_EMPTY
                else:
                    # Move user from their current tenant to the email domain tenant (expected for self-serve users migrating to enterprise via domain capturing)
                    ensure_user_in_tenant_mode = front_end_token_service_pb2.TenantEnsureMode.TENANT_ENSURE_MODE_OVERWRITE
            else:
                # Fall back to user's current tenant (if any)
                tenant = tenant_from_user

        if tenant is not None:
            tenant_details = tenant_map.tenant_to_tenant_details(tenant)
            return tenant, tenant_details, ensure_user_in_tenant_mode
        else:
            return None, None, ensure_user_in_tenant_mode

    def create_oauth_code_and_update_session(
        user: User,
        user_email: str,
        idp_user_id: str,
        tenant_proto: tenant_watcher_pb2.Tenant,
        oauth_params: dict,
    ) -> str:
        """Generate OAuth authorization code and update session.

        Returns:
            Generated authorization code
        """
        # Generate authorization code
        code = "_" + secrets.token_hex(nbytes=16)

        front_end_token_service.CreateCode(
            front_end_token_service_pb2.CreateCodeRequest(
                code=code,
                email=user.email,
                idp_user_id=idp_user_id,
                augment_user_id=user.id,
                client_id=oauth_params["client_id"],
                tenant_id=tenant_proto.id,
                redirect_uri=oauth_params["redirect_uri"],
                code_challenge=oauth_params["code_challenge"],
            )
        )

        # Update session
        flask.session["user_id"] = user.id
        flask.session["nonce"] = user.nonce
        flask.session.permanent = True

        return code

    def build_oauth_redirect_url(
        oauth_params: dict, code: str, tenant: TenantDetails, config: Config
    ) -> str:
        """Build OAuth redirect URL with parameters.

        Returns:
            Complete redirect URL with OAuth parameters
        """
        redirect_params = {
            "code": code,
            "state": oauth_params["state"],
        }

        # Check if this is an extension client that needs tenant_url
        client_config = config.client_config_map.get(oauth_params["client_id"])
        if client_config and not client_config.instant_redirect:
            redirect_params["tenant_url"] = tenant.tenant_url

        redirect_url_with_params = (
            oauth_params["redirect_uri"]
            + ("&" if "?" in oauth_params["redirect_uri"] else "?")
            + urlencode(redirect_params)
        )

        return redirect_url_with_params

    def handle_grpc_error(e: grpc.RpcError, context: str) -> flask.Response:
        """Handle gRPC errors consistently."""
        logging.exception(
            "gRPC error in %s: %s (code: %s)",
            context,
            str(e),
            getattr(e, "code", lambda: "unknown")()
            if callable(getattr(e, "code", None))
            else "unknown",
        )
        return error_response("An unexpected error occurred. Please try again.", 500)

    def handle_unexpected_error(e: Exception, context: str) -> flask.Response:
        """Handle unexpected errors consistently."""
        logging.exception("Unexpected error in %s", context)
        return error_response("An unexpected error occurred. Please try again.", 500)

    @app.route("/health")
    def health():  # pylint: disable=redefined-outer-name
        # Wait for tenant map to load before declaring healthy
        #
        # Can't call into tenant_map in the body of create_app() because tenant_map
        # will launch a background thread, which won't be inherited across the fork.
        if not tenant_map_loaded.is_set():
            logging.info("Waiting for tenant map to load")
            _ = tenant_map.get_tenant("dummy")
            logging.info("Tenant map loaded")
            tenant_map_loaded.set()
        return "Ok"

    def validate_client_id(label: str, client_id: str | None):
        """Validate the client ID."""

        if not client_id:
            _response_counter.labels(label, "no_client_id").inc()
            return _redirect_to_client_error(
                None,
                "invalid_request",
                error_description="No client id defined",
            )

        if client_id not in config.client_config_map:
            _response_counter.labels(label, "invalid_client_id").inc()
            return _redirect_to_client_error(
                None,
                "unauthorized_client",
                error_description="client id is invalid",
            )
        return None

    def validate_common_client_request_args(
        label: str, args: list[tuple[str, str]], test_ok: bool = False
    ):
        """
        Validate the common arguments expected from the client.
        """

        def find_arg(arg_name: str):
            return _find_arg_safe(args, arg_name)

        if len(args) != len(set([arg[0] for arg in args])):
            _response_counter.labels(label, "duplicate_args").inc()
            return _redirect_to_client_error(
                None,
                "invalid_request",
                error_description="Duplicate argument",
            )

        if test_ok and find_arg("test") is not None:
            return None

        client_id = find_arg("client_id")
        response = validate_client_id(label, client_id)
        if response is not None:
            return response
        assert client_id is not None

        # If a redirect isn't provided, default to the empty string. This is
        # helpful for vim as the redirect isn't used and not providing the
        # argument allows the auth url to be shorter. For other clients, the
        # empty string is invalid and will be caught below in the check for
        # valid uris.
        redirect_uri = find_arg("redirect_uri")
        if redirect_uri is None:
            redirect_uri = ""

        # client_config verified by validate_client_id
        client_config = config.client_config_map.get(client_id)
        assert client_config is not None

        client_name = client_config.name

        # Check that the redirect URI is valid
        # All errors above this point are errors that should NOT redirect back to the client as we
        # don't trust the client.
        uri_to_check = redirect_uri
        if uri_to_check in client_config.redirect_uris:
            return None

        if redirect_uri.startswith("http://127.0.0.1:") or redirect_uri.startswith(
            "http://localhost:"
        ):
            uri_to_check = _remove_port_from_url(redirect_uri)

        _login_redirect_uri_counter.labels(uri_to_check).inc()
        if uri_to_check not in client_config.redirect_uris:
            logging.error(
                "Redirect URI %s not in client config %s",
                uri_to_check,
                client_config.redirect_uris,
            )

            # Since we allow the empty string as a valid redirect_uri for the
            # vim client, perform this check here rather than earlier in the
            # client request args validation check.
            if redirect_uri == "":
                return _redirect_to_client_error(
                    None,
                    "invalid_request",
                    error_description="No redirect defined",
                )

            scheme_name = check_if_uri_differs_only_by_scheme(
                client_config.redirect_uris, uri_to_check
            )

            if scheme_name and scheme_name not in ["http", "https"]:
                _response_counter.labels(label, "unsupported_editor").inc()
                return _redirect_to_client_error(
                    None,
                    "invalid_request",
                    error_description=f"Unsupported editor {scheme_name}",
                    client_name=client_name,
                )

            _response_counter.labels(label, "invalid_redirect_uri").inc()

            return _redirect_to_client_error(
                None,
                "invalid_request",
                error_description="redirect uri is incorrect",
                client_name=client_name,
            )
        return None

    def validate_request_args(args: list[tuple[str, str]], test_ok: bool = False):
        """
        # Validate the request according to [RFC 6749, section 5.2](
        # https://datatracker.ietf.org/doc/html/rfc6749#section-5.2)

        # First, check if the redirect URI is present. We can't do much without
        # it. Even errors are meant to be returned to the client using the redirect
        # URI if possible.
        """

        def find_arg(arg_name: str):
            return _find_arg_safe(args, arg_name)

        if test_ok and find_arg("test") is not None:
            return None

        response = validate_common_client_request_args(
            label="authorize", args=args, test_ok=test_ok
        )
        if response is not None:
            # failed validation
            return response

        # Extract the remaining required query parameters
        # Note: client_name and redirect_uri were validated in the common
        # request args validation check.
        client_id = find_arg("client_id")
        assert client_id is not None
        client_config = config.client_config_map.get(client_id)
        assert client_config is not None
        client_name = client_config.name

        redirect_uri = find_arg("redirect_uri")
        response_type = find_arg("response_type")
        code_challenge_method = find_arg("code_challenge_method")
        code_challenge = find_arg("code_challenge")
        state = find_arg("state")

        # The invitations client doesn't use PKCE
        if client_id == "invitations":
            return None

        if not (response_type and code_challenge and state):
            logging.error("Missing required request args: %s", args)
            _response_counter.labels("authorize", "missing_args").inc()
            return _redirect_to_client_error(
                redirect_uri,
                "invalid_request",
                error_description="Missing argument",
            )

        # If the code challenge method isn't specified we'll default to S256
        if code_challenge_method is not None and code_challenge_method != "S256":
            _response_counter.labels("authorize", "invalid_challenge_method").inc()
            return _redirect_to_client_error(
                redirect_uri,
                "invalid_request",
                error_description="Unsupported code challenge method",
            )

        if len(code_challenge) != 43:
            _response_counter.labels("authorize", "invalid_challenge").inc()
            return _redirect_to_client_error(
                redirect_uri,
                "invalid_request",
                error_description="Invalid code challenge",
            )

        # Check that the response type is "code"
        if response_type != "code":
            _response_counter.labels("authorize", "invalid_response_type").inc()
            return _redirect_to_client_error(
                redirect_uri,
                "unsupported_response_type",
                client_name=client_name,
            )

        return None

    def render(template_name: str, enable_fingerprinting: bool = False, **kwargs: Any):
        """Wrap flask.render_template and provide common parameters."""

        if "year" not in kwargs:
            kwargs["year"] = datetime.now(timezone.utc).year

        site_key = captcha_checker.site_key()
        if enable_fingerprinting:
            if "recaptcha_site_key" not in kwargs and site_key:
                kwargs["recaptcha_site_key"] = site_key

            if (
                _VERISOUL_ENABLED.get(base.feature_flags.get_global_context())
                and verisoul is not None
            ):
                kwargs["verisoul"] = {
                    "env": verisoul.env,
                    "project_id": verisoul.project_id,
                }

            kwargs["verosint"] = _VEROSINT_FINGERPRINTING.get(
                base.feature_flags.get_global_context()
            )

            if (
                _HCAPTCHA_COLLECT.get(base.feature_flags.get_global_context())
                and config.hcaptcha is not None
            ):
                kwargs["hcaptcha"] = {
                    "sitekey": config.hcaptcha.sitekey,
                }

        return flask.render_template(template_name, **kwargs)

    @app.route("/logout", methods=["GET"])
    def logout():
        """Endpoint used by the client to log the user out.

        Clears the session cookie and redirects to a validated client url.
        """
        # See https://auth0.com/docs/authenticate/login/logout for an example of
        # validating arguments to the logout request.
        logging.debug("Logging out")
        args = list(flask.request.args.items(True))
        response = validate_common_client_request_args("logout", args, False)
        if response is not None:
            return response
        redirect_uri = flask.request.args.get("redirect_uri")
        assert redirect_uri is not None

        # get user email and tenant name for audit logging
        user_email = flask.session.get("user_email") or flask.session.get("email")
        tenant = (
            None
            if user_email is None
            else tenant_map.get_tenant_for_email_domain(user_email)
        )
        tenant_name = tenant.name if tenant is not None else None

        clear_auth_session_state()

        ocsf_audit_logger.log_authentication_with_tenant(
            audit_ocsf.AuthenticationEventBuilder(audit_ocsf.ActivityID.LOGOFF)
            .with_user(audit_ocsf.User(email_addr=user_email))
            .with_status(audit_ocsf.StatusID.SUCCESS)
            .with_message("User logged out")
            .with_remote(True)
            .with_auth_protocol(audit_ocsf.AuthProtocolID.OAUTH2)
            .with_src_endpoint(
                audit_ocsf.NetworkEndpoint(
                    ip=parse_x_forwarded_for(
                        flask.request.headers.get("X-Forwarded-For", "")
                    ),
                )
            )
            .with_dst_endpoint(
                audit_ocsf.NetworkEndpoint(
                    name="logout",
                    hostname=flask.request.host,
                )
            )
            .build(),
            tenant_name,
        )

        return flask.redirect(redirect_uri)

    def identity_provider_allowed(
        user_id: str, allowed_identity_providers: Sequence[str]
    ):
        return len(allowed_identity_providers) == 0 or any(
            user_id.startswith(idp + "|") for idp in allowed_identity_providers
        )

    @app.route("/terms-accept", methods=["POST", "GET"])
    def terms_accept():
        """Accept terms using session cookies.

        Main responsibilities:
            - check that the user maps to a tenant. If not return error
            - display page asking user to accept terms of service
            - process user acceptance of terms of service
        """
        user_email = flask.session.get("user_email")
        idp_user_id = flask.session.get("idp_user_id")
        given_name = flask.session.get("given_name", "")
        family_name = flask.session.get("family_name", "")

        return accept_internal(
            args=list(flask.request.args.items(True)),
            idp_user_id=idp_user_id,
            user_email=user_email,
            given_name=given_name,
            family_name=family_name,
        )

    def user_has_blocking_suspension(user: User):
        """Check if a user has any suspensions that should block login.

        Args:
            user: The user object to check

        Returns:
            bool: True if the user has any blocking suspensions, False otherwise
        """
        if not hasattr(user, "suspensions"):
            return False

        return any(
            suspension.suspension_type
            == UserSuspensionType.USER_SUSPENSION_TYPE_API_ABUSE
            or suspension.suspension_type
            == UserSuspensionType.USER_SUSPENSION_TYPE_PAYMENT_FRAUD
            for suspension in user.suspensions
        )

    def check_and_redirect_for_invitations(user_email: str, continue_url: str):
        """Check if a user has pending invitations and redirect if needed.

        Args:
            user_email: The email address to check for invitations
            continue_url: The complete URL to return to after handling invitations

        Returns:
            A redirect response if invitations exist, None otherwise
        """
        if not _LOGIN_INVITATIONS_ENABLED.get(base.feature_flags.get_global_context()):
            return None

        logging.info(f"Checking if user {user_email} has any pending invitations")
        if invitation_service.has_invitations(user_email):
            logging.info(
                f"User {user_email} has pending invitations, redirecting to invitations page"
            )
            # Redirect to invitations page with continue_url
            invitations_url = flask.url_for(
                "invitations_page",
                continue_url=continue_url,
                _external=True,
            )
            return flask.redirect(invitations_url)

        return None

    def allow(
        user_id: str | None,
        idp_user_id: str,
        user_email: str,
        action: str,
        counter_func: Callable[[str], None],
        sign_up: bool,
    ) -> CheckerResult:
        # Extract client errors for debugging and bot detection
        client_errors_json = flask.request.form.get("client-errors", "[]")
        try:
            client_errors = json.loads(client_errors_json)
        except json.JSONDecodeError:
            client_errors = []

        # Log client errors for debugging
        if client_errors:
            logging.info(
                "Client-side errors reported for %s: %s", user_email, client_errors
            )
            for error in client_errors:
                if "timeout" in error.lower():
                    _errors_counter.labels("client_error", "timeout").inc()
                elif "blocked" in error.lower():
                    _errors_counter.labels("client_error", "blocked").inc()

        # Bot detection: Real users report errors when fraud detection fails
        verisoul_session_id = flask.request.form.get("verisoul-session-id", "")
        if not verisoul_session_id and not client_errors:
            logging.warning(
                "Suspicious request: no Verisoul session ID and no client errors reported for %s",
                user_email,
            )
            _errors_counter.labels("bot_detection", "no_errors_reported").inc()

        checker_result = captcha_checker.allow(
            flask.request.form.get("g-recaptcha-response", ""),
            action,
            parse_x_forwarded_for(flask.request.headers.get("X-Forwarded-For", "")),
            flask.request.headers.get("User-Agent", ""),
            _RECAPTCHA_THRESHOLD.get(base.feature_flags.get_global_context()),
            user_email,
        )

        if checker_result != CheckerResult.ALLOWED:
            counter_func("captcha-failed")
            return checker_result

        if verisoul_checker is not None:
            checker_result = verisoul_checker.allow(
                verisoul_session_id,
                user_id,
                idp_user_id,
                user_email,
            )
            if checker_result != CheckerResult.ALLOWED:
                counter_func("verisoul-failed")
                return checker_result

        try:
            response = front_end_token_service.RunUserSecurityChecks(
                front_end_token_service_pb2.RunUserSecurityChecksRequest(
                    augment_user_id=user_id or "",
                    idp_user_id=idp_user_id,
                    email_address=user_email,
                    user_agent=flask.request.headers.get("User-Agent", ""),
                    ip_address=parse_x_forwarded_for(
                        flask.request.headers.get("X-Forwarded-For", "")
                    ),
                    verosint_device_id=flask.request.form.get("verosint-device-id", ""),
                    verisoul_session_id=flask.request.form.get(
                        "verisoul-session-id", ""
                    ),
                    recaptcha_token=flask.request.form.get("g-recaptcha-response", ""),
                    hcaptcha_response=flask.request.form.get("h-captcha-response", ""),
                    sign_up=sign_up,
                )
            )
            if not response.passed:
                counter_func("user-checks-failed")
                return CheckerResult.BLOCKED
        except Exception as e:
            logging.error("Failed to run user checks: %s", e)
            counter_func("user-checks-error")
            return CheckerResult.ALLOWED

        return CheckerResult.ALLOWED

    def accept_internal(
        args: list[tuple[str, str]],
        idp_user_id: str | None,
        user_email: str | None,
        given_name: str = "",
        family_name: str = "",
    ):
        """Internal implementation of the /terms-accept endpoint.

        This internal version is shared between the /terms-accept endpoint and the
        /authorize endpoint. The customer-ui uses a long-lived authentication
        cookie that, when valid and present in /authorize, will skip right to
        the "accept" workflow. We bypass the endpoint because of proxying code
        in the nginx config.

        Main responsibilities:
            - check that the user maps to a tenant. If not return error
            - display page asking user to accept terms of service
            - process user acceptance of terms of service

        Args:
            idp_user_id (str): The user ID from the identity provider.
            user_email (str): The user's email address.
        """
        try:
            return _accept_internal(
                args, idp_user_id, user_email, given_name, family_name
            )
        except Exception:
            clear_auth_session_state()
            raise

    def _accept_internal(
        args: list[tuple[str, str]],
        idp_user_id: str | None,
        user_email: str | None,
        given_name: str,
        family_name: str,
    ):
        def find_arg(arg_name: str):
            return _find_arg_safe(args, arg_name)

        logging.info("Checking accept request arguments %s %s", idp_user_id, user_email)

        if user_email is None or idp_user_id is None:
            return (
                render(
                    "unauthenticated.html",
                    user_message="We encountered an unexpected error.",
                    try_again="/login?" + urlencode(args),
                ),
                401,
            )

        if find_arg("test") is not None:
            return "Test succeeded"

        response = validate_request_args(args)
        if response is not None:
            return response

        client_id = find_arg("client_id")
        # We validated in args validation
        assert client_id is not None

        login_query_string = urlencode(args)

        staging_url = config.staging_url
        if (
            flask.request.method == "GET"
            and staging_url is not None
            and staging_user_email_match is not None
            and staging_user_email_match.match(user_email)
        ):
            redirect_uri = find_arg("redirect_uri")

            if redirect_uri is not None and redirect_uri.startswith("https://"):
                try:
                    server_name = urlparse(redirect_uri).netloc
                except Exception:
                    server_name = "unknown"

                return (
                    render(
                        "unauthenticated.html",
                        user_message="You can't use your staging login with "
                        + server_name
                        + ". Please start at the staging server or use a"
                        " production login.",
                    ),
                    401,
                )

            # This user is trying to log in to a staging tenant. Send them to
            # the staging auth-central, rather than sending them through prod's
            # auth-central
            logging.info("Redirecting to staging auth-central %s", staging_url)
            redirect_url = f"{staging_url}/login?{login_query_string}"
            return render(
                "redirect.html",
                delay_ms=1200,
                redirect_url=redirect_url,
            )

        if (
            flask.request.method == "POST"
            and flask.request.form.get("sign_up") == "true"
            and flask.request.form.get("continue") == "continue"
        ):
            checker_result = allow(
                None,
                idp_user_id,
                user_email,
                "signup",
                lambda x: _signup_callbacks_counter.labels("individual", x).inc(),
                sign_up=True,
            )

            if checker_result != CheckerResult.ALLOWED:
                if checker_result == CheckerResult.REGION_BLOCKED:
                    return render(
                        "unauthenticated.html",
                        user_message="Due to increased demand, we're limiting signups in certain regions to maintain performance for existing customers.",
                    )
                else:
                    return render(
                        "unauthenticated.html",
                        user_message="Sign-up rejected",
                        try_again="/login?" + urlencode(args),
                    )

            (user, tenant, status) = do_signup(
                is_individual=True,
                in_usa=False,
                idp_user_id=idp_user_id,
                email=user_email,
                given_name=given_name,
                family_name=family_name,
            )

            _signup_callbacks_counter.labels(
                "individual", status.value if status is not None else "success"
            ).inc()

            if (
                status == SignupStatus.ALREADY_SIGNED_UP
                and user is not None
                and tenant is not None
            ):
                # Maybe user signed up in a different login flow?
                pass
            elif status is not None:
                user_message = f"We were unable to sign you up: {status}"

                if status == SignupStatus.SIGNUP_LIMIT_REACHED:
                    user_message = "We were unable to sign you up. We have reached our signup limit. Please try again in a few hours."
                elif status == SignupStatus.YOUR_ORG_HAS_AUGMENT:
                    # Shouldn't happen because we're in the sign-in flow
                    user_message = "We were unable to sign you up. Your email belongs to an organization that already has an Augment account. Please contact your administrator."
                elif status == SignupStatus.IN_ANOTHER_TENANT:
                    # Shouldn't happen because we're in the sign-in flow
                    user_message = "We were unable to sign you up. You are already signed up for Augment in a different plan or organization."
                elif status == SignupStatus.ALREADY_SIGNED_UP:
                    user_message = "We were unable to sign you up. You are already signed up for Augment."
                elif status == SignupStatus.INVALID_EMAIL:
                    user_message = "We were unable to sign you up. Your e-mail address was missing or invalid."

                return render(
                    "unauthenticated.html",
                    user_message=user_message,
                )

            assert tenant is not None
            assert user is not None

            tenant = tenant_map.tenant_to_tenant_details(tenant)
        else:
            # Table scan lies within!
            try:
                user = front_end_token_service.GetUser(
                    front_end_token_service_pb2.GetUserRequest(
                        idp_user_id=idp_user_id,
                        email_address=user_email,
                    )
                ).user

                # Check if user is blocked
                if user and (user.blocked or user_has_blocking_suspension(user)):
                    logging.warning("Blocked user attempted to log in: %s", user_email)
                    return (
                        render(
                            "unauthenticated.html",
                            user_message=f"Your account has been blocked. User ID {user.id}.",
                        ),
                        403,
                    )

                # Check for invitations for existing users
                continue_url = f"{flask.url_for('terms_accept')}?{login_query_string}"
                invitation_redirect = check_and_redirect_for_invitations(
                    user_email, continue_url
                )
                if invitation_redirect:
                    return invitation_redirect

            except grpc.RpcError as e:
                logging.error("Failed to get user: %s", e)
                raise

            # Use the shared tenant resolution logic
            tenant_proto, tenant, ensure_user_in_tenant_mode = resolve_tenant_for_user(
                user_email, user, tenant_map
            )

            # Handle the case where user is in deleted tenants
            if user is not None and len(user.tenants) > 0 and tenant_proto is None:
                _response_counter.labels("accept", "deleted_tenant").inc()
                return (
                    render(
                        "unauthenticated.html",
                        user_message="We couldn't find the organization you are part of. Please contact your administrator.",
                        details=f"Details: time:{datetime.now(timezone.utc)}",
                    ),
                    401,
                )

            if tenant is not None:
                if not identity_provider_allowed(
                    idp_user_id, tenant.allowed_identity_providers
                ):
                    logging.warning("User used blocked identity provider")
                    return (
                        render(
                            "unauthenticated.html",
                            user_message="Your organization does not allow login through this method. Please try another method.",
                            try_again=f"{flask.url_for('login')}?{login_query_string}",
                        ),
                        401,
                    )

                logging.info("Ensuring user is in tenant")
                try:
                    request = front_end_token_service_pb2.EnsureUserInTenantRequest(
                        email_address=user_email,
                        tenant_id=tenant.tenant_id,
                        idp_user_id=idp_user_id,
                        mode=ensure_user_in_tenant_mode,
                        given_name=given_name,
                        family_name=family_name,
                    )
                    if user:
                        request.augment_user_id = user.id
                    user = front_end_token_service.EnsureUserInTenant(request).user
                except grpc.RpcError as e:
                    logging.error("Failed to ensure user in tenant: %s", e)
                    raise

            else:
                assert tenant is None

                _response_counter.labels("accept", "no_tenant").inc()
                logging.warning("Unable to find tenant")

                # Check for invitations when no tenant is found
                continue_url = f"{flask.url_for('terms_accept')}?{login_query_string}"
                invitation_redirect = check_and_redirect_for_invitations(
                    user_email, continue_url
                )
                if invitation_redirect:
                    return invitation_redirect

                if _IMPLICIT_TOS_ACCEPTANCE.get(
                    base.feature_flags.get_global_context()
                ):
                    return (
                        render(
                            "human_verification.html",
                            post_url=f"{flask.url_for('terms_accept')}?{login_query_string}",
                            enable_fingerprinting=True,
                            sign_up=True,
                        ),
                        401,
                    )
                else:
                    return (
                        render(
                            "not_signed_up.html",
                            email=user_email,
                            post_url=f"{flask.url_for('terms_accept')}?{login_query_string}",
                            login_url=f"{flask.url_for('login')}?{login_query_string}",
                            enable_fingerprinting=True,
                        ),
                        401,
                    )

        logging.info("Found tenant: %s", tenant.name)

        signin_method = flask.request.form.get("continue")
        terms_of_service = flask.request.form.get("terms-of-service")
        implicit = _IMPLICIT_TOS_ACCEPTANCE.get(base.feature_flags.get_global_context())

        def serve_accept(tenant: TenantDetails, user_warning: str | None = None):
            return render(
                "accept.html",
                user_warning=user_warning,
                post_url=f"{flask.url_for('terms_accept')}?{login_query_string}",
                community_tos=tenant.community_tos,
                enable_fingerprinting=True,
            )

        # Check if user previously agreed to terms of service
        logging.info("Checking terms of service for user %s", user_email)
        try:
            terms = front_end_token_service.GetTermsApproval(
                front_end_token_service_pb2.GetTermsApprovalRequest(
                    email=user_email, revision=tenant.tnc_revision
                )
            )
            approved = terms.approved
        except grpc.RpcError as e:
            logging.error("Failed to get terms approval: %s", e)
            raise

        # When implicit acceptance is enabled, route through human verification page until approved.
        if implicit and not approved:
            if flask.request.method == "GET":
                return (
                    render(
                        "human_verification.html",
                        post_url=f"{flask.url_for('terms_accept')}?{login_query_string}",
                        enable_fingerprinting=True,
                        sign_up=False,
                    ),
                    401,
                )
            if flask.request.method == "POST":
                terms_of_service = "accepted"
                signin_method = "continue"

        if approved and signin_method is None:
            _response_counter.labels("tnc_approval", "prior_approval").inc()
            signin_method = "continue"
            terms_of_service = "accepted"
        elif flask.request.method == "POST" and terms_of_service == "accepted":
            sign_up = flask.request.form.get("sign_up") == "true"

            if not sign_up:
                # Reports fingerprints to request insight as a side effect of allow().
                # Not needed for sign_up which already callas allow() earlier in this
                # function.
                #
                # We used to error signup here as a hack - users who signed up
                # by /signup/login flow would get fingerprinted and potentially
                # blocked when they accepted the terms of service. Error here had
                # the unfortunate side effect of potentially blocking existing users who
                # are accepting a new revision of the terms of service.
                #
                # Asking marketing to deprecate old /signup/login flow but have not
                # been able to push it through.
                _ = allow(
                    user.id,
                    idp_user_id,
                    user_email,
                    "accept",
                    lambda x: _response_counter.labels("tnc_approval", x).inc(),
                    sign_up=sign_up,
                )

            _response_counter.labels("tnc_approval", "new_approval").inc()
            front_end_token_service.SetTermsApproval(
                front_end_token_service_pb2.SetTermsApprovalRequest(
                    email=user_email, revision=tenant.tnc_revision, approved=True
                )
            )
        elif (flask.request.method == "GET" or signin_method is None) and not implicit:
            return serve_accept(tenant)

        if not implicit and (
            terms_of_service is None or terms_of_service != "accepted"
        ):
            return serve_accept(
                tenant, "You must accept the terms of service to use Augment."
            )

        if signin_method != "continue":
            return serve_accept(
                tenant, "We encountered an unexpected error, please try again."
            )

        if user is None:
            raise RuntimeError("Internal error - user is None should not happen")

        is_vim_client = client_id == "augment-vim-extension" or client_id == "v"

        ajs_anonymous_id = flask.request.cookies.get("ajs_anonymous_id")
        identify_user(
            user.id,
            {
                "email": user.email,
            },
            ajs_anonymous_id,
        )

        track_event(user.id, "User Logged In", ajs_anonymous_id)

        # Set up a long-lived session. Currently, the session is only used to
        # prevent repeated login for the customer UI.
        logging.info("Updating session for user %s with id %s", user.email, user.id)

        flask.session["idp_user_id"] = idp_user_id
        flask.session["user_email"] = user.email
        flask.session["user_id"] = user.id
        flask.session["nonce"] = user.nonce

        # "permanent" defaults to 31 days but can be configured using
        # PERMANENT_SESSION_LIFETIME configuration key.
        # https://flask.palletsprojects.com/en/stable/api/#sessions
        flask.session.permanent = True

        if client_id == "invitations":
            # Use the configured customer UI URL if available, otherwise fall back to hardcoded URL
            customer_ui_url = config.customer_ui_url
            return flask.redirect(
                location=f"{customer_ui_url}/login",
                code=302,
            )

        logging.info("Checking authorize request arguments")

        client_config = config.client_config_map[client_id]
        client_name = client_config.name

        redirect_uri = find_arg("redirect_uri")
        # For vim we don't use the redirect_uri so it may not be present. In
        # this case, default to an empty string.
        if redirect_uri is None and is_vim_client:
            redirect_uri = ""
        assert redirect_uri is not None

        state = find_arg("state")
        assert state is not None

        code_challenge = find_arg("code_challenge")
        assert code_challenge is not None

        tenant_id = tenant.tenant_id

        logging.info("Checking that the user is authorized")

        try:
            # Generate an authorization code and store it in the database
            code = "_" + secrets.token_hex(nbytes=16)

            front_end_token_service.CreateCode(
                front_end_token_service_pb2.CreateCodeRequest(
                    code=code,
                    email=user.email,
                    idp_user_id=idp_user_id,
                    augment_user_id=user.id,
                    client_id=client_id,
                    tenant_id=tenant_id,
                    redirect_uri=redirect_uri,
                    code_challenge=code_challenge,
                )
            )
        except RuntimeError as e:
            logging.exception("Error while authorizing user: %s", e)
            _response_counter.labels("authorize", "server_error").inc()
            return _redirect_to_client_error(
                redirect_uri,
                "server_error",
                error_description="Internal server error",
                client_name=client_name,
            )

        logging.info("User is authorized")
        _response_counter.labels("authorize", "success").inc()

        if client_config.instant_redirect:
            return _redirect_to_client_no_transition_page(
                redirect_uri,
                {"code": code, "state": state},
            )
        elif is_vim_client:
            return render(
                "complete.html",
                code=code,
                state=state,
                tenant_url=tenant.tenant_url,
            )
        else:
            return _redirect_to_client(
                redirect_uri,
                {"code": code, "state": state, "tenant_url": tenant.tenant_url},
                client_name=client_name,
            )

    @app.route("/authorize", methods=["GET"])
    def authorize():
        query_string = flask.request.query_string.decode("utf-8")
        args = list(flask.request.args.items(True))
        client_id = _find_arg_safe(args, "client_id")
        response = validate_client_id("authorize", client_id)
        if response is not None:
            return response
        assert client_id is not None
        client_config = config.client_config_map[client_id]
        assert client_config is not None
        if client_config.reuse_session_cookie:
            # Look for a session cookie in the request headers. If there, see if
            # the user is still authenticated.
            user_id = flask.session.get("user_id")
            idp_user_id = flask.session.get("idp_user_id")
            email = flask.session.get("user_email") or flask.session.get("email")
            nonce = flask.session.get("nonce")

            if (
                user_id is not None
                and idp_user_id is not None
                and email is not None
                and nonce is not None
            ):
                try:
                    user_nonce = front_end_token_service.GetNonceForUser(
                        front_end_token_service_pb2.GetNonceForUserRequest(
                            user_id=user_id
                        )
                    ).nonce
                except grpc.RpcError as e:
                    logging.error("Failed to get nonce for user: %s", e)
                    user_nonce = None

                is_cookie_valid = nonce == user_nonce
                if is_cookie_valid:
                    logging.info("Found valid session cookie")
                    return accept_internal(
                        args=list(flask.request.args.items(True)),
                        idp_user_id=idp_user_id,
                        user_email=email,
                    )
            else:
                logging.debug("No auth cookie found for customer-ui.")

        # Check for test user in Authorization header if feature flag is enabled
        if _BEARER_TOKEN_AUTH_ENABLED.get(
            base.feature_flags.get_global_context()
        ) and flask.request.headers.get("Authorization"):
            logging.info("Authorization header found")
            test_user_info = decode_bearer_token(
                config, flask.request.headers.get("Authorization")
            )
            if test_user_info:
                logging.info("Test user detected: %s", test_user_info)
                return _oauth2_callback(test_user_info, client_id, query_string)

        # If the cookie is not present or invalid, clear the session and
        # redirect to the login page.
        clear_auth_session_state()

        return flask.redirect(f"/login?{query_string}")

    @app.route("/login", methods=["GET"])
    def login():
        """Initialize Auth0 login flow."""
        args = list(flask.request.args.items(True))
        response = validate_request_args(args, test_ok=True)
        if response:
            return response

        # u for uniquifier - add a random value to the args list
        # so the resulting encoded_state below is unique per window.
        #
        # If a user pastes the same URL into two browser windows, then either
        # browser window will work for login (but not both).
        args.append(("u", secrets.token_urlsafe(8)))

        clear_auth_session_state()
        encoded_state = _base64_url_encode(urlencode(args).encode())
        redirect = flask.url_for("oauth2_callback", _external=True)
        if dev_callback_url is not None:
            encoded_state = _base64_url_encode(redirect.encode()) + "." + encoded_state
            redirect = dev_callback_url

        redirect = auth0_oauth.auth0_login.authorize_redirect(  # type: ignore
            redirect_uri=redirect, state=encoded_state
        )

        return flask.redirect(redirect.headers["Location"], code=302)

    @app.route("/oauth2/callback", methods=["GET"])
    def oauth2_callback():
        """Handle oauth2 callback from Auth0."""

        request_args = [
            arg
            for arg in list(flask.request.args.items(True))
            if arg[0] not in ["code", "token"]
        ]
        login_args: str | None = None

        # authlib modifies flask session even in error path
        debug_session = copy.deepcopy(flask.session)

        error_param = flask.request.args.get("error")
        description_param = flask.request.args.get("error_description")

        try:
            # Reconstruct login args from state
            state = flask.request.args.get("state")
            if not state:
                raise RuntimeError("No state in oauth2 callback")

            if dev_callback_url is not None:
                # Split state on .
                state_parts = state.split(".", 2)
                if len(state_parts) != 2:
                    raise RuntimeError("Invalid state in oauth2 callback")

                state = state_parts[1]

            state = state + "=" * (-len(state) % 4)

            # login_args is under the control of an attacker.
            #
            # An attacker could create a direct link to this endpoint. The
            # attacker could pass a valid code they got from Auth0.
            #
            # The token returned would be for the attacker. The attacker
            # could specify a redirect URI of user's VS.code instance. However,
            # the VS.code instance should reject the attack because it
            # either because there isn't an outstanding authentication or
            # because it's unlikely the state generated by the attacker matches
            # the randomly generated state in the extension.
            #
            # Another layer of protection is that authlib has a session variable
            # with the value of state in its name. If the attacker attempts to present
            # a login_args that wasn't seen by /login, authlib will fail to find the
            # session variable and throw a MismatchingStateError().
            login_args = base64.urlsafe_b64decode(state).decode("utf-8")
            args_list = parse_qsl(login_args)
            args_list = [arg for arg in args_list if arg[0] != "u"]
            login_args = urlencode(args_list)

            override: dict[str, Any] | OAuthError | None = app.config.get(
                "augment_override_authorize_access_token", None
            )
            if isinstance(override, Exception):
                raise override
            if override is None:
                token = auth0_oauth.auth0_login.authorize_access_token()  # type: ignore
            else:
                token = override

            client_id = _find_arg_safe(args_list, "client_id")

            # run this code after /authorize for e2e test login
            # resp is what the token encodes
            userInfo = token.get("userinfo")
            if userInfo is None:
                raise RuntimeError("No user info in oauth2 callback")

            # Okta does not set email_verified, so treat missing as verified.
            email_verified = userInfo.get("email_verified", True)
            if not email_verified:
                raise RuntimeError("No verified email in oauth2 callback")
            return _oauth2_callback(userInfo, client_id, login_args)
        except OAuthError as e:
            logging.exception(
                "Error in auth0callback: %s request args: %s", e, request_args
            )
            logging.info("OAuthError session keys: %s", debug_session.keys())
            size = 0
            for k, v in debug_session.items():
                size += len(str(k))
                size += len(str(v))
            logging.info("OAuthError session size: %s", size)
            # Clear session so user has cleaner slate when trying again
            clear_auth_session_state()

            message, details = oauth_error_to_user_message(
                library_error=e.error,
                library_description=e.description,
                error=error_param,
                description=description_param,
            )

            return (
                render(
                    "unauthenticated.html",
                    user_message=message,
                    details=details,
                ),
                401,
            )
        except InvalidEmailError:
            logging.warning(
                "Invalid email in auth0callback: request args: %s", request_args
            )
            # Clear session so user has cleaner slate when trying again
            clear_auth_session_state()

            return (
                render(
                    "unauthenticated.html",
                    user_message="Sign in failed: the email address provided is missing or not valid.",
                    details=f"Details: time:{datetime.now(timezone.utc)}",
                ),
                401,
            )
        except Exception as e:  # pylint: disable=broad-exception-caught
            logging.exception(
                "Error in auth0callback: %s request args: %s", e, request_args
            )
            return (
                render(
                    "unauthenticated.html",
                    user_message="We encountered an unexpected error.",
                    details=f"Details: time:{datetime.now(timezone.utc)}",
                ),
                401,
            )

    def _oauth2_callback(
        userInfo: dict[str, Any],
        client_id: str | None,
        login_args: str | None,
    ):
        clear_auth_session_state()

        email = userInfo.get("email", "")
        if not email or not is_valid_email(email):
            raise InvalidEmailError()

        flask.session["user_email"] = email
        flask.session["idp_user_id"] = userInfo.get("sub")
        flask.session["given_name"] = userInfo.get("given_name", "")
        flask.session["family_name"] = userInfo.get("family_name", "")

        if _REACT_FRONTEND_ENABLED.get(base.feature_flags.get_global_context()):
            return flask.redirect(
                location=flask.url_for("serve_react_app") + "?" + (login_args or ""),
                code=302,
            )

        if client_id == "invitations":
            return flask.redirect(
                location=flask.url_for("invitations_page"),
                code=302,
            )

        return flask.redirect(
            location=flask.url_for("terms_accept") + "?" + (login_args or ""),
            code=302,
        )

    @app.route("/token", methods=["POST"])
    def token():
        """Endpoint used by the client to exchange the code for an access token."""

        args = None
        if (
            flask.request.headers.get("Content-Type")
            == "application/x-www-form-urlencoded"
        ):
            args = flask.request.form
        elif flask.request.headers.get("Content-Type") == "application/json":
            args = flask.request.json

        if args is None:
            _response_counter.labels("token", "no_args").inc()
            return _abort({"error": "invalid_request"})

        try:
            code, client_id, redirect_uri, grant_type, code_verifier = (
                args[arg]
                for arg in [
                    "code",
                    "client_id",
                    "redirect_uri",
                    "grant_type",
                    "code_verifier",
                ]
            )
        except KeyError:
            _response_counter.labels("token", "missing_args").inc()
            return _abort({"error": "invalid_request"})

        if client_id not in config.client_config_map:
            _response_counter.labels("token", "invalid_grant").inc()
            return _abort({"error": "invalid_grant"})

        expiration_time_seconds = config.client_config_map[
            client_id
        ].expiration_time_seconds

        request = front_end_token_service_pb2.TokenFromCodeRequest(
            code=code,
            code_verifier=code_verifier,
            client_id=client_id,
            redirect_uri=redirect_uri,
            grant_type=grant_type,
            expiration_time_seconds=expiration_time_seconds,
        )
        response = front_end_token_service.TokenFromCode(request)
        if response.error:
            _response_counter.labels("token", "invalid_grant").inc()
            return _abort({"error": response.error})
        else:
            _response_counter.labels("token", "success").inc()
            return flask.jsonify(
                {
                    "access_token": response.access_token,
                    "expires_in": response.expires_in,
                    "token_type": "Bearer",
                }
            )

    def _signup_done(is_individual: bool, status: str):
        if is_individual:
            url = _INDIVIDUAL_SIGNUP_DONE_REDIRECT.get(
                base.feature_flags.get_global_context()
            )
        else:
            url = _SIGNUP_DONE_REDIRECT.get(base.feature_flags.get_global_context())

        _signup_callbacks_counter.labels(
            "individual" if is_individual else "open-source", status
        ).inc()

        # Remove once AU-6845 lands
        if status == SignupStatus.YOUR_ORG_HAS_AUGMENT.value:
            status = SignupStatus.IN_ANOTHER_TENANT.value

        logging.info("Redirecting to signup callback %s: %s", url, status)
        return _redirect_to_client_no_transition_page(
            redirect_uri=url,
            query_args={"status": status},
        )

    @app.route("/signup/login", methods=["GET"])
    def signup_login():
        """Redirect to the auth0 signup page."""
        if _DISABLE_SECOND_SIGNUP_FLOW.get(base.feature_flags.get_global_context()):
            return flask.redirect(
                _INDIVIDUAL_SIGNUP_DONE_REDIRECT.get(
                    base.feature_flags.get_global_context()
                )
            )

        is_individual = flask.request.args.get("individual") == "true"

        try:
            if not get_candidate_signup_tenant_list(config, is_individual):
                logging.error("Signup tenant not configured")
                return _signup_done(is_individual, "internal-error")

            credits_available = front_end_token_service.SignupCreditsAvailable(
                front_end_token_service_pb2.SignupCreditsAvailableRequest()
            ).credits_available

            if credits_available < 1:
                return _signup_done(is_individual, "signup-limit-reached")

            in_usa = flask.request.args.get("us") == "true"
            if not is_individual and not in_usa:
                return _signup_done(is_individual, "not-in-usa")

            flask.session["in_usa"] = in_usa
            flask.session["individual"] = is_individual

            if app.config.get("augment_override_authorize_access_token", None):
                return flask.redirect(flask.url_for("signup_callback", _external=True))

            state = secrets.token_urlsafe(16)
            redirect = flask.url_for("signup_callback", _external=True)
            if dev_callback_url is not None:
                state = (
                    base64.urlsafe_b64encode(redirect.encode()).decode() + "." + state
                )
                redirect = dev_callback_url

            redirect = auth0_oauth.auth0_signup.authorize_redirect(  # type: ignore
                redirect_uri=redirect,
                state=state,
            )
            redirect_uri = redirect.headers["Location"]
            if not is_individual:
                redirect_uri += "&ext-plan=community"

            return flask.redirect(
                location=redirect_uri,
                code=302,
            )
        except Exception as e:  # pylint: disable=broad-exception-caught
            logging.exception("Error processing /signup/login: %s", e)
            return _signup_done(is_individual, "internal-error")

    def _is_email_whitelisted(email: str) -> bool:
        whitelist = _SIMILAR_SIGNUPS_WHITELIST_DOMAINS.get(
            base.feature_flags.get_global_context()
        ).split(",")
        if not whitelist:
            return False
        return any(email.lower().endswith("@" + domain.lower()) for domain in whitelist)

    def do_signup_check(
        is_individual: bool, in_usa: bool, idp_user_id: str, email: str
    ) -> tuple[
        User | None,
        tenant_watcher_pb2.Tenant | None,
        SignupStatus | None,
    ]:
        if not is_valid_email(email):
            return (None, None, SignupStatus.INVALID_EMAIL)

        # Check domain restrictions for discovery/vanguard tenants in staging
        if config.allowed_discovery_user_email_regex:
            allowed_discovery_email_match = re.compile(
                config.allowed_discovery_user_email_regex
            )
            if not allowed_discovery_email_match.match(email):
                logging.info(
                    "Blocking signup for email %s - domain not allowed for discovery/vanguard tenants (is_individual=%s)",
                    email,
                    is_individual,
                )
                return (None, None, SignupStatus.INVALID_EMAIL)

        tenant_names = get_candidate_signup_tenant_list(config, is_individual)
        if not tenant_names:
            raise RuntimeError("Signup tenant not configured")
        else:
            tenant_name = get_signup_tenant(tenant_names, idp_user_id)

        signup_tenant = tenant_map.get_tenant(tenant_name)
        if signup_tenant is None:
            raise RuntimeError("Signup tenant not found: %s", tenant_name)

        if tenant_map.get_tenant_for_email_domain(email) is not None:
            return (None, None, SignupStatus.YOUR_ORG_HAS_AUGMENT)

        try:
            response = front_end_token_service.GetUserAndSimilarEmails(
                front_end_token_service_pb2.GetUserAndSimilarEmailsRequest(
                    idp_user_id=idp_user_id,
                    email_address=email,
                )
            )
            user = response.user
        except grpc.RpcError as e:
            logging.error("Failed to get user: %s", e)
            raise

        if len(response.similar_emails) > 0:
            if _ALLOW_SIMILAR_SIGNUPS.get(base.feature_flags.get_global_context()):
                logging.info(
                    "Allowing signup for similar email: %s %s",
                    email,
                    response.similar_emails,
                )
            elif _is_email_whitelisted(email):
                logging.info(
                    "Allowing signup for whitelisted similar email: %s %s",
                    email,
                    response.similar_emails,
                )
            else:
                logging.info(
                    "Blocking signup for similar email: %s %s",
                    email,
                    response.similar_emails,
                )
                return (None, None, SignupStatus.ALREADY_SIGNED_UP)

        if user is not None:
            for tenant_id in user.tenants:
                tenant = tenant_map.get_tenant_by_id(tenant_id)
                if tenant is None:
                    logging.error(
                        "User %s in deleted tenant %s is trying to sign up",
                        user.id,
                        tenant_id,
                    )
                    # We should handle users in deleted tenants more gracefully but
                    # for now we just fail the signup.
                    return (None, None, SignupStatus.INTERNAL_ERROR)
                if tenant_id == signup_tenant.id:
                    return (user, tenant, SignupStatus.ALREADY_SIGNED_UP)
                # TODO: replace hard-coded string with regexp?
                if "discovery" in tenant.name or "vanguard" in tenant.name:
                    return (user, tenant, SignupStatus.ALREADY_SIGNED_UP)
            if len(user.tenants) > 0:
                return (None, None, SignupStatus.IN_ANOTHER_TENANT)

        credits_available = front_end_token_service.SignupCreditsAvailable(
            front_end_token_service_pb2.SignupCreditsAvailableRequest()
        ).credits_available

        if credits_available < 1:
            return (None, None, SignupStatus.SIGNUP_LIMIT_REACHED)

        return (user, signup_tenant, None)

    def do_signup(
        is_individual: bool,
        in_usa: bool,
        idp_user_id: str,
        email: str,
        given_name: str = "",
        family_name: str = "",
    ) -> tuple[
        User | None,
        tenant_watcher_pb2.Tenant | None,
        SignupStatus | None,
    ]:
        (user, tenant, status) = do_signup_check(
            is_individual, in_usa, idp_user_id, email
        )
        if status is not None:
            return (user, tenant, status)

        signup_allowed = front_end_token_service.SignupAllowed(
            front_end_token_service_pb2.SignupAllowedRequest()
        ).allowed
        if not signup_allowed:
            return (user, tenant, SignupStatus.SIGNUP_LIMIT_REACHED)

        assert tenant is not None

        audit_logger.write_audit_log(
            email,
            auth_entities_pb2.UserId.UserIdType.Name(auth_entities_pb2.UserId.AUGMENT),
            resource=audit.User(user_id=email),
            tenant_name=tenant.name,
            message=f"User {email} signed up {is_individual=} {in_usa=} {idp_user_id=}",
        )

        try:
            request = front_end_token_service_pb2.EnsureUserInTenantRequest(
                email_address=email,
                tenant_id=tenant.id,
                idp_user_id=idp_user_id,
                mode=front_end_token_service_pb2.TenantEnsureMode.TENANT_ENSURE_MODE_ADD_IF_EMPTY,  # Only add user to tenant if they're not already in one (expected for new signups)
                given_name=given_name,
                family_name=family_name,
            )
            if user is not None:
                request.augment_user_id = user.id

            user = front_end_token_service.EnsureUserInTenant(request).user
        except grpc.RpcError as e:
            logging.error("Failed to ensure user in tenant: %s", e)

        if user is None:
            raise RuntimeError("Failed to ensure user in tenant")

        ajs_anonymous_id = flask.request.cookies.get("ajs_anonymous_id")
        identify_user(
            user.id,
            {
                "email": user.email,
            },
            ajs_anonymous_id,
        )

        track_event(user.id, "User Signed Up", ajs_anonymous_id)

        return (user, tenant, None)

    @app.route("/signup/callback", methods=["GET"])
    def signup_callback():
        """Callback from the auth0 signup page."""
        if _DISABLE_SECOND_SIGNUP_FLOW.get(base.feature_flags.get_global_context()):
            return flask.redirect(
                _INDIVIDUAL_SIGNUP_DONE_REDIRECT.get(
                    base.feature_flags.get_global_context()
                )
            )

        is_individual = flask.session.get("individual", False)

        try:
            override: dict[str, Any] | OAuthError | None = app.config.get(
                "augment_override_authorize_access_token", None
            )
            if isinstance(override, Exception):
                raise override
            if override is None:
                token = auth0_oauth.auth0_signup.authorize_access_token()  # type: ignore
            else:
                token = override

            if token is None:
                return _signup_done(is_individual, "internal-error")

            userinfo = token.get("userinfo", {})
            idp_user_id = userinfo.get("sub", "")
            email = userinfo.get("email", "")
            if not email:
                return _signup_done(is_individual, "no-email-address")

            if not userinfo.get("email_verified", False):
                return _signup_done(is_individual, "unverified-email-address")

            in_usa = flask.session.get("in_usa", False)
            if not is_individual and not in_usa:
                return _signup_done(is_individual, "not-in-usa")

            (_, _, status) = do_signup(is_individual, in_usa, idp_user_id, email)
            if status is not None:
                return _signup_done(is_individual, status.value)

            del flask.session["in_usa"]

            return _signup_done(is_individual, "success")
        except Exception as e:  # pylint: disable=broad-exception-caught
            logging.exception("Error while signing up user: %s", e)
            return _signup_done(is_individual, "internal-error")

    @app.route("/echo", methods=["GET"])
    def echo():
        return flask.jsonify(flask.request.args)

    @app.route("/api/bootstrap", methods=["GET"])
    def api_bootstrap():
        """Bootstrap endpoint for the frontend.

        This endpoint checks the user's authentication and terms acceptance status,
        and returns appropriate data for the frontend.

        Required query parameters:
        - client_id: OAuth client ID
        - redirect_uri: URI to redirect to after authentication
        - state: OAuth state parameter for CSRF protection
        - code_challenge: PKCE code challenge for OAuth flow

        Returns JSON with:
        - authenticated: Boolean indicating if user is authenticated
        - terms_approved: Boolean indicating if terms are approved (only if authenticated)
        - redirect_url: URL to redirect the user to (only if authenticated and terms approved)
        - terms_type: String indicating terms type ("community" or "enterprise") (only if authenticated and terms not approved)
        - terms_url: String URL to the appropriate terms of service (only if authenticated and terms not approved)
        - tenant_name: String name of the user's tenant (only if authenticated and terms not approved)

        Error responses return HTTP error codes with:
        - message: Error description
        - support_info: Additional context for debugging
        """
        if not _REACT_FRONTEND_ENABLED.get(base.feature_flags.get_global_context()):
            return flask.jsonify({"message": "Not found"}), 404

        try:
            args_dict = dict(flask.request.args.items(True))
            oauth_params, error = extract_oauth_params(args_dict, "query parameters")
            if error:
                return error

            user, user_email, idp_user_id, error = get_authenticated_user(
                require_exists=False
            )
            if error:
                return error

            if not user_email or not idp_user_id:
                return flask.jsonify({"authenticated": False})

            assert oauth_params is not None
            assert user is not None

            tenant_proto, tenant, _ = resolve_tenant_for_user(
                user_email, user, tenant_map
            )

            # Handle cases where no tenant is found
            if not tenant:
                # Check if user is in deleted tenants
                if user is not None and len(user.tenants) > 0 and tenant_proto is None:
                    # User exists but all their tenants are deleted
                    return error_response(
                        "We couldn't find the organization you are part of. Please contact your administrator.",
                        401,
                    )

                # Check if email domain has a tenant
                tenant_from_email = tenant_map.get_tenant_for_email_domain(user_email)
                if tenant_from_email:
                    # Email domain has a tenant - user should be added to existing tenant
                    tenant_details = tenant_map.tenant_to_tenant_details(
                        tenant_from_email
                    )
                    terms_type = (
                        "community" if tenant_details.community_tos else "enterprise"
                    )
                    terms_url = (
                        "https://www.augmentcode.com/terms-of-service/community"
                        if tenant_details.community_tos
                        else "https://www.augmentcode.com/terms-of-service/enterprise"
                    )
                    return flask.jsonify(
                        {
                            "authenticated": True,
                            "terms_approved": False,
                            "email": user_email,
                            "terms_type": terms_type,
                            "terms_url": terms_url,
                            "tenant_name": tenant_details.name,
                            "terms_revision": tenant_details.tnc_revision,
                        }
                    )
                else:
                    # No tenant for email domain - needs signup
                    return flask.jsonify(
                        {
                            "authenticated": True,
                            "terms_approved": False,
                            "needs_signup": True,
                            "email": user_email,
                        }
                    )

            logging.info("User authenticated: %s", user_email)

            assert tenant is not None
            assert tenant_proto is not None

            # Validate identity provider is allowed for this tenant
            if not identity_provider_allowed(
                idp_user_id, tenant.allowed_identity_providers
            ):
                logging.warning("User used blocked identity provider")
                return error_response(
                    "Your organization does not allow login through this method. Please try another method.",
                    401,
                )

            terms = front_end_token_service.GetTermsApproval(
                front_end_token_service_pb2.GetTermsApprovalRequest(
                    email=user_email, revision=tenant.tnc_revision
                )
            )

            if not terms.approved:
                logging.info("Terms not approved: %s", terms)
                terms_type = "community" if tenant.community_tos else "enterprise"
                terms_url = (
                    "https://www.augmentcode.com/terms-of-service/community"
                    if tenant.community_tos
                    else "https://www.augmentcode.com/terms-of-service/enterprise"
                )
                return flask.jsonify(
                    {
                        "authenticated": True,
                        "terms_approved": False,
                        "terms_type": terms_type,
                        "terms_url": terms_url,
                        "tenant_name": tenant.name,
                        "terms_revision": tenant.tnc_revision,
                    }
                )

            code = create_oauth_code_and_update_session(
                user, user_email, idp_user_id, tenant_proto, oauth_params
            )

            redirect_url = build_oauth_redirect_url(oauth_params, code, tenant, config)

            return flask.jsonify(
                {
                    "authenticated": True,
                    "terms_approved": True,
                    "redirect_url": redirect_url,
                }
            )

        except grpc.RpcError as e:
            return handle_grpc_error(e, "bootstrap endpoint")
        except Exception as e:
            return handle_unexpected_error(e, "bootstrap endpoint")

    @app.route("/api/terms/accept", methods=["POST"])
    def api_terms_accept():
        """Accept terms of service for authenticated user.

        This endpoint allows authenticated users to accept the current terms of service
        for their tenant. After successful acceptance, it generates an OAuth authorization
        code and returns the redirect URL to complete the OAuth flow.

        Required JSON body parameters:
        - client_id: OAuth client ID
        - redirect_uri: URI to redirect to after authentication
        - state: OAuth state parameter for CSRF protection
        - code_challenge: PKCE code challenge for OAuth flow
        - accepted: Boolean indicating terms acceptance (must be true)

        Returns JSON with:
        - success: Boolean indicating if terms were accepted successfully
        - redirect_url: URL to redirect the user to (only on success)

        Error responses return HTTP error codes with:
        - message: Error description
        - support_info: Additional context for debugging
        """
        if not _REACT_FRONTEND_ENABLED.get(base.feature_flags.get_global_context()):
            return flask.jsonify({"message": "Not found"}), 404

        try:
            data = flask.request.get_json()
            if not data or not isinstance(data, dict):
                logging.warning(
                    "Invalid JSON request body in terms accept endpoint: %s (type: %s)",
                    data,
                    type(data).__name__ if data is not None else "None",
                )
                return error_response("Invalid JSON request body", 400)

            oauth_params, error = extract_oauth_params(data, "request body")
            if error:
                return error
            if oauth_params is None:
                logging.error("OAuth params unexpectedly None in terms accept endpoint")
                return error_response("Invalid OAuth parameters", 400)

            # Check accepted flag
            accepted = data.get("accepted")
            terms_revision = data.get("terms_revision")

            if accepted is not True:
                return error_response("Terms must be accepted to continue", 400)

            user, user_email, idp_user_id, error = get_authenticated_user(
                require_exists=False
            )
            if error:
                return error

            if not user_email or not idp_user_id:
                return error_response("Authentication required", 401)

            # Check if user's email domain has an existing tenant
            tenant_from_email = tenant_map.get_tenant_for_email_domain(user_email)

            if not user and not tenant_from_email:
                # User doesn't exist and no tenant for their domain
                return error_response("No tenant found for your email domain", 404)

            tenant_proto = None
            tenant = None

            if user and user.tenants:
                # User exists and has tenants - use their current tenant
                tenant_proto, tenant, error = resolve_user_tenant(user_email, user)
                if error:
                    return error
            elif tenant_from_email:
                # User doesn't exist or has no tenants, but email domain has a tenant
                tenant_proto = tenant_from_email
                tenant = tenant_map.tenant_to_tenant_details(tenant_proto)
            else:
                # This shouldn't happen given the checks above, but handle it
                return error_response("No tenant found", 404)

            assert user_email is not None
            assert tenant is not None
            assert tenant_proto is not None
            assert idp_user_id is not None

            if terms_revision and terms_revision != tenant.tnc_revision:
                logging.warning(
                    "Terms revision mismatch for user %s: client sent %s, current is %s",
                    user_email,
                    terms_revision,
                    tenant.tnc_revision,
                )
                return error_response(
                    "Terms have been updated. Please refresh the page to view the latest version.",
                    409,  # Conflict
                )

            # If user doesn't exist or isn't in the tenant, add them
            if not user or tenant_proto.id not in user.tenants:
                # Check if identity provider is allowed
                if not identity_provider_allowed(
                    idp_user_id, tenant.allowed_identity_providers
                ):
                    return error_response(
                        "Your organization does not allow login through this method",
                        403,
                    )

                # Determine the mode for ensuring user in tenant
                ensure_mode = front_end_token_service_pb2.TenantEnsureMode.TENANT_ENSURE_MODE_ADD_IF_EMPTY
                if user and user.tenants:
                    # User already has tenants - we'll overwrite to move them to the email domain tenant
                    ensure_mode = front_end_token_service_pb2.TenantEnsureMode.TENANT_ENSURE_MODE_OVERWRITE

                # Extract user info from session
                given_name = flask.session.get("given_name", "")
                family_name = flask.session.get("family_name", "")

                # Add user to tenant
                logging.info("Adding user %s to tenant %s", user_email, tenant.name)
                try:
                    request = front_end_token_service_pb2.EnsureUserInTenantRequest(
                        email_address=user_email,
                        tenant_id=tenant.tenant_id,
                        idp_user_id=idp_user_id,
                        mode=ensure_mode,
                        given_name=given_name,
                        family_name=family_name,
                    )
                    if user:
                        request.augment_user_id = user.id

                    response = front_end_token_service.EnsureUserInTenant(request)
                    user = response.user
                except grpc.RpcError as e:
                    logging.error("Failed to ensure user in tenant: %s", e)
                    return error_response("Failed to add user to tenant", 500)

            front_end_token_service.SetTermsApproval(
                front_end_token_service_pb2.SetTermsApprovalRequest(
                    email=user_email, revision=tenant.tnc_revision, approved=True
                )
            )

            logging.info(
                "Terms approved for user %s, revision %s",
                user_email,
                tenant.tnc_revision,
            )

            code = create_oauth_code_and_update_session(
                user, user_email, idp_user_id, tenant_proto, oauth_params
            )

            redirect_url = build_oauth_redirect_url(oauth_params, code, tenant, config)

            return flask.jsonify(
                {
                    "success": True,
                    "redirect_url": redirect_url,
                }
            )

        except grpc.RpcError as e:
            return handle_grpc_error(e, "terms accept endpoint")
        except Exception as e:
            return handle_unexpected_error(e, "terms accept endpoint")

    @app.route("/invitations", methods=["GET"])
    def invitations_page():
        """Display the invitations page for the current user."""
        # Check if the invitations feature is enabled
        if not _LOGIN_INVITATIONS_ENABLED.get(base.feature_flags.get_global_context()):
            return flask.abort(404)

        if "user_email" not in flask.session:
            logging.info("No user email in session, redirecting to login")
            return flask.redirect("/login?client_id=invitations")

        user_email = flask.session["user_email"]
        idp_user_id = flask.session["idp_user_id"]
        logging.info(
            f"Invitations page accessed by user_email: {user_email}, idp_user_id: {idp_user_id}"
        )

        continue_url = flask.request.args.get(
            "continue_url", "/terms-accept?client_id=invitations"
        )

        now = datetime.now(timezone.utc)
        try:
            invitations = invitation_service.get_user_invitations(user_email)

            # If the user has no invitations, redirect directly to continue_url
            if not invitations:
                logging.info(
                    f"User {user_email} has no pending invitations, redirecting to {continue_url}"
                )
                return flask.redirect(continue_url)

            is_team_admin = invitation_service.is_team_admin(idp_user_id, user_email)

            return render(
                "invitations.html",
                invitations=invitations,
                continue_url=continue_url,
                is_team_admin=is_team_admin,
            )
        except ValueError as e:
            logging.error(f"Value error: {e}")
            return render(
                "unauthenticated.html",
                details=f"Error occurred at {now}",
            ), 500
        except grpc.RpcError as e:
            logging.error(f"gRPC error: {e}")
            return render(
                "unauthenticated.html",
                details=f"Error occurred at {now}",
            ), 500
        except Exception:
            return render(
                "unauthenticated.html",
                details=f"Error occurred at {now}",
            ), 500

    @app.route("/api/invitation/status/<resolution_id>", methods=["GET"])
    def get_invitation_resolution_status_api(resolution_id: str):
        """API endpoint for checking the status of an invitation resolution.

        Args:
            resolution_id: The ID of the invitation resolution to check

        Returns:
            JSON response with the status of the invitation resolution
        """
        # Check if the invitations feature is enabled
        if not _LOGIN_INVITATIONS_ENABLED.get(base.feature_flags.get_global_context()):
            return flask.jsonify({"error": "Not found"}), 404

        try:
            if "user_email" not in flask.session:
                return flask.jsonify({"error": "Not authenticated"}), 401

            user_email = flask.session["user_email"]
            logging.info(
                f"Checking invitation resolution status for user {user_email}, resolution ID: {resolution_id}"
            )

            try:
                status = invitation_service.get_invitation_resolution_status(
                    resolution_id
                )
                return flask.jsonify(status)
            except ValueError as e:
                logging.error(f"Value error: {e}")
                return flask.jsonify({"error": str(e)}), 400
            except grpc.RpcError as e:
                logging.error(f"gRPC error: {e}")
                return flask.jsonify(
                    {"error": "Failed to retrieve resolution status"}
                ), 500
            except Exception as e:
                logging.error(f"Unexpected error: {e}")
                return flask.jsonify({"error": "An unexpected error occurred"}), 500

        except Exception as e:
            now = datetime.now(timezone.utc)
            logging.exception(
                f"Error processing invitation status API request at {now}: {e}"
            )
            return flask.jsonify({"error": "An unexpected error occurred"}), 500

    @app.route("/api/signup", methods=["POST"])
    def api_signup():
        """REST API endpoint for user sign-up.

        This endpoint handles user sign-up for authenticated users who don't have a tenant.
        It requires the user to be authenticated via Auth0 first.

        Expected JSON payload:
        {
            "terms_accepted": true,
            "terms_revision": "revision_string_here"
        }

        Returns:
        - 201: Sign-up successful
        - 400: Invalid input data
        - 401: Authentication required
        - 403: Sign-up rejected
        - 409: Terms revision mismatch
        - 429: Sign-up limit reached
        - 500: Internal server error
        """
        if not _REACT_FRONTEND_ENABLED.get(base.feature_flags.get_global_context()):
            return flask.jsonify({"message": "Not found"}), 404

        try:
            idp_user_id = flask.session.get("idp_user_id")
            user_email = flask.session.get("user_email")
            given_name = flask.session.get("given_name", "")
            family_name = flask.session.get("family_name", "")

            if not user_email or not idp_user_id:
                logging.warning("Sign-up attempted without authentication")
                return error_response("Authentication required", 401)

            try:
                data = flask.request.get_json(force=True)
            except Exception:
                return error_response("Invalid request body", 400)

            if not data:
                return error_response("Invalid request body", 400)

            terms_accepted = data.get("terms_accepted", False)
            if not terms_accepted:
                return error_response("Terms must be accepted", 400)

            terms_revision = data.get("terms_revision")

            if (
                allow(
                    None,
                    idp_user_id,
                    user_email,
                    "signup",
                    lambda x: _signup_callbacks_counter.labels("individual", x).inc(),
                    sign_up=True,
                )
                != CheckerResult.ALLOWED
            ):
                logging.warning("Sign-up rejected for user: %s", user_email)
                return error_response("Sign-up rejected", 403)

            try:
                credits_available = front_end_token_service.SignupCreditsAvailable(
                    front_end_token_service_pb2.SignupCreditsAvailableRequest()
                ).credits_available

                if credits_available < 1:
                    return flask.jsonify(
                        {
                            "error": "We have reached our signup limit. Please try again later.",
                            "status": SignupStatus.SIGNUP_LIMIT_REACHED.value,
                        }
                    ), 429
            except grpc.RpcError as e:
                logging.error("Failed to check signup credits: %s", e)
                return error_response("Internal server error", 500)

            (user, tenant, status) = do_signup(
                is_individual=True,
                in_usa=False,
                idp_user_id=idp_user_id,
                email=user_email,
                given_name=given_name,
                family_name=family_name,
            )

            _signup_callbacks_counter.labels(
                "individual", status.value if status is not None else "success"
            ).inc()

            if status is None:
                # Success
                assert user is not None
                assert tenant is not None

                # Update session with user information
                flask.session["user_id"] = user.id
                flask.session["nonce"] = user.nonce
                flask.session.permanent = True

                tenant_details = tenant_map.tenant_to_tenant_details(tenant)

                if terms_revision and terms_revision != tenant_details.tnc_revision:
                    logging.warning(
                        "Terms revision mismatch for user %s during signup: client sent %s, current is %s",
                        user_email,
                        terms_revision,
                        tenant_details.tnc_revision,
                    )
                    return error_response(
                        "Terms have been updated. Please refresh the page to view the latest version.",
                        409,  # Conflict
                    )

                try:
                    front_end_token_service.SetTermsApproval(
                        front_end_token_service_pb2.SetTermsApprovalRequest(
                            email=user_email,
                            revision=tenant_details.tnc_revision,
                            approved=True,
                        )
                    )
                    logging.info("Recorded terms acceptance for user %s", user_email)
                except grpc.RpcError as e:
                    logging.error("Failed to record terms approval: %s", e)

                ajs_anonymous_id = flask.request.cookies.get("ajs_anonymous_id")
                identify_user(
                    user.id,
                    {"email": user.email},
                    ajs_anonymous_id,
                )
                track_event(user.id, "User Signed Up", ajs_anonymous_id)

                return flask.jsonify(
                    {
                        "success": True,
                        "message": "Sign-up successful",
                        "user_id": user.id,
                        "tenant_name": tenant.name,
                        "tenant_url": tenant_details.tenant_url,
                    }
                ), 201

            error_messages = {
                SignupStatus.SIGNUP_LIMIT_REACHED: "We have reached our signup limit. Please try again later.",
                SignupStatus.YOUR_ORG_HAS_AUGMENT: "Your email belongs to an organization that already has an Augment account. Please contact your administrator.",
                SignupStatus.IN_ANOTHER_TENANT: "You are already signed up for Augment in a different plan or organization.",
                SignupStatus.ALREADY_SIGNED_UP: "You are already signed up for Augment.",
                SignupStatus.INVALID_EMAIL: "Your email address was missing or invalid.",
            }

            error_message = error_messages.get(
                status, f"Sign-up failed: {status.value}"
            )

            if (
                status == SignupStatus.ALREADY_SIGNED_UP
                and user is not None
                and tenant is not None
            ):
                # User might have signed up in a different flow, treat as success
                flask.session["user_id"] = user.id
                flask.session["nonce"] = user.nonce
                flask.session.permanent = True

                tenant_details = tenant_map.tenant_to_tenant_details(tenant)

                return flask.jsonify(
                    {
                        "success": True,
                        "message": "Already signed up",
                        "user_id": user.id,
                        "tenant_name": tenant.name,
                        "tenant_url": tenant_details.tenant_url,
                    }
                ), 200

            return error_response(error_message, 400)

        except grpc.RpcError as e:
            return handle_grpc_error(e, "signup endpoint")
        except Exception as e:
            return handle_unexpected_error(e, "signup endpoint")

    @app.route("/api/invitation/resolve", methods=["POST"])
    def resolve_invitation_api():
        """API endpoint to accept or decline invitations."""
        # Check if the invitations feature is enabled
        if not _LOGIN_INVITATIONS_ENABLED.get(base.feature_flags.get_global_context()):
            return flask.jsonify({"error": "Not found"}), 404

        try:
            if "user_email" not in flask.session:
                return flask.jsonify({"error": "Not authenticated"}), 401

            user_email = flask.session["user_email"]
            idp_user_id = flask.session.get("idp_user_id")
            if not user_email or not idp_user_id:
                return flask.jsonify({"error": "Not authenticated"}), 401

            data = flask.request.json
            if not data:
                return flask.jsonify({"error": "Invalid request"}), 400

            accept_invitation_id = data.get("accept_invitation_id")
            decline_invitation_ids = data.get("decline_invitation_ids", [])

            if not accept_invitation_id and not decline_invitation_ids:
                return flask.jsonify({"error": "No action specified"}), 400

            user_invitations = invitation_service.get_user_invitations(user_email)
            user_invitation_ids = {inv["id"] for inv in user_invitations}

            if accept_invitation_id and accept_invitation_id not in user_invitation_ids:
                return flask.jsonify({"error": "Invitation not found"}), 404

            invalid_decline_ids = [
                id for id in decline_invitation_ids if id not in user_invitation_ids
            ]
            if invalid_decline_ids:
                return flask.jsonify(
                    {"error": f"Invalid invitation IDs: {invalid_decline_ids}"}
                ), 404

            resolve_request = auth_pb2.ResolveInvitationsRequest(
                accept_invitation_id=accept_invitation_id or "",
                decline_invitation_ids=decline_invitation_ids,
                idp_user_id=idp_user_id,
            )

            try:
                response = front_end_token_service.ResolveInvitations(resolve_request)
                invitation_resolution_id = response.invitation_resolution_id

                if accept_invitation_id:
                    logging.info(
                        f"User {user_email} accepted invitation {accept_invitation_id}"
                    )
                    _invitation_counter.labels(
                        operation="accept", status="success"
                    ).inc()
                    return flask.jsonify(
                        {
                            "success": True,
                            "invitation_resolution_id": invitation_resolution_id,
                        }
                    )
                else:
                    logging.info(
                        f"User {user_email} declined invitations {decline_invitation_ids}"
                    )
                    _invitation_counter.labels(
                        operation="decline", status="success"
                    ).inc(len(decline_invitation_ids))
                    return flask.jsonify(
                        {
                            "success": True,
                            "invitation_resolution_id": invitation_resolution_id,
                        }
                    )

            except grpc.RpcError as e:
                if isinstance(e, grpc.Call) and (
                    e.code() == grpc.StatusCode.NOT_FOUND
                    or e.code() == grpc.StatusCode.ALREADY_EXISTS
                ):
                    logging.info(f"Invitation already processed: {e}")
                    _invitation_counter.labels(
                        operation="resolve", status="already_processed"
                    ).inc()
                    return flask.jsonify({"success": True, "already_processed": True})
                else:
                    logging.error(f"Error resolving invitations: {e}")
                    _invitation_counter.labels(
                        operation="resolve", status="error"
                    ).inc()
                    return flask.jsonify(
                        {"error": "Failed to process invitations"}
                    ), 500

        except Exception as e:
            now = datetime.now(timezone.utc)
            logging.exception(f"Error processing invitation API request at {now}: {e}")
            _invitation_counter.labels(operation="resolve", status="error").inc()
            return flask.jsonify({"error": "An unexpected error occurred"}), 500

    def flag():
        """Endpoint to set feature flags for testing."""
        args = flask.request.get_json() or {}

        react_frontend = args.get("auth_central_react_frontend", False)
        assert isinstance(react_frontend, bool)
        logging.info("Setting auth_central_react_frontend flag to %s", react_frontend)
        _REACT_FRONTEND_ENABLED.set_default(react_frontend)

        # Handle signup tenant flags for testing
        if "auth_central_signup_tenant" in args:
            signup_tenant = args["auth_central_signup_tenant"]
            logging.info("Setting auth_central_signup_tenant flag to %s", signup_tenant)
            _SIGNUP_TENANT.set_default(signup_tenant)

        if "auth_central_individual_tenant" in args:
            individual_tenant = args["auth_central_individual_tenant"]
            logging.info(
                "Setting auth_central_individual_tenant flag to %s", individual_tenant
            )
            _INDIVIDUAL_TENANT.set_default(individual_tenant)

        return flask.jsonify({})

    if config.enable_flag_endpoint:
        app.add_url_rule("/flag", view_func=flag, methods=["POST"])

    if config.auth0_action_endpoint is not None:
        auth0_action_endpoint = Auth0ActionEndpoint(
            config.auth0_action_endpoint, ri_publisher, front_end_token_service
        )
        app.add_url_rule(
            "/api/notify_auth0_action",
            view_func=auth0_action_endpoint.handle_request,
            methods=["POST"],
        )

    def _redirect_to_client(
        redirect_uri: str | None,
        query_args: dict[str, str | None],
        client_name: str | None = None,
    ):
        query_args_filtered = {k: v for k, v in query_args.items() if v is not None}
        final_redirect_uri = None
        if redirect_uri:
            final_redirect_uri = (
                redirect_uri + "&" + urlencode(query_args_filtered)
                if "?" in redirect_uri
                else redirect_uri + "?" + urlencode(query_args_filtered)
            )
        return render(
            "client_redirect.html",
            redirect=final_redirect_uri,
            client_name=client_name,
            error=query_args.get("error"),
            error_description=query_args.get("error_description"),
        )

    def _redirect_to_client_no_transition_page(
        redirect_uri: str,
        query_args: dict[str, str | None],
    ):
        query_args_filtered = {k: v for k, v in query_args.items() if v is not None}
        final_redirect_uri = (
            redirect_uri + "&" + urlencode(query_args_filtered)
            if "?" in redirect_uri
            else redirect_uri + "?" + urlencode(query_args_filtered)
        )
        return flask.redirect(final_redirect_uri)

    def _redirect_to_client_error(
        redirect_uri: str | None,
        error: str,
        error_description: str | None = None,
        client_name: str | None = None,
    ):
        clear_auth_session_state()

        return _redirect_to_client(
            redirect_uri,
            {
                "error": error,
                "state": flask.request.args.get("state"),
                "error_description": error_description,
            },
            client_name=client_name,
        )

    def _remove_port_from_url(url: str):
        parsed_url = urlparse(url)
        # Check if the URL contains a port number
        if parsed_url.port is None:
            # URL doesn't contain a port number, return the original URL
            return url
        # Reconstruct the URL without the port number
        reconstructed_url = (
            parsed_url.scheme + "://" + (parsed_url.hostname or "") + parsed_url.path
        )
        if parsed_url.query:
            reconstructed_url += "?" + parsed_url.query
        if parsed_url.fragment:
            reconstructed_url += "#" + parsed_url.fragment
        return reconstructed_url

    def _abort(json_response: dict[str, str], status_code: int = 400):
        response = flask.jsonify(json_response)
        response.status_code = status_code
        return flask.abort(code=response)

    # Serve React App - this catch-all route must be registered LAST
    @app.route("/", defaults={"path": ""})
    @app.route("/<path:path>")
    def serve_react_app(path: str):
        """Serve the React frontend application.

        This route must be registered after all API routes to ensure Flask's
        routing precedence handles API routes correctly.
        """
        if not _REACT_FRONTEND_ENABLED.get(base.feature_flags.get_global_context()):
            return flask.jsonify({"error": "Not found"}), 404

        # Get the frontend dist directory path
        # In Bazel, the frontend files are in ../frontend/dist relative to the server
        frontend_dist = pathlib.Path(__file__).parent.parent / "frontend" / "dist"

        # Try to serve static files first (JS, CSS, etc.)
        if path and path != "/":
            file_path = frontend_dist / path
            if file_path.exists() and file_path.is_file():
                return flask.send_from_directory(str(frontend_dist), path)

        # For all other routes, serve index.html (SPA routing)
        index_path = frontend_dist / "index.html"
        if index_path.exists():
            return flask.send_from_directory(str(frontend_dist), "index.html")

        return flask.jsonify({"error": "Frontend not found"}), 404

    return app


class _App(BaseApplication):
    """Gunicorn wrapper for service Flask App."""

    # pylint: disable=abstract-method

    def __init__(self, app: flask.Flask, options: dict):
        self.application = app
        self.options = options
        super().__init__()

    def load_config(self):
        assert (
            self.cfg is not None
        )  # cfg is never None here, as it is initialized by Gunicorn. adding check for pylint
        for key, value in self.options.items():
            self.cfg.set(key, value)

    def load(self):
        return self.application


def when_ready(host: str, port: int, server: Any):
    del server
    GunicornPrometheusMetrics.start_http_server_when_ready(host=host, port=port)


def child_exit(server: Any, worker: Any):
    del server
    GunicornPrometheusMetrics.mark_process_dead_on_child_exit(worker.pid)


def post_fork(config: Config, app: Any, server: Any, worker: Any):
    del server
    del worker
    # feature flags uses background threads. Background threads
    # are not inherited across forks.
    path = None
    if config.feature_flags_sdk_key_path is not None:
        path = pathlib.Path(config.feature_flags_sdk_key_path)

    context = base.feature_flags.Context.setup(
        path, config.dynamic_feature_flags_endpoint
    )
    base.feature_flags.set_global_context(context)


def main():
    # tini will handle signals appropriately. In particular, if the tini process
    # (PID 1) gets a SIGTERM, it will shut down the whole container.
    if os.getpid() == 1:
        os.environ[KILL_PID_ON_EXIT_ENV_VAR_NAME] = str(os.getpid())
        os.execv(  # nosec (B606)
            "/usr/bin/tini-static",
            ["/usr/bin/tini-static", "-g", "--", sys.executable] + sys.argv[:],
        )

    base.tracing.setup_opentelemetry()
    setup_struct_logging()
    logging.getLogger("opentelemetry").setLevel(logging.INFO)

    parser = argparse.ArgumentParser()
    parser.add_argument("--config", type=str, required=True)
    parser.add_argument(
        "--disable-secure-cookie-for-testing",
        action="store_true",
        default=False,
        help="For internal testing only.",
    )
    args = parser.parse_args()

    config = Config.load(Path(args.config))
    logging.info("Config %s", config)

    app = create_app(
        config=config,
        secret_key=SecretStr(Path(config.secrets_path).read_text().strip()),
        prometheus=True,
        disable_secure_cookie_for_testing=args.disable_secure_cookie_for_testing,
    )

    prometheus_addr_parts = config.prometheus_bind_address.rsplit(":", 1)
    prometheus_port = int(prometheus_addr_parts[1])
    prometheus_addr = prometheus_addr_parts[0]

    options = {
        "bind": config.public_bind_address,
        "threads": 64,
        "when_ready": functools.partial(when_ready, prometheus_addr, prometheus_port),
        "post_fork": functools.partial(post_fork, config, app),
        "child_exit": child_exit,
    }
    try:
        _App(app, options).run()
    finally:
        backend_channel = grpc.insecure_channel(f"localhost:{config.backend_port}")
        front_end_token_service = (
            front_end_token_service_pb2_grpc.FrontEndTokenServiceStub(backend_channel)
        )
        logging.info("Sending shutdown to front end token service")
        try:
            front_end_token_service.Shutdown(
                front_end_token_service_pb2.ShutdownRequest()
            )
        except grpc.RpcError as e:
            logging.warning("Failed to shutdown front end token service: %s", e)


def is_valid_email(email: str) -> bool:
    """
    Perform very basic email validation.

    This is not meant to be 100% accurate. Its primary usecase is to prevent phone number signups.
    """
    # Check that there's exactly one "@".
    if email.count("@") != 1:
        return False

    # Check that there's at least one "." after the "@".
    if "." not in email.split("@")[1]:
        return False

    return True


if __name__ == "__main__":
    main()
