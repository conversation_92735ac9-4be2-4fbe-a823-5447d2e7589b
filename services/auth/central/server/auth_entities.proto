syntax = "proto3";
package auth_entities;

import "google/protobuf/timestamp.proto";

option go_package = "github.com/augmentcode/augment/services/auth/central/server/auth_entities_proto";

enum CustomerUiRole {
  UNKNOWN_CUSTOMER_UI_ROLE = 0;
  ADMIN = 1;
}

// the user tier is determined by the plan the user is on
// however, the user tier is not the same as the plan
//
// for example, a user might be on the trial plan, but their user tier is still professional
// there might be multiple plans that map to the same user tier
enum UserTier {
  UNKNOWN_TIER = 0;

  // the user is on the community user tier
  // this tier allows training on user data
  COMMUNITY = 1;

  // the user is on a paid self-serve plan. The user might still by in the trial period
  // a better name would potentially have been "Self-Serve Paid", but the string
  // is part of the public API and can't be changed
  PROFESSIONAL = 2;
}

// deprecated
// enum BillingMethod {
//   BILLING_METHOD_UNKNOWN = 0;
//   BILLING_METHOD_STRIPE = 1;
//   BILLING_METHOD_ORB = 2;
//   BILLING_METHOD_MIGRATING_TO_ORB = 3 [deprecated = true];
//   BILLING_METHOD_ORB_WITH_ENDING_STRIPE = 4 [deprecated = true];
// }

enum UserSuspensionType {
  USER_SUSPENSION_TYPE_UNKNOWN = 0;
  // Block account due to API abuse (this blocks the user from logging in)
  USER_SUSPENSION_TYPE_API_ABUSE = 1;
  // Suspend free trial account due to free trial abuse (login allowed, most APIs blocked)
  USER_SUSPENSION_TYPE_FREE_TRIAL_ABUSE = 2;
  // Suspend account due to community abuse (login allowed, most APIs blocked)
  USER_SUSPENSION_TYPE_COMMUNITY_ABUSE = 3;
  // Suspend account using disposable email from free trial
  USER_SUSPENSION_TYPE_DISPOSABLE_EMAIL = 4;
  // Block accont due to payment fraud
  USER_SUSPENSION_TYPE_PAYMENT_FRAUD = 5;
}

// Record of active user suspensions
// If lifting a suspension, remove the corresponding entry from the user.
// Audit log will contain transactions.
message UserSuspension {
  // Unique identifier for the suspension, used to lift the suspension.
  string suspension_id = 1;
  // When the suspension was created.
  google.protobuf.Timestamp created_time = 2;
  // Suspension type
  UserSuspensionType suspension_type = 3;
  // Evidence for the suspension
  string evidence = 4;
}

message User {
  // A UUID generated for the user
  string id = 1;
  // The user's email
  string email = 2 [debug_redact = true];
  // A nonce, for revoking authentication cookies
  uint64 nonce = 3 [debug_redact = true];
  // The user's tenants (in the form of tenant ids)
  repeated string tenants = 4;
  // The user's creation time
  google.protobuf.Timestamp created_at = 5;
  // The user said they were in the USA at this time
  google.protobuf.Timestamp in_usa = 6;
  // The user's Stripe customer ID
  string stripe_customer_id = 7;
  // Tier change information - both fields should be set together or neither set
  message TierChangeInfo {
    string id = 1;
    UserTier target_tier = 2;
    // When the tier change was created
    google.protobuf.Timestamp created_at = 3;
    // When the tier change was last updated
    google.protobuf.Timestamp updated_at = 4;
  }
  // The tier change info - null means no tier change in progress
  optional TierChangeInfo tier_change = 8;
  // The user's stripe subscription ID
  // optional string subscription_id = 9 [deprecated = true];
  reserved 9;

  // User blocked: Login will be denied for this user.
  bool blocked = 10;

  // Identity provider IDs
  repeated string idp_user_ids = 11;
  // The user's orb customer ID
  string orb_customer_id = 12;
  // Deprecated field
  reserved 13;
  // The billing method used by the user
  // BillingMethod billing_method = 14 [deprecated = true];
  reserved 14;
  // The user's orb subscription ID
  string orb_subscription_id = 15;

  // Used to help prevent duplicate subscription creations.
  // Being deprecated in favor of subscription_creation_info.
  string subscription_creation_id = 16 [deprecated = true];

  // Current active suspensions
  repeated UserSuspension suspensions = 17;

  message SubscriptionCreationInfo {
    enum Status {
      // SUCCESS is the default for backwards compatibility reasons. This allows clients to
      // check the status for all users without special-casing users who were created before
      // we had SubscriptionCreationInfo.
      SUCCESS = 0;
      PENDING = 1;
      // Error state isn't included because we should never give up on a subscription
      // creation.
    }
    string id = 1;
    Status status = 2;
    // When the subscription creation info was created
    google.protobuf.Timestamp created_at = 3;
    // When the subscription creation info was last updated
    google.protobuf.Timestamp updated_at = 4;
  }
  SubscriptionCreationInfo subscription_creation_info = 18;

  // Suspension exempt. No suspensions allowed while true.
  bool suspension_exempt = 19;

  enum DeletionState {
    // Currently NOT_DELETED can also mean that the user requested account deletion but did not file
    // a GDPR/CCPA request. We may split out the normal account deletion case in the future.
    NOT_DELETED = 0;
    // Normal account deletion just removes a user from their tenant and cancels their subscription.
    // GDPR_CCPA_DELETED means the user filed a GDPR/CCPA request to be forgotten, so we also have
    // an obligation to stop syncing PII associated with this user throughout the system (except for
    // narrow exceptions like fraud detection and billing).
    GDPR_CCPA_DELETED = 1;
  }
  DeletionState deletion_state = 20;

  // Promotions for this user
  repeated UserPromotion promotions = 21;

  // The given name of the user
  string given_name = 22;
  // The family name of the user
  string family_name = 23;

  // The pending change for this user (if any such change exists)
  optional PendingChange pending_change = 24;

  // Arbitrary tags applied to the user for tracking purposes
  repeated string tags = 25;
}

enum PromotionStatus {
  PROMOTION_STATUS_UNKNOWN = 0;
  PROMOTION_STATUS_ELIGIBLE = 1;
  PROMOTION_STATUS_INELIGIBLE = 2;
  PROMOTION_STATUS_ENROLLED = 3;
}

message UserPromotion {
  // Promotion name
  // Currently, we only support "windsurf_2025"
  string promotion_name = 1;

  // Current status
  PromotionStatus status = 2;

  // When user became enrolled
  google.protobuf.Timestamp enrolled_at = 3;

  // Number of attempts have been made to enroll the user
  int32 enrollment_attempts = 4;
}

// IMPORTANT: UserTenantMapping is keyed by tenant name, not tenant ID!
message UserTenantMapping {
  string tenant = 1;
  string user_id = 2;
  repeated CustomerUiRole customer_ui_roles = 3;
}

// Many to 1 mapping between IDP user ids and Augment user ids.
message IdpUserMapping {
  string idp_user_id = 1;
  string augment_user_id = 2;
}

message TokenHash {
  // we never reconstruct the token itself from this entry, only validate
  string hash = 1 [debug_redact = true];
  // An opaque tenant ID as maintained by tenant watcher
  string tenant_id = 2;

  // User id from identity provider
  string idp_user_id = 3;

  // The user's email address
  string email_address = 4 [debug_redact = true];

  // Augment user ID (from the user object)
  string augment_user_id = 5;

  // When the token was created
  //
  // There may be tokens missing this field because it was added
  // later -  they should have expiration_time_seconds == 0.
  google.protobuf.Timestamp creation_time = 6;

  // The lifetime of the token is seconds,
  // 0 means the token never expires
  int64 expiration_time_seconds = 7;
}

message Code {
  // verification code (a secret)
  string code = 1 [debug_redact = true];
  // User id from identity provider
  string idp_user_id = 2;
  // the user's email (TODO: could this be looked up in the User table instead?)
  string email = 3 [debug_redact = true];
  // A string identifying the client that generated the code, to accomodate
  // differences in the auth flow. Current supported values are
  // "augment-vscode-extension" and "augment-intellij-plugin"
  string client_id = 4;
  // An opaque tenant ID as maintained by tenant watcher
  string tenant_id = 5;
  // The desired URI to redirect to when reopening the client after authorizing
  string redirect_uri = 6;
  // SHA of a secret generated by the client, to validate that the /token call
  // is coming from the same client that generated the code.
  string code_challenge = 7 [debug_redact = true];
  // If true, the code cannot be used to generate a token again
  bool is_used = 8;
  // Epoch seconds, used for code expiration.
  // Better to manage this explicitly than rely on the bigtable timestamp.
  int64 creation_time_seconds = 9;
  // Augment user ID
  string augment_user_id = 10;
}

message TermsApproval {
  // The email of the user that approved the terms
  string email = 1 [debug_redact = true];

  // Whether the user approved the terms
  bool approved = 2;

  // The revision of the terms that the user approved
  string revision = 3;
}

message SignupLimiterState {
  // The number of credits available for signups
  float credits_available = 1;

  // The time when the state was last updated
  int64 last_update_time_seconds = 2;

  // Maximum allowed credits_available
  int64 max_burst = 3;
}

// Wrapper around an opaque user id and a description of what kind of id it is. This is currently
// only used by auth-query's GetTokenInfo and the Request Insight pipeline, to differentiate between
// users logged in with email vs using an API token. There's no reason this couldn't be used
// elsewhere in the auth APIs though.
message UserId {
  enum UserIdType {
    // Unused default.
    UNKNOWN = 0;

    // user_id is a UUID from auth-central's database.
    AUGMENT = 1;

    // user_id is the id of the caller's API token, defined in jsonnet.
    API_TOKEN = 2;

    // user_id is an ID from Slack. Currently this is only relevant to the RI pipeline, not used
    // in the auth service anywhere.
    SLACK = 3;

    // An internal IAP user, usually from one of the internal support sites. The
    // user_id is of the format "iap:<email>".
    // NOTE: this format is expected by libraries that extract this email for audit logging
    INTERNAL_IAP = 4;
  }

  UserIdType user_id_type = 1;
  string user_id = 2;
}

// Wrapper around state information for a tenant creation, triggered by a CreateTenantForTeam RPC.
message TenantCreation {
  enum Status {
    // Unused default.
    UNKNOWN = 0;

    // Tenant creation is in progress.
    PENDING = 1;

    // Tenant creation succeeded. The following are now true:
    // - A new tenant T exists in the same Vanguard/Discovery namespace that the caller was
    //   previously assigned to.
    // - The caller's stripe subscription is associated with tenant T.
    // - The caller is a member of tenant T and no other tenants.
    // - The caller is an admin of tenant T.
    SUCCESS = 2;

    // Generic failure status. Some reasons this could happen include:
    // - The caller belongs to more than one tenant.
    // - The caller is not in a shared Vanguard or Discovery tenant.
    // - The caller has a tier change operation in progress.
    ERROR = 3;

    // We can add more fine-grained statuses if we want the frontend to display more specific
    // error messages.
  }

  // Unique id for the tenant creation request.
  string id = 1;

  // When this TenantCreation was requested. This is NOT the same as the tenant's creation time,
  // though in a perfect world they're close to each other.
  google.protobuf.Timestamp created_at = 2;

  // Status of the tenant creation. See enum documentation.
  Status status = 3;

  // If status is SUCCESS, this is the ID of the created tenant.
  string tenant_id = 4;
}

// Wrapper around state for an invitation to join a tenant. For now we keep all invitations forever;
// someday we may want to GC them.
message TenantInvitation {
  // All invitations start as PENDING. Possible state transitions are:
  // - PENDING -> ACCEPTED
  // - PENDING -> DECLINED
  enum Status {
    // Unused default.
    UNKNOWN = 0;

    // Invitation is open.
    PENDING = 1;

    // Invitation has been accepted.
    ACCEPTED = 2;

    // Invitation has been declined.
    DECLINED = 3;
  }

  // Unique id for the invitation.
  string id = 1;

  // When the invitation was created.
  google.protobuf.Timestamp created_at = 2;

  // The email of the invited user. This is an email and not a user id because it's possible to
  // invite users who haven't yet signed up for Augment.
  string invitee_email = 3 [debug_redact = true];

  // The ID of the tenant the user has been invited to.
  string tenant_id = 4;

  // The Augment user id of the user who sent the invitation.
  string inviter_user_id = 5;

  // The status of the invitation.
  Status status = 6;

  // The email of the user who sent the invitation. This is used in the sign-in flow to show the
  // user who invited them. It's likely we'll eventually migrate to showing a team name instead.
  string inviter_email = 7 [debug_redact = true];
}

// TenantSubscriptionMapping maps a tenant to a subscription
message TenantSubscriptionMapping {
  // The tenant ID
  string tenant_id = 1;

  // The stripe subscription ID
  // string stripe_subscription_id = 2 [deprecated = true];
  reserved 2;

  // When the mapping was created
  google.protobuf.Timestamp created_at = 3;

  // The stripe customer ID associated with this tenant
  string stripe_customer_id = 4;

  // The orb customer ID associated with this tenant
  string orb_customer_id = 5;

  // Billing method used by this tenant
  // BillingMethod billing_method = 6 [deprecated = true];
  reserved 6;

  // The orb subscription ID associated with this tenant
  string orb_subscription_id = 7;

  // Information about a Orb plan change in progress
  message PlanChangeInfo {
    string id = 1;
    string target_orb_plan_id = 2;
    // When the plan change was created
    google.protobuf.Timestamp created_at = 3;
    // When the plan change was last updated
    google.protobuf.Timestamp updated_at = 4;
  }

  optional PlanChangeInfo plan_change = 24;

  // The number of times a new person has joined this team, while the team is on the trial,
  // and the team has been given free credits
  int32 trial_credits_awarded_count = 8;

  // The pending change for this team (if any such change exists)
  optional PendingChange pending_change = 9;
}

// Subscription represents a Stripe / Orb subscription
// Stripe / Orb is the source of truth for subscription data, but we keep a copy in our database to
// avoid rate limiting issues when querying Stripe's API.
message Subscription {
  // Status of a Stripe subscription
  enum Status {
    UNKNOWN = 0;
    INCOMPLETE = 1;
    INCOMPLETE_EXPIRED = 2;
    TRIALING = 3;
    ACTIVE = 4;
    PAST_DUE = 5;
    CANCELED = 6;
    UNPAID = 7;
    PAUSED = 8;
  }

  // Stripe / Orb subscription ID, set for both Stripe and Orb
  string subscription_id = 1;
  // Stripe customer ID, set for both Stripe and Orb
  string stripe_customer_id = 2;
  // Stripe price ID, not set for Orb
  string price_id = 3;
  // TODO: Rename status field to stripe_status
  // Stripe subscription status (active, canceled, etc.), not set for Orb
  Status status = 4;
  // Number of seats (for team subscriptions), set for both Stripe and Orb
  int32 seats = 5;

  // Timestamp for when the subscription started, set for both Stripe and Orb
  google.protobuf.Timestamp start_date = 6;
  // Timestamp for when the trial ends (null if no trial), set for both Stripe and Orb
  google.protobuf.Timestamp trial_end = 7;
  // Timestamp for when the subscription ends, not set for Orb for now
  google.protobuf.Timestamp end_date = 8;
  // Whether the subscription will cancel at the end of the current period, not set for Orb for
  // now
  bool cancel_at_period_end = 9;
  // Whether the customer has a payment method attached, set for both Stripe and Orb
  // For Orb, this field is set only in the stripe event processor, when a setup intent succeeds.
  bool has_payment_method = 10;

  // Owner of the subscription - either a tenant or a user, set for both Stripe and Orb
  oneof owner {
    // Associated tenant ID (for team subscriptions)
    string tenant_id = 11;
    // Associated user ID (for individual subscriptions)
    string user_id = 12;
  }

  // Timestamps generated by our service
  // Creation timestamp, set for both Stripe and Orb
  google.protobuf.Timestamp created_at = 13;
  // Last updated timestamp, set for both Stripe and Orb
  google.protobuf.Timestamp updated_at = 14;

  // Orb customer ID, not set for Stripe
  string orb_customer_id = 15;

  // Status of an Orb subscription
  enum OrbStatus {
    ORB_STATUS_UNKNOWN = 0;
    ORB_STATUS_UPCOMING = 1;
    ORB_STATUS_ACTIVE = 2;
    ORB_STATUS_ENDED = 3;
  }
  // Orb subscription status, not set for Stripe
  OrbStatus orb_status = 16;

  // Whether the user has an unpaid invoice in Orb, not set for Stripe
  bool has_unpaid_invoice = 17;
  // External plan id of the current plan of the Orb subscription, not set for Stripe
  string external_plan_id = 18;

  // Billing method of this subscription, indicating whether this is a Stripe or an Orb
  // subscription. Orb subscription should always set this to BILLING_METHOD_ORB, otherwise it is
  // a Stripe subscription.
  // BillingMethod billing_method = 19 [deprecated = true];
  reserved 19;

  // Orb seats price ID
  string seats_id = 20;
  // Orb included credits price ID
  string included_credits_id = 21;
  // Orb credits per month
  double credits_per_month = 22;

  // Whether the user has used up all their credits for the month
  bool usage_balance_depleted = 23;

  // Subscription change ID. Set when a subscription change is in progress.
  optional string subscription_change_id = 24;

  // Whether the subscription was cancelled due to payment failure
  bool cancelled_due_to_payment_failure = 25;
}

// Wrapper around state for an invitation resolution request.
message InvitationResolution {
  enum Status {
    // Unused default.
    UNKNOWN = 0;

    // Invitation resolution is in progress.
    PENDING = 1;

    // All invitations were successfully accepted or declined.
    SUCCESS = 2;

    // At least one invitation failed to be accepted or declined. It's possible to have a
    // partial success (i.e., successfully decline invites but fail to accept an invite), but it
    // is not possible to enter this state with an invitation acceptance only partially
    // processed.
    ERROR = 3;
  }

  // Unique id for the invitation resolution request.
  string id = 1;

  // When the invitation resolution was requested.
  google.protobuf.Timestamp created_at = 2;

  // The status of the invitation resolution.
  Status status = 3;
}

// Current pending plan changes
message PlanChange {
  enum Status {
    UNKNOWN = 0;
    PENDING = 1; // Plan change is in progress, checkout session has been created
    SUCCESS = 2; // Plan change completed successfully
    PAYMENT_FAILED = 3; // Payment failed for the plan change
  }

  // The status of the plan change.
  Status status = 1;

  // Target Plan to move to
  string target_plan_id = 2;

  // Checkout Session URL
  string checkout_session_url = 3;

  // Total cost for the plan change. String to avoid floating point errors.
  string total_cost = 4;

  // The Orb Pending Change ID -- used to get the pending change from Orb
  optional string orb_pending_change_id = 5;

  // Checkout Session ID for looking up completed payments
  optional string checkout_session_id = 6;

  // Pubsub message id
  optional string message_id = 7;
}

// All pending changes. Currently, only has plan changes, but plan to migrate other pending changes here
message PendingChange {
  // Unique id for the request.
  string id = 1;

  // When the pending change was created
  google.protobuf.Timestamp created_at = 2;

  // When the pending change was last updated
  google.protobuf.Timestamp updated_at = 3;

  oneof change {
    PlanChange plan_change = 4;
  }
}

// Feature gating information for backend-driven feature control
message FeatureGatingInfo {
  repeated FeatureControl feature_controls = 1;

  // If needed, we may add a subscription_type enum field here to indicate what type of subscription the user is on,
  // e.g. if the client need to differentiate trial users versus others in their business logics.
}

// Feature control information
message FeatureControl {
  enum FeatureType {
    UNKNOWN = 0;

    // This is for disabling access and displaying warning messages at "all features" level,
    // e.g. when user has no active subscription.
    ALL_FEATURES = 1;

    // Specific feature types for granular control
    CHAT = 2;
    AGENT = 3;
    REMOTE_AGENT = 4;
    CLI_AGENT = 5;
  }

  enum FeatureState {
    STATE_UNKNOWN = 0;
    ENABLED = 1;
    DISABLED = 2;
  }

  enum FeatureDisabledReason {
    REASON_UNKNOWN = 0;
    INACTIVE_SUBSCRIPTION = 1;
    USAGE_BALANCE_DEPLETED = 2;
  }

  // The feature types this control applies to
  repeated FeatureType feature_types = 1;

  // Whether the feature is enabled or disabled
  FeatureState state = 2;

  // Reason for disabling the feature
  FeatureDisabledReason disabled_reason = 3;

  // The date when the feature access is ending, e.g. subscription end date
  google.protobuf.Timestamp end_date = 4;

  // Display information for the feature control
  FeatureControlDisplayInfo display_info = 5;
}

// Display information for feature controls
// Note that if a feature is disabled, but no display info is provided, it will 402 with no user-facing message.
// TODO: in the future, we make want to add a displaySurfaces field to determine where the message should be displayed (BANNER, INJECTED_MESSAGE, or only PAYMENT_REQUIRED_RESPONSE to 402)
message FeatureControlDisplayInfo {
  // Short warning message e.g. displayed in a banner
  string warning_message = 1;

  // Longer markdown message
  string mkdown_message = 2;

  // Disclaimer added to the end of a normal chat response periodically
  string reminder_suffix = 3;
}
