package main

import (
	"context"
	"fmt"
	"slices"
	"sort"
	"strings"
	"testing"
	"time"

	featureflag "github.com/augmentcode/augment/base/feature_flags"
	"github.com/augmentcode/augment/base/logging/audit"
	auditocsf "github.com/augmentcode/augment/base/logging/audit_ocsf"
	orb_event "github.com/augmentcode/augment/services/auth/billing_webhook/orb_event/proto"
	auth_entities "github.com/augmentcode/augment/services/auth/central/server/auth_entities_proto"
	authpb "github.com/augmentcode/augment/services/auth/central/server/proto"
	"github.com/augmentcode/augment/services/billing/lib/orb"
	orb_config "github.com/augmentcode/augment/services/billing/lib/orb/config"
	stripelib "github.com/augmentcode/augment/services/billing/lib/stripe"
	"github.com/augmentcode/augment/services/lib/grpc/auth"
	ripublisher "github.com/augmentcode/augment/services/request_insight/publisher"
	tw_client "github.com/augmentcode/augment/services/tenant_watcher/client"
	tw_pb "github.com/augmentcode/augment/services/tenant_watcher/proto"
	tokenscopesproto "github.com/augmentcode/augment/services/token_exchange/token_scopes_proto"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
	"github.com/stripe/stripe-go/v80"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/metadata"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/timestamppb"
)

type testContextConfig struct {
	tenantId              string
	scopes                []string
	expectPermissionError bool
}

func createRequestContext(tenantID string, scopes []string) context.Context {
	claims := &auth.AugmentClaims{
		TenantID:       tenantID,
		TenantName:     tenantName,
		ShardNamespace: tenantNamespace,
		Cloud:          "test-cloud",
		Scope:          scopes,
	}
	return claims.NewContext(context.Background())
}

// TODO(jacqueline): We should really test auth for every endpoint, but the way the tests are set up
// right now makes that difficult. I'm leaving that as a task for after refactoring the tests.
func TestAuthCheck(t *testing.T) {
	// Missing claims should fail.
	err := authCheck(context.Background(), tenantID, tokenscopesproto.Scope_AUTH_R)
	require.Error(t, err)
	require.Equal(t, codes.Unknown, status.Code(err))

	// Mismatch in tenant ID should fail.
	ctx := createAuthorizedRequestContext()
	err = authCheck(ctx, "different-tenant-id", tokenscopesproto.Scope_AUTH_R)
	require.Error(t, err)
	require.Equal(t, codes.PermissionDenied, status.Code(err))

	// Empty tenant ID should fail.
	err = authCheck(ctx, "", tokenscopesproto.Scope_AUTH_R)
	require.Error(t, err)
	require.Equal(t, codes.PermissionDenied, status.Code(err))

	// Missing scope should fail.
	ctx = createRequestContext(tenantID, []string{tokenscopesproto.Scope_AUTH_R.String()})
	err = authCheck(ctx, tenantID, tokenscopesproto.Scope_AUTH_RW)
	require.Error(t, err)
	require.Equal(t, codes.PermissionDenied, status.Code(err))

	// Success case.
	err = authCheck(ctx, tenantID, tokenscopesproto.Scope_AUTH_R)
	require.NoError(t, err)

	// Success case with wildcard tenant ID.
	ctx = createCentralAuthorizedRequestContext()
	err = authCheck(ctx, "different-tenant-id", tokenscopesproto.Scope_AUTH_R)
	require.NoError(t, err)

	// Success case with wildcard tenant ID and no tenant ID in request.
	err = authCheck(ctx, "", tokenscopesproto.Scope_AUTH_R)
	require.NoError(t, err)
}

func getUserCount(t *testing.T, ctx context.Context, dao *UserDAO) int {
	count := 0
	err := dao.FindAll(ctx, func(_ *auth_entities.User) bool {
		count++
		return true
	})
	require.NoError(t, err)
	return count
}

type authServicerSUT struct {
	authServicer *AuthGrpcServer
	daoFactory   *DAOFactory
}

func createAuthServicerSUT(t *testing.T, bigtableFixture *BigtableFixture) *authServicerSUT {
	mockClient := &tw_client.MockTenantWatcherClient{
		Tenants: []*tw_pb.Tenant{
			{
				Id:             tenantID,
				Name:           tenantName,
				ShardNamespace: tenantNamespace,
				Cloud:          "test-cloud",
				AuthConfiguration: &tw_pb.AuthConfiguration{
					Domain: "test-tenant1.com",
				},
				Tier: tw_pb.TenantTier_ENTERPRISE,
			},
			{
				Id:             professionalTenantID,
				Name:           "professional-tenant",
				ShardNamespace: "professional-namespace",
				Cloud:          "test-cloud",
				Tier:           tw_pb.TenantTier_PROFESSIONAL,
			},
			{
				Id:             professionalTenantID2,
				Name:           "professional-tenant2",
				ShardNamespace: "professional-namespace",
				Cloud:          "test-cloud",
				Tier:           tw_pb.TenantTier_PROFESSIONAL,
			},
			{
				Id:             testCommunityTenantID,
				Name:           "test-community-tenant",
				ShardNamespace: "community-namespace",
				Cloud:          "test-cloud",
				Tier:           tw_pb.TenantTier_COMMUNITY,
			},
			{
				Id:             testSelfServeTeamTenantID,
				Name:           "self-serve-team-tenant",
				ShardNamespace: "self-serve-team-namespace",
				Cloud:          "test-cloud",
				Tier:           tw_pb.TenantTier_PROFESSIONAL,
				Config: &tw_pb.Config{
					Configs: map[string]string{
						"is_self_serve_team": "true",
					},
				},
			},
			{
				Id:             deletedTenantID,
				Name:           "deleted-tenant",
				ShardNamespace: "deleted-namespace",
				Cloud:          "test-cloud",
				Tier:           tw_pb.TenantTier_PROFESSIONAL,
				DeletedAt:      "2023-01-01T00:00:00Z",
			},
		},
	}

	daoFactory := NewDAOFactory(bigtableFixture.Table)
	ffHandler := featureflag.NewLocalFeatureFlagHandler()

	ffHandler.Set("check_subscription_status", true)
	ffHandler.Set("enforce_usage_credits", true)
	ffHandler.Set("auth_central_enable_feature_gating_info", true)

	mockAsyncOpsPublisher := NewMockAsyncOpsPublisher()
	auditLogger := audit.NewDefaultAuditLogger()

	ocsfAuditLogger, _ := auditocsf.NewMockOCSFAuditLogger()

	authServicer := NewAuthGrpcServer(
		ffHandler,
		daoFactory,
		NewTenantMap(
			daoFactory,
			mockClient,
			"us-central.api.augmentcode.com",
			ffHandler,
			NewMockAsyncOpsPublisher(),
			auditLogger,
		),
		auditLogger,
		ripublisher.NewRequestInsightPublisherMock(),
		mockAsyncOpsPublisher,
		nil, // asyncOpsWorker
		&StripeConfig{Enabled: false},
		&orb_config.OrbConfig{
			Enabled: false, // Disabled for tests to avoid file reading
			Plans: []orb_config.PlanConfig{
				{
					ID: "orb_community_plan",
					Features: orb_config.PlanFeatures{
						PlanType: orb_config.PlanTypeCommunity,
					},
				},
				{
					ID: "orb_trial_plan",
					Features: orb_config.PlanFeatures{
						PlanType: orb_config.PlanTypePaidTrial,
					},
				},
				{
					ID: "orb_developer_plan",
					Features: orb_config.PlanFeatures{
						PlanType: orb_config.PlanTypePaid,
					},
				},
			},
			AccountingConfig: orb_config.SelfServeAccountingConfig{
				Enabled:      true,
				ProviderType: "netsuite",
				ProviderID:   "516",
			},
		},
		stripelib.NewMockStripeClient(),
		NewMockBillingEventProcessor(),
		ocsfAuditLogger,
	)

	return &authServicerSUT{
		authServicer: authServicer,
		daoFactory:   daoFactory,
	}
}

type createUserOpts struct {
	email     string
	id        string
	idpUserID string
}

func (s *authServicerSUT) createUser(t *testing.T, opts createUserOpts) *auth_entities.User {
	// Should we be using AddUserToTenant ?
	id := uuid.New().String()
	if opts.id != "" {
		id = opts.id
	}
	email := fmt.Sprintf("<EMAIL>", id)
	if opts.email != "" {
		email = opts.email
	}

	idpUserIds := []string{}
	if opts.idpUserID != "" {
		idpUserIds = append(idpUserIds, opts.idpUserID)
	}

	user := &auth_entities.User{
		Id:         id,
		Email:      email,
		Tenants:    []string{tenantID},
		IdpUserIds: idpUserIds,
	}
	created, err := s.daoFactory.GetUserDAO().Create(context.Background(), user)
	require.NoError(t, err)
	return created
}

// Create, Add, List, Remove, List
func caseBasicFlow(t *testing.T, bigtableFixture *BigtableFixture) {
	sut := createAuthServicerSUT(t, bigtableFixture)
	authServicer := sut.authServicer

	ctx := createAuthorizedRequestContext()

	userEmail := fmt.Sprintf("<EMAIL>", uuid.New().String())
	request := &authpb.AddUserToTenantRequest{
		Email:    userEmail,
		TenantId: tenantID,
	}

	// Test: Add user
	addUserResponse, err := authServicer.AddUserToTenant(ctx, request)
	require.NoError(t, err)
	assert.Equal(t, userEmail, addUserResponse.User.Email)

	// Test: Add same user again (should not create duplicate)
	addUserResponse2, err := authServicer.AddUserToTenant(ctx, request)
	require.NoError(t, err)
	assert.Equal(t, addUserResponse.User.Id, addUserResponse2.User.Id)

	// Test: List tenant users
	listRequest := &authpb.ListTenantUsersRequest{TenantId: tenantID}
	tenantUsers, err := authServicer.ListTenantUsers(ctx, listRequest)
	require.NoError(t, err)
	assert.Equal(t, 1, len(tenantUsers.Users))
	assert.Equal(t, userEmail, tenantUsers.Users[0].Email)

	// Test: Remove user from tenant
	removeRequest := &authpb.RemoveUserFromTenantRequest{
		UserId:   addUserResponse.User.Id,
		TenantId: tenantID,
	}
	_, err = authServicer.RemoveUserFromTenant(ctx, removeRequest)
	require.NoError(t, err)

	// Test: List users after removal
	tenantUsers, err = authServicer.ListTenantUsers(ctx, listRequest)
	require.NoError(t, err)
	assert.Equal(t, 0, len(tenantUsers.Users))
}

func caseTokenLookup(t *testing.T, bigtableFixture *BigtableFixture) {
	sut := createAuthServicerSUT(t, bigtableFixture)
	authServicer := sut.authServicer

	tokenHashDAO := sut.daoFactory.GetTokenHashDAO()
	ctx := createAuthorizedRequestContext()

	// Populate with a sample entry
	// sha256 of "hello"
	tokenHash := &auth_entities.TokenHash{
		Hash:          "2cf24dba5fb0a30e26e83b2ac5b9e29e1b161e5c1fa7425e73043362938b9824",
		AugmentUserId: "test-user-id",
		EmailAddress:  "<EMAIL>",
		TenantId:      tenantID,
	}
	_, err := tokenHashDAO.Create(ctx, tokenHash)
	require.NoError(t, err)

	// Verify the token hash exists
	stored, err := tokenHashDAO.Get(ctx, tokenHash.Hash)
	require.NoError(t, err)
	require.Equal(t, tokenHash.AugmentUserId, stored.AugmentUserId)

	// Create user
	userDao := sut.daoFactory.GetUserDAO()
	user := &auth_entities.User{
		Id:            "test-user-id",
		Email:         "<EMAIL>",
		Tenants:       []string{tenantID},
		OrbCustomerId: "orb-customer-123",
	}
	_, err = userDao.Create(ctx, user)
	require.NoError(t, err)

	// Create context with required metadata for gRPC call

	// Token matching the entry should succeed
	request := &authpb.GetTokenInfoRequest{Token: "hello"}
	response, err := authServicer.GetTokenInfo(ctx, request)
	require.NoError(t, err)
	assert.Equal(t, "<EMAIL>", response.UserId)
	assert.Equal(t, "test-user-id", response.AugmentUserId)
	assert.Equal(t, "<EMAIL>", response.UserEmail)
	assert.Equal(t, tenantID, response.TenantId)
	assert.Equal(t, tenantName, response.TenantName)
	// Check enterprise subscription
	enterpriseSub, ok := response.Subscription.(*authpb.GetTokenInfoResponse_Enterprise)
	require.True(t, ok)
	require.NotNil(t, enterpriseSub.Enterprise)

	// Nonexistent token should fail
	request = &authpb.GetTokenInfoRequest{Token: "world"}
	_, err = authServicer.GetTokenInfo(ctx, request)
	require.Error(t, err)
	require.Equal(t, codes.NotFound, status.Code(err))
}

func TestUpdateUser(t *testing.T) {
	bigtableFixture := NewBigtableFixture(t)
	defer bigtableFixture.Cleanup()

	sut := createAuthServicerSUT(t, bigtableFixture)
	authServicer := sut.authServicer

	ctx := createAuthorizedRequestContext()

	userEmail := fmt.Sprintf("<EMAIL>", uuid.New().String())
	request := &authpb.AddUserToTenantRequest{
		Email:    userEmail,
		TenantId: tenantID,
	}

	// Test: Add user
	addUserResponse, err := authServicer.AddUserToTenant(ctx, request)
	require.NoError(t, err)
	assert.Equal(t, userEmail, addUserResponse.User.Email)

	// Test: Get user on tenant
	userOnTenantRequest := &authpb.GetUserOnTenantRequest{
		UserId:   addUserResponse.User.Id,
		TenantId: tenantID,
	}
	userOnTenantResponse, err := authServicer.GetUserOnTenant(ctx, userOnTenantRequest)
	require.NoError(t, err)
	assert.Equal(t, 0, len(userOnTenantResponse.CustomerUiRoles))

	// Test: Update user on tenant
	updateRequest := &authpb.UpdateUserOnTenantRequest{
		UserId:   addUserResponse.User.Id,
		TenantId: tenantID,
		CustomerUiRoles: []auth_entities.CustomerUiRole{
			auth_entities.CustomerUiRole_ADMIN,
		},
	}
	updateResponse, err := authServicer.UpdateUserOnTenant(ctx, updateRequest)
	require.NoError(t, err)
	assert.Equal(t, []auth_entities.CustomerUiRole{auth_entities.CustomerUiRole_ADMIN}, updateResponse.CustomerUiRoles)

	// Test: Get user on tenant after update
	userOnTenantResponse, err = authServicer.GetUserOnTenant(ctx, userOnTenantRequest)
	require.NoError(t, err)
	assert.Equal(t, 1, len(userOnTenantResponse.CustomerUiRoles))
	assert.Equal(t, auth_entities.CustomerUiRole_ADMIN, userOnTenantResponse.CustomerUiRoles[0])

	// Test: Update user on tenant with no roles
	updateRequest = &authpb.UpdateUserOnTenantRequest{
		UserId:   addUserResponse.User.Id,
		TenantId: tenantID,
	}
	_, err = authServicer.UpdateUserOnTenant(ctx, updateRequest)
	require.NoError(t, err)

	// Test: Get user on tenant after update with no roles
	userOnTenantResponse, err = authServicer.GetUserOnTenant(ctx, userOnTenantRequest)
	require.NoError(t, err)
	assert.Equal(t, 0, len(userOnTenantResponse.CustomerUiRoles))
}

func TestDoubleDeletionNotFound(t *testing.T) {
	bigtableFixture := NewBigtableFixture(t)
	defer bigtableFixture.Cleanup()

	sut := createAuthServicerSUT(t, bigtableFixture)
	authServicer := sut.authServicer

	ctx := createAuthorizedRequestContext()

	request := &authpb.RemoveUserFromTenantRequest{
		UserId:   "not-found",
		TenantId: tenantID,
	}
	_, err := authServicer.RemoveUserFromTenant(ctx, request)
	require.Error(t, err)
	require.Equal(t, codes.NotFound, status.Code(err))
}

func TestRevokeUserCookie(t *testing.T) {
	bigtableFixture := NewBigtableFixture(t)
	defer bigtableFixture.Cleanup()

	sut := createAuthServicerSUT(t, bigtableFixture)
	authServicer := sut.authServicer
	daoFactory := sut.daoFactory

	ctx := createAuthorizedRequestContext()

	userDao := daoFactory.GetUserDAO()

	// Populate with a sample entry
	startUserCount := getUserCount(t, ctx, userDao)

	userEmail := fmt.Sprintf("<EMAIL>", uuid.New().String())
	request := &authpb.AddUserToTenantRequest{
		Email:    userEmail,
		TenantId: tenantID,
	}

	// Test: Add user
	addUserResponse, err := authServicer.AddUserToTenant(ctx, request)
	require.NoError(t, err)
	assert.Equal(t, userEmail, addUserResponse.User.Email)

	// Test: Add duplicate user
	_, err = authServicer.AddUserToTenant(ctx, request)
	require.NoError(t, err)
	currentUserCount := getUserCount(t, ctx, userDao)
	assert.Equal(t, startUserCount+1, currentUserCount)

	// Test: Explicitly revoke the cookie
	user, err := userDao.Get(ctx, addUserResponse.User.Id)
	require.NoError(t, err)
	prevNonce := user.Nonce
	_, err = authServicer.RevokeUserCookies(ctx, &authpb.RevokeUserCookiesRequest{
		UserId:   addUserResponse.User.Id,
		TenantId: tenantID,
	})
	require.NoError(t, err)
	user, err = userDao.Get(ctx, addUserResponse.User.Id)
	require.NoError(t, err)
	nonce := user.Nonce
	assert.NotEqual(t, prevNonce, nonce)

	// Test: Remove user from tenant
	prevNonce = nonce
	_, err = authServicer.RemoveUserFromTenant(ctx, &authpb.RemoveUserFromTenantRequest{
		UserId:   addUserResponse.User.Id,
		TenantId: tenantID,
	})
	require.NoError(t, err)
	user, err = userDao.Get(ctx, addUserResponse.User.Id)
	require.NoError(t, err)
	nonce = user.Nonce
	assert.NotEqual(t, prevNonce, nonce)
}

func TestCreateUserSuspensionWithDifferentBillingMethods(t *testing.T) {
	bigtableFixture := NewBigtableFixture(t)
	defer bigtableFixture.Cleanup()

	sut := createAuthServicerSUT(t, bigtableFixture)
	authServicer := sut.authServicer
	daoFactory := sut.daoFactory
	userDAO := daoFactory.GetUserDAO()
	subscriptionDAO := daoFactory.GetSubscriptionDAO()
	ctx := createCentralAuthorizedRequestContext()

	// Test cases for Orb billing (all users are now on Orb)
	testCases := []struct {
		name                string
		orbSubId            string
		hasPaymentMethod    bool
		expectError         bool
		errorContainsString string
	}{
		{
			name:                "Orb user with subscription and no payment method",
			orbSubId:            "orb-sub-123",
			hasPaymentMethod:    false,
			expectError:         false,
			errorContainsString: "",
		},
		{
			name:                "Orb user with subscription and payment method",
			orbSubId:            "orb-sub-124",
			hasPaymentMethod:    true,
			expectError:         true,
			errorContainsString: "User has a payment method configured",
		},
		{
			name:                "Orb user without subscription",
			orbSubId:            "",
			hasPaymentMethod:    false,
			expectError:         true,
			errorContainsString: "Orb user does not have a subscription",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Create a user with Orb subscription ID
			userId := uuid.New().String()
			user := &auth_entities.User{
				Id:                userId,
				Email:             fmt.Sprintf("<EMAIL>", userId),
				Tenants:           []string{professionalTenantID},
				OrbSubscriptionId: tc.orbSubId,
			}
			_, err := userDAO.Create(ctx, user)
			require.NoError(t, err)

			// Create an Orb subscription for the user if they have one
			if tc.orbSubId != "" {
				subscription := &auth_entities.Subscription{
					SubscriptionId:   tc.orbSubId,
					Status:           auth_entities.Subscription_TRIALING,
					HasPaymentMethod: tc.hasPaymentMethod,
					ExternalPlanId:   "orb_trial_plan",
				}
				_, err := subscriptionDAO.Create(ctx, subscription)
				require.NoError(t, err)
			}

			// Try to create a suspension for the user
			_, err = authServicer.CreateUserSuspension(ctx, &authpb.CreateUserSuspensionRequest{
				UserId:         userId,
				TenantId:       tenantID,
				SuspensionType: auth_entities.UserSuspensionType_USER_SUSPENSION_TYPE_FREE_TRIAL_ABUSE,
				Evidence:       "Test evidence",
			})

			if tc.expectError {
				require.Error(t, err)
				if tc.errorContainsString != "" {
					require.Contains(t, err.Error(), tc.errorContainsString)
				}
			} else {
				require.NoError(t, err)

				// Verify the suspension was created
				user, err := userDAO.Get(ctx, userId)
				require.NoError(t, err)
				require.Equal(t, 1, len(user.Suspensions))
				require.Equal(t, auth_entities.UserSuspensionType_USER_SUSPENSION_TYPE_FREE_TRIAL_ABUSE, user.Suspensions[0].SuspensionType)
			}
		})
	}
}

func TestRevokeUser(t *testing.T) {
	bigtableFixture := NewBigtableFixture(t)
	defer bigtableFixture.Cleanup()

	sut := createAuthServicerSUT(t, bigtableFixture)
	authServicer := sut.authServicer

	ctx := createAuthorizedRequestContext()

	userEmail := fmt.Sprintf("<EMAIL>", uuid.New().String())
	request := &authpb.AddUserToTenantRequest{
		Email:    userEmail,
		TenantId: tenantID,
	}

	// Test: Add user
	addUserResponse, err := authServicer.AddUserToTenant(ctx, request)
	require.NoError(t, err)
	assert.Equal(t, userEmail, addUserResponse.User.Email)

	tokenHashDAO := sut.daoFactory.GetTokenHashDAO()

	// Populate with a sample token
	// sha256 of "hello"
	tokenHash := &auth_entities.TokenHash{
		Hash:          "2cf24dba5fb0a30e26e83b2ac5b9e29e1b161e5c1fa7425e73043362938b9824",
		AugmentUserId: addUserResponse.User.Id,
		TenantId:      tenantID,
	}
	_, err = tokenHashDAO.Create(ctx, tokenHash)
	require.NoError(t, err)

	// Verify the token hash exists
	stored, err := tokenHashDAO.Get(ctx, tokenHash.Hash)
	require.NoError(t, err)
	require.Equal(t, tokenHash.AugmentUserId, stored.AugmentUserId)

	userDao := sut.daoFactory.GetUserDAO()

	// Get current user nonce
	user, err := userDao.Get(ctx, addUserResponse.User.Id)
	require.NoError(t, err)
	prevNonce := user.Nonce

	// Test: Get user on tenant
	userOnTenantRequest := &authpb.GetUserOnTenantRequest{
		UserId:   addUserResponse.User.Id,
		TenantId: tenantID,
	}
	_, err = authServicer.GetUserOnTenant(ctx, userOnTenantRequest)
	require.NoError(t, err)

	// Test: Revoke user
	revokeResponse, err := authServicer.RevokeUser(ctx, &authpb.RevokeUserRequest{
		Email:    userEmail,
		TenantId: tenantID,
	})
	require.NoError(t, err)
	assert.Equal(t, int32(1), revokeResponse.TokensDeleted)

	// Test: nonce changed
	user, err = userDao.Get(ctx, addUserResponse.User.Id)
	require.NoError(t, err)
	nonce := user.Nonce
	assert.NotEqual(t, prevNonce, nonce)

	// Test: Token hash deleted
	stored, err = tokenHashDAO.Get(ctx, tokenHash.Hash)
	require.NoError(t, err)
	require.Nil(t, stored)

	// User access has been revoked, but user still assigned to tenant.
	userOnTenantRequest = &authpb.GetUserOnTenantRequest{
		UserId:   addUserResponse.User.Id,
		TenantId: tenantID,
	}
	userOnTenantResponse, err := authServicer.GetUserOnTenant(ctx, userOnTenantRequest)
	require.NoError(t, err)
	assert.Equal(t, 0, len(userOnTenantResponse.CustomerUiRoles))
}

func TestUpdateUserEmail(t *testing.T) {
	bigtableFixture := NewBigtableFixture(t)
	defer bigtableFixture.Cleanup()

	sut := createAuthServicerSUT(t, bigtableFixture)
	authServicer := sut.authServicer
	daoFactory := sut.daoFactory

	ctx := createAuthorizedRequestContext()
	userDao := daoFactory.GetUserDAO()
	tokenHashDAO := daoFactory.GetTokenHashDAO()

	userEmail := fmt.Sprintf("<EMAIL>", uuid.New().String())
	request := &authpb.AddUserToTenantRequest{
		Email:    userEmail,
		TenantId: tenantID,
	}

	// Test: Add user
	addUserResponse, err := authServicer.AddUserToTenant(ctx, request)
	require.NoError(t, err)
	assert.Equal(t, userEmail, addUserResponse.User.Email)
	userId := addUserResponse.User.Id

	// Create a token hash for the user
	tokenHash := &auth_entities.TokenHash{
		Hash:          "2cf24dba5fb0a30e26e83b2ac5b9e29e1b161e5c1fa7425e73043362938b9824",
		AugmentUserId: userId,
		TenantId:      tenantID,
		EmailAddress:  userEmail,
	}
	_, err = tokenHashDAO.Create(ctx, tokenHash)
	require.NoError(t, err)

	// Test cases
	testCases := []struct {
		name          string
		userId        string
		currentEmail  string
		newEmail      string
		expectError   bool
		errorCode     codes.Code
		errorContains string
	}{
		{
			name:         "Success - Valid email update",
			userId:       userId,
			currentEmail: userEmail,
			newEmail:     fmt.Sprintf("<EMAIL>", uuid.New().String()),
			expectError:  false,
		},
		{
			name:          "Error - Current email doesn't match",
			userId:        userId,
			currentEmail:  "<EMAIL>",
			newEmail:      "<EMAIL>",
			expectError:   true,
			errorCode:     codes.InvalidArgument,
			errorContains: "Current email doesn't match",
		},
		{
			name:          "Error - User not found",
			userId:        "non-existent-user-id",
			currentEmail:  userEmail,
			newEmail:      "<EMAIL>",
			expectError:   true,
			errorCode:     codes.NotFound,
			errorContains: "User not found",
		},
	}

	changeEmailCtx := createCentralAuthorizedRequestContext()
	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Test: Update user email
			_, err = authServicer.UpdateUserEmail(changeEmailCtx, &authpb.UpdateUserEmailRequest{
				UserId:       tc.userId,
				CurrentEmail: tc.currentEmail,
				NewEmail:     tc.newEmail,
			})

			if tc.expectError {
				require.Error(t, err)
				require.Equal(t, tc.errorCode, status.Code(err))
				require.Contains(t, err.Error(), tc.errorContains)
			} else {
				require.NoError(t, err)

				// Verify user email was updated
				user, err := userDao.Get(ctx, tc.userId)
				require.NoError(t, err)
				assert.Equal(t, tc.newEmail, user.Email)
			}
		})
	}
}

func TestAuthServiceDeleteUserInDeletedTenant(t *testing.T) {
	bigtableFixture := NewBigtableFixture(t)
	defer bigtableFixture.Cleanup()

	tenantChangeChannel := make(chan tw_client.TenantChange, 1)

	mockClient := &tw_client.MockTenantWatcherClient{
		Changes: []chan tw_client.TenantChange{
			tenantChangeChannel,
		},
	}

	mockTenant := tw_pb.Tenant{
		Id:             tenantID,
		Name:           tenantName,
		ShardNamespace: tenantNamespace,
		Cloud:          "test-cloud",
	}
	mockTenantVersion1 := mockTenant

	tenantChangeChannel <- tw_client.TenantChange{
		Response: &tw_pb.WatchTenantsResponse{
			Tenants: []*tw_pb.TenantChange{
				{
					Type: &tw_pb.TenantChange_Updated{
						Updated: &tw_pb.TenantUpdate{
							Tenant: &mockTenantVersion1,
						},
					},
				},
			},
			IsInitial: true,
			Sync:      true,
		},
	}
	auditLogger := audit.NewDefaultAuditLogger()
	ocsfAuditLogger, _ := auditocsf.NewMockOCSFAuditLogger()

	daoFactory := NewDAOFactory(bigtableFixture.Table)
	tenantMap := NewTenantMap(
		daoFactory,
		mockClient,
		"us-central.api.augmentcode.com",
		featureflag.NewLocalFeatureFlagHandler(),
		NewMockAsyncOpsPublisher(),
		auditLogger,
	)

	authServicer := NewAuthGrpcServer(
		featureflag.NewLocalFeatureFlagHandler(),
		daoFactory,
		tenantMap,
		auditLogger,
		ripublisher.NewRequestInsightPublisherMock(),
		NewMockAsyncOpsPublisher(),
		nil, // asyncOpsWorker
		&StripeConfig{Enabled: false},
		&orb_config.OrbConfig{Enabled: false},
		stripelib.NewMockStripeClient(),
		nil, // billingEventProcessor
		ocsfAuditLogger,
	)

	ctx := createAuthorizedRequestContext()

	userEmail := fmt.Sprintf("<EMAIL>", uuid.New().String())
	request := &authpb.AddUserToTenantRequest{
		Email:    userEmail,
		TenantId: tenantID,
	}

	// Test: Add user
	addUserResponse, err := authServicer.AddUserToTenant(ctx, request)
	require.NoError(t, err)
	assert.Equal(t, userEmail, addUserResponse.User.Email)

	// Delete tenant - note this will be processed asynchronously
	mockTenant.DeletedAt = "2023-01-01T00:00:00Z"
	mockTenantVersion2 := mockTenant

	tenantChangeChannel <- tw_client.TenantChange{
		Response: &tw_pb.WatchTenantsResponse{
			Tenants: []*tw_pb.TenantChange{
				{
					Type: &tw_pb.TenantChange_Updated{
						Updated: &tw_pb.TenantUpdate{
							Tenant: &mockTenantVersion2,
						},
					},
				},
			},
		},
	}

	// Wait until tenant map sees the update
	retries := 0
	backoff := 10 * time.Millisecond
	for {
		tenant, err := tenantMap.GetTenantByID(ctx, tenantID)
		require.NoError(t, err)
		if tenant == nil {
			break
		}
		if retries > 10 {
			require.Fail(t, "timed out waiting for tenant deletion to be observed")
			break
		}
		backoff = min(backoff*2, 1000*time.Millisecond)
		time.Sleep(backoff)
		retries++
	}

	// Test: List tenant users should still work
	listRequest := &authpb.ListTenantUsersRequest{TenantId: tenantID}
	_, err = authServicer.ListTenantUsers(ctx, listRequest)
	require.NoError(t, err)

	// Test: remove user from tenant even though it's deleted
	_, err = authServicer.RemoveUserFromTenant(ctx, &authpb.RemoveUserFromTenantRequest{
		UserId:   addUserResponse.User.Id,
		TenantId: tenantID,
	})
	require.NoError(t, err)
}

func TestAuthServiceGetTokenInfoDeletedTenant(t *testing.T) {
	bigtableFixture := NewBigtableFixture(t)
	defer bigtableFixture.Cleanup()

	tenantChangeChannel := make(chan tw_client.TenantChange, 1)

	mockClient := &tw_client.MockTenantWatcherClient{
		Changes: []chan tw_client.TenantChange{
			tenantChangeChannel,
		},
	}

	mockTenant := tw_pb.Tenant{
		Id:             tenantID,
		Name:           tenantName,
		ShardNamespace: tenantNamespace,
		Cloud:          "test-cloud",
		Tier:           tw_pb.TenantTier_ENTERPRISE,
	}
	mockTenantVersion1 := mockTenant

	tenantChangeChannel <- tw_client.TenantChange{
		Response: &tw_pb.WatchTenantsResponse{
			Tenants: []*tw_pb.TenantChange{
				{
					Type: &tw_pb.TenantChange_Updated{
						Updated: &tw_pb.TenantUpdate{
							Tenant: &mockTenantVersion1,
						},
					},
				},
			},
			IsInitial: true,
			Sync:      true,
		},
	}

	ffHandler := featureflag.NewLocalFeatureFlagHandler()
	ffHandler.Set("check_subscription_status", true)
	ffHandler.Set("enforce_usage_credits", true)
	auditLogger := audit.NewDefaultAuditLogger()
	ocsfAuditLogger, _ := auditocsf.NewMockOCSFAuditLogger()

	daoFactory := NewDAOFactory(bigtableFixture.Table)
	tenantMap := NewTenantMap(
		daoFactory,
		mockClient,
		"us-central.api.augmentcode.com",
		ffHandler,
		NewMockAsyncOpsPublisher(),
		auditLogger,
	)

	authServicer := NewAuthGrpcServer(
		ffHandler,
		daoFactory,
		tenantMap,
		auditLogger,
		ripublisher.NewRequestInsightPublisherMock(),
		NewMockAsyncOpsPublisher(),
		nil, // asyncOpsWorker
		&StripeConfig{Enabled: false},
		&orb_config.OrbConfig{Enabled: false},
		stripelib.NewMockStripeClient(),
		nil, // billingEventProcessor
		ocsfAuditLogger,
	)

	ctx := createAuthorizedRequestContext()

	userEmail := fmt.Sprintf("<EMAIL>", uuid.New().String())
	request := &authpb.AddUserToTenantRequest{
		Email:    userEmail,
		TenantId: tenantID,
	}

	// Test: Add user
	addUserResponse, err := authServicer.AddUserToTenant(ctx, request)
	require.NoError(t, err)
	assert.Equal(t, userEmail, addUserResponse.User.Email)

	tokenHashDAO := daoFactory.GetTokenHashDAO()

	// Populate with a sample token
	// sha256 of "hello"
	tokenHash := &auth_entities.TokenHash{
		Hash:          "2cf24dba5fb0a30e26e83b2ac5b9e29e1b161e5c1fa7425e73043362938b9824",
		AugmentUserId: addUserResponse.User.Id,
		TenantId:      tenantID,
		EmailAddress:  userEmail,
	}
	_, err = tokenHashDAO.Create(ctx, tokenHash)
	require.NoError(t, err)

	// Verify that we can get the token info
	getTokenInfoResponse, err := authServicer.GetTokenInfo(ctx, &authpb.GetTokenInfoRequest{
		Token: "hello",
	})
	require.NoError(t, err)
	assert.Equal(t, addUserResponse.User.Email, getTokenInfoResponse.UserId)
	assert.Equal(t, addUserResponse.User.Id, getTokenInfoResponse.AugmentUserId)
	assert.Equal(t, addUserResponse.User.Email, getTokenInfoResponse.UserEmail)
	assert.Equal(t, tenantID, getTokenInfoResponse.TenantId)
	assert.Equal(t, tenantName, getTokenInfoResponse.TenantName)
	// Check enterprise subscription
	enterpriseSub, ok := getTokenInfoResponse.Subscription.(*authpb.GetTokenInfoResponse_Enterprise)
	require.True(t, ok)
	require.NotNil(t, enterpriseSub.Enterprise)

	// Delete tenant - note this will be processed asynchronously
	mockTenant.DeletedAt = "2023-01-01T00:00:00Z"
	mockTenantVersion2 := mockTenant

	tenantChangeChannel <- tw_client.TenantChange{
		Response: &tw_pb.WatchTenantsResponse{
			Tenants: []*tw_pb.TenantChange{
				{
					Type: &tw_pb.TenantChange_Updated{
						Updated: &tw_pb.TenantUpdate{
							Tenant: &mockTenantVersion2,
						},
					},
				},
			},
		},
	}

	// Wait until tenant map sees the update
	retries := 0
	backoff := 10 * time.Millisecond
	for {
		tenant, err := tenantMap.GetTenantByID(ctx, tenantID)
		require.NoError(t, err)
		if tenant == nil {
			break
		}
		if retries > 10 {
			require.Fail(t, "timed out waiting for tenant deletion to be observed")
			break
		}
		backoff = min(backoff*2, 1000*time.Millisecond)
		time.Sleep(backoff)
		retries++
	}

	// Assert that GetTokenInfo returns 401 after tenant delete
	getTokenInfoResponse, err = authServicer.GetTokenInfo(ctx, &authpb.GetTokenInfoRequest{
		Token: "hello",
	})
	require.Error(t, err)
	assert.Equal(t, codes.NotFound, status.Code(err))
}

func TestAuthServiceGetTokenInfoWithSubscription(t *testing.T) {
	bigtableFixture := NewBigtableFixture(t)
	defer bigtableFixture.Cleanup()

	trialEndTime := time.Now().Add(24 * time.Hour)

	// Define test cases
	tests := []struct {
		name                 string
		tenantTier           tw_pb.TenantTier
		subscriptionStatus   auth_entities.Subscription_OrbStatus
		hasSubscription      bool
		expectedSubscription interface{} // Will hold the expected oneof type
		expectedTrialEnd     *timestamppb.Timestamp
		isTrialPlan          bool // Whether to set external_plan_id to "orb-trial-plan"
		// Feature gating expectations
		expectedFeatureControls []ExpectedFeatureControl
	}{
		{
			name:            "Enterprise tenant",
			tenantTier:      tw_pb.TenantTier_ENTERPRISE,
			hasSubscription: false,
			expectedSubscription: &authpb.GetTokenInfoResponse_Enterprise{
				Enterprise: &authpb.EnterpriseSubscription{},
			},
			expectedFeatureControls: []ExpectedFeatureControl{
				{
					FeatureTypes:      []auth_entities.FeatureControl_FeatureType{auth_entities.FeatureControl_ALL_FEATURES},
					State:             auth_entities.FeatureControl_ENABLED,
					DisabledReason:    auth_entities.FeatureControl_REASON_UNKNOWN,
					HasWarningMessage: false,
					HasMkdownMessage:  false,
				},
			},
		},
		{
			name:               "Community subscription that is active",
			tenantTier:         tw_pb.TenantTier_COMMUNITY,
			subscriptionStatus: auth_entities.Subscription_ORB_STATUS_ACTIVE,
			hasSubscription:    true,
			expectedSubscription: &authpb.GetTokenInfoResponse_ActiveSubscription{
				ActiveSubscription: &authpb.ActiveSubscription{},
			},
			expectedFeatureControls: []ExpectedFeatureControl{
				{
					FeatureTypes:      []auth_entities.FeatureControl_FeatureType{auth_entities.FeatureControl_ALL_FEATURES},
					State:             auth_entities.FeatureControl_ENABLED,
					DisabledReason:    auth_entities.FeatureControl_REASON_UNKNOWN,
					HasWarningMessage: false,
					HasMkdownMessage:  false,
				},
			},
		},
		{
			name:               "Professional subscription that is trialing",
			tenantTier:         tw_pb.TenantTier_PROFESSIONAL,
			subscriptionStatus: auth_entities.Subscription_ORB_STATUS_ACTIVE,
			hasSubscription:    true,
			expectedSubscription: &authpb.GetTokenInfoResponse_ActiveSubscription{
				ActiveSubscription: &authpb.ActiveSubscription{
					EndDate: timestamppb.New(trialEndTime),
				},
			},
			expectedTrialEnd: timestamppb.New(trialEndTime),
			expectedFeatureControls: []ExpectedFeatureControl{
				{
					FeatureTypes:      []auth_entities.FeatureControl_FeatureType{auth_entities.FeatureControl_ALL_FEATURES},
					State:             auth_entities.FeatureControl_ENABLED,
					DisabledReason:    auth_entities.FeatureControl_REASON_UNKNOWN,
					HasWarningMessage: true,
					HasMkdownMessage:  false,
					HasReminderSuffix: true,
				},
			},
		},
		{
			name:            "Professional tenant with no subscription",
			tenantTier:      tw_pb.TenantTier_PROFESSIONAL,
			hasSubscription: false,
			expectedSubscription: &authpb.GetTokenInfoResponse_ActiveSubscription{
				ActiveSubscription: &authpb.ActiveSubscription{},
			},
			expectedFeatureControls: []ExpectedFeatureControl{
				{
					FeatureTypes:      []auth_entities.FeatureControl_FeatureType{auth_entities.FeatureControl_ALL_FEATURES},
					State:             auth_entities.FeatureControl_ENABLED,
					DisabledReason:    auth_entities.FeatureControl_REASON_UNKNOWN,
					HasWarningMessage: false,
					HasMkdownMessage:  false,
				},
			},
		},
		{
			name:               "Professional tenant with usage balance depleted",
			tenantTier:         tw_pb.TenantTier_PROFESSIONAL,
			subscriptionStatus: auth_entities.Subscription_ORB_STATUS_ACTIVE,
			hasSubscription:    true,
			expectedSubscription: &authpb.GetTokenInfoResponse_ActiveSubscription{
				ActiveSubscription: &authpb.ActiveSubscription{},
			},
			expectedFeatureControls: []ExpectedFeatureControl{
				{
					FeatureTypes: []auth_entities.FeatureControl_FeatureType{
						auth_entities.FeatureControl_CHAT,
						auth_entities.FeatureControl_AGENT,
						auth_entities.FeatureControl_REMOTE_AGENT,
						auth_entities.FeatureControl_CLI_AGENT,
					},
					State:             auth_entities.FeatureControl_DISABLED,
					DisabledReason:    auth_entities.FeatureControl_USAGE_BALANCE_DEPLETED,
					HasWarningMessage: true,
					HasMkdownMessage:  true,
				},
				{
					FeatureTypes:      []auth_entities.FeatureControl_FeatureType{auth_entities.FeatureControl_ALL_FEATURES},
					State:             auth_entities.FeatureControl_ENABLED,
					DisabledReason:    auth_entities.FeatureControl_REASON_UNKNOWN,
					HasWarningMessage: false,
					HasMkdownMessage:  false,
				},
			},
		},
		{
			name:               "Professional tenant with inactive subscription",
			tenantTier:         tw_pb.TenantTier_PROFESSIONAL,
			subscriptionStatus: auth_entities.Subscription_ORB_STATUS_ENDED,
			hasSubscription:    true,
			expectedSubscription: &authpb.GetTokenInfoResponse_InactiveSubscription{
				InactiveSubscription: &authpb.InactiveSubscription{},
			},
			expectedFeatureControls: []ExpectedFeatureControl{
				{
					FeatureTypes: []auth_entities.FeatureControl_FeatureType{
						auth_entities.FeatureControl_CHAT,
						auth_entities.FeatureControl_AGENT,
						auth_entities.FeatureControl_REMOTE_AGENT,
						auth_entities.FeatureControl_CLI_AGENT,
					},
					State:             auth_entities.FeatureControl_DISABLED,
					DisabledReason:    auth_entities.FeatureControl_INACTIVE_SUBSCRIPTION,
					HasWarningMessage: true,
					HasMkdownMessage:  true,
				},
				{
					FeatureTypes:      []auth_entities.FeatureControl_FeatureType{auth_entities.FeatureControl_ALL_FEATURES},
					State:             auth_entities.FeatureControl_DISABLED,
					DisabledReason:    auth_entities.FeatureControl_INACTIVE_SUBSCRIPTION,
					HasWarningMessage: false,
					HasMkdownMessage:  false,
				},
			},
		},
		{
			name:               "Enterprise tenant with subscription",
			tenantTier:         tw_pb.TenantTier_ENTERPRISE,
			subscriptionStatus: auth_entities.Subscription_ORB_STATUS_ACTIVE,
			hasSubscription:    true,
			expectedSubscription: &authpb.GetTokenInfoResponse_Enterprise{
				Enterprise: &authpb.EnterpriseSubscription{},
			},
			expectedFeatureControls: []ExpectedFeatureControl{
				{
					FeatureTypes:      []auth_entities.FeatureControl_FeatureType{auth_entities.FeatureControl_ALL_FEATURES},
					State:             auth_entities.FeatureControl_ENABLED,
					DisabledReason:    auth_entities.FeatureControl_REASON_UNKNOWN,
					HasWarningMessage: false,
					HasMkdownMessage:  false,
				},
			},
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			sut := createAuthServicerSUT(t, bigtableFixture)
			authServicer := sut.authServicer
			daoFactory := sut.daoFactory

			// Choose the appropriate tenant ID based on the test case's expected tier
			var testTenantID string
			var testTenantName string
			var testTenantNamespace string
			switch tc.tenantTier {
			case tw_pb.TenantTier_ENTERPRISE:
				testTenantID = tenantID
				testTenantName = tenantName
				testTenantNamespace = tenantNamespace
			case tw_pb.TenantTier_PROFESSIONAL:
				testTenantID = professionalTenantID
				testTenantName = "professional-tenant"
				testTenantNamespace = "professional-namespace"
			case tw_pb.TenantTier_COMMUNITY:
				testTenantID = testCommunityTenantID
				testTenantName = "test-community-tenant"
				testTenantNamespace = "community-namespace"
			default:
				testTenantID = tenantID
				testTenantName = tenantName
				testTenantNamespace = tenantNamespace
			}

			// Create authorized context for the specific tenant
			adminClaims := &auth.AugmentClaims{
				TenantID:       testTenantID,
				TenantName:     testTenantName,
				ShardNamespace: testTenantNamespace,
				Cloud:          "test-cloud",
				Scope:          []string{"AUTH_RW", "AUTH_R", "PII_ADMIN"},
			}
			ctx := adminClaims.NewContext(context.Background())

			featureFlagHandle := featureflag.NewLocalFeatureFlagHandler()
			featureFlagHandle.Set("check_subscription_status", true)
			featureFlagHandle.Set("enforce_usage_credits", true)
			featureFlagHandle.Set("auth_central_enable_feature_gating_info", true)

			// Add a user to the tenant
			userEmail := fmt.Sprintf("<EMAIL>", uuid.New().String())
			request := &authpb.AddUserToTenantRequest{
				Email:    userEmail,
				TenantId: testTenantID,
			}

			// Test: Add user
			addUserResponse, err := authServicer.AddUserToTenant(ctx, request)
			require.NoError(t, err)
			assert.Equal(t, userEmail, addUserResponse.User.Email)

			claims := &auth.AugmentClaims{
				UserID:         addUserResponse.User.Id,
				TenantID:       testTenantID,
				TenantName:     testTenantName,
				ShardNamespace: testTenantNamespace,
				Cloud:          "test-cloud",
				Scope:          []string{tokenscopesproto.Scope_AUTH_RW.String()},
			}
			ctx = claims.NewContext(context.Background())

			tokenHashDAO := daoFactory.GetTokenHashDAO()

			// Create token hash
			tokenHash := &auth_entities.TokenHash{
				Hash:          "2cf24dba5fb0a30e26e83b2ac5b9e29e1b161e5c1fa7425e73043362938b9824",
				AugmentUserId: addUserResponse.User.Id,
				TenantId:      testTenantID,
				EmailAddress:  userEmail,
			}
			_, err = tokenHashDAO.Create(ctx, tokenHash)
			require.NoError(t, err)

			// Create subscription if needed for this test case
			if tc.hasSubscription {
				subscriptionId := "test-subscription-id"
				// Determine if usage balance is depleted based on expected controls
				usageBalanceDepleted := false
				for _, control := range tc.expectedFeatureControls {
					if control.DisabledReason == auth_entities.FeatureControl_USAGE_BALANCE_DEPLETED {
						usageBalanceDepleted = true
						break
					}
				}

				subscription := &auth_entities.Subscription{
					SubscriptionId:       subscriptionId,
					OrbStatus:            tc.subscriptionStatus,
					UsageBalanceDepleted: usageBalanceDepleted,
				}
				if tc.expectedTrialEnd != nil {
					subscription.EndDate = tc.expectedTrialEnd
					subscription.HasPaymentMethod = false
				}

				// Save the subscription to the database
				subscriptionDAO := NewSubscriptionDAO(bigtableFixture.Table)
				_, err = subscriptionDAO.Create(ctx, subscription)
				require.NoError(t, err)

				// Update the user to reference this subscription
				userDAO := daoFactory.GetUserDAO()
				updateFn := func(u *auth_entities.User) bool {
					u.OrbSubscriptionId = subscriptionId
					u.SubscriptionCreationInfo = &auth_entities.User_SubscriptionCreationInfo{
						Id:        "test-subscription-creation-id",
						Status:    auth_entities.User_SubscriptionCreationInfo_SUCCESS,
						CreatedAt: timestamppb.Now(),
						UpdatedAt: timestamppb.Now(),
					}
					return true
				}
				_, err = userDAO.TryUpdate(ctx, addUserResponse.User.Id, updateFn, DefaultRetry)
				require.NoError(t, err)
			}

			// Get token info
			getTokenInfoResponse, err := authServicer.GetTokenInfo(ctx, &authpb.GetTokenInfoRequest{
				Token: "hello",
			})
			require.NoError(t, err)

			// Verify basic fields
			assert.Equal(t, userEmail, getTokenInfoResponse.UserId)
			assert.Equal(t, addUserResponse.User.Id, getTokenInfoResponse.AugmentUserId)
			assert.Equal(t, userEmail, getTokenInfoResponse.UserEmail)
			assert.Equal(t, testTenantID, getTokenInfoResponse.TenantId)
			assert.Equal(t, testTenantName, getTokenInfoResponse.TenantName)

			// Verify subscription matches expected type
			assert.IsType(t, tc.expectedSubscription, getTokenInfoResponse.Subscription)

			// Additional type-specific checks
			switch expected := tc.expectedSubscription.(type) {
			case *authpb.GetTokenInfoResponse_ActiveSubscription:
				actual, ok := getTokenInfoResponse.Subscription.(*authpb.GetTokenInfoResponse_ActiveSubscription)
				require.True(t, ok)
				if expected.ActiveSubscription.EndDate != nil {
					assert.Equal(t, expected.ActiveSubscription.EndDate.AsTime(), actual.ActiveSubscription.EndDate.AsTime())
				}
			}

			// Verify feature gating info (when feature flag is enabled)
			if getTokenInfoResponse.FeatureGatingInfo != nil {
				require.NotNil(t, getTokenInfoResponse.FeatureGatingInfo.FeatureControls)

				// Verify feature controls using helper function
				verifyFeatureGatingInfo(t, getTokenInfoResponse.FeatureGatingInfo, tc.expectedFeatureControls)
			}
		})
	}
}

func TestTokenExpired(t *testing.T) {
	bigtableFixture := NewBigtableFixture(t)
	defer bigtableFixture.Cleanup()

	sut := createAuthServicerSUT(t, bigtableFixture)
	authServicer := sut.authServicer
	daoFactory := sut.daoFactory

	ctx := createAuthorizedRequestContext()

	userEmail := fmt.Sprintf("<EMAIL>", uuid.New().String())
	request := &authpb.AddUserToTenantRequest{
		Email:    userEmail,
		TenantId: tenantID,
	}

	// Test: Add user
	addUserResponse, err := authServicer.AddUserToTenant(ctx, request)
	require.NoError(t, err)
	assert.Equal(t, userEmail, addUserResponse.User.Email)

	tokenHashDAO := daoFactory.GetTokenHashDAO()

	// Populate with a sample token
	// sha256 of "hello"
	tokenHash := &auth_entities.TokenHash{
		Hash:                  "2cf24dba5fb0a30e26e83b2ac5b9e29e1b161e5c1fa7425e73043362938b9824",
		AugmentUserId:         addUserResponse.User.Id,
		TenantId:              tenantID,
		EmailAddress:          userEmail,
		CreationTime:          &timestamppb.Timestamp{Seconds: 1},
		ExpirationTimeSeconds: 1, // 1 second
	}
	_, err = tokenHashDAO.Create(ctx, tokenHash)
	require.NoError(t, err)

	// Verify that we can get the token info
	getTokenInfoResponse, err := authServicer.GetTokenInfo(ctx, &authpb.GetTokenInfoRequest{
		Token: "hello",
	})
	assert.Empty(t, getTokenInfoResponse)
	assert.Equal(t, codes.NotFound, status.Code(err))
	assert.Contains(t, err.Error(), "Token expired")
}

func TestGetUserSubscriptionInfo(t *testing.T) {
	bigtableFixture := NewBigtableFixture(t)
	defer bigtableFixture.Cleanup()

	sut := createAuthServicerSUT(t, bigtableFixture)
	authServicer := sut.authServicer
	daoFactory := sut.daoFactory
	userDAO := daoFactory.GetUserDAO()
	subscriptionDAO := daoFactory.GetSubscriptionDAO()
	ctx := createCentralAuthorizedRequestContext()

	// Test cases for different subscription types
	testCases := []struct {
		name               string
		tenantTier         tw_pb.TenantTier
		userTenants        []string
		orbSubscriptionId  string
		subscriptionStatus auth_entities.Subscription_OrbStatus
		hasSubscription    bool
		expectedType       string // "enterprise", "active", "inactive"
		expectError        bool
		errorCode          codes.Code
		// Feature gating expectations
		expectedFeatureControls []ExpectedFeatureControl
	}{
		{
			name:         "Enterprise tenant returns enterprise subscription",
			tenantTier:   tw_pb.TenantTier_ENTERPRISE,
			userTenants:  []string{tenantID},
			expectedType: "enterprise",
			expectError:  false,
			expectedFeatureControls: []ExpectedFeatureControl{
				{
					FeatureTypes:      []auth_entities.FeatureControl_FeatureType{auth_entities.FeatureControl_ALL_FEATURES},
					State:             auth_entities.FeatureControl_ENABLED,
					DisabledReason:    auth_entities.FeatureControl_REASON_UNKNOWN,
					HasWarningMessage: false,
					HasMkdownMessage:  false,
				},
			},
		},
		{
			name:         "Multi-tenant user returns active subscription",
			tenantTier:   tw_pb.TenantTier_PROFESSIONAL,
			userTenants:  []string{tenantID, professionalTenantID},
			expectedType: "active",
			expectError:  false,
			expectedFeatureControls: []ExpectedFeatureControl{
				{
					FeatureTypes:      []auth_entities.FeatureControl_FeatureType{auth_entities.FeatureControl_ALL_FEATURES},
					State:             auth_entities.FeatureControl_ENABLED,
					DisabledReason:    auth_entities.FeatureControl_REASON_UNKNOWN,
					HasWarningMessage: false,
					HasMkdownMessage:  false,
				},
			},
		},
		{
			name:               "Professional tenant with active Orb subscription",
			tenantTier:         tw_pb.TenantTier_PROFESSIONAL,
			userTenants:        []string{professionalTenantID},
			orbSubscriptionId:  "orb-sub-active",
			subscriptionStatus: auth_entities.Subscription_ORB_STATUS_ACTIVE,
			hasSubscription:    true,
			expectedType:       "active",
			expectError:        false,
			expectedFeatureControls: []ExpectedFeatureControl{
				{
					FeatureTypes:      []auth_entities.FeatureControl_FeatureType{auth_entities.FeatureControl_ALL_FEATURES},
					State:             auth_entities.FeatureControl_ENABLED,
					DisabledReason:    auth_entities.FeatureControl_REASON_UNKNOWN,
					HasWarningMessage: false,
					HasMkdownMessage:  false,
				},
			},
		},
		{
			name:               "Trial subscription ended should return inactive",
			tenantTier:         tw_pb.TenantTier_PROFESSIONAL,
			userTenants:        []string{professionalTenantID},
			orbSubscriptionId:  "orb-sub-ended",
			subscriptionStatus: auth_entities.Subscription_ORB_STATUS_ENDED,
			hasSubscription:    true,
			expectedType:       "inactive",
			expectError:        false,
			expectedFeatureControls: []ExpectedFeatureControl{
				{
					FeatureTypes: []auth_entities.FeatureControl_FeatureType{
						auth_entities.FeatureControl_CHAT,
						auth_entities.FeatureControl_AGENT,
						auth_entities.FeatureControl_REMOTE_AGENT,
						auth_entities.FeatureControl_CLI_AGENT,
					},
					State:             auth_entities.FeatureControl_DISABLED,
					DisabledReason:    auth_entities.FeatureControl_INACTIVE_SUBSCRIPTION,
					HasWarningMessage: true,
					HasMkdownMessage:  true,
				},
				{
					FeatureTypes:      []auth_entities.FeatureControl_FeatureType{auth_entities.FeatureControl_ALL_FEATURES},
					State:             auth_entities.FeatureControl_DISABLED,
					DisabledReason:    auth_entities.FeatureControl_INACTIVE_SUBSCRIPTION,
					HasWarningMessage: false,
					HasMkdownMessage:  false,
				},
			},
		},
		{
			name:               "Professional tenant with usage balance depleted",
			tenantTier:         tw_pb.TenantTier_PROFESSIONAL,
			userTenants:        []string{professionalTenantID},
			orbSubscriptionId:  "orb-sub-active",
			subscriptionStatus: auth_entities.Subscription_ORB_STATUS_ACTIVE,
			hasSubscription:    true,
			expectedType:       "active",
			expectError:        false,
			expectedFeatureControls: []ExpectedFeatureControl{
				{
					FeatureTypes: []auth_entities.FeatureControl_FeatureType{
						auth_entities.FeatureControl_CHAT,
						auth_entities.FeatureControl_AGENT,
						auth_entities.FeatureControl_REMOTE_AGENT,
						auth_entities.FeatureControl_CLI_AGENT,
					},
					State:             auth_entities.FeatureControl_DISABLED,
					DisabledReason:    auth_entities.FeatureControl_USAGE_BALANCE_DEPLETED,
					HasWarningMessage: true,
					HasMkdownMessage:  true,
				},
				{
					FeatureTypes:      []auth_entities.FeatureControl_FeatureType{auth_entities.FeatureControl_ALL_FEATURES},
					State:             auth_entities.FeatureControl_ENABLED,
					DisabledReason:    auth_entities.FeatureControl_REASON_UNKNOWN,
					HasWarningMessage: false,
					HasMkdownMessage:  false,
				},
			},
		},

		{
			name:        "User not found",
			tenantTier:  tw_pb.TenantTier_PROFESSIONAL,
			userTenants: []string{},
			expectError: true,
			errorCode:   codes.NotFound,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			var userId string

			if !tc.expectError || tc.errorCode != codes.NotFound {
				// Create user for this test case
				userId = uuid.New().String()
				user := &auth_entities.User{
					Id:                userId,
					Email:             fmt.Sprintf("<EMAIL>", userId),
					Tenants:           tc.userTenants,
					OrbSubscriptionId: tc.orbSubscriptionId,
				}
				_, err := userDAO.Create(ctx, user)
				require.NoError(t, err)

				// Create subscription if needed
				if tc.hasSubscription && tc.orbSubscriptionId != "" {
					// Determine if usage balance is depleted based on expected controls
					usageBalanceDepleted := false
					for _, control := range tc.expectedFeatureControls {
						if control.DisabledReason == auth_entities.FeatureControl_USAGE_BALANCE_DEPLETED {
							usageBalanceDepleted = true
							break
						}
					}

					subscription := &auth_entities.Subscription{
						SubscriptionId:       tc.orbSubscriptionId,
						OrbStatus:            tc.subscriptionStatus,
						UsageBalanceDepleted: usageBalanceDepleted,
					}
					_, err := subscriptionDAO.Create(ctx, subscription)
					require.NoError(t, err)
				}
			} else {
				// Use non-existent user ID for NotFound test
				userId = "non-existent-user"
			}

			// Determine tenant ID based on tier
			testTenantID := tenantID
			if tc.tenantTier == tw_pb.TenantTier_PROFESSIONAL {
				testTenantID = professionalTenantID
			}

			// Call GetUserSubscriptionInfo
			response, err := authServicer.GetUserSubscriptionInfo(ctx, &authpb.GetUserSubscriptionInfoRequest{
				UserId:   userId,
				TenantId: testTenantID,
			})

			if tc.expectError {
				require.Error(t, err)
				if tc.errorCode != codes.Code(0) {
					require.Equal(t, tc.errorCode, status.Code(err))
				}
				return
			}

			require.NoError(t, err)
			require.NotNil(t, response)

			// Verify the subscription field is populated
			require.NotNil(t, response.Subscription, "Subscription field should be populated")

			// Verify the subscription type
			switch tc.expectedType {
			case "enterprise":
				_, ok := response.Subscription.(*authpb.GetUserSubscriptionInfoResponse_Enterprise)
				require.True(t, ok, "Expected enterprise subscription")
			case "active":
				_, ok := response.Subscription.(*authpb.GetUserSubscriptionInfoResponse_ActiveSubscription)
				require.True(t, ok, "Expected active subscription")
			case "inactive":
				_, ok := response.Subscription.(*authpb.GetUserSubscriptionInfoResponse_InactiveSubscription)
				require.True(t, ok, "Expected inactive subscription")
			default:
				t.Fatalf("Unknown expected type: %s", tc.expectedType)
			}

			// Verify feature gating info
			require.NotNil(t, response.FeatureGatingInfo, "FeatureGatingInfo should be populated")
			require.NotEmpty(t, response.FeatureGatingInfo.FeatureControls, "FeatureControls should not be empty")

			// Verify feature controls using helper function
			verifyFeatureGatingInfo(t, response.FeatureGatingInfo, tc.expectedFeatureControls)
		})
	}
}

// Helper function to verify feature gating info for usage balance depleted scenario
// Expected feature control for test validation
type ExpectedFeatureControl struct {
	FeatureTypes      []auth_entities.FeatureControl_FeatureType
	State             auth_entities.FeatureControl_FeatureState
	DisabledReason    auth_entities.FeatureControl_FeatureDisabledReason
	HasWarningMessage bool
	HasMkdownMessage  bool
	HasReminderSuffix bool
}

// Helper function to verify feature gating info matches expected controls
func verifyFeatureGatingInfo(t *testing.T, featureGatingInfo *auth_entities.FeatureGatingInfo, expectedControls []ExpectedFeatureControl) {
	require.NotNil(t, featureGatingInfo, "FeatureGatingInfo should be populated")
	require.Len(t, featureGatingInfo.FeatureControls, len(expectedControls), "Should have expected number of feature controls")

	// Create a map of actual controls by their feature types for easier matching
	actualControlsMap := make(map[string]*auth_entities.FeatureControl)
	for _, control := range featureGatingInfo.FeatureControls {
		// Create a key from sorted feature types
		key := createFeatureTypesKey(control.FeatureTypes)
		actualControlsMap[key] = control
	}

	// Verify each expected control
	for i, expected := range expectedControls {
		expectedKey := createFeatureTypesKey(expected.FeatureTypes)
		actualControl, found := actualControlsMap[expectedKey]
		require.True(t, found, "Expected control %d with feature types %v not found", i, expected.FeatureTypes)

		// Verify basic properties
		assert.Equal(t, expected.State, actualControl.State, "Control %d state mismatch", i)
		assert.Equal(t, expected.DisabledReason, actualControl.DisabledReason, "Control %d disabled reason mismatch", i)
		assert.ElementsMatch(t, expected.FeatureTypes, actualControl.FeatureTypes, "Control %d feature types mismatch", i)

		// Verify display info
		if expected.HasWarningMessage || expected.HasMkdownMessage || expected.HasReminderSuffix {
			require.NotNil(t, actualControl.DisplayInfo, "Control %d should have display info", i)

			if expected.HasWarningMessage {
				assert.NotEmpty(t, actualControl.DisplayInfo.WarningMessage, "Control %d should have warning message", i)
			} else {
				assert.Empty(t, actualControl.DisplayInfo.WarningMessage, "Control %d should not have warning message", i)
			}

			if expected.HasMkdownMessage {
				assert.NotEmpty(t, actualControl.DisplayInfo.MkdownMessage, "Control %d should have markdown message", i)
			} else {
				assert.Empty(t, actualControl.DisplayInfo.MkdownMessage, "Control %d should not have markdown message", i)
			}

			if expected.HasReminderSuffix {
				assert.NotEmpty(t, actualControl.DisplayInfo.ReminderSuffix, "Control %d should have reminder suffix", i)
			} else {
				assert.Empty(t, actualControl.DisplayInfo.ReminderSuffix, "Control %d should not have reminder suffix", i)
			}
		} else {
			// If no messages expected, DisplayInfo can be nil or have empty messages
			if actualControl.DisplayInfo != nil {
				assert.Empty(t, actualControl.DisplayInfo.WarningMessage, "Control %d should not have warning message", i)
				assert.Empty(t, actualControl.DisplayInfo.MkdownMessage, "Control %d should not have markdown message", i)
				assert.Empty(t, actualControl.DisplayInfo.ReminderSuffix, "Control %d should not have reminder suffix", i)
			}
		}
	}
}

// Helper function to create a consistent key from feature types for matching
func createFeatureTypesKey(featureTypes []auth_entities.FeatureControl_FeatureType) string {
	// Sort feature types to create consistent key
	sorted := make([]auth_entities.FeatureControl_FeatureType, len(featureTypes))
	copy(sorted, featureTypes)

	// Sort using sort.Slice
	sort.Slice(sorted, func(i, j int) bool {
		return sorted[i] < sorted[j]
	})

	// Create string key
	var key strings.Builder
	for i, ft := range sorted {
		if i > 0 {
			key.WriteString(",")
		}
		key.WriteString(fmt.Sprintf("%d", int(ft)))
	}
	return key.String()
}

func TestGetUserBillingInfo(t *testing.T) {
	bigtableFixture := NewBigtableFixture(t)
	defer bigtableFixture.Cleanup()

	sut := createAuthServicerSUT(t, bigtableFixture)
	authServicer := sut.authServicer
	daoFactory := sut.daoFactory

	ctx := createAuthorizedRequestContext()
	ctxTeam := createRequestContext(testSelfServeTeamTenantID, []string{"AUTH_RW", "AUTH_R"})

	// Create a test user
	userEmail := fmt.Sprintf("<EMAIL>", uuid.New().String())
	userDao := daoFactory.GetUserDAO()
	user := &auth_entities.User{
		Id:            "test-user-id",
		Email:         userEmail,
		Tenants:       []string{tenantID},
		OrbCustomerId: "orb-customer-123",
	}
	teamUser := &auth_entities.User{
		Id:            "test-team-user-id",
		Email:         userEmail,
		Tenants:       []string{testSelfServeTeamTenantID},
		OrbCustomerId: "orb-team-customer-456",
	}
	_, err := userDao.Create(ctx, user)
	require.NoError(t, err)
	_, err = userDao.Create(ctx, teamUser)
	require.NoError(t, err)

	// Create user tenant mappings
	userTenantMappingDAO := daoFactory.GetUserTenantMappingDAO("individual")
	userMapping := &auth_entities.UserTenantMapping{
		UserId: "test-user-id",
		Tenant: "individual",
	}
	_, err = userTenantMappingDAO.Create(ctx, userMapping)
	require.NoError(t, err)

	teamUserTenantMappingDAO := daoFactory.GetUserTenantMappingDAO("self-serve-team-tenant")
	teamUserMapping := &auth_entities.UserTenantMapping{
		UserId: "test-team-user-id",
		Tenant: "self-serve-team-tenant",
	}
	_, err = teamUserTenantMappingDAO.Create(ctx, teamUserMapping)
	require.NoError(t, err)

	// Create a tenant subscription mapping for the self-serve team
	tenantSubscriptionMappingDAO := daoFactory.GetTenantSubscriptionMappingDAO()
	tenantSubscriptionMapping := &auth_entities.TenantSubscriptionMapping{
		TenantId:      testSelfServeTeamTenantID,
		OrbCustomerId: "orb-team-customer-456",
	}
	_, err = tenantSubscriptionMappingDAO.Create(ctx, tenantSubscriptionMapping)
	require.NoError(t, err)

	// Test 1: Get user billing info for regular tenant
	request := &authpb.GetUserBillingInfoRequest{
		UserId:   "test-user-id",
		TenantId: tenantID,
	}
	response, err := authServicer.GetUserBillingInfo(ctx, request)
	require.NoError(t, err)
	assert.Equal(t, "test-user-id", response.UserId)
	assert.Equal(t, userEmail, response.Email)
	assert.Equal(t, "orb-customer-123", response.OrbCustomerId)
	assert.False(t, response.IsSelfServeTeam)

	// Test 2: Get user billing info for self-serve team tenant
	request = &authpb.GetUserBillingInfoRequest{
		UserId:   "test-team-user-id",
		TenantId: testSelfServeTeamTenantID,
	}
	response, err = authServicer.GetUserBillingInfo(ctxTeam, request)
	require.NoError(t, err)
	assert.Equal(t, "test-team-user-id", response.UserId)
	assert.Equal(t, userEmail, response.Email)
	assert.Equal(t, "orb-team-customer-456", response.OrbCustomerId)
	assert.True(t, response.IsSelfServeTeam)

	// Test 3: User not found
	request = &authpb.GetUserBillingInfoRequest{
		UserId:   "non-existent-user",
		TenantId: tenantID,
	}
	_, err = authServicer.GetUserBillingInfo(ctx, request)
	require.Error(t, err)
	assert.Equal(t, codes.NotFound, status.Code(err))

	// Test 4: Tenant not found
	request = &authpb.GetUserBillingInfoRequest{
		UserId:   "test-user-id",
		TenantId: "non-existent-tenant",
	}
	_, err = authServicer.GetUserBillingInfo(ctx, request)
	require.Error(t, err)
}

func caseTestMergeDuplicateAccounts(t *testing.T, bigtableFixture *BigtableFixture) {
	sut := createAuthServicerSUT(t, bigtableFixture)
	authServicer := sut.authServicer
	// Inject a mock orb client
	mockOrbClient := orb.NewMockOrbClient()
	authServicer.orbClient = mockOrbClient
	userDAO := sut.daoFactory.GetUserDAO()

	ctx := createCentralAuthAdminRequestContext()

	t.Run("Dry run - merge users with same email", func(t *testing.T) {
		bigtableFixture.ClearTable(t)

		// Create users with same email
		user1 := &auth_entities.User{
			Id:      "merge-user-1",
			Email:   "<EMAIL>",
			Tenants: []string{tenantID},
		}
		user2 := &auth_entities.User{
			Id:      "merge-user-2",
			Email:   "<EMAIL>",
			Tenants: []string{tenantID},
		}

		_, err := userDAO.Create(ctx, user1)
		require.NoError(t, err)
		_, err = userDAO.Create(ctx, user2)
		require.NoError(t, err)

		// Add users to tenants
		tenantMappingDAO := sut.daoFactory.GetUserTenantMappingDAO(tenantName)
		mapping1 := &auth_entities.UserTenantMapping{
			UserId: "merge-user-1",
			Tenant: tenantName,
		}
		_, err = tenantMappingDAO.Create(ctx, mapping1)
		require.NoError(t, err)

		mapping2 := &auth_entities.UserTenantMapping{
			UserId: "merge-user-2",
			Tenant: tenantName,
		}
		_, err = tenantMappingDAO.Create(ctx, mapping2)
		require.NoError(t, err)

		// Run merge in dry-run mode
		resp, err := authServicer.MergeDuplicateAccounts(ctx, &authpb.MergeDuplicateAccountsRequest{
			UserIds: []string{"merge-user-1", "merge-user-2"},
			DryRun:  true,
		})
		require.NoError(t, err)
		require.NotNil(t, resp)
		require.Empty(t, resp.FailedUsers, "Should have no failed users")
		require.NotNil(t, resp.UserMerge, "Should have merge info")

		// Verify primary user is the one with lower ID
		assert.Equal(t, "merge-user-1", resp.UserMerge.PrimaryUserId)
		assert.Contains(t, resp.UserMerge.MergedUserIds, "merge-user-2")

		// Verify both users still exist (dry run)
		_, err = userDAO.Get(ctx, "merge-user-1")
		require.NoError(t, err)
		_, err = userDAO.Get(ctx, "merge-user-2")
		require.NoError(t, err)
	})

	t.Run("Failed merge - insufficient users", func(t *testing.T) {
		bigtableFixture.ClearTable(t)

		// Run merge with only one user
		_, err := authServicer.MergeDuplicateAccounts(ctx, &authpb.MergeDuplicateAccountsRequest{
			UserIds: []string{"single-user"},
			DryRun:  true,
		})
		require.Error(t, err)
		assert.Equal(t, codes.InvalidArgument, status.Code(err))
	})

	t.Run("Dry run - merge users with same IDP user ID and different emails", func(t *testing.T) {
		bigtableFixture.ClearTable(t)

		// Create users with same IDP user ID but different emails
		user1 := &auth_entities.User{
			Id:         "idp-user-1",
			Email:      "<EMAIL>",
			IdpUserIds: []string{"idp-123"},
			Tenants:    []string{tenantID},
		}
		user2 := &auth_entities.User{
			Id:         "idp-user-2",
			Email:      "<EMAIL>",
			IdpUserIds: []string{"idp-123"},
			Tenants:    []string{tenantID},
		}

		_, err := userDAO.Create(ctx, user1)
		require.NoError(t, err)
		_, err = userDAO.Create(ctx, user2)
		require.NoError(t, err)

		// Add users to tenants
		tenantMappingDAO := sut.daoFactory.GetUserTenantMappingDAO(tenantName)
		mapping1 := &auth_entities.UserTenantMapping{
			UserId: "idp-user-1",
			Tenant: tenantName,
		}
		_, err = tenantMappingDAO.Create(ctx, mapping1)
		require.NoError(t, err)

		mapping2 := &auth_entities.UserTenantMapping{
			UserId: "idp-user-2",
			Tenant: tenantName,
		}
		_, err = tenantMappingDAO.Create(ctx, mapping2)
		require.NoError(t, err)

		// Run merge in dry-run mode with IDP merge type
		resp, err := authServicer.MergeDuplicateAccounts(ctx, &authpb.MergeDuplicateAccountsRequest{
			UserIds: []string{"idp-user-1", "idp-user-2"},
			DryRun:  true,
		})
		require.NoError(t, err)
		require.NotNil(t, resp)
		require.Empty(t, resp.FailedUsers, "Should have no failed users")
		require.NotNil(t, resp.UserMerge, "Should have merge info")

		// Verify primary user is the one with lower ID
		assert.Equal(t, "idp-user-1", resp.UserMerge.PrimaryUserId)
		assert.Contains(t, resp.UserMerge.MergedUserIds, "idp-user-2")

		// Verify merged emails are populated
		assert.Len(t, resp.UserMerge.MergedEmails, 2, "Should have both emails")
		assert.Contains(t, resp.UserMerge.MergedEmails, "<EMAIL>")
		assert.Contains(t, resp.UserMerge.MergedEmails, "<EMAIL>")

		// Verify IDP user IDs are collected
		assert.Contains(t, resp.UserMerge.IdpUserIds, "idp-123")

		// Verify both users still exist (dry run)
		_, err = userDAO.Get(ctx, "idp-user-1")
		require.NoError(t, err)
		_, err = userDAO.Get(ctx, "idp-user-2")
		require.NoError(t, err)
	})
}

// Test: Add user tags
func caseAddUserTags(t *testing.T, bigtableFixture *BigtableFixture) {
	sut := createAuthServicerSUT(t, bigtableFixture)
	authServicer := sut.authServicer

	// Use tenant-specific context for adding user, then central for tags
	tenantCtx := createAuthorizedRequestContext()
	centralCtx := createCentralAuthorizedRequestContext()

	userEmail := fmt.Sprintf("<EMAIL>", uuid.New().String())
	request := &authpb.AddUserToTenantRequest{
		Email:    userEmail,
		TenantId: tenantID,
	}

	// Test: Add user with tenant context
	addUserResponse, err := authServicer.AddUserToTenant(tenantCtx, request)
	require.NoError(t, err)
	assert.Equal(t, userEmail, addUserResponse.User.Email)

	// Test: Add tags to user with central context
	addTagsRequest := &authpb.AddUserTagsRequest{
		UserId: addUserResponse.User.Id,
		Tags:   []string{"tag1", "tag2"},
	}
	_, err = authServicer.AddUserTags(centralCtx, addTagsRequest)
	require.NoError(t, err)

	// Test: Verify tags were added
	userDAO := sut.daoFactory.GetUserDAO()
	user, err := userDAO.Get(centralCtx, addUserResponse.User.Id)
	require.NoError(t, err)
	assert.Contains(t, user.Tags, "tag1")
	assert.Contains(t, user.Tags, "tag2")
	assert.Equal(t, 2, len(user.Tags))

	// Test: Add duplicate tag (should not create duplicate, but should add new tag)
	addTagsRequest2 := &authpb.AddUserTagsRequest{
		UserId: addUserResponse.User.Id,
		Tags:   []string{"tag1", "tag3"},
	}
	_, err = authServicer.AddUserTags(centralCtx, addTagsRequest2)
	require.NoError(t, err)

	// Test: Verify no duplicate and new tag added
	user, err = userDAO.Get(centralCtx, addUserResponse.User.Id)
	require.NoError(t, err)
	assert.Contains(t, user.Tags, "tag1")
	assert.Contains(t, user.Tags, "tag2")
	assert.Contains(t, user.Tags, "tag3")
	assert.Equal(t, 3, len(user.Tags))

	// Test: Add repeated tag (only one tag should be added)
	addTagsRequest3 := &authpb.AddUserTagsRequest{
		UserId: addUserResponse.User.Id,
		Tags:   []string{"tag4", "tag4", "tag4"},
	}
	_, err = authServicer.AddUserTags(centralCtx, addTagsRequest3)
	require.NoError(t, err)

	// Test: Verify no duplicate
	user, err = userDAO.Get(centralCtx, addUserResponse.User.Id)
	require.NoError(t, err)
	assert.Equal(t, 4, len(user.Tags))
	assert.Contains(t, user.Tags, "tag4")

	// Test: Add tags to nonexistent user
	addTagsRequest4 := &authpb.AddUserTagsRequest{
		UserId: "nonexistent-user-id",
		Tags:   []string{"tag5", "tag6"},
	}
	_, err = authServicer.AddUserTags(centralCtx, addTagsRequest4)
	require.Error(t, err)
	assert.Equal(t, codes.NotFound, status.Code(err))
}

// Test: Remove user tags
func caseRemoveUserTags(t *testing.T, bigtableFixture *BigtableFixture) {
	sut := createAuthServicerSUT(t, bigtableFixture)
	authServicer := sut.authServicer

	// Use tenant-specific context for adding user, then central for tags
	tenantCtx := createAuthorizedRequestContext()
	centralCtx := createCentralAuthorizedRequestContext()

	userEmail := fmt.Sprintf("<EMAIL>", uuid.New().String())
	request := &authpb.AddUserToTenantRequest{
		Email:    userEmail,
		TenantId: tenantID,
	}

	// Test: Add user
	addUserResponse, err := authServicer.AddUserToTenant(tenantCtx, request)
	require.NoError(t, err)

	// Test: Add tags to user first
	addTagsRequest := &authpb.AddUserTagsRequest{
		UserId: addUserResponse.User.Id,
		Tags:   []string{"tag1", "tag2", "tag3"},
	}
	_, err = authServicer.AddUserTags(centralCtx, addTagsRequest)
	require.NoError(t, err)

	// Test: Remove some tags
	removeTagsRequest := &authpb.RemoveUserTagsRequest{
		UserId: addUserResponse.User.Id,
		Tags:   []string{"tag1", "tag3"},
	}
	_, err = authServicer.RemoveUserTags(centralCtx, removeTagsRequest)
	require.NoError(t, err)

	// Test: Verify tags were removed
	userDAO := sut.daoFactory.GetUserDAO()
	user, err := userDAO.Get(centralCtx, addUserResponse.User.Id)
	require.NoError(t, err)
	assert.NotContains(t, user.Tags, "tag1")
	assert.Contains(t, user.Tags, "tag2")
	assert.NotContains(t, user.Tags, "tag3")
	assert.Equal(t, 1, len(user.Tags))

	// Test: Remove non-existent tag (should not error)
	removeTagsRequest2 := &authpb.RemoveUserTagsRequest{
		UserId: addUserResponse.User.Id,
		Tags:   []string{"nonexistent"},
	}
	_, err = authServicer.RemoveUserTags(centralCtx, removeTagsRequest2)
	require.NoError(t, err)

	// Test: Verify no change
	user, err = userDAO.Get(centralCtx, addUserResponse.User.Id)
	require.NoError(t, err)
	assert.Equal(t, 1, len(user.Tags))
	assert.Contains(t, user.Tags, "tag2")

	// Test: Remove tags to nonexistent user
	removeTagsRequest3 := &authpb.RemoveUserTagsRequest{
		UserId: "nonexistent-user-id",
		Tags:   []string{"tag5", "tag6"},
	}
	_, err = authServicer.RemoveUserTags(centralCtx, removeTagsRequest3)
	require.Error(t, err)
	assert.Equal(t, codes.NotFound, status.Code(err))
}

func TestUpdateUserBillingInfo(t *testing.T) {
	bigtableFixture := NewBigtableFixture(t)
	defer bigtableFixture.Cleanup()

	sut := createAuthServicerSUT(t, bigtableFixture)
	authServicer := sut.authServicer
	daoFactory := sut.daoFactory

	ctx := createAuthorizedRequestContext()

	// Create a test user
	userEmail := fmt.Sprintf("<EMAIL>", uuid.New().String())
	userDao := daoFactory.GetUserDAO()
	user := &auth_entities.User{
		Id:               "test-user-id",
		Email:            userEmail,
		Tenants:          []string{tenantID},
		OrbCustomerId:    "orb-customer-123",
		StripeCustomerId: "stripe-customer-123",
	}
	_, err := userDao.Create(ctx, user)
	require.NoError(t, err)

	// Test scenarios
	testCases := []struct {
		name              string
		userId            string
		tenantId          string
		orbCustomerId     *string
		orbSubscriptionId *string
		stripeCustomerId  *string
		expectError       bool
		expectedErrorCode codes.Code
		validateFunc      func(t *testing.T, updatedUser *auth_entities.User)
	}{
		{
			name:     "No changes when no fields provided",
			userId:   "test-user-id",
			tenantId: tenantID,
			validateFunc: func(t *testing.T, updatedUser *auth_entities.User) {
				assert.Equal(t, "orb-customer-123", updatedUser.OrbCustomerId)
				assert.Equal(t, "stripe-customer-123", updatedUser.StripeCustomerId)
			},
		},
		{
			name:     "Update Orb customer ID",
			userId:   "test-user-id",
			tenantId: tenantID,
			orbCustomerId: func() *string {
				id := "new-orb-customer-456"
				return &id
			}(),
			validateFunc: func(t *testing.T, updatedUser *auth_entities.User) {
				assert.Equal(t, "new-orb-customer-456", updatedUser.OrbCustomerId)
				assert.Equal(t, "stripe-customer-123", updatedUser.StripeCustomerId)
			},
		},
		{
			name:     "Update Orb subscription ID",
			userId:   "test-user-id",
			tenantId: tenantID,
			orbSubscriptionId: func() *string {
				id := "new-orb-subscription-456"
				return &id
			}(),
			validateFunc: func(t *testing.T, updatedUser *auth_entities.User) {
				assert.Equal(t, "new-orb-customer-456", updatedUser.OrbCustomerId) // From previous test
				assert.Equal(t, "new-orb-subscription-456", updatedUser.OrbSubscriptionId)
			},
		},
		{
			name:     "Update Stripe customer ID",
			userId:   "test-user-id",
			tenantId: tenantID,
			stripeCustomerId: func() *string {
				id := "new-stripe-customer-456"
				return &id
			}(),
			validateFunc: func(t *testing.T, updatedUser *auth_entities.User) {
				assert.Equal(t, "new-orb-customer-456", updatedUser.OrbCustomerId)         // From previous test
				assert.Equal(t, "new-orb-subscription-456", updatedUser.OrbSubscriptionId) // From previous test
				assert.Equal(t, "new-stripe-customer-456", updatedUser.StripeCustomerId)
			},
		},
		{
			name:     "Update all fields",
			userId:   "test-user-id",
			tenantId: tenantID,
			orbCustomerId: func() *string {
				id := "final-orb-customer-789"
				return &id
			}(),
			orbSubscriptionId: func() *string {
				id := "final-orb-subscription-789"
				return &id
			}(),
			validateFunc: func(t *testing.T, updatedUser *auth_entities.User) {
				assert.Equal(t, "final-orb-customer-789", updatedUser.OrbCustomerId)
				assert.Equal(t, "final-orb-subscription-789", updatedUser.OrbSubscriptionId)
			},
		},
		{
			name:     "No changes when no fields provided - should remain same as last test",
			userId:   "test-user-id",
			tenantId: tenantID,
			validateFunc: func(t *testing.T, updatedUser *auth_entities.User) {
				// Should remain the same as the last test
				assert.Equal(t, "final-orb-customer-789", updatedUser.OrbCustomerId)
				assert.Equal(t, "final-orb-subscription-789", updatedUser.OrbSubscriptionId)
			},
		},
		{
			name:              "User not found",
			userId:            "non-existent-user",
			tenantId:          tenantID,
			expectError:       true,
			expectedErrorCode: codes.NotFound,
		},
		{
			name:              "Invalid tenant",
			userId:            "test-user-id",
			tenantId:          "non-existent-tenant",
			expectError:       true,
			expectedErrorCode: codes.PermissionDenied,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			request := &authpb.UpdateUserBillingInfoRequest{
				UserId:            tc.userId,
				TenantId:          tc.tenantId,
				OrbCustomerId:     tc.orbCustomerId,
				OrbSubscriptionId: tc.orbSubscriptionId,
				StripeCustomerId:  tc.stripeCustomerId,
			}

			response, err := authServicer.UpdateUserBillingInfo(ctx, request)

			if tc.expectError {
				require.Error(t, err)
				assert.Equal(t, tc.expectedErrorCode, status.Code(err))
			} else {
				require.NoError(t, err)
				require.NotNil(t, response)

				// Verify the user was updated correctly
				updatedUser, err := userDao.Get(ctx, tc.userId)
				require.NoError(t, err)
				require.NotNil(t, updatedUser)

				if tc.validateFunc != nil {
					tc.validateFunc(t, updatedUser)
				}
			}
		})
	}
}

func TestRemoveSelfServeAccountsForTeam(t *testing.T) {
	bigtableFixture := NewBigtableFixture(t)
	defer bigtableFixture.Cleanup()

	sut := createAuthServicerSUT(t, bigtableFixture)
	authServicer := sut.authServicer
	daoFactory := sut.daoFactory

	userDao := daoFactory.GetUserDAO()

	t.Run("Auth checks", func(t *testing.T) {
		// Test with insufficient permissions
		claims := &auth.AugmentClaims{
			TenantID:         "admin-tenant-id",
			TenantName:       "admin-tenant",
			UserID:           "admin-user-id",
			OpaqueUserID:     "admin-user-id",
			OpaqueUserIDType: auth_entities.UserId_INTERNAL_IAP.String(),
			Scope:            []string{tokenscopesproto.Scope_AUTH_R.String()},
		}
		req := &authpb.RemoveSelfServeAccountsForTeamRequest{
			DryRun: true,
		}
		ctx := claims.NewContext(context.Background())
		_, err := authServicer.RemoveSelfServeAccountsForTeam(ctx, req)
		require.Error(t, err)
		require.Equal(t, codes.PermissionDenied, status.Code(err))
	})

	t.Run("DryRun", func(t *testing.T) {
		// Create a user with multiple tenants
		user := &auth_entities.User{
			Id:      "multi-tenant-user",
			Email:   "<EMAIL>",
			Tenants: []string{tenantID, professionalTenantID},
		}
		_, err := userDao.Create(context.Background(), user)
		require.NoError(t, err)

		ctx := createCentralAuthorizedRequestContext()

		// Run in dry-run mode
		req := &authpb.RemoveSelfServeAccountsForTeamRequest{
			DryRun: true,
		}
		resp, err := authServicer.RemoveSelfServeAccountsForTeam(ctx, req)
		require.NoError(t, err)

		// Verify response
		require.NotNil(t, resp)
		require.GreaterOrEqual(t, len(resp.RemovedUsers), 1)

		// Verify user still has both tenants
		updatedUser, err := userDao.Get(context.Background(), "multi-tenant-user")
		require.NoError(t, err)
		require.Contains(t, updatedUser.Tenants, tenantID)
		require.Contains(t, updatedUser.Tenants, professionalTenantID)
	})

	t.Run("ActualRemoval", func(t *testing.T) {
		// Create a user with multiple tenants
		user := &auth_entities.User{
			Id:      "multi-tenant-user-2",
			Email:   "<EMAIL>",
			Tenants: []string{tenantID, professionalTenantID},
		}
		_, err := userDao.Create(context.Background(), user)
		require.NoError(t, err)

		// Add user to tenants
		userTenantMappingDAO := daoFactory.GetUserTenantMappingDAO(tenantName)
		mapping := &auth_entities.UserTenantMapping{
			UserId: "multi-tenant-user-2",
			Tenant: tenantName,
		}
		_, err = userTenantMappingDAO.Create(context.Background(), mapping)
		require.NoError(t, err)

		userTenantMappingDAO = daoFactory.GetUserTenantMappingDAO("professional-tenant")
		mapping = &auth_entities.UserTenantMapping{
			UserId: "multi-tenant-user-2",
			Tenant: "professional-tenant",
		}
		_, err = userTenantMappingDAO.Create(context.Background(), mapping)
		require.NoError(t, err)

		ctx := createCentralAuthorizedRequestContext()

		// Run with actual removal
		req := &authpb.RemoveSelfServeAccountsForTeamRequest{
			DryRun: false,
		}
		resp, err := authServicer.RemoveSelfServeAccountsForTeam(ctx, req)
		require.NoError(t, err)

		// Verify response
		require.NotNil(t, resp)

		// Find our test user in the response
		var userRemoval *authpb.RemoveSelfServeAccountsForTeamResponse_UserRemovals
		for _, removal := range resp.RemovedUsers {
			if removal.UserId == "multi-tenant-user-2" {
				userRemoval = removal
				break
			}
		}

		// Verify user was processed
		require.NotNil(t, userRemoval)
		require.Equal(t, "multi-tenant-user-2", userRemoval.UserId)
		require.Len(t, userRemoval.PrimaryTenants, 1)
		require.Equal(t, tenantID, userRemoval.PrimaryTenants[0].TenantId)
		require.Len(t, userRemoval.RemovedTenants, 1)
		require.Equal(t, professionalTenantID, userRemoval.RemovedTenants[0].TenantId)

		// Verify user now only has the team tenant
		updatedUser, err := userDao.Get(context.Background(), "multi-tenant-user-2")
		require.NoError(t, err)
		require.NotContains(t, updatedUser.Tenants, professionalTenantID)
		require.Contains(t, updatedUser.Tenants, tenantID)
	})
}

func caseRemoveExtraSelfServeTenantsFromUsers(t *testing.T, bigtableFixture *BigtableFixture) {
	sut := createAuthServicerSUT(t, bigtableFixture)
	authServicer := sut.authServicer
	daoFactory := sut.daoFactory
	userDao := daoFactory.GetUserDAO()

	t.Run("Auth checks", func(t *testing.T) {
		// Test with insufficient permissions
		claims := &auth.AugmentClaims{
			TenantID:         "admin-tenant-id",
			TenantName:       "admin-tenant",
			UserID:           "admin-user-id",
			OpaqueUserID:     "admin-user-id",
			OpaqueUserIDType: auth_entities.UserId_INTERNAL_IAP.String(),
			Scope:            []string{tokenscopesproto.Scope_AUTH_R.String()},
		}
		req := &authpb.RemoveExtraSelfServeTenantsFromUsersRequest{}
		ctx := claims.NewContext(context.Background())
		_, err := authServicer.RemoveExtraSelfServeTenantsFromUsers(ctx, req)
		require.Error(t, err)
		require.Equal(t, codes.PermissionDenied, status.Code(err))
	})

	t.Run("DryRun", func(t *testing.T) {
		// Create a user with multiple tenants
		user := &auth_entities.User{
			Id:      "multi-tenant-user-extra",
			Email:   "<EMAIL>",
			Tenants: []string{professionalTenantID2, professionalTenantID},
		}
		_, err := userDao.Create(context.Background(), user)
		require.NoError(t, err)

		// Add user to tenants
		userTenantMappingDAO := daoFactory.GetUserTenantMappingDAO("professional-tenant")
		mapping := &auth_entities.UserTenantMapping{
			UserId: "multi-tenant-user-extra",
			Tenant: "professional-tenant",
		}
		_, err = userTenantMappingDAO.Create(context.Background(), mapping)
		require.NoError(t, err)

		userTenantMappingDAO = daoFactory.GetUserTenantMappingDAO("professional-tenant2")
		mapping = &auth_entities.UserTenantMapping{
			UserId: "multi-tenant-user-extra",
			Tenant: "professional-tenant2",
		}
		_, err = userTenantMappingDAO.Create(context.Background(), mapping)
		require.NoError(t, err)

		ctx := createCentralAuthorizedRequestContext()

		// Run in dry-run mode
		req := &authpb.RemoveExtraSelfServeTenantsFromUsersRequest{}
		resp, err := authServicer.RemoveExtraSelfServeTenantsFromUsers(ctx, req)
		require.NoError(t, err)

		// Verify response
		require.NotNil(t, resp)
		require.GreaterOrEqual(t, len(resp.RemovedUsers), 1)

		// Verify user still has both tenants
		updatedUser, err := userDao.Get(context.Background(), "multi-tenant-user-extra")
		require.NoError(t, err)
		require.Contains(t, updatedUser.Tenants, professionalTenantID)
		require.Contains(t, updatedUser.Tenants, professionalTenantID2)
	})

	t.Run("ActualRemoval", func(t *testing.T) {
		ctx := createCentralAuthorizedRequestContext()

		// Create a user with multiple tenants
		user := &auth_entities.User{
			Id:      "multi-tenant-user-extra-2",
			Email:   "<EMAIL>",
			Tenants: []string{professionalTenantID, professionalTenantID2},
		}
		_, err := userDao.Create(ctx, user)
		require.NoError(t, err)

		// Add user to tenants
		userTenantMappingDAO := daoFactory.GetUserTenantMappingDAO("professional-tenant2")
		mapping := &auth_entities.UserTenantMapping{
			UserId: "multi-tenant-user-extra-2",
			Tenant: "professional-tenant2",
		}
		_, err = userTenantMappingDAO.Create(ctx, mapping)
		require.NoError(t, err)

		userTenantMappingDAO = daoFactory.GetUserTenantMappingDAO("professional-tenant")
		mapping = &auth_entities.UserTenantMapping{
			UserId: "multi-tenant-user-extra-2",
			Tenant: "professional-tenant",
		}
		_, err = userTenantMappingDAO.Create(ctx, mapping)
		require.NoError(t, err)

		// Run with actual removal
		req := &authpb.RemoveExtraSelfServeTenantsFromUsersRequest{
			MakeChanges: true,
		}
		resp, err := authServicer.RemoveExtraSelfServeTenantsFromUsers(ctx, req)
		require.NoError(t, err)

		// Verify response
		require.NotNil(t, resp)
		require.GreaterOrEqual(t, len(resp.RemovedUsers), 1)

		// Find our test user in the response
		var userRemoval *authpb.RemoveExtraSelfServeTenantsFromUsersResponse_UserRemovals
		for _, removal := range resp.RemovedUsers {
			if removal.UserId == "multi-tenant-user-extra-2" {
				userRemoval = removal
				break
			}
		}

		// Verify user was processed
		require.NotNil(t, userRemoval)
		require.Equal(t, "multi-tenant-user-extra-2", userRemoval.UserId)
		require.Equal(t, professionalTenantID, userRemoval.PrimaryTenant.TenantId)
		require.Len(t, userRemoval.RemovedTenants, 1)
		require.Equal(t, professionalTenantID2, userRemoval.RemovedTenants[0].TenantId)

		// Verify user now only has the team tenant
		updatedUser, err := userDao.Get(context.Background(), "multi-tenant-user-extra-2")
		require.NoError(t, err)
		require.NotContains(t, updatedUser.Tenants, professionalTenantID2)
		require.Contains(t, updatedUser.Tenants, professionalTenantID)
	})

	t.Run("WithNamespaceFilter", func(t *testing.T) {
		// Create a user with multiple tenants
		ctx := createCentralAuthorizedRequestContext()

		user := &auth_entities.User{
			Id:      "multi-tenant-user-extra-3",
			Email:   "<EMAIL>",
			Tenants: []string{professionalTenantID, professionalTenantID2},
		}
		_, err := userDao.Create(ctx, user)
		require.NoError(t, err)

		// Add user to tenants
		userTenantMappingDAO := daoFactory.GetUserTenantMappingDAO("professional-tenant2")
		mapping := &auth_entities.UserTenantMapping{
			UserId: "multi-tenant-user-extra-3",
			Tenant: "professional-tenant2",
		}
		_, err = userTenantMappingDAO.Create(ctx, mapping)
		require.NoError(t, err)

		userTenantMappingDAO = daoFactory.GetUserTenantMappingDAO("professional-tenant")
		mapping = &auth_entities.UserTenantMapping{
			UserId: "multi-tenant-user-extra-3",
			Tenant: "professional-tenant",
		}
		_, err = userTenantMappingDAO.Create(ctx, mapping)
		require.NoError(t, err)

		// Run with actual removal
		req := &authpb.RemoveExtraSelfServeTenantsFromUsersRequest{
			MakeChanges:  true,
			NamespaceIds: []string{"professional-namespace"},
		}
		resp, err := authServicer.RemoveExtraSelfServeTenantsFromUsers(ctx, req)
		require.NoError(t, err)

		// Verify response
		require.NotNil(t, resp)
		require.GreaterOrEqual(t, len(resp.RemovedUsers), 1)

		// Find our test user in the response
		var userRemoval *authpb.RemoveExtraSelfServeTenantsFromUsersResponse_UserRemovals
		for _, removal := range resp.RemovedUsers {
			if removal.UserId == "multi-tenant-user-extra-3" {
				userRemoval = removal
				break
			}
		}

		// Verify user was processed
		require.NotNil(t, userRemoval)
		require.Equal(t, "multi-tenant-user-extra-3", userRemoval.UserId)
		require.Equal(t, professionalTenantID, userRemoval.PrimaryTenant.TenantId)
		require.Len(t, userRemoval.RemovedTenants, 1)
		require.Equal(t, professionalTenantID2, userRemoval.RemovedTenants[0].TenantId)

		// Verify user now only has the team tenant
		updatedUser, err := userDao.Get(context.Background(), "multi-tenant-user-extra-3")
		require.NoError(t, err)
		require.NotContains(t, updatedUser.Tenants, professionalTenantID2)
		require.Contains(t, updatedUser.Tenants, professionalTenantID)
	})
}

func caseRemoveDeletedTenantsFromUsers(t *testing.T, bigtableFixture *BigtableFixture) {
	sut := createAuthServicerSUT(t, bigtableFixture)
	authServicer := sut.authServicer
	daoFactory := sut.daoFactory
	userDao := daoFactory.GetUserDAO()

	t.Run("DryRun", func(t *testing.T) {
		// Create a user with multiple tenants
		user := &auth_entities.User{
			Id:      "multi-tenant-user-deleted",
			Email:   "<EMAIL>",
			Tenants: []string{professionalTenantID, deletedTenantID},
		}
		_, err := userDao.Create(context.Background(), user)
		require.NoError(t, err)

		// Add user to tenants
		userTenantMappingDAO := daoFactory.GetUserTenantMappingDAO("professional-tenant")
		mapping := &auth_entities.UserTenantMapping{
			UserId: "multi-tenant-user-deleted",
			Tenant: "professional-tenant",
		}
		_, err = userTenantMappingDAO.Create(context.Background(), mapping)
		require.NoError(t, err)

		userTenantMappingDAO = daoFactory.GetUserTenantMappingDAO("deleted-tenant")
		mapping = &auth_entities.UserTenantMapping{
			UserId: "multi-tenant-user-deleted",
			Tenant: "deleted-tenant",
		}
		_, err = userTenantMappingDAO.Create(context.Background(), mapping)
		require.NoError(t, err)

		ctx := createCentralAuthorizedRequestContext()

		// Run in dry-run mode
		req := &authpb.RemoveDeletedTenantsFromUsersRequest{}
		resp, err := authServicer.RemoveDeletedTenantsFromUsers(ctx, req)
		require.NoError(t, err)

		// Verify response
		require.NotNil(t, resp)
		require.GreaterOrEqual(t, len(resp.RemovedUsers), 1)

		// Verify user still has both tenants
		updatedUser, err := userDao.Get(context.Background(), "multi-tenant-user-deleted")
		require.NoError(t, err)
		require.Contains(t, updatedUser.Tenants, professionalTenantID)
		require.Contains(t, updatedUser.Tenants, deletedTenantID)
	})

	t.Run("ActualRemoval", func(t *testing.T) {
		// Create a user with multiple tenants
		user := &auth_entities.User{
			Id:      "multi-tenant-user-deleted-2",
			Email:   "<EMAIL>",
			Tenants: []string{professionalTenantID, deletedTenantID},
		}
		_, err := userDao.Create(context.Background(), user)
		require.NoError(t, err)

		// Add user to tenants
		userTenantMappingDAO := daoFactory.GetUserTenantMappingDAO("professional-tenant")
		mapping := &auth_entities.UserTenantMapping{
			UserId: "multi-tenant-user-deleted-2",
			Tenant: "professional-tenant",
		}
		_, err = userTenantMappingDAO.Create(context.Background(), mapping)
		require.NoError(t, err)

		userTenantMappingDAO = daoFactory.GetUserTenantMappingDAO("deleted-tenant")
		mapping = &auth_entities.UserTenantMapping{
			UserId: "multi-tenant-user-deleted-2",
			Tenant: "deleted-tenant",
		}
		_, err = userTenantMappingDAO.Create(context.Background(), mapping)
		require.NoError(t, err)

		ctx := createCentralAuthorizedRequestContext()

		// Run with actual removal
		req := &authpb.RemoveDeletedTenantsFromUsersRequest{
			MakeChanges: true,
		}
		resp, err := authServicer.RemoveDeletedTenantsFromUsers(ctx, req)
		require.NoError(t, err)

		// Verify response
		require.NotNil(t, resp)
		require.GreaterOrEqual(t, len(resp.RemovedUsers), 1)

		// Verify user now only has the team tenant
		updatedUser, err := userDao.Get(context.Background(), "multi-tenant-user-deleted-2")
		require.NoError(t, err)
		require.NotContains(t, updatedUser.Tenants, deletedTenantID)
		require.Contains(t, updatedUser.Tenants, professionalTenantID)
	})

	t.Run("ActualRemovalWithTenantMappings", func(t *testing.T) {
		// Create a user with multiple tenants
		user := &auth_entities.User{
			Id:      "multi-tenant-user-deleted-3",
			Email:   "<EMAIL>",
			Tenants: []string{professionalTenantID, "deleted-tenant-id-2"}, // using a tenant that is not setup in tenant map
		}
		_, err := userDao.Create(context.Background(), user)
		require.NoError(t, err)

		// Add user to tenants
		userTenantMappingDAO := daoFactory.GetUserTenantMappingDAO("professional-tenant")
		mapping := &auth_entities.UserTenantMapping{
			UserId: "multi-tenant-user-deleted-3",
			Tenant: "professional-tenant",
		}
		_, err = userTenantMappingDAO.Create(context.Background(), mapping)
		require.NoError(t, err)

		userTenantMappingDAO = daoFactory.GetUserTenantMappingDAO("deleted-tenant-name-2")
		mapping = &auth_entities.UserTenantMapping{
			UserId: "multi-tenant-user-deleted-3",
			Tenant: "deleted-tenant-name-2",
		}
		_, err = userTenantMappingDAO.Create(context.Background(), mapping)
		require.NoError(t, err)

		ctx := createCentralAuthorizedRequestContext()

		// Run with actual removal
		req := &authpb.RemoveDeletedTenantsFromUsersRequest{
			MakeChanges: true,
			TenantMappingsForDeletedTenants: map[string]string{
				"deleted-tenant-id-2": "deleted-tenant-name-2",
			},
		}
		resp, err := authServicer.RemoveDeletedTenantsFromUsers(ctx, req)
		require.NoError(t, err)

		// Verify response
		require.NotNil(t, resp)
		require.GreaterOrEqual(t, len(resp.RemovedUsers), 1)

		// Verify user now only has the team tenant
		updatedUser, err := userDao.Get(context.Background(), "multi-tenant-user-deleted-3")
		require.NoError(t, err)
		require.NotContains(t, updatedUser.Tenants, "deleted-tenant-name-2")
		require.Contains(t, updatedUser.Tenants, professionalTenantID)
	})
}

func caseGetUsers(t *testing.T, bigtableFixture *BigtableFixture) {
	sut := createAuthServicerSUT(t, bigtableFixture)

	t.Run("standard context should fail", func(t *testing.T) {
		ctx := createAuthorizedRequestContext()
		_, err := sut.authServicer.GetUsers(ctx, &authpb.GetUsersRequest{})
		require.Error(t, err)
	})

	t.Run("PII_ADMIN required for deleted users", func(t *testing.T) {
		claims := &auth.AugmentClaims{
			Cloud: "test-cloud",
			Scope: []string{"AUTH_RW", "AUTH_R"},
		}
		ctx := claims.NewContext(context.Background())
		_, err := sut.authServicer.GetUsers(ctx, &authpb.GetUsersRequest{
			PageSize:               10,
			IncludeGdprCcpaDeleted: true,
		})
		require.Error(t, err)
	})

	t.Run("deleted users cases", func(t *testing.T) {
		ctx := createCentralAuthorizedRequestContext()
		userDAO := sut.daoFactory.GetUserDAO()

		// Create a deleted user
		userID := uuid.New().String()
		user := &auth_entities.User{
			Id:            userID,
			Email:         "<EMAIL>",
			Tenants:       []string{},
			DeletionState: auth_entities.User_GDPR_CCPA_DELETED,
		}
		_, err := userDAO.Create(ctx, user)
		require.NoError(t, err)

		// Don't return deleted users by default.
		resp, err := sut.authServicer.GetUsers(ctx, &authpb.GetUsersRequest{
			PageSize: 10,
		})
		require.NoError(t, err)
		require.Empty(t, resp.Users)

		// Return deleted users when requested.
		resp, err = sut.authServicer.GetUsers(ctx, &authpb.GetUsersRequest{
			PageSize:               10,
			IncludeGdprCcpaDeleted: true,
		})
		require.NoError(t, err)
		require.Len(t, resp.Users, 1)
		require.Equal(t, userID, resp.Users[0].Id)
	})

	ctx := createCentralAuthorizedRequestContext()

	// Create users
	users := make([]*auth_entities.User, 0)
	for i := 0; i < 9; i++ {
		users = append(users, sut.createUser(t, createUserOpts{}))
	}
	fred := sut.createUser(t, createUserOpts{email: "<EMAIL>"})
	users = append(users, fred)

	userIDs := make([]string, 0, len(users))
	for _, user := range users {
		userIDs = append(userIDs, user.Id)
	}
	slices.Sort(userIDs)

	// Get all users
	cases := []struct {
		name          string
		request       *authpb.GetUsersRequest
		expectedUsers []string
	}{
		{
			name: "All users",
			request: &authpb.GetUsersRequest{
				PageSize: 10,
			},
			expectedUsers: userIDs,
		},
		{
			name: "Five at a time",
			request: &authpb.GetUsersRequest{
				PageSize: 5,
			},
			expectedUsers: userIDs,
		},
		{
			name:          "No page size requested",
			request:       &authpb.GetUsersRequest{},
			expectedUsers: userIDs,
		},
		{
			name: "Search for FRED",
			request: &authpb.GetUsersRequest{
				PageSize:     10,
				SearchString: "fred",
			},
			expectedUsers: []string{fred.Id},
		},
		{
			name: "Search for fred",
			request: &authpb.GetUsersRequest{
				PageSize:     10,
				SearchString: "FRED",
			},
			expectedUsers: []string{fred.Id},
		},
	}

	for _, tc := range cases {
		t.Run(tc.name, func(t *testing.T) {
			request := tc.request

			var nextPageToken *string = nil
			users := make([]*auth_entities.User, 0, len(tc.expectedUsers))
			for {
				if nextPageToken != nil {
					request.PageToken = *nextPageToken
				}
				response, err := sut.authServicer.GetUsers(ctx, request)
				require.NoError(t, err)
				for _, user := range response.Users {
					users = append(users, user)
				}
				if response.NextPageToken == "" {
					break
				}
				nextPageToken = &response.NextPageToken
			}
			require.Len(t, users, len(tc.expectedUsers))
			for idx, user := range users {
				require.Equal(t, tc.expectedUsers[idx], user.Id)
			}
		})
	}

	request := &authpb.GetUsersRequest{
		PageSize: 10,
	}
	response, err := sut.authServicer.GetUsers(ctx, request)
	require.NoError(t, err)
	require.Len(t, response.Users, 10)
	require.Empty(t, response.NextPageToken)
	for idx, user := range response.Users {
		require.Equal(t, userIDs[idx], user.Id)
	}

	// Get users with a search string
}

func caseForgetUser(t *testing.T, bigtableFixture *BigtableFixture) {
	sut := createAuthServicerSUT(t, bigtableFixture)
	authServicer := sut.authServicer
	daoFactory := sut.daoFactory

	t.Run("AuthChecks", func(t *testing.T) {
		// Test with insufficient permissions
		claims := &auth.AugmentClaims{
			TenantID:         "",
			TenantName:       "",
			UserID:           "<EMAIL>",
			OpaqueUserID:     "<EMAIL>",
			OpaqueUserIDType: auth_entities.UserId_INTERNAL_IAP.String(),
			Scope:            []string{tokenscopesproto.Scope_AUTH_R.String()},
		}
		ctx := claims.NewContext(context.Background())
		_, err := authServicer.ForgetUser(ctx, &authpb.ForgetUserRequest{})
		require.Error(t, err)
		require.Equal(t, codes.PermissionDenied, status.Code(err))
	})

	t.Run("require no tenants", func(t *testing.T) {
		ctx := createCentralAuthorizedRequestContext()
		userDAO := daoFactory.GetUserDAO()

		// Create a user
		userID := uuid.New().String()
		user := &auth_entities.User{
			Id:      userID,
			Email:   "<EMAIL>",
			Tenants: []string{"test-tenant-id"},
		}
		_, err := userDAO.Create(ctx, user)
		require.NoError(t, err)

		// Call ForgetUser
		_, err = authServicer.ForgetUser(ctx, &authpb.ForgetUserRequest{
			UserId: userID,
		})
		require.Error(t, err)
		require.Equal(t, codes.InvalidArgument, status.Code(err))

		// Verify the user is not marked as deleted.
		user, err = userDAO.Get(ctx, userID)
		require.NoError(t, err)
		require.Equal(t, auth_entities.User_NOT_DELETED, user.GetDeletionState())
	})

	t.Run("success", func(t *testing.T) {
		ctx := createCentralAuthorizedRequestContext()
		userDAO := daoFactory.GetUserDAO()
		codeDAO := daoFactory.GetCodeDAO()
		termsDAO := daoFactory.GetTermsDAO()

		// Create a user
		userID := uuid.New().String()
		userEmail := "<EMAIL>"
		user := &auth_entities.User{
			Id:      userID,
			Email:   userEmail,
			Tenants: []string{},
		}
		_, err := userDAO.Create(ctx, user)
		require.NoError(t, err)

		// Create Code entities for this user
		code1 := &auth_entities.Code{
			Code:          "code1",
			AugmentUserId: userID,
			Email:         userEmail,
		}
		_, err = codeDAO.Create(ctx, code1)
		require.NoError(t, err)

		code2 := &auth_entities.Code{
			Code:          "code2",
			AugmentUserId: userID,
			Email:         userEmail,
		}
		_, err = codeDAO.Create(ctx, code2)
		require.NoError(t, err)

		// Create TermsApproval entities for this user's email
		terms1 := &auth_entities.TermsApproval{
			Email:    userEmail,
			Revision: "v1.0",
		}
		_, err = termsDAO.Create(ctx, terms1)
		require.NoError(t, err)

		terms2 := &auth_entities.TermsApproval{
			Email:    userEmail,
			Revision: "v2.0",
		}
		_, err = termsDAO.Create(ctx, terms2)
		require.NoError(t, err)

		// Create TenantInvitation entities where this user is the invitee
		tenantInvitationDAO1 := daoFactory.GetTenantInvitationDAO("tenant1")
		inviteeInvitation1 := &auth_entities.TenantInvitation{
			Id:            "invitee-invitation-1",
			CreatedAt:     timestamppb.Now(),
			InviteeEmail:  userEmail,
			TenantId:      "tenant1",
			InviterUserId: "other-user-1",
			InviterEmail:  "<EMAIL>",
			Status:        auth_entities.TenantInvitation_PENDING,
		}
		_, err = tenantInvitationDAO1.Create(ctx, inviteeInvitation1)
		require.NoError(t, err)

		tenantInvitationDAO2 := daoFactory.GetTenantInvitationDAO("tenant2")
		inviteeInvitation2 := &auth_entities.TenantInvitation{
			Id:            "invitee-invitation-2",
			CreatedAt:     timestamppb.Now(),
			InviteeEmail:  userEmail,
			TenantId:      "tenant2",
			InviterUserId: "other-user-2",
			InviterEmail:  "<EMAIL>",
			Status:        auth_entities.TenantInvitation_PENDING,
		}
		_, err = tenantInvitationDAO2.Create(ctx, inviteeInvitation2)
		require.NoError(t, err)

		// Call ForgetUser
		_, err = authServicer.ForgetUser(ctx, &authpb.ForgetUserRequest{
			UserId: userID,
		})
		require.NoError(t, err)

		// Verify the user is marked as deleted
		user, err = userDAO.Get(ctx, userID)
		require.NoError(t, err)
		require.Equal(t, auth_entities.User_GDPR_CCPA_DELETED, user.GetDeletionState())

		// Verify Code entities are deleted
		code, err := codeDAO.Get(ctx, "code1")
		require.NoError(t, err)
		require.Nil(t, code)
		code, err = codeDAO.Get(ctx, "code2")
		require.NoError(t, err)
		require.Nil(t, code)

		// Verify TermsApproval entities are deleted
		terms, err := termsDAO.Get(ctx, userEmail, "v1.0")
		require.NoError(t, err)
		require.Nil(t, terms)
		terms, err = termsDAO.Get(ctx, userEmail, "v2.0")
		require.NoError(t, err)
		require.Nil(t, terms)

		// Verify TenantInvitation entities where user was invitee are deleted
		inviteeInvitation, err := tenantInvitationDAO1.Get(ctx, "invitee-invitation-1")
		require.NoError(t, err)
		require.Nil(t, inviteeInvitation)
		inviteeInvitation, err = tenantInvitationDAO2.Get(ctx, "invitee-invitation-2")
		require.NoError(t, err)
		require.Nil(t, inviteeInvitation)
	})
}

func TestGetOrbSubscriptionTypeWithNilSubscription(t *testing.T) {
	bigtableFixture := NewBigtableFixture(t)
	defer bigtableFixture.Cleanup()

	sut := createAuthServicerSUT(t, bigtableFixture)
	authServicer := sut.authServicer
	daoFactory := sut.daoFactory

	ctx := createAuthorizedRequestContext()

	// Create a user with Orb billing method and a subscription ID that doesn't exist in the database
	userDao := daoFactory.GetUserDAO()
	user := &auth_entities.User{
		Id:                "test-orb-user-id",
		Email:             "<EMAIL>",
		Tenants:           []string{professionalTenantID},
		OrbCustomerId:     "orb-customer-123",
		OrbSubscriptionId: "non-existent-orb-subscription-id", // This ID doesn't exist in the database
	}
	_, err := userDao.Create(ctx, user)
	require.NoError(t, err)

	// Create user tenant mapping
	userTenantMappingDAO := daoFactory.GetUserTenantMappingDAO("professional-tenant")
	userMapping := &auth_entities.UserTenantMapping{
		UserId: "test-orb-user-id",
		Tenant: "professional-tenant",
	}
	_, err = userTenantMappingDAO.Create(ctx, userMapping)
	require.NoError(t, err)

	// Create a token hash for the user
	tokenHashDAO := daoFactory.GetTokenHashDAO()
	tokenHash := &auth_entities.TokenHash{
		Hash:          "2cf24dba5fb0a30e26e83b2ac5b9e29e1b161e5c1fa7425e73043362938b9824", // sha256 of "hello"
		AugmentUserId: "test-orb-user-id",
		EmailAddress:  "<EMAIL>",
		TenantId:      professionalTenantID,
	}
	_, err = tokenHashDAO.Create(ctx, tokenHash)
	require.NoError(t, err)

	// Call GetTokenInfo - this should not crash even though the subscription doesn't exist
	request := &authpb.GetTokenInfoRequest{Token: "hello"}
	response, err := authServicer.GetTokenInfo(ctx, request)

	// Verify the call succeeds and returns a response
	require.NoError(t, err)
	require.NotNil(t, response)

	// With our fix, we should get an active subscription when subscription is nil
	// The code treats a nil subscription as active with a future end date
	activeSub, ok := response.Subscription.(*authpb.GetTokenInfoResponse_ActiveSubscription)
	require.True(t, ok, "Expected active subscription but got %T", response.Subscription)
	require.NotNil(t, activeSub.ActiveSubscription)
	require.NotNil(t, activeSub.ActiveSubscription.EndDate, "EndDate should not be nil")
	// EndDate should be in the future (14 days from now)
	require.True(t, time.Now().Add(13*24*time.Hour).Before(activeSub.ActiveSubscription.EndDate.AsTime()),
		"EndDate should be at least 13 days in the future")
}

func TestGetOrbSubscriptionTypeWithPendingSubscription(t *testing.T) {
	bigtableFixture := NewBigtableFixture(t)
	defer bigtableFixture.Cleanup()

	sut := createAuthServicerSUT(t, bigtableFixture)
	authServicer := sut.authServicer
	daoFactory := sut.daoFactory

	ctx := createAuthorizedRequestContext()

	// Create a user with Orb billing method, no orb subscription id, and a pending creation
	userDao := daoFactory.GetUserDAO()
	user := &auth_entities.User{
		Id:      "test-orb-user-id",
		Email:   "<EMAIL>",
		Tenants: []string{professionalTenantID},
		SubscriptionCreationInfo: &auth_entities.User_SubscriptionCreationInfo{
			Status:    auth_entities.User_SubscriptionCreationInfo_PENDING,
			Id:        "test-subscription-creation",
			CreatedAt: timestamppb.Now(),
			UpdatedAt: timestamppb.Now(),
		},
	}
	_, err := userDao.Create(ctx, user)
	require.NoError(t, err)

	// Create user tenant mapping
	userTenantMappingDAO := daoFactory.GetUserTenantMappingDAO("professional-tenant")
	userMapping := &auth_entities.UserTenantMapping{
		UserId: "test-orb-user-id",
		Tenant: "professional-tenant",
	}
	_, err = userTenantMappingDAO.Create(ctx, userMapping)
	require.NoError(t, err)

	// Create a token hash for the user
	tokenHashDAO := daoFactory.GetTokenHashDAO()
	tokenHash := &auth_entities.TokenHash{
		Hash:          "2cf24dba5fb0a30e26e83b2ac5b9e29e1b161e5c1fa7425e73043362938b9824", // sha256 of "hello"
		AugmentUserId: "test-orb-user-id",
		EmailAddress:  "<EMAIL>",
		TenantId:      professionalTenantID,
	}
	_, err = tokenHashDAO.Create(ctx, tokenHash)
	require.NoError(t, err)

	// Call GetTokenInfo - this should not crash even though the subscription doesn't exist yet
	request := &authpb.GetTokenInfoRequest{Token: "hello"}
	response, err := authServicer.GetTokenInfo(ctx, request)

	// Verify the call succeeds and returns a response
	require.NoError(t, err)
	require.NotNil(t, response)

	// With our fix, we should get an active subscription when subscription is nil
	// The code treats a nil subscription as active with a future end date
	activeSub, ok := response.Subscription.(*authpb.GetTokenInfoResponse_ActiveSubscription)
	require.True(t, ok, "Expected active subscription but got %T", response.Subscription)
	require.NotNil(t, activeSub.ActiveSubscription)
	require.Nil(t, activeSub.ActiveSubscription.EndDate)
}

func caseTestDeduplicateUsersByEmail(t *testing.T, bigtableFixture *BigtableFixture) {
	sut := createAuthServicerSUT(t, bigtableFixture)
	authServicer := sut.authServicer
	// Inject a mock orb client
	mockOrbClient := orb.NewMockOrbClient()
	authServicer.orbClient = mockOrbClient
	userDAO := sut.daoFactory.GetUserDAO()

	ctx := createCentralAuthAdminRequestContext()

	t.Run("DryRun - Simple duplicate case", func(t *testing.T) {
		bigtableFixture.ClearTable(t)

		// Create two users with the same email
		user1 := &auth_entities.User{
			Id:      "duplicate-user-1",
			Email:   "<EMAIL>",
			Tenants: []string{tenantID},
		}
		user2 := &auth_entities.User{
			Id:      "duplicate-user-2",
			Email:   "<EMAIL>",
			Tenants: []string{tenantID},
		}

		_, err := userDAO.Create(ctx, user1)
		require.NoError(t, err)
		_, err = userDAO.Create(ctx, user2)
		require.NoError(t, err)

		// Add users to tenants
		tenantMappingDAO := sut.daoFactory.GetUserTenantMappingDAO(tenantName)
		mapping1 := &auth_entities.UserTenantMapping{
			UserId: "duplicate-user-1",
			Tenant: tenantName,
		}
		_, err = tenantMappingDAO.Create(ctx, mapping1)
		require.NoError(t, err)

		mapping2 := &auth_entities.UserTenantMapping{
			UserId: "duplicate-user-2",
			Tenant: tenantName,
		}
		_, err = tenantMappingDAO.Create(ctx, mapping2)
		require.NoError(t, err)

		// Run merge in dry-run mode
		resp, err := authServicer.MergeDuplicateAccounts(ctx, &authpb.MergeDuplicateAccountsRequest{
			UserIds: []string{"duplicate-user-1", "duplicate-user-2"},
			DryRun:  true,
		})
		require.NoError(t, err)
		require.NotNil(t, resp)
		require.Empty(t, resp.FailedUsers, "Should have no failed users")
		require.NotNil(t, resp.UserMerge, "Should have one merge info")

		merge := resp.UserMerge
		assert.Equal(t, "<EMAIL>", merge.Email)
		assert.Equal(t, "duplicate-user-1", merge.PrimaryUserId, "Primary user should be the one with lowest ID")
		assert.Contains(t, merge.MergedUserIds, "duplicate-user-2", "MergedUserIds should contain the duplicate user ID")
		assert.Equal(t, "duplicate-user-1", merge.SubscriptionSourceUserId, "Should use primary user as subscription source when no subscriptions")

		// Verify users still exist (dry-run shouldn't change anything)
		_, err = userDAO.Get(ctx, "duplicate-user-1")
		require.NoError(t, err)
		_, err = userDAO.Get(ctx, "duplicate-user-2")
		require.NoError(t, err)
	})

	t.Run("DryRun - Case insensitive email matching", func(t *testing.T) {
		bigtableFixture.ClearTable(t)

		// Create users with same email but different cases
		user1 := &auth_entities.User{
			Id:      "case-user-1",
			Email:   "<EMAIL>",
			Tenants: []string{tenantID},
		}
		user2 := &auth_entities.User{
			Id:      "case-user-2",
			Email:   "<EMAIL>",
			Tenants: []string{tenantID},
		}

		_, err := userDAO.Create(ctx, user1)
		require.NoError(t, err)
		_, err = userDAO.Create(ctx, user2)
		require.NoError(t, err)

		// Add users to tenants
		tenantMappingDAO := sut.daoFactory.GetUserTenantMappingDAO(tenantName)
		mapping1 := &auth_entities.UserTenantMapping{
			UserId: "case-user-1",
			Tenant: tenantName,
		}
		_, err = tenantMappingDAO.Create(ctx, mapping1)
		require.NoError(t, err)

		mapping2 := &auth_entities.UserTenantMapping{
			UserId: "case-user-2",
			Tenant: tenantName,
		}
		_, err = tenantMappingDAO.Create(ctx, mapping2)
		require.NoError(t, err)

		// Run merge in dry-run mode
		resp, err := authServicer.MergeDuplicateAccounts(ctx, &authpb.MergeDuplicateAccountsRequest{
			UserIds: []string{"case-user-1", "case-user-2"},
			DryRun:  true,
		})
		require.NoError(t, err)
		require.NotNil(t, resp)
		require.Empty(t, resp.FailedUsers, "Should have no failed users")
		require.NotNil(t, resp.UserMerge, "Should have merge info")

		merge := resp.UserMerge
		assert.Equal(t, "<EMAIL>", merge.Email, "Email should be normalized to lowercase")
		assert.Equal(t, "case-user-1", merge.PrimaryUserId)
		assert.Contains(t, merge.MergedUserIds, "case-user-2", "MergedUserIds should contain the duplicate user ID")

		// Verify merged emails are populated
		assert.Len(t, merge.MergedEmails, 2, "Should have both emails in MergedEmails")
		assert.Contains(t, merge.MergedEmails, "<EMAIL>")
		assert.Contains(t, merge.MergedEmails, "<EMAIL>")
	})
}

func caseTestDeduplicateUsersByEmailActualMerge(t *testing.T, bigtableFixture *BigtableFixture) {
	sut := createAuthServicerSUT(t, bigtableFixture)
	authServicer := sut.authServicer
	// Inject a mock orb client
	mockOrbClient := orb.NewMockOrbClient()
	authServicer.orbClient = mockOrbClient
	userDAO := sut.daoFactory.GetUserDAO()

	ctx := createCentralAuthAdminRequestContext()

	t.Run("Actual merge - Simple case", func(t *testing.T) {
		bigtableFixture.ClearTable(t)

		// Create two users with the same email
		user1 := &auth_entities.User{
			Id:      "merge-user-1",
			Email:   "<EMAIL>",
			Tenants: []string{tenantID},
		}
		user2 := &auth_entities.User{
			Id:      "merge-user-2",
			Email:   "<EMAIL>",
			Tenants: []string{tenantID},
		}

		_, err := userDAO.Create(ctx, user1)
		require.NoError(t, err)
		_, err = userDAO.Create(ctx, user2)
		require.NoError(t, err)

		// add users to tenants
		tenantMappingDAO := sut.daoFactory.GetUserTenantMappingDAO(tenantName)
		mapping1 := &auth_entities.UserTenantMapping{
			UserId: "merge-user-1",
			Tenant: tenantName,
		}
		_, err = tenantMappingDAO.Create(ctx, mapping1)
		require.NoError(t, err)

		mapping2 := &auth_entities.UserTenantMapping{
			UserId: "merge-user-2",
			Tenant: tenantName,
		}
		_, err = tenantMappingDAO.Create(ctx, mapping2)
		require.NoError(t, err)

		// Run merge with actual changes
		resp, err := authServicer.MergeDuplicateAccounts(ctx, &authpb.MergeDuplicateAccountsRequest{
			UserIds: []string{"merge-user-1", "merge-user-2"},
			DryRun:  false,
		})
		require.NoError(t, err)
		require.NotNil(t, resp)
		require.Empty(t, resp.FailedUsers, "Should have no failed users")
		require.NotNil(t, resp.UserMerge, "Should have merge info")
		merge := resp.UserMerge
		assert.Equal(t, "<EMAIL>", merge.Email)
		assert.Equal(t, "merge-user-1", merge.PrimaryUserId)
		assert.Contains(t, merge.MergedUserIds, "merge-user-2")

		// Verify primary user still exists
		primaryUser, err := userDAO.Get(ctx, "merge-user-1")
		require.NoError(t, err)
		assert.Equal(t, "<EMAIL>", primaryUser.Email)

		// Verify merged user was deleted (has no tenants)
		mergedUser, err := userDAO.Get(ctx, "merge-user-2")
		require.NoError(t, err)
		assert.Equal(t, 0, len(mergedUser.Tenants))
	})

	t.Run("Actual merge - Users with no tenants are skipped", func(t *testing.T) {
		bigtableFixture.ClearTable(t)

		// Create users - one with tenants, one without
		user1 := &auth_entities.User{
			Id:      "no-tenant-user-1",
			Email:   "<EMAIL>",
			Tenants: []string{tenantID},
		}
		user2 := &auth_entities.User{
			Id:      "no-tenant-user-2",
			Email:   "<EMAIL>",
			Tenants: nil, // No tenants - should be skipped
		}

		_, err := userDAO.Create(ctx, user1)
		require.NoError(t, err)
		_, err = userDAO.Create(ctx, user2)
		require.NoError(t, err)

		// Add user with tenants to tenant
		tenantMappingDAO := sut.daoFactory.GetUserTenantMappingDAO(tenantName)
		mapping1 := &auth_entities.UserTenantMapping{
			UserId: "no-tenant-user-1",
			Tenant: tenantName,
		}
		_, err = tenantMappingDAO.Create(ctx, mapping1)
		require.NoError(t, err)

		// Run merge with actual changes
		resp, err := authServicer.MergeDuplicateAccounts(ctx, &authpb.MergeDuplicateAccountsRequest{
			UserIds: []string{"no-tenant-user-1", "no-tenant-user-2"},
			DryRun:  false,
		})
		require.NoError(t, err)
		require.NotNil(t, resp)
		require.Empty(t, resp.FailedUsers, "Should have no failed users")
		require.Empty(t, resp.UserMerge, "Should have no merge info")

		// Verify both users still exist
		_, err = userDAO.Get(ctx, "no-tenant-user-1")
		require.NoError(t, err)
		_, err = userDAO.Get(ctx, "no-tenant-user-2")
		require.NoError(t, err)
	})
}

func caseTestDeduplicateUsersByEmailComplexScenarios(t *testing.T, bigtableFixture *BigtableFixture) {
	sut := createAuthServicerSUT(t, bigtableFixture)
	authServicer := sut.authServicer
	// Inject a mock orb client
	mockOrbClient := orb.NewMockOrbClient()
	authServicer.orbClient = mockOrbClient
	userDAO := sut.daoFactory.GetUserDAO()

	ctx := createCentralAuthAdminRequestContext()

	t.Run("Users with different tenants", func(t *testing.T) {
		bigtableFixture.ClearTable(t)

		// Create users with same email but different tenants
		user1 := &auth_entities.User{
			Id:      "tenant-user-1",
			Email:   "<EMAIL>",
			Tenants: []string{tenantID},
		}
		user2 := &auth_entities.User{
			Id:      "tenant-user-2",
			Email:   "<EMAIL>",
			Tenants: []string{professionalTenantID},
		}

		_, err := userDAO.Create(ctx, user1)
		require.NoError(t, err)
		_, err = userDAO.Create(ctx, user2)
		require.NoError(t, err)

		// Create tenant mappings
		tenantMappingDAO := sut.daoFactory.GetUserTenantMappingDAO(tenantName)
		mapping1 := &auth_entities.UserTenantMapping{
			UserId: "tenant-user-1",
			Tenant: tenantName,
		}
		_, err = tenantMappingDAO.Create(ctx, mapping1)
		require.NoError(t, err)

		tenantMappingDAO = sut.daoFactory.GetUserTenantMappingDAO("professional-tenant")
		mapping2 := &auth_entities.UserTenantMapping{
			UserId: "tenant-user-2",
			Tenant: "professional-tenant",
		}
		_, err = tenantMappingDAO.Create(ctx, mapping2)
		require.NoError(t, err)

		resp, err := authServicer.MergeDuplicateAccounts(ctx, &authpb.MergeDuplicateAccountsRequest{
			UserIds: []string{"tenant-user-1", "tenant-user-2"},
			DryRun:  true,
		})
		require.NoError(t, err)
		require.NotNil(t, resp)
		require.Empty(t, resp.FailedUsers, "Should have no failed users")
		require.NotNil(t, resp.UserMerge, "Should have merge info")

		merge := resp.UserMerge
		assert.Equal(t, "<EMAIL>", merge.Email)
		assert.Equal(t, "tenant-user-1", merge.PrimaryUserId)
		assert.Contains(t, merge.MergedUserIds, "tenant-user-2")
	})

	t.Run("Whitespace and email normalization", func(t *testing.T) {
		bigtableFixture.ClearTable(t)

		// Create users with emails that have whitespace and different cases
		user1 := &auth_entities.User{
			Id:      "whitespace-user-1",
			Email:   "  <EMAIL>  ",
			Tenants: []string{tenantID},
		}
		user2 := &auth_entities.User{
			Id:      "whitespace-user-2",
			Email:   "<EMAIL>",
			Tenants: []string{tenantID},
		}

		_, err := userDAO.Create(ctx, user1)
		require.NoError(t, err)
		_, err = userDAO.Create(ctx, user2)
		require.NoError(t, err)

		// Add users to tenants
		tenantMappingDAO := sut.daoFactory.GetUserTenantMappingDAO(tenantName)
		mapping1 := &auth_entities.UserTenantMapping{
			UserId: "whitespace-user-1",
			Tenant: tenantName,
		}
		_, err = tenantMappingDAO.Create(ctx, mapping1)
		require.NoError(t, err)

		mapping2 := &auth_entities.UserTenantMapping{
			UserId: "whitespace-user-2",
			Tenant: tenantName,
		}
		_, err = tenantMappingDAO.Create(ctx, mapping2)
		require.NoError(t, err)

		// Run merge in dry-run mode
		resp, err := authServicer.MergeDuplicateAccounts(ctx, &authpb.MergeDuplicateAccountsRequest{
			UserIds: []string{"whitespace-user-1", "whitespace-user-2"},
			DryRun:  true,
		})
		require.NoError(t, err)
		require.NotNil(t, resp)
		require.Empty(t, resp.FailedUsers, "Should have no failed users")
		require.NotNil(t, resp.UserMerge, "Should have merge info")

		merge := resp.UserMerge
		assert.Equal(t, "<EMAIL>", merge.Email, "Email should be normalized (trimmed and lowercased)")
		assert.Equal(t, "whitespace-user-1", merge.PrimaryUserId)
		assert.Contains(t, merge.MergedUserIds, "whitespace-user-2")

		// Verify merged emails are populated
		assert.Contains(t, merge.MergedEmails, "<EMAIL>")
		assert.Contains(t, merge.MergedEmails, "  <EMAIL>  ")
	})
	t.Run("Actual merge with subscription transfer from higher ID to lower ID", func(t *testing.T) {
		bigtableFixture.ClearTable(t)

		// Create users with different subscription information
		user1 := &auth_entities.User{
			Id:                "merge-sub-user-1",
			Email:             "<EMAIL>",
			Tenants:           []string{tenantID},
			OrbCustomerId:     "orb-customer-merge-1",
			OrbSubscriptionId: "orb-sub-merge-1",
		}
		user2 := &auth_entities.User{
			Id:                "merge-sub-user-2",
			Email:             "<EMAIL>",
			Tenants:           []string{tenantID},
			OrbCustomerId:     "orb-customer-merge-2",
			OrbSubscriptionId: "orb-sub-merge-2",
		}

		_, err := userDAO.Create(ctx, user1)
		require.NoError(t, err)
		_, err = userDAO.Create(ctx, user2)
		require.NoError(t, err)

		// Add users to tenants
		tenantMappingDAO := sut.daoFactory.GetUserTenantMappingDAO(tenantName)
		mapping1 := &auth_entities.UserTenantMapping{
			UserId: "merge-sub-user-1",
			Tenant: tenantName,
		}
		_, err = tenantMappingDAO.Create(ctx, mapping1)
		require.NoError(t, err)

		mapping2 := &auth_entities.UserTenantMapping{
			UserId: "merge-sub-user-2",
			Tenant: tenantName,
		}
		_, err = tenantMappingDAO.Create(ctx, mapping2)
		require.NoError(t, err)

		// User 1 has a worse subscription
		mockOrbClient.On("GetUserSubscription", mock.Anything, "orb-sub-merge-1", mock.Anything).Return(&orb.OrbSubscriptionInfo{
			OrbSubscriptionID: "orb-sub-merge-1",
			ExternalPlanID:    "orb_community_plan",
			OrbStatus:         "active",
		}, nil)

		// User 2 has a better subscription
		mockOrbClient.On("GetUserSubscription", mock.Anything, "orb-sub-merge-2", mock.Anything).Return(&orb.OrbSubscriptionInfo{
			OrbSubscriptionID: "orb-sub-merge-2",
			ExternalPlanID:    "orb_developer_plan",
			OrbStatus:         "active",
		}, nil)

		// Update the metadata of the "winning" subscription to point to the new primary user
		mockOrbClient.On("UpdateCustomerMetadata", mock.Anything, "orb-customer-merge-2", map[string]string{
			"augment_user_id": "merge-sub-user-1",
		}).Return(nil)

		// Update the email of the "winning" customer to point to the new primary user
		primaryEmail := "<EMAIL>"
		mockOrbClient.On("UpdateCustomerInformation", mock.Anything, "orb-customer-merge-2", orb.OrbCustomerUpdateInfo{
			Email: &primaryEmail,
			Name:  &primaryEmail,
		}, mock.Anything).Return(nil)

		// Cancel the worse subscription
		mockOrbClient.On("CancelOrbSubscriptionUnscheduleFirst", mock.Anything, "orb-sub-merge-1", orb.PlanChangeImmediate, mock.Anything).Return(nil)

		// Run deduplication with actual changes
		resp, err := authServicer.MergeDuplicateAccounts(ctx, &authpb.MergeDuplicateAccountsRequest{
			UserIds: []string{"merge-sub-user-1", "merge-sub-user-2"},
			DryRun:  false,
		})
		require.NoError(t, err)
		require.NotNil(t, resp)

		merge := resp.UserMerge
		assert.Equal(t, "<EMAIL>", merge.Email)
		assert.Equal(t, "merge-sub-user-1", merge.PrimaryUserId)
		assert.Contains(t, merge.MergedUserIds, "merge-sub-user-2")
		assert.Equal(t, "merge-sub-user-2", merge.SubscriptionSourceUserId)

		// Verify primary user now has the subscription information
		primaryUser, err := userDAO.Get(ctx, "merge-sub-user-1")
		require.NoError(t, err)
		assert.Equal(t, "orb-customer-merge-2", primaryUser.OrbCustomerId)
		assert.Equal(t, "orb-sub-merge-2", primaryUser.OrbSubscriptionId)

		// Verify merged user was deleted (has no tenants)
		mergedUser, err := userDAO.Get(ctx, "merge-sub-user-2")
		require.NoError(t, err)
		assert.Equal(t, 0, len(mergedUser.Tenants))
		mockOrbClient.AssertExpectations(t)
	})
	t.Run("Actual merge with active community and inactive developer", func(t *testing.T) {
		bigtableFixture.ClearTable(t)

		// Create users with different subscription information
		user1 := &auth_entities.User{
			Id:                "merge-sub-user-3",
			Email:             "<EMAIL>",
			Tenants:           []string{tenantID},
			OrbCustomerId:     "orb-customer-merge-3",
			OrbSubscriptionId: "orb-sub-merge-3",
		}
		user2 := &auth_entities.User{
			Id:                "merge-sub-user-4",
			Email:             "<EMAIL>",
			Tenants:           []string{tenantID},
			OrbCustomerId:     "orb-customer-merge-4",
			OrbSubscriptionId: "orb-sub-merge-4",
		}

		_, err := userDAO.Create(ctx, user1)
		require.NoError(t, err)
		_, err = userDAO.Create(ctx, user2)
		require.NoError(t, err)

		// Add users to tenants
		tenantMappingDAO := sut.daoFactory.GetUserTenantMappingDAO(tenantName)
		mapping1 := &auth_entities.UserTenantMapping{
			UserId: "merge-sub-user-3",
			Tenant: tenantName,
		}
		_, err = tenantMappingDAO.Create(ctx, mapping1)
		require.NoError(t, err)

		mapping2 := &auth_entities.UserTenantMapping{
			UserId: "merge-sub-user-4",
			Tenant: tenantName,
		}
		_, err = tenantMappingDAO.Create(ctx, mapping2)
		require.NoError(t, err)

		// User 1 has a worse subscription - inactive developer
		mockOrbClient.On("GetUserSubscription", mock.Anything, "orb-sub-merge-3", mock.Anything).Return(&orb.OrbSubscriptionInfo{
			OrbSubscriptionID: "orb-sub-merge-3",
			ExternalPlanID:    "orb_developer_plan",
			OrbStatus:         "ended",
		}, nil)

		// User 2 has a better subscription - active community
		mockOrbClient.On("GetUserSubscription", mock.Anything, "orb-sub-merge-4", mock.Anything).Return(&orb.OrbSubscriptionInfo{
			OrbSubscriptionID: "orb-sub-merge-4",
			ExternalPlanID:    "orb_community_plan",
			OrbStatus:         "active",
		}, nil)

		// Update the metadata of the "winning" subscription to point to the new primary user
		mockOrbClient.On("UpdateCustomerMetadata", mock.Anything, "orb-customer-merge-4", map[string]string{
			"augment_user_id": "merge-sub-user-3",
		}).Return(nil)

		// Update the email of the "winning" customer to point to the new primary user
		primaryEmail := "<EMAIL>"
		mockOrbClient.On("UpdateCustomerInformation", mock.Anything, "orb-customer-merge-4", orb.OrbCustomerUpdateInfo{
			Email: &primaryEmail,
			Name:  &primaryEmail,
		}, mock.Anything).Return(nil)

		// the worse subscription is already ended, so no need to cancel it

		// Run merge with actual changes
		resp, err := authServicer.MergeDuplicateAccounts(ctx, &authpb.MergeDuplicateAccountsRequest{
			UserIds: []string{"merge-sub-user-3", "merge-sub-user-4"},
			DryRun:  false,
		})
		require.NoError(t, err)
		require.NotNil(t, resp)

		merge := resp.UserMerge
		assert.Equal(t, "<EMAIL>", merge.Email)
		assert.Equal(t, "merge-sub-user-3", merge.PrimaryUserId)
		assert.Contains(t, merge.MergedUserIds, "merge-sub-user-4")
		assert.Equal(t, "merge-sub-user-4", merge.SubscriptionSourceUserId)

		// Verify primary user now has the subscription information
		primaryUser, err := userDAO.Get(ctx, "merge-sub-user-3")
		require.NoError(t, err)
		assert.Equal(t, "orb-customer-merge-4", primaryUser.OrbCustomerId)
		assert.Equal(t, "orb-sub-merge-4", primaryUser.OrbSubscriptionId)

		// Verify merged user was deleted (has no tenants)
		mergedUser, err := userDAO.Get(ctx, "merge-sub-user-4")
		require.NoError(t, err)
		assert.Equal(t, 0, len(mergedUser.Tenants))
		mockOrbClient.AssertExpectations(t)
	})
	t.Run("Actual merge with active developer and trial - first user already has winning subscription", func(t *testing.T) {
		bigtableFixture.ClearTable(t)

		// Create users with different subscription information
		user1 := &auth_entities.User{
			Id:                "merge-sub-user-5",
			Email:             "<EMAIL>",
			Tenants:           []string{tenantID},
			OrbCustomerId:     "orb-customer-merge-5",
			OrbSubscriptionId: "orb-sub-merge-5",
		}
		user2 := &auth_entities.User{
			Id:                "merge-sub-user-6",
			Email:             "<EMAIL>",
			Tenants:           []string{tenantID},
			OrbCustomerId:     "orb-customer-merge-6",
			OrbSubscriptionId: "orb-sub-merge-6",
		}

		_, err := userDAO.Create(ctx, user1)
		require.NoError(t, err)
		_, err = userDAO.Create(ctx, user2)
		require.NoError(t, err)

		// Add users to tenants
		tenantMappingDAO := sut.daoFactory.GetUserTenantMappingDAO(tenantName)
		mapping1 := &auth_entities.UserTenantMapping{
			UserId: "merge-sub-user-5",
			Tenant: tenantName,
		}
		_, err = tenantMappingDAO.Create(ctx, mapping1)
		require.NoError(t, err)

		mapping2 := &auth_entities.UserTenantMapping{
			UserId: "merge-sub-user-6",
			Tenant: tenantName,
		}
		_, err = tenantMappingDAO.Create(ctx, mapping2)
		require.NoError(t, err)

		// User 1 has a winning subscription - active developer
		mockOrbClient.On("GetUserSubscription", mock.Anything, "orb-sub-merge-5", mock.Anything).Return(&orb.OrbSubscriptionInfo{
			OrbSubscriptionID: "orb-sub-merge-5",
			ExternalPlanID:    "orb_developer_plan",
			OrbStatus:         "active",
		}, nil)

		// User 2 has a worse subscription - trial
		mockOrbClient.On("GetUserSubscription", mock.Anything, "orb-sub-merge-6", mock.Anything).Return(&orb.OrbSubscriptionInfo{
			OrbSubscriptionID: "orb-sub-merge-6",
			ExternalPlanID:    "orb_trial_plan",
			OrbStatus:         "active",
		}, nil)

		// Update the metadata of the "winning" subscription to point to the new primary user
		mockOrbClient.On("UpdateCustomerMetadata", mock.Anything, "orb-customer-merge-5", map[string]string{
			"augment_user_id": "merge-sub-user-5",
		}).Return(nil)

		primaryEmail := "<EMAIL>"
		mockOrbClient.On("UpdateCustomerInformation", mock.Anything, "orb-customer-merge-5", orb.OrbCustomerUpdateInfo{
			Email: &primaryEmail,
			Name:  &primaryEmail,
		}, mock.Anything).Return(nil)

		// Cancel the worse subscription
		mockOrbClient.On("CancelOrbSubscriptionUnscheduleFirst", mock.Anything, "orb-sub-merge-6", orb.PlanChangeImmediate, mock.Anything).Return(nil)

		// Run merge with actual changes
		resp, err := authServicer.MergeDuplicateAccounts(ctx, &authpb.MergeDuplicateAccountsRequest{
			UserIds: []string{"merge-sub-user-5", "merge-sub-user-6"},
			DryRun:  false,
		})
		require.NoError(t, err)
		require.NotNil(t, resp)
		merge := resp.UserMerge
		assert.Equal(t, "<EMAIL>", merge.Email)
		assert.Equal(t, "merge-sub-user-5", merge.PrimaryUserId)
		assert.Contains(t, merge.MergedUserIds, "merge-sub-user-6")
		assert.Equal(t, "merge-sub-user-5", merge.SubscriptionSourceUserId)

		// Verify primary user now has the subscription information
		primaryUser, err := userDAO.Get(ctx, "merge-sub-user-5")
		require.NoError(t, err)
		assert.Equal(t, "orb-customer-merge-5", primaryUser.OrbCustomerId)
		assert.Equal(t, "orb-sub-merge-5", primaryUser.OrbSubscriptionId)

		// Verify merged user was deleted (has no tenants)
		mergedUser, err := userDAO.Get(ctx, "merge-sub-user-6")
		require.NoError(t, err)
		assert.Equal(t, 0, len(mergedUser.Tenants))

		mockOrbClient.AssertExpectations(t)
	})
}

func caseTestDeduplicateUsersByIdpUserIdComplexScenarios(t *testing.T, bigtableFixture *BigtableFixture) {
	sut := createAuthServicerSUT(t, bigtableFixture)
	authServicer := sut.authServicer
	// Inject a mock orb client
	mockOrbClient := orb.NewMockOrbClient()
	authServicer.orbClient = mockOrbClient
	userDAO := sut.daoFactory.GetUserDAO()

	ctx := createCentralAuthAdminRequestContext()

	t.Run("Transitive connection - A-B-C chain", func(t *testing.T) {
		bigtableFixture.ClearTable(t)

		// User A has IDP IDs [1, 2]
		userA := &auth_entities.User{
			Id:         "user-a",
			Email:      "<EMAIL>",
			IdpUserIds: []string{"idp-1", "idp-2"},
			Tenants:    []string{tenantID},
		}
		// User B has IDP IDs [2, 3] - shares idp-2 with A
		userB := &auth_entities.User{
			Id:         "user-b",
			Email:      "<EMAIL>",
			IdpUserIds: []string{"idp-2", "idp-3"},
			Tenants:    []string{tenantID},
		}
		// User C has IDP IDs [3, 4] - shares idp-3 with B
		userC := &auth_entities.User{
			Id:         "user-c",
			Email:      "<EMAIL>",
			IdpUserIds: []string{"idp-3", "idp-4"},
			Tenants:    []string{tenantID},
		}

		_, err := userDAO.Create(ctx, userA)
		require.NoError(t, err)
		_, err = userDAO.Create(ctx, userB)
		require.NoError(t, err)
		_, err = userDAO.Create(ctx, userC)
		require.NoError(t, err)

		// Run deduplication in dry-run mode
		resp, err := authServicer.MergeDuplicateAccounts(ctx, &authpb.MergeDuplicateAccountsRequest{
			UserIds: []string{"user-a", "user-b", "user-c"},
			DryRun:  true,
		})
		require.NoError(t, err)
		require.NotNil(t, resp)

		merge := resp.UserMerge
		assert.Equal(t, "user-a", merge.PrimaryUserId, "Primary user should be the one with lowest ID")
		assert.Contains(t, merge.MergedUserIds, "user-b")
		assert.Contains(t, merge.MergedUserIds, "user-c")
		assert.Len(t, merge.MergedUserIds, 2)

		// verify that the merge includes all IDP user IDs
		assert.Len(t, merge.IdpUserIds, 4)
		for _, idpUserId := range []string{"idp-1", "idp-2", "idp-3", "idp-4"} {
			assert.Contains(t, merge.IdpUserIds, idpUserId)
		}
	})
}

func caseTestDeduplicateUsersByIdpUserIdActualMerge(t *testing.T, bigtableFixture *BigtableFixture) {
	sut := createAuthServicerSUT(t, bigtableFixture)
	authServicer := sut.authServicer
	// Inject a mock orb client
	mockOrbClient := orb.NewMockOrbClient()
	authServicer.orbClient = mockOrbClient
	userDAO := sut.daoFactory.GetUserDAO()

	ctx := createCentralAuthAdminRequestContext()

	t.Run("Simple merge - Two users sharing one IDP ID", func(t *testing.T) {
		bigtableFixture.ClearTable(t)

		// Two users sharing the same IDP user ID
		user1 := &auth_entities.User{
			Id:         "simple-user-1",
			Email:      "<EMAIL>",
			IdpUserIds: []string{"shared-idp-1"},
			Tenants:    []string{tenantID},
		}
		user2 := &auth_entities.User{
			Id:         "simple-user-2",
			Email:      "<EMAIL>",
			IdpUserIds: []string{"shared-idp-1"},
			Tenants:    []string{tenantID},
		}

		_, err := userDAO.Create(ctx, user1)
		require.NoError(t, err)
		_, err = userDAO.Create(ctx, user2)
		require.NoError(t, err)

		// Add users to tenants
		tenantMappingDAO := sut.daoFactory.GetUserTenantMappingDAO(tenantName)
		mapping1 := &auth_entities.UserTenantMapping{
			UserId: "simple-user-1",
			Tenant: tenantName,
		}
		_, err = tenantMappingDAO.Create(ctx, mapping1)
		require.NoError(t, err)

		mapping2 := &auth_entities.UserTenantMapping{
			UserId: "simple-user-2",
			Tenant: tenantName,
		}
		_, err = tenantMappingDAO.Create(ctx, mapping2)
		require.NoError(t, err)

		// Run deduplication with actual changes
		resp, err := authServicer.MergeDuplicateAccounts(ctx, &authpb.MergeDuplicateAccountsRequest{
			UserIds: []string{"simple-user-1", "simple-user-2"},
			DryRun:  false,
		})
		require.NoError(t, err)
		require.NotNil(t, resp)

		merge := resp.UserMerge
		assert.Equal(t, "simple-user-1", merge.PrimaryUserId)
		assert.Contains(t, merge.MergedUserIds, "simple-user-2")

		// Verify primary user still exists
		primaryUser, err := userDAO.Get(ctx, "simple-user-1")
		require.NoError(t, err)
		assert.Equal(t, "<EMAIL>", primaryUser.Email)
		// Should have collected IDP user IDs from both users
		assert.Contains(t, primaryUser.IdpUserIds, "shared-idp-1")

		// Verify merged user was deleted (has no tenants)
		mergedUser, err := userDAO.Get(ctx, "simple-user-2")
		require.NoError(t, err)
		assert.Equal(t, 0, len(mergedUser.Tenants))
	})

	t.Run("Simple merge with multiple IDP IDs per user", func(t *testing.T) {
		bigtableFixture.ClearTable(t)

		// User 1 has multiple IDP IDs
		user1 := &auth_entities.User{
			Id:         "multi-idp-user-1",
			Email:      "<EMAIL>",
			IdpUserIds: []string{"multi-1", "multi-2", "multi-3"},
			Tenants:    []string{tenantID},
		}
		// User 2 shares one IDP ID with user 1
		user2 := &auth_entities.User{
			Id:         "multi-idp-user-2",
			Email:      "<EMAIL>",
			IdpUserIds: []string{"multi-3", "multi-4", "multi-5"},
			Tenants:    []string{tenantID},
		}

		_, err := userDAO.Create(ctx, user1)
		require.NoError(t, err)
		_, err = userDAO.Create(ctx, user2)
		require.NoError(t, err)

		// Add users to tenants
		tenantMappingDAO := sut.daoFactory.GetUserTenantMappingDAO(tenantName)
		for _, userId := range []string{"multi-idp-user-1", "multi-idp-user-2"} {
			mapping := &auth_entities.UserTenantMapping{
				UserId: userId,
				Tenant: tenantName,
			}
			_, err = tenantMappingDAO.Create(ctx, mapping)
			require.NoError(t, err)
		}

		// Run deduplication with actual changes
		resp, err := authServicer.MergeDuplicateAccounts(ctx, &authpb.MergeDuplicateAccountsRequest{
			UserIds: []string{"multi-idp-user-1", "multi-idp-user-2"},
			DryRun:  false,
		})
		require.NoError(t, err)
		require.NotNil(t, resp)

		merge := resp.UserMerge
		assert.Equal(t, "multi-idp-user-1", merge.PrimaryUserId)
		assert.Contains(t, merge.MergedUserIds, "multi-idp-user-2")

		// Verify primary user has all unique IDP user IDs
		primaryUser, err := userDAO.Get(ctx, "multi-idp-user-1")
		require.NoError(t, err)
		expectedIdpIds := []string{"multi-1", "multi-2", "multi-3", "multi-4", "multi-5"}
		assert.Len(t, primaryUser.IdpUserIds, 5)
		for _, expectedId := range expectedIdpIds {
			assert.Contains(t, primaryUser.IdpUserIds, expectedId)
		}

		// Verify merged user was deleted
		mergedUser, err := userDAO.Get(ctx, "multi-idp-user-2")
		require.NoError(t, err)
		assert.Equal(t, 0, len(mergedUser.Tenants))
	})
}

func addRequestInfoToContext(ctx context.Context) context.Context {
	requestId := uuid.New().String()
	return metadata.NewIncomingContext(ctx, metadata.Pairs("x-request-id", requestId))
}

func TestDeleteTenantSubscriptionMapping(t *testing.T) {
	bigtableFixture := NewBigtableFixture(t)
	defer bigtableFixture.Cleanup()

	testCases := []struct {
		name              string
		tenantId          string
		setupMapping      bool
		contextConfig     testContextConfig
		expectedError     bool
		expectedErrorCode codes.Code
	}{
		{
			name:         "successful deletion with existing mapping",
			tenantId:     "test-tenant-id",
			setupMapping: true,
			contextConfig: testContextConfig{
				tenantId: "*",
				scopes:   []string{tokenscopesproto.Scope_AUTH_RW.String()},
			},
			expectedError: false,
		},
		{
			name:         "successful deletion with non-existing mapping",
			tenantId:     "non-existing-tenant-id",
			setupMapping: false,
			contextConfig: testContextConfig{
				tenantId: "*",
				scopes:   []string{tokenscopesproto.Scope_AUTH_RW.String()},
			},
			expectedError: false,
		},
		{
			name:         "empty tenant_id",
			tenantId:     "",
			setupMapping: false,
			contextConfig: testContextConfig{
				tenantId: "*",
				scopes:   []string{tokenscopesproto.Scope_AUTH_RW.String()},
			},
			expectedError:     true,
			expectedErrorCode: codes.Internal,
		},
		{
			name:         "insufficient permissions - AUTH_R scope",
			tenantId:     "test-tenant-id",
			setupMapping: true,
			contextConfig: testContextConfig{
				tenantId: "*",
				scopes:   []string{tokenscopesproto.Scope_AUTH_R.String()},
			},
			expectedError:     true,
			expectedErrorCode: codes.PermissionDenied,
		},
		{
			name:         "unauthenticated request",
			tenantId:     "test-tenant-id",
			setupMapping: true,
			contextConfig: testContextConfig{
				expectPermissionError: true,
			},
			expectedError:     true,
			expectedErrorCode: codes.Unauthenticated,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			runDeleteTenantSubscriptionMappingTestCase(t, bigtableFixture, tc)
		})
	}
}

func runDeleteTenantSubscriptionMappingTestCase(t *testing.T, bigtableFixture *BigtableFixture, tc struct {
	name              string
	tenantId          string
	setupMapping      bool
	contextConfig     testContextConfig
	expectedError     bool
	expectedErrorCode codes.Code
},
) {
	bigtableFixture.ClearTable(t)

	// Create auth servicer
	daoFactory := NewDAOFactory(bigtableFixture.Table)
	authServicer := &AuthGrpcServer{
		daoFactory:  daoFactory,
		auditLogger: audit.NewDefaultAuditLogger(),
	}

	// Setup tenant subscription mapping if needed
	if tc.setupMapping {
		tenantSubscriptionMappingDAO := daoFactory.GetTenantSubscriptionMappingDAO()
		mapping := &auth_entities.TenantSubscriptionMapping{
			TenantId:         tc.tenantId,
			StripeCustomerId: "test-stripe-customer-id",
			CreatedAt:        timestamppb.Now(),
		}
		_, err := tenantSubscriptionMappingDAO.Create(context.Background(), mapping)
		require.NoError(t, err)
	}

	// Create context
	var ctx context.Context
	if !tc.contextConfig.expectPermissionError {
		claims := &auth.AugmentClaims{
			TenantID:         tc.contextConfig.tenantId,
			TenantName:       "test-tenant",
			UserID:           "test-user-id",
			OpaqueUserID:     "test-user-id",
			OpaqueUserIDType: auth_entities.UserId_INTERNAL_IAP.String(),
			Scope:            tc.contextConfig.scopes,
		}
		ctx = claims.NewContext(context.Background())
		ctx = addRequestInfoToContext(ctx)
	} else {
		ctx = context.Background()
		ctx = addRequestInfoToContext(ctx)
	}

	// Test the endpoint
	req := &authpb.DeleteTenantSubscriptionMappingRequest{
		TenantId: tc.tenantId,
	}
	resp, err := authServicer.DeleteTenantSubscriptionMapping(ctx, req)

	// Verify error expectations
	if tc.expectedError {
		require.Error(t, err)
		require.Equal(t, tc.expectedErrorCode, status.Code(err))
		return
	}

	// Verify successful response
	require.NoError(t, err)
	require.NotNil(t, resp)
	// Response is now empty, so we just verify it's not nil

	// If we had a mapping and the operation succeeded, verify it was deleted
	if tc.setupMapping && tc.tenantId != "" {
		tenantSubscriptionMappingDAO := daoFactory.GetTenantSubscriptionMappingDAO()
		_, err := tenantSubscriptionMappingDAO.Get(context.Background(), tc.tenantId)
		// The delete operation should succeed, and subsequent Get should fail
		if err == nil {
			t.Logf("Warning: Expected mapping to be deleted but it still exists")
		}
	}
}

func TestUpdateSubscriptionOwnerToTeam(t *testing.T) {
	bigtableFixture := NewBigtableFixture(t)
	defer bigtableFixture.Cleanup()

	testCases := []struct {
		name               string
		subscriptionIds    []string
		setupSubscriptions []struct {
			id    string
			owner interface{} // *auth_entities.Subscription_UserId or *auth_entities.Subscription_TenantId
		}
		setupUser         bool
		userTenants       []string
		setupTenant       bool
		tenantConfig      map[string]string
		contextConfig     testContextConfig
		expectedError     bool
		expectedErrorCode codes.Code
		expectedResults   []struct {
			subscriptionId string
			success        bool
			errorMessage   string
		}
	}{
		{
			name:            "successful update from user to team",
			subscriptionIds: []string{"test-subscription-id"},
			setupSubscriptions: []struct {
				id    string
				owner interface{}
			}{
				{
					id:    "test-subscription-id",
					owner: &auth_entities.Subscription_UserId{UserId: "test-user-id"},
				},
			},
			setupUser:   true,
			userTenants: []string{"test-tenant-id"},
			setupTenant: true,
			tenantConfig: map[string]string{
				"is_self_serve_team": "true",
			},
			contextConfig: testContextConfig{
				tenantId: "*",
				scopes:   []string{tokenscopesproto.Scope_AUTH_RW.String()},
			},
			expectedError: false,
			expectedResults: []struct {
				subscriptionId string
				success        bool
				errorMessage   string
			}{
				{
					subscriptionId: "test-subscription-id",
					success:        true,
					errorMessage:   "",
				},
			},
		},
		{
			name:            "multiple subscriptions - mixed results",
			subscriptionIds: []string{"test-subscription-1", "non-existing-subscription", "test-subscription-2"},
			setupSubscriptions: []struct {
				id    string
				owner interface{}
			}{
				{
					id:    "test-subscription-1",
					owner: &auth_entities.Subscription_UserId{UserId: "test-user-id"},
				},
				{
					id:    "test-subscription-2",
					owner: &auth_entities.Subscription_TenantId{TenantId: "test-tenant-id"},
				},
			},
			setupUser:   true,
			userTenants: []string{"test-tenant-id"},
			setupTenant: true,
			tenantConfig: map[string]string{
				"is_self_serve_team": "true",
			},
			contextConfig: testContextConfig{
				tenantId: "*",
				scopes:   []string{tokenscopesproto.Scope_AUTH_RW.String()},
			},
			expectedError: false,
			expectedResults: []struct {
				subscriptionId string
				success        bool
				errorMessage   string
			}{
				{
					subscriptionId: "test-subscription-1",
					success:        true,
					errorMessage:   "",
				},
				{
					subscriptionId: "non-existing-subscription",
					success:        false,
					errorMessage:   "Subscription not found",
				},
				{
					subscriptionId: "test-subscription-2",
					success:        false,
					errorMessage:   "Subscription owner is not a user",
				},
			},
		},
		{
			name:            "insufficient permissions - AUTH_R scope",
			subscriptionIds: []string{"test-subscription-id"},
			setupSubscriptions: []struct {
				id    string
				owner interface{}
			}{
				{
					id:    "test-subscription-id",
					owner: &auth_entities.Subscription_UserId{UserId: "test-user-id"},
				},
			},
			setupUser:   true,
			userTenants: []string{"test-tenant-id"},
			setupTenant: true,
			tenantConfig: map[string]string{
				"is_self_serve_team": "true",
			},
			contextConfig: testContextConfig{
				tenantId: "*",
				scopes:   []string{tokenscopesproto.Scope_AUTH_R.String()},
			},
			expectedError:     true,
			expectedErrorCode: codes.PermissionDenied,
		},
		{
			name:            "unauthenticated request",
			subscriptionIds: []string{"test-subscription-id"},
			setupSubscriptions: []struct {
				id    string
				owner interface{}
			}{
				{
					id:    "test-subscription-id",
					owner: &auth_entities.Subscription_UserId{UserId: "test-user-id"},
				},
			},
			contextConfig: testContextConfig{
				expectPermissionError: true,
			},
			expectedError:     true,
			expectedErrorCode: codes.Unauthenticated,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			runUpdateSubscriptionOwnerToTeamTestCase(t, bigtableFixture, tc)
		})
	}
}

func runUpdateSubscriptionOwnerToTeamTestCase(t *testing.T, bigtableFixture *BigtableFixture, tc struct {
	name               string
	subscriptionIds    []string
	setupSubscriptions []struct {
		id    string
		owner interface{}
	}
	setupUser         bool
	userTenants       []string
	setupTenant       bool
	tenantConfig      map[string]string
	contextConfig     testContextConfig
	expectedError     bool
	expectedErrorCode codes.Code
	expectedResults   []struct {
		subscriptionId string
		success        bool
		errorMessage   string
	}
},
) {
	bigtableFixture.ClearTable(t)

	// Create mock tenant watcher client
	mockTenantWatcherClient := &tw_client.MockTenantWatcherClient{
		Tenants: []*tw_pb.Tenant{},
	}

	// Setup tenant if needed
	if tc.setupTenant {
		tenant := &tw_pb.Tenant{
			Id:             "test-tenant-id",
			Name:           "test-tenant",
			ShardNamespace: "test-namespace",
			Cloud:          "CLOUD_DEV",
			Tier:           tw_pb.TenantTier_PROFESSIONAL,
		}
		if tc.tenantConfig != nil {
			tenant.Config = &tw_pb.Config{
				Configs: tc.tenantConfig,
			}
		}
		mockTenantWatcherClient.Tenants = append(mockTenantWatcherClient.Tenants, tenant)
	}
	ocsfAuditLogger, _ := auditocsf.NewMockOCSFAuditLogger()

	// Create auth servicer
	daoFactory := NewDAOFactory(bigtableFixture.Table)
	authServicer := NewAuthGrpcServer(
		featureflag.NewLocalFeatureFlagHandler(),
		daoFactory,
		NewTenantMap(
			daoFactory,
			mockTenantWatcherClient,
			"us-central.api.augmentcode.com",
			featureflag.NewLocalFeatureFlagHandler(),
			NewMockAsyncOpsPublisher(),
			audit.NewDefaultAuditLogger(),
		),
		audit.NewDefaultAuditLogger(),
		ripublisher.NewRequestInsightPublisherMock(),
		NewMockAsyncOpsPublisher(),
		nil, // asyncOpsWorker
		&StripeConfig{Enabled: false},
		&orb_config.OrbConfig{
			Enabled: true,
		},
		stripelib.NewMockStripeClient(),
		nil, // billingEventProcessor
		ocsfAuditLogger,
	)

	// Setup subscriptions if needed
	for _, sub := range tc.setupSubscriptions {
		subscriptionDAO := daoFactory.GetSubscriptionDAO()
		subscription := &auth_entities.Subscription{
			SubscriptionId: sub.id,
			CreatedAt:      timestamppb.Now(),
			UpdatedAt:      timestamppb.Now(),
		}
		// Set the owner based on the type
		switch owner := sub.owner.(type) {
		case *auth_entities.Subscription_UserId:
			subscription.Owner = owner
		case *auth_entities.Subscription_TenantId:
			subscription.Owner = owner
		}
		_, err := subscriptionDAO.Create(context.Background(), subscription)
		require.NoError(t, err)
	}

	// Setup user if needed
	if tc.setupUser {
		userDAO := daoFactory.GetUserDAO()
		user := &auth_entities.User{
			Id:        "test-user-id",
			Email:     "<EMAIL>",
			Tenants:   tc.userTenants,
			CreatedAt: timestamppb.Now(),
		}
		_, err := userDAO.Create(context.Background(), user)
		require.NoError(t, err)
	}

	// Create context
	var ctx context.Context
	if !tc.contextConfig.expectPermissionError {
		claims := &auth.AugmentClaims{
			TenantID:         tc.contextConfig.tenantId,
			TenantName:       "test-tenant",
			UserID:           "test-user-id",
			OpaqueUserID:     "test-user-id",
			OpaqueUserIDType: auth_entities.UserId_INTERNAL_IAP.String(),
			Scope:            tc.contextConfig.scopes,
		}
		ctx = claims.NewContext(context.Background())
		ctx = addRequestInfoToContext(ctx)
	} else {
		ctx = context.Background()
		ctx = addRequestInfoToContext(ctx)
	}

	// Test the endpoint
	req := &authpb.UpdateSubscriptionOwnerToTeamRequest{
		SubscriptionIds: tc.subscriptionIds,
	}
	resp, err := authServicer.UpdateSubscriptionOwnerToTeam(ctx, req)

	// Verify error expectations
	if tc.expectedError {
		require.Error(t, err)
		require.Equal(t, tc.expectedErrorCode, status.Code(err))
		return
	}

	// Verify successful response
	require.NoError(t, err)
	require.NotNil(t, resp)

	// Verify results if expected
	if tc.expectedResults != nil {
		require.Equal(t, len(tc.expectedResults), len(resp.Results))
		for i, expectedResult := range tc.expectedResults {
			actualResult := resp.Results[i]
			require.Equal(t, expectedResult.subscriptionId, actualResult.SubscriptionId)
			require.Equal(t, expectedResult.success, actualResult.Success)
			if expectedResult.errorMessage != "" {
				require.Contains(t, actualResult.ErrorMessage, expectedResult.errorMessage)
			}
		}

		// For successful updates, verify the subscription owner was actually updated
		for _, expectedResult := range tc.expectedResults {
			if expectedResult.success {
				subscriptionDAO := daoFactory.GetSubscriptionDAO()
				updatedSubscription, err := subscriptionDAO.Get(context.Background(), expectedResult.subscriptionId)
				require.NoError(t, err)
				require.NotNil(t, updatedSubscription)

				// Verify the owner is now a tenant
				tenantOwner, ok := updatedSubscription.Owner.(*auth_entities.Subscription_TenantId)
				require.True(t, ok, "Expected subscription owner to be a tenant")
				require.Equal(t, "test-tenant-id", tenantOwner.TenantId)
			}
		}
	}
}

func caseDeduplicateUserTenantList(t *testing.T, bigtableFixture *BigtableFixture) {
	sut := createAuthServicerSUT(t, bigtableFixture)
	authServicer := sut.authServicer
	daoFactory := sut.daoFactory

	ctx := createCentralAuthorizedRequestContext()
	userDAO := daoFactory.GetUserDAO()

	// Create a user with duplicate tenants
	user := &auth_entities.User{
		Id:      "duplicate-tenant-user",
		Email:   "<EMAIL>",
		Tenants: []string{tenantID, tenantID, tenantID}, // Same tenant repeated
	}
	_, err := userDAO.Create(ctx, user)
	require.NoError(t, err)

	// Test dry run
	resp, err := authServicer.DeduplicateUserTenantList(ctx, &authpb.DeduplicateUserTenantListRequest{
		UserId:      "duplicate-tenant-user",
		MakeChanges: false,
	})
	require.NoError(t, err)
	require.Equal(t, []string{tenantID, tenantID, tenantID}, resp.PreviousTenants)
	require.Equal(t, tenantID, resp.NewTenant)

	// Test actual changes
	resp, err = authServicer.DeduplicateUserTenantList(ctx, &authpb.DeduplicateUserTenantListRequest{
		UserId:      "duplicate-tenant-user",
		MakeChanges: true,
	})
	require.NoError(t, err)
	require.Equal(t, []string{tenantID, tenantID, tenantID}, resp.PreviousTenants)
	require.Equal(t, tenantID, resp.NewTenant)

	// Verify user was updated
	updatedUser, err := userDAO.Get(ctx, "duplicate-tenant-user")
	require.NoError(t, err)
	require.Equal(t, []string{tenantID}, updatedUser.Tenants)

	// Test user with multiple different tenants (should fail)
	user2 := &auth_entities.User{
		Id:      "multi-tenant-user",
		Email:   "<EMAIL>",
		Tenants: []string{tenantID, professionalTenantID},
	}
	_, err = userDAO.Create(ctx, user2)
	require.NoError(t, err)

	_, err = authServicer.DeduplicateUserTenantList(ctx, &authpb.DeduplicateUserTenantListRequest{
		UserId:      "multi-tenant-user",
		MakeChanges: false,
	})
	require.Error(t, err)
	require.Equal(t, codes.FailedPrecondition, status.Code(err))
}

func TestAuthServicer(t *testing.T) {
	bigtableFixture := NewBigtableFixture(t)
	defer bigtableFixture.Cleanup()

	testCases := []struct {
		name         string
		testCaseFunc func(t *testing.T, bigtableFixture *BigtableFixture)
	}{
		{
			name:         "Basic flow",
			testCaseFunc: caseBasicFlow,
		},
		{
			name:         "GetTokenInfo",
			testCaseFunc: caseTokenLookup,
		},
		{
			name:         "GetUsers",
			testCaseFunc: caseGetUsers,
		},
		{
			name:         "ForgetUser",
			testCaseFunc: caseForgetUser,
		},
		{
			name:         "RemoveExtraSelfServeTenantsFromUsers",
			testCaseFunc: caseRemoveExtraSelfServeTenantsFromUsers,
		},
		{
			name:         "RemoveDeletedTenantsFromUsers",
			testCaseFunc: caseRemoveDeletedTenantsFromUsers,
		},
		{
			name:         "DeduplicateUsersByEmail",
			testCaseFunc: caseTestDeduplicateUsersByEmail,
		},
		{
			name:         "DeduplicateUsersByEmail - Actual merge",
			testCaseFunc: caseTestDeduplicateUsersByEmailActualMerge,
		},
		{
			name:         "DeduplicateUsersByEmail - Complex scenarios",
			testCaseFunc: caseTestDeduplicateUsersByEmailComplexScenarios,
		},
		{
			name:         "DeduplicateUserTenantList",
			testCaseFunc: caseDeduplicateUserTenantList,
		},
		{
			name:         "DeduplicateUsersByIdpUserId - Complex scenarios",
			testCaseFunc: caseTestDeduplicateUsersByIdpUserIdComplexScenarios,
		},
		{
			name:         "DeduplicateUsersByIdpUserId",
			testCaseFunc: caseTestDeduplicateUsersByIdpUserIdActualMerge,
		},
		{
			name:         "MergeDuplicateAccounts",
			testCaseFunc: caseTestMergeDuplicateAccounts,
		},
		{
			name:         "AddUserTags",
			testCaseFunc: caseAddUserTags,
		},
		{
			name:         "RemoveUserTags",
			testCaseFunc: caseRemoveUserTags,
		},
	}

	for _, tc := range testCases {
		bigtableFixture.ClearTable(t)

		t.Run(tc.name, func(t *testing.T) {
			tc.testCaseFunc(t, bigtableFixture)
		})
	}
}

func TestSuspensionFilteringByPlanType(t *testing.T) {
	bigtableFixture := NewBigtableFixture(t)
	defer bigtableFixture.Cleanup()

	sut := createAuthServicerSUT(t, bigtableFixture)
	authServicer := sut.authServicer
	daoFactory := sut.daoFactory
	ctx := createAuthorizedRequestContext()

	t.Run("Free Trial Suspensions Filtered By Plan Type", func(t *testing.T) {
		// Create a user with free trial abuse suspension
		userID := uuid.New().String()
		testTenantID := professionalTenantID // Use professional tenant (not enterprise)
		orbSubID := "test-orb-sub-" + uuid.New().String()

		freeTrialSuspension := &auth_entities.UserSuspension{
			SuspensionId:   uuid.New().String(),
			CreatedTime:    timestamppb.Now(),
			SuspensionType: auth_entities.UserSuspensionType_USER_SUSPENSION_TYPE_FREE_TRIAL_ABUSE,
			Evidence:       "Test suspension for free trial abuse",
		}

		user := &auth_entities.User{
			Id:                userID,
			Email:             fmt.Sprintf("<EMAIL>", userID),
			Tenants:           []string{testTenantID},
			StripeCustomerId:  "cus_" + uuid.New().String(),
			OrbSubscriptionId: orbSubID,
			Suspensions:       []*auth_entities.UserSuspension{freeTrialSuspension},
		}

		userDAO := daoFactory.GetUserDAO()
		_, err := userDAO.Create(ctx, user)
		require.NoError(t, err, "Failed to create test user")

		// Create trial subscription
		subscriptionDAO := daoFactory.GetSubscriptionDAO()
		subscription := &auth_entities.Subscription{
			SubscriptionId: orbSubID,
			Status:         auth_entities.Subscription_ACTIVE,
			ExternalPlanId: "orb_trial_plan", // Trial plan
		}
		_, err = subscriptionDAO.Create(ctx, subscription)
		require.NoError(t, err, "Failed to create trial subscription")

		// Create context for professional tenant
		professionalCtx := createRequestContext(testTenantID, []string{"AUTH_RW", "AUTH_R"})
		tenantIDPtr := testTenantID
		getUserResp, err := authServicer.GetUser(professionalCtx, &authpb.GetUserRequest{
			UserId:   userID,
			TenantId: &tenantIDPtr,
		})
		require.NoError(t, err, "Failed to get user with trial plan")
		assert.Len(t, getUserResp.User.Suspensions, 1, "Free trial abuse suspension should be visible for trial plan")
		assert.Equal(t, auth_entities.UserSuspensionType_USER_SUSPENSION_TYPE_FREE_TRIAL_ABUSE, getUserResp.User.Suspensions[0].SuspensionType)
	})

	t.Run("Community Suspensions Filtered By Plan Type", func(t *testing.T) {
		// Create a user with community abuse suspension
		userID := uuid.New().String()
		testTenantID := professionalTenantID // Use professional tenant (not enterprise)
		orbSubID := "test-orb-sub-" + uuid.New().String()

		communitySuspension := &auth_entities.UserSuspension{
			SuspensionId:   uuid.New().String(),
			CreatedTime:    timestamppb.Now(),
			SuspensionType: auth_entities.UserSuspensionType_USER_SUSPENSION_TYPE_COMMUNITY_ABUSE,
			Evidence:       "Test suspension for community abuse",
		}

		user := &auth_entities.User{
			Id:                userID,
			Email:             fmt.Sprintf("<EMAIL>", userID),
			Tenants:           []string{testTenantID},
			StripeCustomerId:  "cus_" + uuid.New().String(),
			OrbSubscriptionId: orbSubID,
			Suspensions:       []*auth_entities.UserSuspension{communitySuspension},
		}

		userDAO := daoFactory.GetUserDAO()
		_, err := userDAO.Create(ctx, user)
		require.NoError(t, err, "Failed to create test user")

		// Create community subscription
		subscriptionDAO := daoFactory.GetSubscriptionDAO()
		subscription := &auth_entities.Subscription{
			SubscriptionId: orbSubID,
			Status:         auth_entities.Subscription_ACTIVE,
			ExternalPlanId: "orb_community_plan", // Community plan
		}
		_, err = subscriptionDAO.Create(ctx, subscription)
		require.NoError(t, err, "Failed to create community subscription")

		// Create context for professional tenant
		professionalCtx := createRequestContext(testTenantID, []string{"AUTH_RW", "AUTH_R"})
		tenantIDPtr := testTenantID
		getUserResp, err := authServicer.GetUser(professionalCtx, &authpb.GetUserRequest{
			UserId:   userID,
			TenantId: &tenantIDPtr,
		})
		require.NoError(t, err, "Failed to get user with community plan")
		// TODO: Community suspensions are currently not enforced. Change when enforcement resumes
		assert.Len(t, getUserResp.User.Suspensions, 0, "Community abuse suspension should not be visible for community plan")
		// assert.Len(t, getUserResp.User.Suspensions, 1, "Community abuse suspension should be visible for community plan")
		// assert.Equal(t, auth_entities.UserSuspensionType_USER_SUSPENSION_TYPE_COMMUNITY_ABUSE, getUserResp.User.Suspensions[0].SuspensionType)
	})

	t.Run("Self-Serve Team Users - All Suspensions Filtered", func(t *testing.T) {
		// Create a user with all suspension types
		userID := uuid.New().String()

		freeTrialSuspension := &auth_entities.UserSuspension{
			SuspensionId:   uuid.New().String(),
			CreatedTime:    timestamppb.Now(),
			SuspensionType: auth_entities.UserSuspensionType_USER_SUSPENSION_TYPE_FREE_TRIAL_ABUSE,
			Evidence:       "Test suspension for free trial abuse",
		}
		communitySuspension := &auth_entities.UserSuspension{
			SuspensionId:   uuid.New().String(),
			CreatedTime:    timestamppb.Now(),
			SuspensionType: auth_entities.UserSuspensionType_USER_SUSPENSION_TYPE_COMMUNITY_ABUSE,
			Evidence:       "Test suspension for community abuse",
		}
		apiSuspension := &auth_entities.UserSuspension{
			SuspensionId:   uuid.New().String(),
			CreatedTime:    timestamppb.Now(),
			SuspensionType: auth_entities.UserSuspensionType_USER_SUSPENSION_TYPE_API_ABUSE,
			Evidence:       "Test suspension for API abuse",
		}
		paymentFraudSuspension := &auth_entities.UserSuspension{
			SuspensionId:   uuid.New().String(),
			CreatedTime:    timestamppb.Now(),
			SuspensionType: auth_entities.UserSuspensionType_USER_SUSPENSION_TYPE_PAYMENT_FRAUD,
			Evidence:       "Test suspension for payment fraud",
		}

		user := &auth_entities.User{
			Id:               userID,
			Email:            fmt.Sprintf("<EMAIL>", userID),
			Tenants:          []string{testSelfServeTeamTenantID},
			StripeCustomerId: "cus_" + uuid.New().String(),
			OrbCustomerId:    "orb_" + uuid.New().String(),
			Suspensions:      []*auth_entities.UserSuspension{freeTrialSuspension, communitySuspension, apiSuspension, paymentFraudSuspension},
		}

		userDAO := daoFactory.GetUserDAO()
		_, err := userDAO.Create(ctx, user)
		require.NoError(t, err, "Failed to create test user")

		// Create context for self-serve team tenant
		teamCtx := createRequestContext(testSelfServeTeamTenantID, []string{"AUTH_RW", "AUTH_R"})
		tenantIDPtr := testSelfServeTeamTenantID
		getUserResp, err := authServicer.GetUser(teamCtx, &authpb.GetUserRequest{
			UserId:   userID,
			TenantId: &tenantIDPtr,
		})
		require.NoError(t, err, "Failed to get self-serve team user")

		// Should only have API_ABUSE and PAYMENT_FRAUD (2 suspensions) - FREE_TRIAL_ABUSE and COMMUNITY_ABUSE filtered out
		assert.Len(t, getUserResp.User.Suspensions, 2, "Only blocking suspensions should remain for self-serve team users")
		assert.Equal(t, auth_entities.UserSuspensionType_USER_SUSPENSION_TYPE_API_ABUSE, getUserResp.User.Suspensions[0].SuspensionType, "API abuse suspension should remain")
		assert.Equal(t, auth_entities.UserSuspensionType_USER_SUSPENSION_TYPE_PAYMENT_FRAUD, getUserResp.User.Suspensions[1].SuspensionType, "payment fraud suspension should remain")
	})

	t.Run("Enterprise Users - All Suspensions Filtered", func(t *testing.T) {
		// Create a user with all suspension types
		userID := uuid.New().String()
		enterpriseTenantID := tenantID // Use the default tenant "individual-2" which is enterprise

		freeTrialSuspension := &auth_entities.UserSuspension{
			SuspensionId:   uuid.New().String(),
			CreatedTime:    timestamppb.Now(),
			SuspensionType: auth_entities.UserSuspensionType_USER_SUSPENSION_TYPE_FREE_TRIAL_ABUSE,
			Evidence:       "Test suspension for free trial abuse",
		}
		communitySuspension := &auth_entities.UserSuspension{
			SuspensionId:   uuid.New().String(),
			CreatedTime:    timestamppb.Now(),
			SuspensionType: auth_entities.UserSuspensionType_USER_SUSPENSION_TYPE_COMMUNITY_ABUSE,
			Evidence:       "Test suspension for community abuse",
		}
		apiSuspension := &auth_entities.UserSuspension{
			SuspensionId:   uuid.New().String(),
			CreatedTime:    timestamppb.Now(),
			SuspensionType: auth_entities.UserSuspensionType_USER_SUSPENSION_TYPE_API_ABUSE,
			Evidence:       "Test suspension for API abuse",
		}
		paymentFraudSuspension := &auth_entities.UserSuspension{
			SuspensionId:   uuid.New().String(),
			CreatedTime:    timestamppb.Now(),
			SuspensionType: auth_entities.UserSuspensionType_USER_SUSPENSION_TYPE_PAYMENT_FRAUD,
			Evidence:       "Test suspension for payment fraud",
		}

		user := &auth_entities.User{
			Id:               userID,
			Email:            fmt.Sprintf("<EMAIL>", userID),
			Tenants:          []string{enterpriseTenantID},
			StripeCustomerId: "cus_" + uuid.New().String(),
			OrbCustomerId:    "orb_" + uuid.New().String(),
			Suspensions:      []*auth_entities.UserSuspension{freeTrialSuspension, communitySuspension, apiSuspension, paymentFraudSuspension},
		}

		userDAO := daoFactory.GetUserDAO()
		_, err := userDAO.Create(ctx, user)
		require.NoError(t, err, "Failed to create test user")

		tenantIDPtr := enterpriseTenantID
		getUserResp, err := authServicer.GetUser(ctx, &authpb.GetUserRequest{
			UserId:   userID,
			TenantId: &tenantIDPtr,
		})
		require.NoError(t, err, "Failed to get enterprise user")

		// Should only have API_ABUSE (1 suspension) - FREE_TRIAL_ABUSE and COMMUNITY_ABUSE filtered out
		assert.Len(t, getUserResp.User.Suspensions, 1, "Only API abuse suspensions should remain for enterprise users")
		assert.Equal(t, auth_entities.UserSuspensionType_USER_SUSPENSION_TYPE_API_ABUSE, getUserResp.User.Suspensions[0].SuspensionType, "Only API abuse suspension should remain")
	})
}

// Test Backfill Cancellations:
func TestBackfillCancellations(t *testing.T) {
	bigtableFixture := NewBigtableFixture(t)
	defer bigtableFixture.Cleanup()

	sut := createAuthServicerSUT(t, bigtableFixture)
	authServicer := sut.authServicer

	mockOrbClient := orb.NewMockOrbClient()
	authServicer.orbClient = mockOrbClient

	mockStripeClient := stripelib.NewMockStripeClient()
	authServicer.stripeClient = mockStripeClient

	ctx := createCentralAuthorizedRequestContext()

	t.Run("No failed invoices", func(t *testing.T) {
		// Set up mock to return no failed invoices
		mockOrbClient.On("ListFailedInvoices", mock.Anything, mock.Anything, mock.Anything).Return([]*orb.OrbInvoice{}, nil).Once()

		// Execute the request
		resp, err := authServicer.BackfillCancellations(ctx, &authpb.BackfillCancellationsRequest{
			MakeChanges:          true,
			MaxInvoicesToProcess: 10,
			MinimumInvoiceAmount: "0",
		})

		// Verify response
		require.NoError(t, err)
		require.NotNil(t, resp)

		// Verify number of invoices -- all 0
		assert.Len(t, resp.SuccessfulInvoiceIds, 0)
		assert.Len(t, resp.FailedInvoiceIds, 0)
		assert.Len(t, resp.NotAttemptedInvoiceIds, 0)
	})

	t.Run("Invoice with ended subscription", func(t *testing.T) {
		// Reset mock orb client
		mockOrbClient.ExpectedCalls = nil
		mockOrbClient.Calls = nil

		// Set up mock to return a failed invoice with an ended subscription
		mockOrbClient.On("ListFailedInvoices", mock.Anything, mock.Anything, mock.Anything).Return([]*orb.OrbInvoice{
			{
				ID:              "invoice_1",
				SubscriptionID:  "subscription_1",
				Status:          "issued",
				Source:          "subscription",
				PaymentAttempts: 2,
			},
		}, nil).Once()
		mockOrbClient.On("GetUserSubscription", mock.Anything, "subscription_1", mock.Anything).Return(&orb.OrbSubscriptionInfo{
			OrbStatus: "ended",
		}, nil).Once()
		mockOrbClient.On("GetCustomerInfo", mock.Anything, mock.Anything).Return(&orb.OrbCustomerInfo{
			OrbCustomerID:    "customer_1",
			StripeCustomerID: "stripe_customer_1",
		}, nil).Once()
		mockOrbClient.On("VoidInvoice", mock.Anything, mock.Anything, mock.Anything).Return(nil).Once()

		// Execute the request
		resp, err := authServicer.BackfillCancellations(ctx, &authpb.BackfillCancellationsRequest{
			MakeChanges:          true,
			MaxInvoicesToProcess: 10,
			MinimumInvoiceAmount: "0",
		})

		// Verify response
		require.NoError(t, err)
		require.NotNil(t, resp)

		assert.Len(t, resp.SuccessfulInvoiceIds, 0)
		assert.Len(t, resp.FailedInvoiceIds, 0)
		assert.Len(t, resp.NotAttemptedInvoiceIds, 0)
		assert.Len(t, resp.VoidedInvoiceIds, 1)
	})

	t.Run("Invoice with active subscription, should be cancelled", func(t *testing.T) {
		// Reset mock orb client
		mockOrbClient.ExpectedCalls = nil
		mockOrbClient.Calls = nil

		// Set up mock to return a failed invoice with an active subscription
		mockOrbClient.On("ListFailedInvoices", mock.Anything, mock.Anything, mock.Anything).Return([]*orb.OrbInvoice{
			{
				ID:              "invoice_1",
				SubscriptionID:  "subscription_1",
				Status:          "issued",
				Source:          "subscription",
				PaymentAttempts: 5,
				AmountDue:       "10000",
			},
		}, nil).Once()
		mockOrbClient.On("GetUserSubscription", mock.Anything, "subscription_1", mock.Anything).Return(&orb.OrbSubscriptionInfo{
			OrbStatus: "active",
		}, nil)
		mockOrbClient.On("CancelOrbSubscriptionUnscheduleFirst", mock.Anything, "subscription_1", orb.PlanChangeImmediate, mock.Anything).Return(nil).Once()
		mockOrbClient.On("GetCustomerInfo", mock.Anything, mock.Anything).Return(&orb.OrbCustomerInfo{
			OrbCustomerID:    "customer_1",
			StripeCustomerID: "stripe_customer_1",
		}, nil).Once()
		mockOrbClient.On("VoidInvoice", mock.Anything, "invoice_1", mock.Anything).Return(nil).Once()

		// Execute the request
		resp, err := authServicer.BackfillCancellations(ctx, &authpb.BackfillCancellationsRequest{
			MakeChanges:          true,
			MaxInvoicesToProcess: 10,
			MinimumInvoiceAmount: "0",
		})

		// Verify response
		require.NoError(t, err)
		require.NotNil(t, resp)

		// Verify number of invoices
		assert.Len(t, resp.SuccessfulInvoiceIds, 1)
		assert.Len(t, resp.FailedInvoiceIds, 0)
		assert.Len(t, resp.NotAttemptedInvoiceIds, 0)

		// Assert we cancel them
		mockOrbClient.AssertNumberOfCalls(t, "CancelOrbSubscriptionUnscheduleFirst", 1)
	})

	t.Run("Invoice with active subscription, don't make changes", func(t *testing.T) {
		// Reset mock orb client
		mockOrbClient.ExpectedCalls = nil
		mockOrbClient.Calls = nil

		// Set up mock to return a failed invoice with an active subscription
		mockOrbClient.On("ListFailedInvoices", mock.Anything, mock.Anything, mock.Anything).Return([]*orb.OrbInvoice{
			{
				ID:              "invoice_1",
				SubscriptionID:  "subscription_1",
				Status:          "issued",
				Source:          "subscription",
				PaymentAttempts: 2,
				AmountDue:       "10000",
			},
		}, nil).Once()
		mockOrbClient.On("GetUserSubscription", mock.Anything, "subscription_1", mock.Anything).Return(&orb.OrbSubscriptionInfo{
			OrbStatus: "active",
		}, nil)
		mockOrbClient.On("CancelOrbSubscriptionUnscheduleFirst", mock.Anything, "subscription_1", orb.PlanChangeImmediate, mock.Anything).Return(nil).Once()
		mockOrbClient.On("GetCustomerInfo", mock.Anything, mock.Anything).Return(&orb.OrbCustomerInfo{
			OrbCustomerID:    "customer_1",
			StripeCustomerID: "stripe_customer_1",
		}, nil).Once()

		// Execute the request
		resp, err := authServicer.BackfillCancellations(ctx, &authpb.BackfillCancellationsRequest{
			MakeChanges:          false,
			MaxInvoicesToProcess: 10,
			MinimumInvoiceAmount: "0",
		})

		// Verify response
		require.NoError(t, err)
		require.NotNil(t, resp)

		// Verify number of invoices
		assert.Len(t, resp.SuccessfulInvoiceIds, 1)
		assert.Len(t, resp.FailedInvoiceIds, 0)
		assert.Len(t, resp.NotAttemptedInvoiceIds, 0)

		// Assert we cancel them
		mockOrbClient.AssertNumberOfCalls(t, "CancelOrbSubscriptionUnscheduleFirst", 0)
	})

	t.Run("non-issued invoice", func(t *testing.T) {
		// Reset mock orb client
		mockOrbClient.ExpectedCalls = nil
		mockOrbClient.Calls = nil

		// Set up mock to return a failed invoice that is not from a subscription or is not issued
		mockOrbClient.On("ListFailedInvoices", mock.Anything, mock.Anything, mock.Anything).Return([]*orb.OrbInvoice{
			{
				ID:              "invoice_1",
				SubscriptionID:  "subscription_1",
				Status:          "paid",
				Source:          "subscription",
				PaymentAttempts: 2,
			},
		}, nil).Once()
		mockOrbClient.On("GetCustomerInfo", mock.Anything, mock.Anything).Return(&orb.OrbCustomerInfo{
			OrbCustomerID:    "customer_1",
			StripeCustomerID: "stripe_customer_1",
		}, nil).Once()

		// Execute the request
		resp, err := authServicer.BackfillCancellations(ctx, &authpb.BackfillCancellationsRequest{
			MakeChanges:          true,
			MaxInvoicesToProcess: 10,
			MinimumInvoiceAmount: "0",
		})

		// Verify response
		require.NoError(t, err)
		require.NotNil(t, resp)
		assert.Len(t, resp.SuccessfulInvoiceIds, 0)
		assert.Len(t, resp.FailedInvoiceIds, 0)
		assert.Len(t, resp.NotAttemptedInvoiceIds, 1)
	})

	t.Run("one-off invoices, one we should void and one we should not", func(t *testing.T) {
		// Reset mock orb client
		mockOrbClient.ExpectedCalls = nil
		mockOrbClient.Calls = nil

		// Set up mock to return a failed invoice that is not from a subscription or is not issued
		mockOrbClient.On("ListFailedInvoices", mock.Anything, mock.Anything, mock.Anything).Return([]*orb.OrbInvoice{
			{
				ID:              "invoice_1",
				Status:          "issued",
				Source:          "one_off",
				PaymentAttempts: 1,
				AmountDue:       "100",
				IssuedTime:      time.Now().Add(-1 * 24 * time.Hour),
			},
			{
				ID:              "invoice_2",
				Status:          "issued",
				Source:          "one_off",
				PaymentAttempts: 5,
				AmountDue:       "100",
				IssuedTime:      time.Now().Add(-1 * 24 * time.Hour),
			},
		}, nil).Once()
		mockOrbClient.On("GetCustomerInfo", mock.Anything, mock.Anything).Return(&orb.OrbCustomerInfo{
			OrbCustomerID:    "customer_1",
			StripeCustomerID: "stripe_customer_1",
		}, nil).Once()
		mockOrbClient.On("VoidInvoice", mock.Anything, mock.Anything, mock.Anything).Return(nil).Once()

		// Execute the request
		resp, err := authServicer.BackfillCancellations(ctx, &authpb.BackfillCancellationsRequest{
			MakeChanges:          true,
			MaxInvoicesToProcess: 10,
			MinimumInvoiceAmount: "0",
		})

		// Verify response
		require.NoError(t, err)
		require.NotNil(t, resp)
		assert.Len(t, resp.SuccessfulInvoiceIds, 0)
		assert.Len(t, resp.FailedInvoiceIds, 0)
		assert.Len(t, resp.NotAttemptedInvoiceIds, 1)
		assert.Len(t, resp.VoidedInvoiceIds, 1)
	})
}

func TestRemovePendingStatus(t *testing.T) {
	bigtableFixture := NewBigtableFixture(t)
	defer bigtableFixture.Cleanup()

	sut := createAuthServicerSUT(t, bigtableFixture)
	authServicer := sut.authServicer
	validClaims := &auth.AugmentClaims{
		TenantID:         "",
		TenantName:       "",
		UserID:           "<EMAIL>",
		OpaqueUserID:     "iap:<EMAIL>",
		OpaqueUserIDType: auth_entities.UserId_INTERNAL_IAP.String(),
		Scope:            []string{tokenscopesproto.Scope_AUTH_RW.String()},
	}
	validCtx := validClaims.NewContext(context.Background())

	t.Run("Auth Checks", func(t *testing.T) {
		// Test with insufficient permissions (Auth_R instead of Auth_RW)
		claims := &auth.AugmentClaims{
			TenantID:         "",
			TenantName:       "",
			UserID:           "<EMAIL>",
			OpaqueUserID:     "<EMAIL>",
			OpaqueUserIDType: auth_entities.UserId_INTERNAL_IAP.String(),
			Scope:            []string{tokenscopesproto.Scope_AUTH_R.String()},
		}
		ctx := claims.NewContext(context.Background())
		_, err := authServicer.RemovePendingStatus(ctx, &authpb.RemovePendingStatusRequest{})
		require.Error(t, err)
		require.Equal(t, codes.PermissionDenied, status.Code(err))

		// Test with Non-IAP User
		claims = &auth.AugmentClaims{
			TenantID:         "",
			TenantName:       "",
			UserID:           "<EMAIL>",
			OpaqueUserID:     "<EMAIL>",
			OpaqueUserIDType: auth_entities.UserId_AUGMENT.String(),
			Scope:            []string{tokenscopesproto.Scope_AUTH_RW.String()},
		}
		ctx = claims.NewContext(context.Background())
		_, err = authServicer.RemovePendingStatus(ctx, &authpb.RemovePendingStatusRequest{})
		require.Error(t, err)
		require.Equal(t, codes.PermissionDenied, status.Code(err))
	})

	t.Run("Nonexistent user/tenant/subscription", func(t *testing.T) {
		// User not found
		_, err := authServicer.RemovePendingStatus(validCtx, &authpb.RemovePendingStatusRequest{
			Request: &authpb.RemovePendingStatusRequest_UserId{
				UserId: "non-existent-user",
			},
		})
		require.Error(t, err)
		require.Equal(t, codes.NotFound, status.Code(err))

		// Tenant not found
		_, err = authServicer.RemovePendingStatus(validCtx, &authpb.RemovePendingStatusRequest{
			Request: &authpb.RemovePendingStatusRequest_TenantId{
				TenantId: "non-existent-tenant",
			},
		})
		require.Error(t, err)
		require.Equal(t, codes.NotFound, status.Code(err))

		// Subscription not found
		_, err = authServicer.RemovePendingStatus(validCtx, &authpb.RemovePendingStatusRequest{
			Request: &authpb.RemovePendingStatusRequest_SubscriptionId{
				SubscriptionId: "non-existent-subscription",
			},
		})
		require.Error(t, err)
		require.Equal(t, codes.NotFound, status.Code(err))
	})

	t.Run("Update user", func(t *testing.T) {
		// Create user with pending tier change
		userDAO := sut.daoFactory.GetUserDAO()
		user := &auth_entities.User{
			Id:      "user-with-tier-change",
			Email:   "<EMAIL>",
			Tenants: []string{"tenant-1"},
			TierChange: &auth_entities.User_TierChangeInfo{
				Id:         "tier-change-1",
				TargetTier: auth_entities.UserTier_PROFESSIONAL,
			},
		}
		_, err := userDAO.Create(validCtx, user)
		require.NoError(t, err)

		// Remove pending tier change
		_, err = authServicer.RemovePendingStatus(validCtx, &authpb.RemovePendingStatusRequest{
			Request: &authpb.RemovePendingStatusRequest_UserId{
				UserId: "user-with-tier-change",
			},
		})
		require.NoError(t, err)

		// Verify user was updated
		updatedUser, err := userDAO.Get(validCtx, "user-with-tier-change")
		require.NoError(t, err)
		require.Nil(t, updatedUser.TierChange)
	})

	t.Run("Update tenant subscription mapping", func(t *testing.T) {
		// Create tenant with pending plan change
		tenantSubscriptionMappingDAO := sut.daoFactory.GetTenantSubscriptionMappingDAO()
		tenant := &auth_entities.TenantSubscriptionMapping{
			TenantId: "tenant-with-plan-change",
			PlanChange: &auth_entities.TenantSubscriptionMapping_PlanChangeInfo{
				Id:              "plan-change-1",
				TargetOrbPlanId: "orb-plan-1",
			},
		}
		_, err := tenantSubscriptionMappingDAO.Create(validCtx, tenant)
		require.NoError(t, err)

		// Remove pending plan change
		_, err = authServicer.RemovePendingStatus(validCtx, &authpb.RemovePendingStatusRequest{
			Request: &authpb.RemovePendingStatusRequest_TenantId{
				TenantId: "tenant-with-plan-change",
			},
		})
		require.NoError(t, err)

		// Verify tenant was updated
		updatedTenant, err := tenantSubscriptionMappingDAO.Get(validCtx, "tenant-with-plan-change")
		require.NoError(t, err)
		require.Nil(t, updatedTenant.PlanChange)
	})

	t.Run("Update subscription", func(t *testing.T) {
		// Create subscription with pending seat change
		subscriptionDAO := sut.daoFactory.GetSubscriptionDAO()
		changeId := "seat-change-1"
		subscription := &auth_entities.Subscription{
			SubscriptionId:       "subscription-with-seat-change",
			SubscriptionChangeId: &changeId,
		}
		_, err := subscriptionDAO.Create(validCtx, subscription)
		require.NoError(t, err)

		// Remove pending seat change
		_, err = authServicer.RemovePendingStatus(validCtx, &authpb.RemovePendingStatusRequest{
			Request: &authpb.RemovePendingStatusRequest_SubscriptionId{
				SubscriptionId: "subscription-with-seat-change",
			},
		})
		require.NoError(t, err)

		// Verify subscription was updated
		updatedSubscription, err := subscriptionDAO.Get(validCtx, "subscription-with-seat-change")
		require.NoError(t, err)
		require.Nil(t, updatedSubscription.SubscriptionChangeId)
	})
}

func TestAllowNewTrialForUsers(t *testing.T) {
	bigtableFixture := NewBigtableFixture(t)
	defer bigtableFixture.Cleanup()

	sut := createAuthServicerSUT(t, bigtableFixture)

	// Set up mock orb client
	mockOrbClient := &orb.MockOrbClient{}
	sut.authServicer.orbClient = mockOrbClient

	ctx := createRequestContext(auth.TenantIDWildcard, []string{tokenscopesproto.Scope_AUTH_ADMIN.String()})

	t.Run("successful trial allow for regular user", func(t *testing.T) {
		// Reset mock
		mockOrbClient.ExpectedCalls = nil
		mockOrbClient.Calls = nil

		// Create a regular user with subscription data
		userDAO := sut.daoFactory.GetUserDAO()
		user := &auth_entities.User{
			Id:                     "user-1",
			Email:                  "<EMAIL>",
			Tenants:                []string{professionalTenantID},
			OrbCustomerId:          "orb-customer-1",
			OrbSubscriptionId:      "", // No active subscription
			SubscriptionCreationId: "sub-creation-1",
		}
		_, err := userDAO.Create(ctx, user)
		require.NoError(t, err)

		// Create user-tenant mapping (use tenant name, not ID)
		userTenantMappingDAO := sut.daoFactory.GetUserTenantMappingDAO("professional-tenant")
		mapping := &auth_entities.UserTenantMapping{
			UserId: "user-1",
			Tenant: professionalTenantID,
		}
		_, err = userTenantMappingDAO.Create(ctx, mapping)
		require.NoError(t, err)

		// Execute request
		resp, err := sut.authServicer.AllowNewTrialForUsers(ctx, &authpb.AllowNewTrialForUsersRequest{
			UserIds: []string{"user-1"},
		})

		// Verify response
		require.NoError(t, err)
		require.NotNil(t, resp)
		require.Len(t, resp.Results, 1)
		assert.Equal(t, authpb.TrialAllowResult_STATUS_SUCCESS, resp.Results[0].Status)
		assert.Equal(t, "user-1", resp.Results[0].UserId)
		assert.Empty(t, resp.Results[0].ErrorMessage)

		// Verify user subscription fields were cleared
		updatedUser, err := userDAO.Get(ctx, "user-1")
		require.NoError(t, err)
		assert.Empty(t, updatedUser.SubscriptionCreationId)
		assert.Empty(t, updatedUser.OrbSubscriptionId)

		// Verify user was removed from tenant
		assert.Empty(t, updatedUser.Tenants)

		// Verify tenant mapping was deleted
		deletedMapping, err := userTenantMappingDAO.GetByUser(ctx, "user-1")
		require.NoError(t, err)
		assert.Nil(t, deletedMapping)
	})

	t.Run("user with active non-trial orb subscription should fail", func(t *testing.T) {
		// Reset mock
		mockOrbClient.ExpectedCalls = nil
		mockOrbClient.Calls = nil

		// Create a user with active orb subscription
		userDAO := sut.daoFactory.GetUserDAO()
		user := &auth_entities.User{
			Id:                "user-active-sub",
			Email:             "<EMAIL>",
			Tenants:           []string{professionalTenantID},
			OrbCustomerId:     "orb-customer-active",
			OrbSubscriptionId: "orb-sub-active",
		}
		_, err := userDAO.Create(ctx, user)
		require.NoError(t, err)

		// Mock orb client to return active subscription
		mockOrbClient.On("GetUserSubscription", mock.Anything, "orb-sub-active", mock.Anything).
			Return(&orb.OrbSubscriptionInfo{
				OrbStatus: "active",
			}, nil).Once()

		// Execute request
		resp, err := sut.authServicer.AllowNewTrialForUsers(ctx, &authpb.AllowNewTrialForUsersRequest{
			UserIds: []string{"user-active-sub"},
		})

		// Verify response
		require.NoError(t, err)
		require.NotNil(t, resp)
		require.Len(t, resp.Results, 1)
		assert.Equal(t, authpb.TrialAllowResult_STATUS_ERROR, resp.Results[0].Status)
		assert.Equal(t, "user-active-sub", resp.Results[0].UserId)
		assert.Contains(t, resp.Results[0].ErrorMessage, "user has active non-trial subscription")

		// Verify orb client was called
		mockOrbClient.AssertExpectations(t)
	})

	t.Run("user with active trial subscription should succeed", func(t *testing.T) {
		// Reset mock
		mockOrbClient.ExpectedCalls = nil
		mockOrbClient.Calls = nil

		// Create a user with active trial subscription
		userDAO := sut.daoFactory.GetUserDAO()
		user := &auth_entities.User{
			Id:                "user-active-trial",
			Email:             "<EMAIL>",
			Tenants:           []string{professionalTenantID},
			OrbCustomerId:     "orb-customer-trial",
			OrbSubscriptionId: "orb-sub-trial",
		}
		_, err := userDAO.Create(ctx, user)
		require.NoError(t, err)

		// Create user-tenant mapping
		userTenantMappingDAO := sut.daoFactory.GetUserTenantMappingDAO("professional-tenant")
		mapping := &auth_entities.UserTenantMapping{
			UserId: "user-active-trial",
			Tenant: professionalTenantID,
		}
		_, err = userTenantMappingDAO.Create(ctx, mapping)
		require.NoError(t, err)

		// Mock orb client to return active trial subscription created more than 48 hours ago
		mockOrbClient.On("GetUserSubscription", mock.Anything, "orb-sub-trial", mock.Anything).
			Return(&orb.OrbSubscriptionInfo{
				OrbStatus:      "active",
				ExternalPlanID: sut.authServicer.orbConfig.GetTrialPlan().ID,
				CreatedAt:      time.Now().Add(-72 * time.Hour), // 72 hours ago (more than 48)
			}, nil).Once()

		mockOrbClient.On("CancelOrbSubscriptionUnscheduleFirst", mock.Anything, "orb-sub-trial", orb.PlanChangeImmediate, mock.Anything).
			Return(nil).Once()

		// Execute request
		resp, err := sut.authServicer.AllowNewTrialForUsers(ctx, &authpb.AllowNewTrialForUsersRequest{
			UserIds: []string{"user-active-trial"},
		})

		// Verify response
		require.NoError(t, err)
		require.NotNil(t, resp)
		require.Len(t, resp.Results, 1)
		assert.Equal(t, authpb.TrialAllowResult_STATUS_SUCCESS, resp.Results[0].Status)
		assert.Equal(t, "user-active-trial", resp.Results[0].UserId)
		assert.Empty(t, resp.Results[0].ErrorMessage)

		// Verify user subscription fields were cleared
		updatedUser, err := userDAO.Get(ctx, "user-active-trial")
		require.NoError(t, err)
		assert.Empty(t, updatedUser.OrbSubscriptionId)

		// Verify user was removed from tenant
		assert.Empty(t, updatedUser.Tenants)

		// Verify tenant mapping was deleted
		deletedMapping, err := userTenantMappingDAO.GetByUser(ctx, "user-active-trial")
		require.NoError(t, err)
		assert.Nil(t, deletedMapping)

		// Verify orb client was called
		mockOrbClient.AssertExpectations(t)
	})

	t.Run("user with active trial subscription created less than 48 hours ago should fail", func(t *testing.T) {
		// Reset mock
		mockOrbClient.ExpectedCalls = nil
		mockOrbClient.Calls = nil

		// Create a user with active trial subscription
		userDAO := sut.daoFactory.GetUserDAO()
		user := &auth_entities.User{
			Id:                "user-recent-trial",
			Email:             "<EMAIL>",
			Tenants:           []string{professionalTenantID},
			OrbCustomerId:     "orb-customer-recent-trial",
			OrbSubscriptionId: "orb-sub-recent-trial",
		}
		_, err := userDAO.Create(ctx, user)
		require.NoError(t, err)

		// Create user-tenant mapping
		userTenantMappingDAO := sut.daoFactory.GetUserTenantMappingDAO("professional-tenant")
		mapping := &auth_entities.UserTenantMapping{
			UserId: "user-recent-trial",
			Tenant: professionalTenantID,
		}
		_, err = userTenantMappingDAO.Create(ctx, mapping)
		require.NoError(t, err)

		// Mock orb client to return active trial subscription created less than 48 hours ago
		mockOrbClient.On("GetUserSubscription", mock.Anything, "orb-sub-recent-trial", mock.Anything).
			Return(&orb.OrbSubscriptionInfo{
				OrbStatus:      "active",
				ExternalPlanID: sut.authServicer.orbConfig.GetTrialPlan().ID,
				CreatedAt:      time.Now().Add(-24 * time.Hour), // 24 hours ago (less than 48)
			}, nil).Once()

		// Execute request
		resp, err := sut.authServicer.AllowNewTrialForUsers(ctx, &authpb.AllowNewTrialForUsersRequest{
			UserIds: []string{"user-recent-trial"},
		})

		// Verify response
		require.NoError(t, err)
		require.NotNil(t, resp)
		require.Len(t, resp.Results, 1)
		assert.Equal(t, authpb.TrialAllowResult_STATUS_ERROR, resp.Results[0].Status)
		assert.Equal(t, "user-recent-trial", resp.Results[0].UserId)
		assert.Contains(t, resp.Results[0].ErrorMessage, "user has active trial subscription created less than 48 hours ago")

		// Verify user subscription fields were NOT cleared (since operation failed)
		updatedUser, err := userDAO.Get(ctx, "user-recent-trial")
		require.NoError(t, err)
		assert.Equal(t, "orb-sub-recent-trial", updatedUser.OrbSubscriptionId)

		// Verify user was NOT removed from tenant (since operation failed)
		assert.Equal(t, []string{professionalTenantID}, updatedUser.Tenants)

		// Verify tenant mapping was NOT deleted (since operation failed)
		existingMapping, err := userTenantMappingDAO.GetByUser(ctx, "user-recent-trial")
		require.NoError(t, err)
		assert.NotNil(t, existingMapping)

		// Verify orb client was called only once (for GetUserSubscription, not for cancellation)
		mockOrbClient.AssertExpectations(t)
	})

	t.Run("user with no tenant should succeed", func(t *testing.T) {
		// Reset mock
		mockOrbClient.ExpectedCalls = nil
		mockOrbClient.Calls = nil

		// Create a user with no tenant
		userDAO := sut.daoFactory.GetUserDAO()
		user := &auth_entities.User{
			Id:                "user-no-tenant",
			Email:             "<EMAIL>",
			Tenants:           []string{}, // No tenant
			OrbCustomerId:     "orb-customer-no-tenant",
			OrbSubscriptionId: "orb-sub-no-tenant",
		}
		_, err := userDAO.Create(ctx, user)
		require.NoError(t, err)

		// Mock orb client to return inactive subscription
		mockOrbClient.On("GetUserSubscription", mock.Anything, "orb-sub-no-tenant", mock.Anything).
			Return(&orb.OrbSubscriptionInfo{
				OrbStatus: "inactive",
			}, nil).Once()

		// Execute request
		resp, err := sut.authServicer.AllowNewTrialForUsers(ctx, &authpb.AllowNewTrialForUsersRequest{
			UserIds: []string{"user-no-tenant"},
		})

		// Verify response
		require.NoError(t, err)
		require.NotNil(t, resp)
		require.Len(t, resp.Results, 1)
		assert.Equal(t, authpb.TrialAllowResult_STATUS_SUCCESS, resp.Results[0].Status)
		assert.Equal(t, "user-no-tenant", resp.Results[0].UserId)
		assert.Empty(t, resp.Results[0].ErrorMessage)

		// Verify user subscription fields were cleared
		updatedUser, err := userDAO.Get(ctx, "user-no-tenant")
		require.NoError(t, err)
		assert.Empty(t, updatedUser.OrbSubscriptionId)

		// Verify orb client was called
		mockOrbClient.AssertExpectations(t)
	})

	t.Run("user not found should fail", func(t *testing.T) {
		// Execute request for non-existent user
		resp, err := sut.authServicer.AllowNewTrialForUsers(ctx, &authpb.AllowNewTrialForUsersRequest{
			UserIds: []string{"non-existent-user"},
		})

		// Verify response
		require.NoError(t, err)
		require.NotNil(t, resp)
		require.Len(t, resp.Results, 1)
		assert.Equal(t, authpb.TrialAllowResult_STATUS_ERROR, resp.Results[0].Status)
		assert.Equal(t, "non-existent-user", resp.Results[0].UserId)
		assert.Contains(t, resp.Results[0].ErrorMessage, "user not found")
	})

	t.Run("AUTH_RW scope should be denied", func(t *testing.T) {
		claims := &auth.AugmentClaims{
			TenantID:         "",
			TenantName:       "",
			UserID:           "<EMAIL>",
			OpaqueUserID:     "<EMAIL>",
			OpaqueUserIDType: "GOOGLE",
			UserEmail:        "<EMAIL>",
			Scope:            []string{"AUTH_RW"},
		}
		nonIapCtx := claims.NewContext(context.Background())

		// Execute request
		_, err := sut.authServicer.AllowNewTrialForUsers(nonIapCtx, &authpb.AllowNewTrialForUsersRequest{
			UserIds: []string{"user-1"},
		})

		// Verify error
		require.Error(t, err)
		require.Equal(t, codes.PermissionDenied, status.Code(err))
	})
}

func TestBackfillNetsuiteInfo(t *testing.T) {
	bigtableFixture := NewBigtableFixture(t)
	defer bigtableFixture.Cleanup()

	sut := createAuthServicerSUT(t, bigtableFixture)

	// Set up mock orb client
	mockOrbClient := orb.NewMockOrbClient()
	authServicer := sut.authServicer
	authServicer.orbClient = mockOrbClient
	validClaims := &auth.AugmentClaims{
		TenantID:         "",
		TenantName:       "",
		UserID:           "<EMAIL>",
		OpaqueUserID:     "iap:<EMAIL>",
		OpaqueUserIDType: auth_entities.UserId_INTERNAL_IAP.String(),
		Scope:            []string{tokenscopesproto.Scope_AUTH_ADMIN.String()},
	}
	validCtx := validClaims.NewContext(context.Background())

	t.Run("Auth Checks", func(t *testing.T) {
		// Test with insufficient permissions (Auth_RW instead of Auth_ADMIN)
		claims := &auth.AugmentClaims{
			TenantID:         "",
			TenantName:       "",
			UserID:           "<EMAIL>",
			OpaqueUserID:     "<EMAIL>",
			OpaqueUserIDType: auth_entities.UserId_INTERNAL_IAP.String(),
			Scope:            []string{tokenscopesproto.Scope_AUTH_RW.String()},
		}
		ctx := claims.NewContext(context.Background())
		_, err := authServicer.BackfillNetsuiteInfo(ctx, &authpb.BackfillNetsuiteInfoRequest{})
		require.Error(t, err)
		require.Equal(t, codes.PermissionDenied, status.Code(err))
	})

	t.Run("Invalid Preconditions", func(t *testing.T) {
		// Test with max users <= 0
		_, err := authServicer.BackfillNetsuiteInfo(validCtx, &authpb.BackfillNetsuiteInfoRequest{
			MaxUsers: 0,
			DryRun:   false,
		})
		require.Error(t, err)
		require.Equal(t, codes.FailedPrecondition, status.Code(err))

		// Test with netsuite disabled
		sut.authServicer.orbConfig.AccountingConfig.Enabled = false
		_, err = authServicer.BackfillNetsuiteInfo(validCtx, &authpb.BackfillNetsuiteInfoRequest{
			MaxUsers: 100,
			DryRun:   false,
		})
		require.Error(t, err)
		require.Equal(t, codes.FailedPrecondition, status.Code(err))

		sut.authServicer.orbConfig.AccountingConfig.Enabled = true
	})

	t.Run("User already has info", func(t *testing.T) {
		mockOrbClient.On("ListCustomers", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(&orb.OrbCustomersPage{
			Customers: []orb.OrbCustomerInfo{
				{
					OrbCustomerID: "customer_1",
					Email:         "<EMAIL>",
					AccountingInfo: &orb.AccountingProviderInfo{
						ProviderType: sut.authServicer.orbConfig.AccountingConfig.ProviderType,
						ProviderID:   sut.authServicer.orbConfig.AccountingConfig.ProviderID,
					},
				},
			},
			HasMore: false,
		}, nil).Once()

		resp, err := authServicer.BackfillNetsuiteInfo(validCtx, &authpb.BackfillNetsuiteInfoRequest{
			MaxUsers: 100,
			DryRun:   false,
		})
		require.NoError(t, err)
		require.NotNil(t, resp)
		assert.Len(t, resp.FailedCustomerIds, 0)
		assert.Len(t, resp.SuccessfulCustomerIds, 0)
		assert.Len(t, resp.UnchangedCustomerIds, 1)
	})

	t.Run("User has different netsuite ID", func(t *testing.T) {
		mockOrbClient.On("ListCustomers", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(&orb.OrbCustomersPage{
			Customers: []orb.OrbCustomerInfo{
				{
					OrbCustomerID: "customer_1",
					Email:         "<EMAIL>",
					AccountingInfo: &orb.AccountingProviderInfo{
						ProviderType: sut.authServicer.orbConfig.AccountingConfig.ProviderType,
						ProviderID:   "123",
					},
				},
			},
			HasMore: false,
		}, nil).Once()
		mockOrbClient.On("UpdateCustomerInformation", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(nil).Once()

		resp, err := authServicer.BackfillNetsuiteInfo(validCtx, &authpb.BackfillNetsuiteInfoRequest{
			MaxUsers: 100,
			DryRun:   false,
		})
		require.NoError(t, err)
		require.NotNil(t, resp)
		assert.Len(t, resp.FailedCustomerIds, 0)
		assert.Len(t, resp.SuccessfulCustomerIds, 1)
		assert.Len(t, resp.UnchangedCustomerIds, 0)
		mockOrbClient.AssertCalled(t, "UpdateCustomerInformation", mock.Anything, mock.Anything, mock.Anything, mock.Anything)
	})

	t.Run("Dry run", func(t *testing.T) {
		// clear calls
		mockOrbClient.ExpectedCalls = nil
		mockOrbClient.Calls = nil

		mockOrbClient.On("ListCustomers", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(&orb.OrbCustomersPage{
			Customers: []orb.OrbCustomerInfo{
				{
					OrbCustomerID: "customer_1",
					Email:         "<EMAIL>",
				},
				{
					OrbCustomerID: "customer_2",
					Email:         "<EMAIL>",
				},
			},
			HasMore: false,
		}, nil).Once()

		resp, err := authServicer.BackfillNetsuiteInfo(validCtx, &authpb.BackfillNetsuiteInfoRequest{
			MaxUsers: 100,
			DryRun:   true,
		})
		require.NoError(t, err)
		require.NotNil(t, resp)
		assert.Len(t, resp.FailedCustomerIds, 0)
		assert.Len(t, resp.SuccessfulCustomerIds, 2)
		assert.Len(t, resp.UnchangedCustomerIds, 0)
		mockOrbClient.AssertNotCalled(t, "UpdateCustomerInformation", mock.Anything, mock.Anything, mock.Anything, mock.Anything)
	})

	t.Run("Make changes (not dry run)", func(t *testing.T) {
		// clear calls
		mockOrbClient.ExpectedCalls = nil
		mockOrbClient.Calls = nil

		mockOrbClient.On("ListCustomers", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(&orb.OrbCustomersPage{
			Customers: []orb.OrbCustomerInfo{
				{
					OrbCustomerID: "customer_1",
					Email:         "<EMAIL>",
				},
				{
					OrbCustomerID: "customer_2",
					Email:         "<EMAIL>",
				},
			},
			HasMore: false,
		}, nil).Once()
		mockOrbClient.On("UpdateCustomerInformation", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(nil).Twice()

		resp, err := authServicer.BackfillNetsuiteInfo(validCtx, &authpb.BackfillNetsuiteInfoRequest{
			MaxUsers: 100,
			DryRun:   false,
		})
		require.NoError(t, err)
		require.NotNil(t, resp)
		assert.Len(t, resp.FailedCustomerIds, 0)
		assert.Len(t, resp.SuccessfulCustomerIds, 2)
		assert.Len(t, resp.UnchangedCustomerIds, 0)
		mockOrbClient.AssertNumberOfCalls(t, "UpdateCustomerInformation", 2)
	})
}

func TestSyncCustomerAddress(t *testing.T) {
	bigtableFixture := NewBigtableFixture(t)
	defer bigtableFixture.Cleanup()

	sut := createAuthServicerSUT(t, bigtableFixture)

	// Set up mock orb client
	mockOrbClient := orb.NewMockOrbClient()
	authServicer := sut.authServicer
	authServicer.orbClient = mockOrbClient
	validClaims := &auth.AugmentClaims{
		TenantID:         "",
		TenantName:       "",
		UserID:           "<EMAIL>",
		OpaqueUserID:     "iap:<EMAIL>",
		OpaqueUserIDType: auth_entities.UserId_INTERNAL_IAP.String(),
		Scope:            []string{tokenscopesproto.Scope_AUTH_ADMIN.String()},
	}
	validCtx := validClaims.NewContext(context.Background())

	t.Run("Existing address", func(t *testing.T) {
		results := &BackfillAddressResults{}
		err := authServicer.syncCustomerAddress(validCtx, orb.OrbCustomerInfo{
			OrbCustomerID: "customer_1",
			BillingAddress: &orb.Address{
				Line1:      "123 Main St",
				City:       "San Francisco",
				State:      "CA",
				PostalCode: "94105",
				Country:    "US",
			},
		}, false, results)
		require.NoError(t, err)
		assert.Len(t, results.UnchangedCustomers, 1)
		assert.Contains(t, results.UnchangedCustomers, "customer_1")

		// Try just postal code
		results = &BackfillAddressResults{}
		err = authServicer.syncCustomerAddress(validCtx, orb.OrbCustomerInfo{
			OrbCustomerID: "customer_2",
			BillingAddress: &orb.Address{
				PostalCode: "94105",
			},
		}, false, results)
		require.NoError(t, err)
		assert.Len(t, results.UnchangedCustomers, 1)
		assert.Contains(t, results.UnchangedCustomers, "customer_2")
	})

	t.Run("No Stripe Cusomter ID", func(t *testing.T) {
		results := &BackfillAddressResults{}
		err := authServicer.syncCustomerAddress(validCtx, orb.OrbCustomerInfo{
			OrbCustomerID: "customer_3",
		}, false, results)
		require.NoError(t, err)
		assert.Len(t, results.FailedCustomers, 1)
		assert.Contains(t, results.FailedCustomers, "customer_3")
	})

	t.Run("Customer has address", func(t *testing.T) {
		mockStripeClient := stripelib.NewMockStripeClient()
		authServicer.stripeClient = mockStripeClient

		// Customer has an address
		mockStripeClient.Customers["<EMAIL>"] = &stripe.Customer{
			ID:    "stripe_customer_1",
			Email: "<EMAIL>",
			Address: &stripe.Address{
				Line1:      "123 Main St",
				Line2:      "Apt 4B",
				City:       "San Francisco",
				State:      "CA",
				PostalCode: "94105",
				Country:    "US",
			},
		}

		results := &BackfillAddressResults{}
		err := authServicer.syncCustomerAddress(validCtx, orb.OrbCustomerInfo{
			OrbCustomerID:    "customer_4",
			StripeCustomerID: "stripe_customer_1",
		}, true, results)
		require.NoError(t, err)
		assert.Len(t, results.SuccessfulCustomers, 1)
		assert.Contains(t, results.SuccessfulCustomers, "customer_4")
	})

	t.Run("Customer payment method has address", func(t *testing.T) {
		mockStripeClient := stripelib.NewMockStripeClient()
		authServicer.stripeClient = mockStripeClient

		// Customer has an address
		mockStripeClient.Customers["<EMAIL>"] = &stripe.Customer{
			ID:    "stripe_customer_1",
			Email: "<EMAIL>",
		}

		// Customer has a payment method
		mockStripeClient.CustomerPaymentMethods["stripe_customer_1"] = []*stripe.PaymentMethod{
			{
				ID: "pm_123",
				BillingDetails: &stripe.PaymentMethodBillingDetails{
					Address: &stripe.Address{
						Line1:      "123 Main St",
						Line2:      "Apt 4B",
						City:       "San Francisco",
						State:      "CA",
						PostalCode: "94105",
						Country:    "US",
					},
				},
			},
		}

		results := &BackfillAddressResults{}
		err := authServicer.syncCustomerAddress(validCtx, orb.OrbCustomerInfo{
			OrbCustomerID:    "customer_5",
			StripeCustomerID: "stripe_customer_1",
		}, true, results)
		require.NoError(t, err)
		assert.Len(t, results.SuccessfulCustomers, 1)
		assert.Contains(t, results.SuccessfulCustomers, "customer_5")
	})

	t.Run("Customer has no payment methods", func(t *testing.T) {
		mockStripeClient := stripelib.NewMockStripeClient()
		authServicer.stripeClient = mockStripeClient

		// Create customer
		mockStripeClient.Customers["<EMAIL>"] = &stripe.Customer{
			ID:    "stripe_customer_1",
			Email: "<EMAIL>",
		}

		results := &BackfillAddressResults{}
		err := authServicer.syncCustomerAddress(validCtx, orb.OrbCustomerInfo{
			OrbCustomerID:    "customer_6",
			StripeCustomerID: "stripe_customer_1",
		}, true, results)
		require.NoError(t, err)
		assert.Len(t, results.NoStripeAddressCustomers, 1)
		assert.Contains(t, results.NoStripeAddressCustomers, "customer_6")
	})

	t.Run("Customer has multiple payment methods, use most recent", func(t *testing.T) {
		mockStripeClient := stripelib.NewMockStripeClient()
		authServicer.stripeClient = mockStripeClient
		mockOrbClient := orb.NewMockOrbClient()
		authServicer.orbClient = mockOrbClient

		// Create customer
		mockStripeClient.Customers["<EMAIL>"] = &stripe.Customer{
			ID:    "stripe_customer_1",
			Email: "<EMAIL>",
		}

		// Set two payment methods
		mockStripeClient.CustomerPaymentMethods["stripe_customer_1"] = []*stripe.PaymentMethod{
			{
				ID:      "pm_123",
				Created: 10,
				BillingDetails: &stripe.PaymentMethodBillingDetails{
					Address: &stripe.Address{
						Line1:      "123 Main St",
						Line2:      "Apt 4B",
						City:       "San Francisco",
						State:      "CA",
						PostalCode: "94105",
						Country:    "US",
					},
				},
			},
			{
				ID:      "pm_456",
				Created: 20,
				BillingDetails: &stripe.PaymentMethodBillingDetails{
					Address: &stripe.Address{
						Line1:      "456 Main St",
						Line2:      "Apt 4B",
						City:       "San Francisco",
						State:      "CA",
						PostalCode: "94105",
						Country:    "US",
					},
				},
			},
		}

		// Mock Orb client to return the most recent address
		mockOrbClient.On("UpdateCustomerInformation", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(nil).Once()

		results := &BackfillAddressResults{}
		err := authServicer.syncCustomerAddress(validCtx, orb.OrbCustomerInfo{
			OrbCustomerID:    "customer_7",
			StripeCustomerID: "stripe_customer_1",
		}, false, results)
		require.NoError(t, err)
		assert.Len(t, results.SuccessfulCustomers, 1)
		assert.Contains(t, results.SuccessfulCustomers, "customer_7")
		mockOrbClient.AssertNumberOfCalls(t, "UpdateCustomerInformation", 1)
		// Assert called with the most recent address
		mockOrbClient.AssertCalled(t, "UpdateCustomerInformation",
			mock.Anything,
			mock.Anything,
			mock.MatchedBy(func(updateInfo orb.OrbCustomerUpdateInfo) bool {
				return updateInfo.Address.Line1 == "456 Main St" // Most recent payment method
			}),
			mock.Anything)
	})

	t.Run("Dry run makes no changes", func(t *testing.T) {
		mockStripeClient := stripelib.NewMockStripeClient()
		authServicer.stripeClient = mockStripeClient
		mockOrbClient := orb.NewMockOrbClient()
		authServicer.orbClient = mockOrbClient

		// Create customer
		mockStripeClient.Customers["<EMAIL>"] = &stripe.Customer{
			ID:    "stripe_customer_1",
			Email: "<EMAIL>",
		}

		// Customer has a payment method
		mockStripeClient.CustomerPaymentMethods["stripe_customer_1"] = []*stripe.PaymentMethod{
			{
				ID: "pm_123",
				BillingDetails: &stripe.PaymentMethodBillingDetails{
					Address: &stripe.Address{
						Line1:      "123 Main St",
						Line2:      "Apt 4B",
						City:       "San Francisco",
						State:      "CA",
						PostalCode: "94105",
						Country:    "US",
					},
				},
			},
		}

		results := &BackfillAddressResults{}
		err := authServicer.syncCustomerAddress(validCtx, orb.OrbCustomerInfo{
			OrbCustomerID:    "customer_8",
			StripeCustomerID: "stripe_customer_1",
		}, true, results)
		require.NoError(t, err)
		assert.Len(t, results.SuccessfulCustomers, 1)
		assert.Contains(t, results.SuccessfulCustomers, "customer_8")
	})
}

func setupCustomersForBackfillTest(mockOrbClient *orb.MockOrbClient, mockStripeClient *stripelib.MockStripeClient) {
	// clear previous callss
	mockOrbClient.ExpectedCalls = nil
	mockOrbClient.Calls = nil
	mockOrbClient.On("ListCustomers", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(&orb.OrbCustomersPage{
		Customers: []orb.OrbCustomerInfo{
			{
				OrbCustomerID:    "customer_1",
				StripeCustomerID: "stripe_customer_1",
				Email:            "<EMAIL>",
			},
			{
				OrbCustomerID:    "customer_2",
				StripeCustomerID: "stripe_customer_2",
				Email:            "<EMAIL>",
			},
		},
		HasMore: false,
	}, nil).Once()
	mockOrbClient.On("UpdateCustomerInformation", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(nil)
	mockOrbClient.On("GetCustomerInfo", mock.Anything, "customer_1").Return(
		&orb.OrbCustomerInfo{
			OrbCustomerID:    "customer_1",
			StripeCustomerID: "stripe_customer_1",
			Email:            "<EMAIL>",
		}, nil)
	mockOrbClient.On("GetCustomerInfo", mock.Anything, "customer_2").Return(
		&orb.OrbCustomerInfo{
			OrbCustomerID:    "customer_2",
			StripeCustomerID: "stripe_customer_2",
			Email:            "<EMAIL>",
		}, nil)

	mockStripeClient.Customers["<EMAIL>"] = &stripe.Customer{
		ID:    "stripe_customer_1",
		Email: "<EMAIL>",
		Address: &stripe.Address{
			Line1:      "123 Main St",
			Line2:      "Apt 4B",
			City:       "San Francisco",
			State:      "CA",
			PostalCode: "94105",
			Country:    "US",
		},
	}
	mockStripeClient.Customers["<EMAIL>"] = &stripe.Customer{
		ID:    "stripe_customer_2",
		Email: "<EMAIL>",
		Address: &stripe.Address{
			Line1:      "456 Main St",
			Line2:      "Apt 4B",
			City:       "San Francisco",
			State:      "CA",
			PostalCode: "94105",
			Country:    "US",
		},
	}
}

func TestBackfillAddresses(t *testing.T) {
	bigtableFixture := NewBigtableFixture(t)
	defer bigtableFixture.Cleanup()

	sut := createAuthServicerSUT(t, bigtableFixture)

	// Set up mock orb client
	mockOrbClient := orb.NewMockOrbClient()
	authServicer := sut.authServicer
	authServicer.orbClient = mockOrbClient
	validClaims := &auth.AugmentClaims{
		TenantID:         "",
		TenantName:       "",
		UserID:           "<EMAIL>",
		OpaqueUserID:     "iap:<EMAIL>",
		OpaqueUserIDType: auth_entities.UserId_INTERNAL_IAP.String(),
		Scope:            []string{tokenscopesproto.Scope_AUTH_ADMIN.String()},
	}
	validCtx := validClaims.NewContext(context.Background())

	t.Run("Auth checks", func(t *testing.T) {
		// Test with insufficient permissions (Auth_RW instead of Auth_ADMIN)
		claims := &auth.AugmentClaims{
			TenantID:         "",
			TenantName:       "",
			UserID:           "<EMAIL>",
			OpaqueUserID:     "<EMAIL>",
			OpaqueUserIDType: auth_entities.UserId_INTERNAL_IAP.String(),
			Scope:            []string{tokenscopesproto.Scope_AUTH_RW.String()},
		}
		ctx := claims.NewContext(context.Background())
		_, err := authServicer.BackfillAddresses(ctx, &authpb.BackfillAddressesRequest{})
		require.Error(t, err)
		require.Equal(t, codes.PermissionDenied, status.Code(err))
	})

	t.Run("Max users", func(t *testing.T) {
		_, err := authServicer.BackfillAddresses(validCtx, &authpb.BackfillAddressesRequest{
			MaxUsers: 0,
		})
		require.Error(t, err)
		require.Equal(t, codes.FailedPrecondition, status.Code(err))
	})

	t.Run("Users and other params mismatch", func(t *testing.T) {
		_, err := authServicer.BackfillAddresses(validCtx, &authpb.BackfillAddressesRequest{
			MaxUsers:       1,
			OrbCustomerIds: []string{"customer_1", "customer_2"},
		})
		require.Error(t, err)
		require.Equal(t, codes.FailedPrecondition, status.Code(err))

		_, err = authServicer.BackfillAddresses(validCtx, &authpb.BackfillAddressesRequest{
			MaxUsers: 100,
			CustomerCreatedBeforeTime: &timestamppb.Timestamp{
				Seconds: 100,
			},
			OrbCustomerIds: []string{"customer_1", "customer_2"},
		})
		require.Error(t, err)
		require.Equal(t, codes.FailedPrecondition, status.Code(err))
	})

	t.Run("Dry Run", func(t *testing.T) {
		setupCustomersForBackfillTest(mockOrbClient, authServicer.stripeClient.(*stripelib.MockStripeClient))

		resp, err := authServicer.BackfillAddresses(validCtx, &authpb.BackfillAddressesRequest{
			MaxUsers: 100,
			DryRun:   true,
		})
		require.NoError(t, err)
		require.NotNil(t, resp)
		assert.Len(t, resp.SuccessfulCustomerIds, 2)
		assert.Len(t, resp.FailedCustomerIds, 0)
		assert.Len(t, resp.NoStripeAddressCustomerIds, 0)
		assert.Len(t, resp.UnchangedCustomerIds, 0)
		mockOrbClient.AssertNotCalled(t, "UpdateCustomerInformation", mock.Anything, mock.Anything, mock.Anything, mock.Anything)
	})

	t.Run("Make changes (not dry run)", func(t *testing.T) {
		setupCustomersForBackfillTest(mockOrbClient, authServicer.stripeClient.(*stripelib.MockStripeClient))

		resp, err := authServicer.BackfillAddresses(validCtx, &authpb.BackfillAddressesRequest{
			MaxUsers: 100,
			DryRun:   false,
		})
		require.NoError(t, err)
		require.NotNil(t, resp)
		assert.Len(t, resp.SuccessfulCustomerIds, 2)
		assert.Len(t, resp.FailedCustomerIds, 0)
		assert.Len(t, resp.NoStripeAddressCustomerIds, 0)
		assert.Len(t, resp.UnchangedCustomerIds, 0)
		mockOrbClient.AssertNumberOfCalls(t, "UpdateCustomerInformation", 2)
	})

	t.Run("Specific IDs passed in, dry run", func(t *testing.T) {
		setupCustomersForBackfillTest(mockOrbClient, authServicer.stripeClient.(*stripelib.MockStripeClient))

		resp, err := authServicer.BackfillAddresses(validCtx, &authpb.BackfillAddressesRequest{
			MaxUsers:       100,
			DryRun:         true,
			OrbCustomerIds: []string{"customer_1", "customer_2"},
		})
		require.NoError(t, err)
		require.NotNil(t, resp)
		assert.Len(t, resp.SuccessfulCustomerIds, 2)
		assert.Len(t, resp.FailedCustomerIds, 0)
		assert.Len(t, resp.NoStripeAddressCustomerIds, 0)
		assert.Len(t, resp.UnchangedCustomerIds, 0)
		mockOrbClient.AssertNotCalled(t, "UpdateCustomerInformation", mock.Anything, mock.Anything, mock.Anything, mock.Anything)
	})

	t.Run("Specific IDs passed in", func(t *testing.T) {
		setupCustomersForBackfillTest(mockOrbClient, authServicer.stripeClient.(*stripelib.MockStripeClient))

		resp, err := authServicer.BackfillAddresses(validCtx, &authpb.BackfillAddressesRequest{
			MaxUsers:       100,
			DryRun:         false,
			OrbCustomerIds: []string{"customer_1"},
		})
		require.NoError(t, err)
		require.NotNil(t, resp)
		assert.Len(t, resp.SuccessfulCustomerIds, 1)
		assert.Len(t, resp.FailedCustomerIds, 0)
		assert.Len(t, resp.NoStripeAddressCustomerIds, 0)
		assert.Len(t, resp.UnchangedCustomerIds, 0)
		mockOrbClient.AssertNumberOfCalls(t, "UpdateCustomerInformation", 1)
	})
}

func TestTriggerOrbSubscriptionEvent(t *testing.T) {
	bigtableFixture := NewBigtableFixture(t)
	defer bigtableFixture.Cleanup()

	sut := createAuthServicerSUT(t, bigtableFixture)
	authServicer := sut.authServicer
	daoFactory := sut.daoFactory
	mockBillingEventProcessor := authServicer.billingEventProcessor.(*MockBillingEventProcessor)
	subscriptionDAO := daoFactory.GetSubscriptionDAO()

	// Create IAP user context
	validClaims := &auth.AugmentClaims{
		TenantID:         "",
		TenantName:       "",
		UserID:           "<EMAIL>",
		OpaqueUserID:     "iap:<EMAIL>",
		OpaqueUserIDType: auth_entities.UserId_INTERNAL_IAP.String(),
		Scope:            []string{tokenscopesproto.Scope_AUTH_ADMIN.String()},
	}
	ctx := validClaims.NewContext(context.Background())

	// Create a test subscription
	subscriptionId := "test-subscription-id"
	orbCustomerId := "test-orb-customer-id"
	subscription := &auth_entities.Subscription{
		SubscriptionId: subscriptionId,
		OrbCustomerId:  orbCustomerId,
		OrbStatus:      auth_entities.Subscription_ORB_STATUS_ACTIVE,
	}

	_, err := subscriptionDAO.Create(ctx, subscription)
	require.NoError(t, err)

	t.Run("successful processing", func(t *testing.T) {
		// Set up mock expectation for successful processing
		mockBillingEventProcessor.On("ProcessOrbEvent", mock.Anything, mock.MatchedBy(func(event *orb_event.OrbEvent) bool {
			return event.EventType == "subscription.manual_update_triggered" &&
				event.OrbCustomerId != nil && *event.OrbCustomerId == orbCustomerId
		})).Return(nil).Once()

		req := &authpb.TriggerOrbSubscriptionEventRequest{
			SubscriptionId: subscriptionId,
			OrbCustomerId:  orbCustomerId,
		}

		response, err := authServicer.TriggerOrbSubscriptionEvent(ctx, req)
		require.NoError(t, err)
		require.NotNil(t, response)
		assert.NotNil(t, response.Subscription)
		assert.Equal(t, subscriptionId, response.Subscription.SubscriptionId)

		// Verify that ProcessOrbEvent was called with the expected parameters
		mockBillingEventProcessor.AssertExpectations(t)
	})

	t.Run("validates required parameters", func(t *testing.T) {
		// Test missing subscription_id
		req := &authpb.TriggerOrbSubscriptionEventRequest{
			OrbCustomerId: orbCustomerId,
		}
		_, err := authServicer.TriggerOrbSubscriptionEvent(ctx, req)
		require.Error(t, err)
		assert.Equal(t, codes.InvalidArgument, status.Code(err))
		assert.Contains(t, err.Error(), "subscription_id is required")

		// Test missing orb_customer_id
		req2 := &authpb.TriggerOrbSubscriptionEventRequest{
			SubscriptionId: subscriptionId,
		}
		_, err = authServicer.TriggerOrbSubscriptionEvent(ctx, req2)
		require.Error(t, err)
		assert.Equal(t, codes.InvalidArgument, status.Code(err))
		assert.Contains(t, err.Error(), "orb_customer_id is required")
	})

	t.Run("requires admin authentication", func(t *testing.T) {
		// Test with non-admin scope
		nonAdminClaims := &auth.AugmentClaims{
			TenantID:         "",
			TenantName:       "",
			UserID:           "<EMAIL>",
			OpaqueUserID:     "iap:<EMAIL>",
			OpaqueUserIDType: auth_entities.UserId_INTERNAL_IAP.String(),
			Scope:            []string{tokenscopesproto.Scope_AUTH_RW.String()},
		}
		nonAdminCtx := nonAdminClaims.NewContext(context.Background())

		req := &authpb.TriggerOrbSubscriptionEventRequest{
			SubscriptionId: subscriptionId,
			OrbCustomerId:  orbCustomerId,
		}

		_, err := authServicer.TriggerOrbSubscriptionEvent(nonAdminCtx, req)
		require.Error(t, err)
		assert.Contains(t, err.Error(), "Access denied")
	})
}
