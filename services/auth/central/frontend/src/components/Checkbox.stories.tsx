import { fn } from "@storybook/test";
import type { <PERSON><PERSON>, StoryObj, Decorator } from "@storybook/react";
import { Checkbox } from "./Checkbox";

const meta = {
  title: "Components/Checkbox",
  component: Checkbox,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  argTypes: {
    onChange: { action: "changed" },
  },
} satisfies Meta<typeof Checkbox>;

export default meta;
type Story = StoryObj<typeof meta>;

// Default checkbox story
export const Default: Story = {
  args: {
    label: "Sample Label",
    checked: false,
    disabled: false,
    onChange: fn(),
  },
};

export const WithLink: Story = {
  args: {
    label: (
      <>
        I agree to the{" "}
        <a href="#" className="">
          terms of service
        </a>
      </>
    ),
    checked: false,
    disabled: false,
    onChange: fn(),
  },
};

export const Disabled: Story = {
  args: {
    label: "This checkbox is disabled",
    checked: false,
    disabled: true,
    onChange: fn(),
  },
};
