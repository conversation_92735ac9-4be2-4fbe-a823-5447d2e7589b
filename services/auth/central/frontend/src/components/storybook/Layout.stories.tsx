import { useState, useEffect } from "react";
import type { <PERSON>a, StoryObj } from "@storybook/react";

import { Layout } from "../Layout";
import { LoadingPage } from "../../pages/LoadingPage";
import { TermsAcceptancePage } from "../../pages/TermsAcceptancePage";
import { ErrorPage } from "../../pages/ErrorPage";

const meta: Meta<typeof Layout> = {
  title: "Components/Layout",
  component: Layout,
  parameters: {
    layout: "fullscreen",
  },
  argTypes: {
    transitionDelay: {
      control: { type: "range", min: 0.5, max: 5, step: 0.5 },
      defaultValue: 2,
      description: "Delay between page transitions in seconds",
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

// Component that cycles through different pages to demonstrate transitions
function PageTransitionDemo({
  transitionDelay = 2,
}: {
  transitionDelay?: number;
}) {
  const [currentPage, setCurrentPage] = useState(0);

  // Pages to cycle through
  const pages = [
    {
      key: "loading",
      component: <LoadingPage message="Checking authentication status..." />,
    },
    {
      key: "terms",
      component: (
        <TermsAcceptancePage
          oauthParams={{
            client_id: "test-client",
            redirect_uri: "https://app.example.com/callback",
            state: "test-state",
            code_challenge: "test-challenge",
          }}
          termsUrl="https://example.com/terms"
          termsRevision="1.0"
        />
      ),
    },
    {
      key: "error",
      component: (
        <ErrorPage
          message="An error occurred during authentication"
          supportInfo={{ timestamp: new Date().toISOString() }}
        />
      ),
    },
  ];

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentPage((prev) => (prev + 1) % pages.length);
    }, transitionDelay * 1000);

    return () => clearInterval(interval);
  }, [transitionDelay, pages.length]);

  // The Layout component now handles AnimatePresence internally
  // We just need to render the current page component
  return pages[currentPage].component;
}

export const Default: Story = {
  render: () => <PageTransitionDemo />,
  parameters: {
    docs: {
      description: {
        story:
          "Demonstrates the default page transition animation cycling through Loading → Terms Acceptance → Error pages with a 2-second delay between transitions.",
      },
    },
  },
};

export const SinglePageTransition: Story = {
  render: () => {
    const [showError, setShowError] = useState(false);

    return (
      <>
        <div className="fixed top-4 right-4 z-50">
          <button
            onClick={() => setShowError(!showError)}
            className="px-4 py-2 bg-aug-primary text-white rounded-md hover:bg-aug-primary-dark transition-colors"
          >
            Toggle Page
          </button>
        </div>
        {showError ? (
          <ErrorPage
            message="Authentication failed"
            supportInfo={{ timestamp: new Date().toISOString() }}
          />
        ) : (
          <LoadingPage message="Loading application..." />
        )}
      </>
    );
  },
  parameters: {
    docs: {
      description: {
        story:
          "Manual page transition control. Click the 'Toggle Page' button to switch between Loading and Error pages and observe the smooth transition animation.",
      },
    },
  },
};
