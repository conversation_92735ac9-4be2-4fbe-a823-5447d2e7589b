@import url("https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Berkeley+Mono&display=swap");
@import "tailwindcss";

/* Augmentcode.com Design System Colors */
:root {
  /* Core Colors */
  --aug-black: oklch(0 0 0);
  --aug-white: oklch(1 0 0);
  --aug-swiss-white: oklch(0.98 0.0011 17.18);
  --aug-swiss-gray-light: oklch(0.89 0.0026 48.72);

  /* Neutral Grays */
  --aug-neutral-gray: oklch(0.36 0.0088 219.71);
  --aug-neutral-gray-light: oklch(0.7 0.0088 219.71);
  --aug-neutral-gray-medium: oklch(0.32 0 0);
  --aug-neutral-gray-dark: oklch(0.2 0.0122 237.44);

  /* Silver/Metallic (Primary) */
  --aug-metallic: oklch(0.83 0.0885 62.78);
  --aug-metallic-dark: oklch(0.37 0.0341 62.91);

  /* Accent Colors */
  --aug-lavender: oklch(70% 0.164328 285.3);
  --aug-lavender-new: oklch(67.8% 0.158243 277.3);
  --aug-lavender-dark-mode: oklch(0.8317 0.0891 288.59);

  /* Dark Theme Specific */
  --aug-background: var(--aug-black);
  --aug-foreground: var(--aug-swiss-white);
  --aug-foreground-muted: var(--aug-swiss-gray-light);
  --aug-border: oklch(1 0 0 / 50%);
  --aug-border-muted: oklch(1 0 0 / 20%);
  --aug-border-accent: var(--aug-lavender);

  /* Component Colors */
  --aug-card-bg: oklch(0.205 0 0);
  --aug-button-bg: oklch(0.269 0 0);
  --aug-button-hover: oklch(0.32 0 0);
  --aug-input-bg: oklch(1 0 0 / 15%);

  /* Legacy mappings for compatibility */
  --aug-primary: var(--aug-metallic);
  --aug-primary-dark: var(--aug-metallic-dark);
  --aug-bg-dark: var(--aug-background);
  --aug-bg-dark-secondary: var(--aug-card-bg);
  --aug-text-light: var(--aug-foreground);
  --aug-text-light-secondary: var(--aug-foreground-muted);
  --aug-text-light-tertiary: var(--aug-neutral-gray-light);
  --aug-border-dark: var(--aug-border);
}

/* Tailwind CSS custom colors */
@theme {
  /* Font families */
  --font-family-sans: "Inter", system-ui, -apple-system, BlinkMacSystemFont,
    "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
  --font-family-mono: "Berkeley Mono", "SF Mono", "Monaco", "Inconsolata",
    "Fira Code", "Fira Mono", "Droid Sans Mono", "Courier New", monospace;

  /* Primary Colors - Silver/Metallic */
  --color-aug-primary: var(--aug-metallic);
  --color-aug-primary-dark: var(--aug-metallic-dark);

  /* Neutral Colors */
  --color-aug-white: var(--aug-white);
  --color-aug-swiss-white: var(--aug-swiss-white);
  --color-aug-gray-light: var(--aug-swiss-gray-light);
  --color-aug-gray: var(--aug-neutral-gray-light);
  --color-aug-gray-dark: var(--aug-neutral-gray-dark);
  --color-aug-black: var(--aug-black);

  /* Dark Theme Colors */
  --color-aug-bg-dark: var(--aug-background);
  --color-aug-bg-dark-secondary: var(--aug-card-bg);
  --color-aug-text-light: var(--aug-foreground);
  --color-aug-text-light-secondary: var(--aug-foreground-muted);
  --color-aug-text-light-tertiary: var(--aug-neutral-gray-light);
  --color-aug-border-dark: var(--aug-border);
  --color-aug-border-muted: var(--aug-border-muted);

  /* Component Colors */
  --color-aug-card-bg: var(--aug-card-bg);
  --color-aug-button-dark: var(--aug-button-bg);
  --color-aug-button-dark-hover: var(--aug-button-hover);

  /* Accent Colors */
  --color-aug-lavender: var(--aug-lavender);
  --color-aug-lavender-new: var(--aug-lavender-new);
  --color-aug-lavender-dark-mode: var(--aug-lavender-dark-mode);

  /* Default text color */
  --color-aug: var(--aug-text-light-secondary);
}

/* Global styles */
body {
  margin: 0;
  font-family:
    "Inter",
    -apple-system,
    BlinkMacSystemFont,
    "Segoe UI",
    "Roboto",
    "Oxygen",
    "Ubuntu",
    "Cantarell",
    "Fira Sans",
    "Droid Sans",
    "Helvetica Neue",
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: var(--aug-bg-dark);
  color: var(--aug-text-light);
}

/* Monospace font for code elements */
code,
pre,
kbd,
samp {
  font-family: "Berkeley Mono", "SF Mono", "Monaco", "Inconsolata", "Fira Code",
    "Fira Mono", "Droid Sans Mono", "Courier New", monospace;
}

a {
  @apply text-aug-text-light-secondary hover:text-aug-text-light no-underline transition-colors;
}

/* Checkbox link styling - more specific to override default anchor styles */
.checkbox-link {
  color: var(--aug-lavender-dark-mode) !important;
  text-decoration: underline !important;
  text-underline-offset: 2px !important;
  font-weight: 500 !important;
  transition: color 0.15s ease-in-out !important;
}

.checkbox-link:hover {
  color: var(--aug-lavender) !important;
}

/* Utility classes from AugmentCode.com */
@layer utilities {
  /* Silver gradient text effect */
  .silver-text {
    background: linear-gradient(
      0deg,
      #fafafa -127.68%,
      #d8d5d3 13.31%,
      rgba(255, 255, 255, 0) 193.28%
    );
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }

  /* Silver gradient background */
  .silver-gradient {
    background: linear-gradient(
      0deg,
      #fafafa -127.68%,
      #d8d5d3 8.11%,
      rgba(255, 255, 255, 0) 193.28%
    );
  }

  /* Metallic border gradient */
  .border-metallic {
    border: transparent;
    background: linear-gradient(
      to right,
      var(--aug-metallic-dark),
      var(--aug-metallic),
      var(--aug-metallic-dark)
    );
    padding: 1px;
  }

  /* Subtle glow effect */
  .shadow-glow {
    box-shadow: 0 0 9.96px 0 rgba(197, 191, 255, 0.29);
  }

  /* Premium button style */
  .button-premium {
    background: linear-gradient(to bottom, oklch(0.32 0 0), oklch(0.269 0 0));
    border: 1px solid oklch(1 0 0 / 30%);
    box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  }

  .button-premium:hover {
    background: linear-gradient(to bottom, oklch(0.35 0 0), oklch(0.3 0 0));
    border-color: oklch(1 0 0 / 50%);
    box-shadow:
      0 4px 6px -1px rgb(0 0 0 / 0.1),
      0 2px 4px -2px rgb(0 0 0 / 0.1);
  }
}
