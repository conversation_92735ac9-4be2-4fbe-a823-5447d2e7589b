load("@rules_jsonnet//jsonnet:jsonnet.bzl", "jsonnet_library")
load("//tools/bzl:go.bzl", "go_binary", "go_library", "go_oci_image", "go_test")
load("//tools/bzl:kubecfg.bzl", "kubecfg")
load("//tools/bzl:metadata.bzl", "metadata_test")

go_library(
    name = "server_lib",
    srcs = [
        "main.go",
        "plan_comparator.go",
        "plans_validator.go",
    ],
    importpath = "github.com/augmentcode/augment/services/billing/central",
    visibility = ["//visibility:private"],
    deps = [
        "//base/feature_flags:feature_flags_go",
        "//base/go/secretstring:secretstring_go",
        "//base/logging:logging_go",
        "//base/tracing/go:tracing_go",
        "//services/auth/central/client:auth_client_go",
        "//services/auth/central/server:auth_entities_go_proto",
        "//services/auth/central/server:auth_go_grpc",
        "//services/billing/lib/orb:orb_lib",
        "//services/billing/lib/orb/config",
        "//services/lib/grpc/auth:grpc_auth_go",
        "//services/lib/grpc/recovery:grpc_recovery_go",
        "//services/lib/grpc/tls_config:grpc_tls_config_go",
        "//services/lib/request_context:request_context_go",
        "//services/token_exchange:token_exchange_go_proto",
        "//services/token_exchange/client:client_go",
        "@com_github_google_go_cmp//cmp",
        "@com_github_google_go_cmp//cmp/cmpopts",
        "@com_github_google_uuid//:uuid",
        "@com_github_grpc_ecosystem_go_grpc_middleware_providers_prometheus//:prometheus",
        "@com_github_orbcorp_orb_go//:orb-go",
        "@com_github_prometheus_client_golang//prometheus",
        "@com_github_prometheus_client_golang//prometheus/promauto",
        "@com_github_prometheus_client_golang//prometheus/promhttp",
        "@com_github_rs_zerolog//log",
        "@com_github_stripe_stripe_go_v80//:go_default_library",
        "@com_github_stripe_stripe_go_v80//customer",
        "@com_github_stripe_stripe_go_v80//subscription",
        "@io_opentelemetry_go_contrib_instrumentation_google_golang_org_grpc_otelgrpc//:otelgrpc",
        "@org_golang_google_api//option",
        "@org_golang_google_grpc//:grpc",
        "@org_golang_google_grpc//health",
        "@org_golang_google_grpc//health/grpc_health_v1",
        "@org_golang_google_grpc//reflection",
        "@org_golang_google_protobuf//proto",
        "@org_uber_go_automaxprocs//:automaxprocs",
    ],
)

go_binary(
    name = "server",
    embed = [":server_lib"],
)

go_oci_image(
    name = "image",
    package_name = package_name(),
    binary = ":server",
)

kubecfg(
    name = "kubecfg",
    src = "deploy.jsonnet",
    data = [
        ":image",
    ],
    visibility = [
        "//services/deploy:__subpackages__",
    ],
    deps = [
        "//deploy/common:cert-lib",
        "//deploy/common:cloud_info",
        "//deploy/common:config-map-lib",
        "//deploy/common:dynamic-feature-flags-lib",
        "//deploy/common:grpc-lib",
        "//deploy/common:lib",
        "//deploy/common:node-lib",
        "//deploy/common:telemetry-lib",
        "//deploy/gcp:gcp-lib",
        "//services/billing/lib/orb:orb_jsonnet",
        "//services/deploy:endpoints",
    ],
)

metadata_test(
    name = "metadata_test",
    src = "METADATA.jsonnet",
    deps = [
        ":kubecfg",
    ],
)

go_test(
    name = "plan_comparator_test_go",
    srcs = [
        "plan_comparator_test.go",
    ],
    embed = [":server_lib"],
    deps = [
        "//services/billing/lib/orb:orb_lib",
        "//services/billing/lib/orb/config",
        "@com_github_orbcorp_orb_go//:orb-go",
    ],
)

go_test(
    name = "plans_validator_test_go",
    srcs = [
        "plans_validator_test.go",
    ],
    embed = [":server_lib"],
    deps = [
        "//services/billing/lib/orb:orb_lib",
        "//services/billing/lib/orb/config",
        "@com_github_orbcorp_orb_go//:orb-go",
        "@com_github_stretchr_testify//assert",
        "@com_github_stretchr_testify//mock",
        "@com_github_stretchr_testify//require",
    ],
)
