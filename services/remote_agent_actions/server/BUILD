load("@rules_proto//proto:defs.bzl", "proto_library")
load("//tools/bzl:go.bzl", "go_binary", "go_grpc_library", "go_library", "go_oci_image", "go_test")
load("//tools/bzl:kubecfg.bzl", "kubecfg")
load("//tools/bzl:metadata.bzl", "metadata_test")

proto_library(
    name = "trigger_entities_proto",
    srcs = ["trigger_entities.proto"],
    deps = [
        "//services/remote_agent_actions:remote_agent_actions_proto",
        "//services/remote_agents:remote_agents_proto",
        "@protobuf//:timestamp_proto",
    ],
)

go_grpc_library(
    name = "trigger_entities_go_proto",
    importpath = "github.com/augmentcode/augment/services/remote_agent_actions/server/trigger_entities_proto",
    proto = ":trigger_entities_proto",
    visibility = ["//services/remote_agent_actions:__subpackages__"],
    deps = [
        "//services/remote_agent_actions:remote_agent_actions_go_proto",
        "//services/remote_agents:remote_agents_go_proto",
    ],
)

go_library(
    name = "server_lib",
    srcs = [
        "auth_helpers.go",
        "cron_evaluator.go",
        "cron_task.go",
        "github_entity_convert.go",
        "github_entity_matcher.go",
        "llm_title_generator.go",
        "main.go",
        "schedule_trigger_task.go",
        "scheduler_storage.go",
        "singleton_task_manager.go",
        "trigger_adapter.go",
        "trigger_helpers.go",
        "trigger_service.go",
        "trigger_storage.go",
    ],
    importpath = "github.com/augmentcode/augment/services/remote_agent_actions/server",
    visibility = ["//visibility:private"],
    deps = [
        "//base/blob_names:blob_names_go_proto",
        "//base/feature_flags:feature_flags_go",
        "//base/go/secretstring:secretstring_go",
        "//base/logging:logging_go",
        "//base/tracing/go:tracing_go",
        "//services/api_proxy:public_api_go_proto",
        "//services/auth/central/client:auth_client_go",
        "//services/auth/central/server:auth_entities_go_proto",
        "//services/auth/central/server:auth_go_grpc",
        "//services/auth/central/server:front_end_token_service_go_grpc",
        "//services/bigtable_proxy:bigtable_proxy_go_proto",
        "//services/bigtable_proxy/client:client_go",
        "//services/chat_host:chat_host_go_proto",
        "//services/chat_host/client/go",
        "//services/integrations/github:github_event_go_proto",
        "//services/integrations/github/processor/client:client_go",
        "//services/integrations/linear/client_go",
        "//services/lib/grpc/auth:grpc_auth_go",
        "//services/lib/grpc/metrics:grpc_metrics_go",
        "//services/lib/grpc/recovery:grpc_recovery_go",
        "//services/lib/grpc/tls_config:grpc_tls_config_go",
        "//services/lib/request_context:request_context_go",
        "//services/remote_agent_actions:remote_agent_actions_go_proto",
        "//services/remote_agent_actions/server:trigger_entities_go_proto",
        "//services/remote_agent_actions/server/integrations/github",
        "//services/remote_agent_actions/server/integrations/linear",
        "//services/remote_agents:remote_agents_go_proto",
        "//services/remote_agents/client:client_go",
        "//services/request_insight:request_insight_go_proto",
        "//services/request_insight/publisher:publisher_go",
        "//services/tenant_watcher/client:client_go",
        "//services/token_exchange:token_scopes_go_proto",
        "//services/token_exchange/client:client_go",
        "@com_github_golang_jwt_jwt_v5//:jwt",
        "@com_github_google_uuid//:uuid",
        "@com_github_grpc_ecosystem_go_grpc_middleware_providers_prometheus//:prometheus",
        "@com_github_prometheus_client_golang//prometheus",
        "@com_github_prometheus_client_golang//prometheus/promauto",
        "@com_github_prometheus_client_golang//prometheus/promhttp",
        "@com_github_robfig_cron//:go_default_library",
        "@com_github_rs_zerolog//log",
        "@com_google_cloud_go_bigtable//apiv2/bigtablepb:go_default_library",
        "@io_k8s_apimachinery//pkg/apis/meta/v1:meta",
        "@io_k8s_client_go//kubernetes",
        "@io_k8s_client_go//rest",
        "@io_k8s_client_go//tools/leaderelection",
        "@io_k8s_client_go//tools/leaderelection/resourcelock",
        "@io_opentelemetry_go_contrib_instrumentation_google_golang_org_grpc_otelgrpc//:otelgrpc",
        "@org_golang_google_grpc//:grpc",
        "@org_golang_google_grpc//codes",
        "@org_golang_google_grpc//credentials",
        "@org_golang_google_grpc//health",
        "@org_golang_google_grpc//health/grpc_health_v1",
        "@org_golang_google_grpc//reflection",
        "@org_golang_google_grpc//status",
        "@org_golang_google_protobuf//encoding/protojson",
        "@org_golang_google_protobuf//proto",
        "@org_golang_google_protobuf//types/known/structpb",
        "@org_golang_google_protobuf//types/known/timestamppb",
        "@org_uber_go_automaxprocs//:automaxprocs",
    ],
)

go_binary(
    name = "server",
    embed = [":server_lib"],
)

go_oci_image(
    name = "image",
    package_name = package_name(),
    binary = ":server",
    tars = ["//tools/docker:grpc_health_probe_tar"],
)

kubecfg(
    name = "kubecfg",
    src = "deploy.jsonnet",
    data = [
        ":image",
    ],
    visibility = ["//services:__subpackages__"],
    deps = [
        "//deploy/common:cert-lib",
        "//deploy/common:cloud_info",
        "//deploy/common:config-map-lib",
        "//deploy/common:grpc-lib",
        "//deploy/common:lib",
        "//deploy/common:lock-lib",
        "//deploy/common:node-lib",
        "//deploy/common:telemetry-lib",
        "//deploy/gcp:gcp-lib",
        "//services/deploy:endpoints",
        "//services/request_insight/publisher:publisher_lib",
    ],
)

go_test(
    name = "trigger_service_test",
    srcs = ["trigger_service_test.go"],
    embed = [":server_lib"],
    deps = [
        "//services/remote_agent_actions:remote_agent_actions_go_proto",
        "@com_github_stretchr_testify//assert",
        "@com_github_stretchr_testify//mock",
    ],
)

go_test(
    name = "github_entity_matcher_webhook_test",
    srcs = ["github_entity_matcher_webhook_test.go"],
    embed = [":server_lib"],
    deps = [
        "//services/remote_agent_actions:remote_agent_actions_go_proto",
        "@com_github_stretchr_testify//assert",
        "@com_github_stretchr_testify//require",
    ],
)

metadata_test(
    name = "metadata_test",
    src = "METADATA.jsonnet",
    deps = [
        ":kubecfg",
        "//deploy/tenants:namespaces",
    ],
)
