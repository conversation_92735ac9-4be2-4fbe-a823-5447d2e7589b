package main

import (
	"context"
	"fmt"

	bigtableproto "cloud.google.com/go/bigtable/apiv2/bigtablepb"
	bigtableproxy "github.com/augmentcode/augment/services/bigtable_proxy/client"
	bigtableproxyproto "github.com/augmentcode/augment/services/bigtable_proxy/proto"
	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
	triggerentitiesproto "github.com/augmentcode/augment/services/remote_agent_actions/server/trigger_entities_proto"
	"github.com/rs/zerolog/log"
	"google.golang.org/protobuf/proto"
)

const (
	schedulerStateFamilyName = "SchedulerState"
	schedulerStateColumn     = "state"
)

// SchedulerStorage handles scheduler state persistence
type SchedulerStorage struct {
	bigtableProxyClient bigtableproxy.BigtableProxyClient
}

// NewSchedulerStorage creates a new scheduler storage instance
func NewSchedulerStorage(bigtableProxyClient bigtableproxy.BigtableProxyClient) *SchedulerStorage {
	return &SchedulerStorage{
		bigtableProxyClient: bigtableProxyClient,
	}
}

// GetSchedulerState retrieves the scheduler state for a tenant
func (s *SchedulerStorage) GetSchedulerState(ctx context.Context, requestContext *requestcontext.RequestContext, tenantID string) (*triggerentitiesproto.SchedulerState, error) {
	rowKey := fmt.Sprintf("scheduler_state:%s", tenantID)

	response, err := s.bigtableProxyClient.ReadRows(
		ctx,
		tenantID,
		bigtableproxyproto.TableName_REMOTE_AGENTS,
		&bigtableproto.RowSet{
			RowKeys: [][]byte{[]byte(rowKey)},
		},
		nil,
		1,
		requestContext,
	)
	if err != nil {
		return nil, err
	}

	if len(response) == 0 {
		// Return empty scheduler state if not found
		return &triggerentitiesproto.SchedulerState{
			ScheduledTriggers: []*triggerentitiesproto.ScheduledTrigger{},
		}, nil
	}

	// Find the scheduler state data
	var schedulerStateData []byte
	for _, cell := range response[0].Cells {
		if cell.FamilyName == schedulerStateFamilyName && string(cell.Qualifier) == schedulerStateColumn {
			schedulerStateData = cell.Value
			break
		}
	}

	if len(schedulerStateData) == 0 {
		return &triggerentitiesproto.SchedulerState{
			ScheduledTriggers: []*triggerentitiesproto.ScheduledTrigger{},
		}, nil
	}

	var schedulerState triggerentitiesproto.SchedulerState
	if err := proto.Unmarshal(schedulerStateData, &schedulerState); err != nil {
		log.Ctx(ctx).Error().Err(err).Str("tenant_id", tenantID).Msg("Failed to unmarshal scheduler state")
		return nil, err
	}

	return &schedulerState, nil
}

// SaveSchedulerState saves the scheduler state for a tenant
func (s *SchedulerStorage) SaveSchedulerState(ctx context.Context, requestContext *requestcontext.RequestContext, tenantID string, schedulerState *triggerentitiesproto.SchedulerState) error {
	rowKey := fmt.Sprintf("scheduler_state:%s", tenantID)

	schedulerStateData, err := proto.Marshal(schedulerState)
	if err != nil {
		return err
	}

	mutation := &bigtableproto.Mutation{
		Mutation: &bigtableproto.Mutation_SetCell_{
			SetCell: &bigtableproto.Mutation_SetCell{
				FamilyName:      schedulerStateFamilyName,
				ColumnQualifier: []byte(schedulerStateColumn),
				Value:           schedulerStateData,
			},
		},
	}

	entries := []*bigtableproto.MutateRowsRequest_Entry{
		{
			RowKey:    []byte(rowKey),
			Mutations: []*bigtableproto.Mutation{mutation},
		},
	}

	_, err = s.bigtableProxyClient.MutateRows(
		ctx,
		tenantID,
		bigtableproxyproto.TableName_REMOTE_AGENTS,
		entries,
		requestContext,
	)
	return err
}

// GetScheduledTrigger retrieves a specific scheduled trigger from scheduler state
func (s *SchedulerStorage) GetScheduledTrigger(ctx context.Context, requestContext *requestcontext.RequestContext, tenantID, triggerID string) (*triggerentitiesproto.ScheduledTrigger, error) {
	schedulerState, err := s.GetSchedulerState(ctx, requestContext, tenantID)
	if err != nil {
		return nil, err
	}

	for _, scheduledTrigger := range schedulerState.ScheduledTriggers {
		if scheduledTrigger.TriggerId == triggerID {
			return scheduledTrigger, nil
		}
	}

	return nil, nil // Not found
}

// UpdateScheduledTrigger updates or adds a scheduled trigger in the scheduler state
func (s *SchedulerStorage) UpdateScheduledTrigger(ctx context.Context, requestContext *requestcontext.RequestContext, tenantID string, scheduledTrigger *triggerentitiesproto.ScheduledTrigger) error {
	schedulerState, err := s.GetSchedulerState(ctx, requestContext, tenantID)
	if err != nil {
		return err
	}

	// Find and update existing trigger, or add new one
	found := false
	for i, existingTrigger := range schedulerState.ScheduledTriggers {
		if existingTrigger.TriggerId == scheduledTrigger.TriggerId {
			schedulerState.ScheduledTriggers[i] = scheduledTrigger
			found = true
			break
		}
	}

	if !found {
		schedulerState.ScheduledTriggers = append(schedulerState.ScheduledTriggers, scheduledTrigger)
	}

	return s.SaveSchedulerState(ctx, requestContext, tenantID, schedulerState)
}

// RemoveScheduledTrigger removes a scheduled trigger from the scheduler state
func (s *SchedulerStorage) RemoveScheduledTrigger(ctx context.Context, requestContext *requestcontext.RequestContext, tenantID, triggerID string) error {
	schedulerState, err := s.GetSchedulerState(ctx, requestContext, tenantID)
	if err != nil {
		return err
	}

	// Filter out the trigger to remove
	var updatedTriggers []*triggerentitiesproto.ScheduledTrigger
	for _, scheduledTrigger := range schedulerState.ScheduledTriggers {
		if scheduledTrigger.TriggerId != triggerID {
			updatedTriggers = append(updatedTriggers, scheduledTrigger)
		}
	}

	schedulerState.ScheduledTriggers = updatedTriggers
	return s.SaveSchedulerState(ctx, requestContext, tenantID, schedulerState)
}
