load("@crates//:defs.bzl", "aliases", "all_crate_deps")
load("@rules_rust//cargo:defs.bzl", "cargo_build_script")
load("//tools/bzl:go.bzl", "go_library")
load("//tools/bzl:rust.bzl", "rust_library", "rust_test")

rust_library(
    name = "client_rs",
    srcs = ["client.rs"],
    aliases = aliases(),
    crate_name = "remote_agents_client",
    edition = "2021",
    proc_macro_deps = all_crate_deps(
        proc_macro = True,
    ),
    visibility = ["//services:__subpackages__"],
    deps = all_crate_deps(
        normal = True,
    ) + [
        ":proto_gen",
        "//base/blob_names/rust:blob_names",
        "//base/rust/tracing-tonic",
        "//services/lib/grpc/auth:grpc_auth",
        "//services/lib/grpc/client:grpc_client_rs",
        "//services/lib/request_context:request_context_rs",
    ],
)

cargo_build_script(
    name = "proto_gen",
    srcs = [
        "build.rs",
    ],
    aliases = aliases(build = True),
    build_script_env = {
        "PROTOC": "$(execpath @protobuf//:protoc)",
    },
    data = [
        "//base/blob_names:blob_names_proto",
        "//services/chat_host:chat_proto",
        "//services/remote_agents:remote_agents_proto",
        "@protobuf//:protoc",
        "@protobuf//:timestamp_proto",
    ],
    proc_macro_deps = all_crate_deps(
        build_proc_macro = True,
    ),
    deps = all_crate_deps(
        build = True,
    ),
)

rust_test(
    name = "remote_agents_client_test",
    aliases = aliases(
        normal_dev = True,
        proc_macro_dev = True,
    ),
    crate = ":client_rs",
    proc_macro_deps = all_crate_deps(
        proc_macro_dev = True,
    ),
    deps = all_crate_deps(
        normal_dev = True,
    ),
)

go_library(
    name = "client_go",
    srcs = [
        "client.go",
        "mock_client.go",
    ],
    importpath = "github.com/augmentcode/augment/services/remote_agents/client",
    visibility = ["//services:__subpackages__"],
    deps = [
        "//services/lib/grpc/client:grpc_client_go",
        "//services/lib/request_context:request_context_go",
        "//services/remote_agents:remote_agents_go_proto",
        "@com_github_stretchr_testify//mock",
        "@org_golang_google_grpc//:grpc",
        "@org_golang_google_grpc//credentials",
        "@org_golang_google_grpc//credentials/insecure",
        "@org_golang_google_grpc//metadata",
    ],
)
