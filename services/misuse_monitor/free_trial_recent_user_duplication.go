package main

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"cloud.google.com/go/bigquery"
	featureflags "github.com/augmentcode/augment/base/feature_flags"
	authclient "github.com/augmentcode/augment/services/auth/central/client"
	auth_entities "github.com/augmentcode/augment/services/auth/central/server/auth_entities_proto"
	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
	tokenexchange "github.com/augmentcode/augment/services/token_exchange/client"
	tokenscopesproto "github.com/augmentcode/augment/services/token_exchange/token_scopes_proto"
	"github.com/rs/zerolog/log"
	"google.golang.org/api/iterator"
)

// If true, search for users but don't actually suspend them.
var freeTrialRecentUserDuplicationDryRunFlag = featureflags.NewBoolFlag("free_trial_recent_user_duplication_dry_run", true)

type FreeTrialRecentUserDuplicationJob struct {
	bqClient            *bigquery.Client
	datasetName         string
	jobName             string
	authClient          authclient.AuthClient
	tokenExchangeClient tokenexchange.TokenExchangeClient
	featureFlagHandle   featureflags.FeatureFlagHandle
}

// Ensure MisuseMonitorJob implements the Job interface
var _ Job = (*FreeTrialRecentUserDuplicationJob)(nil)

func NewFreeTrialRecentUserDuplicationJob(
	ctx context.Context,
	projectId string,
	datasetName string,
	authClient authclient.AuthClient,
	tokenExchangeClient tokenexchange.TokenExchangeClient,
	featureFlagHandle featureflags.FeatureFlagHandle,
) (*FreeTrialRecentUserDuplicationJob, error) {
	// The BigQuery API doesn't let us parameterize dataset/table names, so we have to inject this
	// into our query with string manipulation. Make sure it doesn't contain contain anything that
	// could be malicious.
	if !CheckDatasetName(datasetName) {
		return nil, fmt.Errorf("Invalid dataset name %s", datasetName)
	}

	bqClient, err := bigquery.NewClient(ctx, projectId)
	if err != nil {
		return nil, fmt.Errorf("error creating bigquery client: %w", err)
	}

	return &FreeTrialRecentUserDuplicationJob{
		bqClient:            bqClient,
		datasetName:         datasetName,
		jobName:             "free-trial-recent-user-duplicate",
		authClient:          authClient,
		tokenExchangeClient: tokenExchangeClient,
		featureFlagHandle:   featureFlagHandle,
	}, nil
}

func (m *FreeTrialRecentUserDuplicationJob) Close() {
	m.bqClient.Close()
}

func (m *FreeTrialRecentUserDuplicationJob) Run(ctx context.Context) error {
	// Get users from BigQuery
	suspects, err := m.getSuspects(ctx)
	if err != nil {
		return fmt.Errorf("error getting malicious users: %w", err)
	}

	log.Info().Msgf("Total of %d users to process", len(suspects))

	// Ban the users
	err = m.suspendSuspects(ctx, suspects)
	if err != nil {
		return fmt.Errorf("error banning users: %w", err)
	}

	return nil
}

type freeTrialRecentUserSuspect struct {
	ID              string    `bigquery:"opaque_user_id"`
	SessionIDs      []string  `bigquery:"session_ids"`
	CreatedAt       time.Time `bigquery:"created_at"`
	Category        string    `bigquery:"category"`
	InactiveIds     []string  `bigquery:"inactive_ids"`
	TrialIds        []string  `bigquery:"trial_ids"`
	ProfessionalIds []string  `bigquery:"professional_ids"`
	CommunityIds    []string  `bigquery:"community_ids"`
	TeamIds         []string  `bigquery:"team_ids"`
	TeamTrialIds    []string  `bigquery:"team_trial_ids"`
	EnterpriseIds   []string  `bigquery:"enterprise_ids"`
}

func (m *FreeTrialRecentUserDuplicationJob) getSuspects(ctx context.Context) ([]*freeTrialRecentUserSuspect, error) {
	// Construct the query.
	query := m.bqClient.Query(`
		WITH
	-- aggregate by user activity
	activity AS (
		SELECT
			opaque_user_id,
			session_id,
		FROM request_aggregates_by_session
		GROUP BY 1,2
	),
	-- identify users
	recent_user_changes AS (
		SELECT
			ROW_NUMBER() OVER (PARTITION BY augment_user_id ORDER BY time DESC) as rn,
			augment_user_id as opaque_user_id,
			tenant_id,
			LOWER(user_email) as email,
			time AS created_at
		FROM add_user_to_tenant
		WHERE time >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 1 HOUR)
	),
  recent_user AS (
		SELECT
		*
		FROM recent_user_changes
		WHERE rn = 1
	),
	all_user AS (
		SELECT
			id as opaque_user_id,
			orb_subscription_id as subscription_id,
			tenant_ids[0] as tenant_id,
			LOWER(email) as email,
			created_at,
			EXISTS(
				SELECT 1
				FROM UNNEST(suspensions) AS suspension
				WHERE suspension.suspension_type = 'USER_SUSPENSION_TYPE_DISPOSABLE_EMAIL'
			) as disposable_email_suspension,
		FROM user
		WHERE ARRAY_LENGTH(tenant_ids) = 1
	),
	-- determine service tier
	tier AS (
		SELECT
			id as tenant_id,
			CASE
				WHEN tier = 'PROFESSIONAL' AND is_self_serve_team
					THEN 'TEAM'
				ELSE tier
			END AS tier
		FROM tenant
	),
	-- determine subscription type
	sub AS (
		SELECT
			subscription_id,
			CASE
				WHEN orb_status = 'ORB_STATUS_ACTIVE'
					THEN CASE
						WHEN external_plan_id = 'orb_trial_plan'
							THEN 'TRIAL'
						ELSE 'ACTIVE'
					END
				ELSE 'INACTIVE'
			END AS subscription_category
		FROM subscription
	),
	tenant_sub_map AS (
		SELECT
			tenant_id,
			orb_subscription_id
		FROM tenant_subscription_mapping
	),
	-- build user profile for historic users
	profile AS (
		SELECT
			all_user.opaque_user_id,
			all_user.created_at,
			all_user.email,
			all_user.disposable_email_suspension,
			activity.session_id,
			CASE
				WHEN sub.subscription_category = 'ACTIVE'
					THEN tier.tier
				WHEN sub.subscription_category = 'INACTIVE'
					THEN CASE
						-- Enterprise and teams activity for inactive users always counts as enterprise or teams activity.
						WHEN tier.tier IN ('ENTERPRISE', 'TEAM')
							THEN tier.tier
						ELSE 'INACTIVE'
					END
				WHEN sub.subscription_category = 'TRIAL'
						THEN CASE
								WHEN tier.tier = 'TEAM'
										THEN 'TEAM_TRIAL'
								ELSE 'TRIAL'
						END
				ELSE sub.subscription_category
			END as category,
		FROM all_user
		JOIN activity ON activity.opaque_user_id = all_user.opaque_user_id
		JOIN tier ON all_user.tenant_id = tier.tenant_id
		LEFT JOIN tenant_sub_map ON tier.tier = 'TEAM' AND all_user.tenant_id = tenant_sub_map.tenant_id
		JOIN sub
			ON CASE
				WHEN tier.tier = 'TEAM' THEN tenant_sub_map.orb_subscription_id
				ELSE all_user.subscription_id
			END = sub.subscription_id
	),
	-- Consider recent users to be suspect.
	suspect AS (
		SELECT
			recent_user.opaque_user_id,
			email,
			created_at,
			category,
			ARRAY_AGG(DISTINCT activity.session_id) as session_ids,
		FROM recent_user
		WHERE tier.tier IN ('PROFESSIONAL', 'TEAM', 'COMMUNITY')
		JOIN activity ON activity.opaque_user_id = recent_user.opaque_user_id
		JOIN tier ON recent_user.tenant_id = tier.tenant_id
		GROUP BY 1, 2, 3
	)
	-- Find suspects that have had prior accounts
	SELECT
		suspect.opaque_user_id,
		suspect.session_ids,
    	suspect.created_at,
		suspect.category,
		-- Lists of each category of connected users
		ARRAY_AGG(DISTINCT CASE WHEN profile.category = 'INACTIVE' THEN profile.opaque_user_id END IGNORE NULLS) AS inactive_ids,
		ARRAY_AGG(DISTINCT CASE WHEN profile.category = 'TRIAL' THEN profile.opaque_user_id END IGNORE NULLS) AS trial_ids,
		ARRAY_AGG(DISTINCT CASE WHEN profile.category = 'PROFESSIONAL' THEN profile.opaque_user_id END IGNORE NULLS) AS professional_ids,
		ARRAY_AGG(DISTINCT CASE WHEN profile.category = 'COMMUNITY' THEN profile.opaque_user_id END IGNORE NULLS) AS community_ids,
		ARRAY_AGG(DISTINCT CASE WHEN profile.category = 'TEAM' THEN profile.opaque_user_id END IGNORE NULLS) AS team_ids,
		ARRAY_AGG(DISTINCT CASE WHEN profile.category = 'TEAM_TRIAL' THEN profile.opaque_user_id END IGNORE NULLS) AS team_trial_ids,
		ARRAY_AGG(DISTINCT CASE WHEN profile.category = 'ENTERPRISE' THEN profile.opaque_user_id END IGNORE NULLS) AS enterprise_ids,
	FROM suspect
	JOIN profile
		ON profile.session_id IN UNNEST(suspect.session_ids)
		AND suspect.opaque_user_id != profile.opaque_user_id
		-- Only match users created before the suspect user
		AND suspect.created_at > profile.created_at
		-- Don't match users with same email in case it's our error
		AND suspect.email != profile.email
		-- Ignore trials that have been suspended for disposable email
		-- User may use a non-disposable email to create one valid trial.
		AND NOT profile.disposable_email_suspension
	GROUP BY 1, 2, 3
	ORDER BY
		-- Deal with newest user first
		suspect.created_at DESC
	`)

	// Set the default dataset ID in the query config
	query.QueryConfig.DefaultDatasetID = m.datasetName
	query.Parameters = []bigquery.QueryParameter{}

	// Run the query.
	it, err := query.Read(ctx)
	if err != nil {
		log.Error().Msgf("Query error: %v", err)
		return nil, fmt.Errorf("error running query: %w", err)
	}

	// Parse the results.
	var suspects []*freeTrialRecentUserSuspect
	for {
		var row freeTrialRecentUserSuspect
		err := it.Next(&row)
		if err == iterator.Done {
			break
		} else if err != nil {
			log.Error().Msgf("Query results error: %v", err)
			return nil, fmt.Errorf("error parsing query results: %w", err)
		} else {
			suspects = append(suspects, &row)
		}
	}
	log.Info().Msgf("Found %d freeTrialDuplicates to ban", len(suspects))
	return suspects, nil
}

func (m *FreeTrialRecentUserDuplicationJob) suspendSuspects(
	ctx context.Context,
	suspects []*freeTrialRecentUserSuspect,
) error {
	dryRun, err := freeTrialRecentUserDuplicationDryRunFlag.Get(m.featureFlagHandle)
	if err != nil {
		log.Error().Err(err).Msgf("Error getting dry run flag, defaulting to true")
		dryRun = true
	}
	if dryRun {
		log.Info().Msg("*** DRY RUN! Not suspending users. ***")
	}

	MisuseUsersFound.WithLabelValues(m.jobName).Set(float64(len(suspects)))

	// Limit users suspended per execution
	suspensionsToIssue := 1000

	sessionId := requestcontext.NewRandomRequestSessionId()

	// Get wildcard token for suspension operations (no tenant-specific token needed)
	token, err := m.tokenExchangeClient.GetSignedTokenForServiceWithNamespace(
		ctx, "", "", []tokenscopesproto.Scope{tokenscopesproto.Scope_AUTH_RW},
	)
	if err != nil {
		log.Error().Err(err).Msg("Error getting wildcard token for suspension operations")
		return fmt.Errorf("error getting wildcard token: %w", err)
	}
	requestCtx := requestcontext.New(
		requestcontext.NewRandomRequestId(), sessionId, "misuse-monitor", token)

	for _, duplicate := range suspects {

		// Suspensions are issued for duplicate inactive, trial, professional and community accounts only.
		// Ignore duplicate teams and enterprise accounts if those are the only duplicates.
		duplicateCount := len(duplicate.InactiveIds) + len(duplicate.TrialIds) + len(duplicate.ProfessionalIds) + len(duplicate.CommunityIds) + len(duplicate.TeamTrialIds)
		if duplicateCount == 0 {
			MisuseActionOutcome.WithLabelValues(m.jobName, "ineligible_duplicate", strconv.FormatBool(dryRun)).Inc()
			log.Info().Msgf("Skipping duplicate user %s, no inactive, trial, professional or community duplicates", duplicate.ID)
			continue
		}

		// Suspension NOT issued until at least 2 other accounts are detected
		if duplicateCount < 2 {
			MisuseActionOutcome.WithLabelValues(m.jobName, "trial_count_too_low", strconv.FormatBool(dryRun)).Inc()
			log.Info().Msgf("Skipping duplicate user %s, only %d trials detected", duplicate.ID, duplicateCount)
			continue
		}

		// Check if the user is exempt or already suspended for free trial abuse
		userObj, err := m.authClient.GetUser(ctx, requestCtx, duplicate.ID, nil)
		if err != nil {
			MisuseActionOutcome.WithLabelValues(m.jobName, "user_not_found", strconv.FormatBool(dryRun)).Inc()
			log.Error().Msgf("Error getting user for suspension for user %s: %v", duplicate.ID, err)
			continue
		}
		if userObj.SuspensionExempt {
			log.Info().Msgf("User %s is suspension exempt", duplicate.ID)
			MisuseActionOutcome.WithLabelValues(m.jobName, "suspension_exempt", strconv.FormatBool(dryRun)).Inc()
			continue
		}
		if userObj.Suspensions != nil && len(userObj.Suspensions) > 0 {
			alreadySuspended := false
			for _, suspension := range userObj.Suspensions {
				if suspension.SuspensionType == auth_entities.UserSuspensionType_USER_SUSPENSION_TYPE_FREE_TRIAL_ABUSE {
					log.Info().Msgf("User %s is already suspended for free trial abuse", duplicate.ID)
					MisuseActionOutcome.WithLabelValues(m.jobName, "already_suspended", strconv.FormatBool(dryRun)).Inc()
					alreadySuspended = true
					break
				}
			}
			if alreadySuspended {
				continue
			}
		}

		// Limit session IDs to first 3 for evidence
		sessionIDsForEvidence := duplicate.SessionIDs
		if len(sessionIDsForEvidence) > 3 {
			sessionIDsForEvidence = sessionIDsForEvidence[:3]
		}
		evidence := fmt.Sprintf("Duplicate accounts with free trial: %d Shared session IDs: %v inactive: %d trial: %d professional: %d community: %d team_trial: %d team: %d enterprise: %d",
			len(duplicate.SessionIDs), sessionIDsForEvidence, len(duplicate.InactiveIds), len(duplicate.TrialIds), len(duplicate.ProfessionalIds), len(duplicate.CommunityIds), len(duplicate.TeamTrialIds), len(duplicate.TeamIds), len(duplicate.EnterpriseIds))
		log.Info().Msgf("Misuse monitor detected free trial duplication by user %s. %s",
			duplicate.ID, evidence)
		if suspensionsToIssue <= 0 {
			MisuseActionOutcome.WithLabelValues(m.jobName, "suspension_limit_reached", strconv.FormatBool(dryRun)).Inc()
			continue
		}
		if !dryRun {
			// Issue free trial abuse suspension
			suspensionID, _, err := m.authClient.CreateUserSuspension(
				ctx, requestCtx, duplicate.ID, "", auth_entities.UserSuspensionType_USER_SUSPENSION_TYPE_FREE_TRIAL_ABUSE, evidence)
			if err != nil {
				log.Error().Msgf("Error creating suspension for user %s: %v", duplicate.ID, err)
				MisuseActionOutcome.WithLabelValues(m.jobName, "create_suspension_error", strconv.FormatBool(dryRun)).Inc()
				continue
			}
			suspensionsToIssue--
			log.Info().Msgf("Created suspension %s for user %s", suspensionID, duplicate.ID)
		}
		MisuseActionOutcome.WithLabelValues(m.jobName, "suspension_created", strconv.FormatBool(dryRun)).Inc()
	}

	return nil
}
