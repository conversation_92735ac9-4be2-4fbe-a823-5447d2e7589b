package main

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"cloud.google.com/go/bigquery"
	featureflags "github.com/augmentcode/augment/base/feature_flags"
	authclient "github.com/augmentcode/augment/services/auth/central/client"
	auth_entities "github.com/augmentcode/augment/services/auth/central/server/auth_entities_proto"
	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
	tokenexchange "github.com/augmentcode/augment/services/token_exchange/client"
	tokenscopesproto "github.com/augmentcode/augment/services/token_exchange/token_scopes_proto"
	"github.com/rs/zerolog/log"
	"google.golang.org/api/iterator"
)

// If true, search for users but don't actually suspend them.
var disposableEmailDomainDryRunFlag = featureflags.NewBoolFlag("disposable_email_domain_dry_run", true)

type DisposableEmailDomainJob struct {
	bqClient            *bigquery.Client
	datasetName         string
	jobName             string
	authClient          authclient.AuthClient
	tokenExchangeClient tokenexchange.TokenExchangeClient
	featureFlagHandle   featureflags.FeatureFlagHandle
}

// Ensure DisposableEmailDomainJob implements the Job interface
var _ Job = (*DisposableEmailDomainJob)(nil)

func NewDisposableEmailDomainJob(
	ctx context.Context,
	projectId string,
	datasetName string,
	authClient authclient.AuthClient,
	tokenExchangeClient tokenexchange.TokenExchangeClient,
	featureFlagHandle featureflags.FeatureFlagHandle,
) (*DisposableEmailDomainJob, error) {
	// The BigQuery API doesn't let us parameterize dataset/table names, so we have to inject this
	// into our query with string manipulation. Make sure it doesn't contain contain anything that
	// could be malicious.
	if !CheckDatasetName(datasetName) {
		return nil, fmt.Errorf("Invalid dataset name %s", datasetName)
	}

	bqClient, err := bigquery.NewClient(ctx, projectId)
	if err != nil {
		return nil, fmt.Errorf("error creating bigquery client: %w", err)
	}

	return &DisposableEmailDomainJob{
		bqClient:            bqClient,
		datasetName:         datasetName,
		jobName:             "disposable-email-domain",
		authClient:          authClient,
		tokenExchangeClient: tokenExchangeClient,
		featureFlagHandle:   featureFlagHandle,
	}, nil
}

func (m *DisposableEmailDomainJob) Close() {
	m.bqClient.Close()
}

func (m *DisposableEmailDomainJob) Run(ctx context.Context) error {
	// Get users from BigQuery
	suspects, err := m.getSuspects(ctx)
	if err != nil {
		return fmt.Errorf("error getting disposable email suspect users: %w", err)
	}

	log.Info().Msgf("Total of %d users to process", len(suspects))

	// Ban the users
	err = m.suspendSuspects(ctx, suspects)
	if err != nil {
		return fmt.Errorf("error suspending users: %w", err)
	}

	return nil
}

type disposableEmailSuspect struct {
	ID          string    `bigquery:"opaque_user_id"`
	EmailDomain string    `bigquery:"email_domain"`
	Tier        string    `bigquery:"tier"`
	CreatedAt   time.Time `bigquery:"created_at"`
}

func (m *DisposableEmailDomainJob) getSuspects(ctx context.Context) ([]*disposableEmailSuspect, error) {
	// Construct the query.
	// Using verisoul data to identify disposable email users.
	query := m.bqClient.Query(`
	WITH
	recent_user_changes AS (
		SELECT
			augment_user_id AS opaque_user_id,
			tenant_id,
			LOWER(user_email) AS email,
			time AS created_at,
			ROW_NUMBER() OVER (PARTITION BY augment_user_id ORDER BY time DESC) as rn
		FROM add_user_to_tenant
		WHERE time >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 14 DAY)
		-- Only those users created after the disposable email ban was put into effect, about 2:30PM on 6/17.
		AND time >= TIMESTAMP('2025-06-17 14:30:00-07:00')
	),
	recent_user AS (
		SELECT
		*
		FROM recent_user_changes
		WHERE rn = 1
	),
	-- determine service tier
	tier AS (
		SELECT
			id as tenant_id,
			CASE
				WHEN tier = 'PROFESSIONAL' AND is_self_serve_team
					THEN 'TEAM'
				ELSE tier
			END AS tier
		FROM tenant
	),
	-- Email analysis from Verisoul reports
	verisoul_report AS (
		SELECT
      time,
			LOWER(JSON_EXTRACT_SCALAR(report, '$.account.email.email')) as email,
			SAFE_CAST(JSON_EXTRACT_SCALAR(report, '$.account.email.disposable') AS BOOL) is_disposable,
		FROM verisoul
		WHERE report IS NOT NULL
			AND JSON_EXTRACT_SCALAR(report, '$.account.email.email') IS NOT NULL
	),
	verisoul_report_ranked AS (
		SELECT
			email,
			is_disposable,
			ROW_NUMBER() OVER (PARTITION BY email ORDER BY time DESC) as rn
		FROM verisoul_report
	),
	disposable_email AS (
		SELECT
		*
		FROM verisoul_report_ranked
		WHERE rn = 1
	),
	-- identify users
	user_id AS (
		SELECT
			id as opaque_user_id,
			EXISTS(
				SELECT 1
				FROM UNNEST(suspensions) AS suspension
				WHERE suspension.suspension_type = 'USER_SUSPENSION_TYPE_DISPOSABLE_EMAIL'
			) as suspended,
			suspension_exempt,
		FROM user
	)

	SELECT
		ru.opaque_user_id,
		REGEXP_EXTRACT(de.email, r'@(.*)') as email_domain,
		tier.tier,
		ru.created_at,
	FROM recent_user ru
	JOIN disposable_email de
		ON de.email = ru.email
	JOIN tier
		ON ru.tenant_id = tier.tenant_id
	LEFT JOIN user_id
		ON ru.opaque_user_id = user_id.opaque_user_id
	WHERE
		tier.tier IN ('PROFESSIONAL', 'TEAM', 'COMMUNITY')
		-- discard those that are already suspended or exempt
		AND (user_id.suspended IS NULL OR NOT user_id.suspended)
		AND (user_id.suspension_exempt IS NULL OR NOT user_id.suspension_exempt)
		AND (
			de.is_disposable
			-- Also suspend user with these specific email patterns
			OR ru.email LIKE '%@2925.com'
			OR ru.email LIKE '%@wucur.com'
		)
	ORDER BY created_at DESC
	`)

	// Set the default dataset ID in the query config
	query.QueryConfig.DefaultDatasetID = m.datasetName
	query.Parameters = []bigquery.QueryParameter{}

	// Run the query.
	it, err := query.Read(ctx)
	if err != nil {
		log.Error().Msgf("Query error: %v", err)
		return nil, fmt.Errorf("error running query: %w", err)
	}

	// Parse the results.
	var disposableEmailSuspects []*disposableEmailSuspect
	for {
		var row disposableEmailSuspect
		err := it.Next(&row)
		if err == iterator.Done {
			break
		} else if err != nil {
			log.Error().Msgf("Query results error: %v", err)
			return nil, fmt.Errorf("error parsing query results: %w", err)
		} else {
			disposableEmailSuspects = append(disposableEmailSuspects, &row)
		}
	}
	log.Info().Msgf("Found %d freeTrialDuplicates to ban", len(disposableEmailSuspects))
	return disposableEmailSuspects, nil
}

func (m *DisposableEmailDomainJob) suspendSuspects(
	ctx context.Context,
	suspects []*disposableEmailSuspect,
) error {
	dryRun, err := disposableEmailDomainDryRunFlag.Get(m.featureFlagHandle)
	if err != nil {
		log.Error().Err(err).Msgf("Error getting dry run flag, defaulting to true")
		dryRun = true
	}
	if dryRun {
		log.Info().Msg("*** DRY RUN! Not suspending users. ***")
	}

	communitySuspensionsEnabled := communitySuspensionsEnabled(m.featureFlagHandle)

	MisuseUsersFound.WithLabelValues(m.jobName).Set(float64(len(suspects)))

	// Limit users suspended per execution
	suspensionsToIssue := 1000

	sessionId := requestcontext.NewRandomRequestSessionId()

	// Get wildcard token for suspension operations (no tenant-specific token needed)
	token, err := m.tokenExchangeClient.GetSignedTokenForServiceWithNamespace(
		ctx, "", "", []tokenscopesproto.Scope{tokenscopesproto.Scope_AUTH_RW},
	)
	if err != nil {
		log.Error().Err(err).Msg("Error getting wildcard token for suspension operations")
		return fmt.Errorf("error getting wildcard token: %w", err)
	}
	requestCtx := requestcontext.New(
		requestcontext.NewRandomRequestId(), sessionId, "misuse-monitor", token)

	for _, suspect := range suspects {

		// Check if the user is exempt or already suspended for using disposable email
		userObj, err := m.authClient.GetUser(ctx, requestCtx, suspect.ID, nil)
		if err != nil {
			MisuseActionOutcome.WithLabelValues(m.jobName, "user_not_found", strconv.FormatBool(dryRun)).Inc()
			log.Error().Msgf("Error getting user for suspension for user %s: %v", suspect.ID, err)
			continue
		}
		if userObj.SuspensionExempt {
			log.Info().Msgf("User %s is suspension exempt", suspect.ID)
			MisuseActionOutcome.WithLabelValues(m.jobName, "suspension_exempt", strconv.FormatBool(dryRun)).Inc()
			continue
		}
		if userObj.Suspensions != nil && len(userObj.Suspensions) > 0 {
			alreadySuspended := false
			for _, suspension := range userObj.Suspensions {
				if suspension.SuspensionType == auth_entities.UserSuspensionType_USER_SUSPENSION_TYPE_DISPOSABLE_EMAIL {
					log.Info().Msgf("User %s is already suspended for disposable email domain %s", suspect.ID, suspect.EmailDomain)
					MisuseActionOutcome.WithLabelValues(m.jobName, "already_suspended", strconv.FormatBool(dryRun)).Inc()
					alreadySuspended = true
					break
				}
			}
			if alreadySuspended {
				continue
			}
		}

		if !communitySuspensionsEnabled && suspect.Tier == "COMMUNITY" {
			log.Info().Msgf("User %s is in community tier, skipping", suspect.ID)
			MisuseActionOutcome.WithLabelValues(m.jobName, "community_suspensions_disabled", strconv.FormatBool(dryRun)).Inc()
			continue
		}

		evidence := fmt.Sprintf("Use of disposable email domain: %s", suspect.EmailDomain)
		log.Info().Msgf("Misuse monitor detected disposable email by user %s. %s",
			suspect.ID, evidence)
		if suspensionsToIssue <= 0 {
			MisuseActionOutcome.WithLabelValues(m.jobName, "suspension_limit_reached", strconv.FormatBool(dryRun)).Inc()
			continue
		}
		if !dryRun {
			// Issue disposable email suspension
			suspensionID, _, err := m.authClient.CreateUserSuspension(
				ctx, requestCtx, suspect.ID, "", auth_entities.UserSuspensionType_USER_SUSPENSION_TYPE_DISPOSABLE_EMAIL, evidence)
			if err != nil {
				log.Error().Msgf("Error creating suspension for user %s: %v", suspect.ID, err)
				MisuseActionOutcome.WithLabelValues(m.jobName, "create_suspension_error", strconv.FormatBool(dryRun)).Inc()
				continue
			}
			suspensionsToIssue--
			log.Info().Msgf("Created suspension %s for user %s", suspensionID, suspect.ID)
		}
		MisuseActionOutcome.WithLabelValues(m.jobName, "suspension_created", strconv.FormatBool(dryRun)).Inc()
	}

	return nil
}
