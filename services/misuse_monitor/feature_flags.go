package main

import (
	featureflags "github.com/augmentcode/augment/base/feature_flags"
	"github.com/rs/zerolog/log"
)

var communityAbuseDryRunFlag = featureflags.NewBoolFlag("community_abuse_dry_run", true)

// If true, search for users but don't actually suspend them.
func communitySuspensionsEnabled(featureFlagHandle featureflags.FeatureFlagHandle) bool {
	communityAccountSuspensionEnabled, err := communityAbuseDryRunFlag.Get(featureFlagHandle)
	if err != nil {
		log.Error().Err(err).Msgf("Error getting community account suspension enabled flag, defaulting to false")
		communityAccountSuspensionEnabled = false
	}
	return communityAccountSuspensionEnabled
}
