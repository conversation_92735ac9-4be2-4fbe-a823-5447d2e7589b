package main

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"cloud.google.com/go/bigquery"
	featureflags "github.com/augmentcode/augment/base/feature_flags"
	authclient "github.com/augmentcode/augment/services/auth/central/client"
	auth_entities "github.com/augmentcode/augment/services/auth/central/server/auth_entities_proto"
	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
	tokenexchange "github.com/augmentcode/augment/services/token_exchange/client"
	tokenscopesproto "github.com/augmentcode/augment/services/token_exchange/token_scopes_proto"
	"github.com/rs/zerolog/log"
	"google.golang.org/api/iterator"
)

// If true, search for users but don't actually suspend them.
var freeTrialConversationDuplicationDryRunFlag = featureflags.NewBoolFlag("free_trial_conversation_duplication_dry_run", true)

type FreeTrialConversationDuplicationJob struct {
	bqClient            *bigquery.Client
	datasetName         string
	jobName             string
	authClient          authclient.AuthClient
	tokenExchangeClient tokenexchange.TokenExchangeClient
	featureFlagHandle   featureflags.FeatureFlagHandle
}

// Ensure FreeTrialConversationDuplicationJob implements the Job interface
var _ Job = (*FreeTrialConversationDuplicationJob)(nil)

func NewFreeTrialConversationDuplicationJob(
	ctx context.Context,
	projectId string,
	datasetName string,
	authClient authclient.AuthClient,
	tokenExchangeClient tokenexchange.TokenExchangeClient,
	featureFlagHandle featureflags.FeatureFlagHandle,
) (*FreeTrialConversationDuplicationJob, error) {
	// The BigQuery API doesn't let us parameterize dataset/table names, so we have to inject this
	// into our query with string manipulation. Make sure it doesn't contain contain anything that
	// could be malicious.
	if !CheckDatasetName(datasetName) {
		return nil, fmt.Errorf("Invalid dataset name %s", datasetName)
	}

	bqClient, err := bigquery.NewClient(ctx, projectId)
	if err != nil {
		return nil, fmt.Errorf("error creating bigquery client: %w", err)
	}

	return &FreeTrialConversationDuplicationJob{
		bqClient:            bqClient,
		datasetName:         datasetName,
		jobName:             "free-trial-conversation-duplicate",
		authClient:          authClient,
		tokenExchangeClient: tokenExchangeClient,
		featureFlagHandle:   featureFlagHandle,
	}, nil
}

func (m *FreeTrialConversationDuplicationJob) Close() {
	m.bqClient.Close()
}

func (m *FreeTrialConversationDuplicationJob) Run(ctx context.Context) error {
	// Get users from BigQuery
	freeTrialConversationDuplicates, err := m.getFreeTrialConversationDuplicates(ctx)
	if err != nil {
		return fmt.Errorf("error getting malicious users: %w", err)
	}

	log.Info().Msgf("Total of %d users to process", len(freeTrialConversationDuplicates))

	// Ban the users
	err = m.suspendFreeTrialConversationDuplicates(ctx, freeTrialConversationDuplicates)
	if err != nil {
		return fmt.Errorf("error banning users: %w", err)
	}

	return nil
}

type freeTrialConversationDuplicate struct {
	ID              string    `bigquery:"opaque_user_id"`
	CreatedAt       time.Time `bigquery:"created_at"`
	Category        string    `bigquery:"category"`
	ConversationIDs []string  `bigquery:"conversation_ids"`
	InactiveIds     []string  `bigquery:"inactive_ids"`
	TrialIds        []string  `bigquery:"trial_ids"`
	ProfessionalIds []string  `bigquery:"professional_ids"`
	CommunityIds    []string  `bigquery:"community_ids"`
	TeamTrialIds    []string  `bigquery:"team_trial_ids"`
	TeamIds         []string  `bigquery:"team_ids"`
	EnterpriseIds   []string  `bigquery:"enterprise_ids"`
}

func (m *FreeTrialConversationDuplicationJob) getFreeTrialConversationDuplicates(ctx context.Context) ([]*freeTrialConversationDuplicate, error) {
	// Construct the query.
	query := m.bqClient.Query(`
	WITH
	-- Map sessions to conversation IDs
	session_conversations AS (
	    WITH parsed AS (
	        SELECT DISTINCT
	            session_id,
	            JSON_EXTRACT_SCALAR(sanitized_json, '$.conversation_id') as conversation_id
	        FROM agent_request_event
	        WHERE time >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 30 DAY)
	    )
	    select session_id, conversation_id from parsed
	    WHERE conversation_id IS NOT NULL and conversation_id != "__NEW_AGENT__"
	),
	-- Get user activity and join with conversations
	conversation_activity AS (
	    SELECT
	        ras.opaque_user_id,
	        sc.conversation_id,
	        SUM(ras.request_count) as request_count
	    FROM request_aggregates_by_session ras
	    JOIN session_conversations sc ON ras.session_id = sc.session_id
			AND ras.request_type in ('CHAT', 'AGENT_CHAT')
	        and ras.last_request_time >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 30 DAY)
	    GROUP BY 1, 2
	),
	-- identify users
	user_id AS (
		SELECT
			id as opaque_user_id,
			tenant_ids[0] as tenant_id,
			orb_subscription_id as subscription_id,
			created_at,
			LOWER(email) as email,
			EXISTS(
				SELECT 1
				FROM UNNEST(suspensions) AS suspension
				WHERE suspension.suspension_type = 'USER_SUSPENSION_TYPE_FREE_TRIAL_ABUSE'
			) as suspended,
			EXISTS(
				SELECT 1
				FROM UNNEST(suspensions) AS suspension
				WHERE suspension.suspension_type = 'USER_SUSPENSION_TYPE_DISPOSABLE_EMAIL'
			) as disposable_email_suspension,
			suspension_exempt as exempt
		FROM user
		WHERE ARRAY_LENGTH(tenant_ids) = 1
	),
	-- determine service tier
	tier AS (
		SELECT
			id as tenant_id,
			CASE
				WHEN tier = 'PROFESSIONAL' AND is_self_serve_team
					THEN 'TEAM'
				ELSE tier
			END AS tier
		FROM tenant
	),
	-- determine subscription type
	sub AS (
		SELECT
			subscription_id,
			CASE
				WHEN orb_status = 'ORB_STATUS_ACTIVE'
					THEN CASE
						WHEN external_plan_id = 'orb_trial_plan'
							THEN 'TRIAL'
						ELSE 'ACTIVE'
					END
				ELSE 'INACTIVE'
			END AS subscription_category
		FROM subscription
	),
	tenant_sub_map AS (
		SELECT
			tenant_id,
			orb_subscription_id
		FROM tenant_subscription_mapping
	),
	-- build user profile from all the above
	profile AS (
		SELECT
			conversation_activity.opaque_user_id,
			user_id.tenant_id,
			conversation_activity.conversation_id,
			conversation_activity.request_count,
			user_id.email,
			user_id.created_at,
			user_id.suspended,
			user_id.disposable_email_suspension,
			user_id.exempt,
			CASE
				WHEN sub.subscription_category = 'ACTIVE'
					THEN tier.tier
				WHEN sub.subscription_category = 'INACTIVE'
					THEN CASE
						-- Enterprise and teams activity for inactive users always counts as enterprise or teams activity.
						WHEN tier.tier IN ('ENTERPRISE', 'TEAM')
							THEN tier.tier
						ELSE 'INACTIVE'
					END
				WHEN sub.subscription_category = 'TRIAL'
						THEN CASE
								WHEN tier.tier = 'TEAM'
										THEN 'TEAM_TRIAL'
								ELSE 'TRIAL'
						END
				ELSE sub.subscription_category
			END as category,
			tier.tier,
			sub.subscription_category
		FROM user_id
		JOIN conversation_activity ON conversation_activity.opaque_user_id = user_id.opaque_user_id
		JOIN tier ON user_id.tenant_id = tier.tenant_id
		LEFT JOIN tenant_sub_map ON tier.tier = 'TEAM' AND user_id.tenant_id = tenant_sub_map.tenant_id
		JOIN sub
			ON CASE
				WHEN tier.tier = 'TEAM' THEN tenant_sub_map.orb_subscription_id
				ELSE user_id.subscription_id
			END = sub.subscription_id
	),
	-- Want to know all the conversation IDs used by each suspect user.
	suspect AS (
		SELECT
			opaque_user_id,
			tenant_id,
			email,
			tier,
			created_at,
			category,
			ARRAY_AGG(DISTINCT conversation_id) as conversation_ids,
			SUM(request_count) as request_count
		FROM profile
		WHERE category IN ('TRIAL', 'TEAM_TRIAL', 'COMMUNITY')
		-- Skip suspended and exempt suspect users
		AND NOT profile.suspended AND NOT profile.exempt
		GROUP BY 1, 2, 3, 4, 5, 6
	)
	-- For each free trial user, what other users shared a conversation ID?
	SELECT
		suspect.opaque_user_id,
		suspect.created_at,
		suspect.category,
		ARRAY_AGG(DISTINCT other.conversation_id) as conversation_ids,
		-- Lists of each category of connected users
		ARRAY_AGG(DISTINCT CASE WHEN other.category = 'INACTIVE' THEN other.opaque_user_id END IGNORE NULLS) AS inactive_ids,
		ARRAY_AGG(DISTINCT CASE WHEN other.category = 'TRIAL' THEN other.opaque_user_id END IGNORE NULLS) AS trial_ids,
		ARRAY_AGG(DISTINCT CASE WHEN other.category = 'PROFESSIONAL' THEN other.opaque_user_id END IGNORE NULLS) AS professional_ids,
		ARRAY_AGG(DISTINCT CASE WHEN other.category = 'COMMUNITY' THEN other.opaque_user_id END IGNORE NULLS) AS community_ids,
		ARRAY_AGG(DISTINCT CASE WHEN other.category = 'TEAM' THEN other.opaque_user_id END IGNORE NULLS) AS team_ids,
		ARRAY_AGG(DISTINCT CASE WHEN other.category = 'TEAM_TRIAL' THEN other.opaque_user_id END IGNORE NULLS) AS team_trial_ids,
		ARRAY_AGG(DISTINCT CASE WHEN other.category = 'ENTERPRISE' THEN other.opaque_user_id END IGNORE NULLS) AS enterprise_ids,
	FROM suspect
	JOIN profile AS other
	ON other.conversation_id IN UNNEST(suspect.conversation_ids)
	AND suspect.opaque_user_id != other.opaque_user_id
	-- Only match users created before the suspect user
	AND suspect.created_at > other.created_at
	-- Don't match users with same email in case it's our error
	AND suspect.email != other.email
	-- Ignore suspects that have been suspended for disposable email
	-- User may use a non-disposable email to create a valid account.
	AND NOT other.disposable_email_suspension
	GROUP BY 1, 2, 3
	ORDER BY
		-- Deal with newest suspect user first
		suspect.created_at DESC
	`)

	// Set the default dataset ID in the query config
	query.QueryConfig.DefaultDatasetID = m.datasetName
	query.Parameters = []bigquery.QueryParameter{}

	// Run the query.
	it, err := query.Read(ctx)
	if err != nil {
		log.Error().Msgf("Query error: %v", err)
		return nil, fmt.Errorf("error running query: %w", err)
	}

	// Parse the results.
	var freeTrialConversationDuplicates []*freeTrialConversationDuplicate
	for {
		var row freeTrialConversationDuplicate
		err := it.Next(&row)
		if err == iterator.Done {
			break
		} else if err != nil {
			log.Error().Msgf("Query results error: %v", err)
			return nil, fmt.Errorf("error parsing query results: %w", err)
		} else {
			freeTrialConversationDuplicates = append(freeTrialConversationDuplicates, &row)
		}
	}
	log.Info().Msgf("Found %d freeTrialConversationDuplicates to ban", len(freeTrialConversationDuplicates))
	return freeTrialConversationDuplicates, nil
}

func (m *FreeTrialConversationDuplicationJob) suspendFreeTrialConversationDuplicates(
	ctx context.Context,
	freeTrialConversationDuplicates []*freeTrialConversationDuplicate,
) error {
	dryRun, err := freeTrialConversationDuplicationDryRunFlag.Get(m.featureFlagHandle)
	if err != nil {
		log.Error().Err(err).Msgf("Error getting dry run flag, defaulting to true")
		dryRun = true
	}
	if dryRun {
		log.Info().Msg("*** DRY RUN! Not suspending users. ***")
	}

	communitySuspensionsEnabled := communitySuspensionsEnabled(m.featureFlagHandle)

	MisuseUsersFound.WithLabelValues(m.jobName).Set(float64(len(freeTrialConversationDuplicates)))

	// Limit users suspended per execution
	suspensionsToIssue := 1000

	sessionId := requestcontext.NewRandomRequestSessionId()

	// Get wildcard token for suspension operations (no tenant-specific token needed)
	token, err := m.tokenExchangeClient.GetSignedTokenForServiceWithNamespace(
		ctx, "", "", []tokenscopesproto.Scope{tokenscopesproto.Scope_AUTH_RW},
	)
	if err != nil {
		log.Error().Err(err).Msg("Error getting wildcard token for suspension operations")
		return fmt.Errorf("error getting wildcard token: %w", err)
	}
	requestCtx := requestcontext.New(
		requestcontext.NewRandomRequestId(), sessionId, "misuse-monitor", token)

	for _, duplicate := range freeTrialConversationDuplicates {

		// Suspensions are issued for duplicate inactive, trial, team trial,  professional and community accounts only.
		// Ignore duplicate teams and enterprise accounts if those are the only duplicates.
		duplicateCount := len(duplicate.InactiveIds) + len(duplicate.TrialIds) + len(duplicate.ProfessionalIds) + len(duplicate.CommunityIds) + len(duplicate.TeamTrialIds)
		if duplicateCount == 0 {
			MisuseActionOutcome.WithLabelValues(m.jobName, "ineligible_duplicate", strconv.FormatBool(dryRun)).Inc()
			log.Info().Msgf("Skipping duplicate user %s, no inactive, trial, professional or community duplicates", duplicate.ID)
			continue
		}

		// Suspension NOT issued until at least 2 other accounts are detected
		if duplicateCount < 2 {
			MisuseActionOutcome.WithLabelValues(m.jobName, "trial_count_too_low", strconv.FormatBool(dryRun)).Inc()
			log.Info().Msgf("Skipping duplicate user %s, only %d trials detected", duplicate.ID, duplicateCount)
			continue
		}

		// Suspensions issued for community accounts only if feature flag enabled.
		if duplicate.Category == "COMMUNITY" && !communitySuspensionsEnabled {
			MisuseActionOutcome.WithLabelValues(m.jobName, "community_account_suspension_disabled", strconv.FormatBool(dryRun)).Inc()
			continue
		}

		// Check if the user is exempt or already suspended for free trial abuse
		userObj, err := m.authClient.GetUser(ctx, requestCtx, duplicate.ID, nil)
		if err != nil {
			MisuseActionOutcome.WithLabelValues(m.jobName, "user_not_found", strconv.FormatBool(dryRun)).Inc()
			log.Error().Msgf("Error getting user for suspension for user %s: %v", duplicate.ID, err)
			continue
		}
		if userObj.SuspensionExempt {
			log.Info().Msgf("User %s is suspension exempt", duplicate.ID)
			MisuseActionOutcome.WithLabelValues(m.jobName, "suspension_exempt", strconv.FormatBool(dryRun)).Inc()
			continue
		}
		if userObj.Suspensions != nil && len(userObj.Suspensions) > 0 {
			alreadySuspended := false
			for _, suspension := range userObj.Suspensions {
				if suspension.SuspensionType == auth_entities.UserSuspensionType_USER_SUSPENSION_TYPE_FREE_TRIAL_ABUSE {
					log.Info().Msgf("User %s is already suspended for free trial abuse", duplicate.ID)
					MisuseActionOutcome.WithLabelValues(m.jobName, "already_suspended", strconv.FormatBool(dryRun)).Inc()
					alreadySuspended = true
					break
				}
			}
			if alreadySuspended {
				continue
			}
		}

		// report count of conversatsions plus up to first three conversations
		conversationIDsForEvidence := duplicate.ConversationIDs
		if len(conversationIDsForEvidence) > 3 {
			conversationIDsForEvidence = conversationIDsForEvidence[:3]
		}
		evidence := fmt.Sprintf("Duplicate accounts with free trial: %d Shared conversation IDs: %v category: %s inactive: %d trial: %d professional: %d community: %d team_trial: %d team: %d enterprise: %d",
			len(duplicate.ConversationIDs), conversationIDsForEvidence, duplicate.Category, len(duplicate.InactiveIds), len(duplicate.TrialIds), len(duplicate.ProfessionalIds), len(duplicate.CommunityIds), len(duplicate.TeamTrialIds), len(duplicate.TeamIds), len(duplicate.EnterpriseIds))
		log.Info().Msgf("Misuse monitor detected free trial duplication by user %s. %s", duplicate.ID, evidence)
		if suspensionsToIssue <= 0 {
			MisuseActionOutcome.WithLabelValues(m.jobName, "suspension_limit_reached", strconv.FormatBool(dryRun)).Inc()
			continue
		}
		if !dryRun {
			// Issue abuse suspension
			suspensionType := auth_entities.UserSuspensionType_USER_SUSPENSION_TYPE_FREE_TRIAL_ABUSE
			if duplicate.Category == "COMMUNITY" {
				suspensionType = auth_entities.UserSuspensionType_USER_SUSPENSION_TYPE_COMMUNITY_ABUSE
			}

			suspensionID, _, err := m.authClient.CreateUserSuspension(
				ctx, requestCtx, duplicate.ID, "", suspensionType, evidence)
			if err != nil {
				log.Error().Msgf("Error creating suspension for user %s: %v", duplicate.ID, err)
				MisuseActionOutcome.WithLabelValues(m.jobName, "create_suspension_error", strconv.FormatBool(dryRun)).Inc()
				continue
			}
			suspensionsToIssue--
			log.Info().Msgf("Created suspension %s for user %s", suspensionID, duplicate.ID)
		}
		MisuseActionOutcome.WithLabelValues(m.jobName, "suspension_created", strconv.FormatBool(dryRun)).Inc()
	}

	return nil
}
