load("//tools/bzl:go.bzl", "go_library", "go_test")

go_library(
    name = "tasks_lib",
    srcs = [
        "analytics_forget_user.go",
        "auth_central_delete_account.go",
        "auth_central_forget_user.go",
        "content_manager_delete_blobs.go",
        "interface.go",
        "remote_agents_delete_data.go",
        "settings_delete_user_settings.go",
        "sharded_client_factory.go",
        "utils.go",
        "vanguard_export_delete_blobs.go",
        "vanguard_export_delete_request_data.go",
    ],
    importpath = "github.com/augmentcode/augment/services/shredder/server/tasks",
    visibility = ["//services/shredder/server:__subpackages__"],
    deps = [
        "//services/auth/central/client:auth_client_go",
        "//services/content_manager:content_manager_go_proto",
        "//services/content_manager/client:client_go",
        "//services/lib/request_context:request_context_go",
        "//services/remote_agents/client:client_go",
        "//services/request_insight/analytics/client:client_go",
        "//services/request_insight/central:request_insight_central_go_proto",
        "//services/request_insight/central/client:client_go",
        "//services/settings/client:client_go",
        "//services/shredder:shredder_admin_go_proto",
        "//services/tenant_watcher:tenant_watcher_go_proto",
        "//services/tenant_watcher/client:client_go",
        "//services/tenant_watcher/util:go_lib",
        "//services/token_exchange:token_exchange_go_proto",
        "//services/token_exchange:token_scopes_go_proto",
        "//services/token_exchange/client:client_go",
        "@com_github_prometheus_client_golang//prometheus",
        "@com_github_prometheus_client_golang//prometheus/promauto:go_default_library",
        "@com_github_rs_zerolog//log",
        "@com_github_stretchr_testify//mock",
        "@org_golang_google_protobuf//types/known/timestamppb",
    ],
)

go_test(
    name = "tasks_test",
    srcs = [
        "analytics_forget_user_test.go",
        "auth_central_delete_account_test.go",
        "auth_central_forget_user_test.go",
        "content_manager_delete_blobs_test.go",
        "remote_agents_delete_data_test.go",
        "settings_delete_user_settings_test.go",
        "test_utils.go",
        "vanguard_export_delete_blobs_test.go",
        "vanguard_export_delete_request_data_test.go",
    ],
    embed = [":tasks_lib"],
    deps = [
        "//base/go/secretstring:secretstring_go",
        "//services/auth/central/client:auth_client_go",
        "//services/auth/central/server:auth_entities_go_proto",
        "//services/auth/central/server:auth_go_grpc",
        "//services/content_manager:content_manager_go_proto",
        "//services/content_manager/client:client_go",
        "//services/lib/request_context:request_context_go",
        "//services/remote_agents/client:client_go",
        "//services/request_insight/analytics:request_insight_analytics_go_proto",
        "//services/request_insight/analytics/client:client_go",
        "//services/request_insight/central:request_insight_central_go_proto",
        "//services/request_insight/central/client:client_go",
        "//services/settings:settings_go_proto",
        "//services/settings/client:client_go",
        "//services/shredder:shredder_admin_go_proto",
        "//services/tenant_watcher:tenant_watcher_go_proto",
        "//services/tenant_watcher/client:client_go",
        "//services/tenant_watcher/util:go_lib",
        "//services/token_exchange:token_scopes_go_proto",
        "//services/token_exchange/client:client_go",
        "@com_github_prometheus_client_golang//prometheus",
        "@com_github_prometheus_client_golang//prometheus/promauto:go_default_library",
        "@com_github_rs_zerolog//log",
        "@com_github_stretchr_testify//mock",
        "@com_github_stretchr_testify//require",
        "@org_golang_google_protobuf//types/known/timestamppb",
    ],
)
