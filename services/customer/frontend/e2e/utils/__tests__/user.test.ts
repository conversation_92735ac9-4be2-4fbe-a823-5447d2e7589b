// eslint-disable-next-line no-restricted-imports
import { describe, test, expect, vi, beforeEach, afterEach } from "vitest";
import type { Page, Route, Request } from "@playwright/test";
import crypto from "crypto";
import { SecretManagerServiceClient } from "@google-cloud/secret-manager";
import { generateTestUser } from "../user";
import { includeAuthHeader } from "../auth";
import * as sessionModule from "../session";
import { user, setUser } from "../session";

/**
 * Vitest tests for user.ts.
 *
 * To run these tests, execute:
 * `pnpm exec vitest run e2e/utils/__tests__/user.test.ts --config=e2e/vitest.e2e.config.ts`
 *
 * Background: user.ts contains utility functions for managing test users,
 * generating bearer tokens, and intercepting requests to add authentication headers.
 */

// Mock the app-config module
vi.mock("../app-config", () => ({
  APP_CONFIG: {
    projectId: "test-project-id",
    secretId: "test-secret-id",
    customerUiUrl: "https://app.test.augmentcode.com",
  },
}));

// Mock the Google Cloud Secret Manager
vi.mock("@google-cloud/secret-manager", () => ({
  SecretManagerServiceClient: vi.fn(),
}));

// Mock crypto module
vi.mock("crypto", () => ({
  default: {
    createHmac: vi.fn(),
  },
}));

describe("user.ts", () => {
  let mockSecretManagerClient: any;
  let mockHmac: any;
  let mockPage: Page;
  let mockRoute: Route;
  let mockRequest: Request;

  beforeEach(() => {
    // Reset all mocks
    vi.clearAllMocks();

    // Note: We can't directly reset user due to module exports,
    // but individual tests can manage their own user state

    // Mock crypto.createHmac with dynamic signature based on input
    let hmacCallCount = 0;
    mockHmac = {
      update: vi.fn().mockImplementation((data) => {
        // Store the data for use in digest
        mockHmac._lastData = data;
        return mockHmac;
      }),
      digest: vi.fn().mockImplementation(() => {
        // Create a unique signature based on the actual data
        hmacCallCount++;
        const data = mockHmac._lastData || "default";
        // Create a hash of the data to ensure different inputs produce different outputs
        const hash = Buffer.from(String(data)).toString("base64").slice(0, 12);
        return `sig-${hmacCallCount}-${hash}`;
      }),
      _lastData: null,
    };
    (crypto.createHmac as any).mockReturnValue(mockHmac);

    // Mock SecretManagerServiceClient
    mockSecretManagerClient = {
      accessSecretVersion: vi.fn().mockResolvedValue([
        {
          payload: {
            data: Buffer.from("mock-secret-value"),
          },
        },
      ]),
    };
    (SecretManagerServiceClient as any).mockImplementation(() => mockSecretManagerClient);

    // Mock Playwright objects
    mockRequest = {
      url: vi.fn(),
      headers: vi.fn().mockReturnValue({}),
    } as any;

    mockRoute = {
      request: vi.fn().mockReturnValue(mockRequest),
      continue: vi.fn(),
    } as any;

    mockPage = {
      route: vi.fn(),
    } as any;
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe("generateTestUser", () => {
    test("should generate a test user", () => {
      const customName = "testUser123";
      const user = generateTestUser(customName);

      expect(user.email).toMatch(
        new RegExp(`^${customName}_e2e_customerui_\\d+-[a-z0-9]{6}@augm\\.io$`),
      );
      expect(user.sub).toMatch(/^test\|\d+-[a-z0-9]{6}$/);
    });

    test("should generate unique users on multiple calls", () => {
      const user1 = generateTestUser();
      const user2 = generateTestUser();

      expect(user1.email).not.toBe(user2.email);
      expect(user1.sub).not.toBe(user2.sub);
    });
  });

  describe("setUser and user", () => {
    test("should set and retrieve current user", () => {
      const testUser = generateTestUser("testSetUser");

      setUser(testUser);

      expect(sessionModule.user).toBe(testUser);
      expect(sessionModule.user.email).toBe(testUser.email);
      expect(sessionModule.user.sub).toBe(testUser.sub);
    });

    test("should change user with setUser", () => {
      // esm modules use live bindings, so we need to re-import
      // to get the latest value of the user variable
      const user1 = generateTestUser("user1");
      const user2 = generateTestUser("user2");

      setUser(user1);
      expect(user).toBe(user1);

      setUser(user2);
      expect(user).toBe(user2);
    });

    test("should update current user when called multiple times", () => {
      const user1 = generateTestUser("user1");
      const user2 = generateTestUser("user2");

      setUser(user1);
      expect(sessionModule.user).toBe(user1);

      setUser(user2);
      expect(sessionModule.user).toBe(user2);
    });
  });

  describe("includeAuthHeader", () => {
    test("should register a route handler on the page", () => {
      includeAuthHeader(mockPage);

      expect(mockPage.route).toHaveBeenCalledWith("**/*", expect.any(Function));
    });

    test("should add authorization header for auth-central URLs", async () => {
      const testUser = generateTestUser("authTest");
      setUser(testUser);

      includeAuthHeader(mockPage);

      // Get the route handler function
      const routeHandler = (mockPage.route as any).mock.calls[0][1];

      // Mock auth-central URL
      (mockRequest.url as any).mockReturnValue(
        "https://auth-central.test.augmentcode.com/api/test",
      );
      const headers = { "content-type": "application/json" };
      (mockRequest.headers as any).mockReturnValue(headers);

      await routeHandler(mockRoute);

      expect(mockRoute.continue).toHaveBeenCalledWith({
        headers: {
          "content-type": "application/json",
          authorization: expect.stringMatching(/^Bearer .+/),
        },
      });
    });

    test("should add authorization header for customer UI URLs", async () => {
      const testUser = generateTestUser("customerTest");
      setUser(testUser);

      includeAuthHeader(mockPage);

      const routeHandler = (mockPage.route as any).mock.calls[0][1];

      // Mock customer UI URL
      (mockRequest.url as any).mockReturnValue("https://app.test.augmentcode.com/dashboard");
      const headers = { accept: "text/html" };
      (mockRequest.headers as any).mockReturnValue(headers);

      await routeHandler(mockRoute);

      expect(mockRoute.continue).toHaveBeenCalledWith({
        headers: {
          accept: "text/html",
          authorization: expect.stringMatching(/^Bearer .+/),
        },
      });
    });

    test("should not add authorization header for other URLs", async () => {
      const testUser = generateTestUser("otherTest");
      setUser(testUser);

      includeAuthHeader(mockPage);

      const routeHandler = (mockPage.route as any).mock.calls[0][1];

      // Mock external URL
      (mockRequest.url as any).mockReturnValue("https://external-api.example.com/data");
      const headers = { "user-agent": "test-agent" };
      (mockRequest.headers as any).mockReturnValue(headers);

      await routeHandler(mockRoute);

      expect(mockRoute.continue).toHaveBeenCalledWith({
        headers: {
          "user-agent": "test-agent",
          // No authorization header should be added
        },
      });
    });

    test("should cache bearer tokens for the same user", async () => {
      const testUser = generateTestUser("cacheTest");
      setUser(testUser);

      includeAuthHeader(mockPage);

      const routeHandler = (mockPage.route as any).mock.calls[0][1];

      (mockRequest.url as any).mockReturnValue(
        "https://auth-central.test.augmentcode.com/api/test1",
      );
      (mockRequest.headers as any).mockReturnValue({});

      // First call
      await routeHandler(mockRoute);
      const firstCallHeaders = (mockRoute.continue as any).mock.calls[0][0].headers;

      // Reset mock
      (mockRoute.continue as any).mockClear();

      // Second call with same user
      (mockRequest.url as any).mockReturnValue(
        "https://auth-central.test.augmentcode.com/api/test2",
      );
      await routeHandler(mockRoute);
      const secondCallHeaders = (mockRoute.continue as any).mock.calls[0][0].headers;

      // Should use the same token (cached)
      expect(firstCallHeaders.authorization).toBe(secondCallHeaders.authorization);

      // Secret should only be fetched once
      expect(mockSecretManagerClient.accessSecretVersion).toHaveBeenCalledTimes(1);
    });

    test("should create different tokens for different users", async () => {
      // This test verifies that the token generation works correctly
      // by checking that different user data produces different tokens

      const user1 = generateTestUser("user1");
      const user2 = generateTestUser("user2");

      // Verify users are actually different
      expect(user1.email).not.toBe(user2.email);
      expect(user1.sub).not.toBe(user2.sub);

      // Since the route handler uses the current user at execution time,
      // we'll test the token generation more directly by checking that
      // different users would produce different base64url encoded data

      const user1Data = Buffer.from(JSON.stringify(user1)).toString("base64url");
      const user2Data = Buffer.from(JSON.stringify(user2)).toString("base64url");

      // The encoded data should be different for different users
      expect(user1Data).not.toBe(user2Data);

      // Test that the route handler works with different users by setting
      // different users and verifying the tokens contain the correct user data
      setUser(user1);
      includeAuthHeader(mockPage);
      const routeHandler = (mockPage.route as any).mock.calls[0][1];

      (mockRequest.url as any).mockReturnValue(
        "https://auth-central.test.augmentcode.com/api/test",
      );
      (mockRequest.headers as any).mockReturnValue({});

      await routeHandler(mockRoute);
      const headers = (mockRoute.continue as any).mock.calls[0][0].headers;

      // Verify the token contains user1 data
      const token = headers.authorization.replace("Bearer ", "");
      const tokenData = token.split(".")[0];
      const decodedUser = JSON.parse(Buffer.from(tokenData, "base64url").toString());

      expect(decodedUser.email).toBe(user1.email);
      expect(decodedUser.sub).toBe(user1.sub);
    });
  });

  describe("token creation and secret management", () => {
    test("should call SecretManagerServiceClient with correct parameters", async () => {
      const testUser = generateTestUser("secretTest");
      setUser(testUser);

      includeAuthHeader(mockPage);
      const routeHandler = (mockPage.route as any).mock.calls[0][1];

      (mockRequest.url as any).mockReturnValue(
        "https://auth-central.test.augmentcode.com/api/test",
      );
      (mockRequest.headers as any).mockReturnValue({});

      await routeHandler(mockRoute);

      expect(mockSecretManagerClient.accessSecretVersion).toHaveBeenCalledWith({
        name: "projects/test-project-id/secrets/test-secret-id/versions/latest",
      });
    });

    test("should create HMAC with correct parameters", async () => {
      const testUser = generateTestUser("hmacTest");
      setUser(testUser);

      includeAuthHeader(mockPage);
      const routeHandler = (mockPage.route as any).mock.calls[0][1];

      (mockRequest.url as any).mockReturnValue(
        "https://auth-central.test.augmentcode.com/api/test",
      );
      (mockRequest.headers as any).mockReturnValue({});

      await routeHandler(mockRoute);

      expect(crypto.createHmac).toHaveBeenCalledWith("sha256", "mock-secret-value");
      expect(mockHmac.update).toHaveBeenCalled();
      expect(mockHmac.digest).toHaveBeenCalledWith("base64url");
    });

    test("should handle secret manager errors gracefully", async () => {
      // Mock secret manager to throw an error
      mockSecretManagerClient.accessSecretVersion.mockRejectedValue(new Error("Secret not found"));

      const testUser = generateTestUser("errorTest");
      setUser(testUser);

      includeAuthHeader(mockPage);
      const routeHandler = (mockPage.route as any).mock.calls[0][1];

      (mockRequest.url as any).mockReturnValue(
        "https://auth-central.test.augmentcode.com/api/test",
      );
      (mockRequest.headers as any).mockReturnValue({});

      // Should throw the error
      await expect(routeHandler(mockRoute)).rejects.toThrow("Secret not found");
    });

    test("should handle empty secret payload", async () => {
      // Mock secret manager to return empty payload
      mockSecretManagerClient.accessSecretVersion.mockResolvedValue([
        {
          payload: {
            data: null,
          },
        },
      ]);

      const testUser = generateTestUser("emptySecretTest");
      setUser(testUser);

      includeAuthHeader(mockPage);
      const routeHandler = (mockPage.route as any).mock.calls[0][1];

      (mockRequest.url as any).mockReturnValue(
        "https://auth-central.test.augmentcode.com/api/test",
      );
      (mockRequest.headers as any).mockReturnValue({});

      // Should throw an error for empty secret
      await expect(routeHandler(mockRoute)).rejects.toThrow("Secret 'test-secret-id' is empty");
    });

    test("should create bearer token with correct format", async () => {
      const testUser = generateTestUser("formatTest");
      setUser(testUser);

      // Mock base64url encoding behavior
      const mockBuffer = {
        toString: vi.fn().mockReturnValue("mock-base64url-data"),
      };
      vi.spyOn(Buffer, "from").mockReturnValue(mockBuffer as any);

      includeAuthHeader(mockPage);
      const routeHandler = (mockPage.route as any).mock.calls[0][1];

      (mockRequest.url as any).mockReturnValue(
        "https://auth-central.test.augmentcode.com/api/test",
      );
      (mockRequest.headers as any).mockReturnValue({});

      await routeHandler(mockRoute);

      const authHeader = (mockRoute.continue as any).mock.calls[0][0].headers.authorization;

      // Should have Bearer prefix and correct format
      expect(authHeader).toMatch(/^Bearer mock-base64url-data\.sig-\d+-.+$/);

      // Restore original Buffer.from
      (Buffer.from as any).mockRestore();
    });
  });
});
