import chalk from "chalk";
import type { <PERSON><PERSON><PERSON>, BrowserContext, Page, TestInfo } from "@playwright/test";
import { testInfo } from "./fixtures";
import { forwardBrowserLogs } from "./logs";
import type { TestUser } from "./user";
import { includeAuth<PERSON>eader } from "./auth";
import { type PageExt, patchLocatorMethods, patchScreenshot } from "./playwright-extensions";

export function clearSession() {
  [browser, browserContext, page, user] = [] as any;
}

export let browser: Browser;

export function setBrowser(nextBrowser: Browser): Browser {
  browser = nextBrowser;
  return browser;
}

export let browserContext: BrowserContext;

export function setBrowserContext(nextBrowserContext: BrowserContext): BrowserContext {
  browserContext = nextBrowserContext;
  return browserContext;
}

export let page: PageExt;

export function setPage(nextPage: Page): PageExt {
  page = extendPage(nextPage, testInfo!);
  return page;
}

const pages = new WeakSet<PageExt | Page>();

export function extendPage(page: Page, testInfo: TestInfo): PageExt {
  if (!testInfo) {
    throw new Error("testInfo is not set");
  }
  if (pages.has(page)) {
    return page as PageExt;
  }
  pages.add(page);
  let lastUrl = "";
  page.on("framenavigated", (frame) => {
    if (frame === page.mainFrame() && frame.url() !== lastUrl) {
      lastUrl = frame.url();
      console.info(chalk.gray(`navigated to ${frame.url()}`));
    }
  });
  forwardBrowserLogs(page);
  includeAuthHeader(page);
  patchScreenshot(page, testInfo);
  patchLocatorMethods(page);
  return page as PageExt;
}

export let user: TestUser;

export function setUser(nextUser: TestUser): TestUser {
  user = nextUser;
  return user;
}
