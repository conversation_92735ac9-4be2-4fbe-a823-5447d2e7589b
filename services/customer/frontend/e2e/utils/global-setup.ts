import { rmSync } from "fs";
import { dirname, resolve } from "path";
import { fileURLToPath } from "url";

async function globalSetup() {
  console.info("🧹 Cleaning up test directories before starting tests...");

  const utilsDir = dirname(fileURLToPath(import.meta.url));
  const reportDir = resolve(utilsDir, "../report");
  const resultsDir = resolve(utilsDir, "../results");

  try {
    // Remove report directory if it exists
    rmSync(reportDir, { recursive: true, force: true });
    console.info("  - Cleaned up report directory");
  } catch (error) {
    console.info("  - Report directory doesn't exist or couldn't be cleaned");
  }

  try {
    // Remove results directory if it exists
    rmSync(resultsDir, { recursive: true, force: true });
    console.info("  - Cleaned up results directory");
  } catch (error) {
    console.info("  - Results directory doesn't exist or couldn't be cleaned");
  }
}

export default globalSetup;
