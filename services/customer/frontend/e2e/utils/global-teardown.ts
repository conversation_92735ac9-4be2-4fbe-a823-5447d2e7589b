import { convertPlaywrightVideos } from "./video-converter";
import { resolve, dirname } from "path";
import { fileURLToPath } from "url";

/**
 * Global teardown function for Playwright tests
 * This runs after all tests have completed and converts WebM videos to MP4 format
 */
async function globalTeardown() {
  console.info("Running global teardown - converting videos to MP4 format...");

  try {
    // Convert videos in the default output directory
    const __filename = fileURLToPath(import.meta.url);
    const __dirname = dirname(__filename);
    const outputDir = resolve(__dirname, "../results");
    convertPlaywrightVideos(outputDir);

    console.info("Video conversion completed successfully");
  } catch (error) {
    console.error("Error during video conversion in global teardown:", error);
    // Don't throw the error to avoid failing the test run
  }
}

export default globalTeardown;
