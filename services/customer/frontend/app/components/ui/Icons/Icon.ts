import {
  PersonI<PERSON>,
  LightningBoltIcon,
  LockClosedIcon,
  RocketIcon,
  ClockIcon,
} from "@radix-ui/react-icons";
import type { IconProps as RadixIconProps } from "@radix-ui/themes";
import { forwardRef, createElement } from "react";

// Icon mapping from string names to Radix UI components
const iconMapping = {
  person: PersonIcon,
  clock: ClockIcon,
  rocket: RocketIcon,
  lightning: LightningBoltIcon,
  lock: LockClosedIcon,
} as const;

// Type for Radix UI icon components - constrained to only the icons we actually use
export type IconName = keyof typeof iconMapping;

export type IconProps = React.ComponentProps<typeof Icon>;

/**
 * Displays an icon based on a string name.
 *
 * @param name - The string name of the icon
 * @returns The corresponding Radix UI icon component
 *
 * @example
 * ```tsx
 * <Icon name="person" />;
 * ```
 */
export const Icon = forwardRef<
  SVGSVGElement,
  { name: IconName } & RadixIconProps
>(function Icon({ name, ...props }, ref) {
  if (!iconMapping[name]) {
    throw new Error(
      `Icon not found: ${name}. Available icons: ${Object.keys(iconMapping).join(", ")}`,
    );
  }
  return createElement(iconMapping[name], { ...props, ref });
});
