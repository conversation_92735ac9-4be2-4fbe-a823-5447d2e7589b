import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import ShareLayout from "../ShareLayout";
import { formatISO, subMinutes, subSeconds } from "date-fns";
import { piDay } from "app/mocks";

const meta: Meta<typeof ShareLayout> = {
  title: "Layouts/Share Layout",
  component: ShareLayout,
};

export default meta;

type Story = StoryObj<typeof ShareLayout>;

const mockChat = [
  {
    requestId: "1",
    message: "What is this repo about?",
    response: `This repository is for the Requests library, which is a popular HTTP library for Python. Based on the information provided in the excerpts, here are some key points about Requests:

1. It's described as "an elegant and simple HTTP library for Python, built for human beings."
2. The library allows users to send HTTP/1.1 requests easily, without the need to manually add query strings to URLs or form-encode POST data.
3. It's one of the most downloaded Python packages, with around 30 million downloads per week.`,
    lastRequestTimestamp: subSeconds(piDay, 100), // 100 seconds ago
  },
  {
    requestId: "2",
    message: "Do we know that the backend can hundle this?",
    response: `You're right to be cautious about this. Let's review what we know about the backend based on the provided file excerpts:

1. In the services/ap_proxy/server/sxc/handlers.rs file, we see a CompletionRequest being handled, but there's no explicit mention of a temperature parameter.
2. The research/model server/completion handlers.py file shows some completion handling, but again, there's no clear indication of a temperature parameter.
3. The research/apis/completionutils.py file does mention a temperature parameter in the context of a TritonClient:

\`\`\`js
/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./app/**/*.{js,ts,jsx,tsx,mdx}",
    "./pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./components/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    extend: {},
  },
  plugins: [],
  corePlugins: {
    preflight: false, // This disables Tailwind's reset styles
  },
  presets: [require('tailwindcss/defaultConfig'), require('tailwindcss/nesting')],
}
\`\`\``,
    lastRequestTimestamp: subMinutes(piDay, 1), // 1 minute ago
  },
  {
    requestId: "3",
    message: "Do we know that the backend can hundle this?",
    response: `
Here's an ordered list with code blocks:

1. First, create a basic Python class:

\`\`\`python path=example/veryLongFileNameToTestSomeOverlapingIssues.py mode=EDIT
class User:
    def __init__(self, name):
        self.name = name
\`\`\`

2. Here is a Mermaid diagram:

\`\`\`mermaid path=diagrams/login_flow.md mode=EDIT
graph TD
    A[Start] --> B{Is user logged in?}
    B -- Yes --> C[Show Dashboard]
    B -- No --> D[Show Login Form]
    D --> E[Validate Credentials]
    E -- Valid --> C
    E -- Invalid --> D
    C --> F[End]
\`\`\`

3. Finally, use it like this:

\`\`\`python path=example/main.py mode=EDIT
user = User("Alice")
print(user.greet())
\`\`\`

And here's an unordered list:

- Basic data types:
  - integers
  - strings
  - booleans
- Control structures:
  - if/else
  - loops
  - switches
- Object-oriented concepts:
  - classes
  - inheritance
  - polymorphism


`,
    lastRequestTimestamp: subSeconds(piDay, 3),
  },
  {
    requestId: "4",
    message: "A very long question \n\n ",
    response: `This repository is for the Requests library, which is a popular HTTP library for Python. Based on the information provided in the excerpts, here are some key points about Requests:

1. It's described as "an elegant and simple HTTP library for Python, built for human beings."
2. The library allows users to send HTTP/1.1 requests easily, without the need to manually add query strings to URLs or form-encode POST data.
3. It's one of the most downloaded Python packages, with around 30 million downloads per week.`,
    lastRequestTimestamp: piDay,
  },
  {
    requestId: "5",
    message: `I'm getting this error when trying to run my React application with TypeScript. I've been stuck on this for hours and can't figure out what's wrong:

ERROR in ./src/components/UserProfile.tsx
TypeScript error: Property 'user' does not exist on type 'Props'.ts(2339)

Here's my component code:

\`\`\`typescript
interface Props {
  userId: string;
  onUpdate: (data: any) => void;
}

const UserProfile: React.FC<Props> = ({ user, onUpdate }) => {
  return (
    <div className="profile-container">
      <h1>{user.name}</h1>
      <p>{user.email}</p>
    </div>
  );
};
\`\`\`

I've tried:
1. Checking all my type definitions
2. Updating my tsconfig.json
3. Reinstalling node_modules

Can you help me identify what's causing this error and how to fix it?`,
    response: `I can see the issue in your code. The error occurs because you've defined a Props interface that only includes 'userId' and 'onUpdate', but you're trying to destructure a 'user' prop that isn't declared in the interface.

Here's how to fix it:

\`\`\`typescript
interface Props {
  userId: string;
  user: {
    name: string;
    email: string;
  };
  onUpdate: (data: any) => void;
}

const UserProfile: React.FC<Props> = ({ user, userId, onUpdate }) => {
  return (
    <div className="profile-container">
      <h1>{user.name}</h1>
      <p>{user.email}</p>
    </div>
  );
};
\`\`\`

The error occurred because:
1. Your Props interface didn't include the 'user' property
2. TypeScript strictly checks prop types
3. The component was trying to access properties of an undefined prop

Additional recommendations:
- Consider using a more specific type for the onUpdate callback
- Add optional properties with '?' if they're not always required
- Use proper TypeScript types instead of 'any' where possible`,
    lastRequestTimestamp: subSeconds(piDay, 45), // 45 seconds ago
  },
];

export const Default: Story = {
  args: {
    title: "Sample Chat",
    date: formatISO(piDay),
    chat: mockChat,
    reader_email: "<EMAIL>",
    author_email: "<EMAIL>",
    // Create a date more than a month ago to trigger the callout
    lastRequestDate: formatISO(piDay), // This will show the callout
  },
};

export const WithoutCallout: Story = {
  args: {
    ...Default.args,
    lastRequestDate: formatISO(piDay), // Recent date won't show the callout
  },
};
