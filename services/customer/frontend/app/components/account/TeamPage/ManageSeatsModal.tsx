import { Button } from "@radix-ui/themes";
import { useMutation, useQuery } from "@tanstack/react-query";
import {
  subscriptionQueryOptions,
  teamQueryOptions,
  updateTeamSeats,
  userQueryOptions,
  paymentQueryOptions,
} from "app/client-cache";
import Modal from "app/components/ui/Modal";
import { isTeamActive } from "app/schemas/team";
import { Tooltip } from "app/components/ui/Tooltip";
import { useEffect, useState } from "react";
import { NumberControl } from "../../ui/NumberControl/NumberControl";
import { Badge } from "app/components/ui/Badge";
import { Callout } from "app/components/ui/Callout";
import { typography } from "app/utils/style";
import { Card } from "app/components/ui/Card/Card";
import * as utils from "./utils";
import { formatMoney } from "app/utils/string";
import { CommunityPlanWarning } from "./CommunityPlanWarning";
import { TrialTeamInviteWarning } from "./TrialTeamInviteWarning";
import { SeatAllocationWarning } from "./SeatAllocationWarning";
import { PaymentMethodCallout } from "./PaymentMethodCallout";
import { OrbPlanInfo_PlanType } from "~services/auth/central/server/auth_pb";

export function ManageSeatsModal() {
  const updateSeatsMutation = useMutation(updateTeamSeats);
  const { data: teamData } = useQuery(teamQueryOptions);
  const { data: userData } = useQuery(userQueryOptions);
  const { data: subscriptionData } = useQuery(subscriptionQueryOptions);
  const { data: paymentData } = useQuery(paymentQueryOptions);
  const userIsAdmin = !!userData?.isAdmin;
  const [seats, setSeats] = useState<number>(NaN);
  useEffect(() => () => setSeats(NaN), []);
  if (!userData || !userIsAdmin || !isTeamActive(teamData) || !subscriptionData)
    return null;
  const planName = subscriptionData.planName ?? "plan";
  const { seats: totalSeats, users: teamMembers, invitations } = teamData.team;
  if (isNaN(seats)) {
    setSeats(totalSeats);
    return null;
  }

  function handleUpdateSeats() {
    if (seats === totalSeats) return;
    updateSeatsMutation.mutate(seats);
  }

  const totalMembers = utils.totalCurrentMembers(teamMembers, invitations);
  const maxSeats = subscriptionData.maxNumSeats;
  const isTrialPlan = subscriptionData.planType === OrbPlanInfo_PlanType.TRIAL;
  const needsPaymentMethod =
    utils.additionalSeatsNeeded(seats, totalSeats) > 0 && !isTrialPlan;
  // Default to true so a transient failure to load payment info doesn't block
  // legitimate admins who DO have a payment method from purchasing seats. The
  // backend enforces payment method requirements for seat purchases.
  const hasPaymentMethod = paymentData?.hasPaymentMethod ?? true;

  // If subscription is ending, prevent decreasing seats since changes are effective next billing cycle
  const isSubscriptionEnding = !!subscriptionData.subscriptionEndDate;
  const minSeats = isSubscriptionEnding ? totalSeats : totalMembers;
  return (
    <Modal
      title="Manage Seats"
      onOpenChange={() => {
        setSeats(NaN);
      }}
      description={`Adjust the number of seats on your ${planName}`}
      trigger={(open) => (
        <Button onClick={open} aria-label="Manage Seats" variant="ghost">
          Manage Seats
        </Button>
      )}
      footer={(close) => (
        <>
          <Button variant="soft" onClick={close}>
            Cancel
          </Button>
          {needsPaymentMethod && !hasPaymentMethod ? (
            <Tooltip
              content="A payment method is required to purchase additional seats. Please add a payment method to your account."
              enabled={true}
            >
              <Button onClick={close(handleUpdateSeats)} disabled={true}>
                Save changes
              </Button>
            </Tooltip>
          ) : (
            <Button onClick={close(handleUpdateSeats)}>Save changes</Button>
          )}
        </>
      )}
    >
      <div className="plan-info">
        <div className="plan-info-header">
          <div className="plan-name">{planName}</div>
          <div className="plan-price">
            Seats: ${subscriptionData.pricePerSeat} per user per month
          </div>
        </div>
        <Badge color="green">Current plan</Badge>
      </div>
      <Card className="seat-card">
        <div className="card-header">
          <div className="card-title">Seats</div>
          <div className="card-description">
            {totalMembers} currently in use
          </div>
        </div>
        <SeatControl
          currentSeats={seats}
          onChange={setSeats}
          minSeats={minSeats}
          maxSeats={maxSeats}
        />
      </Card>
      {!isTrialPlan && (
        <SeatAllocationWarning
          newMembersCount={Math.max(0, seats - totalSeats)}
          additionalSeatsNeeded={Math.max(0, seats - totalSeats)}
        />
      )}
      {needsPaymentMethod && !hasPaymentMethod && (
        <PaymentMethodCallout message="A payment method is required to purchase additional seats." />
      )}
      {seats < totalSeats && (
        <Callout type="info" size="small">
          <div>Removed seats remain active until next billing cycle.</div>
        </Callout>
      )}
      <div className="billing-info">
        <div className="billing-info-row">
          <span>Current monthly billing</span>
          <span>
            {formatMoney(
              totalSeats * parseFloat(subscriptionData.pricePerSeat),
            )}
          </span>
        </div>
        <div className="billing-info-row">
          <span>New monthly billing</span>
          <span>
            {formatMoney(seats * parseFloat(subscriptionData.pricePerSeat))}
          </span>
        </div>
      </div>
      <CommunityPlanWarning />
      <TrialTeamInviteWarning />
      <style scoped>{`
        :scope {
          display: flex;
          flex-direction: column;
          gap: var(--ds-spacing-4);
        }

        .plan-info {
          display: flex;
          flex-direction: row;
          justify-content: space-between;
          align-items: center;
        }

        .plan-info-header {
          display: flex;
          flex-direction: column;
          align-items: start;
          gap: var(--ds-spacing-1);

          .plan-name {
            ${typography.text3.bold}
          }

          .plan-price {
            ${typography.text3.regular}
            color: var(--ds-color-text-subtle);
          }
        }

        .plan-info-header {
          ${typography.text3.regular}
        }

        .seat-card {
          display: flex;
          flex-direction: row;
          justify-content: space-between;
          align-items: center;
        }

        .card-header {
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          align-items: start;
        }

        .card-title {
          ${typography.text2.bold}
        }

        .card-description {
          ${typography.text3.regular}
          color: var(--ds-color-text-subtle);
        }

        .billing-info {
          display: flex;
          flex-direction: column;
          gap: var(--ds-spacing-1);
          ${typography.text3.regular}
          color: var(--ds-color-text-subtle);

          .billing-info-row {
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            align-items: flex-end;
          }
        }
      `}</style>
    </Modal>
  );
}

type SeatControlProps = {
  currentSeats: number;
  onChange: (value: number) => void;
  minSeats: number;
  maxSeats: number;
};

function SeatControl({
  currentSeats,
  onChange,
  minSeats,
  maxSeats,
}: SeatControlProps) {
  const [seats, setSeats] = useState(currentSeats);
  function handleSeatsChange(value: number) {
    if (isNaN(value)) return;
    if (value < minSeats) return;
    setSeats(value);
    onChange(value);
  }
  return (
    <NumberControl
      value={seats}
      min={minSeats}
      max={maxSeats}
      onChange={handleSeatsChange}
    />
  );
}
