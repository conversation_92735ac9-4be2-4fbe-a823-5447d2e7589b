import { mutationOptions } from "../queryOptions";
import { toast } from "app/components/ui/Toast";
import { PaymentMethodResponseSchema } from "app/schemas/payment-method";

export const managePaymentMethod = mutationOptions({
  mutationFn: async ({
    hasPaymentMethod,
    returnUrl,
  }: {
    hasPaymentMethod: boolean;
    returnUrl?: string;
  }): Promise<PaymentMethodResponseSchema> => {
    const endpoint = hasPaymentMethod
      ? "/api/update-customer-profile"
      : "/api/setup-payment-method";

    const body = returnUrl ? { returnUrl } : {};

    const response = await fetch(endpoint, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(body),
    });

    const data = await response.json();

    if (!response.ok) {
      throw new Error(
        data.error ||
          `Failed to ${hasPaymentMethod ? "update customer profile" : "setup payment method"}`,
      );
    }

    return PaymentMethodResponseSchema.parse(data);
  },
  onSuccess: (data) => {
    // Redirect to the Stripe checkout/portal URL
    if (data.url) {
      window.location.href = data.url;
    } else {
      toast.error({
        title: "Error",
        description: "No URL was returned. Please try again.",
      });
    }
  },
  onError: (error) => {
    console.error("Error managing payment method:", error);
    toast.error({
      title: "Error",
      description: "Failed to manage payment method. Please try again.",
    });
  },
});
