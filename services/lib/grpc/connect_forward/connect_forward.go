package connectforward

import (
	"context"
	"fmt"
	"strings"
	"unsafe"

	"connectrpc.com/connect"
	"github.com/rs/zerolog/log"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/metadata"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/reflect/protoreflect"

	"github.com/augmentcode/augment/base/go/secretstring"
	"github.com/augmentcode/augment/base/proto/redact"
	forwardoptionsproto "github.com/augmentcode/augment/services/lib/grpc/connect_forward/forward_options_proto"
	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
	authoptionsproto "github.com/augmentcode/augment/services/token_exchange/auth_options_proto"
	tokenscopesproto "github.com/augmentcode/augment/services/token_exchange/token_scopes_proto"
)

// ForwardUserInfo contains user identification and context information
// required for forwarding ConnectRPC requests to gRPC services with proper
// user impersonation and authorization.
type ForwardUserInfo struct {
	UserID         string // Unique identifier for the user
	UserEmail      string // User's email address for authentication
	ShardNamespace string // Kubernetes namespace for user's shard
	TenantID       string // Tenant identifier for multi-tenancy support
}

// ForwarderFactoryService defines the interface for services that can forward
// ConnectRPC requests to gRPC backends with proper authentication and authorization.
// Implementations handle user impersonation, token management, and request routing.
type ForwarderFactoryService interface {
	// GetServiceToken fetches the proper authentication token for this request
	GetServiceToken(ctx context.Context, userInfo *ForwardUserInfo, routeInfo *RouteInfo, req *connect.Request[any], scopes []tokenscopesproto.Scope) (*secretstring.SecretString, error)

	// GetForwardUserInfo extracts user information from the ConnectRPC request
	GetForwardUserInfo(ctx context.Context, req *connect.Request[any]) (*ForwardUserInfo, error)

	GetServiceDescriptor() protoreflect.ServiceDescriptor

	GetConnFactory() GrpcConnFactory

	// RequestSource returns the source identifier for requests
	GetRequestSource() string
}

type forwardMap struct {
	ffs         ForwarderFactoryService
	serviceName string
	methodName  string
	scopes      []tokenscopesproto.Scope
}

type ForwarderFactory struct {
	services  map[string]ForwarderFactoryService // Map of service names to forwarder services
	procedure map[string]forwardMap
}

func NewForwarderFactory() *ForwarderFactory {
	return &ForwarderFactory{
		services:  make(map[string]ForwarderFactoryService),
		procedure: make(map[string]forwardMap),
	}
}

func (ff *ForwarderFactory) GetService(serviceName string) ForwarderFactoryService {
	return ff.services[serviceName]
}

func (ff *ForwarderFactory) AddService(ffs ForwarderFactoryService) {
	serviceName := string(ffs.GetServiceDescriptor().FullName())
	log.Info().Msgf("Adding service %s", serviceName)
	ff.services[serviceName] = ffs
}

func (ff *ForwarderFactory) AddMethodsFromFileDescriptor(fd protoreflect.FileDescriptor) error {
	for i := 0; i < fd.Services().Len(); i++ {
		sd := fd.Services().Get(i)
		for j := 0; j < sd.Methods().Len(); j++ {
			method := sd.Methods().Get(j)
			opts := method.Options()
			if !proto.HasExtension(opts, forwardoptionsproto.E_ForwardMethodName) {
				return fmt.Errorf("method %s does not have forward method name", method.FullName())
			}
			// The frontend procedure name is constructed from the current service and method
			// ConnectRPC procedures start with a leading slash
			frontendProcedureName := fmt.Sprintf("/%s/%s", sd.FullName(), method.Name())
			// The backend procedure name comes from the proto extension
			backendProcedureName := proto.GetExtension(opts, forwardoptionsproto.E_ForwardMethodName).(string)
			forwardMethodName, err := getMethodFromProcedure(backendProcedureName)
			if err != nil {
				return err
			}
			forwardServiceName, err := getServiceFromProcedure(backendProcedureName)
			if err != nil {
				return err
			}
			ffs := ff.GetService(forwardServiceName)
			if ffs == nil {
				return fmt.Errorf("service %s not found", forwardServiceName)
			}
			// check that the method exists
			if ffs.GetServiceDescriptor().Methods().ByName(protoreflect.Name(forwardMethodName)) == nil {
				return fmt.Errorf("method %s not found", forwardMethodName)
			}
			scopes := getTokenScopes(ffs.GetServiceDescriptor(), forwardMethodName)
			ff.procedure[frontendProcedureName] = forwardMap{
				ffs:         ffs,
				serviceName: forwardServiceName,
				methodName:  forwardMethodName,
				scopes:      scopes,
			}
		}
	}
	return nil
}

// getTokenScopes extracts required token scopes from gRPC method annotations.
// It looks for the RequiredTokenScopes extension in the method options and
// returns the list of scopes needed to authorize calls to that method.
func getTokenScopes(sd protoreflect.ServiceDescriptor, methodName string) []tokenscopesproto.Scope {
	method := sd.Methods().ByName(protoreflect.Name(methodName))
	opts := method.Options()
	if !proto.HasExtension(opts, authoptionsproto.E_RequiredTokenScopes) {
		return nil
	}

	scopes := proto.GetExtension(opts, authoptionsproto.E_RequiredTokenScopes).([]tokenscopesproto.Scope)
	return scopes
}

// statusErrorToConnect converts gRPC status errors to ConnectRPC errors
// with appropriate error codes and messages for client consumption.
func StatusErrorToConnect(err error) error {
	if st, ok := status.FromError(err); ok {
		switch st.Code() {
		case codes.NotFound:
			return connect.NewError(connect.CodeNotFound, err)
		case codes.PermissionDenied:
			return connect.NewError(connect.CodePermissionDenied, fmt.Errorf("permission denied: %w", err))
		case codes.InvalidArgument:
			return connect.NewError(connect.CodeInvalidArgument, fmt.Errorf("invalid request: %w", err))
		default:
			return connect.NewError(connect.CodeInternal, fmt.Errorf("call failed: %w", err))
		}
	}
	return connect.NewError(connect.CodeInternal, fmt.Errorf("call failed: %w", err))
}

// getMethodFromProcedure extracts the method name from a ConnectRPC procedure string.
// Procedure strings are in the format "/service.name/MethodName" and this function
// returns the last component (MethodName).
func getMethodFromProcedure(procedure string) (string, error) {
	parts := strings.Split(procedure, "/")
	if len(parts) < 2 {
		return "", fmt.Errorf("invalid procedure: %s", procedure)
	}
	return parts[len(parts)-1], nil
}

func getServiceFromProcedure(procedure string) (string, error) {
	parts := strings.Split(procedure, "/")
	if len(parts) < 2 {
		return "", fmt.Errorf("invalid procedure: %s", procedure)
	}
	return parts[0], nil
}

// extractRequestIDs extracts request ID and session ID from Connect request headers
// Returns generated IDs with warnings if headers are missing
func extractRequestIDs[T any](req *connect.Request[T]) (requestcontext.RequestId, requestcontext.RequestSessionId) {
	const (
		requestIdHeader        = "x-request-id"
		requestSessionIdHeader = "x-request-session-id"
	)

	// Extract request ID
	requestIdStr := req.Header().Get(requestIdHeader)
	var requestId requestcontext.RequestId
	if requestIdStr == "" {
		log.Warn().Msg("Request ID not set in headers, generating new ID")
		requestId = requestcontext.NewRandomRequestId()
	} else {
		requestId = requestcontext.RequestId(requestIdStr)
	}

	// Extract session ID
	sessionIdStr := req.Header().Get(requestSessionIdHeader)
	var sessionId requestcontext.RequestSessionId
	if sessionIdStr == "" {
		log.Warn().Msg("Session ID not set in headers, defaulting to request ID")
		sessionId = requestcontext.RequestSessionId(string(requestId))
	} else {
		sessionId = requestcontext.RequestSessionId(sessionIdStr)
	}

	return requestId, sessionId
}

type RouteInfo struct {
	ShardNamespace string
	TenantID       string
}

// Invoke executes a gRPC method call using the configured connection.
// It constructs the full method path and delegates to the gRPC connection.
func (ff *ForwarderFactory) Invoke(ctx context.Context, ffs ForwarderFactoryService, serviceName string, methodName string, msg proto.Message, resp any, routeInfo *RouteInfo) error {
	methodPath := fmt.Sprintf("/%s/%s", serviceName, methodName)
	log.Info().Msgf("Calling gRPC method %s", methodPath)
	namespace := ""
	if routeInfo != nil {
		namespace = routeInfo.ShardNamespace
	}
	conn, err := ffs.GetConnFactory().GetConn(ctx, serviceName, namespace)
	if err != nil {
		return connect.NewError(connect.CodeInternal, fmt.Errorf("failed to get connection: %w", err))
	}
	log.Info().Msgf("Request: %v", redact.ToRedactString(msg))
	return conn.Invoke(ctx, methodPath, msg, resp)
}

// Invoke executes a gRPC method call using the configured connection.
// It constructs the full method path and delegates to the gRPC connection.
func invoke(ctx context.Context, connFactory GrpcConnFactory, serviceName string, methodName string, msg proto.Message, resp any, routeInfo *RouteInfo) error {
	methodPath := fmt.Sprintf("/%s/%s", serviceName, methodName)
	log.Info().Msgf("Calling gRPC method %s", methodPath)
	namespace := ""
	if routeInfo != nil {
		namespace = routeInfo.ShardNamespace
	}
	conn, err := connFactory.GetConn(ctx, serviceName, namespace)
	if err != nil {
		return connect.NewError(connect.CodeInternal, fmt.Errorf("failed to get connection: %w", err))
	}
	return conn.Invoke(ctx, methodPath, msg, resp)
}

// Forward is the main function that forwards ConnectRPC requests to gRPC services
// with proper user impersonation, authentication, and authorization.
//
// Type parameters:
//   - TConnectReq: The ConnectRPC request message type
//   - TConnectResp: The ConnectRPC response message type
//
// Parameters:
//   - ctx: Request context
//   - ffs: ForwarderFactoryService implementation that handles the forwarding logic
//   - req: The incoming ConnectRPC request
//   - resp: Pointer to the response object to populate
//   - reqTransform: Optional function to transform the request before forwarding
//   - respTransform: Optional function to transform the response after receiving
//
// Returns the ConnectRPC response or an error if the forwarding fails.
// This function handles token creation, request context setup, gRPC invocation,
// and error translation between gRPC and ConnectRPC formats.
func Forward[TConnectReq, TConnectResp any](
	ctx context.Context,
	ff *ForwarderFactory,
	req *connect.Request[TConnectReq],
	resp *TConnectResp,
	reqTransform func(ctx context.Context, userInfo *ForwardUserInfo, req *TConnectReq) (*RouteInfo, error),
	respTransform func(ctx context.Context, userInfo *ForwardUserInfo, resp *TConnectResp) error,
) (*connect.Response[TConnectResp], error) {
	anyReq := (*connect.Request[any])(unsafe.Pointer(req))
	log.Info().Msgf("Forwarding request %s", req.Spec().Procedure)
	forwardMap, ok := ff.procedure[req.Spec().Procedure]
	if !ok {
		return nil, connect.NewError(connect.CodeNotFound, fmt.Errorf("procedure %s not found", req.Spec().Procedure))
	}
	ffs := forwardMap.ffs
	requestId, sessionId := extractRequestIDs(req)
	requestCtx := requestcontext.New(
		requestId,
		sessionId,
		ffs.GetRequestSource(),
		secretstring.SecretString{}, // no token, we add it later
	)
	ctx = requestcontext.NewIncomingContext(ctx, requestCtx)

	userInfo, err := ffs.GetForwardUserInfo(ctx, anyReq)
	if err != nil {
		log.Warn().Err(err).Msg("Failed to get forward user info")
		return nil, err
	}

	var routeInfo *RouteInfo
	if reqTransform != nil {
		routeInfo, err = reqTransform(ctx, userInfo, req.Msg)
		if err != nil {
			log.Warn().Err(err).Msg("Request transform failed")
			return nil, StatusErrorToConnect(err)
		}
	}
	token, err := ffs.GetServiceToken(ctx, userInfo, routeInfo, anyReq, forwardMap.scopes)
	if err != nil {
		log.Warn().Err(err).Msg("Failed to get request context")
		return nil, err
	}
	requestCtx = requestCtx.WithAuthToken(*token)
	ctx = requestCtx.AnnotateLogContext(ctx)
	ctx = requestcontext.NewOutgoingContext(ctx, requestCtx)
	ctx = metadata.NewIncomingContext(ctx, requestCtx.ToMetadata())
	ctx = metadata.NewOutgoingContext(ctx, requestCtx.ToMetadata())
	msg := req.Any().(proto.Message)
	log.Info().Msgf("Request: %v", redact.ToRedactString(msg))
	err = ff.Invoke(ctx, ffs, forwardMap.serviceName, forwardMap.methodName, msg, resp, routeInfo)
	if err != nil {
		log.Warn().Err(err).Msg("gRPC call failed")
		return nil, StatusErrorToConnect(err)
	}
	if respTransform != nil {
		err = respTransform(ctx, userInfo, resp)
		if err != nil {
			log.Warn().Err(err).Msg("Response transform failed")
			return nil, StatusErrorToConnect(err)
		}
	}
	connectResp := connect.NewResponse(resp)
	respMsg := connectResp.Any().(proto.Message)
	log.Info().Msgf("Response: %v", redact.ToRedactString(respMsg))
	return connectResp, nil
}
