# Notification Service

The Notification service is a gRPC server that stores and retrieves user notifications.
it is a service in a shard namespace, but it is often called from central services.

## Interface

See notification.proto for the GRPC protocol definition.

## Consistency

The notification service is strongly consistent.
Notifications are stored in BigTable and are retrieved atomically.

## Security

The Notification server is secured using mTLS. The tenant is protected by service tokens.
The user notifications are only available to the same user.
To create new notifications or delete notifications, the NOTIFICATION_ADMIN scope is required.

## Persistence

The notification service uses BigTable for persistence.
The `Notification` table is a user-keyed table. The user id is automatically prefixed to the row key.
By default, users can only read/write their own rows. For non-enterprise users, the rows are maintained
when the user switches tenants.

It has two column families:
- `notifications`: Stores pending notifications for users with a TTL of 30 days
- `receipts`: Stores received notifications with a TTL of 7 days

## Data Classifications

Any data managed by the notification service is considered personal data (keyed on `opaque_user_id`), but not necessariliy PII.
However, the message might contain email addresses or named, which would be PII.

The data is considered confidental, but not restricted.

## Row Keys

Row keys are formatted as:
- For regular notifications: `{opaque_user_id}#{notification_id}`
- For categorized notifications: `{opaque_user_id}#{category}`

This allows efficient retrieval of all notifications for a specific user and enables category-based grouping.


## Usage

To create a notification for a user:
```go
resp, err := notificationClient.CreateNotification(ctx, &notificationproto.CreateNotificationRequest{
    OpaqueUserId: "user123",
    Level: notificationproto.NotificationLevel_NOTIFICATION_LEVEL_INFO,
    DisplayType: notificationproto.NotificationDisplayType_DISPLAY_TYPE_BANNER,
    NotificationName: "message-1",
    Message: "This is a notification message",
    ActionItems: []*notificationproto.ActionItem{
        {
            Title: "View Details",
            Url: "https://example.com/details",
        },
    },
})
```

### Notification Names

Each message has a notification name, which is a caller defined notification identifier.
It serves as idempotency key.
Each caller needs to set the dedupliaton key. A caller can create a message with a notification name
multiple times. This is then treated as an update. If the user already marked the message as seen, the
message will not be shown again.

The design a dedupliation key was choosen to avoid having to store state in the caller, e.g.
the notification name can define the mapping from invoice id to notifiation. We do not manually need
to store the mapping.

The callers are responsible for choosing a notification name that doesn't create conflicts with
other services.
