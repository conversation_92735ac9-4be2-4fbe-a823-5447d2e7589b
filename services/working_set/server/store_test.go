package main

import (
	"context"
	"encoding/binary"
	"testing"
	"time"

	"cloud.google.com/go/pubsub"
	blob_names "github.com/augmentcode/augment/base/blob_names"
	fakebigtableproxy "github.com/augmentcode/augment/services/bigtable_proxy/client/fake_client"
	bigtable_pb "github.com/augmentcode/augment/services/bigtable_proxy/proto"
	checkpoint_indexer "github.com/augmentcode/augment/services/checkpoint_indexer/proto"
	contentmanagerclient "github.com/augmentcode/augment/services/content_manager/client"
	content_manager_pb "github.com/augmentcode/augment/services/content_manager/proto"
	tenantwatcherclient "github.com/augmentcode/augment/services/tenant_watcher/client"
	tenantwatcher_pb "github.com/augmentcode/augment/services/tenant_watcher/proto"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
	"google.golang.org/protobuf/proto"
)

const testSubName string = "test-message-verification-sub"

func prepare_store_fixture(
	ctx context.Context,
	t *testing.T,
	minimumBlobs int,
	checkpointBlobNames *[]blob_names.BlobName,
) (WorkingSetStore, *pubsub.Subscription, *contentmanagerclient.MockContentManagerClient, BigtableHelper, func()) {
	pubsubFixture := NewPubSubFixture(t, "test-ann-index-creation-topic", "test-ann-index-creation-sub")

	fakeRequestContextHelper := &FakeRequestContextHelper{}
	mockContentManagerClient := contentmanagerclient.NewMockContentManagerClient()
	fakeBigtable := fakebigtableproxy.NewInMemoryBigtable([]string{bigtable_pb.TableName_WORKING_SET.String()})
	mockTenantWatcherClient := &tenantwatcherclient.MockTenantWatcherClient{
		Tenants: []*tenantwatcher_pb.Tenant{
			{Id: "tenant1", Name: "Tenant 1"},
		},
	}

	testSub, err := pubsubFixture.Client.CreateSubscription(ctx, testSubName, pubsub.SubscriptionConfig{
		Topic: pubsubFixture.Topic,
	})
	require.NoError(t, err, "Failed to create test subscription")

	persistToBigtableFn := func() bool { return true } // persist to Bigtable
	bigtableHelper := NewBigtableHelper(ctx, fakeRequestContextHelper, fakeBigtable, persistToBigtableFn)

	store := NewWorkingSetStore(
		ctx,
		fakeRequestContextHelper,
		mockContentManagerClient,
		bigtableHelper,
		mockTenantWatcherClient,
		10*time.Minute,      // sessionTimeout
		1*time.Minute,       // statsLoop
		5*time.Minute,       // cleanupLoop
		1*time.Hour,         // checkpointRenewalLoop
		1*time.Hour,         // checkpointRenewalInterval
		30*time.Minute,      // checkpointBlobCacheTTL
		100,                 // checkpointBlobCacheSizeInMB
		false,               // publishBlobIndexMetrics
		24*time.Hour,        // checkpointExpirationTime
		30*time.Minute,      // annIndexBlobCacheTTL
		100,                 // annIndexBlobCacheSizeInMB
		pubsubFixture.Topic, // annIndexCreationTopic
		pubsubFixture.Sub,   // annIndexCreationSub
		24*time.Hour,        // transformationKeyExpirationTime
		1*time.Hour,         // pendingAnnIndexCreationTimeout
		1*time.Hour,         // annIndexExpirationTime
		minimumBlobs,        // minimumBlobs
		16,                  // cpuTaskPoolMaxSize
		64,                  // ioTaskPoolSize
		"v1",                // minHashVersion
		"a^",                // checkpointIndexerRegexFilter (matches nothing)
		10000.0,             // allowableRenewalsPerSecond
		true,                // isRunningUnderTest
	)
	if checkpointBlobNames != nil {
		mockContentManagerClient.On("GetAllBlobsFromCheckpoint", mock.AnythingOfType("*context.cancelCtx"), mock.Anything, mock.Anything, mock.Anything).
			Return(*checkpointBlobNames, nil)
	}
	cleanup := func() {
		pubsubFixture.Cleanup()
		testSub.Delete(ctx)
	}
	return store, testSub, mockContentManagerClient, bigtableHelper, cleanup
}

func prepare_checkpoint_blobnames_fixture() []blob_names.BlobName {
	return []blob_names.BlobName{
		blob_names.NewBlobNameFromBytes([]byte("aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa")),
		blob_names.NewBlobNameFromBytes([]byte("bbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbb")),
		blob_names.NewBlobNameFromBytes([]byte("cccccccccccccccccccccccccccccccc")),
	}
}

func handlePubSubMessages(ctx context.Context, testSub *pubsub.Subscription, onMessage func(msg *pubsub.Message), onErr func(e error), onTimeout func()) {
	receiveCtx, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()

	msgChan := make(chan *pubsub.Message, 1)
	errChan := make(chan error, 1)

	go func() {
		err := testSub.Receive(receiveCtx, func(ctx context.Context, msg *pubsub.Message) {
			msgChan <- msg
			msg.Ack()
			cancel() // Cancel after receiving the first message
		})
		if err != nil && err != context.Canceled {
			errChan <- err
		}
	}()

	// Wait for a message or timeout
	select {
	case msg := <-msgChan:
		onMessage(msg)
	case err := <-errChan:
		onErr(err)
	case <-receiveCtx.Done():
		if receiveCtx.Err() == context.DeadlineExceeded {
			onTimeout()
		}
	}
}

func TestWorkingSetStore_TriggerAnnIndexCreation(t *testing.T) {
	// Arrange
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	checkpointBlobNames := prepare_checkpoint_blobnames_fixture()
	store, testSub, mockContentManagerClient, bigtableHelper, cleanup := prepare_store_fixture(ctx, t, 1, &checkpointBlobNames)
	defer cleanup()

	err := store.Run(ctx)
	assert.NoError(t, err)

	// Act
	checkpointId := "checkpoint1"
	tenantId := "tenant1"
	transformationKey := "transformation1"
	err = store.CreateAnnIndexForCheckpoint(ctx, checkpointId, tenantId, transformationKey)
	assert.NoError(t, err)

	// Wait for the goroutine that processes the ANN index to complete
	err = store.WaitForAsyncProcessing(ctx)
	assert.NoError(t, err)

	// Assert
	mockContentManagerClient.AssertExpectations(t)
	// Verify through bigtableHelper that checkpoint1 was marked as an active checkpoint
	checkpoints, err := bigtableHelper.GetAllActiveCheckpoints("tenant1")
	assert.NoError(t, err)
	assert.Len(t, checkpoints, 1, "Expected one active checkpoint")
	assert.Equal(t, "checkpoint1", checkpoints[0].CheckpointID, "Expected checkpoint ID to be 'checkpoint1'")
	assert.Less(t, time.Now().Sub(checkpoints[0].Timestamp).Seconds(), 60.0,
		"Expected the checkpoint1 timestamp to be recent (within the last minute)")

	// Verify that the checkpoint blob names can be retrieved from the cache and they match
	cachedBlobNames, err := store.GetCachedCheckpointBlobNames("tenant1", checkpointId)
	assert.NoError(t, err)
	assert.Equal(t, checkpointBlobNames, cachedBlobNames, "Cached blob names should match the original blob names")

	// Verify through bigtableHelper that transformationKey was marked as active
	transformationKeys, err := bigtableHelper.GetAllActiveTransformationKeys("tenant1")
	assert.NoError(t, err)
	assert.Len(t, transformationKeys, 1, "Expected one active transformation key")
	assert.Equal(t, "transformation1", transformationKeys[0].TransformationKey, "Expected transformation key to be 'transformation1'")
	assert.Less(t, time.Now().Sub(transformationKeys[0].Timestamp).Seconds(), 60.0,
		"Expected the transformation key timestamp to be recent (within the last minute)")

	// Verify that a checkpoint_indexer.CreateIndexRequest message has been published on pubsubFixture.Topic
	handlePubSubMessages(ctx, testSub,
		func(msg *pubsub.Message) { // onMessage
			assert.NotEmpty(t, msg.Data, "Message data should not be empty")

			var pubsubMsg checkpoint_indexer.CheckpointIndexerPubsub
			err := proto.Unmarshal(msg.Data, &pubsubMsg)
			require.NoError(t, err, "Failed to unmarshal protobuf message")

			createRequest := pubsubMsg.GetCreateIndexRequest()
			require.NotNil(t, createRequest, "Expected a CreateIndexRequest message")

			assert.Equal(t, "tenant1", createRequest.GetTenantId(), "Expected tenant ID to be 'tenant1'")
			assert.Equal(t, "checkpoint1", createRequest.GetCheckpointId(), "Expected checkpoint ID to be 'checkpoint1'")
			assert.Equal(t, "transformation1", createRequest.GetTransformationKey(), "Expected transformation key to be 'transformation1'")
		},
		func(e error) { // onErr
			t.Fatalf("Error receiving pubsub message: %v", e)
		},
		func() { // onTimeout
			t.Fatal("Timed out waiting for pubsub message")
		},
	)
}

func TestWorkingSetStore_SkipSmallCheckpoints(t *testing.T) {
	// Arrange
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	checkpointBlobNames := []blob_names.BlobName{
		blob_names.NewBlobNameFromBytes([]byte("aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa")),
	}
	store, testSub, mockContentManagerClient, bigtableHelper, cleanup := prepare_store_fixture(ctx, t, 10, &checkpointBlobNames)
	defer cleanup()

	err := store.Run(ctx)
	assert.NoError(t, err)

	// Act
	checkpointId := "checkpoint1"
	tenantId := "tenant1"
	transformationKey := "transformation1"
	err = store.CreateAnnIndexForCheckpoint(ctx, checkpointId, tenantId, transformationKey)
	assert.NoError(t, err)

	// Wait for the goroutine that processes the ANN index to complete
	err = store.WaitForAsyncProcessing(ctx)
	assert.NoError(t, err)

	// Assert
	mockContentManagerClient.AssertExpectations(t)
	// Verify through bigtableHelper that checkpoint1 was not marked as an active checkpoint
	checkpoints, err := bigtableHelper.GetAllActiveCheckpoints("tenant1")
	assert.NoError(t, err)
	assert.Len(t, checkpoints, 1, "Expected one active checkpoint")
	// Verify through bigtableHelper that transformationKey was marked as active
	transformationKeys, err := bigtableHelper.GetAllActiveTransformationKeys("tenant1")
	assert.NoError(t, err)
	assert.Len(t, transformationKeys, 1, "Expected one active transformation key")

	// Verify that the checkpoint blob names can be retrieved from the cache and they match
	cachedBlobNames, err := store.GetCachedCheckpointBlobNames("tenant1", checkpointId)
	assert.NoError(t, err)
	assert.Equal(t, checkpointBlobNames, cachedBlobNames, "Cached blob names should match the original blob names")

	// verify that no messages were published (as best as we can anyways)
	// unfortunately the best way for us to do this is to make sure the read times out
	// without seeing any messages, but this does slow down the test to take at least
	// TIMEOUT seconds
	handlePubSubMessages(ctx, testSub,
		func(msg *pubsub.Message) { // onMessage
			t.Fatalf("Unexpected pubsub message %v", msg)
		},
		func(e error) { // onErr
			t.Fatalf("Unexpected error while receiving pubsub message: %v", err)
		},
		func() { // onTimeout
		},
	)
}

// TestWorkingSetStore_Init tests the initialization path (loading state from Bigtable)
func TestWorkingSetStore_Init(t *testing.T) {
	// Arrange
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// Init() doesn't call GetAllBlobsFromCheckpoint, so we don't need to mock it
	store, _, mockContentManagerClient, bigtableHelper, cleanup := prepare_store_fixture(ctx, t, 1, nil)
	defer cleanup()

	// Set up mock for GetAllActiveCheckpoints
	timestamp := time.Now()
	scheduledRenewalTime := timestamp.Add(1 * time.Hour)
	err := bigtableHelper.WriteActiveCheckpoint("tenant1", "checkpoint1", timestamp, scheduledRenewalTime)
	assert.NoError(t, err)

	// Set up mock for GetAllActiveTransformationKeys
	err = bigtableHelper.WriteActiveTransformationKey("tenant1", "transformation1", timestamp)
	assert.NoError(t, err)

	// Mock GetBestAnnIndex to return an actual index
	indexId := "index123"
	mockContentManagerClient.On("GetBestAnnIndex", mock.AnythingOfType("*context.cancelCtx"), &[]string{"tenant1"}[0], "transformation1", "checkpoint1", mock.AnythingOfType("*requestcontext.RequestContext")).
		Return(&contentmanagerclient.GetBestAnnIndexResult{
			IndexId:      indexId,
			AddedBlobs:   []blob_names.BlobName{},
			RemovedBlobs: []blob_names.BlobName{},
		}, nil)

	// Prepare ANN index blob names and mock the GetAnnIndexBlobInfos call
	annIndexBlobNames := []blob_names.BlobName{
		blob_names.NewBlobNameFromBytes([]byte("aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa")),
		blob_names.NewBlobNameFromBytes([]byte("bbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbb")),
		blob_names.NewBlobNameFromBytes([]byte("cccccccccccccccccccccccccccccccc")),
	}
	mockGetAnnIndexBlobInfos(mockContentManagerClient, "tenant1", "transformation1", indexId, annIndexBlobNames)

	// Test initializing the store
	err = store.Init(ctx)
	assert.NoError(t, err)

	mockContentManagerClient.AssertExpectations(t)

	// Verify that checkpoint1 was loaded properly
	activeCheckpoints, err := store.GetActiveCheckpoints("tenant1")
	assert.NoError(t, err)
	assert.Len(t, activeCheckpoints, 1, "Expected one active checkpoint")
	assert.Equal(t, "checkpoint1", activeCheckpoints[0], "Expected checkpoint ID to be 'checkpoint1'")

	// Verify that transformation1 was loaded properly
	activeTransformationKeys, err := store.GetActiveTransformationKeys("tenant1")
	assert.NoError(t, err)
	assert.Len(t, activeTransformationKeys, 1, "Expected one active transformation key")
	assert.Equal(t, "transformation1", activeTransformationKeys[0], "Expected transformation key to be 'transformation1'")

	// Verify that the index blob names can be retrieved from the cache and they match
	cachedIndexBlobNames, err := store.GetCachedIndexBlobNames("tenant1", "transformation1", indexId)
	assert.NoError(t, err)
	assert.Equal(t, annIndexBlobNames, cachedIndexBlobNames, "Cached index blob names should match the original blob names")

	// Verify that the mapping between checkpoint1 and index123 was established
	annIndexId, err := store.GetAnnIndexMappingForCheckpoint("tenant1", "checkpoint1", "transformation1")
	assert.NoError(t, err)
	assert.Equal(t, indexId, annIndexId, "ANN index ID should match the expected value")
}

// TestWorkingSetStore_NoReindexThreshold tests when delta is below trigger reindexing threshold (no reindexing)
func TestWorkingSetStore_NoReindexThreshold(t *testing.T) {
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	checkpointBlobNames, indexBlobNames := generateCheckpointAndIndexBlobNames(100000, 99000)

	store, testSub, mockContentManagerClient, _, cleanup := prepare_store_fixture(ctx, t, 1, &checkpointBlobNames)
	defer cleanup()

	err := store.Run(ctx)
	assert.NoError(t, err)

	indexId := "test_index_small_delta"
	// Mock GetAnnIndexBlobInfos to return the index blob names
	mockGetAnnIndexBlobInfos(mockContentManagerClient, "tenant1", "transformation1", indexId, indexBlobNames)

	// Mock AddAnnIndexMapping to capture the mapping call
	mockAddAnnIndexMapping(mockContentManagerClient, "tenant1", "transformation1", "checkpoint1", indexId)

	// Manually add the LSH index and candidate to simulate existing index
	store.AddLSHIndex("tenant1", "transformation1", indexId, indexBlobNames)

	// Test the delta thresholding logic by calling CreateAnnIndexForCheckpoint
	err = store.CreateAnnIndexForCheckpoint(ctx, "checkpoint1", "tenant1", "transformation1")
	assert.NoError(t, err)

	err = store.WaitForAsyncProcessing(ctx)
	assert.NoError(t, err)

	mockContentManagerClient.AssertExpectations(t)
	_, err = store.GetAnnIndexMappingForCheckpoint("tenant1", "checkpoint1", "transformation1")
	assert.NoError(t, err)

	// Verify that the checkpoint blob names can be retrieved from the cache and they match
	cachedBlobNames, err := store.GetCachedCheckpointBlobNames("tenant1", "checkpoint1")
	assert.NoError(t, err)
	assert.Equal(t, checkpointBlobNames, cachedBlobNames, "Cached blob names should match the original blob names")

	// Verify that no new index creation was triggered (no pubsub message)
	handlePubSubMessages(ctx, testSub,
		func(msg *pubsub.Message) { // onMessage
			t.Fatalf("Unexpected pubsub message for new index creation: %v", msg)
		},
		func(e error) { // onErr
			t.Fatalf("Unexpected error while receiving pubsub message: %v", e)
		},
		func() { // onTimeout - this is expected
		},
	)
}

// TestWorkingSetStore_ReuseExistingIndexThreshold tests when existing mapping is reused, but reindexing is triggered
func TestWorkingSetStore_ReuseExistingIndexThreshold(t *testing.T) {
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	checkpointBlobNames, indexBlobNames := generateCheckpointAndIndexBlobNames(100000, 94000)

	store, testSub, mockContentManagerClient, _, cleanup := prepare_store_fixture(ctx, t, 1, &checkpointBlobNames)
	defer cleanup()

	err := store.Run(ctx)
	assert.NoError(t, err)

	indexId := "test_index_medium_delta"
	// Mock GetAnnIndexBlobInfos to return the index blob names
	mockGetAnnIndexBlobInfos(mockContentManagerClient, "tenant1", "transformation1", indexId, indexBlobNames)

	// Mock AddAnnIndexMapping to capture the mapping call
	mockAddAnnIndexMapping(mockContentManagerClient, "tenant1", "transformation1", "checkpoint1", indexId)

	// Manually add the LSH index and candidate to simulate existing index
	store.AddLSHIndex("tenant1", "transformation1", indexId, indexBlobNames)

	// Test the delta thresholding logic by calling CreateAnnIndexForCheckpoint
	err = store.CreateAnnIndexForCheckpoint(ctx, "checkpoint1", "tenant1", "transformation1")
	assert.NoError(t, err)

	err = store.WaitForAsyncProcessing(ctx)
	assert.NoError(t, err)

	mockContentManagerClient.AssertExpectations(t)
	_, err = store.GetAnnIndexMappingForCheckpoint("tenant1", "checkpoint1", "transformation1")
	assert.NoError(t, err)

	// Verify that the checkpoint blob names can be retrieved from the cache and they match
	cachedBlobNames, err := store.GetCachedCheckpointBlobNames("tenant1", "checkpoint1")
	assert.NoError(t, err)
	assert.Equal(t, checkpointBlobNames, cachedBlobNames, "Cached blob names should match the original blob names")

	// Verify that a checkpoint_indexer.CreateIndexRequest message has been published on pubsubFixture.Topic
	handlePubSubMessages(ctx, testSub,
		func(msg *pubsub.Message) { // onMessage
			assert.NotEmpty(t, msg.Data, "Message data should not be empty")

			var pubsubMsg checkpoint_indexer.CheckpointIndexerPubsub
			err := proto.Unmarshal(msg.Data, &pubsubMsg)
			require.NoError(t, err, "Failed to unmarshal protobuf message")

			createRequest := pubsubMsg.GetCreateIndexRequest()
			require.NotNil(t, createRequest, "Expected a CreateIndexRequest message")

			assert.Equal(t, "tenant1", createRequest.GetTenantId(), "Expected tenant ID to be 'tenant1'")
			assert.Equal(t, "checkpoint1", createRequest.GetCheckpointId(), "Expected checkpoint ID to be 'checkpoint1'")
			assert.Equal(t, "transformation1", createRequest.GetTransformationKey(), "Expected transformation key to be 'transformation1'")
		},
		func(e error) { // onErr
			t.Fatalf("Error receiving pubsub message: %v", e)
		},
		func() { // onTimeout
			t.Fatal("Timed out waiting for pubsub message")
		},
	)
}

// TestWorkingSetStore_AboveReuseIndexThreshold tests when mapping is NOT reused and reindexing is triggered
func TestWorkingSetStore_AboveReuseIndexThreshold(t *testing.T) {
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	checkpointBlobNames, indexBlobNames := generateCheckpointAndIndexBlobNames(100000, 79000)

	store, testSub, mockContentManagerClient, _, cleanup := prepare_store_fixture(ctx, t, 1, &checkpointBlobNames)
	defer cleanup()

	err := store.Run(ctx)
	assert.NoError(t, err)

	// Mock GetAnnIndexBlobInfos to return the index blob names
	indexId := "test_index_high_delta"
	mockGetAnnIndexBlobInfos(mockContentManagerClient, "tenant1", "transformation1", indexId, indexBlobNames)

	// Manually add the LSH index and candidate to simulate existing index
	store.AddLSHIndex("tenant1", "transformation1", indexId, indexBlobNames)

	// Test the delta thresholding logic by calling CreateAnnIndexForCheckpoint
	err = store.CreateAnnIndexForCheckpoint(ctx, "checkpoint1", "tenant1", "transformation1")
	assert.NoError(t, err)

	err = store.WaitForAsyncProcessing(ctx)
	assert.NoError(t, err)

	// Assert
	mockContentManagerClient.AssertExpectations(t)
	_, err = store.GetAnnIndexMappingForCheckpoint("tenant1", "checkpoint1", "transformation1")
	assert.Error(t, err, "Expected an error since the mapping should not have been reused")

	// Verify that the checkpoint blob names can be retrieved from the cache and they match
	cachedBlobNames, err := store.GetCachedCheckpointBlobNames("tenant1", "checkpoint1")
	assert.NoError(t, err)
	assert.Equal(t, checkpointBlobNames, cachedBlobNames, "Cached blob names should match the original blob names")

	// Verify that a checkpoint_indexer.CreateIndexRequest message has been published on pubsubFixture.Topic
	handlePubSubMessages(ctx, testSub,
		func(msg *pubsub.Message) { // onMessage
			assert.NotEmpty(t, msg.Data, "Message data should not be empty")

			var pubsubMsg checkpoint_indexer.CheckpointIndexerPubsub
			err := proto.Unmarshal(msg.Data, &pubsubMsg)
			require.NoError(t, err, "Failed to unmarshal protobuf message")

			createRequest := pubsubMsg.GetCreateIndexRequest()
			require.NotNil(t, createRequest, "Expected a CreateIndexRequest message")

			assert.Equal(t, "tenant1", createRequest.GetTenantId(), "Expected tenant ID to be 'tenant1'")
			assert.Equal(t, "checkpoint1", createRequest.GetCheckpointId(), "Expected checkpoint ID to be 'checkpoint1'")
			assert.Equal(t, "transformation1", createRequest.GetTransformationKey(), "Expected transformation key to be 'transformation1'")
		},
		func(e error) { // onErr
			t.Fatalf("Error receiving pubsub message: %v", e)
		},
		func() { // onTimeout
			t.Fatal("Timed out waiting for pubsub message")
		},
	)
}

// generateCheckpointAndIndexBlobNames generates a set of random blob names for a checkpoint and an index
// the index blob names will have numMatchingBlobs that match the checkpoint blob names
func generateCheckpointAndIndexBlobNames(numBlobs, numMatchingBlobs int) ([]blob_names.BlobName, []blob_names.BlobName) {
	checkpointBlobNames := make([]blob_names.BlobName, numBlobs)
	indexBlobNames := make([]blob_names.BlobName, numBlobs)
	for i := 0; i < numBlobs; i++ {
		b, err := generateRandomByteArr(i, 'a')
		if err != nil {
			panic(err)
		}
		checkpointBlobNames[i] = blob_names.NewBlobNameFromBytes(b)
	}
	for i := 0; i < numMatchingBlobs; i++ {
		indexBlobNames[i] = checkpointBlobNames[i]
	}
	for i := numMatchingBlobs; i < numBlobs; i++ {
		b, err := generateRandomByteArr(i, 'b')
		if err != nil {
			panic(err)
		}
		indexBlobNames[i] = blob_names.NewBlobNameFromBytes(b)
	}
	return checkpointBlobNames, indexBlobNames
}

// generateRandomByteArr generates a random byte array of length 32
func generateRandomByteArr(index int, padding byte) ([]byte, error) {
	b := make([]byte, 32)

	// Encode index `i` into the first 8 bytes
	binary.BigEndian.PutUint64(b[:8], uint64(index))

	// Fill the rest with 'a'
	for j := 8; j < 32; j++ {
		b[j] = padding
	}
	return b, nil
}

// mockGetAnnIndexBlobInfos mocks the GetAnnIndexBlobInfos call on the content manager client
func mockGetAnnIndexBlobInfos(mockContentManagerClient *contentmanagerclient.MockContentManagerClient, tenantId, transformationKey, indexId string, indexBlobNames []blob_names.BlobName) {
	annIndexBlobInfos := make([]contentmanagerclient.AnnIndexBlobInfo, len(indexBlobNames))

	for i, blobName := range indexBlobNames {
		annIndexBlobInfos[i] = contentmanagerclient.AnnIndexBlobInfo{
			BlobName:   blobName,
			ChunkCount: 1,
		}
	}

	annIndexBlobInfosResult := contentmanagerclient.GetAnnIndexBlobInfosResult{
		Err:   nil,
		Infos: annIndexBlobInfos,
	}

	mockContentManagerClient.On("GetAnnIndexBlobInfos",
		mock.AnythingOfType("*context.cancelCtx"),
		mock.MatchedBy(func(key contentmanagerclient.AnnIndexKey) bool {
			return key.TenantId != nil && *key.TenantId == tenantId &&
				key.TransformationKey == transformationKey &&
				key.IndexId == indexId
		}),
		mock.AnythingOfType("requestcontext.RequestContext"),
	).Return(&annIndexBlobInfosResult, nil)
}

// mockAddAnnIndexMapping mocks the AddAnnIndexMapping call on the content manager client
func mockAddAnnIndexMapping(mockContentManagerClient *contentmanagerclient.MockContentManagerClient, tenantId, transformationKey, checkpointId, indexId string) {
	mockContentManagerClient.On("AddAnnIndexMapping",
		mock.AnythingOfType("*context.cancelCtx"),
		&[]string{tenantId}[0],
		transformationKey,
		checkpointId,
		indexId,
		mock.AnythingOfType("[]blob_names.BlobName"), // added blobs
		mock.AnythingOfType("[]blob_names.BlobName"), // removed blobs
		mock.AnythingOfType("requestcontext.RequestContext"),
	).Return(&content_manager_pb.AddAnnIndexMappingResponse{}, nil)
}
