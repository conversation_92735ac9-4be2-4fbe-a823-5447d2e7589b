package main

import (
	"context"
	"errors"
	"fmt"
	"math"
	"math/rand"
	"regexp"
	"runtime"
	"sort"
	"strings"
	"sync"
	"sync/atomic"
	"time"

	"cloud.google.com/go/pubsub"
	"github.com/alitto/pond/v2"
	blob_names "github.com/augmentcode/augment/base/blob_names"
	blobs_pb "github.com/augmentcode/augment/base/blob_names/proto"
	"github.com/augmentcode/augment/base/go/blobset_cache"
	checkpoint_indexer "github.com/augmentcode/augment/services/checkpoint_indexer/proto"
	contentmanagerclient "github.com/augmentcode/augment/services/content_manager/client"
	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
	tenantwatcherclient "github.com/augmentcode/augment/services/tenant_watcher/client"
	"github.com/rs/zerolog/log"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/proto"
)

type WorkingSetStore interface {
	CreateAnnIndexForCheckpoint(ctx context.Context, checkpointID string, tenantID string, transformationKey string) error
	RegisterWorkingSet(ctx context.Context, tenantID string, sessionId requestcontext.RequestSessionId, blobs []*blobs_pb.Blobs) error
	GetActiveCheckpoints(tenantID string) ([]string, error)
	GetActiveTransformationKeys(tenantID string) ([]string, error)
	Init(context.Context) error
	Run(ctx context.Context) error
	// The following methods are for testing purposes only
	WaitForAsyncProcessing(ctx context.Context) error
	GetCachedCheckpointBlobNames(tenantID string, checkpointID string) ([]blob_names.BlobName, error)
	GetCachedIndexBlobNames(tenantID string, transformationKey string, indexID string) ([]blob_names.BlobName, error)
	GetWorkingSet(sessionId requestcontext.RequestSessionId) (*WorkingSet, error)
	GetAnnIndexMappingForCheckpoint(tenantID string, checkpointID string, transformationKey string) (string, error)
	AddLSHIndex(tenantID string, transformationKey string, indexId string, indexBlobNames []blob_names.BlobName)
}

type WorkingSet struct {
	tenantID  string
	sessionId requestcontext.RequestSessionId
	blobs     []*blobs_pb.Blobs
	ttl       time.Time
}

// at the default 4MB response limit, this allows each blobInfo response to be
// up to 32KB, or ~100 tfkeys
const grpcDefaultBatchSize = 128

// Max number of index candidates to consider when matching a checkpoint against existing indexes
const maxIndexCandidatesToConsider = 5

// If a checkpoint and an index differ by fewer than this many blobnames, we consider the index a match. No new indexing is triggered
const triggerReindexingDeltaThreshold = 10000

// If a checkpoint and an index differ by fewer than this percentage of blobs, we consider the index a match. No new indexing is triggered
const triggerReindexingRatioThreshold = 0.10

// If a checkpoint and an index differ by fewer than this many blobnames, we will continue to use the existing (checkpoint<->index) mapping and trigger reindexing.
const reuseIndexDeltaThreshold = 25000

// If a checkpoint and an index differ by fewer than this percentage of blobs, we will continue to use the existing (checkpoint<->index) mapping and trigger reindexing.
const reuseIndexRatioThreshold = 0.25

// We will ignore timestamp updates to BigTable if the ANN index has been updated within 1 hour.
const reuseExistingTimestampThreshold = 1 * time.Hour

func (ws *WorkingSet) Stats() string {
	return fmt.Sprintf("tenantID=%s | sessionId=%s | blobs=%v", ws.tenantID, ws.sessionId, formatBlobsList(ws.blobs))
}

func (ws *WorkingSet) String() string {
	return fmt.Sprintf("WorkingSet{tenantID=%s, sessionId=%s, blobs=%v, ttl=%v}", ws.tenantID, ws.sessionId, formatBlobsList(ws.blobs), ws.ttl)
}

func IsEqualBlobs(a, b []*blobs_pb.Blobs) bool {
	if len(a) != len(b) {
		return false
	}
	for i, blobs := range a {
		if !proto.Equal(blobs, b[i]) {
			return false
		}
	}
	return true
}

func NewWorkingSet(tenantID string, sessionId requestcontext.RequestSessionId, blobs []*blobs_pb.Blobs, ttl time.Time) *WorkingSet {
	return &WorkingSet{
		tenantID:  tenantID,
		sessionId: sessionId,
		blobs:     blobs,
		ttl:       ttl,
	}
}

type CheckpointKey struct {
	tenantID     string
	checkpointID string
}

func CheckpointKeyToBlobSetKey(key CheckpointKey) blobset_cache.BlobSetKey {
	return blobset_cache.BlobSetKey{
		TenantID: key.tenantID,
		SetID:    key.checkpointID,
	}
}

type AnnIndexKey struct {
	tenantID          string
	transformationKey string
	annIndexID        string
}

func AnnIndexKeyToBlobSetKey(key AnnIndexKey) blobset_cache.BlobSetKey {
	return blobset_cache.BlobSetKey{
		TenantID: key.tenantID,
		SetID:    fmt.Sprintf("ann-index-%s-%s", key.transformationKey, key.annIndexID),
	}
}

type AnnIndexContext struct {
	annIndexID string
	added      []blob_names.BlobName
	removed    []blob_names.BlobName
}

type CheckpointPerTransformationKey struct {
	tenantID          string
	transformationKey string
	checkpointID      string
}

type LSHIndexKey struct {
	tenantID          string
	transformationKey string
}

type TransformationKeyPerTenant struct {
	tenantID          string
	transformationKey string
}

type AnnIndexRequestKey struct {
	tenantID          string
	checkpointID      string
	transformationKey string
}

type AnnIndexInfo struct {
	timestamp time.Time
	signature []int
	blobCount int
}

type ActiveCheckpointInfo struct {
	lastUsedTime    time.Time
	lastRenewedTime time.Time
}

type workingSetStore struct {
	sessionTimeout            time.Duration
	statsLoop                 time.Duration
	cleanupLoop               time.Duration
	checkpointRenewalLoop     time.Duration
	checkpointRenewalInterval time.Duration
	isRunningUnderTest        bool
	backgroundCtx             context.Context // Background context for goroutine pool tasks

	bigtableHelper       BigtableHelper
	contentManagerClient contentmanagerclient.ContentManagerClient
	requestContextHelper RequestContextHelper
	tenantWatcherClient  tenantwatcherclient.TenantWatcherClient
	cpuTaskPool          pond.Pool
	ioTaskPool           pond.Pool

	hourlyResidentSet   map[string]map[blob_names.BlobName]struct{}
	prevResidentTenants map[string]struct{}
	tenantCache         map[string]string // Maps tenant_id to tenant_name

	checkpointBlobsCache     *blobset_cache.BlobSetCache
	activeCheckpoints        sync.Map // map[CheckpointKey]ActiveCheckpointInfo
	checkpointExpirationTime time.Duration
	checkpointTaskMutex      sync.RWMutex
	pendingCheckpointTasks   map[CheckpointKey]pond.Task

	annIndexBlobsCache             *blobset_cache.BlobSetCache
	annIndexCreationTopic          *pubsub.Topic
	annIndexCreationSub            *pubsub.Subscription
	pendingAnnIndexMutex           sync.RWMutex
	pendingAnnIndexes              map[CheckpointPerTransformationKey]time.Time // Track ANN indexes that are being created with timestamp
	pendingAnnIndexCreationTimeout time.Duration                                // Timeout for pending ANN index creation entries
	// Track in-flight ANN index checkpoint requests to prevent duplicate processing
	annIndexRequestsMap sync.Map
	// For testing purposes - tracks async operations and signals when all are complete
	asyncProcessingWG   sync.WaitGroup
	asyncProcessingDone chan struct{}

	// Map from CheckpointPerTransformationKey to ANNIndexDelta
	checkpointToAnnIndexMap sync.Map

	lshIndexMutex sync.RWMutex
	lsh_indexes   map[LSHIndexKey]*LSHIndex

	// Tracks the last time an ANN index was used.
	// Does not contain any additional information about the index.
	activeAnnIndexes sync.Map // map[AnnIndexKey]AnnIndexInfo

	publishBlobindexMetrics bool

	transformationKeyExpirationTime time.Duration
	activeTransformationKeys        sync.Map

	annIndexExpirationTime time.Duration

	// Tracking cache stats for metrics
	lastCheckpointStats blobset_cache.CacheStats
	lastIndexStats      blobset_cache.CacheStats

	// Minimum number of blobs required to be present in a checkpoint to create an ANN index
	minCheckpointSizeToIndex int

	// Version of the MinHash algorithm to use
	minHashVersion string

	// Regex filter applied to checkpoint paths before indexing
	checkpointIndexerRegexFilter string

	allowableRenewalsPerSecond float64
}

func NewWorkingSetStore(
	ctx context.Context,
	requestContextHelper RequestContextHelper,
	contentManagerClient contentmanagerclient.ContentManagerClient,
	bigtableHelper BigtableHelper,
	tenantWatcherClient tenantwatcherclient.TenantWatcherClient,
	sessionTimeout time.Duration,
	statsLoop time.Duration,
	cleanupLoop time.Duration,
	checkpointRenewalLoop time.Duration,
	checkpointRenewalInterval time.Duration,
	checkpointBlobCacheTTL time.Duration,
	checkpointBlobCacheSizeInMB int,
	publishBlobIndexMetrics bool,
	checkpointExpirationTime time.Duration,
	annIndexBlobCacheTTL time.Duration,
	annIndexBlobCacheSizeInMB int,
	annIndexCreationTopic *pubsub.Topic,
	annIndexCreationSub *pubsub.Subscription,
	transformationKeyExpirationTime time.Duration,
	pendingAnnIndexCreationTimeout time.Duration,
	annIndexExpirationTime time.Duration,
	minCheckpointSizeToIndex int,
	cpuTaskPoolMaxSize int,
	ioTaskPoolSize int,
	minHashVersion string,
	checkpointIndexerRegexFilter string,
	allowableRenewalsPerSecond float64,
	isRunningUnderTest bool,
) WorkingSetStore {
	store := &workingSetStore{
		backgroundCtx:                   ctx,
		sessionTimeout:                  sessionTimeout,
		requestContextHelper:            requestContextHelper,
		contentManagerClient:            contentManagerClient,
		bigtableHelper:                  bigtableHelper,
		tenantWatcherClient:             tenantWatcherClient,
		hourlyResidentSet:               make(map[string]map[blob_names.BlobName]struct{}),
		prevResidentTenants:             make(map[string]struct{}),
		lsh_indexes:                     make(map[LSHIndexKey]*LSHIndex),
		activeAnnIndexes:                sync.Map{},
		statsLoop:                       statsLoop,
		cleanupLoop:                     cleanupLoop,
		checkpointRenewalLoop:           checkpointRenewalLoop,
		checkpointRenewalInterval:       checkpointRenewalInterval,
		publishBlobindexMetrics:         publishBlobIndexMetrics,
		pendingCheckpointTasks:          make(map[CheckpointKey]pond.Task),
		activeCheckpoints:               sync.Map{},
		checkpointExpirationTime:        checkpointExpirationTime,
		annIndexCreationTopic:           annIndexCreationTopic,
		annIndexCreationSub:             annIndexCreationSub,
		pendingAnnIndexes:               make(map[CheckpointPerTransformationKey]time.Time),
		checkpointToAnnIndexMap:         sync.Map{},
		transformationKeyExpirationTime: transformationKeyExpirationTime,
		pendingAnnIndexCreationTimeout:  pendingAnnIndexCreationTimeout,
		annIndexExpirationTime:          annIndexExpirationTime,
		asyncProcessingDone:             make(chan struct{}),
		tenantCache:                     make(map[string]string),
		annIndexRequestsMap:             sync.Map{},
		minCheckpointSizeToIndex:        minCheckpointSizeToIndex,
		minHashVersion:                  minHashVersion,
		checkpointIndexerRegexFilter:    checkpointIndexerRegexFilter,
		allowableRenewalsPerSecond:      allowableRenewalsPerSecond,
		isRunningUnderTest:              isRunningUnderTest,
	}

	cpuPoolSize := min(runtime.NumCPU(), cpuTaskPoolMaxSize)
	log.Info().Msgf("Using a CPU task pool of %d goroutines", cpuPoolSize)
	store.cpuTaskPool = pond.NewPool(cpuPoolSize)

	log.Info().Msgf("Using an IO task pool of %d goroutines", ioTaskPoolSize)
	store.ioTaskPool = pond.NewPool(ioTaskPoolSize)

	checkpointEvictionCallback := func(key blobset_cache.BlobSetKey, blobNames []blob_names.BlobName) {
		log.Info().Msgf("Evicting checkpoint %v from cache (%d blobs)",
			key, len(blobNames))
	}

	annIndexEvictionCallback := func(key blobset_cache.BlobSetKey, blobNames []blob_names.BlobName) {
		log.Info().Msgf("Evicting ANN index %v from cache (%d blobs)",
			key, len(blobNames))
	}

	checkpointBlobsCache, err := blobset_cache.NewBlobSetCache(
		checkpointBlobCacheTTL,
		checkpointBlobCacheSizeInMB,
		60*time.Second, // how often cache cleanup runs
		checkpointEvictionCallback)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to create checkpoint blobname cache with blobset_cache")
	}
	store.checkpointBlobsCache = checkpointBlobsCache

	annIndexBlobsCache, err := blobset_cache.NewBlobSetCache(
		annIndexBlobCacheTTL,
		annIndexBlobCacheSizeInMB,
		60*time.Second, // how often cache cleanup runs
		annIndexEvictionCallback)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to create ANN index blobname cache with blobset_cache")
	}
	store.annIndexBlobsCache = annIndexBlobsCache

	return store
}

// getMetricsTenantName returns the tenant name to use for metric labels for a given tenant ID.
// If the tenant ID is not found in the cache, it returns the tenant ID itself.
func (s *workingSetStore) getMetricsTenantName(tenantID string) string {
	if name, ok := s.tenantCache[tenantID]; ok {
		return name
	}
	return tenantID
}

// Load state from Bigtable and initialize various in-memory data structures.
// Init will bail out if any Bigtable or Content manager calls fail since we'd
// rather not start the service with partial state.
func (s *workingSetStore) Init(ctx context.Context) error {
	startTime := time.Now()
	log.Info().Msg("Initializing working set store")

	tenantsList, err := s.tenantWatcherClient.GetTenantsStream(ctx, "")
	if err != nil {
		log.Error().Err(err).Msg("Failed to get tenants from tenant_watcher service")
		return err
	}

	tenants := make([]string, 0, len(tenantsList))
	for _, tenant := range tenantsList {
		tenants = append(tenants, tenant.Id)
		// Populate the tenant cache with tenant ID to name mapping
		s.tenantCache[tenant.Id] = tenantwatcherclient.MetricsTenantName(tenant)
	}
	log.Debug().Msgf("Found %d tenants from tenant_watcher service", len(tenants))

	// For each tenant, load all active checkpoints / transformation keys / pending indexes
	for _, tenantID := range tenants {
		log.Info().Msgf("Loading working set state for tenant %s", tenantID)
		checkpoints, err := s.bigtableHelper.GetAllActiveCheckpoints(tenantID)
		if err != nil {
			log.Error().Err(err).Msgf("Failed to get active checkpoints for tenant %s", tenantID)
			return err
		}

		for _, cp := range checkpoints {
			key := CheckpointKey{tenantID: tenantID, checkpointID: cp.CheckpointID}
			s.activeCheckpoints.Store(key, ActiveCheckpointInfo{lastUsedTime: cp.Timestamp, lastRenewedTime: cp.LastRenewedTime})
			log.Debug().Msgf("Loaded active checkpoint %v with timestamp %s", key, cp.Timestamp)
		}

		transformationKeys, err := s.bigtableHelper.GetAllActiveTransformationKeys(tenantID)
		if err != nil {
			log.Error().Err(err).Msgf("Failed to get active transformation keys for tenant %s", tenantID)
			return err
		}

		for _, tk := range transformationKeys {
			key := TransformationKeyPerTenant{
				tenantID:          tenantID,
				transformationKey: tk.TransformationKey,
			}
			s.activeTransformationKeys.Store(key, tk.Timestamp)
			log.Debug().Msgf("Loaded active transformation key %v with timestamp %s", key, tk.Timestamp)
		}

		pendingIndexes, err := s.bigtableHelper.GetAllPendingIndexes(tenantID)
		if err != nil {
			log.Error().Err(err).Msgf("Failed to get pending indexes for tenant %s", tenantID)
			return err
		}

		s.pendingAnnIndexMutex.Lock()
		for _, pi := range pendingIndexes {
			key := CheckpointPerTransformationKey{
				tenantID:          tenantID,
				checkpointID:      pi.CheckpointID,
				transformationKey: pi.TransformationKey,
			}
			s.pendingAnnIndexes[key] = pi.Timestamp
			log.Debug().Msgf("Loaded pending index %v", key)
		}
		s.pendingAnnIndexMutex.Unlock()

		// Keep track of the number of checkpoint->index mappings saved for this tenant
		var indexMappingsCount int32 = 0
		// Track all indexIDs seen through GetBestAnnIndex responses with reference counts
		seenIndexIDs := &sync.Map{}

		requestContext, err := s.requestContextHelper.GetBackgroundRequestContext(tenantID)
		if err != nil {
			log.Error().Err(err).Msgf("Failed to get request context for tenant %s", tenantID)
			return err
		}

		// For each checkpoint x transformation key combination, get the best ANN index
		log.Info().Msgf("Obtaining ANN index mappings for tenant %s", tenantID)
		// need to split this pool out because .getBlobNamesForCheckpoint submits to the
		// IoTaskPool, which will deadlock if we also submit the init tasks there.
		initIoTaskPool := pond.NewPool(s.ioTaskPool.MaxConcurrency())
		mappingsGroup := initIoTaskPool.NewGroup()
		for _, cp := range checkpoints {
			initIoTaskPool.SubmitErr(func() error {
				for _, tk := range transformationKeys {
					checkpoint := cp // Create local copies to avoid closure issues
					transformationKey := tk

					result, err := s.contentManagerClient.GetBestAnnIndex(
						ctx,
						&tenantID,
						transformationKey.TransformationKey,
						checkpoint.CheckpointID,
						requestContext,
					)
					if err != nil {
						if status.Code(err) == codes.NotFound {
							log.Debug().Err(err).Msgf("No ANN index mapping found for checkpoint %s and transformation key %s",
								checkpoint.CheckpointID, transformationKey.TransformationKey)
							continue
						} else {
							log.Error().Err(err).Msgf("Failed to get best ANN index for checkpoint %s and transformation key %s",
								checkpoint.CheckpointID, transformationKey.TransformationKey)
							// ignore invalid ANN index state during init
							continue
						}
					}

					// If we got a valid response, save the mapping
					if result != nil && result.IndexId != "" {
						key := CheckpointPerTransformationKey{
							tenantID:          tenantID,
							checkpointID:      checkpoint.CheckpointID,
							transformationKey: transformationKey.TransformationKey,
						}
						value := AnnIndexContext{
							annIndexID: result.IndexId,
							added:      result.AddedBlobs,
							removed:    result.RemovedBlobs,
						}
						s.checkpointToAnnIndexMap.Store(key, value)
						log.Debug().Msgf("Loaded ANN index mapping: checkpoint %s -> index %s for transformation key %s",
							checkpoint.CheckpointID, result.IndexId, transformationKey.TransformationKey)

						indexKey := AnnIndexKey{
							tenantID:          tenantID,
							transformationKey: transformationKey.TransformationKey,
							annIndexID:        result.IndexId,
						}

						countPtr, _ := seenIndexIDs.LoadOrStore(indexKey, new(int64))
						atomic.AddInt64(countPtr.(*int64), 1)

						atomic.AddInt32(&indexMappingsCount, 1)
					}
				}
				return nil
			})
		}

		// Wait for all tasks to complete and check for errors
		if err := mappingsGroup.Wait(); err != nil {
			return err
		}
		initIoTaskPool.StopAndWait()

		// Resolve all seen ANN indexes and add them to the LSH index
		log.Info().Msgf("Resolving ANN indexes for tenant %s", tenantID)
		resolveGroup := s.cpuTaskPool.NewGroup()

		// Collect and sort indexes by reference count (ascending order) so that indexes with
		// the most references are submitted to the task pool last. We do this in order to
		// ensure that most referred indexes are the most likely to survive LRU eviction if
		// the index cache is not able to accommodate all active indexes in memory.
		var indexEntries []struct {
			key      AnnIndexKey
			refCount int64
		}
		seenIndexIDs.Range(func(key, value interface{}) bool {
			idxKey := key.(AnnIndexKey)
			refCount := *value.(*int64)
			indexEntries = append(indexEntries, struct {
				key      AnnIndexKey
				refCount int64
			}{key: idxKey, refCount: refCount})
			return true
		})
		sort.Slice(indexEntries, func(i, j int) bool {
			return indexEntries[i].refCount < indexEntries[j].refCount
		})

		// Retrieve previously computed signatures from Bigtable
		activeIndexes, err := s.bigtableHelper.GetAllActiveIndexes(s.minHashVersion, tenantID)
		if err != nil {
			log.Error().Err(err).Msgf("Failed to get active indexes for tenant %s", tenantID)
		}
		for _, idx := range activeIndexes {
			idxKey := AnnIndexKey{
				tenantID:          tenantID,
				transformationKey: idx.TransformationKey,
				annIndexID:        idx.CheckpointID,
			}
			s.addAnnIndexToLSHIndex(idxKey, idx.Signature)

			// Update the active ANN index last used timestamp
			s.activeAnnIndexes.Store(idxKey, AnnIndexInfo{
				timestamp: idx.Timestamp,
				blobCount: idx.BlobCount,
				signature: idx.Signature,
			})
		}

		var seenIndexCount int = len(indexEntries)
		for _, entry := range indexEntries {
			idxKey := entry.key

			// Skip indexes that we already have signatures for
			if _, ok := s.activeAnnIndexes.Load(idxKey); ok {
				continue
			}
			// Submit task to the pool with error handling
			resolveGroup.SubmitErr(func() error {
				_, err := s.resolveAnnIndexAndAddToLSHIndex(ctx, idxKey)
				return err
			})
		}

		// Wait for all tasks to complete and check for errors
		if err := resolveGroup.Wait(); err != nil {
			return err
		}

		log.Info().Msgf("Loaded %d ANN indexes, %d checkpoint<->index mappings for tenant %s",
			seenIndexCount, indexMappingsCount, tenantID)
		log.Info().Msgf("Init stats for tenant %s: %d active checkpoints, %d pending indexes, %d active transformation keys",
			tenantID, len(checkpoints), len(pendingIndexes), len(transformationKeys))
	}

	log.Info().Msgf("WorkingsetStore Init() took %s", time.Since(startTime))
	return nil
}

func (s *workingSetStore) Run(ctx context.Context) error {
	go s.runAnnIndexCreationListenerLoop(ctx)
	go s.runStatsLoop(ctx)
	go s.runCleanupLoop(ctx)
	go s.runHourlyStatsLoop(ctx)
	go s.runCheckpointRenewalLoop(ctx)
	return nil
}

func (s *workingSetStore) runCleanupLoop(ctx context.Context) error {
	for {
		select {
		case <-time.After(s.cleanupLoop):
			s.cleanup(ctx)
		case <-ctx.Done():
			return ctx.Err()
		}
	}
}

func (s *workingSetStore) cleanupCheckpoint(key CheckpointKey) error {
	log.Info().Msgf("Cleaning up checkpoint %v", key)

	_, exists := s.activeCheckpoints.Load(key)
	if !exists {
		return fmt.Errorf("checkpoint %v not found in activeCheckpoints", key)
	}

	s.checkpointTaskMutex.RLock()
	pendingTask, isPending := s.pendingCheckpointTasks[key]

	if isPending {
		log.Info().Msgf("Checkpoint %v is pending resolution, waiting before cleanup", key)
		s.checkpointTaskMutex.RUnlock()
		pendingTask.Wait()

		s.checkpointTaskMutex.RLock()
		_, stillPending := s.pendingCheckpointTasks[key]
		if stillPending {
			log.Warn().Msgf("Pending resolution task for checkpoint %v still exists after wait", key)
		}
	}
	s.checkpointTaskMutex.RUnlock()

	err := s.bigtableHelper.DeleteActiveCheckpoint(key.tenantID, key.checkpointID)
	if err != nil {
		log.Error().Err(err).Msgf("Failed to delete checkpoint %v from Bigtable", key)
		return err
	}
	s.activeCheckpoints.Delete(key)

	blobSetKey := CheckpointKeyToBlobSetKey(key)
	s.checkpointBlobsCache.DeleteBlobSet(blobSetKey)

	return nil
}

func (s *workingSetStore) cleanup(ctx context.Context) {
	log.Info().Msgf("Running cleanup loop")

	var checkpointsToCleanup []CheckpointKey
	s.activeCheckpoints.Range(func(k, v interface{}) bool {
		key := k.(CheckpointKey)
		timestamp := v.(ActiveCheckpointInfo).lastUsedTime
		if time.Now().Sub(timestamp) > s.checkpointExpirationTime {
			checkpointsToCleanup = append(checkpointsToCleanup, key)
		}
		return true
	})

	for _, key := range checkpointsToCleanup {
		err := s.cleanupCheckpoint(key)
		if err != nil {
			log.Error().Err(err).Msgf("Failed to clean up checkpoint %v", key)
		}
	}

	s.activeTransformationKeys.Range(func(key, value interface{}) bool {
		timestamp, ok := value.(time.Time)
		if !ok {
			log.Error().Msgf("Failed to clean up inactive transformation key %v", key)
			return true
		}
		if ok && time.Now().Sub(timestamp) > s.transformationKeyExpirationTime {
			err := s.bigtableHelper.DeleteActiveTransformationKey(key.(TransformationKeyPerTenant).tenantID,
				key.(TransformationKeyPerTenant).transformationKey)
			if err != nil {
				log.Error().Err(err).Msgf("Failed to delete transformation key %v from Bigtable", key)
				return true // continue to next key
			}
			s.activeTransformationKeys.Delete(key)
			log.Info().Msgf("Cleaned up inactive transformation key %v", key)
		}
		return true
	})

	// Clean up pending ANN index creation entries that have timed out
	s.pendingAnnIndexMutex.Lock()
	var keysToDelete []CheckpointPerTransformationKey
	for key, timestamp := range s.pendingAnnIndexes {
		if time.Now().Sub(timestamp) > s.pendingAnnIndexCreationTimeout {
			keysToDelete = append(keysToDelete, key)
		}
	}
	for _, key := range keysToDelete {
		err := s.bigtableHelper.DeletePendingIndex(key.tenantID, key.checkpointID, key.transformationKey)
		if err != nil {
			log.Error().Err(err).Msgf("Failed to delete pending index %v from Bigtable", key)
			continue
		}
		delete(s.pendingAnnIndexes, key)
		log.Info().Msgf("Cleaned up timed out pending ANN index creation for %v", key)
	}
	s.pendingAnnIndexMutex.Unlock()

	// Clean up active ANN indexes that have not been seen for a long time
	var annIndexesToDelete []AnnIndexKey
	s.activeAnnIndexes.Range(func(key, value interface{}) bool {
		annIndexInfo, ok := value.(AnnIndexInfo)
		if !ok {
			log.Error().Msgf("Failed to clean up inactive ANN index %v", key)
			return true
		}
		if ok && time.Now().Sub(annIndexInfo.timestamp) > s.annIndexExpirationTime {
			annIndexesToDelete = append(annIndexesToDelete, key.(AnnIndexKey))
		}
		return true
	})
	for _, key := range annIndexesToDelete {
		err := s.bigtableHelper.DeleteActiveIndex(s.minHashVersion, key.tenantID, key.annIndexID, key.transformationKey)
		if err != nil {
			log.Error().Err(err).Msgf("Failed to delete active index %v from Bigtable", key)
			continue
		}
		s.activeAnnIndexes.Delete(key)
		log.Info().Msgf("Cleaned up unused active ANN index %v", key)
	}
}

// GetCachedCheckpointBlobNames returns the blob names for a checkpoint if it is cached for testing purposes
func (s *workingSetStore) GetCachedCheckpointBlobNames(tenantID string, checkpointID string) ([]blob_names.BlobName, error) {
	checkpointKey := CheckpointKey{tenantID: tenantID, checkpointID: checkpointID}
	blobSetKey := CheckpointKeyToBlobSetKey(checkpointKey)

	blobNames, err := s.checkpointBlobsCache.GetBlobSet(blobSetKey)
	if err != nil {
		if errors.Is(err, blobset_cache.ErrEntryNotFound) {
			log.Debug().Msgf("Checkpoint not found in cache: %v", checkpointKey)
			return nil, err
		} else {
			log.Error().Err(err).Msgf("Error getting checkpoint blob names from cache for key %v", checkpointKey)
			return nil, err
		}
	}

	return blobNames, nil
}

// GetCachedIndexBlobNames returns the blob names for an ANN index if it is cached for testing purposes
func (s *workingSetStore) GetCachedIndexBlobNames(tenantID string, transformationKey string, indexID string) ([]blob_names.BlobName, error) {
	idxKey := AnnIndexKey{
		tenantID:          tenantID,
		transformationKey: transformationKey,
		annIndexID:        indexID,
	}
	blobSetKey := AnnIndexKeyToBlobSetKey(idxKey)

	blobNames, err := s.annIndexBlobsCache.GetBlobSet(blobSetKey)
	if err != nil {
		if errors.Is(err, blobset_cache.ErrEntryNotFound) {
			log.Debug().Msgf("Index not found in cache: %v", idxKey)
			return nil, err
		} else {
			log.Error().Err(err).Msgf("Error getting index blob names from cache for key %v", idxKey)
			return nil, err
		}
	}

	return blobNames, nil
}

// GetAnnIndexMappingForCheckpoint returns the ANN index ID for a checkpoint and transformation key
// This is used for testing purposes only
func (s *workingSetStore) GetAnnIndexMappingForCheckpoint(tenantID string, checkpointID string, transformationKey string) (string, error) {
	key := CheckpointPerTransformationKey{
		tenantID:          tenantID,
		checkpointID:      checkpointID,
		transformationKey: transformationKey,
	}

	value, found := s.checkpointToAnnIndexMap.Load(key)
	if !found {
		return "", fmt.Errorf("no ANN index mapping found for checkpoint %s and transformation key %s", checkpointID, transformationKey)
	}

	annIndexID := value.(AnnIndexContext).annIndexID

	// Update the active ANN index last used timestamp ANN index is not evicted from BigTable
	annIndexInfo, annIndexInfoFound := s.activeAnnIndexes.Load(AnnIndexKey{tenantID: tenantID, transformationKey: transformationKey, annIndexID: annIndexID})
	if !annIndexInfoFound {
		return "", fmt.Errorf("no ANN index size found for index %s", annIndexID)
	}
	if err := s.updateActiveIndex(AnnIndexKey{tenantID: tenantID, transformationKey: transformationKey, annIndexID: annIndexID}, annIndexInfo.(AnnIndexInfo).signature, annIndexInfo.(AnnIndexInfo).blobCount); err != nil {
		log.Error().Err(err).Msgf("Failed to update active index for %v", AnnIndexKey{tenantID: tenantID, transformationKey: transformationKey, annIndexID: annIndexID})
	}
	return annIndexID, nil
}

// GetActiveCheckpoints returns a list of active checkpoint IDs for the given tenant.
// This is used for testing purposes only
func (s *workingSetStore) GetActiveCheckpoints(tenantID string) ([]string, error) {
	var checkpointIDs []string

	s.activeCheckpoints.Range(func(k, v interface{}) bool {
		key := k.(CheckpointKey)
		if key.tenantID == tenantID {
			checkpointIDs = append(checkpointIDs, key.checkpointID)
		}
		return true
	})

	return checkpointIDs, nil
}

// GetActiveTransformationKeys returns a list of active transformation keys for the given tenant.
// This is used for testing purposes only
func (s *workingSetStore) GetActiveTransformationKeys(tenantID string) ([]string, error) {
	var transformationKeys []string

	s.activeTransformationKeys.Range(func(key, value interface{}) bool {
		transformationKeyPerTenant, ok := key.(TransformationKeyPerTenant)
		if ok && transformationKeyPerTenant.tenantID == tenantID {
			transformationKeys = append(transformationKeys, transformationKeyPerTenant.transformationKey)
		}
		return true
	})

	return transformationKeys, nil
}

// AddLSHIndex adds a new entry to the LSH index table.
// This used for testing purposes only.
func (s *workingSetStore) AddLSHIndex(tenantID string, transformationKey string, indexId string, indexBlobNames []blob_names.BlobName) {
	s.lshIndexMutex.Lock()
	defer s.lshIndexMutex.Unlock()

	lshIndexKey := LSHIndexKey{tenantID: tenantID, transformationKey: transformationKey}
	s.lsh_indexes[lshIndexKey] = NewLSHIndex()

	// Add the index to LSH with the computed signature.
	testSignature := computeMinHashSignature(indexId, indexBlobNames)
	s.lsh_indexes[lshIndexKey].AddAnnIndex(indexId, testSignature)

	s.updateActiveIndex(AnnIndexKey{tenantID: tenantID, transformationKey: transformationKey, annIndexID: indexId}, testSignature, len(indexBlobNames))
}

func (s *workingSetStore) runHourlyStatsLoop(ctx context.Context) error {
	// sleep until next hour then submit stats
	for {
		now := time.Now()
		// jitter the refresh to prevent high load at the turn of the hour
		next := now.Truncate(time.Hour).
			Add(time.Hour).
			Add(time.Second * time.Duration(rand.Intn(60)))
		delta := next.Sub(now)
		log.Info().Msgf("Next hourly observation will be submitted in %f minutes", delta.Minutes())

		select {
		case <-time.After(delta):
			s.submitHourlyStats()
		case <-ctx.Done():
			return ctx.Err()
		}
	}
}

func (s *workingSetStore) submitHourlyStats() {
	var empty struct{}
	// don't split the lock for now to prevent re-acquisition contention
	log.Info().Msgf("Submitting hourly resident blob stats for %d tenants", len(s.hourlyResidentSet))
	prevTenants := s.prevResidentTenants
	s.prevResidentTenants = make(map[string]struct{})
	for tenantID, blobNames := range s.hourlyResidentSet {
		hourlyResidentBlobsMetric.WithLabelValues(s.getMetricsTenantName(tenantID)).Set(float64(len(blobNames)))
		s.prevResidentTenants[tenantID] = empty
		delete(prevTenants, tenantID)
	}
	for tenantID := range prevTenants {
		hourlyResidentBlobsMetric.WithLabelValues(s.getMetricsTenantName(tenantID)).Set(0.0)
	}
	// maps don't release bucket memory, so to properly empty this datastructure we simply
	// assign a fresh one
	s.hourlyResidentSet = make(map[string]map[blob_names.BlobName]struct{})
}

func (s *workingSetStore) runStatsLoop(ctx context.Context) error {
	for {
		select {
		case <-time.After(s.statsLoop):
			s.reportMetrics() // publish metrics to Prometheus
			s.printStats(ctx)
		case <-ctx.Done():
			return ctx.Err()
		}
	}
}

func (s *workingSetStore) runCheckpointRenewalLoop(ctx context.Context) error {
	for {
		select {
		case <-time.After(s.checkpointRenewalLoop):
			s.renewCheckpoints(ctx)
		case <-ctx.Done():
			return ctx.Err()
		}
	}
}

func (s *workingSetStore) countActiveTransformationKeys() int {
	count := 0
	s.activeTransformationKeys.Range(func(_, _ interface{}) bool {
		count++
		return true
	})
	return count
}

func (s *workingSetStore) calculateCacheHitMissDeltas() (int64, int64, int64, int64) {
	checkpointStats := s.checkpointBlobsCache.Stats()
	indexStats := s.annIndexBlobsCache.Stats()
	checkpointHitsDelta := checkpointStats.Hits - s.lastCheckpointStats.Hits
	checkpointMissesDelta := checkpointStats.Misses - s.lastCheckpointStats.Misses
	indexHitsDelta := indexStats.Hits - s.lastIndexStats.Hits
	indexMissesDelta := indexStats.Misses - s.lastIndexStats.Misses

	// Store current stats for next delta calculation
	s.lastCheckpointStats = checkpointStats
	s.lastIndexStats = indexStats

	return checkpointHitsDelta, checkpointMissesDelta, indexHitsDelta, indexMissesDelta
}

// reportMetrics updates all Prometheus metrics
func (s *workingSetStore) reportMetrics() {
	// Cache metrics
	blobsetCacheEntriesMetric.WithLabelValues("checkpoint").Set(float64(s.checkpointBlobsCache.NumEntries()))
	blobsetCacheEntriesMetric.WithLabelValues("ann_index").Set(float64(s.annIndexBlobsCache.NumEntries()))
	blobsetCacheMemoryMetric.WithLabelValues("checkpoint").Set(float64(s.checkpointBlobsCache.MemorySizeInBytes()))
	blobsetCacheMemoryMetric.WithLabelValues("ann_index").Set(float64(s.annIndexBlobsCache.MemorySizeInBytes()))

	// Cache hit/miss metrics
	checkpointHitsDelta, checkpointMissesDelta, indexHitsDelta, indexMissesDelta := s.calculateCacheHitMissDeltas()
	blobsetCacheUsageMetric.WithLabelValues("checkpoint", "hit").Add(float64(checkpointHitsDelta))
	blobsetCacheUsageMetric.WithLabelValues("checkpoint", "miss").Add(float64(checkpointMissesDelta))
	blobsetCacheUsageMetric.WithLabelValues("ann_index", "hit").Add(float64(indexHitsDelta))
	blobsetCacheUsageMetric.WithLabelValues("ann_index", "miss").Add(float64(indexMissesDelta))

	// LSH index metrics
	s.lshIndexMutex.RLock()
	for key, idx := range s.lsh_indexes {
		lshIndexEntriesMetric.WithLabelValues(s.getMetricsTenantName(key.tenantID), key.transformationKey).Set(float64(idx.NumAnnIndexes()))
	}
	s.lshIndexMutex.RUnlock()

	annIndexRequestQueueDepth.Set(float64(s.cpuTaskPool.WaitingTasks()))

	// Store size metrics
	// Active checkpoints by tenant
	checkpointsByTenant := make(map[string]int)
	s.activeCheckpoints.Range(func(key, _ interface{}) bool {
		cpKey := key.(CheckpointKey)
		checkpointsByTenant[cpKey.tenantID]++
		return true
	})
	for tenantID, count := range checkpointsByTenant {
		activeCheckpointsMetric.WithLabelValues(s.getMetricsTenantName(tenantID)).Set(float64(count))
	}

	// Active transformation keys by tenant
	transformationKeysByTenant := make(map[string]int)
	s.activeTransformationKeys.Range(func(key, _ interface{}) bool {
		tkpt, ok := key.(TransformationKeyPerTenant)
		if ok {
			transformationKeysByTenant[tkpt.tenantID]++
		}
		return true
	})
	for tenantID, count := range transformationKeysByTenant {
		activeTransformationKeysMetric.WithLabelValues(s.getMetricsTenantName(tenantID)).Set(float64(count))
	}

	// Pending ANN indexes by tenant and transformation key
	s.pendingAnnIndexMutex.RLock()
	pendingIndexesByTenantAndTKey := make(map[string]map[string]int)
	for key := range s.pendingAnnIndexes {
		if _, ok := pendingIndexesByTenantAndTKey[key.tenantID]; !ok {
			pendingIndexesByTenantAndTKey[key.tenantID] = make(map[string]int)
		}
		pendingIndexesByTenantAndTKey[key.tenantID][key.transformationKey]++
	}
	s.pendingAnnIndexMutex.RUnlock()
	for tenantID, tkeys := range pendingIndexesByTenantAndTKey {
		tenant := s.getMetricsTenantName(tenantID)
		for tkey, count := range tkeys {
			pendingAnnIndexesMetric.WithLabelValues(tenant, tkey).Set(float64(count))
		}
	}
}

func (s *workingSetStore) countActiveCheckpoints() int {
	count := 0
	s.activeCheckpoints.Range(func(_, _ interface{}) bool {
		count++
		return true
	})
	return count
}

func (s *workingSetStore) updateActiveIndex(idxKey AnnIndexKey, signature []int, blobCount int) error {
	// Update the active ANN index last used timestamp
	timestamp := time.Now()
	annIndexInfo, annIndexInfoFound := s.activeAnnIndexes.Load(idxKey)
	if annIndexInfoFound {
		// If the index is the same and was updated recently (within 1 hour), don't update it
		if annIndexInfo.(AnnIndexInfo).timestamp.After(timestamp.Add(-reuseExistingTimestampThreshold)) {
			return nil
		}
	}

	s.activeAnnIndexes.Store(idxKey, AnnIndexInfo{
		timestamp: timestamp,
		blobCount: blobCount,
		signature: signature,
	})
	return s.bigtableHelper.WriteActiveIndex(s.minHashVersion, idxKey.tenantID, idxKey.annIndexID, idxKey.transformationKey, timestamp, signature, blobCount)
}

func (s *workingSetStore) updateActiveCheckpoint(key CheckpointKey, renew bool) error {
	timestamp := time.Now()
	activeCheckpointInfo, activeCheckpointInfoFound := s.activeCheckpoints.Load(key)

	// for new checkpoints, set the last renewed time to now
	lastRenewedTime := timestamp
	if activeCheckpointInfoFound && !renew {
		// If the checkpoint exists, reuse the existing last renewed time
		// UNLESS renew is specified as part of renewCheckpoints()
		lastRenewedTime = activeCheckpointInfo.(ActiveCheckpointInfo).lastRenewedTime
	}
	s.activeCheckpoints.Store(key, ActiveCheckpointInfo{lastUsedTime: timestamp, lastRenewedTime: lastRenewedTime})
	return s.bigtableHelper.WriteActiveCheckpoint(key.tenantID, key.checkpointID, timestamp, lastRenewedTime)
}

func (s *workingSetStore) printStats(ctx context.Context) {
	log.Info().Msgf("Working set store stats: %d active checkpoints, %d active transformation keys",
		s.countActiveCheckpoints(), s.countActiveTransformationKeys())
	log.Info().Msgf("%d checkpoints cached, cache allocation: %d bytes",
		s.checkpointBlobsCache.NumEntries(), s.checkpointBlobsCache.MemorySizeInBytes())
	log.Info().Msgf("%d ANN indexes cached, cache allocation: %d bytes",
		s.annIndexBlobsCache.NumEntries(), s.annIndexBlobsCache.MemorySizeInBytes())

	allBlobNameSet := s.getBlobNamesPerTenant(ctx)

	// cancel at 3/4 of the allotted time between loops to ensure that we don't
	// accidentally elongate time between reports
	deadlineCtx, cancel := context.WithDeadline(ctx, time.Now().Add(time.Duration(float64(s.statsLoop)*0.75)))
	defer cancel()

	loggedCancellation := false
	for tenantID, blobNameSet := range allBlobNameSet {
		tenant := s.getMetricsTenantName(tenantID)
		log.Debug().Msgf("Tenant %v: %d blob names", tenant, len(blobNameSet))
		blobCountMetric.WithLabelValues(tenant).Observe(float64(len(blobNameSet)))

		blobNameList := make([]blob_names.BlobName, 0, len(blobNameSet))
		if _, ok := s.hourlyResidentSet[tenantID]; !ok {
			s.hourlyResidentSet[tenantID] = make(map[blob_names.BlobName]struct{})
		}
		for blobName := range blobNameSet {
			s.hourlyResidentSet[tenantID][blobName] = struct{}{}
			blobNameList = append(blobNameList, blobName)
		}
		if s.publishBlobindexMetrics {
			if deadlineCtx.Err() == nil {
				s.submitBlobIndexStatsForTenant(deadlineCtx, tenantID, tenant, blobNameList)
			} else if !loggedCancellation {
				log.Warn().Msgf("Context was cancelled before stats submission completion: %s", deadlineCtx.Err().Error())
				loggedCancellation = true
			}
		}
	}
}

func (s *workingSetStore) renewCheckpoints(ctx context.Context) {
	type checkpointInfo struct {
		lastRenewedTime time.Time
		checkpointKey   CheckpointKey
	}

	// Track the least recently renewed checkpoint for each tenant
	leastRecentlyRenewedCheckpoints := make(map[string]time.Time)
	var checkpointsToRenew []checkpointInfo

	s.activeCheckpoints.Range(func(key, value interface{}) bool {
		activeCheckpointInfo, ok := value.(ActiveCheckpointInfo)
		if !ok {
			log.Error().Msgf("Failed to get active checkpoint info for %v", key)
			return true
		}
		// Log checkpoints that do not have timestamp set. These are legacy checkpoints
		if activeCheckpointInfo.lastRenewedTime.IsZero() {
			log.Debug().Msgf("Last renewed time is not set for %v", key)
		}
		cutoffTime := time.Now().Add(-s.checkpointRenewalInterval)

		if _, ok := leastRecentlyRenewedCheckpoints[key.(CheckpointKey).tenantID]; !ok || activeCheckpointInfo.lastRenewedTime.Before(leastRecentlyRenewedCheckpoints[key.(CheckpointKey).tenantID]) {
			leastRecentlyRenewedCheckpoints[key.(CheckpointKey).tenantID] = activeCheckpointInfo.lastRenewedTime
		}

		// if the checkpoint has not been renewed in the last renewal interval
		// add it to the list of checkpoints to renew
		if activeCheckpointInfo.lastRenewedTime.Before(cutoffTime) {
			checkpointsToRenew = append(checkpointsToRenew, checkpointInfo{
				checkpointKey:   key.(CheckpointKey),
				lastRenewedTime: activeCheckpointInfo.lastRenewedTime,
			})
		}
		return true
	})

	// Publish the age of the least recently renewed checkpoint for each tenant
	for tenantID, oldestCheckpointTime := range leastRecentlyRenewedCheckpoints {
		currentTime := time.Now()
		checkpointAge := currentTime.Sub(oldestCheckpointTime).Hours()
		leastRecentlyRenewedCheckpointMetric.WithLabelValues(s.getMetricsTenantName(tenantID)).Set(checkpointAge)
	}

	// Sort the checkpoints to renew by last renewed time
	sort.Slice(checkpointsToRenew, func(i, j int) bool {
		return checkpointsToRenew[i].lastRenewedTime.Before(checkpointsToRenew[j].lastRenewedTime)
	})

	for _, checkpoint := range checkpointsToRenew {
		startTime := time.Now()
		scheduledRenewalTime := checkpoint.lastRenewedTime.Add(s.checkpointRenewalInterval)
		requestContext, err := s.requestContextHelper.GetBackgroundRequestContext(checkpoint.checkpointKey.tenantID)
		if err != nil {
			log.Error().Err(err).Msgf("Failed to get request context for tenant %s", checkpoint.checkpointKey.tenantID)
			continue
		}
		resp, err := s.contentManagerClient.ExtendCheckpointLifetimes(
			ctx,
			checkpoint.checkpointKey.tenantID,
			checkpoint.checkpointKey.checkpointID,
			requestContext,
		)
		if err != nil {
			log.Error().Err(err).Msgf("Failed to extend checkpoint lifetime for %v", checkpoint.checkpointKey)
			continue
		}
		if err := s.updateActiveCheckpoint(checkpoint.checkpointKey, true); err != nil {
			log.Error().Err(err).Msgf("Failed to update active checkpoint for %v", checkpoint.checkpointKey)
		}

		renewalCompletionTime := time.Now()
		checkpointRenewalQueuingDelay := renewalCompletionTime.Sub(scheduledRenewalTime).Seconds()
		checkpointRenewalQueuingLatencyMetric.WithLabelValues(s.getMetricsTenantName(checkpoint.checkpointKey.tenantID)).Observe(checkpointRenewalQueuingDelay)

		// Adds rate limiting logic.
		elapsedTime := time.Since(startTime).Seconds()
		checkpointSize := float64(resp.TotalBlobsCount)
		if checkpointSize > elapsedTime*s.allowableRenewalsPerSecond {
			log.Debug().Msgf("Checkpoint %v renewal took %v seconds for %v blobs, which is more than the allowable rate of %v blobs/second", checkpoint.checkpointKey, elapsedTime, checkpointSize, s.allowableRenewalsPerSecond)

			// Sleep until the time it would take to process the checkpoints blobs at the allowable rate
			time.Sleep(time.Duration((checkpointSize-elapsedTime*s.allowableRenewalsPerSecond)/s.allowableRenewalsPerSecond) * time.Second)
		}
	}
}

func (s *workingSetStore) resolveBlobNames(ctx context.Context, tenantID string, blobs []*blobs_pb.Blobs) []blob_names.BlobName {
	blobNames := make([]blob_names.BlobName, 0)

	for _, b := range blobs {
		var currentBlobNames []blob_names.BlobName
		if b.BaselineCheckpointId == nil || *b.BaselineCheckpointId == "" {
			currentBlobNames = []blob_names.BlobName{}
		} else {
			checkpointKey := CheckpointKey{tenantID: tenantID, checkpointID: *b.BaselineCheckpointId}
			cpBlobs, err := s.getBlobNamesForCheckpoint(checkpointKey, "Tenant blob name stats")
			if err != nil {
				log.Warn().Err(err).Msgf("Tenant blob name stats failed to get blob names for checkpoint: %v", checkpointKey)
				currentBlobNames = []blob_names.BlobName{}
			} else {
				currentBlobNames = cpBlobs
			}
		}

		// Apply deletes
		deletedSet := make(map[string]bool)
		for _, blob := range b.Deleted {
			deletedSet[string(blob)] = true
		}

		// Filter out deleted blobs
		filteredBlobNames := make([]blob_names.BlobName, 0, len(currentBlobNames))
		for _, blobName := range currentBlobNames {
			if !deletedSet[string(blobName)] {
				filteredBlobNames = append(filteredBlobNames, blobName)
			}
		}

		// Add new blobs
		for _, blob := range b.Added {
			filteredBlobNames = append(filteredBlobNames, blob_names.NewBlobNameFromBytes(blob))
		}

		blobNames = append(blobNames, filteredBlobNames...)
	}

	return blobNames
}

func (s *workingSetStore) getBlobNamesPerTenant(ctx context.Context) map[string]map[blob_names.BlobName]bool {
	// from tenant to blob names set
	allBlobNameSet := make(map[string]map[blob_names.BlobName]bool)

	s.activeCheckpoints.Range(
		func(key, value interface{}) bool {
			// Skip checkpoints that have not been updated in the last session timeout
			if time.Now().Sub(value.(ActiveCheckpointInfo).lastUsedTime) > s.sessionTimeout {
				return true
			}

			blobNames, err := s.getBlobNamesForCheckpoint(key.(CheckpointKey), "Tenant blob name stats")
			if err != nil {
				log.Warn().Err(err).Msgf("Tenant blob name stats failed to get blob names for checkpoint: %v", key)
				blobNames = []blob_names.BlobName{}
			}
			tenant := s.getMetricsTenantName(key.(CheckpointKey).tenantID)
			blobCountBySessionMetric.WithLabelValues(tenant).Observe(float64(len(blobNames)))

			tenantBlobNameSet, ok := allBlobNameSet[key.(CheckpointKey).tenantID]
			if !ok {
				tenantBlobNameSet = make(map[blob_names.BlobName]bool)
				allBlobNameSet[key.(CheckpointKey).tenantID] = tenantBlobNameSet
			}

			for _, blobName := range blobNames {
				tenantBlobNameSet[blobName] = true
			}

			return true
		},
	)

	return allBlobNameSet
}

func (s *workingSetStore) submitBlobIndexStatsForTenant(ctx context.Context, tenantID string, metricsTenantName string, blobNameList []blob_names.BlobName) {
	indexedBlobsCnt := make(map[string]int)
	unindexedBlobsCnt := make(map[string]int)
	rc, err := s.requestContextHelper.GetBackgroundRequestContext(tenantID)
	if err != nil {
		log.Error().Str("tenant_id", tenantID).Msgf("Failed to get requestContext: %s", err.Error())
		return
	}

	batchSize := grpcDefaultBatchSize
	for len(blobNameList) > 0 {
		partitionIdx := min(batchSize, len(blobNameList))
		batchInfo, err := s.contentManagerClient.BatchGetBlobInfo(ctx, blobNameList[:partitionIdx], tenantID, rc)
		if err != nil {
			// attempt to deal with response size issues by shrinking the number of
			// requested blobinfos
			if s, ok := status.FromError(err); ok && s.Code() == codes.ResourceExhausted && batchSize > 1 {
				nextBatchSize := batchSize / 2
				log.Info().
					Int("current_batch_size", batchSize).
					Int("new_batch_size", nextBatchSize).
					Msg("Current batch size is too large, lowering and retrying")
				batchSize /= 2
				continue
			}
			// log and move on
			log.Warn().Msgf("Failed to fetch blobinfos for blobs: %v", blobNameList[:partitionIdx])
		} else {
			for _, info := range batchInfo.GetBlobInfos() {
				// if queried blob is unknown to content manager skip
				if info == nil || info.GetBlobInfo() == nil {
					continue
				}
				indexedKeys := make(map[string]struct{})
				for _, tfKey := range info.GetBlobInfo().GetUploadedTransformationKeys() {
					indexedKeys[tfKey] = struct{}{}
				}
				for _, seenKey := range info.GetBlobInfo().GetInformedTransformationKeys() {
					if _, ok := indexedBlobsCnt[seenKey]; !ok {
						indexedBlobsCnt[seenKey] = 0
					}
					if _, ok := unindexedBlobsCnt[seenKey]; !ok {
						unindexedBlobsCnt[seenKey] = 0
					}
					// assumption informedTfKeys IS_SUPERSET_OF uploadedTfKeys
					if _, ok := indexedKeys[seenKey]; ok {
						indexedBlobsCnt[seenKey] += 1
					} else {
						unindexedBlobsCnt[seenKey] += 1
					}
				}
			}
		}
		blobNameList = blobNameList[partitionIdx:]
	}
	for key, count := range indexedBlobsCnt {
		blobsIndexStatusByTfKeyMetric.WithLabelValues(metricsTenantName, blobIndexStatusIndexed, key).Set(float64(count))
	}
	for key, count := range unindexedBlobsCnt {
		blobsIndexStatusByTfKeyMetric.WithLabelValues(metricsTenantName, blobIndexStatusUnindexed, key).Set(float64(count))
	}
}

// resolveAnnIndexAndAddToLSHIndex resolves the blob names for an ANN index, computes its minhash signature,
// and adds it to the LSH index. It returns the blob names and an error if the operation fails.
func (s *workingSetStore) resolveAnnIndexAndAddToLSHIndex(ctx context.Context, idxKey AnnIndexKey) ([]blob_names.BlobName, error) {
	// Resolve + cache the blob names for the new ANN index
	indexBlobNames, err := s.resolveAnnIndex(ctx, idxKey)
	if err != nil {
		return nil, err
	}

	// Compute the minhash signature for the new ANN index
	signature := computeMinHashSignature(idxKey.annIndexID, indexBlobNames)

	// Add the new ANN index's signature hashes into the corresponding LSH index
	s.addAnnIndexToLSHIndex(idxKey, signature)

	log.Info().Msgf("ANN index successfully resolved and added to LSH index: %v", idxKey)
	// Update the active ANN index last used timestamp
	if err := s.updateActiveIndex(idxKey, signature, len(indexBlobNames)); err != nil {
		log.Error().Err(err).Msgf("Failed to update active index for %v", idxKey)
	}

	return indexBlobNames, nil
}

func (s *workingSetStore) handleAnnIndexCreationResponse(ctx context.Context, tenantID string, transformationKey string, checkpointID string, indexID string) error {
	key := CheckpointPerTransformationKey{
		tenantID:          tenantID,
		checkpointID:      checkpointID,
		transformationKey: transformationKey,
	}

	s.pendingAnnIndexMutex.RLock()
	timestamp, exists := s.pendingAnnIndexes[key]
	s.pendingAnnIndexMutex.RUnlock()
	if !exists {
		log.Warn().Msgf("Received unexpected ANN index creation response for %v", key)
		return nil
	}
	tenant := s.getMetricsTenantName(tenantID)
	annIndexCreationLatencyMetric.WithLabelValues(tenant, transformationKey).Observe(time.Since(timestamp).Seconds())
	annIndexOpsMetric.WithLabelValues(tenant, transformationKey, "created").Inc()

	// Defer removal of the pending index creation request after we have resolved the index.
	defer func() {
		s.pendingAnnIndexMutex.Lock()
		err := s.bigtableHelper.DeletePendingIndex(tenantID, checkpointID, transformationKey)
		if err != nil {
			log.Error().Err(err).Msgf("Failed to delete pending index %v from Bigtable", key)
			// cleanup() will reap this pendingAnnIndexes entry eventually.
		} else {
			delete(s.pendingAnnIndexes, key)
		}
		s.pendingAnnIndexMutex.Unlock()
	}()

	log.Info().Msgf("Received ANN index creation response for checkpoint: %v new index: %s", key, indexID)

	idxKey := AnnIndexKey{
		transformationKey: transformationKey,
		annIndexID:        indexID,
		tenantID:          tenantID,
	}

	indexBlobNames, err := s.resolveAnnIndexAndAddToLSHIndex(ctx, idxKey)
	if err != nil {
		// If index resolution failed we will bail out. Since we haven't updated the checkpoint
		// to ANN index mapping yet the next CreateAnnIndexForCheckpoint call will request a new
		// index creation.
		log.Error().Err(err).Msgf("ANN index creation response handler failed to resolve ANN index %v", idxKey)
		return err
	}

	cpKey := CheckpointKey{tenantID: tenantID, checkpointID: checkpointID}
	cpBlobNames, err := s.getBlobNamesForCheckpoint(cpKey, "ANN index creation response handler")
	if err != nil {
		log.Error().Err(err).Msgf("ANN index creation response handler failed to resolve checkpoint %v", cpKey)
		return err
	}

	added, removed := computeBlobSetDelta(indexBlobNames, cpBlobNames)
	log.Info().Msgf("New ANN index %s for checkpoint %s has delta: (+%d, -%d)",
		indexID, checkpointID, len(added), len(removed))

	// Finalize the checkpoint to ANN index mapping for this checkpoint
	value := AnnIndexContext{
		annIndexID: indexID,
		added:      added,
		removed:    removed,
	}
	s.checkpointToAnnIndexMap.Store(key, value)

	err = s.persistCheckpointToIndexMapping(ctx, checkpointID, idxKey, added, removed)
	if err != nil {
		log.Error().Msgf("Failed to persist checkpoint->index mapping for checkpoint %s error: %v",
			checkpointID, err)
		return err
	}

	return nil
}

func (s *workingSetStore) runAnnIndexCreationListenerLoop(ctx context.Context) error {
	for {
		select {
		case <-time.After(30 * time.Second):
			// Having to retry this is not an expected case but it helps us recover from errors
			s.runAnnIndexCreationListener(ctx)
		case <-ctx.Done():
			return ctx.Err()
		}
	}
}

func (s *workingSetStore) runAnnIndexCreationListener(ctx context.Context) error {
	err := s.annIndexCreationSub.Receive(ctx, func(ctx context.Context, msg *pubsub.Message) {
		var pubsubMsg checkpoint_indexer.CheckpointIndexerPubsub
		if err := proto.Unmarshal(msg.Data, &pubsubMsg); err != nil {
			log.Error().Err(err).Msg("Failed to unmarshal checkpoint indexer pubsub message")
			return
		}

		if response := pubsubMsg.GetCreateIndexResponse(); response != nil {
			err := s.handleAnnIndexCreationResponse(
				ctx,
				response.GetTenantId(),
				response.GetTransformationKey(),
				response.GetCheckpointId(),
				response.GetIndexId(),
			)
			if err != nil {
				log.Error().Err(err).Msgf("Failed to handle ANN index creation response: %v", msg)
				return
			} else {
				msg.Ack()
			}
		} else {
			// Ignore CreateIndexRequest messages
			log.Debug().Msgf("Ignoring pubsub message: %v", msg.ID)
			msg.Ack()
		}
	})
	if err != nil {
		if errors.Is(err, context.Canceled) {
			log.Warn().Err(err).Msg("Context cancelled during checkpoint index creation subscription")
		} else {
			log.Error().Err(err).Msg("Failed to subscribe to checkpoint index creation requests")
		}
		return err
	}

	return nil
}

// RegisterWorkingSet is being deprecated.
// Currently it is a no-op in the working set store
func (s *workingSetStore) RegisterWorkingSet(ctx context.Context, tenantID string, sessionId requestcontext.RequestSessionId, blobs []*blobs_pb.Blobs) error {
	return nil
}

// GetWorkingSet retrieves a WorkingSet by sessionId
// This is being deprecated along with RegisterWorkingSet
func (s *workingSetStore) GetWorkingSet(sessionId requestcontext.RequestSessionId) (*WorkingSet, error) {
	return nil, nil
}

func (s *workingSetStore) sendWorkingsetDeltaMetrics(tenantID string, prevWs *WorkingSet, curWs *WorkingSet) {
	if len(prevWs.blobs) != len(curWs.blobs) {
		// if the length of the blobs array has changed, we can't compute the deltas
		// anymore as it is unclear how to re-map the repos that the blobs came from
		return
	}
	deleted := 0
	added := 0
	for i := range len(prevWs.blobs) {
		prevBlobsSet := s.computeBlobSet(tenantID, prevWs.blobs[i])
		curBlobsSet := s.computeBlobSet(tenantID, curWs.blobs[i])
		if prevBlobsSet == nil || curBlobsSet == nil {
			// if either async checkpoint computation is not done yet don't post the
			// metric
			return
		}
		for blob := range *prevBlobsSet {
			if _, found := (*curBlobsSet)[blob]; !found {
				deleted += 1
			}
		}
		for blob := range *curBlobsSet {
			if _, found := (*prevBlobsSet)[blob]; !found {
				added += 1
			}
		}
	}
	tenant := s.getMetricsTenantName(tenantID)
	blobsAddedMetric.WithLabelValues(tenant).Observe(float64(added))
	blobsDeletedMetric.WithLabelValues(tenant).Observe(float64(deleted))
}

func (s *workingSetStore) computeBlobSet(tenantID string, blobs *blobs_pb.Blobs) *map[string]struct{} {
	var empty struct{}
	if blobs.BaselineCheckpointId == nil {
		// checkpoints are not guaranteed for blob deltas (e.g. initial diff, small repo)
		return nil
	}

	checkpointKey := CheckpointKey{tenantID: tenantID, checkpointID: *blobs.BaselineCheckpointId}
	blobNames, err := s.getBlobNamesForCheckpoint(checkpointKey, "Workingset delta metrics")
	if err != nil {
		log.Warn().Err(err).Msgf("Workingset delta metrics failed to get blob names for checkpoint: %v", checkpointKey)
		return nil
	}
	// we rebuild the map on each request instead of e.g. storing the checkpoints as
	// "sets". This is because go has no efficient deep-copy operator for maps so we'd
	// need to rebuild it from scratch to copy it anyways if we stored it. If performance
	// becomes an issue, we should look into union datastructures instead to skip the
	// rebuild.
	deletedSet := make(map[string]struct{})
	for _, blob := range blobs.Deleted {
		deletedSet[string(blob)] = empty
	}
	blobsSet := make(map[string]struct{})
	for _, blobName := range blobNames {
		if _, found := deletedSet[string(blobName)]; !found {
			blobsSet[string(blobName)] = empty
		}
	}
	// add new blobs
	for _, blob := range blobs.Added {
		blobsSet[string(blob)] = empty
	}
	return &blobsSet
}

type blobNameOrString interface {
	blob_names.BlobName | string | []byte
}

func formatBlobsList(blobs []*blobs_pb.Blobs) string {
	if len(blobs) == 0 {
		return "[]"
	}
	buf := strings.Builder{}
	for i, blobs := range blobs {
		if i > 0 {
			buf.WriteString(", ")
		}
		buf.WriteString(formatBlobs(blobs))
	}
	return buf.String()
}

func formatBlobs(blobs *blobs_pb.Blobs) string {
	if blobs == nil {
		return "Blobs{nil}"
	}
	baselineCheckpointId := "(nil)"
	if blobs.BaselineCheckpointId != nil {
		baselineCheckpointId = *blobs.BaselineCheckpointId
	}
	return fmt.Sprintf("Blobs{baseline_checkpoint_id=%s, added=%v, deleted=%v}",
		baselineCheckpointId,
		formatBlobNames(blobs.Added),
		formatBlobNames(blobs.Deleted),
	)
}

func formatBlobName[T blobNameOrString](blobName T) string {
	if blobName, ok := any(blobName).([]byte); ok {
		return fmt.Sprintf("%x", blobName)
	}
	return fmt.Sprintf("%v", blobName)
}

func formatBlobNames[T blobNameOrString](blobNames []T) string {
	if len(blobNames) == 0 {
		return "[]"
	}
	if len(blobNames) > 2 {
		return fmt.Sprintf("[%s, ... (%d items), %s]", formatBlobName(blobNames[0]), len(blobNames)-2, formatBlobName(blobNames[len(blobNames)-1]))
	} else if len(blobNames) == 2 {
		return fmt.Sprintf("[%s, %s]", formatBlobName(blobNames[0]), formatBlobName(blobNames[1]))
	} else {
		return fmt.Sprintf("[%s]", formatBlobName(blobNames[0]))
	}
}

// submitCheckpointResolutionTask submits a task to resolve a checkpoint and returns the task
// the caller needs to hold checkpointTaskMutex in write mode
func (s *workingSetStore) submitCheckpointResolutionTask(checkpointKey CheckpointKey) pond.Task {
	// Track this async operation for testing
	if s.isRunningUnderTest {
		s.asyncProcessingWG.Add(1)
	}

	task := s.ioTaskPool.SubmitErr(func() error {
		if s.isRunningUnderTest {
			defer s.asyncProcessingWG.Done()
		}
		return s.resolveCheckpoint(s.backgroundCtx, checkpointKey)
	})
	s.pendingCheckpointTasks[checkpointKey] = task
	return task
}

func (s *workingSetStore) resolveCheckpoint(ctx context.Context, checkpointKey CheckpointKey) error {
	startTime := time.Now()
	blobSetKey := CheckpointKeyToBlobSetKey(checkpointKey)

	defer func() {
		s.checkpointTaskMutex.Lock()
		delete(s.pendingCheckpointTasks, checkpointKey)
		s.checkpointTaskMutex.Unlock()
	}()

	requestContext, err := s.requestContextHelper.GetBackgroundRequestContext(checkpointKey.tenantID)
	if err != nil {
		log.Error().Msgf("Failed to get request context: %v", err)
		return err
	}

	blobNames, err := s.fetchBlobNamesForCheckpoint(ctx, checkpointKey.checkpointID, requestContext)
	if err != nil {
		log.Error().Msgf("Failed to resolve blob names: %v", err)
		return err
	}

	tenant := s.getMetricsTenantName(checkpointKey.tenantID)
	checkpointSizeMetric.WithLabelValues(tenant).Observe(float64(len(blobNames)))

	if err := s.checkpointBlobsCache.SetBlobSet(blobSetKey, blobNames); err != nil {
		log.Error().Err(err).Msgf("Failed to store checkpoint blob names in cache for key %v", checkpointKey)
		return err
	}

	elapsedTime := time.Since(startTime).Seconds()
	resolutionLatencyMetric.WithLabelValues(s.getMetricsTenantName(checkpointKey.tenantID), "", "checkpoint").Observe(elapsedTime)
	log.Info().Msgf("Checkpoint resolution for %v took %v seconds", checkpointKey, elapsedTime)

	return nil
}

func (c *workingSetStore) fetchBlobNamesForCheckpoint(ctx context.Context, checkpointID string, requestContext *requestcontext.RequestContext) ([]blob_names.BlobName, error) {
	blobNames := make([]blob_names.BlobName, 0)
	b, err := c.contentManagerClient.GetAllBlobsFromCheckpoint(ctx, checkpointID, nil, requestContext)
	if err != nil {
		return nil, err
	}
	blobNames = append(blobNames, b...)

	log.Info().Msgf("checkpoint: %s: %v", checkpointID, formatBlobNames(blobNames))
	return blobNames, nil
}

func (s *workingSetStore) triggerAnnIndexCreation(ctx context.Context, tenantID string, checkpointID string, transformationKey string) error {
	key := CheckpointPerTransformationKey{tenantID: tenantID, transformationKey: transformationKey, checkpointID: checkpointID}

	timestamp := time.Now()
	// See if we are waiting on checkpoint indexer to index this checkpoint/tkey
	s.pendingAnnIndexMutex.Lock()
	if _, found := s.pendingAnnIndexes[key]; found {
		log.Info().Msgf("Waiting for checkpoint indexer to create ANN index for %v", key)
		s.pendingAnnIndexMutex.Unlock()
		return nil
	}
	s.pendingAnnIndexes[key] = timestamp
	s.pendingAnnIndexMutex.Unlock()

	log.Info().Msgf("Triggering creation of a new ANN index for %v", key)

	re, err := regexp.Compile(s.checkpointIndexerRegexFilter)
	if err != nil {
		log.Warn().Err(err).Msgf("Failed to compile checkpoint indexer regex filter: %s", s.checkpointIndexerRegexFilter)
	} else {
		// filters specific checkpoint IDs out, only runs if regex was valid
		if re.MatchString(checkpointID) {
			log.Debug().Msgf("Checkpoint %s matches regex filter %s, skipping index creation", checkpointID, s.checkpointIndexerRegexFilter)
			return nil
		}
	}

	// Prepare the pub/sub message to trigger the index creation
	createIndexRequest := &checkpoint_indexer.CreateIndexRequest{
		TenantId:          tenantID,
		CheckpointId:      checkpointID,
		TransformationKey: transformationKey,
	}
	message := &checkpoint_indexer.CheckpointIndexerPubsub{
		Message: &checkpoint_indexer.CheckpointIndexerPubsub_CreateIndexRequest{
			CreateIndexRequest: createIndexRequest,
		},
	}
	messageBytes, err := proto.Marshal(message)
	if err != nil {
		log.Error().Err(err).Msg("Failed to marshal checkpoint index creation request")
		s.pendingAnnIndexMutex.Lock()
		delete(s.pendingAnnIndexes, key)
		s.pendingAnnIndexMutex.Unlock()
		return err
	}

	// Publish the message to the pub/sub topic for index creation
	result := s.annIndexCreationTopic.Publish(ctx, &pubsub.Message{
		Data: messageBytes,
	})
	id, err := result.Get(ctx)
	if err != nil {
		log.Error().Err(err).Msg("Failed to publish checkpoint index creation request")
		s.pendingAnnIndexMutex.Lock()
		delete(s.pendingAnnIndexes, key)
		s.pendingAnnIndexMutex.Unlock()
		return err
	}

	log.Info().Msgf("Published checkpoint index creation request with ID: %s for checkpoint: %s, transformation: %s",
		id, checkpointID, transformationKey)

	err = s.bigtableHelper.WritePendingIndex(tenantID, checkpointID, transformationKey, timestamp)
	if err != nil {
		log.Error().Err(err).Msgf("Failed to write pending index for %v", key)
	}

	return err
}

func (s *workingSetStore) resolveAnnIndex(ctx context.Context, indexKey AnnIndexKey) ([]blob_names.BlobName, error) {
	startTime := time.Now()
	blobSetKey := AnnIndexKeyToBlobSetKey(indexKey)

	requestContext, err := s.requestContextHelper.GetBackgroundRequestContext(indexKey.tenantID)
	if err != nil {
		log.Error().Msgf("Failed to get request context: %v", err)
		return nil, err
	}

	blobNames, err := s.fetchBlobNamesForAnnIndex(ctx, indexKey, requestContext)
	if err != nil {
		log.Error().Msgf("Failed to resolve blob names for ann index: %v", err)
		return nil, err
	}

	if err := s.annIndexBlobsCache.SetBlobSet(blobSetKey, blobNames); err != nil {
		log.Error().Err(err).Msgf("Failed to store ann index blob names in cache for %v", indexKey)
	}

	elapsedTime := time.Since(startTime).Seconds()
	resolutionLatencyMetric.WithLabelValues(s.getMetricsTenantName(indexKey.tenantID), indexKey.transformationKey, "ann_index").Observe(elapsedTime)
	log.Info().Msgf("ANN index resolution for %v took %v seconds", indexKey, elapsedTime)

	return blobNames, nil
}

func (c *workingSetStore) fetchBlobNamesForAnnIndex(ctx context.Context, indexKey AnnIndexKey, requestContext *requestcontext.RequestContext) ([]blob_names.BlobName, error) {
	res, err := c.contentManagerClient.GetAnnIndexBlobInfos(ctx,
		contentmanagerclient.AnnIndexKey{
			TenantId:          &indexKey.tenantID,
			TransformationKey: indexKey.transformationKey,
			IndexId:           indexKey.annIndexID,
		}, *requestContext)
	if err != nil {
		return nil, err
	}

	blobNames := make([]blob_names.BlobName, len(res.Infos))
	for i, info := range res.Infos {
		blobNames[i] = info.BlobName
	}

	log.Info().Msgf("ann index: %s: %v", indexKey.annIndexID, formatBlobNames(blobNames))
	return blobNames, nil
}

func (s *workingSetStore) persistCheckpointToIndexMapping(ctx context.Context, checkpointID string, idxKey AnnIndexKey, added []blob_names.BlobName, removed []blob_names.BlobName) error {
	requestContext, err := s.requestContextHelper.GetBackgroundRequestContext(idxKey.tenantID)
	if err != nil {
		return fmt.Errorf("failed to get request context: %w", err)
	}

	_, err = s.contentManagerClient.AddAnnIndexMapping(
		ctx,
		&idxKey.tenantID,
		idxKey.transformationKey,
		checkpointID,
		idxKey.annIndexID,
		added,
		removed,
		*requestContext,
	)
	if err != nil {
		return fmt.Errorf("failed to add ANN index mapping: %w", err)
	}

	log.Info().Msgf("Persisted checkpoint to index mapping checkpoint: %s -> ANN index: %s", checkpointID, idxKey.annIndexID)

	return nil
}

// Adds signature hashes for the given ANN index to the corresponding LSH index
// This facilitates searching for candidate ANN indexes for a given checkpoint
func (s *workingSetStore) addAnnIndexToLSHIndex(key AnnIndexKey, signature []int) {
	s.lshIndexMutex.Lock()
	defer s.lshIndexMutex.Unlock()

	indexKey := LSHIndexKey{tenantID: key.tenantID, transformationKey: key.transformationKey}
	lsh_index, exists := s.lsh_indexes[indexKey]
	if !exists {
		lsh_index = NewLSHIndex()
		s.lsh_indexes[indexKey] = lsh_index
	}
	lsh_index.AddAnnIndex(key.annIndexID, signature)
}

// Returns the difference (added,removed) in blob names between set1 and set2
// For example if set1 = [a,b,c] and set2 = [b,c,d] then the delta is (added=[d], removed=[a])
func computeBlobSetDelta(set1 []blob_names.BlobName, set2 []blob_names.BlobName) (added []blob_names.BlobName, removed []blob_names.BlobName) {
	set1Map := make(map[string]struct{})
	for _, blobName := range set1 {
		set1Map[string(blobName)] = struct{}{}
	}

	set2Map := make(map[string]struct{})
	for _, blobName := range set2 {
		set2Map[string(blobName)] = struct{}{}
	}

	for blobName := range set1Map {
		if _, found := set2Map[blobName]; !found {
			removed = append(removed, blob_names.BlobName(blobName))
		}
	}

	for blobName := range set2Map {
		if _, found := set1Map[blobName]; !found {
			added = append(added, blob_names.BlobName(blobName))
		}
	}

	return added, removed
}

// getBlobNamesForCheckpoint retrieves the blob names for a checkpoint, resolving it if necessary.
// It returns the blob names and an error if the checkpoint could not be resolved.
func (s *workingSetStore) getBlobNamesForCheckpoint(checkpointKey CheckpointKey, logPrefix string) ([]blob_names.BlobName, error) {
	// First check if checkpoint is already in cache
	cpBlobs, err := s.checkpointBlobsCache.GetBlobSet(CheckpointKeyToBlobSetKey(checkpointKey))
	if err != nil {
		if !errors.Is(err, blobset_cache.ErrEntryNotFound) {
			return nil, err
		}
		// If the checkpoint isn't in cache we either need to wait for a pending resolution or
		// kick off a new resolution
		var pendingTask pond.Task
		s.checkpointTaskMutex.Lock()
		pendingTaskValue, isPending := s.pendingCheckpointTasks[checkpointKey]
		if isPending {
			log.Info().Msgf("%s needs to wait for pending resolution of checkpoint: %v", logPrefix, checkpointKey)
			pendingTask = pendingTaskValue
		} else {
			log.Info().Msgf("%s needs to resolve checkpoint: %v", logPrefix, checkpointKey)
			pendingTask = s.submitCheckpointResolutionTask(checkpointKey)
		}
		s.checkpointTaskMutex.Unlock()

		err = pendingTask.Wait()
		if err != nil {
			return nil, err
		}

		cpBlobs, err = s.checkpointBlobsCache.GetBlobSet(CheckpointKeyToBlobSetKey(checkpointKey))
		if err != nil {
			return nil, err
		}
	}

	return cpBlobs, nil
}

// For the given checkpoint, first try to find an existing ANN index that is "close enough".
// If we can't find one, create a new ANN index for the checkpoint.
func (s *workingSetStore) findOrCreateAnnIndexForCheckpoint(ctx context.Context, request AnnIndexRequestKey) error {
	startTime := time.Now()
	tenant := s.getMetricsTenantName(request.tenantID)
	defer s.annIndexRequestsMap.Delete(request) // make sure we always clear the pending flag

	checkpointKey := CheckpointKey{tenantID: request.tenantID, checkpointID: request.checkpointID}
	cpBlobs, err := s.getBlobNamesForCheckpoint(checkpointKey, "ANN indexing")
	if err != nil {
		return err
	}

	if len(cpBlobs) < s.minCheckpointSizeToIndex {
		log.Info().Msgf(
			"Checkpoint %v has too few blobs to create index: %d blobs (minimum: %d)",
			checkpointKey, len(cpBlobs), s.minCheckpointSizeToIndex)
		return nil
	}

	// Compute the checkpoint's signature and look for candidate indexes that match with that signature
	cpSignature := computeMinHashSignature(checkpointKey.checkpointID, cpBlobs)

	// Find candidate ANN indexes that have matching hashes with this checkpoint's signature
	s.lshIndexMutex.RLock()
	var candidateIndexes []LSHIndexSearchResult
	lsh_index, exists := s.lsh_indexes[LSHIndexKey{tenantID: request.tenantID, transformationKey: request.transformationKey}]
	if exists {
		lshSearchStartTime := time.Now()
		candidateIndexes = lsh_index.FindCandidates(cpSignature)

		lshSearchLatencyMetric.WithLabelValues(tenant, request.transformationKey).Observe(time.Since(lshSearchStartTime).Seconds())
		lshSearchNumCandidatesMetric.WithLabelValues(tenant, request.transformationKey).Observe(float64(len(candidateIndexes)))
	} else {
		log.Warn().Msgf("No LSH index found for tenant %s and transformation key %s",
			request.tenantID, request.transformationKey)
	}
	s.lshIndexMutex.RUnlock()

	// Evaluate each candidate ANN index to see if we find an index where the delta with the checkpoint is small enough

	// Under the soft threshold, we continue to use the existing mapping and do not trigger new indexing
	// Between the soft and hard threshold, we continue to use the existing mapping, but trigger new indexing
	// Above the hard indexing threshold, we trigger new indexing and use the new mapping.
	triggerReindex := true
	minDelta := math.MaxInt
	var finalIdxKey AnnIndexKey
	var finalIdxDelta AnnIndexContext
	var finalCandidateMatchCount int

	// Iterate over all the top candidates to find the one with the smallest delta.
	// Early termination if we find a match that is below the soft threshold.
	if len(candidateIndexes) > 0 {
		log.Info().Msgf("%d candidates indexes found that are similar to checkpoint %s (will consider top %d)",
			len(candidateIndexes), checkpointKey.checkpointID, maxIndexCandidatesToConsider)
		logNumIndexesWithTopHashCount(candidateIndexes)
		if len(candidateIndexes) > maxIndexCandidatesToConsider {
			candidateIndexes = candidateIndexes[:maxIndexCandidatesToConsider]
		}
		for _, candidateIdx := range candidateIndexes {
			idxKey := AnnIndexKey{tenantID: request.tenantID, transformationKey: request.transformationKey, annIndexID: candidateIdx.Id}
			idxBlobs, err := s.annIndexBlobsCache.GetBlobSet(AnnIndexKeyToBlobSetKey(idxKey))
			if err != nil {
				// If index blobs are not found in the cache synchronously resolve them here
				var err error
				idxBlobs, err = s.resolveAnnIndex(ctx, idxKey)
				if err != nil {
					log.Error().Msgf("Failed to resolve blob names for ann index: %v error: %s", idxKey, err)
					continue
				}
			}

			added, removed := computeBlobSetDelta(idxBlobs, cpBlobs)
			delta := len(added) + len(removed)

			// Keep track of the best mapping we've seen so far with existing candidates
			if delta < minDelta {
				minDelta = delta
				finalIdxDelta = AnnIndexContext{
					annIndexID: candidateIdx.Id,
					added:      added,
					removed:    removed,
				}
				finalIdxKey = idxKey
				finalCandidateMatchCount = candidateIdx.MatchCount
			}

			// No reindex is triggered if there is a small enough delta with an existing mapping.
			// We define small enough as the smallest value between triggerReindexingDeltaThreshold
			// and 10% of the checkpoint's blob count.
			if delta < min(triggerReindexingDeltaThreshold, int(float64(len(cpBlobs))*triggerReindexingRatioThreshold)) {

				log.Info().Msgf("Index %s is a good enough match for checkpoint %s. No reindexing will be triggered",
					idxKey.annIndexID, checkpointKey.checkpointID)
				triggerReindex = false
			}

		}
	} else {
		log.Info().Msgf("No candidate indexes found that are similar to checkpoint %s", checkpointKey.checkpointID)
	}

	// Reuse the index if the delta is small enough. We define small enough as the smallest value between reuseIndexDeltaThreshold and 25% of the checkpoint's blob count
	if minDelta < min(reuseIndexDeltaThreshold, int(float64(len(cpBlobs))*reuseIndexRatioThreshold)) {
		log.Info().Msgf("Index %s will be used for checkpoint %s delta: (+%d,-%d) common signature hashes: %d",
			finalIdxKey.annIndexID, checkpointKey.checkpointID, len(finalIdxDelta.added), len(finalIdxDelta.removed), finalCandidateMatchCount)

		annIndexOpsMetric.WithLabelValues(tenant, request.transformationKey, "reused").Inc()
		annIndexReuseDeltaMetric.WithLabelValues(tenant, request.transformationKey).Observe(float64(minDelta))

		// Update the in-memory checkpoint to index mapping
		key := CheckpointPerTransformationKey{
			tenantID:          request.tenantID,
			transformationKey: request.transformationKey,
			checkpointID:      request.checkpointID,
		}
		s.checkpointToAnnIndexMap.Store(key, finalIdxDelta)

		// Persist the checkpoint->index mapping with the content manager
		err := s.persistCheckpointToIndexMapping(ctx, request.checkpointID, finalIdxKey, finalIdxDelta.added, finalIdxDelta.removed)
		if err != nil {
			log.Error().Msgf("Failed to persist checkpoint->index mapping for checkpoint %s error: %v",
				request.checkpointID, err)
		}
	}

	// Trigger the creation of a new ANN index if no existing mapping has small enough delta.
	if triggerReindex {
		err := s.triggerAnnIndexCreation(ctx, request.tenantID, request.checkpointID, request.transformationKey)
		if err != nil {
			log.Error().Msgf("Failed to trigger creation of a new ANN index for checkpoint %s error: %v",
				checkpointKey.checkpointID, err)
			return err
		}
	}

	// We only post the search latency metric on successful searches, not on error paths.
	annIndexSearchLatencyMetric.WithLabelValues(tenant, request.transformationKey).Observe(time.Since(startTime).Seconds())
	return nil
}

// WaitForAsyncProcessing waits for all async processing operations to complete
// This is used for testing purposes only
func (s *workingSetStore) WaitForAsyncProcessing(ctx context.Context) error {
	if !s.isRunningUnderTest {
		panic("WaitForAsyncProcessing() should only be called when running under test")
	}

	done := make(chan struct{})
	go func() {
		s.asyncProcessingWG.Wait()
		close(done)
	}()

	select {
	case <-done:
		return nil
	case <-ctx.Done():
		return ctx.Err()
	}
}

// logNumIndexesWithTopHashCount calculates and logs how many indexes have the same top match count
// It expects a sorted slice of candidate indexes (sorted by matchCount in descending order)
func logNumIndexesWithTopHashCount(candidateIndexes []LSHIndexSearchResult) {
	if len(candidateIndexes) == 0 {
		return
	}

	topHashCount := candidateIndexes[0].MatchCount
	numIndexesWithTopHashCount := 1
	for i := 1; i < len(candidateIndexes); i++ {
		if candidateIndexes[i].MatchCount == topHashCount {
			numIndexesWithTopHashCount++
		} else {
			break // Stop when we see a different match count since the list is sorted
		}
	}
	log.Info().Msgf("%d hashes are common between the top %d indexes", topHashCount, numIndexesWithTopHashCount)
}

func (s *workingSetStore) CreateAnnIndexForCheckpoint(ctx context.Context, checkpointID string, tenantID string, transformationKey string) error {
	// Refresh the expiration time for the checkpoint
	if err := s.updateActiveCheckpoint(CheckpointKey{tenantID: tenantID, checkpointID: checkpointID}, false); err != nil {
		log.Error().Err(err).Msgf("Failed to update active checkpoint for %v", CheckpointKey{tenantID: tenantID, checkpointID: checkpointID})
	}
	// Refresh the expiration time for the transformation key
	timestamp := time.Now()
	transformationKeyPerTenant := TransformationKeyPerTenant{tenantID: tenantID, transformationKey: transformationKey}
	s.activeTransformationKeys.Store(transformationKeyPerTenant, timestamp)
	if err := s.bigtableHelper.WriteActiveTransformationKey(tenantID, transformationKey, timestamp); err != nil {
		log.Error().Err(err).Msgf("Failed to write active transformation key for %v", transformationKeyPerTenant)
		// continue since subsequent RPC calls will update the active transformation key timestamp
	}

	key := CheckpointPerTransformationKey{
		tenantID:          tenantID,
		transformationKey: transformationKey,
		checkpointID:      checkpointID,
	}
	index, mappingFound := s.checkpointToAnnIndexMap.Load(key)

	if mappingFound {
		annIndexID := index.(AnnIndexContext).annIndexID
		annIndexDelta := index.(AnnIndexContext)

		// Update the active ANN index last used timestamp
		annIndexInfo, annIndexInfoFound := s.activeAnnIndexes.Load(AnnIndexKey{tenantID: tenantID, transformationKey: transformationKey, annIndexID: annIndexID})

		// Utilize the index blob count only if it is valid
		if annIndexInfoFound {
			if err := s.updateActiveIndex(AnnIndexKey{tenantID: tenantID, transformationKey: transformationKey, annIndexID: annIndexID}, annIndexInfo.(AnnIndexInfo).signature, annIndexInfo.(AnnIndexInfo).blobCount); err != nil {
				log.Error().Err(err).Msgf("Failed to update active index for %v", AnnIndexKey{tenantID: tenantID, transformationKey: transformationKey, annIndexID: annIndexID})
			}

			delta := len(annIndexDelta.added) + len(annIndexDelta.removed)

			if delta < min(triggerReindexingDeltaThreshold, int(float64(annIndexInfo.(AnnIndexInfo).blobCount)*triggerReindexingRatioThreshold)) {
				log.Info().Msgf("Checkpoint: %s is already associated with ANN index %s (delta: +%d, -%d) and the delta is small enough to reuse the index",
					key, annIndexDelta.annIndexID, len(annIndexDelta.added), len(annIndexDelta.removed))
				return nil
			}
		}
	}

	s.pendingAnnIndexMutex.RLock()
	// See if we are waiting on checkpoint indexer to index this checkpoint/tkey
	cptKey := CheckpointPerTransformationKey{tenantID: tenantID, transformationKey: transformationKey, checkpointID: checkpointID}
	if _, found := s.pendingAnnIndexes[cptKey]; found {
		log.Info().Msgf("Waiting for checkpoint indexer to create ANN index for %v", key)
		s.pendingAnnIndexMutex.RUnlock()
		return nil
	}

	// See if we have an in-flight findOrCreateAnnIndexForCheckpoint() operation
	request := AnnIndexRequestKey{tenantID: tenantID, checkpointID: checkpointID, transformationKey: transformationKey}
	_, alreadyInFlight := s.annIndexRequestsMap.LoadOrStore(request, struct{}{})
	if alreadyInFlight {
		log.Info().Msgf("Find or create ANN index task already in flight for %v", request)
		s.pendingAnnIndexMutex.RUnlock()
		return nil
	}
	s.pendingAnnIndexMutex.RUnlock()

	// Track this async operation for testing
	if s.isRunningUnderTest {
		s.asyncProcessingWG.Add(1)
	}

	task := s.cpuTaskPool.Submit(func() {
		if s.isRunningUnderTest {
			defer s.asyncProcessingWG.Done()
		}

		err := s.findOrCreateAnnIndexForCheckpoint(s.backgroundCtx, request)
		if err != nil {
			log.Error().Err(err).Msgf("Failed to find or create ANN index for %v", request)
		}
	})

	// Spawn off a goroutine to wait on the task and log any task errors from pond. Note that
	// the task function doesn't return any errors, it logs them instead, so errors we get
	// here will strictly be from pond / task execution.
	go func() {
		err := task.Wait()
		if err != nil {
			// Unfortunately pond converts panics into errors. We want panics to
			// actually panic the process, so we need to convert the error back to a panic.
			if errors.Is(err, pond.ErrPanic) {
				log.Error().Err(err).Msgf("Received a panic from pond task %v", request)
				panic(err)
			} else {
				log.Error().Err(err).Msgf("Index search task failed for %v", request)
			}
		}
	}()

	return nil
}
