package main

import (
	"context"
	"strings"
	"time"

	"github.com/redis/go-redis/v9"
	"github.com/rs/zerolog/log"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/augmentcode/augment/services/lib/grpc/auth"
	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
	memstoreproto "github.com/augmentcode/augment/services/memstore/proto"
)

// MemstoreServer implements the Memstore service
type MemstoreServer struct {
	memstoreproto.UnimplementedMemstoreServer
	// Redis client for storing key-value pairs
	redisClient *redis.Client
}

// NewMemstoreServer creates a new Memstore server
func NewMemstoreServer(redisClient *redis.Client) *MemstoreServer {
	return &MemstoreServer{
		redisClient: redisClient,
	}
}

// Returns a version of the provided key that is unique to the tenant.
func transformKeyForTenant(key string, tenantID string) string {
	return tenantID + "#" + key
}

func getTenantUniqueKey(ctx context.Context, key string) (string, error) {
	claims, ok := auth.GetAugmentClaims(ctx)
	if !ok {
		return "", status.Errorf(codes.PermissionDenied, "Unauthenticated. Could not fetch claims from context.")
	}
	tenantID, err := claims.GetTenantID()
	if err != nil {
		return "", status.Errorf(codes.PermissionDenied, "Service tokens are not allowed to access the memstore.")
	}
	return transformKeyForTenant(key, tenantID), nil
}

// Get retrieves the value for a key
func (s *MemstoreServer) Get(ctx context.Context, req *memstoreproto.GetRequest) (*memstoreproto.GetResponse, error) {
	requestContext, _ := requestcontext.FromGrpcContext(ctx)
	ctx = requestContext.AnnotateLogContext(ctx)

	log.Ctx(ctx).Info().Msgf("Get: %s", req.Key)

	key, err := getTenantUniqueKey(ctx, req.Key)
	if err != nil {
		return nil, err
	}

	// Get value from Redis
	val, err := s.redisClient.Get(ctx, key).Result()
	if err == redis.Nil {
		// Key does not exist
		return &memstoreproto.GetResponse{}, nil
	} else if err != nil {
		log.Ctx(ctx).Error().Err(err).Msgf("Failed to get key %s from Redis", key)
		return nil, status.Errorf(codes.Internal, "Failed to get value from Redis: %v", err)
	}

	// There might be an unnecessary copy here because of cast to []byte. Eventually can
	// do an unsafe cast like the go-redis writer does if we want to squeeze performance:
	// https://github.com/redis/go-redis/blob/8ba559ca5db4ecb34565a6bf3ddda4c130b6b775/internal/util/unsafe.go#L15-L22
	return &memstoreproto.GetResponse{Value: []byte(val)}, nil
}

// Set sets the value for a key
func (s *MemstoreServer) Set(ctx context.Context, req *memstoreproto.SetRequest) (*memstoreproto.SetResponse, error) {
	requestContext, _ := requestcontext.FromGrpcContext(ctx)
	ctx = requestContext.AnnotateLogContext(ctx)

	if req.Value == nil {
		return nil, status.Errorf(codes.InvalidArgument, "Value to set must be non-empty.")
	}

	log.Ctx(ctx).Info().Msgf("Set: %s", req.Key)

	key, err := getTenantUniqueKey(ctx, req.Key)
	if err != nil {
		return nil, err
	}

	// Store the value in Redis with optional expiry
	var expiry time.Duration
	if req.EX != nil && *req.EX > 0 {
		expiry = time.Duration(*req.EX) * time.Second
		log.Ctx(ctx).Debug().Msgf("Setting key %s with expiry %d seconds", key, *req.EX)
	}

	setErr := s.redisClient.Set(ctx, key, req.Value, expiry).Err()

	if setErr != nil {
		log.Ctx(ctx).Error().Err(setErr).Msgf("Failed to set key %s in Redis", key)
		return nil, status.Errorf(codes.Internal, "Failed to set value in Redis: %v", setErr)
	}

	return &memstoreproto.SetResponse{}, nil
}

// Del deletes a key from the Memstore
func (s *MemstoreServer) Del(ctx context.Context, req *memstoreproto.DelRequest) (*memstoreproto.DelResponse, error) {
	requestContext, _ := requestcontext.FromGrpcContext(ctx)
	ctx = requestContext.AnnotateLogContext(ctx)

	log.Ctx(ctx).Info().Msgf("Del: %v", req.Keys)

	// Map every key to its tenant-specific key.
	keys := make([]string, len(req.Keys))
	for i, key := range req.Keys {
		tenantKey, err := getTenantUniqueKey(ctx, key)
		if err != nil {
			return nil, err
		}
		keys[i] = tenantKey
	}

	// Delete keys from Redis
	deleted, err := s.redisClient.Del(ctx, keys...).Result()
	if err != nil {
		log.Ctx(ctx).Error().Strs("keys", req.Keys).Err(err).Msgf("Failed to delete keys from Redis")
		return nil, status.Errorf(codes.Internal, "Failed to delete keys from Redis: %v", err)
	}

	return &memstoreproto.DelResponse{
		NumDeleted: deleted,
	}, nil
}

// Incr increments the integer value of a key by one
func (s *MemstoreServer) Incr(ctx context.Context, req *memstoreproto.IncrRequest) (*memstoreproto.IncrResponse, error) {
	requestContext, _ := requestcontext.FromGrpcContext(ctx)
	ctx = requestContext.AnnotateLogContext(ctx)

	log.Ctx(ctx).Info().Msgf("Incr: %s", req.Key)

	key, err := getTenantUniqueKey(ctx, req.Key)
	if err != nil {
		return nil, err
	}

	newValue, err := s.redisClient.Incr(ctx, key).Result()
	if err != nil {
		// "value is not an integer or out of range" is the actual full error string
		// the Redis server returns. Unfortunately there's no way to do this as a
		// type assertion to my knowledge, have to look at the error string.
		if strings.Contains(err.Error(), "value is not an integer") {
			log.Ctx(ctx).Error().Err(err).Msgf("Failed to increment key %s in Redis: value is not an integer or out of range", key)
			return nil, status.Errorf(codes.FailedPrecondition, "Value is not an integer or out of range.")
		}
		log.Ctx(ctx).Error().Err(err).Msgf("Failed to increment key %s in Redis", key)
		return nil, status.Errorf(codes.Internal, "Failed to increment value in Redis: %v", err)
	}

	return &memstoreproto.IncrResponse{NewValue: newValue}, nil
}

// IncrBy increments the integer value of a key by the requested value
func (s *MemstoreServer) IncrBy(ctx context.Context, req *memstoreproto.IncrByRequest) (*memstoreproto.IncrByResponse, error) {
	requestContext, _ := requestcontext.FromGrpcContext(ctx)
	ctx = requestContext.AnnotateLogContext(ctx)

	log.Ctx(ctx).Info().Msgf("IncrBy: %s by %d", req.Key, req.Increment)

	key, err := getTenantUniqueKey(ctx, req.Key)
	if err != nil {
		return nil, err
	}

	newValue, err := s.redisClient.IncrBy(ctx, key, req.Increment).Result()
	if err != nil {
		// See Incr for why we handle the error this way.
		if strings.Contains(err.Error(), "value is not an integer") {
			log.Ctx(ctx).Error().Err(err).Msgf("Failed to increment key %s by %d in Redis: value is not an integer or out of range", key, req.Increment)
			return nil, status.Errorf(codes.FailedPrecondition, "Value is not an integer or out of range.")
		}
		log.Ctx(ctx).Error().Err(err).Msgf("Failed to increment key %s by %d in Redis", key, req.Increment)
		return nil, status.Errorf(codes.Internal, "Failed to increment value in Redis: %v", err)
	}

	return &memstoreproto.IncrByResponse{NewValue: newValue}, nil
}

// ExpireAt sets the expiry time for a key to the specified Unix timestamp
func (s *MemstoreServer) ExpireAt(ctx context.Context, req *memstoreproto.ExpireAtRequest) (*memstoreproto.ExpireAtResponse, error) {
	requestContext, _ := requestcontext.FromGrpcContext(ctx)
	ctx = requestContext.AnnotateLogContext(ctx)

	log.Ctx(ctx).Info().Msgf("ExpireAt: %s at %d", req.Key, req.ExpireAt)

	key, err := getTenantUniqueKey(ctx, req.Key)
	if err != nil {
		return nil, err
	}

	// Technically Redis would raise these errors if we just passed these arguments through,
	// but we can provide better gRPC status codes if we check them here.
	if req.NX && (req.XX || req.GT || req.LT) {
		return nil, status.Errorf(codes.InvalidArgument, "Cannot specify both NX and any of XX/GT/LT")
	}
	if req.GT && req.LT {
		return nil, status.Errorf(codes.InvalidArgument, "Cannot specify both GT and LT")
	}

	// Convert Unix timestamp to time.Time
	expireTime := time.Unix(int64(req.ExpireAt), 0)

	// Build Redis EXPIREAT command arguments
	args := []interface{}{"EXPIREAT", key, req.ExpireAt}

	// Add optional flags
	if req.NX {
		args = append(args, "NX")
	}
	if req.XX {
		args = append(args, "XX")
	}
	if req.GT {
		args = append(args, "GT")
	}
	if req.LT {
		args = append(args, "LT")
	}

	// Execute EXPIREAT command. We use `Do` instead of `ExpireAt` because the Golang SDK
	// doesn't expose the extra EXPIREAT flags (NX, XX, GT, and LT).
	result, err := s.redisClient.Do(ctx, args...).Result()
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msgf("Failed to set expiry for key %s in Redis", key)
		return nil, status.Errorf(codes.Internal, "Failed to set expiry in Redis: %v", err)
	}

	// Redis EXPIREAT returns 1 if the timeout was set, 0 if key does not exist or timeout could not be set
	success := false
	if resultInt, ok := result.(int64); ok {
		success = resultInt == 1
	}

	log.Ctx(ctx).Debug().Msgf("ExpireAt result for key %s: success=%t, expire_time=%s", key, success, expireTime.Format(time.RFC3339))

	return &memstoreproto.ExpireAtResponse{Success: success}, nil
}

// Publish publishes a message to a channel
func (s *MemstoreServer) Publish(ctx context.Context, req *memstoreproto.PublishRequest) (*memstoreproto.PublishResponse, error) {
	requestContext, _ := requestcontext.FromGrpcContext(ctx)
	ctx = requestContext.AnnotateLogContext(ctx)

	if req.Channel == "" {
		return nil, status.Errorf(codes.InvalidArgument, "Channel must be non-empty.")
	}
	if req.Message == nil {
		return nil, status.Errorf(codes.InvalidArgument, "Message must be non-empty.")
	}

	// Get tenant-specific channel name
	channel, err := getTenantUniqueKey(ctx, req.Channel)
	if err != nil {
		return nil, err
	}

	// Publish message to Redis channel
	receivers, err := s.redisClient.Publish(ctx, channel, req.Message).Result()
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msgf("Failed to publish message to channel %s in Redis", channel)
		return nil, status.Errorf(codes.Internal, "Failed to publish message to Redis: %v", err)
	}

	return &memstoreproto.PublishResponse{
		Receivers: receivers,
	}, nil
}

// Subscribe subscribes to messages on a channel
func (s *MemstoreServer) Subscribe(req *memstoreproto.SubscribeRequest, stream memstoreproto.Memstore_SubscribeServer) error {
	ctx := stream.Context()
	requestContext, _ := requestcontext.FromGrpcContext(ctx)
	ctx = requestContext.AnnotateLogContext(ctx)

	if req.Channel == "" {
		return status.Errorf(codes.InvalidArgument, "Channel must be non-empty.")
	}

	log.Ctx(ctx).Info().Msgf("Subscribe: channel=%s", req.Channel)

	// Get tenant-specific channel name
	channel, err := getTenantUniqueKey(ctx, req.Channel)
	if err != nil {
		return err
	}

	// Create a new PubSub
	pubsub := s.redisClient.Subscribe(ctx, channel)
	defer pubsub.Close()

	// Listen for messages
	ch := pubsub.Channel()
	for {
		select {
		case <-ctx.Done():
			log.Ctx(ctx).Info().Msgf("Client disconnected from channel %s", req.Channel)
			return nil
		case msg, ok := <-ch:
			if !ok {
				log.Ctx(ctx).Info().Msgf("Channel %s closed", req.Channel)
				return nil
			}

			// Send the message to the client, but use the original channel name
			// instead of the tenant-specific one to maintain abstraction
			err := stream.Send(&memstoreproto.SubscribeResponse{
				Channel: req.Channel,
				Message: []byte(msg.Payload),
			})
			if err != nil {
				log.Ctx(ctx).Error().Err(err).Msgf("Failed to send message to client for channel %s", req.Channel)
				return err
			}
		}
	}
}
