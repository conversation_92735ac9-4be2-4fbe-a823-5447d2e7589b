package main

import (
	"context"
	"testing"

	tenant_watcher_proto "github.com/augmentcode/augment/services/tenant_watcher/proto"
	"github.com/stretchr/testify/require"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/types/known/durationpb"
)

// Create a tenant with all fields populated, and read it back.
func TestCreateAndGetTenant_AllFields(t *testing.T) {
	ctx := context.Background()
	persistence, cleanup := NewTenantDatabase(ctx)
	defer cleanup()

	tenant := &tenant_watcher_proto.Tenant{
		Id:             "test-id",
		Name:           "test-name",
		ShardNamespace: "test-namespace",
		Cloud:          "test-cloud",
		Tier:           tenant_watcher_proto.TenantTier_ENTERPRISE,
		Config: &tenant_watcher_proto.Config{
			Configs: map[string]string{
				"test-key":  "test-value",
				"test-key2": "test-value2",
			},
		},
		OtherNamespace:    "test-other-namespace",
		EncryptionKeyName: "test-encryption-key-name",
		EncryptionKeyTtl:  &durationpb.Duration{Seconds: 3600},
		Version:           "123",
		DeletedAt:         "2020-01-01T00:00:00Z",
		AuthConfiguration: &tenant_watcher_proto.AuthConfiguration{
			Domain:                   "test-domain",
			UsernameDomains:          []string{"test-username-domain", "test-username-domain2"},
			EmailAddressDomains:      []string{"test-email-address-domain", "test-email-address-domain2"},
			AllowedIdentityProviders: []string{"test-allowed-identity-provider", "test-allowed-identity-provider2"},
		},
	}
	err := persistence.CreateTenant(ctx, tenant)
	require.NoError(t, err)

	dbTenant, err := persistence.GetTenantByID(ctx, "test-id")
	require.NoError(t, err)

	require.True(t, proto.Equal(tenant, dbTenant), "Expected %v, got %v", tenant, dbTenant)
}

// Create a tenant with the minimum set of fields populated, and read it back.
func TestCreateAndGetTenant_MinimumFields(t *testing.T) {
	ctx := context.Background()
	persistence, cleanup := NewTenantDatabase(ctx)
	defer cleanup()

	tenant := &tenant_watcher_proto.Tenant{
		Id:                "test-id",
		Name:              "test-name",
		ShardNamespace:    "test-namespace",
		Cloud:             "test-cloud",
		Tier:              tenant_watcher_proto.TenantTier_ENTERPRISE,
		Version:           "123",
		AuthConfiguration: &tenant_watcher_proto.AuthConfiguration{},
		Config:            &tenant_watcher_proto.Config{},
	}
	err := persistence.CreateTenant(ctx, tenant)
	require.NoError(t, err)

	dbTenant, err := persistence.GetTenantByID(ctx, "test-id")
	require.NoError(t, err)

	require.True(t, proto.Equal(tenant, dbTenant), "Expected %v, got %v", tenant, dbTenant)
}
