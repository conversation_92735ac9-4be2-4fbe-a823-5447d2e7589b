package main

import (
	"context"
	"os"
	"testing"

	spanneremulator "github.com/augmentcode/augment/base/test_utils/spanner/emulator"
)

// This file has test fixtures shared across multiple test files.

// Shared emulator to use across all tests.
var spannerEmulator *spanneremulator.SpannerEmulator = nil

func TestMain(m *testing.M) {
	var err error
	spannerEmulator, err = spanneremulator.New(context.Background())
	if err != nil {
		panic(err)
	}
	defer spannerEmulator.Close()

	// Run the tests.
	os.Exit(m.Run())
}

func NewTenantDatabase(ctx context.Context) (*SpannerPersistence, func()) {
	client, cleanup, err := spannerEmulator.NewDatabaseFromJson(ctx, "spanner_ddl.json")
	if err != nil {
		panic(err)
	}

	persistence, err := NewSpannerPersistence(ctx, &SpannerConfig{
		DatabaseFullName: client.DatabaseName(),
	})
	if err != nil {
		panic(err)
	}

	return persistence, func() {
		persistence.Close()
		cleanup()
	}
}
