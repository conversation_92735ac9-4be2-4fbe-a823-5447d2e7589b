load("@rules_jsonnet//jsonnet:jsonnet.bzl", "jsonnet_library", "jsonnet_to_json")
load("//tools/bzl:go.bzl", "go_binary", "go_library", "go_oci_image", "go_test")
load("//tools/bzl:kubecfg.bzl", "kubecfg")
load("//tools/bzl:metadata.bzl", "metadata_test")
load("//tools/bzl:python.bzl", "py_library")

kubecfg(
    name = "kubecfg_shared",
    src = "deploy_shared.jsonnet",
    cluster_wide = True,
)

go_library(
    name = "server_lib",
    srcs = [
        "main.go",
        "spanner_persistence.go",
        "tenant_info.go",
        "tenant_server.go",
    ],
    importpath = "github.com/augmentcode/augment/services/tenant_watcher/server",
    visibility = ["//visibility:private"],
    deps = [
        "//base/logging:logging_go",
        "//base/logging/audit:audit_go",
        "//base/tracing/go:tracing_go",
        "//services/lib/grpc/auth:grpc_auth_go",
        "//services/lib/grpc/recovery:grpc_recovery_go",
        "//services/lib/grpc/service:grpc_service_go",
        "//services/lib/grpc/tls_config:grpc_tls_config_go",
        "//services/lib/request_context:request_context_go",
        "//services/tenant_watcher:tenant_watcher_go_proto",
        "//services/tenant_watcher/crd",
        "//services/token_exchange:token_exchange_go_proto",
        "//services/token_exchange:token_scopes_go_proto",
        "//services/token_exchange/client:client_go",
        "@com_github_grpc_ecosystem_go_grpc_middleware_providers_prometheus//:prometheus",
        "@com_github_prometheus_client_golang//prometheus",
        "@com_github_prometheus_client_golang//prometheus/promhttp",
        "@com_github_rs_zerolog//log",
        "@com_google_cloud_go_spanner//:spanner",
        "@io_k8s_apimachinery//pkg/api/errors",
        "@io_k8s_apimachinery//pkg/apis/meta/v1:meta",
        "@io_k8s_apimachinery//pkg/runtime/schema",
        "@io_k8s_client_go//tools/cache",
        "@io_opentelemetry_go_contrib_instrumentation_google_golang_org_grpc_otelgrpc//:otelgrpc",
        "@io_opentelemetry_go_otel_exporters_otlp_otlptrace_otlptracegrpc//:otlptracegrpc",
        "@org_golang_google_api//iterator",
        "@org_golang_google_grpc//:grpc",
        "@org_golang_google_grpc//codes",
        "@org_golang_google_grpc//credentials",
        "@org_golang_google_grpc//credentials/insecure",
        "@org_golang_google_grpc//health",
        "@org_golang_google_grpc//health/grpc_health_v1",
        "@org_golang_google_grpc//peer",
        "@org_golang_google_grpc//reflection",
        "@org_golang_google_grpc//status",
        # "@org_golang_google_protobuf//types/known/timestamppb",
        "@org_golang_google_protobuf//types/known/durationpb",
        "@org_uber_go_automaxprocs//:automaxprocs",
    ],
)

go_test(
    name = "server_test",
    srcs = [
        "spanner_persistence_test.go",
        "tenant_info_test.go",
        "tenant_server_test.go",
        "test_fixtures.go",
    ],
    data = [":spanner_ddl_json"],
    embed = [":server_lib"],
    deps = [
        "//base/test_utils/spanner:emulator_go",
        "@com_github_rs_zerolog//log",
        "@com_github_stretchr_testify//assert",
        "@com_github_stretchr_testify//require",
        "@org_golang_google_grpc//credentials",
        "@org_golang_google_grpc//peer",
        "@org_golang_google_protobuf//proto",
    ],
)

go_binary(
    name = "server",
    embed = [":server_lib"],
    visibility = ["//visibility:public"],
)

go_oci_image(
    name = "image",
    package_name = package_name(),
    binary = ":server",
    tars = ["//tools/docker:grpc_health_probe_tar"],
)

py_library(
    name = "tenant_watcher_test_setup",
    testonly = True,
    srcs = [
        "tenant_watcher_test_setup.py",
    ],
    data = [
        ":server",
    ],
    pyright_extra_args = {
        "reportMissingParameterType": True,
    },
    visibility = ["//services:__subpackages__"],
    deps = [
        "//services/tenant_watcher:tenant_watcher_py_proto",
    ],
)

kubecfg(
    name = "kubecfg",
    src = "deploy.jsonnet",
    data = [
        ":image",
    ],
    visibility = [
        "//services:__subpackages__",
        "//tools:__subpackages__",
    ],
    deps = [
        ":spanner_ddl",
        "//deploy/common:cert-lib",
        "//deploy/common:cloud_info",
        "//deploy/common:config-map-lib",
        "//deploy/common:dynamic-feature-flags-lib",
        "//deploy/common:grpc-lib",
        "//deploy/common:lib",
        "//deploy/common:node-lib",
        "//deploy/common:telemetry-lib",
        "//deploy/gcp:gcp-lib",
        "//services/deploy:endpoints",
    ],
)

jsonnet_library(
    name = "spanner_ddl",
    srcs = [
        "spanner_ddl.jsonnet",
    ],
)

jsonnet_to_json(
    name = "spanner_ddl_json",
    src = "spanner_ddl.jsonnet",
    outs = ["spanner_ddl.json"],
    deps = [
        ":spanner_ddl",
    ],
)

metadata_test(
    name = "metadata_test",
    src = "METADATA.jsonnet",
    deps = [
        ":kubecfg",
        ":kubecfg_shared",
    ],
)
