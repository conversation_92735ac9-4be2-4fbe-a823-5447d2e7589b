// Schema DDL statements go here. This is an immutable list. ConfigConnector will apply just new
// statements. Changes are applied atomically and are all-or-nothing.
//
// Every DDL statement should have its own entry in the list and should NOT end with a semicolon or
// include SQL comments, or parsing will fail.
//
// See Spanner docs for best practices: https://cloud.google.com/spanner/docs/schema-and-data-model

[
  |||
    CREATE TABLE Tenant (
      TenantID STRING(36) NOT NULL,
      Name STRING(MAX) NOT NULL,
      ShardNamespace STRING(MAX) NOT NULL,
      OtherNamespace STRING(MAX),
      Cloud STRING(MAX) NOT NULL,
      Version INT64 NOT NULL,
      Tier STRING(MAX) NOT NULL,
      DeletedAt TIMESTAMP,
      Domain STRING(MAX),
      UsernameDomains ARRAY<STRING(MAX)>,
      EmailAddressDomains ARRAY<STRING(MAX)>,
      AllowedIdentityProviders ARRAY<STRING(MAX)>,
      EncryptionKeyName STRING(MAX),
      EncryptionKeyTTLSeconds INT64,
    ) PRIMARY KEY (TenantID)
  |||,

  |||
    CREATE TABLE TenantConfig (
      TenantID STRING(36) NOT NULL,
      Key STRING(MAX) NOT NULL,
      Value STRING(MAX) NOT NULL,
    ) PRIMARY KEY (TenantID, Key),
    INTERLEAVE IN PARENT Tenant ON DELETE CASCADE
  |||,

  // Index for looking up tenants by name and enforcing that names are unique.
  |||
    CREATE UNIQUE INDEX Tenant_Name ON Tenant (Name)
  |||,

  // Index for looking up tenants by shard namespace.
  |||
    CREATE INDEX Tenant_ShardNamespace ON Tenant (ShardNamespace)
  |||,
]
