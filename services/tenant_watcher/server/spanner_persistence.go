package main

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"cloud.google.com/go/spanner"
	tenant_watcher_proto "github.com/augmentcode/augment/services/tenant_watcher/proto"
	"github.com/rs/zerolog/log"
	"google.golang.org/api/iterator"
	"google.golang.org/protobuf/types/known/durationpb"
)

type SpannerConfig struct {
	// Full name of the database, in the form
	// "projects/<project>/instances/<instance>/databases/<database>".
	DatabaseFullName string `json:"database_full_name"`

	// Temporary flag for completely disabling spanner in tenant-central.
	Enabled bool `json:"enabled"`
}

type SpannerPersistence struct {
	spannerClient *spanner.Client
}

// Represents a row in the Tenant table.
type tenantRow struct {
	TenantID                 string
	Name                     string
	ShardNamespace           string
	OtherNamespace           spanner.NullString
	Cloud                    string
	Version                  int64
	Tier                     string
	DeletedAt                spanner.NullTime
	Domain                   spanner.NullString
	UsernameDomains          []string
	EmailAddressDomains      []string
	AllowedIdentityProviders []string
	EncryptionKeyName        spanner.NullString
	EncryptionKeyTTLSeconds  spanner.NullInt64
}

// Represents a row in the TenantConfig table.
type tenantConfigRow struct {
	TenantID string
	Key      string
	Value    string
}

// Represents a row in the Tenant table with the associated config rows. Useful for queries that
// return the tenant and its config in a single query.
type tenantRowWithConfig struct {
	tenantRow
	Configs []*tenantConfigRow
}

// Use these constants in place of * in SELECT queries, to avoid schema migration issues. This
// should be kept in sync with the tenantRow and tenantConfigRow definitions above.
const (
	allTenantFields       = "TenantID, Name, ShardNamespace, OtherNamespace, Cloud, Version, Tier, DeletedAt, Domain, UsernameDomains, EmailAddressDomains, AllowedIdentityProviders, EncryptionKeyName, EncryptionKeyTTLSeconds"
	allTenantConfigFields = "TenantID, Key, Value"
)

func NewSpannerPersistence(
	ctx context.Context, config *SpannerConfig,
) (*SpannerPersistence, error) {
	spannerClient, err := spanner.NewClient(ctx, config.DatabaseFullName)
	if err != nil {
		return nil, fmt.Errorf("failed to create spanner client: %w", err)
	}

	return &SpannerPersistence{
		spannerClient: spannerClient,
	}, nil
}

func (s *SpannerPersistence) Close() {
	s.spannerClient.Close()
}

func (s *SpannerPersistence) CreateTenant(
	ctx context.Context, tenant *tenant_watcher_proto.Tenant,
) error {
	tenantRow, configRows, err := tenantProtoToSpannerRows(tenant)
	if err != nil {
		return fmt.Errorf("failed to convert proto to spanner row: %w", err)
	}

	_, err = s.spannerClient.ReadWriteTransaction(ctx, func(ctx context.Context, txn *spanner.ReadWriteTransaction) error {
		mutations := make([]*spanner.Mutation, 0, 1+len(configRows))

		// Create the tenant row.
		tenantInsertion, err := spanner.InsertStruct("Tenant", tenantRow)
		if err != nil {
			return fmt.Errorf("failed to insert tenant row: %w", err)
		}
		mutations = append(mutations, tenantInsertion)

		// Create the tenant config rows.
		for _, row := range configRows {
			configInsertion, err := spanner.InsertStruct("TenantConfig", row)
			if err != nil {
				return fmt.Errorf("failed to insert tenant config row: %w", err)
			}
			mutations = append(mutations, configInsertion)
		}

		return txn.BufferWrite(mutations)
	})
	if err != nil {
		return fmt.Errorf("failed to create tenant in spanner: %w", err)
	}
	return nil
}

func (s *SpannerPersistence) GetTenantByID(
	ctx context.Context, tenantID string,
) (*tenant_watcher_proto.Tenant, error) {
	txn := s.spannerClient.ReadOnlyTransaction()
	defer txn.Close()

	iter := txn.Query(ctx, spanner.Statement{
		SQL: fmt.Sprintf(`
			SELECT %s, ARRAY(SELECT AS STRUCT Key, Value FROM TenantConfig WHERE TenantID = @tenantID) AS Configs
			FROM Tenant
			WHERE TenantID = @tenantID
		`, allTenantFields),
		Params: map[string]interface{}{
			"tenantID": tenantID,
		},
	})
	defer iter.Stop()

	tenants, err := tenantsAndConfigsFromIter(ctx, iter)
	if err != nil {
		return nil, fmt.Errorf("failed to read tenant row: %w", err)
	} else if len(tenants) == 0 {
		return nil, nil
	} else if len(tenants) > 1 {
		// This should be impossible.
		log.Ctx(ctx).Error().Msgf("Multiple tenants returned for ID %s", tenantID)
		return nil, fmt.Errorf("multiple tenants returned for ID %s", tenantID)
	}

	return tenants[0], nil
}

func tenantProtoToSpannerRows(
	tenant *tenant_watcher_proto.Tenant,
) (*tenantRow, []*tenantConfigRow, error) {
	versionInt, err := strconv.Atoi(tenant.Version)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to convert version to int: %w", err)
	}
	tenantRow := &tenantRow{
		TenantID:       tenant.Id,
		Name:           tenant.Name,
		ShardNamespace: tenant.ShardNamespace,
		Cloud:          tenant.Cloud,
		Version:        int64(versionInt),
		Tier:           tenant.Tier.String(),
	}

	if tenant.AuthConfiguration != nil {
		tenantRow.UsernameDomains = tenant.AuthConfiguration.UsernameDomains
		tenantRow.EmailAddressDomains = tenant.AuthConfiguration.EmailAddressDomains
		tenantRow.AllowedIdentityProviders = tenant.AuthConfiguration.AllowedIdentityProviders
		if tenant.AuthConfiguration.Domain != "" {
			tenantRow.Domain = spanner.NullString{Valid: true, StringVal: tenant.AuthConfiguration.Domain}
		}
	}
	if tenant.OtherNamespace != "" {
		tenantRow.OtherNamespace = spanner.NullString{Valid: true, StringVal: tenant.OtherNamespace}
	}
	if tenant.DeletedAt != "" {
		deletedAtTime, err := time.Parse(time.RFC3339, tenant.DeletedAt)
		if err != nil {
			return nil, nil, fmt.Errorf("failed to parse DeletedAt timestamp")
		}
		tenantRow.DeletedAt = spanner.NullTime{Valid: true, Time: deletedAtTime}
	}
	if tenant.EncryptionKeyName != "" {
		tenantRow.EncryptionKeyName = spanner.NullString{Valid: true, StringVal: tenant.EncryptionKeyName}
	}
	if tenant.EncryptionKeyTtl != nil {
		tenantRow.EncryptionKeyTTLSeconds = spanner.NullInt64{Valid: true, Int64: int64(tenant.EncryptionKeyTtl.Seconds)}
	}

	var configRows []*tenantConfigRow
	if tenant.Config != nil {
		configRows = make([]*tenantConfigRow, 0, len(tenant.Config.Configs))
		for k, v := range tenant.Config.Configs {
			configRows = append(configRows, &tenantConfigRow{
				TenantID: tenant.Id,
				Key:      k,
				Value:    v,
			})
		}
	}

	return tenantRow, configRows, nil
}

func tenantRowWithConfigToProto(
	row *tenantRowWithConfig,
) *tenant_watcher_proto.Tenant {
	configs := make(map[string]string, len(row.Configs))
	for _, config := range row.Configs {
		configs[config.Key] = config.Value
	}

	authConfig := &tenant_watcher_proto.AuthConfiguration{
		UsernameDomains:          row.UsernameDomains,
		EmailAddressDomains:      row.EmailAddressDomains,
		AllowedIdentityProviders: row.AllowedIdentityProviders,
	}
	if row.Domain.Valid {
		authConfig.Domain = row.Domain.StringVal
	}

	tenant := &tenant_watcher_proto.Tenant{
		Id:                row.TenantID,
		Name:              row.Name,
		ShardNamespace:    row.ShardNamespace,
		Cloud:             row.Cloud,
		Version:           strconv.FormatInt(row.Version, 10),
		Tier:              tenant_watcher_proto.TenantTier(tenant_watcher_proto.TenantTier_value[row.Tier]),
		AuthConfiguration: authConfig,
		Config: &tenant_watcher_proto.Config{
			Configs: configs,
		},
	}

	if row.DeletedAt.Valid {
		tenant.DeletedAt = row.DeletedAt.Time.Format(time.RFC3339)
	}
	if row.OtherNamespace.Valid {
		tenant.OtherNamespace = row.OtherNamespace.StringVal
	}
	if row.EncryptionKeyName.Valid {
		tenant.EncryptionKeyName = row.EncryptionKeyName.StringVal
	}
	if row.EncryptionKeyTTLSeconds.Valid {
		tenant.EncryptionKeyTtl = &durationpb.Duration{Seconds: row.EncryptionKeyTTLSeconds.Int64}
	}

	return tenant
}

func tenantsAndConfigsFromIter(
	ctx context.Context, iter *spanner.RowIterator,
) ([]*tenant_watcher_proto.Tenant, error) {
	var tenants []*tenant_watcher_proto.Tenant
	for {
		row, err := iter.Next()
		if err == iterator.Done {
			break
		} else if err != nil {
			return nil, fmt.Errorf("failed to read tenant row: %w", err)
		}

		var tenantRowWithConfig tenantRowWithConfig
		if err := row.ToStruct(&tenantRowWithConfig); err != nil {
			return nil, fmt.Errorf("failed to convert row to struct: %w", err)
		}
		tenants = append(tenants, tenantRowWithConfigToProto(&tenantRowWithConfig))
	}

	return tenants, nil
}
